import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Contact support section
            Text(
              'Contact Support',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildContactItem(
              icon: Icons.email_outlined,
              title: 'Email',
              subtitle: '<EMAIL>',
              onTap: () {
                // Open email app
              },
            ),
            _buildContactItem(
              icon: Icons.phone_outlined,
              title: 'Phone',
              subtitle: '+****************',
              onTap: () {
                // Open phone app
              },
            ),
            _buildContactItem(
              icon: Icons.chat_outlined,
              title: 'Live Chat',
              subtitle: 'Available 24/7',
              onTap: () {
                // Open chat
              },
            ),
            const SizedBox(height: 32),

            // FAQ section
            Text(
              'Frequently Asked Questions',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildFaqItem(
              question: 'How do I create a new task?',
              answer: 'To create a new task, go to the Tasks tab and tap on the + button in the bottom right corner. Fill in the task details and tap Save.',
            ),
            _buildFaqItem(
              question: 'How do I assign a task to someone?',
              answer: 'When creating or editing a task, you can select an assignee from the dropdown menu. Only users in your department or with appropriate permissions will be available.',
            ),
            _buildFaqItem(
              question: 'How do I track task progress?',
              answer: 'You can update the completion percentage of a task by editing it. The progress will be visible in the task list and dashboard.',
            ),
            _buildFaqItem(
              question: 'How do I add comments to a task?',
              answer: 'Open a task and scroll down to the comments section. Type your comment in the text field and tap Send.',
            ),
            _buildFaqItem(
              question: 'How do I attach files to a task?',
              answer: 'When viewing a task, tap on the attachment icon to add files from your device.',
            ),
            const SizedBox(height: 32),

            // Video tutorials section
            Text(
              'Video Tutorials',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildVideoTutorialItem(
              title: 'Getting Started',
              duration: '5:32',
              thumbnail: 'assets/images/tutorial_1.jpg',
              onTap: () {
                // Open video
              },
            ),
            _buildVideoTutorialItem(
              title: 'Task Management',
              duration: '7:15',
              thumbnail: 'assets/images/tutorial_2.jpg',
              onTap: () {
                // Open video
              },
            ),
            _buildVideoTutorialItem(
              title: 'Team Collaboration',
              duration: '6:48',
              thumbnail: 'assets/images/tutorial_3.jpg',
              onTap: () {
                // Open video
              },
            ),
            const SizedBox(height: 32),

            // Submit feedback section
            Card(
              color: AppColors.primary.withAlpha(26), // 0.1 * 255 = ~26
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Submit Feedback',
                      style: AppStyles.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'We value your feedback! Let us know how we can improve the app.',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // Open feedback form
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Send Feedback'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppColors.primary,
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildFaqItem({
    required String question,
    required String answer,
  }) {
    return ExpansionTile(
      title: Text(
        question,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(answer),
        ),
      ],
    );
  }

  Widget _buildVideoTutorialItem({
    required String title,
    required String duration,
    required String thumbnail,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  height: 160,
                  width: double.infinity,
                  color: Colors.grey.shade300,
                  child: const Icon(
                    Icons.image,
                    size: 48,
                    color: Colors.grey,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(179), // 0.7 * 255 = ~179
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(179), // 0.7 * 255 = ~179
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      duration,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
