using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using webApi.Services;

namespace webApi.Attributes
{
    /// <summary>
    /// Attribute مخصص للتحقق من صلاحية المستخدم بشكل ديناميكي قبل تنفيذ أي أكشن
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
    public class HasPermissionAttribute : Attribute, IAsyncAuthorizationFilter
    {
        private readonly string _permissionName;
        public HasPermissionAttribute(string permissionName)
        {
            _permissionName = permissionName;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;
            if (!user.Identity.IsAuthenticated)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // استخراج userId من Claims
            var userIdClaim = user.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                context.Result = new ForbidResult();
                return;
            }

            // جلب خدمة الصلاحيات
            var permissionService = context.HttpContext.RequestServices.GetService<UserPermissionService>();
            if (permissionService == null)
            {
                context.Result = new StatusCodeResult(500);
                return;
            }

            // استخدام الطريقة المحسنة للتحقق من الصلاحية
            var hasPermission = await permissionService.HasPermissionAsync(userId, _permissionName);
            if (!hasPermission)
            {
                // إرجاع رسالة خطأ واضحة مع تفاصيل الصلاحية المطلوبة
                context.Result = new ObjectResult(new
                {
                    message = $"ليس لديك صلاحية للوصول إلى هذا المورد. الصلاحية المطلوبة: {_permissionName}",
                    error = "Insufficient Permissions",
                    requiredPermission = _permissionName,
                    userId = userId
                })
                {
                    StatusCode = 403 // Forbidden
                };
                return;
            }
        }
    }
}
