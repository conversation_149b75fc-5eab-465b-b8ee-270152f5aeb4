import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_contributor_model.dart';



/// تبويب النظرة الشاملة للمهمة
class TaskOverviewTab extends StatefulWidget {
  const TaskOverviewTab({super.key});

  @override
  State<TaskOverviewTab> createState() => _TaskOverviewTabState();
}

class _TaskOverviewTabState extends State<TaskOverviewTab> {
  final TaskController _taskController = Get.find<TaskController>();

  @override
  void initState() {
    super.initState();
    _loadContributors();
  }

  /// تحميل المساهمين للمهمة الحالية
  Future<void> _loadContributors() async {
    final currentTask = _taskController.currentTask;
    if (currentTask != null) {
      try {
        await _taskController.loadTaskContributors(currentTask.id);
      } catch (e) {
        debugPrint('خطأ في تحميل المساهمين: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // استبدال Obx بـ GetBuilder لتجنب تحديث الحالة أثناء البناء
    return GetBuilder<TaskController>(
      id: 'task_details', // إضافة نفس المعرف المستخدم في task_detail_screen
      builder: (controller) {
        if (controller.isLoading) {
          return const Center(child: LoadingIndicator());
        }

        final task = controller.currentTask;
        if (task == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'لا توجد بيانات',
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'لم يتم العثور على بيانات المهمة',
                  style: AppStyles.bodyMedium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المهمة الأساسية
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: AppColors.primary.withAlpha(51), width: 1),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: AppColors.primary),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'معلومات المهمة',
                              style: AppStyles.titleMedium.copyWith(color: AppColors.primary),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 16),

                      // معلومات المهمة
                      // رقم المهمة - Task ID
                      _buildTaskInfoItem('رقم المهمة', '#${task.id}', Icons.tag, 
                        customWidget: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(26),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: AppColors.primary.withAlpha(77)),
                          ),
                          child: Text(
                            '#${task.id}',
                            style: AppStyles.bodyMedium.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      _buildTaskInfoItem('العنوان', task.title, Icons.title),
                      
                      // الحقول الجديدة - New fields
                      // الوارد - Incoming
                      if (task.incoming?.isNotEmpty == true)
                        _buildTaskInfoItem('الوارد', task.incoming!, Icons.input, 
                          customWidget: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.input, size: 16, color: Colors.blue.shade700),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    task.incoming!,
                                    style: AppStyles.bodyMedium.copyWith(
                                      color: Colors.blue.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      
                      // الملاحظات - Note
                      if (task.note?.isNotEmpty == true)
                        _buildTaskInfoItem('الملاحظات', task.note!, Icons.note, 
                          customWidget: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade50,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.orange.shade200),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.note, size: 16, color: Colors.orange.shade700),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    task.note!,
                                    style: AppStyles.bodyMedium.copyWith(
                                      color: Colors.orange.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      
                      // الحالة مع الأيقونة الموحدة
                      _buildTaskInfoItem('الحالة', '', null, customWidget: _buildStatusWithIcon(task.status)),
                      // تحسين عرض نسبة الإكمال مع مؤشر دائري ملون
                      GetBuilder<TaskController>(
                        id: 'task_details',
                        builder: (controller) {
                          final currentTask = controller.currentTask ?? task;
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 60,
                                  height: 60,
                                  child: CircularProgressIndicator(
                                    value: currentTask.completionPercentage / 100,
                                    strokeWidth: 6,
                                    color: _getProgressColor(currentTask.completionPercentage.toDouble()),
                                    backgroundColor: Colors.grey.shade300,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    '${currentTask.completionPercentage}%',
                                    style: AppStyles.titleLarge.copyWith(
                                      color: _getProgressColor(currentTask.completionPercentage.toDouble()),
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      if (task.dueDate != null)
                        _buildTaskInfoItem(
                          'الموعد النهائي',
                          _formatDate(task.dueDateDateTime!),
                          Icons.event
                        ),
                    ],
                  ),
                ),
              ),

              // قسم الإحصائيات
              const SizedBox(height: 16),
              _buildStatisticsSection(task),

              // قسم المهام الفرعية
              if (task.subtasks.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildSubtasksSection(),
              ],

              // قسم المساهمين
              const SizedBox(height: 16),
              _buildContributorsSection(),
            ],
          ),
        );
      },
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatisticsSection(dynamic task) {
    final commentsCount = task.comments?.length ?? 0;
    final attachmentsCount = task.attachments?.length ?? 0;
    debugPrint('📦 [task_overview_tab] attachmentsCount: ' + attachmentsCount.toString());
    final contributors = _taskController.taskContributors;
    final contributorsCount = contributors.length;
    final subtasksCount = task.subtasks?.length ?? 0;
    
    // حساب المهام الفرعية المكتملة
    int completedSubtasks = 0;
    if (task.subtasks != null && task.subtasks is List) {
      try {
        completedSubtasks = task.subtasks.where((subtask) => subtask.isCompleted == true).length;
      } catch (e) {
        debugPrint('خطأ في حساب المهام الفرعية المكتملة: $e');
        completedSubtasks = 0;
      }
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.primary.withAlpha(51), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'إحصائيات المهمة',
                    style: AppStyles.titleMedium.copyWith(color: AppColors.primary),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // الإحصائيات في شبكة متجاوبة
            LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = MediaQuery.of(context).size.width;

                // تحديد عدد الأعمدة ونسبة العرض للارتفاع بناءً على عرض الشاشة
                int crossAxisCount = 2;
                double childAspectRatio = 1.8;

                if (screenWidth > 800) {
                  crossAxisCount = 4;
                  childAspectRatio = 1.3;
                } else if (screenWidth > 600) {
                  crossAxisCount = 3;
                  childAspectRatio = 1.5;
                } else if (screenWidth > 400) {
                  crossAxisCount = 2;
                  childAspectRatio = 1.8;
                } else {
                  crossAxisCount = 1;
                  childAspectRatio = 3.5;
                }

                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    _buildStatisticCard(
                      'التعليقات',
                      commentsCount.toString(),
                      Icons.comment,
                      Colors.blue,
                    ),
                    _buildStatisticCard(
                      'المرفقات',
                      attachmentsCount.toString(),
                      Icons.attach_file,
                      Colors.green,
                    ),
                    _buildStatisticCard(
                      'المساهمون',
                      contributorsCount.toString(),
                      Icons.people,
                      Colors.orange,
                    ),
                    _buildStatisticCard(
                      'المهام الفرعية',
                      '$completedSubtasks / $subtasksCount',
                      Icons.checklist,
                      Colors.purple,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية متجاوبة
  Widget _buildStatisticCard(String title, String value, IconData icon, Color color) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد الأحجام بناءً على المساحة المتاحة
        final cardWidth = constraints.maxWidth;
        final cardHeight = constraints.maxHeight;

        // تحديد أحجام متجاوبة بناءً على أبعاد البطاقة
        double iconSize = (cardHeight * 0.15).clamp(16.0, 32.0);
        double valueFontSize = (cardHeight * 0.2).clamp(12.0, 24.0);
        double titleFontSize = (cardHeight * 0.13).clamp(10.0, 16.0);
        double padding = (cardWidth * 0.06).clamp(7.0, 18.0);
        double verticalSpacing = (cardHeight * 0.04).clamp(2.0, 8.0);

        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withAlpha(51), width: 1.5),
            boxShadow: [
              BoxShadow(
                color: color.withAlpha(13),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // الأيقونة
                Icon(
                  icon,
                  color: color,
                  size: iconSize,
                ),
                SizedBox(height: verticalSpacing),

                // القيمة
                Expanded(
                  flex: 2,
                  child: Center(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        value,
                        style: AppStyles.titleMedium.copyWith(
                          color: color,
                          fontWeight: FontWeight.bold,
                          fontSize: valueFontSize,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                      ),
                    ),
                  ),
                ),

                SizedBox(height: verticalSpacing * 0.5),

                // العنوان
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      title,
                      style: AppStyles.bodySmall.copyWith(
                        color: color,
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء عنصر معلومات المهمة
  Widget _buildTaskInfoItem(String label, String value, IconData? icon, {Widget? customWidget}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (icon != null) Icon(icon, size: 20, color: Colors.grey),
            if (icon != null) const SizedBox(width: 8),
            Text(
              '$label:',
              style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 250),
              child: customWidget ?? Text(
                value,
                style: AppStyles.bodyMedium,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المهام الفرعية
  Widget _buildSubtasksSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.primary.withAlpha(51), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.checklist, color: AppColors.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'المهام الفرعية',
                    style: AppStyles.titleMedium.copyWith(color: AppColors.primary),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // عرض معلومات المهام الفرعية
            _buildSubtasksInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات المهام الفرعية
  Widget _buildSubtasksInfo() {
    final subtasks = _taskController.subtasks;
    int completedCount = 0;
    
    // حساب المهام الفرعية المكتملة
    try {
      completedCount = subtasks.where((subtask) => subtask.isCompleted).length;
    } catch (e) {
      debugPrint('خطأ في حساب المهام الفرعية المكتملة: $e');
      completedCount = 0;
    }
    
    final totalCount = subtasks.length;
    final completionPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // مؤشر التقدم الكلي للمهام الفرعية
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تقدم المهام الفرعية: ${completionPercentage.toInt()}%',
                    style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: completionPercentage / 100,
                    backgroundColor: Colors.grey.withAlpha(51),
                    color: _getProgressColor(completionPercentage),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$completedCount / $totalCount',
                style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم المساهمين
  Widget _buildContributorsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: AppColors.primary.withAlpha(51), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.people, color: AppColors.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'المساهمون',
                    style: AppStyles.titleMedium.copyWith(color: AppColors.primary),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // عرض قائمة المساهمين
            _buildContributorsList(),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المساهمين
  Widget _buildContributorsList() {
    final contributors = _taskController.taskContributors;

    if (contributors.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'لا توجد بيانات مساهمة بعد',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    // ترتيب المساهمين: النشطين أولاً ثم حسب نسبة المساهمة
    final sortedContributors = List<TaskContributor>.from(contributors)
      ..sort((a, b) {
        // النشطين أولاً
        if (a.isActiveContributor && !b.isActiveContributor) return -1;
        if (!a.isActiveContributor && b.isActiveContributor) return 1;
        // ثم حسب نسبة المساهمة
        return b.contributionPercentage.compareTo(a.contributionPercentage);
      });

    return Column(
      children: [
        ...sortedContributors.map((contributor) =>
          _buildContributorItem(
            contributor.userId.toString(),
            contributor.contributionPercentage,
            contributor: contributor,
          )
        ),
      ],
    );
  }

  /// بناء عنصر المساهم
  Widget _buildContributorItem(String userId, double percentage, {TaskContributor? contributor}) {
    // استخدام اسم المساهم من البيانات المحملة مسبقاً لتجنب مشكلة FutureBuilder
    final userName = contributor?.userName ?? 'مستخدم غير معروف';

    // تحديد لون وأيقونة المساهم بناءً على حالته
    final isActive = contributor?.isActiveContributor ?? percentage > 0;
    final contributorColor = isActive ? AppColors.primary : Colors.grey;
    final contributorIcon = isActive ? Icons.person : Icons.person_outline;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: isActive ? AppColors.primary.withAlpha(51) : Colors.grey.withAlpha(51),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isActive ? AppColors.primary.withAlpha(13) : Colors.grey.withAlpha(13),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: contributorColor,
          child: Icon(
            contributorIcon,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                userName,
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (!isActive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(51),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  'محتمل',
                  style: AppStyles.bodySmall.copyWith(
                    color: Colors.orange.shade700,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey.withAlpha(51),
              color: _getProgressColor(percentage),
              minHeight: 6,
              borderRadius: BorderRadius.circular(3),
            ),
            if (contributor != null) ...[
              const SizedBox(height: 4),
              Text(
                contributor.contributionDescription,
                style: AppStyles.bodySmall.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 11,
                ),
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: AppStyles.titleSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: contributorColor,
              ),
            ),
            if (contributor != null && isActive) ...[
              const SizedBox(height: 2),
              Text(
                '${contributor.totalUpdates} تحديث',
                style: AppStyles.bodySmall.copyWith(
                  color: Colors.grey.shade600,
                  fontSize: 10,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// الحصول على لون مؤشر التقدم بناءً على النسبة
  Color _getProgressColor(double percentage) {
    if (percentage >= 75) {
      return Colors.green;
    } else if (percentage >= 50) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// تنسيق التاريخ ليظهر بشكل مناسب
  String _formatDate(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd – kk:mm').format(dateTime);
  }

  /// بناء عنصر الحالة مع الأيقونة الموحدة
  Widget _buildStatusWithIcon(String status) {
    IconData iconData;
    Color color;

    switch (status) {
      case 'مفتوحة':
        iconData = Icons.open_in_new;
        color = Colors.blue;
        break;
      case 'مغلقة':
        iconData = Icons.lock;
        color = Colors.grey;
        break;
      case 'قيد التنفيذ':
        iconData = Icons.play_arrow;
        color = Colors.green;
        break;
      case 'معلقة':
        iconData = Icons.pause;
        color = Colors.orange;
        break;
      default:
        iconData = Icons.help_outline;
        color = Colors.red;
        break;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(iconData, size: 16, color: color),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            status,
            style: AppStyles.bodyMedium.copyWith(color: color),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }


}
