import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/message_reaction_models.dart';
import '../services/api/message_reactions_api_service.dart';

/// متحكم تفاعلات الرسائل
class MessageReactionsController extends GetxController {
  final MessageReactionsApiService _apiService = MessageReactionsApiService();

  // قوائم التفاعلات
  final RxList<MessageReaction> _allReactions = <MessageReaction>[].obs;
  final RxList<MessageReaction> _filteredReactions = <MessageReaction>[].obs;
  final RxList<MessageReaction> _messageReactions = <MessageReaction>[].obs;

  // التفاعل الحالي
  final Rx<MessageReaction?> _currentReaction = Rx<MessageReaction?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _messageFilter = Rx<int?>(null);
  final Rx<String?> _reactionTypeFilter = Rx<String?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);

  // إحصائيات التفاعلات
  final RxMap<String, int> _reactionCounts = <String, int>{}.obs;

  // Getters
  List<MessageReaction> get allReactions => _allReactions;
  List<MessageReaction> get filteredReactions => _filteredReactions;
  List<MessageReaction> get messageReactions => _messageReactions;
  MessageReaction? get currentReaction => _currentReaction.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get messageFilter => _messageFilter.value;
  String? get reactionTypeFilter => _reactionTypeFilter.value;
  int? get userFilter => _userFilter.value;
  Map<String, int> get reactionCounts => _reactionCounts;

  @override
  void onInit() {
    super.onInit();
    loadAllReactions();
  }

  /// تحميل جميع تفاعلات الرسائل
  Future<void> loadAllReactions() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final reactions = await _apiService.getAllReactions();
      _allReactions.assignAll(reactions);
      _applyFilters();
      debugPrint('تم تحميل ${reactions.length} تفاعل رسالة');
    } catch (e) {
      _error.value = 'خطأ في تحميل تفاعلات الرسائل: $e';
      debugPrint('خطأ في تحميل تفاعلات الرسائل: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تفاعل رسالة بالمعرف
  Future<void> getReactionById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final reaction = await _apiService.getReactionById(id);
      _currentReaction.value = reaction;
      debugPrint('تم تحميل تفاعل الرسالة');
    } catch (e) {
      _error.value = 'خطأ في تحميل تفاعل الرسالة: $e';
      debugPrint('خطأ في تحميل تفاعل الرسالة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة تفاعل على رسالة
  Future<bool> addReaction(int messageId, int userId, String reactionType) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // إنشاء كائن التفاعل
      final reactionData = MessageReaction(
        id: 0, // سيتم تعيينه من الخادم
        messageId: messageId,
        userId: userId,
        reaction: reactionType,
        reactionType: reactionType,
        createdAt: DateTime.now().millisecondsSinceEpoch,
      );

      final reaction = await _apiService.addReaction(reactionData);
      _allReactions.add(reaction);
      _applyFilters();
      _updateReactionCounts();
      debugPrint('تم إضافة تفاعل على الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة تفاعل على الرسالة: $e';
      debugPrint('خطأ في إضافة تفاعل على الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إزالة تفاعل من رسالة
  Future<bool> removeReaction(int reactionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteReaction(reactionId);
      _allReactions.removeWhere((r) => r.id == reactionId);
      _applyFilters();
      _updateReactionCounts();
      debugPrint('تم إزالة تفاعل من الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إزالة تفاعل من الرسالة: $e';
      debugPrint('خطأ في إزالة تفاعل من الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تبديل تفاعل على رسالة
  Future<bool> toggleReaction(int messageId, int userId, String reactionType) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final result = await _apiService.toggleReaction(messageId, userId, reactionType);

      if (result != null) {
        // تم إضافة تفاعل جديد
        _allReactions.add(result);
      } else {
        // تم إزالة تفاعل موجود
        _allReactions.removeWhere((r) =>
          r.messageId == messageId &&
          r.userId == userId &&
          r.reactionType == reactionType);
      }

      _applyFilters();
      _updateReactionCounts();
      debugPrint('تم تبديل تفاعل الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تبديل تفاعل الرسالة: $e';
      debugPrint('خطأ في تبديل تفاعل الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تفاعلات رسالة محددة
  Future<void> getReactionsByMessage(int messageId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final reactions = await _apiService.getReactionsByMessage(messageId);
      _messageReactions.assignAll(reactions);
      _updateMessageReactionCounts(reactions);
      debugPrint('تم تحميل ${reactions.length} تفاعل للرسالة $messageId');
    } catch (e) {
      _error.value = 'خطأ في تحميل تفاعلات الرسالة: $e';
      debugPrint('خطأ في تحميل تفاعلات الرسالة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تفاعلات المستخدم
  Future<List<MessageReaction>> getUserReactions(int userId) async {
    try {
      final reactions = await _apiService.getReactionsByUser(userId);
      debugPrint('تم تحميل ${reactions.length} تفاعل للمستخدم $userId');
      return reactions;
    } catch (e) {
      debugPrint('خطأ في تحميل تفاعلات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على أكثر التفاعلات استخداماً
  Future<Map<String, int>?> getPopularReactions() async {
    try {
      final popularReactions = await _apiService.getPopularReactions();
      debugPrint('تم تحميل التفاعلات الشائعة');

      // تحويل List<Map<String, dynamic>> إلى Map<String, int>
      final result = <String, int>{};
      for (final item in popularReactions) {
        if (item['reaction'] != null && item['count'] != null) {
          result[item['reaction'] as String] = item['count'] as int;
        }
      }
      return result;
    } catch (e) {
      debugPrint('خطأ في تحميل التفاعلات الشائعة: $e');
      return null;
    }
  }

  /// التحقق من وجود تفاعل للمستخدم على رسالة
  bool hasUserReacted(int messageId, int userId, String reactionType) {
    return _allReactions.any((reaction) =>
        reaction.messageId == messageId &&
        reaction.userId == userId &&
        reaction.reactionType == reactionType);
  }

  /// الحصول على تفاعل المستخدم على رسالة
  MessageReaction? getUserReactionOnMessage(int messageId, int userId) {
    return _allReactions.firstWhereOrNull((reaction) =>
        reaction.messageId == messageId && reaction.userId == userId);
  }

  /// تحديث إحصائيات التفاعلات
  void _updateReactionCounts() {
    final counts = <String, int>{};
    for (final reaction in _allReactions) {
      final reactionKey = reaction.reactionType ?? reaction.reaction;
      counts[reactionKey] = (counts[reactionKey] ?? 0) + 1;
    }
    _reactionCounts.assignAll(counts);
  }

  /// تحديث إحصائيات تفاعلات رسالة محددة
  void _updateMessageReactionCounts(List<MessageReaction> reactions) {
    final counts = <String, int>{};
    for (final reaction in reactions) {
      final reactionKey = reaction.reactionType ?? reaction.reaction;
      counts[reactionKey] = (counts[reactionKey] ?? 0) + 1;
    }
    _reactionCounts.assignAll(counts);
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allReactions.where((reaction) {
      // مرشح الرسالة
      if (_messageFilter.value != null && reaction.messageId != _messageFilter.value) {
        return false;
      }

      // مرشح نوع التفاعل
      if (_reactionTypeFilter.value != null && reaction.reactionType != _reactionTypeFilter.value) {
        return false;
      }

      // مرشح المستخدم
      if (_userFilter.value != null && reaction.userId != _userFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredReactions.assignAll(filtered);
  }

  /// تعيين مرشح الرسالة
  void setMessageFilter(int? messageId) {
    _messageFilter.value = messageId;
    _applyFilters();
  }

  /// تعيين مرشح نوع التفاعل
  void setReactionTypeFilter(String? reactionType) {
    _reactionTypeFilter.value = reactionType;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _messageFilter.value = null;
    _reactionTypeFilter.value = null;
    _userFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllReactions();
  }
}
