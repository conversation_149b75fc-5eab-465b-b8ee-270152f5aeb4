import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import '../../constants/app_colors.dart';
import '../../services/unified_permission_service.dart';

/// مربع حوار معاينة الملفات قبل الرفع
/// 
/// TODO: تنفيذ مربع حوار معاينة الملفات
/// - عرض معاينة للملفات قبل الرفع
/// - دعم معاينة أنواع مختلفة من الملفات (صور، PDF، مستندات)
/// - إمكانية تعديل اسم الملف قبل الرفع
/// - إمكانية إلغاء الملفات قبل الرفع
class FilePreviewDialog extends StatefulWidget {
  /// قائمة الملفات المراد معاينتها
  final List<File> files;
  
  /// دالة يتم استدعاؤها عند تأكيد الرفع
  final Function(List<File> files, List<String> fileNames) onConfirm;
  
  /// إنشاء مربع حوار معاينة الملفات
  const FilePreviewDialog({
    super.key,
    required this.files,
    required this.onConfirm,
  });

  @override
  State<FilePreviewDialog> createState() => _FilePreviewDialogState();
}

class _FilePreviewDialogState extends State<FilePreviewDialog> {
  final _permissionService = Get.find<UnifiedPermissionService>();

  /// قائمة أسماء الملفات
  late List<String> _fileNames;

  /// قائمة الملفات المحددة
  late List<File> _selectedFiles;

  @override
  void initState() {
    super.initState();
    _selectedFiles = List.from(widget.files);
    _fileNames = _selectedFiles.map((file) => path.basename(file.path)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('معاينة الملفات قبل الرفع'.tr),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // TODO: تنفيذ واجهة معاينة الملفات
            Text('سيتم تنفيذ واجهة معاينة الملفات هنا'.tr),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text('إلغاء'.tr),
        ),
        if (_permissionService.canUploadFiles())
          ElevatedButton(
            onPressed: () {
              widget.onConfirm(_selectedFiles, _fileNames);
            Get.back();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: Text('رفع'.tr),
        ),
      ],
    );
  }
}
