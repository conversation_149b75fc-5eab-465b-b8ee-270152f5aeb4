import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'professional_reports/screens/debug_workload_report_screen.dart';
import 'professional_reports/screens/workload_distribution_report_screen.dart';

class TestWorkloadReport extends StatelessWidget {
  const TestWorkloadReport({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار تقرير توزيع عبء العمل'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'اختر نوع الاختبار:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton.icon(
              icon: const Icon(Icons.analytics),
              label: const Text('عرض التقرير النهائي'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                Get.to(() => const WorkloadDistributionReportScreen());
              },
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton.icon(
              icon: const Icon(Icons.bug_report),
              label: const Text('فحص البيانات والأخطاء'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                Get.to(() => const DebugWorkloadReportScreen());
              },
            ),
            
            const SizedBox(height: 32),
            
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تعليمات:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. ابدأ بفحص البيانات أولاً للتأكد من وجود مهام ومستخدمين'),
                    Text('2. إذا كانت البيانات موجودة، جرب التقرير النهائي'),
                    Text('3. إذا كان التقرير يظهر أصفار، ارجع لفحص البيانات'),
                    Text('4. تحقق من وجود مهام مرتبطة بالمستخدمين'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}