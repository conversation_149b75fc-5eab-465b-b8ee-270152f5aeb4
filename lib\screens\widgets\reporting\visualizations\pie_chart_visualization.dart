import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../constants/app_styles.dart';

/// تعداد موضع المفتاح
enum ChartLegendPosition {
  top,
  bottom,
  left,
  right,
}

/// مكون المخطط الدائري باستخدام Syncfusion Charts
class PieChartVisualization extends StatelessWidget {
  /// عنوان المخطط
  final String title;

  /// وصف المخطط
  final String? description;

  /// بيانات المخطط
  final List<Map<String, dynamic>> data;

  /// حقل القيمة
  final String valueField;

  /// حقل التسمية
  final String labelField;

  /// إظهار القيم
  final bool showValues;

  /// إظهار النسب المئوية
  final bool showPercentages;

  /// إظهار المفتاح
  final bool showLegend;

  /// موضع المفتاح
  final ChartLegendPosition? legendPosition;

  /// ألوان القطاعات
  final List<Color>? sectionColors;

  /// العرض
  final double? width;

  /// الارتفاع
  final double? height;

  const PieChartVisualization({
    super.key,
    required this.title,
    this.description,
    required this.data,
    required this.valueField,
    required this.labelField,
    this.showValues = true,
    this.showPercentages = true,
    this.showLegend = true,
    this.legendPosition,
    this.sectionColors,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    // تحديد الارتفاع
    final calculatedHeight = height ?? 300.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المخطط
              Text(
                title,
                style: AppStyles.titleMedium,
              ),
              if (description != null && description!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  description!,
                  style: AppStyles.bodySmall,
                ),
              ],
              const SizedBox(height: 16),
              // المخطط
              SizedBox(
                height: calculatedHeight,
                child: _buildChart(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المخطط باستخدام Syncfusion
  Widget _buildChart() {
    return SfCircularChart(
      tooltipBehavior: TooltipBehavior(enable: true),
      legend: Legend(
        isVisible: showLegend,
        position: _getSyncfusionLegendPosition(),
        overflowMode: LegendItemOverflowMode.wrap,
      ),
      series: <CircularSeries>[
        PieSeries<Map<String, dynamic>, String>(
          dataSource: data,
          xValueMapper: (Map<String, dynamic> item, _) => item[labelField].toString(),
          yValueMapper: (Map<String, dynamic> item, _) => _getValueFromData(item),
          pointColorMapper: (Map<String, dynamic> item, int index) => _getSectionColor(index),
          dataLabelSettings: DataLabelSettings(
            isVisible: showValues,
            labelPosition: ChartDataLabelPosition.outside,
            useSeriesColor: true,
            textStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            labelIntersectAction: LabelIntersectAction.shift,
          ),
          enableTooltip: true,
          explode: true,
          explodeIndex: 0,
          explodeOffset: '10%',
          radius: '80%',
        ),
      ],
    );
  }

  /// تحويل موضع المفتاح إلى Syncfusion
  LegendPosition _getSyncfusionLegendPosition() {
    switch (legendPosition) {
      case ChartLegendPosition.top:
        return LegendPosition.top;
      case ChartLegendPosition.left:
        return LegendPosition.left;
      case ChartLegendPosition.right:
        return LegendPosition.right;
      case ChartLegendPosition.bottom:
      default:
        return LegendPosition.bottom;
    }
  }

  /// الحصول على قيمة من البيانات
  double _getValueFromData(Map<String, dynamic> item) {
    final value = item[valueField];
    if (value is int) {
      return value.toDouble();
    } else if (value is double) {
      return value;
    } else if (value is String) {
      return double.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// الحصول على لون القطاع
  Color _getSectionColor(int index) {
    if (sectionColors != null && index < sectionColors!.length) {
      return sectionColors![index];
    }

    // ألوان افتراضية
    final defaultColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
    ];

    return defaultColors[index % defaultColors.length];
  }
}