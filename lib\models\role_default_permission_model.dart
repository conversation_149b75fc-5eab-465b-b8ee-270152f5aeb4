import 'permission_models.dart';

/// نموذج ربط الدور الافتراضي بالصلاحية
class RoleDefaultPermission {
  final int id;
  final int roleId;
  final int permissionId;
  final int createdAt;
  final int? createdBy;

  // العلاقات البرمجية
  final Permission? permission;

  const RoleDefaultPermission({
    required this.id,
    required this.roleId,
    required this.permissionId,
    required this.createdAt,
    this.createdBy,
    this.permission,
  });

  factory RoleDefaultPermission.fromJson(Map<String, dynamic> json) {
    return RoleDefaultPermission(
      id: json['id'] as int,
      roleId: json['roleId'] as int,
      permissionId: json['permissionId'] as int,
      createdAt: json['createdAt'] as int,
      createdBy: json['createdBy'] as int?,
      permission: json['permission'] != null ? Permission.fromJson(json['permission']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'roleId': roleId,
      'permissionId': permissionId,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'permission': permission?.toJson(),
    };
  }
}
