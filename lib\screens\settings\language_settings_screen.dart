import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LanguageSettingsScreen extends StatelessWidget {
  const LanguageSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: Text('language'.tr),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // معلومات عن اللغة
                Text(
                  'اللغة العربية هي اللغة الوحيدة المدعومة في التطبيق',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'التطبيق يدعم اللغة العربية فقط مع دعم كامل للاتجاه من اليمين إلى اليسار.',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 24),

                // أيقونة اللغة العربية
                const Center(
                  child: Icon(
                    Icons.language,
                    size: 64,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 16),
                const Center(
                  child: Text(
                    'العربية',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
