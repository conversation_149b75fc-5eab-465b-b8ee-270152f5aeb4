import 'dart:async';
import 'package:flutter/foundation.dart';

/// فئة لإدارة الوصول المتزامن إلى قاعدة البيانات
///
/// تستخدم لضمان عدم حدوث تضارب في العمليات المتزامنة على قاعدة البيانات
class DatabaseMutex {
  static final DatabaseMutex _instance = DatabaseMutex._internal();
  final Map<String, _ResourceLock> _locks = {};

  // إعدادات مهلة الانتظار
  final Duration _defaultTimeout = const Duration(seconds: 30);
  final Duration _defaultRetryDelay = const Duration(milliseconds: 100);
  final int _maxRetries = 5;

  factory DatabaseMutex() => _instance;

  DatabaseMutex._internal();

  /// الحصول على قفل لمورد معين مع مهلة زمنية
  ///
  /// [resourceName] اسم المورد المراد قفله
  /// [timeout] المهلة الزمنية القصوى للانتظار (اختياري)
  ///
  /// يعيد Future يتم حله عندما يصبح المورد متاحًا
  /// يرمي استثناء TimeoutException إذا تجاوزت المهلة الزمنية
  Future<void> acquire(String resourceName, {Duration? timeout}) async {
    final effectiveTimeout = timeout ?? _defaultTimeout;
    final stopwatch = Stopwatch()..start();

    debugPrint('طلب قفل: $resourceName (مهلة: ${effectiveTimeout.inSeconds}s)');

    // انتظار حتى يصبح المورد متاحًا أو تنتهي المهلة
    while (_locks.containsKey(resourceName)) {
      if (stopwatch.elapsed > effectiveTimeout) {
        debugPrint('انتهت مهلة انتظار القفل: $resourceName');
        throw TimeoutException('انتهت مهلة انتظار القفل: $resourceName', effectiveTimeout);
      }

      try {
        await _locks[resourceName]!.completer.future.timeout(
          effectiveTimeout - stopwatch.elapsed,
          onTimeout: () {
            throw TimeoutException('انتهت مهلة انتظار القفل: $resourceName', effectiveTimeout);
          }
        );
      } catch (e) {
        if (e is TimeoutException) {
          rethrow;
        }
        // إذا كان هناك خطأ آخر، ننتظر قليلاً ثم نحاول مرة أخرى
        await Future.delayed(_defaultRetryDelay);
      }
    }

    // إنشاء قفل جديد
    _locks[resourceName] = _ResourceLock(
      completer: Completer<void>(),
      acquiredAt: DateTime.now(),
    );

    debugPrint('تم الحصول على قفل: $resourceName (بعد ${stopwatch.elapsed.inMilliseconds}ms)');
  }

  /// تحرير قفل لمورد معين
  ///
  /// [resourceName] اسم المورد المراد تحرير قفله
  void release(String resourceName) {
    debugPrint('تحرير قفل: $resourceName');

    if (_locks.containsKey(resourceName)) {
      final lock = _locks[resourceName]!;
      final duration = DateTime.now().difference(lock.acquiredAt);

      // إكمال المستقبل وإزالة القفل
      lock.completer.complete();
      _locks.remove(resourceName);

      debugPrint('تم تحرير قفل: $resourceName (مدة الاستخدام: ${duration.inMilliseconds}ms)');
    }
  }

  /// تنفيذ عملية مع قفل
  ///
  /// [resourceName] اسم المورد المراد قفله
  /// [operation] العملية المراد تنفيذها
  /// [timeout] المهلة الزمنية القصوى للانتظار (اختياري)
  /// [retries] عدد المحاولات في حالة الفشل (اختياري)
  ///
  /// يعيد نتيجة العملية
  Future<T> withLock<T>(
    String resourceName,
    Future<T> Function() operation, {
    Duration? timeout,
    int? retries,
  }) async {
    final effectiveRetries = retries ?? _maxRetries;
    int attempt = 0;

    while (true) {
      attempt++;
      try {
        // الحصول على القفل
        await acquire(resourceName, timeout: timeout);

        // تنفيذ العملية
        return await operation();
      } catch (e) {
        if (e is TimeoutException) {
          if (attempt >= effectiveRetries) {
            debugPrint('فشل الحصول على قفل بعد $attempt محاولات: $resourceName');
            rethrow;
          }

          debugPrint('محاولة $attempt من $effectiveRetries للحصول على قفل: $resourceName');
          await Future.delayed(_defaultRetryDelay * attempt);
          continue;
        }

        // إعادة رمي الاستثناءات الأخرى
        rethrow;
      } finally {
        // تحرير القفل بغض النظر عن نتيجة العملية
        if (_locks.containsKey(resourceName)) {
          release(resourceName);
        }
      }
    }
  }
}

/// فئة داخلية لتمثيل قفل المورد
class _ResourceLock {
  final Completer<void> completer;
  final DateTime acquiredAt;

  _ResourceLock({
    required this.completer,
    required this.acquiredAt,
  });
}
