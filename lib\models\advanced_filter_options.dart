/// خيارات الفلترة المتقدمة للمخططات
class AdvancedFilterOptions {
  /// تصفية حسب القسم
  final List<int>? departmentIds;
  
  /// تصفية حسب المستخدم
  final List<int>? userIds;
  
  /// تصفية حسب حالة المهمة
  final List<int>? statusIds;
  
  /// تصفية حسب أولوية المهمة
  final List<int>? priorityIds;
  
  /// تصفية حسب نوع المهمة
  final List<int>? typeIds;
  
  /// تاريخ البداية
  final DateTime? startDate;
  
  /// تاريخ النهاية
  final DateTime? endDate;
  
  /// نص البحث
  final String? searchText;
  
  /// تصفية المهام المكتملة فقط
  final bool? completedOnly;
  
  /// تصفية المهام المتأخرة فقط
  final bool? overdueOnly;
  
  /// تصفية المهام النشطة فقط
  final bool? activeOnly;

  const AdvancedFilterOptions({
    this.departmentIds,
    this.userIds,
    this.statusIds,
    this.priorityIds,
    this.typeIds,
    this.startDate,
    this.endDate,
    this.searchText,
    this.completedOnly,
    this.overdueOnly,
    this.activeOnly,
  });

  /// إنشاء نسخة جديدة مع تحديث بعض القيم
  AdvancedFilterOptions copyWith({
    List<int>? departmentIds,
    List<int>? userIds,
    List<int>? statusIds,
    List<int>? priorityIds,
    List<int>? typeIds,
    DateTime? startDate,
    DateTime? endDate,
    String? searchText,
    bool? completedOnly,
    bool? overdueOnly,
    bool? activeOnly,
  }) {
    return AdvancedFilterOptions(
      departmentIds: departmentIds ?? this.departmentIds,
      userIds: userIds ?? this.userIds,
      statusIds: statusIds ?? this.statusIds,
      priorityIds: priorityIds ?? this.priorityIds,
      typeIds: typeIds ?? this.typeIds,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchText: searchText ?? this.searchText,
      completedOnly: completedOnly ?? this.completedOnly,
      overdueOnly: overdueOnly ?? this.overdueOnly,
      activeOnly: activeOnly ?? this.activeOnly,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'departmentIds': departmentIds,
      'userIds': userIds,
      'statusIds': statusIds,
      'priorityIds': priorityIds,
      'typeIds': typeIds,
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'searchText': searchText,
      'completedOnly': completedOnly,
      'overdueOnly': overdueOnly,
      'activeOnly': activeOnly,
    };
  }

  /// إنشاء من JSON (alias لـ fromMap للتوافق)
  factory AdvancedFilterOptions.fromJson(Map<String, dynamic> json) {
    return AdvancedFilterOptions.fromMap(json);
  }

  /// إنشاء من Map
  factory AdvancedFilterOptions.fromMap(Map<String, dynamic> map) {
    return AdvancedFilterOptions(
      departmentIds: map['departmentIds']?.cast<int>(),
      userIds: map['userIds']?.cast<int>(),
      statusIds: map['statusIds']?.cast<int>(),
      priorityIds: map['priorityIds']?.cast<int>(),
      typeIds: map['typeIds']?.cast<int>(),
      startDate: map['startDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['startDate'])
          : null,
      endDate: map['endDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['endDate'])
          : null,
      searchText: map['searchText'],
      completedOnly: map['completedOnly'],
      overdueOnly: map['overdueOnly'],
      activeOnly: map['activeOnly'],
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return departmentIds?.isNotEmpty == true ||
           userIds?.isNotEmpty == true ||
           statusIds?.isNotEmpty == true ||
           priorityIds?.isNotEmpty == true ||
           typeIds?.isNotEmpty == true ||
           startDate != null ||
           endDate != null ||
           searchText?.isNotEmpty == true ||
           completedOnly == true ||
           overdueOnly == true ||
           activeOnly == true;
  }

  /// إعادة تعيين جميع الفلاتر
  AdvancedFilterOptions clear() {
    return const AdvancedFilterOptions();
  }

  @override
  String toString() {
    return 'AdvancedFilterOptions('
        'departmentIds: $departmentIds, '
        'userIds: $userIds, '
        'statusIds: $statusIds, '
        'priorityIds: $priorityIds, '
        'typeIds: $typeIds, '
        'startDate: $startDate, '
        'endDate: $endDate, '
        'searchText: $searchText, '
        'completedOnly: $completedOnly, '
        'overdueOnly: $overdueOnly, '
        'activeOnly: $activeOnly'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AdvancedFilterOptions &&
        other.departmentIds == departmentIds &&
        other.userIds == userIds &&
        other.statusIds == statusIds &&
        other.priorityIds == priorityIds &&
        other.typeIds == typeIds &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.searchText == searchText &&
        other.completedOnly == completedOnly &&
        other.overdueOnly == overdueOnly &&
        other.activeOnly == activeOnly;
  }

  @override
  int get hashCode {
    return departmentIds.hashCode ^
        userIds.hashCode ^
        statusIds.hashCode ^
        priorityIds.hashCode ^
        typeIds.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        searchText.hashCode ^
        completedOnly.hashCode ^
        overdueOnly.hashCode ^
        activeOnly.hashCode;
  }
}


