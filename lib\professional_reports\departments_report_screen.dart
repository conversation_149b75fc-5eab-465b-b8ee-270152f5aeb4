import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/department_model.dart';
import 'package:flutter_application_2/services/api/departments_api_service.dart';
import 'departments_report_filter_widget.dart';
import 'report_departments.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class DepartmentsReportScreen extends StatefulWidget {
  const DepartmentsReportScreen({Key? key}) : super(key: key);

  @override
  State<DepartmentsReportScreen> createState() => _DepartmentsReportScreenState();
}

class _DepartmentsReportScreenState extends State<DepartmentsReportScreen> {
  List<Department> allDepartments = [];
  bool loading = false;
  String? pdfPath;
  bool loadingData = true;

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  Future<void> fetchData() async {
    // جلب الأقسام الحقيقية من API
    allDepartments = await DepartmentsApiService().getDepartmentHierarchy();
    setState(() { loadingData = false; });
  }

  Future<void> generateReport({
    List<int>? departmentIds,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? taskTypes,
    List<String>? taskStatuses,
  }) async {
    setState(() { loading = true; pdfPath = null; });
    final doc = await generateDepartmentsPdfReport(
      departmentIds: departmentIds,
      fromDate: fromDate,
      toDate: toDate,
      taskTypes: taskTypes,
      taskStatuses: taskStatuses,
    );
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/departments_report.pdf');
    await file.writeAsBytes(await doc.save());
    setState(() {
      loading = false;
      pdfPath = file.path;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم توليد التقرير بنجاح!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تقرير الإدارات')), 
      body: loading || loadingData
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: DepartmentsReportFilterWidget(
                allDepartments: allDepartments,
                onGenerateReport: generateReport,
              ),
            ),
      bottomNavigationBar: pdfPath != null
          ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('تم حفظ التقرير في: $pdfPath'),
            )
          : null,
    );
  }
}
