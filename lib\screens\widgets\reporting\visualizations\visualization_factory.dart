import 'package:flutter/material.dart';

import 'bar_chart_visualization.dart' as bar_chart;
import 'line_chart_visualization.dart' as line_chart;
import 'pie_chart_visualization.dart' as pie_chart;
import 'table_visualization.dart';
import 'kpi_card_visualization.dart';

/// تعداد أنواع التصور المرئي
enum VisualizationType {
  bar<PERSON><PERSON>,
  lineChart,
  pieChart,
  areaChart,
  table,
  kpiCard,
  gauge<PERSON>hart,
  heatMap,
  radarChart,
  bubbleChart,
  gantt<PERSON>hart,
  treeMap,
  summary,
  custom,
}

/// تعداد اتجاه المخطط
enum ChartOrientation {
  vertical,
  horizontal,
}

/// تعداد موضع المفتاح
enum LegendPosition {
  top,
  bottom,
  left,
  right,
}

/// نموذج التصور المرئي
class ReportVisualization {
  final String id;
  final String title;
  final String? description;
  final VisualizationType type;
  final String? xAxisField;
  final String? xAxisLabel;
  final String? yAxisField;
  final String? yAxisLabel;
  final List<String> dataFields;
  final List<String>? dataLabels;
  final ChartOrientation orientation;
  final bool showValues;
  final bool showLabels;
  final bool showGrid;
  final bool showLegend;
  final LegendPosition? legendPosition;
  final List<Color>? seriesColors;
  final Map<String, dynamic>? settings;

  const ReportVisualization({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.xAxisField,
    this.xAxisLabel,
    this.yAxisField,
    this.yAxisLabel,
    this.dataFields = const [],
    this.dataLabels,
    this.orientation = ChartOrientation.vertical,
    this.showValues = true,
    this.showLabels = true,
    this.showGrid = true,
    this.showLegend = true,
    this.legendPosition,
    this.seriesColors,
    this.settings,
  });
}

// TODO: Implement these visualization types
// import 'area_chart_visualization.dart';
// import 'gauge_chart_visualization.dart';
// import 'heat_map_visualization.dart';
// import 'radar_chart_visualization.dart';
// import 'bubble_chart_visualization.dart';
// import 'gantt_chart_visualization.dart';
// import 'tree_map_visualization.dart';
// import 'summary_visualization.dart';

/// مصنع التصورات المرئية
///
/// يقوم بإنشاء التصور المرئي المناسب بناءً على النوع
class VisualizationFactory {
  /// إنشاء تصور مرئي
  static Widget createVisualization({
    required ReportVisualization visualization,
    required List<Map<String, dynamic>> data,
  }) {
    switch (visualization.type) {
      case VisualizationType.barChart:
        return bar_chart.BarChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: data,
          xAxisField: visualization.xAxisField ?? '',
          xAxisLabel: visualization.xAxisLabel,
          yAxisField: visualization.yAxisField ?? '',
          yAxisLabel: visualization.yAxisLabel,
          orientation: visualization.orientation == ChartOrientation.vertical
              ? bar_chart.ChartOrientation.vertical
              : bar_chart.ChartOrientation.horizontal,
          showValues: visualization.showValues,
          showLabels: visualization.showLabels,
          showGrid: visualization.showGrid,
          showLegend: visualization.showLegend,
          legendPosition: _convertLegendPosition(visualization.legendPosition),
          seriesColors: visualization.seriesColors,
        );

      case VisualizationType.lineChart:
        // Create a list of y-axis fields from the dataFields
        final yAxisFields = <String>[];
        if (visualization.yAxisField != null) {
          yAxisFields.add(visualization.yAxisField!);
        } else if (visualization.dataFields.isNotEmpty) {
          // If yAxisField is not specified, use the first data field
          yAxisFields.add(visualization.dataFields.first);
        }

        return line_chart.LineChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: data,
          xAxisField: visualization.xAxisField ?? '',
          xAxisLabel: visualization.xAxisLabel,
          yAxisFields: yAxisFields,
          yAxisLabel: visualization.yAxisLabel,
          showDots: true, // Default value
          showArea: false, // Default value
          showLabels: visualization.showLabels,
          showGrid: visualization.showGrid,
          showLegend: visualization.showLegend,
          legendPosition: _convertLegendPositionForLineChart(visualization.legendPosition),
          seriesColors: visualization.seriesColors,
        );

      case VisualizationType.pieChart:
        // Get the label and value fields from dataFields if available
        String labelField = '';
        String valueField = '';

        if (visualization.dataFields.length >= 2) {
          labelField = visualization.dataFields[0];
          valueField = visualization.dataFields[1];
        } else if (visualization.dataFields.length == 1) {
          // If only one field is provided, use it as the value field
          // and use the first available key in the data as the label
          valueField = visualization.dataFields[0];

          // Find a suitable label field from the data
          if (data.isNotEmpty && data.first.isNotEmpty) {
            // Use the first key that's not the value field
            for (var key in data.first.keys) {
              if (key != valueField) {
                labelField = key;
                break;
              }
            }

            // If we couldn't find another field, use the same field for both
            if (labelField.isEmpty) {
              labelField = valueField;
            }
          }
        }

        // Ensure we have valid fields
        if (labelField.isEmpty && data.isNotEmpty && data.first.isNotEmpty) {
          labelField = data.first.keys.first;
        }

        if (valueField.isEmpty && data.isNotEmpty && data.first.isNotEmpty) {
          // Try to find a numeric field for the value
          for (var key in data.first.keys) {
            final value = data.first[key];
            if (value is num || (value is String && double.tryParse(value) != null)) {
              valueField = key;
              break;
            }
          }

          // If no numeric field found, use the first field
          if (valueField.isEmpty) {
            valueField = data.first.keys.first;
          }
        }

        return pie_chart.PieChartVisualization(
          title: visualization.title,
          description: visualization.description,
          data: data,
          labelField: labelField,
          valueField: valueField,
          showValues: visualization.showValues,
          showPercentages: true,
          showLegend: visualization.showLegend,
          legendPosition: _convertLegendPositionForPieChart(visualization.legendPosition),
          sectionColors: visualization.seriesColors,
        );

      case VisualizationType.table:
        // Get fields from dataFields or from the first data item
        final fields = visualization.dataFields.isNotEmpty
            ? visualization.dataFields
            : (data.isNotEmpty ? data.first.keys.toList() : <String>[]);

        return TableVisualization(
          title: visualization.title,
          description: visualization.description,
          data: data,
          fields: fields,
          fieldLabels: visualization.dataLabels,
          showHeader: true,
          showAlternatingRows: true,
          showBorders: true,
          showRowNumbers: true,
          rowsPerPage: 10,
        );

      case VisualizationType.kpiCard:
        // KPI card expects a single data item, not a list
        final kpiData = data.isNotEmpty ? data.first : <String, dynamic>{};

        // Get the value field from dataFields if available
        String valueField = '';
        if (visualization.dataFields.isNotEmpty) {
          valueField = visualization.dataFields.first;
        }

        return KpiCardVisualization(
          title: visualization.title,
          description: visualization.description,
          data: kpiData,
          valueField: valueField,
          format: visualization.settings?['format'] as String?,
          color: visualization.seriesColors?.isNotEmpty == true
              ? visualization.seriesColors!.first
              : Colors.blue,
          icon: Icons.analytics,
        );

      case VisualizationType.areaChart:
      case VisualizationType.gaugeChart:
      case VisualizationType.heatMap:
      case VisualizationType.radarChart:
      case VisualizationType.bubbleChart:
      case VisualizationType.ganttChart:
      case VisualizationType.treeMap:
      case VisualizationType.summary:
        // These visualization types are not implemented yet
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.construction,
                  size: 48,
                  color: Colors.orange,
                ),
                const SizedBox(height: 16),
                Text(
                  'هذا النوع من التصور المرئي غير متاح حاليًا: ${visualization.type.toString().split('.').last}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'سيتم تنفيذه قريبًا',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );

      case VisualizationType.custom:
        // للتصورات المرئية المخصصة، يمكن إضافة منطق خاص هنا
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'تصور مرئي مخصص: ${visualization.title}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );

      default:
        // في حالة عدم التعرف على نوع التصور المرئي
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'نوع تصور مرئي غير معروف: ${visualization.type}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),
        );
    }
  }

  /// تحويل موضع المفتاح للمخطط الشريطي
  static bar_chart.ChartLegendPosition? _convertLegendPosition(LegendPosition? position) {
    if (position == null) return null;
    switch (position) {
      case LegendPosition.top:
        return bar_chart.ChartLegendPosition.top;
      case LegendPosition.bottom:
        return bar_chart.ChartLegendPosition.bottom;
      case LegendPosition.left:
        return bar_chart.ChartLegendPosition.left;
      case LegendPosition.right:
        return bar_chart.ChartLegendPosition.right;
    }
  }

  /// تحويل موضع المفتاح للمخطط الخطي
  static line_chart.ChartLegendPosition? _convertLegendPositionForLineChart(LegendPosition? position) {
    if (position == null) return null;
    switch (position) {
      case LegendPosition.top:
        return line_chart.ChartLegendPosition.top;
      case LegendPosition.bottom:
        return line_chart.ChartLegendPosition.bottom;
      case LegendPosition.left:
        return line_chart.ChartLegendPosition.left;
      case LegendPosition.right:
        return line_chart.ChartLegendPosition.right;
    }
  }

  /// تحويل موضع المفتاح للمخطط الدائري
  static pie_chart.ChartLegendPosition? _convertLegendPositionForPieChart(LegendPosition? position) {
    if (position == null) return null;
    switch (position) {
      case LegendPosition.top:
        return pie_chart.ChartLegendPosition.top;
      case LegendPosition.bottom:
        return pie_chart.ChartLegendPosition.bottom;
      case LegendPosition.left:
        return pie_chart.ChartLegendPosition.left;
      case LegendPosition.right:
        return pie_chart.ChartLegendPosition.right;
    }
  }
}
