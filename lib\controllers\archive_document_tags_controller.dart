import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/archive_document_tag_models.dart';
import '../services/api/archive_document_tags_api_service.dart';

/// متحكم روابط مستندات الأرشيف بالعلامات
class ArchiveDocumentTagsController extends GetxController {
  final ArchiveDocumentTagsApiService _apiService = ArchiveDocumentTagsApiService();

  // قوائم الروابط
  final RxList<ArchiveDocumentTag> _allDocumentTags = <ArchiveDocumentTag>[].obs;
  final RxList<ArchiveDocumentTag> _filteredDocumentTags = <ArchiveDocumentTag>[].obs;

  // الربط الحالي
  final Rx<ArchiveDocumentTag?> _currentDocumentTag = Rx<ArchiveDocumentTag?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _documentFilter = Rx<int?>(null);
  final Rx<int?> _tagFilter = Rx<int?>(null);

  // الإحصائيات
  final RxMap<String, dynamic> _statistics = <String, dynamic>{}.obs;

  // Getters
  List<ArchiveDocumentTag> get allDocumentTags => _allDocumentTags;
  List<ArchiveDocumentTag> get filteredDocumentTags => _filteredDocumentTags;
  ArchiveDocumentTag? get currentDocumentTag => _currentDocumentTag.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get documentFilter => _documentFilter.value;
  int? get tagFilter => _tagFilter.value;
  Map<String, dynamic> get statistics => _statistics;

  @override
  void onInit() {
    super.onInit();
    loadAllDocumentTags();
  }

  /// تحميل جميع روابط المستندات والعلامات
  Future<void> loadAllDocumentTags() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentTags = await _apiService.getAllDocumentTags();
      _allDocumentTags.assignAll(documentTags);
      _applyFilters();
      debugPrint('تم تحميل ${documentTags.length} ربط مستند-علامة');
    } catch (e) {
      _error.value = 'خطأ في تحميل روابط المستندات والعلامات: $e';
      debugPrint('خطأ في تحميل روابط المستندات والعلامات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على علامات مستند محدد
  Future<List<ArchiveDocumentTag>> getDocumentTags(int documentId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final documentTags = await _apiService.getDocumentTags(documentId);
      debugPrint('تم تحميل ${documentTags.length} علامة للمستند $documentId');
      return documentTags;
    } catch (e) {
      _error.value = 'خطأ في تحميل علامات المستند: $e';
      debugPrint('خطأ في تحميل علامات المستند: $e');
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مستندات علامة محددة
  Future<List<ArchiveDocumentTag>> getTagDocuments(int tagId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tagDocuments = await _apiService.getTagDocuments(tagId);
      debugPrint('تم تحميل ${tagDocuments.length} مستند للعلامة $tagId');
      return tagDocuments;
    } catch (e) {
      _error.value = 'خطأ في تحميل مستندات العلامة: $e';
      debugPrint('خطأ في تحميل مستندات العلامة: $e');
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  /// ربط مستند بعلامة
  Future<bool> linkDocumentTag(int documentId, int tagId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.linkDocumentTag(documentId, tagId);
      if (success) {
        // إضافة الربط الجديد إلى القائمة المحلية
        final newDocumentTag = ArchiveDocumentTag(
          documentId: documentId,
          tagId: tagId,
        );
        _allDocumentTags.add(newDocumentTag);
        _applyFilters();
        debugPrint('تم ربط المستند $documentId بالعلامة $tagId');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في ربط المستند بالعلامة: $e';
      debugPrint('خطأ في ربط المستند بالعلامة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء ربط مستند بعلامة
  Future<bool> unlinkDocumentTag(int documentId, int tagId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.unlinkDocumentTag(documentId, tagId);
      if (success) {
        // إزالة الربط من القائمة المحلية
        _allDocumentTags.removeWhere((dt) => 
          dt.documentId == documentId && dt.tagId == tagId);
        _applyFilters();
        debugPrint('تم إلغاء ربط المستند $documentId بالعلامة $tagId');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إلغاء ربط المستند بالعلامة: $e';
      debugPrint('خطأ في إلغاء ربط المستند بالعلامة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// ربط مستند بعدة علامات
  Future<bool> linkDocumentMultipleTags(int documentId, List<int> tagIds) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.linkDocumentMultipleTags(documentId, tagIds);
      if (success) {
        // إضافة الروابط الجديدة إلى القائمة المحلية
        for (final tagId in tagIds) {
          final newDocumentTag = ArchiveDocumentTag(
            documentId: documentId,
            tagId: tagId,
          );
          // تجنب الإضافة المكررة
          if (!_allDocumentTags.any((dt) => 
            dt.documentId == documentId && dt.tagId == tagId)) {
            _allDocumentTags.add(newDocumentTag);
          }
        }
        _applyFilters();
        debugPrint('تم ربط المستند $documentId بـ ${tagIds.length} علامة');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في ربط المستند بعدة علامات: $e';
      debugPrint('خطأ في ربط المستند بعدة علامات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final stats = await _apiService.getStatistics();
      if (stats != null) {
        _statistics.assignAll(stats);
        debugPrint('تم تحميل إحصائيات روابط المستندات والعلامات');
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل الإحصائيات: $e';
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allDocumentTags.where((documentTag) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        // يمكن البحث في معرف المستند أو معرف العلامة
        if (!documentTag.documentId.toString().contains(query) &&
            !documentTag.tagId.toString().contains(query)) {
          return false;
        }
      }

      // مرشح المستند
      if (_documentFilter.value != null && 
          documentTag.documentId != _documentFilter.value) {
        return false;
      }

      // مرشح العلامة
      if (_tagFilter.value != null && 
          documentTag.tagId != _tagFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredDocumentTags.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المستند
  void setDocumentFilter(int? documentId) {
    _documentFilter.value = documentId;
    _applyFilters();
  }

  /// تعيين مرشح العلامة
  void setTagFilter(int? tagId) {
    _tagFilter.value = tagId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _documentFilter.value = null;
    _tagFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllDocumentTags();
    await loadStatistics();
  }

  /// البحث في روابط المستندات والعلامات
  Future<void> search(String query) async {
    if (query.isEmpty) {
      await loadAllDocumentTags();
      return;
    }

    _isLoading.value = true;
    _error.value = '';

    try {
      final results = await _apiService.searchDocumentTags(query);
      _allDocumentTags.assignAll(results);
      _applyFilters();
      debugPrint('تم العثور على ${results.length} نتيجة للبحث: "$query"');
    } catch (e) {
      _error.value = 'خطأ في البحث: $e';
      debugPrint('خطأ في البحث: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير البيانات
  Future<String?> exportData(String format) async {
    try {
      return await _apiService.exportDocumentTags(format);
    } catch (e) {
      _error.value = 'خطأ في تصدير البيانات: $e';
      debugPrint('خطأ في تصدير البيانات: $e');
      return null;
    }
  }

  /// استيراد البيانات
  Future<bool> importData(String data, String format) async {
    try {
      final success = await _apiService.importDocumentTags(data, format);
      if (success) {
        await loadAllDocumentTags();
      }
      return success;
    } catch (e) {
      _error.value = 'خطأ في استيراد البيانات: $e';
      debugPrint('خطأ في استيراد البيانات: $e');
      return false;
    }
  }
}
