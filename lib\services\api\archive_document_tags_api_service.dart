import 'package:flutter/foundation.dart';
import '../../models/archive_document_tag_models.dart';
import 'api_service.dart';

/// خدمة API لربط مستندات الأرشيف بالعلامات - متطابقة مع ASP.NET Core API
class ArchiveDocumentTagsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع روابط المستندات والعلامات
  Future<List<ArchiveDocumentTag>> getAllDocumentTags() async {
    try {
      final response = await _apiService.get('/api/ArchiveDocumentTags');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final List<dynamic> jsonList = response.body as List<dynamic>;
        return jsonList.map((json) => ArchiveDocumentTag.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على روابط المستندات والعلامات: $e');
      rethrow;
    }
  }

  /// الحصول على علامات مستند محدد
  Future<List<ArchiveDocumentTag>> getDocumentTags(int documentId) async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/document/$documentId');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final List<dynamic> jsonList = response.body as List<dynamic>;
        return jsonList.map((json) => ArchiveDocumentTag.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على علامات المستند: $e');
      rethrow;
    }
  }

  /// الحصول على مستندات علامة محددة
  Future<List<ArchiveDocumentTag>> getTagDocuments(int tagId) async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/tag/$tagId');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final List<dynamic> jsonList = response.body as List<dynamic>;
        return jsonList.map((json) => ArchiveDocumentTag.fromJson(json as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات العلامة: $e');
      rethrow;
    }
  }

  /// ربط مستند بعلامة
  Future<bool> linkDocumentTag(int documentId, int tagId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocumentTags/link',
        {
          'documentId': documentId,
          'tagId': tagId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في ربط المستند بالعلامة: $e');
      return false;
    }
  }

  /// إلغاء ربط مستند بعلامة
  Future<bool> unlinkDocumentTag(int documentId, int tagId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocumentTags/unlink',
        {
          'documentId': documentId,
          'tagId': tagId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء ربط المستند بالعلامة: $e');
      return false;
    }
  }

  /// ربط مستند بعدة علامات
  Future<bool> linkDocumentMultipleTags(int documentId, List<int> tagIds) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocumentTags/link-multiple',
        {
          'documentId': documentId,
          'tagIds': tagIds,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في ربط المستند بعدة علامات: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات روابط المستندات والعلامات
  Future<Map<String, dynamic>?> getStatistics() async {
    try {
      final response = await _apiService.get('/api/ArchiveDocumentTags/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الروابط: $e');
      return null;
    }
  }

  /// إنشاء ربط جديد
  Future<ArchiveDocumentTag?> createDocumentTag(ArchiveDocumentTag documentTag) async {
    try {
      final response = await _apiService.post('/ArchiveDocumentTags/link', documentTag.toJson());
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ArchiveDocumentTag.fromJson(response.body as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء ربط المستند بالعلامة: $e');
      return null;
    }
  }

  /// حذف ربط
  Future<bool> deleteDocumentTag(int documentId, int tagId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocumentTags/unlink',
        {
          'documentId': documentId,
          'tagId': tagId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف ربط المستند بالعلامة: $e');
      return false;
    }
  }

  /// البحث في روابط المستندات والعلامات
  Future<List<ArchiveDocumentTag>> searchDocumentTags(String query) async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/search?searchTerm=$query');
      return _apiService.handleListResponse<ArchiveDocumentTag>(
        response,
        (json) => ArchiveDocumentTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن روابط المستندات والعلامات: $e');
      rethrow;
    }
  }

  /// الحصول على روابط المستندات والعلامات مع التصفية
  Future<List<ArchiveDocumentTag>> getFilteredDocumentTags({
    int? documentId,
    int? tagId,
    int? limit,
    int? offset,
  }) async {
    try {
      var url = '/ArchiveDocumentTags';
      var params = <String, String>{};

      if (documentId != null) params['documentId'] = documentId.toString();
      if (tagId != null) params['tagId'] = tagId.toString();
      if (limit != null) params['limit'] = limit.toString();
      if (offset != null) params['offset'] = offset.toString();

      if (params.isNotEmpty) {
        url = '$url?${params.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }

      final response = await _apiService.get(url);
      return _apiService.handleListResponse<ArchiveDocumentTag>(
        response,
        (json) => ArchiveDocumentTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على روابط المستندات والعلامات المصفاة: $e');
      rethrow;
    }
  }

  /// تحديث ربط مستند بعلامة
  Future<bool> updateDocumentTag(ArchiveDocumentTag documentTag) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocumentTags/${documentTag.documentId}/${documentTag.tagId}',
        documentTag.toJson(),
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث ربط المستند بالعلامة: $e');
      return false;
    }
  }

  /// الحصول على عدد المستندات لكل علامة
  Future<Map<int, int>> getDocumentCountPerTag() async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/count-per-tag');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = Map<String, dynamic>.from(response.body as Map);
        return data.map((key, value) => MapEntry(int.parse(key), value as int));
      }
      return {};
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد المستندات لكل علامة: $e');
      return {};
    }
  }

  /// الحصول على عدد العلامات لكل مستند
  Future<Map<int, int>> getTagCountPerDocument() async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/count-per-document');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final data = Map<String, dynamic>.from(response.body as Map);
        return data.map((key, value) => MapEntry(int.parse(key), value as int));
      }
      return {};
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد العلامات لكل مستند: $e');
      return {};
    }
  }

  /// تصدير روابط المستندات والعلامات
  Future<String?> exportDocumentTags(String format) async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير روابط المستندات والعلامات: $e');
      return null;
    }
  }

  /// استيراد روابط المستندات والعلامات
  Future<bool> importDocumentTags(String data, String format) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocumentTags/import',
        {
          'data': data,
          'format': format,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد روابط المستندات والعلامات: $e');
      return false;
    }
  }

  /// مزامنة روابط المستندات والعلامات
  Future<bool> syncDocumentTags() async {
    try {
      final response = await _apiService.post('/ArchiveDocumentTags/sync', {});
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مزامنة روابط المستندات والعلامات: $e');
      return false;
    }
  }

  /// التحقق من صحة روابط المستندات والعلامات
  Future<Map<String, dynamic>?> validateDocumentTags() async {
    try {
      final response = await _apiService.get('/ArchiveDocumentTags/validate');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة روابط المستندات والعلامات: $e');
      return null;
    }
  }
}
