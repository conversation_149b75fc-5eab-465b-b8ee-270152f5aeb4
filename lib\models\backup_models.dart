import 'user_model.dart';

/// نموذج النسخة الاحتياطية - متطابق مع ASP.NET Core API
class Backup {
  final int id;
  final String fileName;
  final String filePath;
  final int fileSize; // بالبايت
  final int createdBy;
  final int createdAt;
  final String? description;
  final bool isAutoBackup;
  final bool isRestored;
  final int? restoredAt;
  final int? restoredBy;

  // Navigation properties
  final User? createdByNavigation;
  final User? restoredByNavigation;

  const Backup({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.createdBy,
    required this.createdAt,
    this.description,
    this.isAutoBackup = false,
    this.isRestored = false,
    this.restoredAt,
    this.restoredBy,
    this.createdByNavigation,
    this.restoredByNavigation,
  });

  factory Backup.fromJson(Map<String, dynamic> json) {
    return Backup(
      id: json['id'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: json['fileSize'] as int,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      description: json['description'] as String?,
      isAutoBackup: json['isAutoBackup'] as bool? ?? false,
      isRestored: json['isRestored'] as bool? ?? false,
      restoredAt: json['restoredAt'] as int?,
      restoredBy: json['restoredBy'] as int?,
      createdByNavigation: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      restoredByNavigation: json['restoredByNavigation'] != null
          ? User.fromJson(json['restoredByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'description': description,
      'isAutoBackup': isAutoBackup,
      'isRestored': isRestored,
      'restoredAt': restoredAt,
      'restoredBy': restoredBy,
    };
  }

  Backup copyWith({
    int? id,
    String? fileName,
    String? filePath,
    int? fileSize,
    int? createdBy,
    int? createdAt,
    String? description,
    bool? isAutoBackup,
    bool? isRestored,
    int? restoredAt,
    int? restoredBy,
    User? createdByNavigation,
    User? restoredByNavigation,
  }) {
    return Backup(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      description: description ?? this.description,
      isAutoBackup: isAutoBackup ?? this.isAutoBackup,
      isRestored: isRestored ?? this.isRestored,
      restoredAt: restoredAt ?? this.restoredAt,
      restoredBy: restoredBy ?? this.restoredBy,
      createdByNavigation: createdByNavigation ?? this.createdByNavigation,
      restoredByNavigation: restoredByNavigation ?? this.restoredByNavigation,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الاستعادة كـ DateTime
  DateTime? get restoredAtDateTime => restoredAt != null
      ? DateTime.fromMillisecondsSinceEpoch(restoredAt! * 1000)
      : null;

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// الحصول على نوع النسخة الاحتياطية
  String get backupType => isAutoBackup ? 'تلقائية' : 'يدوية';

  /// الحصول على حالة النسخة الاحتياطية
  String get status => isRestored ? 'تم الاستعادة' : 'متاحة';

  @override
  String toString() {
    return 'Backup(id: $id, fileName: $fileName, size: $fileSizeFormatted, type: $backupType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Backup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء نسخة احتياطية
class CreateBackupRequest {
  final String? description;
  final bool isAutoBackup;

  const CreateBackupRequest({
    this.description,
    this.isAutoBackup = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'isAutoBackup': isAutoBackup,
    };
  }
}

/// نموذج طلب استعادة نسخة احتياطية
class RestoreBackupRequest {
  final int backupId;
  final bool confirmRestore;

  const RestoreBackupRequest({
    required this.backupId,
    this.confirmRestore = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'backupId': backupId,
      'confirmRestore': confirmRestore,
    };
  }
}

/// إحصائيات النسخ الاحتياطية
class BackupStatistics {
  final int totalBackups;
  final int autoBackups;
  final int manualBackups;
  final int restoredBackups;
  final int totalSize;
  final DateTime? lastBackupDate;
  final DateTime? lastRestoreDate;

  const BackupStatistics({
    required this.totalBackups,
    required this.autoBackups,
    required this.manualBackups,
    required this.restoredBackups,
    required this.totalSize,
    this.lastBackupDate,
    this.lastRestoreDate,
  });

  factory BackupStatistics.fromJson(Map<String, dynamic> json) {
    return BackupStatistics(
      totalBackups: json['totalBackups'] as int,
      autoBackups: json['autoBackups'] as int,
      manualBackups: json['manualBackups'] as int,
      restoredBackups: json['restoredBackups'] as int,
      totalSize: json['totalSize'] as int,
      lastBackupDate: json['lastBackupDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastBackupDate'] as int) * 1000)
          : null,
      lastRestoreDate: json['lastRestoreDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastRestoreDate'] as int) * 1000)
          : null,
    );
  }

  /// الحصول على الحجم الإجمالي بصيغة قابلة للقراءة
  String get totalSizeFormatted {
    if (totalSize < 1024) {
      return '$totalSize B';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  @override
  String toString() {
    return 'BackupStatistics(total: $totalBackups, auto: $autoBackups, manual: $manualBackups, size: $totalSizeFormatted)';
  }
}
