import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة تصدير المخططات
/// 
/// توفر وظائف تصدير المخططات بصيغ مختلفة مثل PDF و Excel و CSV
class ChartExportService extends GetxService {
  
  /// تصدير المخطط إلى PDF
  Future<String?> exportToPdf(
    String chartId, {
    required String title,
    required Map<String, dynamic> data,
    required String chartType,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();
      
      // إضافة صفحة للمخطط
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // عنوان المخطط
                pw.Text(
                  title,
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 20),
                
                // معلومات الفترة الزمنية
                pw.Text(
                  'الفترة: ${_formatDate(startDate)} - ${_formatDate(endDate)}',
                  style: const pw.TextStyle(fontSize: 14),
                ),
                pw.SizedBox(height: 20),
                
                // بيانات المخطط
                _buildPdfDataTable(data, chartType),
              ],
            );
          },
        ),
      );
      
      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/chart_${chartId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());
      
      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير PDF: $e');
      return null;
    }
  }
  
  /// تصدير المخطط إلى Excel
  Future<String?> exportToExcel({
    required String title,
    required Map<String, dynamic> data,
    required String chartType,
  }) async {
    try {
      // إنشاء ملف Excel
      final excel = Excel.createExcel();
      final sheet = excel['البيانات'];
      
      // إضافة العنوان
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(title);
      sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
        bold: true,
        fontSize: 16,
      );
      
      // إضافة البيانات
      int row = 3;
      if (data.containsKey('series')) {
        // بيانات خطية أو مساحية
        final series = data['series'] as List;
        sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('التاريخ');
        
        // إضافة عناوين السلاسل
        for (int i = 0; i < series.length; i++) {
          final seriesName = series[i]['name'];
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: i + 1, rowIndex: row - 1)).value = seriesName;
        }
        
        row++;
        
        // إضافة البيانات
        if (series.isNotEmpty) {
          final firstSeries = series[0]['data'] as List;
          for (int i = 0; i < firstSeries.length; i++) {
            final point = firstSeries[i];
            final date = DateTime.fromMillisecondsSinceEpoch(point['x']);
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row - 1)).value = TextCellValue(_formatDate(date));
            
            for (int j = 0; j < series.length; j++) {
              final seriesData = series[j]['data'] as List;
              if (i < seriesData.length) {
                sheet.cell(CellIndex.indexByColumnRow(columnIndex: j + 1, rowIndex: row - 1)).value = seriesData[i]['y'];
              }
            }
            row++;
          }
        }
      } else {
        // بيانات دائرية أو شريطية
        sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('الاسم');
        sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue('القيمة');
        row++;
        
        data.forEach((key, value) {
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row - 1)).value = TextCellValue(key);
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row - 1)).value = DoubleCellValue(value.toDouble());
          row++;
        });
      }
      
      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/chart_${DateTime.now().millisecondsSinceEpoch}.xlsx');
      await file.writeAsBytes(excel.encode()!);
      
      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير Excel: $e');
      return null;
    }
  }
  
  /// تصدير المخطط إلى CSV
  Future<String?> exportToCsv({
    required String title,
    required Map<String, dynamic> data,
    required String chartType,
  }) async {
    try {
      List<List<dynamic>> csvData = [];
      
      // إضافة العنوان
      csvData.add([title]);
      csvData.add([]); // سطر فارغ
      
      if (data.containsKey('series')) {
        // بيانات خطية أو مساحية
        final series = data['series'] as List;
        List<String> headers = ['التاريخ'];
        
        // إضافة عناوين السلاسل
        for (final s in series) {
          headers.add(s['name']);
        }
        csvData.add(headers);
        
        // إضافة البيانات
        if (series.isNotEmpty) {
          final firstSeries = series[0]['data'] as List;
          for (int i = 0; i < firstSeries.length; i++) {
            List<dynamic> row = [];
            final point = firstSeries[i];
            final date = DateTime.fromMillisecondsSinceEpoch(point['x']);
            row.add(_formatDate(date));
            
            for (final s in series) {
              final seriesData = s['data'] as List;
              if (i < seriesData.length) {
                row.add(seriesData[i]['y']);
              } else {
                row.add(0);
              }
            }
            csvData.add(row);
          }
        }
      } else {
        // بيانات دائرية أو شريطية
        csvData.add(['الاسم', 'القيمة']);
        
        data.forEach((key, value) {
          csvData.add([key, value]);
        });
      }
      
      // تحويل إلى CSV
      final csvString = const ListToCsvConverter().convert(csvData);
      
      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/chart_${DateTime.now().millisecondsSinceEpoch}.csv');
      await file.writeAsString(csvString);
      
      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير CSV: $e');
      return null;
    }
  }
  
  /// بناء جدول البيانات في PDF
  pw.Widget _buildPdfDataTable(Map<String, dynamic> data, String chartType) {
    if (data.containsKey('series')) {
      // بيانات خطية أو مساحية
      final series = data['series'] as List;
      List<List<String>> tableData = [];
      
      // إضافة العناوين
      List<String> headers = ['التاريخ'];
      for (final s in series) {
        headers.add(s['name']);
      }
      tableData.add(headers);
      
      // إضافة البيانات
      if (series.isNotEmpty) {
        final firstSeries = series[0]['data'] as List;
        for (int i = 0; i < firstSeries.length; i++) {
          List<String> row = [];
          final point = firstSeries[i];
          final date = DateTime.fromMillisecondsSinceEpoch(point['x']);
          row.add(_formatDate(date));
          
          for (final s in series) {
            final seriesData = s['data'] as List;
            if (i < seriesData.length) {
              row.add(seriesData[i]['y'].toString());
            } else {
              row.add('0');
            }
          }
          tableData.add(row);
        }
      }
      
      return pw.TableHelper.fromTextArray(
        data: tableData,
        headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        cellAlignment: pw.Alignment.center,
      );
    } else {
      // بيانات دائرية أو شريطية
      List<List<String>> tableData = [
        ['الاسم', 'القيمة']
      ];
      
      data.forEach((key, value) {
        tableData.add([key, value.toString()]);
      });
      
      return pw.TableHelper.fromTextArray(
        data: tableData,
        headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        cellAlignment: pw.Alignment.center,
      );
    }
  }
  
  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
