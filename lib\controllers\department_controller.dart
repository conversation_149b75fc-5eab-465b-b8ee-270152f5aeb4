import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/department_model.dart';
import '../services/api/departments_api_service.dart';
import '../services/cache/department_cache_service.dart';
import '../utils/department_error_handler.dart';

/// متحكم الأقسام
class DepartmentController extends GetxController {
  final DepartmentsApiService _apiService = DepartmentsApiService();

  // قوائم الأقسام
  final RxList<Department> _allDepartments = <Department>[].obs;
  final RxList<Department> _activeDepartments = <Department>[].obs;
  final RxList<DepartmentStats> _departmentStats = <DepartmentStats>[].obs;

  // القسم الحالي
  final Rx<Department?> _currentDepartment = Rx<Department?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<Department> get allDepartments => _allDepartments;
  List<Department> get activeDepartments => _activeDepartments;
  List<Department> get departments => _allDepartments; // Alias for compatibility
  List<DepartmentStats> get departmentStats => _departmentStats;
  Department? get currentDepartment => _currentDepartment.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllDepartments();
  }

  /// تحميل جميع الأقسام مع دعم التخزين المؤقت
  Future<void> loadAllDepartments({bool forceRefresh = false}) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاولة استخدام التخزين المؤقت أولاً
      if (!forceRefresh) {
        final cachedDepartments = await DepartmentCacheService.getCachedAllDepartments();
        if (cachedDepartments != null) {
          _allDepartments.assignAll(cachedDepartments);
          _updateActiveDepartments();
          debugPrint('تم تحميل ${cachedDepartments.length} قسم من التخزين المؤقت');
          _isLoading.value = false;
          return;
        }
      }

      // تحميل من API
      final departments = await _apiService.getAllDepartments();
      _allDepartments.assignAll(departments);
      _updateActiveDepartments();

      // حفظ في التخزين المؤقت
      await DepartmentCacheService.cacheAllDepartments(departments);

      debugPrint('تم تحميل ${departments.length} قسم من API');
    } catch (e) {
      _error.value = 'خطأ في تحميل الأقسام: $e';
      DepartmentErrorHandler.handleApiError(e, context: 'تحميل الأقسام');
      debugPrint('خطأ في تحميل الأقسام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل قسم بواسطة المعرف
  Future<void> loadDepartmentById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final department = await _apiService.getDepartmentById(id);
      if (department != null) {
        _currentDepartment.value = department;
        debugPrint('تم تحميل القسم: ${department.name}');
      } else {
        _error.value = 'القسم غير موجود';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل القسم: $e';
      debugPrint('خطأ في تحميل القسم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل إحصائيات الأقسام
  Future<void> loadDepartmentStats() async {
    try {
      // حاليا لا توجد API للإحصائيات، سيتم حسابها محليا
      final stats = <DepartmentStats>[];
      for (final dept in _allDepartments) {
        // يمكن إضافة حساب الإحصائيات هنا لاحقا
        final stat = DepartmentStats(
          departmentId: dept.id,
          departmentName: dept.name,
          totalEmployees: 0,
          activeEmployees: 0,
          totalTasks: 0,
          completedTasks: 0,
          pendingTasks: 0,
          overdueTasks: 0,
        );
        stats.add(stat);
      }
      _departmentStats.assignAll(stats);
      debugPrint('تم تحميل إحصائيات ${stats.length} قسم');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات الأقسام: $e');
    }
  }

  /// البحث في الأقسام
  void searchDepartments(String query) {
    _searchQuery.value = query;
    _updateActiveDepartments();
  }

  /// إنشاء قسم جديد مع التحقق المحسن
  Future<bool> createDepartment({
    required String name,
    String? description,
    int? managerId,
    int? parentId,
    bool isActive = true,
  }) async {
    // التحقق من صحة البيانات
    final nameError = DepartmentErrorHandler.validateDepartmentName(name);
    if (nameError != null) {
      DepartmentErrorHandler.handleValidationError('اسم القسم', nameError);
      return false;
    }

    final descriptionError = DepartmentErrorHandler.validateDepartmentDescription(description);
    if (descriptionError != null) {
      DepartmentErrorHandler.handleValidationError('وصف القسم', descriptionError);
      return false;
    }

    _isLoading.value = true;
    _error.value = '';

    try {
      final department = Department(
        id: 0, // سيتم تعيينه من الخادم
        name: name.trim(),
        description: description?.trim(),
        managerId: managerId,
        parentId: parentId,
        isActive: isActive,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final newDepartment = await _apiService.createDepartment(department);
      // newDepartment لا يمكن أن تكون null هنا
      _allDepartments.add(newDepartment);
      _currentDepartment.value = newDepartment;
      _updateActiveDepartments();

      // إلغاء صلاحية التخزين المؤقت
      await DepartmentCacheService.clearCache();

      DepartmentErrorHandler.showSuccess('تم إنشاء القسم "${newDepartment.name}" بنجاح');
      debugPrint('تم إنشاء القسم: ${newDepartment.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء القسم: $e';
      DepartmentErrorHandler.handleApiError(e, context: 'إنشاء القسم');
      debugPrint('خطأ في إنشاء القسم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث قسم
  Future<bool> updateDepartment({
    required int id,
    String? name,
    String? description,
    int? managerId,
    bool? isActive,
  }) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على القسم الحالي
      final currentDepartment = _allDepartments.firstWhereOrNull((dept) => dept.id == id);
      if (currentDepartment == null) {
        _error.value = 'القسم غير موجود';
        return false;
      }

      // إنشاء نسخة محدثة من القسم
      final updatedDepartment = currentDepartment.copyWith(
        name: name ?? currentDepartment.name,
        description: description ?? currentDepartment.description,
        managerId: managerId ?? currentDepartment.managerId,
        isActive: isActive ?? currentDepartment.isActive,
      );

      final result = await _apiService.updateDepartment(updatedDepartment.id, updatedDepartment);
      // result لا يمكن أن يكون null هنا
      final index = _allDepartments.indexWhere((dept) => dept.id == id);
      if (index != -1) {
        _allDepartments[index] = result;
      }
      _currentDepartment.value = result;
      _updateActiveDepartments();
      debugPrint('تم تحديث القسم: ${result.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث القسم: $e';
      debugPrint('خطأ في تحديث القسم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.deleteDepartment(id);
      if (success) {
        _allDepartments.removeWhere((dept) => dept.id == id);
        if (_currentDepartment.value?.id == id) {
          _currentDepartment.value = null;
        }
        _updateActiveDepartments();
        debugPrint('تم حذف القسم');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف القسم: $e';
      debugPrint('خطأ في حذف القسم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تفعيل/إلغاء تفعيل قسم
  Future<bool> toggleDepartmentStatus(int id) async {
    final department = _allDepartments.firstWhereOrNull((dept) => dept.id == id);
    if (department == null) return false;

    return await updateDepartment(
      id: id,
      isActive: !department.isActive,
    );
  }

  /// تعيين مدير قسم
  Future<bool> assignManager(int departmentId, int managerId) async {
    return await updateDepartment(
      id: departmentId,
      managerId: managerId,
    );
  }

  /// إزالة مدير قسم
  Future<bool> removeManager(int departmentId) async {
    return await updateDepartment(
      id: departmentId,
      managerId: null,
    );
  }

  /// نقل قسم إلى قسم آخر (تغيير الأب)
  Future<bool> moveDepartment(int departmentId, int? newParentId) async {
    try {
      final result = await _apiService.moveDepartment(departmentId, newParentId);
      if (result) {
        // تحديث القائمة محليًا بعد النقل
        await loadAllDepartments(forceRefresh: true);
      }
      return result;
    } catch (e) {
      _error.value = 'خطأ في نقل القسم: $e';
      debugPrint('خطأ في نقل القسم: $e');
      return false;
    }
  }

  /// تحديث قائمة الأقسام النشطة
  void _updateActiveDepartments() {
    var filtered = List<Department>.from(_allDepartments);

    // تطبيق مرشح النشاط
    if (_showActiveOnly.value) {
      filtered = filtered.where((dept) => dept.isActive).toList();
    }

    // تطبيق مرشح البحث
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((dept) =>
          dept.name.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          (dept.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }

    _activeDepartments.assignAll(filtered);
  }

  /// تعيين مرشح النشاط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _updateActiveDepartments();
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery.value = '';
    _updateActiveDepartments();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllDepartments(),
      loadDepartmentStats(),
    ]);
  }

  /// الحصول على قسم بواسطة المعرف
  Department? getDepartmentById(int id) {
    return _allDepartments.firstWhereOrNull((dept) => dept.id == id);
  }

  /// الحصول على الأقسام التي يديرها مستخدم معين
  List<Department> getDepartmentsByManager(int managerId) {
    return _allDepartments.where((dept) => dept.managerId == managerId).toList();
  }

  /// الحصول على عدد الأقسام النشطة
  int get activeDepartmentCount => 
      _allDepartments.where((dept) => dept.isActive).length;

  /// الحصول على عدد الأقسام غير النشطة
  int get inactiveDepartmentCount => 
      _allDepartments.where((dept) => !dept.isActive).length;
}
