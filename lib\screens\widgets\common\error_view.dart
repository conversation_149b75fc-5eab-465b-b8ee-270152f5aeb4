import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// عرض الخطأ المخصص
/// يعرض رسالة خطأ مع أيقونة وزر إعادة المحاولة الاختياري
class ErrorView extends StatelessWidget {
  /// رسالة الخطأ المعروضة
  final String message;
  
  /// دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة
  final VoidCallback? onRetry;
  
  /// نص زر إعادة المحاولة
  final String? retryText;
  
  /// أيقونة الخطأ
  final IconData icon;
  
  /// حجم الأيقونة
  final double iconSize;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// إنشاء عرض الخطأ
  const ErrorView({
    super.key,
    required this.message,
    this.onRetry,
    this.retryText,
    this.icon = Icons.error_outline,
    this.iconSize = 48.0,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryText ?? 'إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
