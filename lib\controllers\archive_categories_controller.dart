import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/archive_models.dart';
import '../services/api/archive_categories_api_service.dart';

/// متحكم فئات الأرشيف
class ArchiveCategoriesController extends GetxController {
  final ArchiveCategoriesApiService _apiService = ArchiveCategoriesApiService();

  // قوائم الفئات
  final RxList<ArchiveCategory> _allCategories = <ArchiveCategory>[].obs;
  final RxList<ArchiveCategory> _filteredCategories = <ArchiveCategory>[].obs;

  // الفئة الحالية
  final Rx<ArchiveCategory?> _currentCategory = Rx<ArchiveCategory?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<ArchiveCategory> get allCategories => _allCategories;
  List<ArchiveCategory> get filteredCategories => _filteredCategories;
  ArchiveCategory? get currentCategory => _currentCategory.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllCategories();
  }

  /// تحميل جميع فئات الأرشيف
  Future<void> loadAllCategories() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final categories = await _apiService.getAllCategories();
      _allCategories.assignAll(categories);
      _applyFilters();
      debugPrint('تم تحميل ${categories.length} فئة أرشيف');
    } catch (e) {
      _error.value = 'خطأ في تحميل فئات الأرشيف: $e';
      debugPrint('خطأ في تحميل فئات الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على فئة أرشيف بالمعرف
  Future<void> getCategoryById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final category = await _apiService.getCategoryById(id);
      _currentCategory.value = category;
      debugPrint('تم تحميل فئة الأرشيف: ${category?.name ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل فئة الأرشيف: $e';
      debugPrint('خطأ في تحميل فئة الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء فئة أرشيف جديدة
  Future<bool> createCategory(ArchiveCategory category) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newCategory = await _apiService.createCategory(category);
      if (newCategory != null) {
        _allCategories.add(newCategory);
        _applyFilters();
        debugPrint('تم إنشاء فئة أرشيف جديدة: ${newCategory.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء فئة الأرشيف: $e';
      debugPrint('خطأ في إنشاء فئة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث فئة أرشيف
  Future<bool> updateCategory(ArchiveCategory category) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.updateCategory(category);
      if (success) {
        final index = _allCategories.indexWhere((c) => c.id == category.id);
        if (index != -1) {
          _allCategories[index] = category;
          _applyFilters();
        }
        debugPrint('تم تحديث فئة الأرشيف: ${category.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث فئة الأرشيف: $e';
      debugPrint('خطأ في تحديث فئة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف فئة أرشيف
  Future<bool> deleteCategory(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteCategory(id);
      _allCategories.removeWhere((c) => c.id == id);
      _applyFilters();
      debugPrint('تم حذف فئة الأرشيف');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف فئة الأرشيف: $e';
      debugPrint('خطأ في حذف فئة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allCategories.where((category) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!category.name.toLowerCase().contains(query) &&
            !(category.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !category.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredCategories.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllCategories();
  }
}
