/// نماذج الواجهات
/// 
/// تحتوي على نماذج البيانات المتعلقة بواجهات النظام
library;

/// نموذج الواجهة
class InterfaceModel {
  final String name;
  final String displayName;
  final String? description;
  final String? category;
  final bool isActive;

  const InterfaceModel({
    required this.name,
    required this.displayName,
    this.description,
    this.category,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'description': description,
      'category': category,
      'isActive': isActive,
    };
  }

  factory InterfaceModel.fromJson(Map<String, dynamic> json) {
    return InterfaceModel(
      name: json['name'] ?? '',
      displayName: json['displayName'] ?? '',
      description: json['description'],
      category: json['category'],
      isActive: json['isActive'] ?? true,
    );
  }
}

/// نموذج الدور المخصص
class CustomRole {
  final String id;
  final String name;
  final String? description;
  final bool isActive;
  final int createdAt;

  const CustomRole({
    required this.id,
    required this.name,
    this.description,
    this.isActive = true,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  factory CustomRole.fromJson(Map<String, dynamic> json) {
    return CustomRole(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] ?? 0,
    );
  }
}

/// خدمة الواجهات
class InterfaceService {
  /// الحصول على جميع الواجهات
  static List<InterfaceModel> getAllInterfaces() {
    return [
      const InterfaceModel(
        name: 'tasks',
        displayName: 'المهام',
        description: 'إدارة المهام والمشاريع',
        category: 'core',
      ),
      const InterfaceModel(
        name: 'dashboard',
        displayName: 'لوحة المعلومات',
        description: 'عرض الإحصائيات والتقارير',
        category: 'core',
      ),
      const InterfaceModel(
        name: 'messages',
        displayName: 'الرسائل',
        description: 'نظام المراسلة والتواصل',
        category: 'communication',
      ),
      const InterfaceModel(
        name: 'notifications',
        displayName: 'الإشعارات',
        description: 'إدارة الإشعارات',
        category: 'communication',
      ),
      const InterfaceModel(
        name: 'users',
        displayName: 'المستخدمين',
        description: 'إدارة المستخدمين والأدوار',
        category: 'admin',
      ),
      const InterfaceModel(
        name: 'reports',
        displayName: 'التقارير',
        description: 'إنشاء وعرض التقارير',
        category: 'reports',
      ),
      const InterfaceModel(
        name: 'settings',
        displayName: 'الإعدادات',
        description: 'إعدادات النظام',
        category: 'admin',
      ),
      const InterfaceModel(
        name: 'admin',
        displayName: 'الإدارة',
        description: 'لوحة التحكم الإدارية',
        category: 'admin',
      ),
    ];
  }

  /// الحصول على الواجهات مجمعة حسب الفئة
  static Map<String, List<InterfaceModel>> getInterfacesGroupedByCategory() {
    final interfaces = getAllInterfaces();
    final Map<String, List<InterfaceModel>> grouped = {};

    for (final interface in interfaces) {
      final category = interface.category ?? 'other';
      if (!grouped.containsKey(category)) {
        grouped[category] = [];
      }
      grouped[category]!.add(interface);
    }

    return grouped;
  }

  /// الحصول على اسم العرض للفئة
  static String getCategoryDisplayName(String? category) {
    switch (category) {
      case 'core':
        return 'الأساسية';
      case 'communication':
        return 'التواصل';
      case 'admin':
        return 'الإدارة';
      case 'reports':
        return 'التقارير';
      default:
        return 'أخرى';
    }
  }
}

/// مستودع الأدوار المخصصة
class CustomRoleRepository {
  /// الحصول على جميع الأدوار
  Future<List<CustomRole>> getAllRoles() async {
    // محاكاة البيانات
    return [
      CustomRole(
        id: '1',
        name: 'مدير النظام',
        description: 'صلاحيات كاملة للنظام',
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      ),
      CustomRole(
        id: '2',
        name: 'مدير المشروع',
        description: 'إدارة المشاريع والمهام',
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      ),
      CustomRole(
        id: '3',
        name: 'موظف',
        description: 'صلاحيات أساسية للموظفين',
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      ),
    ];
  }
}

/// مستودع الصلاحيات المخصصة
class CustomPermissionRepository {
  /// التحقق من وجود صلاحية للدور
  Future<bool> hasCustomRolePermission(
    String roleId,
    dynamic type,
    dynamic scope, {
    String? description,
  }) async {
    // محاكاة التحقق من الصلاحية
    return false;
  }

  /// منح صلاحية للدور
  Future<void> grantCustomRolePermission(
    String roleId,
    dynamic type,
    dynamic scope, {
    String? description,
  }) async {
    // محاكاة منح الصلاحية
  }

  /// إلغاء صلاحية للدور
  Future<void> revokeCustomRolePermission(
    String roleId,
    dynamic type,
    dynamic scope, {
    String? description,
  }) async {
    // محاكاة إلغاء الصلاحية
  }
}

/// تعدادات الصلاحيات
enum PermissionType {
  view,
  create,
  edit,
  delete,
  admin,
}

enum PermissionScope {
  tasks,
  users,
  reports,
  settings,
  interfaces,
}
