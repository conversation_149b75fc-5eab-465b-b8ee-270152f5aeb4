import 'package:flutter/foundation.dart';
import '../../models/role_model.dart';
import '../../models/permission_models.dart';
import 'api_service.dart';

/// خدمة API موحدة للأدوار - متطابقة مع ASP.NET Core API
/// تجمع جميع وظائف إدارة الأدوار والصلاحيات في خدمة واحدة محسنة
class RolesApiService {
  final ApiService _apiService = ApiService();

  // ===== الأدوار الأساسية =====

  /// الحصول على جميع الأدوار
  Future<List<Role>> getAllRoles() async {
    try {
      debugPrint('🔄 جاري جلب جميع الأدوار...');
      final response = await _apiService.get('/api/Roles');
      final roles = _apiService.handleListResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
      debugPrint('✅ تم جلب ${roles.length} دور');
      return roles;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الأدوار: $e');
      rethrow;
    }
  }

  /// الحصول على دور محدد
  Future<Role> getRole(int id) async {
    try {
      debugPrint('🔄 جاري جلب الدور $id...');
      final response = await _apiService.get('/api/Roles/$id');
      final role = _apiService.handleResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
      debugPrint('✅ تم جلب الدور: ${role.name}');
      return role;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الدور: $e');
      rethrow;
    }
  }

  /// الحصول على تفاصيل الدور مع الصلاحيات والمستخدمين
  Future<Map<String, dynamic>> getRoleDetails(int id) async {
    try {
      debugPrint('🔄 جاري جلب تفاصيل الدور $id...');
      final response = await _apiService.get('/api/Roles/$id/details');
      final details = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم جلب تفاصيل الدور');
      return details;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على تفاصيل الدور: $e');
      rethrow;
    }
  }

  /// إنشاء دور جديد
  Future<Map<String, dynamic>> createRole({
    required String name,
    required String displayName,
    String? description,
    int level = 1,
    bool isActive = true,
    required int createdBy,
    List<int> defaultPermissionIds = const [],
  }) async {
    try {
      debugPrint('🔄 جاري إنشاء دور جديد: $name...');
      
      final requestData = {
        'name': name,
        'displayName': displayName,
        'description': description,
        'level': level,
        'isActive': isActive,
        'createdBy': createdBy,
        'defaultPermissionIds': defaultPermissionIds,
      };

      final response = await _apiService.post('/api/Roles', requestData);
      final result = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم إنشاء الدور بنجاح');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الدور: $e');
      rethrow;
    }
  }

  /// تحديث دور موجود
  Future<bool> updateRole({
    required int id,
    String? displayName,
    String? description,
    int? level,
    bool? isActive,
    required int updatedBy,
  }) async {
    try {
      debugPrint('🔄 جاري تحديث الدور $id...');
      
      final requestData = <String, dynamic>{
        'updatedBy': updatedBy,
      };

      if (displayName != null) requestData['displayName'] = displayName;
      if (description != null) requestData['description'] = description;
      if (level != null) requestData['level'] = level;
      if (isActive != null) requestData['isActive'] = isActive;

      final response = await _apiService.put('/api/Roles/$id', requestData);
      
      if (response.statusCode == 204) {
        debugPrint('✅ تم تحديث الدور بنجاح');
        return true;
      } else {
        throw Exception('فشل في تحديث الدور: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الدور: $e');
      rethrow;
    }
  }

  /// حذف دور
  Future<Map<String, dynamic>> deleteRole(int id) async {
    try {
      debugPrint('🔄 جاري حذف الدور $id...');
      final response = await _apiService.delete('/api/Roles/$id');

      if (response.statusCode == 200) {
        final result = _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );
        debugPrint('✅ تم حذف الدور بنجاح');
        return result;
      } else {
        throw Exception('فشل في حذف الدور: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الدور: $e');
      rethrow;
    }
  }

  // ===== إدارة صلاحيات الأدوار =====

  /// الحصول على صلاحيات دور محدد
  Future<List<Permission>> getRolePermissions(int roleId) async {
    try {
      debugPrint('🔄 جاري جلب صلاحيات الدور $roleId...');
      final response = await _apiService.get('/api/Roles/$roleId/permissions');
      final summaries = _apiService.handleListResponse<PermissionSummary>(
        response,
        (json) => PermissionSummary.fromJson(json),
      );
      debugPrint('✅ تم جلب ${summaries.length} صلاحية للدور');

      // تحويل PermissionSummary إلى Permission
      final permissions = summaries.map((summary) => summary.toPermission()).toList();
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات الدور: $e');
      rethrow;
    }
  }

  /// منح صلاحية لدور
  Future<bool> grantPermissionToRole({
    required int roleId,
    required int permissionId,
    required int userId,
  }) async {
    try {
      debugPrint('🔄 جاري منح صلاحية $permissionId للدور $roleId...');
      
      final requestData = {
        'permissionId': permissionId,
        'userId': userId,
      };

      final response = await _apiService.post('/api/Roles/$roleId/permissions/grant', requestData);
      
      if (response.statusCode == 200) {
        debugPrint('✅ تم منح الصلاحية للدور بنجاح');
        return true;
      } else {
        throw Exception('فشل في منح الصلاحية: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في منح الصلاحية للدور: $e');
      rethrow;
    }
  }

  /// سحب صلاحية من دور
  Future<bool> revokePermissionFromRole({
    required int roleId,
    required int permissionId,
    required int userId,
  }) async {
    try {
      debugPrint('🔄 جاري سحب صلاحية $permissionId من الدور $roleId...');
      
      final requestData = {
        'permissionId': permissionId,
        'userId': userId,
      };

      final response = await _apiService.post('/api/Roles/$roleId/permissions/revoke', requestData);
      
      if (response.statusCode == 200) {
        debugPrint('✅ تم سحب الصلاحية من الدور بنجاح');
        return true;
      } else {
        throw Exception('فشل في سحب الصلاحية: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في سحب الصلاحية من الدور: $e');
      rethrow;
    }
  }

  /// منح صلاحيات متعددة لدور
  Future<Map<String, dynamic>> grantMultiplePermissionsToRole({
    required int roleId,
    required List<int> permissionIds,
    required int userId,
    bool replaceExisting = false,
  }) async {
    try {
      debugPrint('🔄 جاري منح ${permissionIds.length} صلاحية للدور $roleId...');
      
      final requestData = {
        'permissionIds': permissionIds,
        'userId': userId,
        'replaceExisting': replaceExisting,
      };

      final response = await _apiService.post('/api/Roles/$roleId/permissions/grant-multiple', requestData);
      final result = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم منح الصلاحيات للدور بنجاح');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في منح الصلاحيات للدور: $e');
      rethrow;
    }
  }

  /// الحصول على مستخدمي دور محدد
  Future<List<Map<String, dynamic>>> getRoleUsers(int roleId) async {
    try {
      debugPrint('🔄 جاري جلب مستخدمي الدور $roleId...');
      final response = await _apiService.get('/api/Roles/$roleId/users');
      final users = _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      debugPrint('✅ تم جلب ${users.length} مستخدم للدور');
      return users;
    } catch (e) {
      debugPrint('❌ خطأ في جلب مستخدمي الدور: $e');
      rethrow;
    }
  }
}
