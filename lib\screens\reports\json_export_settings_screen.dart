import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/report_controller.dart';
import '../../utils/app_styles.dart';
import '../../services/unified_permission_service.dart';

/// شاشة إعدادات تصدير JSON
///
/// توفر واجهة لتخصيص إعدادات تصدير التقارير بتنسيق JSON
class JsonExportSettingsScreen extends StatefulWidget {
  /// معرف التقرير
  final String reportId;
  
  /// عنوان التقرير
  final String title;

  const JsonExportSettingsScreen({
    super.key,
    required this.reportId,
    required this.title,
  });

  @override
  State<JsonExportSettingsScreen> createState() => _JsonExportSettingsScreenState();
}

class _JsonExportSettingsScreenState extends State<JsonExportSettingsScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  
  // إعدادات التصدير
  bool _includeSummary = true;
  bool _includeVisualizationData = true;
  bool _includeReportInfo = true;
  bool _prettyPrint = true;
  bool _includeMetadata = true;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات تصدير JSON'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تصدير التقرير: ${widget.title}',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 24),
            const Text(
              'خصائص التصدير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingSwitch(
              title: 'تضمين معلومات التقرير',
              subtitle: 'العنوان، الوصف، النوع، تاريخ الإنشاء، إلخ',
              value: _includeReportInfo,
              onChanged: (value) {
                if (UnifiedPermissionService().canExportReport()) {
                  setState(() {
                    _includeReportInfo = value;
                  });
                }
              },
            ),
            _buildSettingSwitch(
              title: 'تضمين ملخص التقرير',
              subtitle: 'إحصائيات وملخصات البيانات',
              value: _includeSummary,
              onChanged: (value) {
                setState(() {
                  _includeSummary = value;
                });
              },
            ),
            _buildSettingSwitch(
              title: 'تضمين بيانات التصور المرئي',
              subtitle: 'بيانات المخططات والرسوم البيانية',
              value: _includeVisualizationData,
              onChanged: (value) {
                setState(() {
                  _includeVisualizationData = value;
                });
              },
            ),
            _buildSettingSwitch(
              title: 'تضمين البيانات الوصفية',
              subtitle: 'معلومات إضافية عن التقرير والبيانات',
              value: _includeMetadata,
              onChanged: (value) {
                setState(() {
                  _includeMetadata = value;
                });
              },
            ),
            const Divider(),
            const SizedBox(height: 16),
            const Text(
              'تنسيق الملف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingSwitch(
              title: 'تنسيق مقروء',
              subtitle: 'تنسيق JSON بشكل مقروء مع مسافات ومسافات بادئة',
              value: _prettyPrint,
              onChanged: (value) {
                setState(() {
                  _prettyPrint = value;
                });
              },
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _exportReport,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
                child: const Text(
                  'تصدير التقرير',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// بناء مفتاح إعداد
  Widget _buildSettingSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.purple,
    );
  }
  
  /// تصدير التقرير
  Future<void> _exportReport() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );
    
    try {
      // تحميل التقرير
      await _reportController.loadReport(widget.reportId);
      
      // تنفيذ التقرير
      await _reportController.executeCurrentReport();
      
      // تصدير التقرير بتنسيق JSON
      final filePath = await _reportController.exportCurrentReportToJsonWithSettings(
        includeSummary: _includeSummary,
        includeVisualizationData: _includeVisualizationData,
        includeReportInfo: _includeReportInfo,
        prettyPrint: _prettyPrint,
        includeMetadata: _includeMetadata,
      );
      
      // إغلاق مؤشر التحميل
      Get.back();
      
      if (filePath != null) {
        // عرض رسالة نجاح
        Get.back(); // العودة إلى الشاشة السابقة
        Get.snackbar(
          'تم التصدير بنجاح',
          'تم تصدير التقرير بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
      } else {
        // عرض رسالة خطأ
        Get.back(); // العودة إلى الشاشة السابقة
        Get.snackbar(
          'فشل التصدير',
          'فشل تصدير التقرير بتنسيق JSON',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();
      
      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
