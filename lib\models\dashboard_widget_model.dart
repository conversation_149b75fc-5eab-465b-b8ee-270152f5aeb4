import 'package:flutter/material.dart';

/// موضع العنصر
class WidgetPosition {
  final double x;
  final double y;

  const WidgetPosition({
    required this.x,
    required this.y,
  });

  factory WidgetPosition.fromJson(Map<String, dynamic> json) {
    return WidgetPosition(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
    };
  }

  WidgetPosition copyWith({
    double? x,
    double? y,
  }) {
    return WidgetPosition(
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }

  @override
  String toString() => 'WidgetPosition(x: $x, y: $y)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WidgetPosition && other.x == x && other.y == y;
  }

  @override
  int get hashCode => Object.hash(x, y);
}

/// حجم العنصر
class WidgetSize {
  final double width;
  final double height;

  const WidgetSize({
    required this.width,
    required this.height,
  });

  factory WidgetSize.fromJson(Map<String, dynamic> json) {
    return WidgetSize(
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
    };
  }

  WidgetSize copyWith({
    double? width,
    double? height,
  }) {
    return WidgetSize(
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }

  @override
  String toString() => 'WidgetSize(width: $width, height: $height)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WidgetSize && other.width == width && other.height == height;
  }

  @override
  int get hashCode => Object.hash(width, height);
}

/// أنواع عناصر لوحة المعلومات للنموذج الثاني
enum DashboardWidgetType {
  barChart,
  lineChart,
  pieChart,
  areaChart,
  table,
  kpiCard,
  gaugeChart,
  heatMap,
  radarChart,
  bubbleChart,
  ganttChart,
  treeMap,
  summary,
  custom, taskStatusChart, taskProgressChart, departmentPerformanceChart, userPerformanceChart,
}

/// اتجاه المخطط
enum ChartOrientation {
  horizontal,
  vertical,
}

/// نموذج عنصر لوحة المعلومات للنموذج الثاني
class DashboardWidget {
  final String id;
  final String dashboardId;
  final String title;
  final String? description;
  final DashboardWidgetType type;
  final String dataSource;
  final String query;
  final Offset position;
  final Size size;
  final Color? headerColor;
  final String? xAxisField;
  final String? xAxisLabel;
  final String? yAxisField;
  final String? yAxisLabel;
  final String? labelField;
  final String? valueField;
  final ChartOrientation orientation;
  final bool? showValues;
  final bool? showLabels;
  final bool? showGrid;
  final bool? showLegend;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdById;

  const DashboardWidget({
    required this.id,
    required this.dashboardId,
    required this.title,
    this.description,
    required this.type,
    required this.dataSource,
    required this.query,
    required this.position,
    required this.size,
    this.headerColor,
    this.xAxisField,
    this.xAxisLabel,
    this.yAxisField,
    this.yAxisLabel,
    this.labelField,
    this.valueField,
    this.orientation = ChartOrientation.vertical,
    this.showValues,
    this.showLabels,
    this.showGrid,
    this.showLegend,
    required this.createdAt,
    required this.updatedAt,
    required this.createdById,
  });

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
      id: json['id'] as String,
      dashboardId: json['dashboardId'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: DashboardWidgetType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => DashboardWidgetType.custom,
      ),
      dataSource: json['dataSource'] as String,
      query: json['query'] as String,
      position: Offset(
        (json['position']['dx'] as num).toDouble(),
        (json['position']['dy'] as num).toDouble(),
      ),
      size: Size(
        (json['size']['width'] as num).toDouble(),
        (json['size']['height'] as num).toDouble(),
      ),
      headerColor: json['headerColor'] != null
          ? Color(json['headerColor'] as int)
          : null,
      xAxisField: json['xAxisField'] as String?,
      xAxisLabel: json['xAxisLabel'] as String?,
      yAxisField: json['yAxisField'] as String?,
      yAxisLabel: json['yAxisLabel'] as String?,
      labelField: json['labelField'] as String?,
      valueField: json['valueField'] as String?,
      orientation: ChartOrientation.values.firstWhere(
        (e) => e.toString().split('.').last == json['orientation'],
        orElse: () => ChartOrientation.vertical,
      ),
      showValues: json['showValues'] as bool?,
      showLabels: json['showLabels'] as bool?,
      showGrid: json['showGrid'] as bool?,
      showLegend: json['showLegend'] as bool?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdById: json['createdById'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'dataSource': dataSource,
      'query': query,
      'position': {
        'dx': position.dx,
        'dy': position.dy,
      },
      'size': {
        'width': size.width,
        'height': size.height,
      },
      'headerColor': headerColor?.toARGB32().toRadixString(16),
      'xAxisField': xAxisField,
      'xAxisLabel': xAxisLabel,
      'yAxisField': yAxisField,
      'yAxisLabel': yAxisLabel,
      'labelField': labelField,
      'valueField': valueField,
      'orientation': orientation.toString().split('.').last,
      'showValues': showValues,
      'showLabels': showLabels,
      'showGrid': showGrid,
      'showLegend': showLegend,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdById': createdById,
    };
  }

  DashboardWidget copyWith({
    String? id,
    String? dashboardId,
    String? title,
    String? description,
    DashboardWidgetType? type,
    String? dataSource,
    String? query,
    Offset? position,
    Size? size,
    Color? headerColor,
    String? xAxisField,
    String? xAxisLabel,
    String? yAxisField,
    String? yAxisLabel,
    String? labelField,
    String? valueField,
    ChartOrientation? orientation,
    bool? showValues,
    bool? showLabels,
    bool? showGrid,
    bool? showLegend,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdById,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      dashboardId: dashboardId ?? this.dashboardId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      dataSource: dataSource ?? this.dataSource,
      query: query ?? this.query,
      position: position ?? this.position,
      size: size ?? this.size,
      headerColor: headerColor ?? this.headerColor,
      xAxisField: xAxisField ?? this.xAxisField,
      xAxisLabel: xAxisLabel ?? this.xAxisLabel,
      yAxisField: yAxisField ?? this.yAxisField,
      yAxisLabel: yAxisLabel ?? this.yAxisLabel,
      labelField: labelField ?? this.labelField,
      valueField: valueField ?? this.valueField,
      orientation: orientation ?? this.orientation,
      showValues: showValues ?? this.showValues,
      showLabels: showLabels ?? this.showLabels,
      showGrid: showGrid ?? this.showGrid,
      showLegend: showLegend ?? this.showLegend,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdById: createdById ?? this.createdById,
    );
  }

  @override
  String toString() {
    return 'DashboardWidget(id: $id, title: $title, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardWidget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
