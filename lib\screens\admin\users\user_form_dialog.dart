import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/user_model.dart';
import '../../../models/role_model.dart';
import '../../../models/department_model.dart';
import '../shared/admin_form_widget.dart';
import '../shared/admin_dialog_widget.dart';

/// حوار نموذج المستخدم (إضافة/تعديل)
class UserFormDialog extends StatefulWidget {
  final User? user; // null للإضافة، User للتعديل

  const UserFormDialog({super.key, this.user});

  @override
  State<UserFormDialog> createState() => _UserFormDialogState();
}

class _UserFormDialogState extends State<UserFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final AdminController _adminController = Get.find<AdminController>();
  
  // متحكمات النص
  late final TextEditingController _nameController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;
  
  // القيم المحددة
  Role? _selectedRole;
  Department? _selectedDepartment;
  bool _isActive = true;
  
  final RxBool _isLoading = false.obs;
  final RxBool _obscurePassword = true.obs;
  final RxBool _obscureConfirmPassword = true.obs;

  bool get _isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadFormData();
  }

  /// تهيئة متحكمات النص
  void _initializeControllers() {
    // طباعة البيانات للتشخيص
    if (widget.user != null) {
      debugPrint('🔍 تهيئة النموذج للمستخدم: ${widget.user!.name}');
      debugPrint('🔍 البيانات: firstName=${widget.user!.firstName}, lastName=${widget.user!.lastName}, username=${widget.user!.username}');
    }

    _nameController = TextEditingController(text: widget.user?.name ?? '');
    _firstNameController = TextEditingController(text: widget.user?.firstName ?? '');
    _lastNameController = TextEditingController(text: widget.user?.lastName ?? '');
    _emailController = TextEditingController(text: widget.user?.email ?? '');
    _usernameController = TextEditingController(text: widget.user?.username ?? '');
    _passwordController = TextEditingController();
    _confirmPasswordController = TextEditingController();

    if (widget.user != null) {
      _isActive = widget.user!.isActive;

      // التأكد من تحديث الواجهة بعد تهيئة البيانات
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {});
      });
    }
  }

  /// تحميل بيانات النموذج
  Future<void> _loadFormData() async {
    try {
      await _adminController.loadRoles();
      await _adminController.loadDepartments();

      if (widget.user != null) {
        debugPrint('🔍 تحميل بيانات النموذج للمستخدم: ${widget.user!.name}');
        debugPrint('🔍 roleId: ${widget.user!.roleId}, departmentId: ${widget.user!.departmentId}');

        // البحث عن الدور والقسم المحددين
        _selectedRole = _adminController.roles.firstWhereOrNull(
          (role) => role.id == widget.user!.roleId,
        );
        _selectedDepartment = _adminController.departments.firstWhereOrNull(
          (dept) => dept.id == widget.user!.departmentId,
        );

        debugPrint('🔍 الدور المحدد: ${_selectedRole?.displayName}');
        debugPrint('🔍 القسم المحدد: ${_selectedDepartment?.name}');

        // التأكد من أن البيانات محملة في الحقول
        debugPrint('🔍 قيم الحقول بعد التحميل:');
        debugPrint('  - name: "${_nameController.text}"');
        debugPrint('  - firstName: "${_firstNameController.text}"');
        debugPrint('  - lastName: "${_lastNameController.text}"');
        debugPrint('  - username: "${_usernameController.text}"');
        debugPrint('  - email: "${_emailController.text}"');
      }

      setState(() {});
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل بيانات النموذج: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الحوار
            _buildDialogHeader(),
            
            // محتوى النموذج
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: _buildForm(),
              ),
            ),
            
            // أزرار الحوار
            _buildDialogActions(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isEditing ? Icons.edit : Icons.person_add,
            color: Colors.white,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// بناء النموذج
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // الاسم الكامل
          AdminTextField(
            label: 'الاسم الكامل',
            controller: _nameController,
            isRequired: true,
            prefixIcon: Icons.person,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'الاسم الكامل مطلوب';
              }
              if (value.trim().length < 2) {
                return 'الاسم يجب أن يكون أكثر من حرفين';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // الاسم الأول والأخير
          Row(
            children: [
              Expanded(
                child: AdminTextField(
                  label: 'الاسم الأول',
                  controller: _firstNameController,
                  prefixIcon: Icons.person_outline,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdminTextField(
                  label: 'الاسم الأخير',
                  controller: _lastNameController,
                  prefixIcon: Icons.person_outline,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // البريد الإلكتروني
          AdminTextField(
            label: 'البريد الإلكتروني',
            controller: _emailController,
            isRequired: true,
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'البريد الإلكتروني مطلوب';
              }
              if (!GetUtils.isEmail(value.trim())) {
                return 'البريد الإلكتروني غير صحيح';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // اسم المستخدم
          AdminTextField(
            label: 'اسم المستخدم',
            controller: _usernameController,
            prefixIcon: Icons.account_circle,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                if (value.trim().length < 6) {
                  return 'اسم المستخدم يجب أن يكون أكثر من 6 أحرف';
                }
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // كلمة المرور (للإضافة أو التعديل الاختياري)
          if (!_isEditing) ...[
            // كلمة المرور للمستخدمين الجدد (مطلوبة)
            Obx(() => AdminTextField(
              label: 'كلمة المرور',
              controller: _passwordController,
              isRequired: true,
              obscureText: _obscurePassword.value,
              prefixIcon: Icons.lock,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword.value ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () => _obscurePassword.toggle(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'كلمة المرور مطلوبة';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب أن تكون أكثر من 6 أحرف';
                }
                return null;
              },
            )),

            const SizedBox(height: 16),

            Obx(() => AdminTextField(
              label: 'تأكيد كلمة المرور',
              controller: _confirmPasswordController,
              isRequired: true,
              obscureText: _obscureConfirmPassword.value,
              prefixIcon: Icons.lock_outline,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword.value ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () => _obscureConfirmPassword.toggle(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'تأكيد كلمة المرور مطلوب';
                }
                if (value != _passwordController.text) {
                  return 'كلمة المرور غير متطابقة';
                }
                return null;
              },
            )),

            const SizedBox(height: 16),
          ] else ...[
            // كلمة المرور للمستخدمين الموجودين (اختيارية)
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ExpansionTile(
                title: Row(
                  children: [
                    Icon(Icons.lock_reset, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    const Text(
                      'تغيير كلمة المرور',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                subtitle: const Text(
                  'اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور',
                  style: TextStyle(fontSize: 12),
                ),
                children: [
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child: Column(
                      children: [
                        // تحذير
                        Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade100,
                            border: Border.all(color: Colors.orange.shade300),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info, color: Colors.orange.shade700, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'سيتم تغيير كلمة المرور وسيحتاج المستخدم لاستخدام كلمة المرور الجديدة',
                                  style: TextStyle(
                                    color: Colors.orange.shade700,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        Obx(() => AdminTextField(
                          label: 'كلمة المرور الجديدة',
                          controller: _passwordController,
                          obscureText: _obscurePassword.value,
                          prefixIcon: Icons.lock,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword.value ? Icons.visibility : Icons.visibility_off,
                            ),
                            onPressed: () => _obscurePassword.toggle(),
                          ),
                          validator: (value) {
                            if (value != null && value.trim().isNotEmpty) {
                              if (value.length < 6) {
                                return 'كلمة المرور يجب أن تكون أكثر من 6 أحرف';
                              }
                            }
                            return null;
                          },
                        )),

                        const SizedBox(height: 16),

                        Obx(() => AdminTextField(
                          label: 'تأكيد كلمة المرور الجديدة',
                          controller: _confirmPasswordController,
                          obscureText: _obscureConfirmPassword.value,
                          prefixIcon: Icons.lock_outline,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscureConfirmPassword.value ? Icons.visibility : Icons.visibility_off,
                            ),
                            onPressed: () => _obscureConfirmPassword.toggle(),
                          ),
                          validator: (value) {
                            if (_passwordController.text.isNotEmpty) {
                              if (value == null || value.trim().isEmpty) {
                                return 'تأكيد كلمة المرور مطلوب';
                              }
                              if (value != _passwordController.text) {
                                return 'كلمة المرور غير متطابقة';
                              }
                            }
                            return null;
                          },
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),
          ],
          
          // الدور
          AdminDropdownField<Role>(
            label: 'الدور',
            value: _selectedRole,
            isRequired: true,
            prefixIcon: Icons.security,
            items: _adminController.roles.map((role) {
              return DropdownMenuItem<Role>(
                value: role,
                child: Text(role.displayName),
              );
            }).toList(),
            onChanged: (role) => setState(() => _selectedRole = role),
            validator: (value) {
              if (value == null) {
                return 'يجب اختيار دور للمستخدم';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // القسم
          AdminDropdownField<Department>(
            label: 'القسم',
            value: _selectedDepartment,
            prefixIcon: Icons.business,
            items: _adminController.departments.map((dept) {
              return DropdownMenuItem<Department>(
                value: dept,
                child: Text(dept.name),
              );
            }).toList(),
            onChanged: (dept) => setState(() => _selectedDepartment = dept),
          ),
          
          const SizedBox(height: 16),
          
          // حالة التفعيل
          AdminSwitchField(
            label: 'المستخدم نشط',
            subtitle: 'تحديد ما إذا كان المستخدم يمكنه تسجيل الدخول',
            value: _isActive,
            onChanged: (value) => setState(() => _isActive = value),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الحوار
  Widget _buildDialogActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 16),
          Obx(() => ElevatedButton(
            onPressed: _isLoading.value ? null : _saveUser,
            child: _isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_isEditing ? 'تحديث' : 'إضافة'),
          )),
        ],
      ),
    );
  }

  /// حفظ المستخدم
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;
    
    try {
      // استدعاء API لحفظ المستخدم
      final adminController = Get.find<AdminController>();

      bool success = false;

      if (widget.user == null) {
        // إنشاء مستخدم جديد
        success = await adminController.createUser(User(
          id: 0, // سيتم تعيينه من الخادم
          name: _nameController.text,
          email: _emailController.text,
          username: _usernameController.text.isNotEmpty ? _usernameController.text : null,
          firstName: _firstNameController.text.isNotEmpty ? _firstNameController.text : null,
          lastName: _lastNameController.text.isNotEmpty ? _lastNameController.text : null,
          password: _passwordController.text, // مطلوب للمستخدمين الجدد
          isActive: _isActive,
          roleId: _selectedRole?.id ?? 1,
          departmentId: _selectedDepartment?.id,
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ));
      } else {
        // تحديث مستخدم موجود - لا نرسل كلمة المرور إلا إذا تم تغييرها
        debugPrint('🔄 تحديث المستخدم: ${widget.user!.name}');
        debugPrint('🔄 البيانات الجديدة:');
        debugPrint('  - name: "${_nameController.text}"');
        debugPrint('  - firstName: "${_firstNameController.text}"');
        debugPrint('  - lastName: "${_lastNameController.text}"');
        debugPrint('  - username: "${_usernameController.text}"');
        debugPrint('  - email: "${_emailController.text}"');
        debugPrint('  - roleId: ${_selectedRole?.id}');
        debugPrint('  - departmentId: ${_selectedDepartment?.id}');
        debugPrint('  - isActive: $_isActive');

        final updatedUser = widget.user!.copyWith(
          name: _nameController.text,
          email: _emailController.text,
          username: _usernameController.text.isNotEmpty ? _usernameController.text : null,
          firstName: _firstNameController.text.isNotEmpty ? _firstNameController.text : null,
          lastName: _lastNameController.text.isNotEmpty ? _lastNameController.text : null,
          isActive: _isActive,
          roleId: _selectedRole?.id,
          departmentId: _selectedDepartment?.id,
          // تضمين كلمة المرور فقط إذا تم إدخالها
          password: _passwordController.text.isNotEmpty ? _passwordController.text : null,
        );

        debugPrint('🔄 البيانات المرسلة: ${updatedUser.toJson()}');
        success = await adminController.updateUser(updatedUser);
      }

      if (success) {
        debugPrint('✅ نجح ${_isEditing ? "تحديث" : "إضافة"} المستخدم');

        // إغلاق الحوار أولاً
        Get.back(result: true);

        // ثم إظهار رسالة النجاح
        await Future.delayed(const Duration(milliseconds: 100));

        String successMessage;
        if (_isEditing) {
          bool passwordChanged = _passwordController.text.isNotEmpty;
          successMessage = passwordChanged
            ? 'تم تحديث المستخدم وتغيير كلمة المرور بنجاح'
            : 'تم تحديث المستخدم بنجاح';
        } else {
          successMessage = 'تم إضافة المستخدم بنجاح';
        }

        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: successMessage,
        );
      } else {
        // إذا فشلت العملية، عرض رسالة خطأ
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: adminController.error.isNotEmpty
            ? adminController.error
            : (_isEditing ? 'فشل في تحديث المستخدم' : 'فشل في إضافة المستخدم'),
        );
      }
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: _isEditing
          ? 'فشل في تحديث المستخدم: $e'
          : 'فشل في إضافة المستخدم: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
}
