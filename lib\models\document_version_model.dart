import 'dart:convert';

/// نموذج إصدار المستند
/// يحتوي على معلومات كل إصدار محفوظ من المستند
class DocumentVersion {
  final String id;
  final String content;
  final DateTime timestamp;
  final String? authorName;
  final String? changeDescription;
  final int wordCount;
  final int characterCount;
  final Map<String, dynamic>? metadata;

  DocumentVersion({
    required this.id,
    required this.content,
    required this.timestamp,
    this.authorName,
    this.changeDescription,
    required this.wordCount,
    required this.characterCount,
    this.metadata,
  });

  /// إنشاء إصدار جديد من المحتوى الحالي
  factory DocumentVersion.fromContent({
    required String content,
    String? authorName,
    String? changeDescription,
    Map<String, dynamic>? metadata,
  }) {
    final plainText = _extractPlainText(content);
    return DocumentVersion(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      timestamp: DateTime.now(),
      authorName: authorName,
      changeDescription: changeDescription,
      wordCount: _countWords(plainText),
      characterCount: plainText.length,
      metadata: metadata,
    );
  }

  /// تحويل من JSON
  factory DocumentVersion.fromJson(Map<String, dynamic> json) {
    return DocumentVersion(
      id: json['id'] as String,
      content: json['content'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      authorName: json['authorName'] as String?,
      changeDescription: json['changeDescription'] as String?,
      wordCount: json['wordCount'] as int,
      characterCount: json['characterCount'] as int,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'authorName': authorName,
      'changeDescription': changeDescription,
      'wordCount': wordCount,
      'characterCount': characterCount,
      'metadata': metadata,
    };
  }

  /// الحصول على النص الخام من المحتوى
  String get plainText => _extractPlainText(content);

  /// الحصول على ملخص التغييرات
  String get summary {
    if (changeDescription != null && changeDescription!.isNotEmpty) {
      return changeDescription!;
    }
    return 'تحديث المستند - $wordCount كلمة، $characterCount حرف';
  }

  /// الحصول على تاريخ منسق
  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// مقارنة مع إصدار آخر
  bool isContentDifferent(DocumentVersion other) {
    return content != other.content;
  }

  /// حساب نسبة التشابه مع إصدار آخر
  double similarityWith(DocumentVersion other) {
    final thisText = plainText;
    final otherText = other.plainText;
    
    if (thisText == otherText) return 1.0;
    if (thisText.isEmpty && otherText.isEmpty) return 1.0;
    if (thisText.isEmpty || otherText.isEmpty) return 0.0;
    
    // حساب تشابه بسيط بناءً على الكلمات المشتركة
    final thisWords = thisText.toLowerCase().split(RegExp(r'\s+'));
    final otherWords = otherText.toLowerCase().split(RegExp(r'\s+'));
    
    final commonWords = thisWords.where((word) => otherWords.contains(word)).length;
    final totalWords = (thisWords.length + otherWords.length) / 2;
    
    return commonWords / totalWords;
  }

  @override
  String toString() {
    return 'DocumentVersion(id: $id, timestamp: $formattedDate, words: $wordCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DocumentVersion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// استخراج النص الخام من المحتوى
  static String _extractPlainText(String content) {
    try {
      // محاولة تحليل المحتوى كـ JSON (Quill Delta)
      final decoded = jsonDecode(content);
      if (decoded is List) {
        // استخراج النص من Quill Delta
        final buffer = StringBuffer();
        for (final op in decoded) {
          if (op is Map && op.containsKey('insert')) {
            final insert = op['insert'];
            if (insert is String) {
              buffer.write(insert);
            }
          }
        }
        return buffer.toString();
      }
    } catch (e) {
      // إذا فشل التحليل، إرجاع المحتوى كما هو
    }
    return content;
  }

  /// حساب عدد الكلمات
  static int _countWords(String text) {
    if (text.isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }
}

/// إحصائيات تاريخ الإصدارات
class VersionHistoryStats {
  final int totalVersions;
  final DateTime? firstVersion;
  final DateTime? lastVersion;
  final int totalChanges;
  final List<String> contributors;
  final Map<String, int> contributorCounts;

  VersionHistoryStats({
    required this.totalVersions,
    this.firstVersion,
    this.lastVersion,
    required this.totalChanges,
    required this.contributors,
    required this.contributorCounts,
  });

  /// إنشاء إحصائيات من قائمة الإصدارات
  factory VersionHistoryStats.fromVersions(List<DocumentVersion> versions) {
    if (versions.isEmpty) {
      return VersionHistoryStats(
        totalVersions: 0,
        totalChanges: 0,
        contributors: [],
        contributorCounts: {},
      );
    }

    final sortedVersions = List<DocumentVersion>.from(versions)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    final contributors = <String>{};
    final contributorCounts = <String, int>{};

    for (final version in versions) {
      if (version.authorName != null) {
        contributors.add(version.authorName!);
        contributorCounts[version.authorName!] = 
            (contributorCounts[version.authorName!] ?? 0) + 1;
      }
    }

    return VersionHistoryStats(
      totalVersions: versions.length,
      firstVersion: sortedVersions.first.timestamp,
      lastVersion: sortedVersions.last.timestamp,
      totalChanges: versions.length - 1, // أول إصدار ليس تغيير
      contributors: contributors.toList(),
      contributorCounts: contributorCounts,
    );
  }

  /// الحصول على أكثر المساهمين نشاطاً
  String? get mostActiveContributor {
    if (contributorCounts.isEmpty) return null;
    
    String? mostActive;
    int maxCount = 0;
    
    contributorCounts.forEach((contributor, count) {
      if (count > maxCount) {
        maxCount = count;
        mostActive = contributor;
      }
    });
    
    return mostActive;
  }

  /// الحصول على متوسط التغييرات في اليوم
  double get averageChangesPerDay {
    if (firstVersion == null || lastVersion == null || totalChanges == 0) {
      return 0.0;
    }
    
    final daysDiff = lastVersion!.difference(firstVersion!).inDays;
    if (daysDiff == 0) return totalChanges.toDouble();
    
    return totalChanges / daysDiff;
  }
}
