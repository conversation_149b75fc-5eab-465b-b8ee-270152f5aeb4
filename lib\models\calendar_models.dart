import 'user_model.dart';
import 'task_models.dart';

/// أنواع أحداث التقويم
enum CalendarEventType {
  task,
  meeting,
  reminder,
  vacation,
  other
}

/// أنماط تكرار الأحداث
enum EventRecurrencePattern {
  none,
  daily,
  weekly,
  monthly,
  yearly
}

/// أوقات التنبيه للأحداث
enum EventReminderTime {
  none,
  fiveMinutes,
  fifteenMinutes,
  thirtyMinutes,
  oneHour,
  oneDay
}

/// نموذج حدث التقويم - متطابق مع ASP.NET Core API
class CalendarEvent {
  final int id;
  final String title;
  final String? description;
  final int startTime; // Unix timestamp
  final int endTime; // Unix timestamp
  final bool allDay;
  final String? location;
  final String? color;
  final int userId;
  final int? taskId;
  final String? recurrenceRule;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final int? duration; // بالثواني (متطابق مع API)

  // خصائص إضافية للتقويم
  final CalendarEventType eventType;
  final EventRecurrencePattern recurrencePattern;
  final int recurrenceCount;
  final DateTime? recurrenceEndDate;
  final EventReminderTime reminderTime;
  final bool isReminderEnabled;

  // Navigation properties
  final User? user;
  final Task? task;

  const CalendarEvent({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.allDay = false,
    this.location,
    this.color,
    required this.userId,
    this.taskId,
    this.recurrenceRule,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.duration,
    this.eventType = CalendarEventType.other,
    this.recurrencePattern = EventRecurrencePattern.none,
    this.recurrenceCount = 0,
    this.recurrenceEndDate,
    this.reminderTime = EventReminderTime.none,
    this.isReminderEnabled = false,
    this.user,
    this.task,
  });

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      startTime: json['startTime'] as int,
      endTime: json['endTime'] as int,
      allDay: json['allDay'] as bool? ?? false,
      location: json['location'] as String?,
      color: json['color'] as String?,
      userId: json['userId'] as int,
      taskId: json['taskId'] as int?,
      recurrenceRule: json['recurrenceRule'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      duration: json['duration'] as int?,
      eventType: _parseEventType(json['eventType'] as String?),
      recurrencePattern: _parseRecurrencePattern(json['recurrencePattern'] as String?),
      recurrenceCount: json['recurrenceCount'] as int? ?? 0,
      recurrenceEndDate: json['recurrenceEndDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['recurrenceEndDate'] as int) * 1000)
          : null,
      reminderTime: _parseReminderTime(json['reminderTime'] as String?),
      isReminderEnabled: json['isReminderEnabled'] as bool? ?? false,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startTime': startTime,
      'endTime': endTime,
      'allDay': allDay,
      'location': location,
      'color': color,
      'userId': userId,
      'taskId': taskId,
      'recurrenceRule': recurrenceRule,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'duration': duration,
    };
  }

  CalendarEvent copyWith({
    int? id,
    String? title,
    String? description,
    int? startTime,
    int? endTime,
    bool? allDay,
    String? location,
    String? color,
    int? userId,
    int? taskId,
    String? recurrenceRule,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    int? duration,
    CalendarEventType? eventType,
    EventRecurrencePattern? recurrencePattern,
    int? recurrenceCount,
    DateTime? recurrenceEndDate,
    EventReminderTime? reminderTime,
    bool? isReminderEnabled,
    User? user,
    Task? task,
  }) {
    return CalendarEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      allDay: allDay ?? this.allDay,
      location: location ?? this.location,
      color: color ?? this.color,
      userId: userId ?? this.userId,
      taskId: taskId ?? this.taskId,
      recurrenceRule: recurrenceRule ?? this.recurrenceRule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      duration: duration ?? this.duration,
      eventType: eventType ?? this.eventType,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      recurrenceCount: recurrenceCount ?? this.recurrenceCount,
      recurrenceEndDate: recurrenceEndDate ?? this.recurrenceEndDate,
      reminderTime: reminderTime ?? this.reminderTime,
      isReminderEnabled: isReminderEnabled ?? this.isReminderEnabled,
      user: user ?? this.user,
      task: task ?? this.task,
    );
  }

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime get startDateTime => 
      DateTime.fromMillisecondsSinceEpoch(startTime * 1000);

  /// الحصول على تاريخ النهاية كـ DateTime
  DateTime get endDateTime => 
      DateTime.fromMillisecondsSinceEpoch(endTime * 1000);

  /// الحصول على مدة الحدث بالثواني
  int get eventDurationInSeconds {
    if (duration != null) return duration!;
    return endTime - startTime;
  }

  /// الحصول على مدة الحدث بالدقائق
  int get eventDurationInMinutes {
    return (eventDurationInSeconds / 60).round();
  }

  /// الحصول على مدة الحدث بالساعات
  double get eventDurationInHours {
    return eventDurationInSeconds / 3600;
  }

  /// التحقق من كون الحدث جاري حالياً
  bool get isOngoing {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= startTime && now <= endTime;
  }

  /// التحقق من كون الحدث في المستقبل
  bool get isFuture {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return startTime > now;
  }

  /// التحقق من كون الحدث في الماضي
  bool get isPast {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return endTime < now;
  }

  /// التحقق من كون الحدث اليوم
  bool get isToday {
    final now = DateTime.now();
    final eventStart = startDateTime;
    return eventStart.year == now.year &&
           eventStart.month == now.month &&
           eventStart.day == now.day;
  }

  /// التحقق من كون الحدث غداً
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final eventStart = startDateTime;
    return eventStart.year == tomorrow.year &&
           eventStart.month == tomorrow.month &&
           eventStart.day == tomorrow.day;
  }

  /// الحصول على وصف مدة الحدث
  String get durationDescription {
    final hours = eventDurationInHours;
    final minutes = eventDurationInMinutes % 60;

    if (hours >= 1) {
      if (minutes > 0) {
        return '${hours.floor()} ساعة و $minutes دقيقة';
      } else {
        return '${hours.floor()} ساعة';
      }
    } else {
      return '$minutes دقيقة';
    }
  }

  /// التحقق من تضارب الحدث مع حدث آخر
  bool conflictsWith(CalendarEvent other) {
    return (startTime < other.endTime && endTime > other.startTime);
  }

  @override
  String toString() {
    return 'CalendarEvent(id: $id, title: $title, startTime: $startDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalendarEvent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء حدث تقويم
class CreateCalendarEventRequest {
  final String title;
  final String? description;
  final int startTime;
  final int endTime;
  final bool allDay;
  final String? location;
  final String? color;
  final int? taskId;
  final String? recurrenceRule;

  const CreateCalendarEventRequest({
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.allDay = false,
    this.location,
    this.color,
    this.taskId,
    this.recurrenceRule,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'startTime': startTime,
      'endTime': endTime,
      'allDay': allDay,
      'location': location,
      'color': color,
      'taskId': taskId,
      'recurrenceRule': recurrenceRule,
    };
  }
}

/// نموذج طلب تحديث حدث تقويم
class UpdateCalendarEventRequest {
  final String? title;
  final String? description;
  final int? startTime;
  final int? endTime;
  final bool? allDay;
  final String? location;
  final String? color;
  final int? taskId;
  final String? recurrenceRule;

  const UpdateCalendarEventRequest({
    this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.allDay,
    this.location,
    this.color,
    this.taskId,
    this.recurrenceRule,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (description != null) json['description'] = description;
    if (startTime != null) json['startTime'] = startTime;
    if (endTime != null) json['endTime'] = endTime;
    if (allDay != null) json['allDay'] = allDay;
    if (location != null) json['location'] = location;
    if (color != null) json['color'] = color;
    if (taskId != null) json['taskId'] = taskId;
    if (recurrenceRule != null) json['recurrenceRule'] = recurrenceRule;
    return json;
  }
}

/// نموذج عرض التقويم
class CalendarView {
  final DateTime startDate;
  final DateTime endDate;
  final List<CalendarEvent> events;
  final List<Task> tasks;

  const CalendarView({
    required this.startDate,
    required this.endDate,
    required this.events,
    required this.tasks,
  });

  factory CalendarView.fromJson(Map<String, dynamic> json) {
    return CalendarView(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      events: (json['events'] as List)
          .map((e) => CalendarEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      tasks: (json['tasks'] as List)
          .map((t) => Task.fromJson(t as Map<String, dynamic>))
          .toList(),
    );
  }

  /// الحصول على الأحداث في يوم معين
  List<CalendarEvent> getEventsForDate(DateTime date) {
    return events.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.year == date.year &&
             eventDate.month == date.month &&
             eventDate.day == date.day;
    }).toList();
  }

  /// الحصول على المهام المستحقة في يوم معين
  List<Task> getTasksForDate(DateTime date) {
    return tasks.where((task) {
      if (task.dueDate == null) return false;
      final taskDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
      return taskDate.year == date.year &&
             taskDate.month == date.month &&
             taskDate.day == date.day;
    }).toList();
  }

  /// التحقق من وجود أحداث في يوم معين
  bool hasEventsOnDate(DateTime date) {
    return getEventsForDate(date).isNotEmpty;
  }

  /// التحقق من وجود مهام في يوم معين
  bool hasTasksOnDate(DateTime date) {
    return getTasksForDate(date).isNotEmpty;
  }
}

/// دوال مساعدة لتحليل البيانات من JSON
CalendarEventType _parseEventType(String? value) {
  if (value == null) return CalendarEventType.other;
  switch (value.toLowerCase()) {
    case 'task':
      return CalendarEventType.task;
    case 'meeting':
      return CalendarEventType.meeting;
    case 'reminder':
      return CalendarEventType.reminder;
    case 'vacation':
      return CalendarEventType.vacation;
    case 'other':
    default:
      return CalendarEventType.other;
  }
}

EventRecurrencePattern _parseRecurrencePattern(String? value) {
  if (value == null) return EventRecurrencePattern.none;
  switch (value.toLowerCase()) {
    case 'daily':
      return EventRecurrencePattern.daily;
    case 'weekly':
      return EventRecurrencePattern.weekly;
    case 'monthly':
      return EventRecurrencePattern.monthly;
    case 'yearly':
      return EventRecurrencePattern.yearly;
    case 'none':
    default:
      return EventRecurrencePattern.none;
  }
}

EventReminderTime _parseReminderTime(String? value) {
  if (value == null) return EventReminderTime.none;
  switch (value.toLowerCase()) {
    case 'fiveminutes':
      return EventReminderTime.fiveMinutes;
    case 'fifteenminutes':
      return EventReminderTime.fifteenMinutes;
    case 'thirtyminutes':
      return EventReminderTime.thirtyMinutes;
    case 'onehour':
      return EventReminderTime.oneHour;
    case 'oneday':
      return EventReminderTime.oneDay;
    case 'none':
    default:
      return EventReminderTime.none;
  }
}
