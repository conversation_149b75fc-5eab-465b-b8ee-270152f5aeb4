import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_models.dart';
import '../../services/unified_permission_service.dart';

/// شاشة تذكيرات المهام - عرض المهام المتأخرة والمقتربة من الموعد النهائي
class TaskRemindersScreen extends StatefulWidget {
  const TaskRemindersScreen({super.key});

  @override
  State<TaskRemindersScreen> createState() => _TaskRemindersScreenState();
}

class _TaskRemindersScreenState extends State<TaskRemindersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TaskController _taskController = Get.find<TaskController>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await Future.wait([
      _taskController.loadOverdueTasks(),
      _taskController.loadTasksDueSoon(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تذكيرات المهام'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.warning),
              text: 'المهام المتأخرة',
            ),
            Tab(
              icon: Icon(Icons.schedule),
              text: 'المقتربة من الموعد',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverdueTasksTab(),
          _buildDueSoonTasksTab(),
        ],
      ),
    );
  }

  /// تبويب المهام المتأخرة
  Widget _buildOverdueTasksTab() {
    return Obx(() {
      if (_taskController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_taskController.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل البيانات',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _taskController.error,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _taskController.loadOverdueTasks(),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      final overdueTasks = _taskController.allTasks
          .where((task) => _isTaskOverdue(task))
          .toList();

      if (overdueTasks.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 64,
                color: Colors.green.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد مهام متأخرة',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'جميع المهام في الوقت المحدد',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => _taskController.loadOverdueTasks(),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: overdueTasks.length,
          itemBuilder: (context, index) {
            final task = overdueTasks[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: Colors.red.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'متأخرة بـ ${_getOverdueDuration(task)}',
                          style: TextStyle(
                            color: Colors.red.shade600,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildTaskCard(task),
                ],
              ),
            );
          },
        ),
      );
    });
  }

  /// تبويب المهام المقتربة من الموعد النهائي
  Widget _buildDueSoonTasksTab() {
    return Obx(() {
      if (_taskController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_taskController.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل البيانات',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                _taskController.error,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _taskController.loadTasksDueSoon(),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      final dueSoonTasks = _taskController.allTasks
          .where((task) => _isTaskDueSoon(task))
          .toList();

      if (dueSoonTasks.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.schedule,
                size: 64,
                color: Colors.blue.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد مهام مقتربة من الموعد النهائي',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'جميع المهام لديها وقت كافٍ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () => _taskController.loadTasksDueSoon(),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: dueSoonTasks.length,
          itemBuilder: (context, index) {
            final task = dueSoonTasks[index];
            final urgencyLevel = _getUrgencyLevel(task);
            
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: urgencyLevel.color.withValues(alpha: 0.1),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          urgencyLevel.icon,
                          color: urgencyLevel.color,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'متبقي ${_getRemainingDuration(task)}',
                          style: TextStyle(
                            color: urgencyLevel.color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildTaskCard(task),
                ],
              ),
            );
          },
        ),
      );
    });
  }

  /// التحقق من كون المهمة متأخرة
  bool _isTaskOverdue(Task task) {
    if (task.dueDate == null) return false;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return task.dueDate! < now && 
           task.status != 'مكتملة' && 
           task.status != 'ملغية';
  }

  /// التحقق من كون المهمة مقتربة من الموعد النهائي
  bool _isTaskDueSoon(Task task) {
    if (task.dueDate == null) return false;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final fortyEightHours = 48 * 60 * 60;
    return task.dueDate! <= (now + fortyEightHours) &&
           task.dueDate! > now &&
           task.status != 'مكتملة' &&
           task.status != 'ملغية';
  }

  /// الحصول على مدة التأخير
  String _getOverdueDuration(Task task) {
    if (task.dueDate == null) return '';
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final overdueDuration = now - task.dueDate!;

    if (overdueDuration < 3600) {
      final minutes = overdueDuration ~/ 60;
      return '$minutes دقيقة';
    } else if (overdueDuration < 86400) {
      final hours = overdueDuration ~/ 3600;
      return '$hours ساعة';
    } else {
      final days = overdueDuration ~/ 86400;
      return '$days يوم';
    }
  }

  /// الحصول على المدة المتبقية
  String _getRemainingDuration(Task task) {
    if (task.dueDate == null) return '';
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final remainingDuration = task.dueDate! - now;

    if (remainingDuration < 3600) {
      final minutes = remainingDuration ~/ 60;
      return '$minutes دقيقة';
    } else if (remainingDuration < 86400) {
      final hours = remainingDuration ~/ 3600;
      return '$hours ساعة';
    } else {
      final days = remainingDuration ~/ 86400;
      return '$days يوم';
    }
  }

  /// الحصول على مستوى الإلحاح
  UrgencyLevel _getUrgencyLevel(Task task) {
    if (task.dueDate == null) {
      return UrgencyLevel(
        icon: Icons.schedule,
        color: Colors.blue,
      );
    }

    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final remainingDuration = task.dueDate! - now;

    if (remainingDuration <= 6 * 3600) { // 6 ساعات أو أقل
      return UrgencyLevel(
        icon: Icons.warning,
        color: Colors.red,
      );
    } else if (remainingDuration <= 24 * 3600) { // 24 ساعة أو أقل
      return UrgencyLevel(
        icon: Icons.schedule_outlined,
        color: Colors.orange,
      );
    } else { // 48 ساعة أو أقل
      return UrgencyLevel(
        icon: Icons.schedule,
        color: Colors.blue,
      );
    }
  }

  /// بناء بطاقة المهمة
  Widget _buildTaskCard(Task task) {
    return ListTile(
      title: Row(
        children: [
          // رقم المهمة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            child: Text(
              '#${task.id}',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          // عنوان المهمة
          Expanded(
            child: Text(
              task.title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (task.description?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              task.description!,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              // الحالة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(task.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  task.status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // الأولوية
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getPriorityColor(task.priority),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  task.priority,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () => _navigateToTaskDetail(task),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'مكتملة':
      case 'completed':
        return Colors.green;
      case 'قيد التنفيذ':
      case 'in_progress':
        return Colors.blue;
      case 'معلقة':
      case 'pending':
        return Colors.orange;
      case 'ملغية':
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'عالية':
      case 'high':
        return Colors.red;
      case 'متوسطة':
      case 'medium':
        return Colors.orange;
      case 'منخفضة':
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// الانتقال إلى تفاصيل المهمة
  void _navigateToTaskDetail(Task task) {
    // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
    final permissionService = Get.find<UnifiedPermissionService>();
    if (!permissionService.canViewTaskDetails()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لعرض تفاصيل المهام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    Get.toNamed('/task/detail', arguments: {'taskId': task.id.toString()});
  }
}

/// مستوى الإلحاح
class UrgencyLevel {
  final IconData icon;
  final Color color;

  UrgencyLevel({
    required this.icon,
    required this.color,
  });
}
