/// نماذج بيانات التقارير الاحترافية
/// 
/// هذا الملف يحتوي على جميع النماذج المطلوبة لإنشاء التقارير
/// مع دعم كامل للغة العربية والتوافق مع قاعدة البيانات

import 'package:equatable/equatable.dart';

/// نموذج بيانات تقرير المهام
class TaskReportData  {
  /// معرف المهمة
  final int taskId;
  
  /// عنوان المهمة
  final String title;
  
  /// وصف المهمة
  final String? description;
  
  /// حالة المهمة (مكتملة، قيد التنفيذ، متأخرة، ملغاة)
  final String status;
  
  /// أولوية المهمة (عالية، متوسطة، منخفضة)
  final String priority;
  
  /// نسبة الإنجاز (0-100)
  final int completionPercentage;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ البداية المخطط
  final DateTime? startDate;
  
  /// تاريخ الانتهاء المخطط
  final DateTime? dueDate;
  
  /// تاريخ الإنجاز الفعلي
  final DateTime? completedAt;
  
  /// الوقت المقدر (بالساعات)
  final int? estimatedTime;
  
  /// الوقت الفعلي (بالساعات)
  final int? actualTime;
  
  /// معلومات المنشئ
  final UserInfo creator;
  
  /// معلومات المكلف بالمهمة
  final UserInfo? assignee;
  
  /// معلومات القسم
  final DepartmentInfo? department;
  
  /// نوع المهمة
  final String? taskType;
  
  /// عدد التعليقات
  final int commentsCount;
  
  /// عدد المرفقات
  final int attachmentsCount;
  
  /// هل المهمة محذوفة
  final bool isDeleted;

  const TaskReportData({
    required this.taskId,
    required this.title,
    this.description,
    required this.status,
    required this.priority,
    required this.completionPercentage,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.completedAt,
    this.estimatedTime,
    this.actualTime,
    required this.creator,
    this.assignee,
    this.department,
    this.taskType,
    this.commentsCount = 0,
    this.attachmentsCount = 0,
    this.isDeleted = false,
  });

  /// تحويل من JSON
  factory TaskReportData.fromJson(Map<String, dynamic> json) {
    return TaskReportData(
      taskId: json['id'] is int ? json['id'] : (int.tryParse(json['id']?.toString() ?? '') ?? 0),
      title: json['title']?.toString() ?? '',
      description: json['description']?.toString(),
      status: json['status']?.toString() ?? '',
      priority: json['priority']?.toString() ?? '',
      completionPercentage: json['completionPercentage'] is int ? json['completionPercentage'] : (int.tryParse(json['completionPercentage']?.toString() ?? '') ?? 0),
      createdAt: DateTime.fromMillisecondsSinceEpoch((json['createdAt'] is int ? json['createdAt'] : (int.tryParse(json['createdAt']?.toString() ?? '') ?? 0)) * 1000),
      startDate: json['startDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['startDate'] is int ? json['startDate'] : (int.tryParse(json['startDate']?.toString() ?? '') ?? 0)) * 1000)
          : null,
      dueDate: json['dueDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['dueDate'] is int ? json['dueDate'] : (int.tryParse(json['dueDate']?.toString() ?? '') ?? 0)) * 1000)
          : null,
      completedAt: json['completedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['completedAt'] is int ? json['completedAt'] : (int.tryParse(json['completedAt']?.toString() ?? '') ?? 0)) * 1000)
          : null,
      estimatedTime: json['estimatedTime'] is int ? json['estimatedTime'] : int.tryParse(json['estimatedTime']?.toString() ?? ''),
      actualTime: json['actualTime'] is int ? json['actualTime'] : int.tryParse(json['actualTime']?.toString() ?? ''),
      creator: (json['creator'] is Map<String, dynamic>)
          ? UserInfo.fromJson(json['creator'] as Map<String, dynamic>)
          : UserInfo(id: 0, name: '-', email: '-', role: 0),
      assignee: (json['assignee'] is Map<String, dynamic>)
          ? UserInfo.fromJson(json['assignee'] as Map<String, dynamic>)
          : null,
      department: (json['department'] is Map<String, dynamic>)
          ? DepartmentInfo.fromJson(json['department'] as Map<String, dynamic>)
          : null,
      taskType: json['taskType']?['name'] as String?,
      commentsCount: (json['taskComments'] is List) ? json['taskComments'].length : 0,
      attachmentsCount: (json['attachments'] is List) ? json['attachments'].length : 0,
      isDeleted: json['isDeleted'] as bool? ?? false,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'title': title,
      'description': description,
      'status': status,
      'priority': priority,
      'completionPercentage': completionPercentage,
      'createdAt': createdAt.toIso8601String(),
      'startDate': startDate?.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'estimatedTime': estimatedTime,
      'actualTime': actualTime,
      'creator': creator.toJson(),
      'assignee': assignee?.toJson(),
      'department': department?.toJson(),
      'taskType': taskType,
      'commentsCount': commentsCount,
      'attachmentsCount': attachmentsCount,
      'isDeleted': isDeleted,
    };
  }

  /// حساب عدد الأيام المتأخرة
  int get daysOverdue {
    if (dueDate == null || status == 'مكتملة') return 0;
    final now = DateTime.now();
    if (now.isAfter(dueDate!)) {
      return now.difference(dueDate!).inDays;
    }
    return 0;
  }

  /// هل المهمة متأخرة
  bool get isOverdue => daysOverdue > 0;

  /// حساب مدة المهمة بالأيام
  int? get durationInDays {
    if (startDate == null || dueDate == null) return null;
    return dueDate!.difference(startDate!).inDays;
  }

  /// حساب الوقت المتبقي بالأيام
  int? get remainingDays {
    if (dueDate == null || status == 'مكتملة') return null;
    final now = DateTime.now();
    if (now.isBefore(dueDate!)) {
      return dueDate!.difference(now).inDays;
    }
    return 0;
  }

  @override
  List<Object?> get props => [
    taskId, title, description, status, priority, completionPercentage,
    createdAt, startDate, dueDate, completedAt, estimatedTime, actualTime,
    creator, assignee, department, taskType, commentsCount, attachmentsCount, isDeleted
  ];
}

/// نموذج معلومات المستخدم للتقارير
class UserInfo extends Equatable {
  /// معرف المستخدم
  final int id;
  
  /// اسم المستخدم
  final String name;
  
  /// البريد الإلكتروني
  final String email;
  
  /// اسم المستخدم
  final String? username;
  
  /// صورة الملف الشخصي
  final String? profileImage;
  
  /// الدور
  final int role;
  
  /// هل المستخدم نشط
  final bool isActive;

  const UserInfo({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    this.profileImage,
    required this.role,
    this.isActive = true,
  });

  /// تحويل من JSON
  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] is int ? json['id'] : (int.tryParse(json['id']?.toString() ?? '') ?? 0),
      name: json['name']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      username: json['username']?.toString(),
      profileImage: json['profileImage']?.toString(),
      role: json['role'] is int ? json['role'] : (int.tryParse(json['role']?.toString() ?? '') ?? 0),
      isActive: json['isActive'] is bool ? json['isActive'] : (json['isActive']?.toString().toLowerCase() == 'true'),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'profileImage': profileImage,
      'role': role,
      'isActive': isActive,
    };
  }

  /// الحصول على اسم الدور
  String get roleName {
    switch (role) {
      case 1:
        return 'مدير النظام';
      case 2:
        return 'مدير';
      case 3:
        return 'مشرف';
      case 4:
        return 'موظف';
      default:
        return 'غير محدد';
    }
  }

  @override
  List<Object?> get props => [id, name, email, username, profileImage, role, isActive];
}

/// نموذج معلومات القسم للتقارير
class DepartmentInfo extends Equatable {
  /// معرف القسم
  final int id;
  
  /// اسم القسم
  final String name;
  
  /// وصف القسم
  final String? description;
  
  /// معرف المدير
  final int? managerId;
  
  /// اسم المدير
  final String? managerName;
  
  /// هل القسم نشط
  final bool isActive;
  
  /// المستوى في الهيكل التنظيمي
  final int level;

  const DepartmentInfo({
    required this.id,
    required this.name,
    this.description,
    this.managerId,
    this.managerName,
    this.isActive = true,
    this.level = 0,
  });

  /// تحويل من JSON
  factory DepartmentInfo.fromJson(Map<String, dynamic> json) {
    return DepartmentInfo(
      id: json['id'] is int ? json['id'] : (int.tryParse(json['id']?.toString() ?? '') ?? 0),
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      managerId: json['managerId'] is int ? json['managerId'] : int.tryParse(json['managerId']?.toString() ?? ''),
      managerName: json['manager']?['name']?.toString(),
      isActive: json['isActive'] is bool ? json['isActive'] : (json['isActive']?.toString().toLowerCase() == 'true'),
      level: json['level'] is int ? json['level'] : (int.tryParse(json['level']?.toString() ?? '') ?? 0),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'managerId': managerId,
      'managerName': managerName,
      'isActive': isActive,
      'level': level,
    };
  }

  @override
  List<Object?> get props => [id, name, description, managerId, managerName, isActive, level];
}

/// نموذج إحصائيات التقرير
class ReportStatistics extends Equatable {
  /// إجمالي المهام
  final int totalTasks;
  
  /// المهام المكتملة
  final int completedTasks;
  
  /// المهام قيد التنفيذ
  final int inProgressTasks;
  
  /// المهام المتأخرة
  final int overdueTasks;
  
  /// المهام الملغاة
  final int cancelledTasks;
  
  /// متوسط نسبة الإنجاز
  final double averageCompletion;
  
  /// إجمالي الوقت المقدر
  final int totalEstimatedTime;
  
  /// إجمالي الوقت الفعلي
  final int totalActualTime;
  
  /// عدد المستخدمين النشطين
  final int activeUsers;
  
  /// عدد الأقسام النشطة
  final int activeDepartments;

  const ReportStatistics({
    required this.totalTasks,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.overdueTasks,
    required this.cancelledTasks,
    required this.averageCompletion,
    required this.totalEstimatedTime,
    required this.totalActualTime,
    required this.activeUsers,
    required this.activeDepartments,
  });

  /// حساب نسبة الإنجاز
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// حساب نسبة التأخير
  double get overdueRate {
    if (totalTasks == 0) return 0.0;
    return (overdueTasks / totalTasks) * 100;
  }

  /// حساب كفاءة الوقت
  double get timeEfficiency {
    if (totalEstimatedTime == 0) return 0.0;
    return (totalEstimatedTime / totalActualTime) * 100;
  }

  @override
  List<Object?> get props => [
    totalTasks, completedTasks, inProgressTasks, overdueTasks, cancelledTasks,
    averageCompletion, totalEstimatedTime, totalActualTime, activeUsers, activeDepartments
  ];
}

/// نموذج معايير تصفية التقارير
class ReportFilters extends Equatable {
  /// تاريخ البداية
  final DateTime? startDate;
  
  /// تاريخ النهاية
  final DateTime? endDate;
  
  /// معرفات المستخدمين المحددين
  final List<int>? userIds;
  
  /// معرفات الأقسام المحددة
  final List<int>? departmentIds;
  
  /// حالات المهام المحددة
  final List<String>? statuses;
  
  /// أولويات المهام المحددة
  final List<String>? priorities;
  
  /// أنواع المهام المحددة
  final List<String>? taskTypes;
  
  /// تضمين المهام المحذوفة
  final bool includeDeleted;
  
  /// تضمين المهام المكتملة فقط
  final bool completedOnly;
  
  /// تضمين المهام المتأخرة فقط
  final bool overdueOnly;

  const ReportFilters({
    this.startDate,
    this.endDate,
    this.userIds,
    this.departmentIds,
    this.statuses,
    this.priorities,
    this.taskTypes,
    this.includeDeleted = false,
    this.completedOnly = false,
    this.overdueOnly = false,
  });

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'userIds': userIds,
      'departmentIds': departmentIds,
      'statuses': statuses,
      'priorities': priorities,
      'taskTypes': taskTypes,
      'includeDeleted': includeDeleted,
      'completedOnly': completedOnly,
      'overdueOnly': overdueOnly,
    };
  }

  /// إنشاء نسخة معدلة
  ReportFilters copyWith({
    DateTime? startDate,
    DateTime? endDate,
    List<int>? userIds,
    List<int>? departmentIds,
    List<String>? statuses,
    List<String>? priorities,
    List<String>? taskTypes,
    bool? includeDeleted,
    bool? completedOnly,
    bool? overdueOnly,
  }) {
    return ReportFilters(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userIds: userIds ?? this.userIds,
      departmentIds: departmentIds ?? this.departmentIds,
      statuses: statuses ?? this.statuses,
      priorities: priorities ?? this.priorities,
      taskTypes: taskTypes ?? this.taskTypes,
      includeDeleted: includeDeleted ?? this.includeDeleted,
      completedOnly: completedOnly ?? this.completedOnly,
      overdueOnly: overdueOnly ?? this.overdueOnly,
    );
  }

  @override
  List<Object?> get props => [
    startDate, endDate, userIds, departmentIds, statuses, priorities, taskTypes,
    includeDeleted, completedOnly, overdueOnly
  ];
}