import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

/// مساعد للحصول على معلومات المستخدم الحالي
/// يحل مشكلة TODO items المتعلقة بالحصول على معرف المستخدم
class UserHelper {
  /// الحصول على معرف المستخدم الحالي
  static int getCurrentUserId() {
    try {
      final authController = Get.find<AuthController>();
      return authController.getCurrentUserIdOrDefault();
    } catch (e) {
      // في حالة عدم وجود AuthController، نرجع قيمة افتراضية
      return 1;
    }
  }

  /// الحصول على معرف المستخدم الحالي مع إمكانية إرجاع null
  static int? getCurrentUserIdNullable() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentUserId;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من تسجيل الدخول
  static bool isLoggedIn() {
    try {
      final authController = Get.find<AuthController>();
      return authController.isLoggedIn;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على اسم المستخدم الحالي
  static String getCurrentUserName() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentUser.value?.name ?? 'مستخدم غير معروف';
    } catch (e) {
      return 'مستخدم غير معروف';
    }
  }

  /// الحصول على دور المستخدم الحالي
  static String getCurrentUserRole() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentUser.value?.role.displayName ?? 'مستخدم';
    } catch (e) {
      return 'مستخدم';
    }
  }

  /// التحقق من صلاحيات المستخدم
  static bool hasAdminRights() {
    try {
      final authController = Get.find<AuthController>();
      return authController.isAnyAdmin;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من صلاحيات المدير
  static bool hasManagerRights() {
    try {
      final authController = Get.find<AuthController>();
      return authController.hasManagerRights;
    } catch (e) {
      return false;
    }
  }
}
