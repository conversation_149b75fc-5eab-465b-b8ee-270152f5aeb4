
/// نموذج مبسط وشامل لتقارير المهام
class ReportTaskModel {
  final int id;
  final String title;
  final String? description;
  final String status;
  final String priority;
  final int completionPercentage;
  final DateTime createdAt;
  final DateTime? startDate;
  final DateTime? dueDate;
  final DateTime? completedAt;
  final String? creatorName;
  final String? assigneeName;
  final String? departmentName;
  final String? taskTypeName;
  final List<String> contributors;
  final List<ReportSubtask> subtasks;
  final List<ReportComment> comments;
  final List<ReportAttachment> attachments;

  ReportTaskModel({
    required this.id,
    required this.title,
    this.description,
    required this.status,
    required this.priority,
    required this.completionPercentage,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.completedAt,
    this.creatorName,
    this.assigneeName,
    this.departmentName,
    this.taskTypeName,
    this.contributors = const [],
    this.subtasks = const [],
    this.comments = const [],
    this.attachments = const [],
  });

  /// تحويل من أي كائن مهمة (Task) إلى نموذج التقارير
  factory ReportTaskModel.fromTask(dynamic task) {
    final data = task.toJson();
    return ReportTaskModel(
      id: data['id'] ?? 0,
      title: data['title'] ?? '',
      description: data['description'],
      status: data['status'] ?? '-',
      priority: data['priority'] ?? '-',
      completionPercentage: data['completionPercentage'] ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch((data['createdAt'] ?? 0) * 1000),
      startDate: data['startDate'] != null ? DateTime.fromMillisecondsSinceEpoch(data['startDate'] * 1000) : null,
      dueDate: data['dueDate'] != null ? DateTime.fromMillisecondsSinceEpoch(data['dueDate'] * 1000) : null,
      completedAt: data['completedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(data['completedAt'] * 1000) : null,
      creatorName: data['creator'] is Map ? data['creator']['name'] : null,
      assigneeName: data['assignee'] is Map ? data['assignee']['name'] : null,
      departmentName: data['department'] is Map ? data['department']['name'] : null,
      taskTypeName: data['taskType'] is Map ? data['taskType']['name'] : null,
      contributors: data['contributors'] is List
        ? (data['contributors'] as List).map((c) => c is Map && c['name'] != null ? c['name'] : c.toString()).toList().cast<String>()
        : [],
      subtasks: data['subtasks'] is List
        ? (data['subtasks'] as List).map((s) => ReportSubtask.fromJson(s)).toList()
        : [],
      comments: data['comments'] is List
        ? (data['comments'] as List).map((c) => ReportComment.fromJson(c)).toList()
        : [],
      attachments: data['attachments'] is List
        ? (data['attachments'] as List).map((a) => ReportAttachment.fromJson(a)).toList()
        : [],
    );
  }
}

class ReportSubtask {
  final String title;
  final bool isCompleted;
  ReportSubtask({required this.title, required this.isCompleted});
  factory ReportSubtask.fromJson(dynamic json) {
    if (json is Map) {
      return ReportSubtask(
        title: json['title'] ?? '-',
        isCompleted: json['isCompleted'] ?? false,
      );
    }
    return ReportSubtask(title: json.toString(), isCompleted: false);
  }
}

class ReportComment {
  final String? userName;
  final String text;
  final DateTime? createdAt;
  ReportComment({this.userName, required this.text, this.createdAt});
  factory ReportComment.fromJson(dynamic json) {
    if (json is Map) {
      return ReportComment(
        userName: json['user'] is Map ? json['user']['name'] : null,
        text: json['text'] ?? '-',
        createdAt: json['createdAt'] != null ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'] * 1000) : null,
      );
    }
    return ReportComment(text: json.toString());
  }
}

class ReportAttachment {
  final String fileName;
  final String? url;
  ReportAttachment({required this.fileName, this.url});
  factory ReportAttachment.fromJson(dynamic json) {
    if (json is Map) {
      return ReportAttachment(
        fileName: json['fileName'] ?? '-',
        url: json['url'],
      );
    }
    return ReportAttachment(fileName: json.toString());
  }
}
