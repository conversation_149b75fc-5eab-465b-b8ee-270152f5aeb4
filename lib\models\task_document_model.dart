import 'archive_models.dart';
import 'task_models.dart';
import 'user_model.dart';

/// نموذج ربط المستندات بالمهام باستخدام نظام الأرشفة
/// يوفر تكامل كامل بين نظام المهام ونظام الأرشفة الموجود
class TaskDocument {
  final int taskId;
  final int archiveDocumentId;
  final TaskDocumentType type;
  final String? description;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isShared;
  final TaskDocumentPermission permission;

  // Navigation properties
  final Task? task;
  final ArchiveDocument? archiveDocument;
  final User? createdByUser;

  const TaskDocument({
    required this.taskId,
    required this.archiveDocumentId,
    required this.type,
    this.description,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isShared = false,
    this.permission = TaskDocumentPermission.read,
    this.task,
    this.archiveDocument,
    this.createdByUser,
  });

  /// معرف فريد للمستند (مركب من taskId و archiveDocumentId)
  String get id => '${taskId}_$archiveDocumentId';

  factory TaskDocument.fromJson(Map<String, dynamic> json) {
    return TaskDocument(
      taskId: json['taskId'] as int,
      archiveDocumentId: json['archiveDocumentId'] as int,
      type: TaskDocumentType.fromValue(json['type'] as String? ?? 'report'),
      description: json['description'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isShared: json['isShared'] as bool? ?? false,
      permission: TaskDocumentPermission.fromValue(json['permission'] as String? ?? 'read'),
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      archiveDocument: json['archiveDocument'] != null
          ? ArchiveDocument.fromJson(json['archiveDocument'] as Map<String, dynamic>)
          : null,
      createdByUser: json['createdByUser'] != null
          ? User.fromJson(json['createdByUser'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'archiveDocumentId': archiveDocumentId,
      'type': type.value,
      'description': description,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isShared': isShared,
      'permission': permission.value,
    };
  }

  TaskDocument copyWith({
    int? taskId,
    int? archiveDocumentId,
    TaskDocumentType? type,
    String? description,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    bool? isShared,
    TaskDocumentPermission? permission,
  }) {
    return TaskDocument(
      taskId: taskId ?? this.taskId,
      archiveDocumentId: archiveDocumentId ?? this.archiveDocumentId,
      type: type ?? this.type,
      description: description ?? this.description,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isShared: isShared ?? this.isShared,
      permission: permission ?? this.permission,
      task: task,
      archiveDocument: archiveDocument,
      createdByUser: createdByUser,
    );
  }
}

/// أنواع المستندات المرتبطة بالمهام
enum TaskDocumentType {
  report('report', 'تقرير'),
  analysis('analysis', 'تحليل'),
  plan('plan', 'خطة'),
  attachment('attachment', 'مرفق'),
  note('note', 'ملاحظة'),
  specification('specification', 'مواصفات'),
  documentation('documentation', 'توثيق');

  const TaskDocumentType(this.value, this.displayName);

  final String value;
  final String displayName;

  static TaskDocumentType fromValue(String value) {
    return TaskDocumentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaskDocumentType.report,
    );
  }

  static List<TaskDocumentType> get allTypes => TaskDocumentType.values;
}

/// صلاحيات المستندات في المهام
enum TaskDocumentPermission {
  read('read', 'قراءة فقط'),
  write('write', 'قراءة وكتابة'),
  admin('admin', 'إدارة كاملة');

  const TaskDocumentPermission(this.value, this.displayName);

  final String value;
  final String displayName;

  static TaskDocumentPermission fromValue(String value) {
    return TaskDocumentPermission.values.firstWhere(
      (permission) => permission.value == value,
      orElse: () => TaskDocumentPermission.read,
    );
  }

  bool get canRead => true;
  bool get canWrite => this == TaskDocumentPermission.write || this == TaskDocumentPermission.admin;
  bool get canDelete => this == TaskDocumentPermission.admin;
  bool get canShare => this == TaskDocumentPermission.admin;
}

/// إحصائيات مستندات المهمة
class TaskDocumentStats {
  final int totalDocuments;
  final int reportCount;
  final int analysisCount;
  final int planCount;
  final int attachmentCount;
  final int sharedDocuments;
  final int recentDocuments;

  const TaskDocumentStats({
    required this.totalDocuments,
    required this.reportCount,
    required this.analysisCount,
    required this.planCount,
    required this.attachmentCount,
    required this.sharedDocuments,
    required this.recentDocuments,
  });

  factory TaskDocumentStats.fromJson(Map<String, dynamic> json) {
    return TaskDocumentStats(
      totalDocuments: json['totalDocuments'] ?? 0,
      reportCount: json['reportCount'] ?? 0,
      analysisCount: json['analysisCount'] ?? 0,
      planCount: json['planCount'] ?? 0,
      attachmentCount: json['attachmentCount'] ?? 0,
      sharedDocuments: json['sharedDocuments'] ?? 0,
      recentDocuments: json['recentDocuments'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalDocuments': totalDocuments,
      'reportCount': reportCount,
      'analysisCount': analysisCount,
      'planCount': planCount,
      'attachmentCount': attachmentCount,
      'sharedDocuments': sharedDocuments,
      'recentDocuments': recentDocuments,
    };
  }
}

/// طلب إنشاء مستند مرتبط بمهمة
class CreateTaskDocumentRequest {
  final int taskId;
  final String title;
  final String? description;
  final TaskDocumentType type;
  final String? content;
  final bool isShared;
  final TaskDocumentPermission permission;
  final int createdBy;

  const CreateTaskDocumentRequest({
    required this.taskId,
    required this.title,
    this.description,
    required this.type,
    this.content,
    this.isShared = false,
    this.permission = TaskDocumentPermission.read,
    required this.createdBy,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'title': title,
      'description': description,
      'type': type.value,
      'content': content,
      'isShared': isShared,
      'permission': permission.value,
      'createdBy': createdBy,
    };
  }
}

/// طلب تحديث مستند مرتبط بمهمة
class UpdateTaskDocumentRequest {
  final int taskId;
  final int archiveDocumentId;
  final String? title;
  final String? description;
  final String? content;
  final TaskDocumentType? type;
  final bool? isShared;
  final TaskDocumentPermission? permission;
  final int updatedBy;

  const UpdateTaskDocumentRequest({
    required this.taskId,
    required this.archiveDocumentId,
    this.title,
    this.description,
    this.content,
    this.type,
    this.isShared,
    this.permission,
    required this.updatedBy,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'taskId': taskId,
      'archiveDocumentId': archiveDocumentId,
      'updatedBy': updatedBy,
    };

    if (title != null) json['title'] = title;
    if (description != null) json['description'] = description;
    if (content != null) json['content'] = content;
    if (type != null) json['type'] = type!.value;
    if (isShared != null) json['isShared'] = isShared;
    if (permission != null) json['permission'] = permission!.value;

    return json;
  }
}



/// فلتر البحث في مستندات المهام
class TaskDocumentFilter {
  final int? taskId;
  final TaskDocumentType? type;
  final bool? isShared;
  final TaskDocumentPermission? permission;
  final String? searchQuery;
  final DateTime? fromDate;
  final DateTime? toDate;

  const TaskDocumentFilter({
    this.taskId,
    this.type,
    this.isShared,
    this.permission,
    this.searchQuery,
    this.fromDate,
    this.toDate,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};

    if (taskId != null) params['taskId'] = taskId.toString();
    if (type != null) params['type'] = type!.value;
    if (isShared != null) params['isShared'] = isShared.toString();
    if (permission != null) params['permission'] = permission!.value;
    if (searchQuery != null && searchQuery!.isNotEmpty) params['search'] = searchQuery;
    if (fromDate != null) params['fromDate'] = fromDate!.millisecondsSinceEpoch.toString();
    if (toDate != null) params['toDate'] = toDate!.millisecondsSinceEpoch.toString();

    return params;
  }
}
