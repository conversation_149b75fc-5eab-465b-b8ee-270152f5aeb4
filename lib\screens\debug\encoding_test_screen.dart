// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../../utils/encoding_diagnostics.dart';
// import '../../utils/api_diagnostics.dart';

// /// شاشة اختبار الترميز العربي
// class EncodingTestScreen extends StatefulWidget {
//   const EncodingTestScreen({super.key});

//   @override
//   State<EncodingTestScreen> createState() => _EncodingTestScreenState();
// }

// class _EncodingTestScreenState extends State<EncodingTestScreen> {
//   bool _isRunningTest = false;
//   Map<String, dynamic>? _testResults;
//   Map<String, dynamic>? _apiDiagnostics;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('اختبار الترميز العربي'),
//         backgroundColor: Colors.blue,
//         foregroundColor: Colors.white,
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             _buildTestControls(),
//             const SizedBox(height: 20),
//             if (_isRunningTest) _buildLoadingIndicator(),
//             if (_testResults != null) _buildTestResults(),
//             if (_apiDiagnostics != null) _buildApiDiagnostics(),
//           ],
//         ),
//       ),
//     );
//   }

//   /// بناء أزرار التحكم في الاختبار
//   Widget _buildTestControls() {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             const Text(
//               'اختبارات الترميز والاتصال',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             Row(
//               children: [
//                 Expanded(
//                   child: ElevatedButton.icon(
//                     onPressed: _isRunningTest ? null : _runEncodingTest,
//                     icon: const Icon(Icons.text_fields),
//                     label: const Text('اختبار الترميز'),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.green,
//                       foregroundColor: Colors.white,
//                     ),
//                   ),
//                 ),
//                 const SizedBox(width: 10),
//                 Expanded(
//                   child: ElevatedButton.icon(
//                     onPressed: _isRunningTest ? null : _runApiDiagnostics,
//                     icon: const Icon(Icons.api),
//                     label: const Text('تشخيص API'),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.orange,
//                       foregroundColor: Colors.white,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 10),
//             ElevatedButton.icon(
//               onPressed: _isRunningTest ? null : _runFullTest,
//               icon: const Icon(Icons.play_arrow),
//               label: const Text('تشغيل جميع الاختبارات'),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.blue,
//                 foregroundColor: Colors.white,
//               ),
//             ),
//             const SizedBox(height: 10),
//             ElevatedButton.icon(
//               onPressed: _clearResults,
//               icon: const Icon(Icons.clear),
//               label: const Text('مسح النتائج'),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.red,
//                 foregroundColor: Colors.white,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   /// بناء مؤشر التحميل
//   Widget _buildLoadingIndicator() {
//     return const Card(
//       child: Padding(
//         padding: EdgeInsets.all(16.0),
//         child: Column(
//           children: [
//             CircularProgressIndicator(),
//             SizedBox(height: 16),
//             Text('جاري تشغيل الاختبارات...'),
//           ],
//         ),
//       ),
//     );
//   }

//   /// بناء نتائج اختبار الترميز
//   Widget _buildTestResults() {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text(
//               'نتائج اختبار الترميز',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             _buildEncodingTestSection('اختبار API الخام', _testResults!['rawApiTest']),
//             _buildEncodingTestSection('اختبار UTF-8', _testResults!['utf8Test']),
//             _buildEncodingTestSection('اختبار تحليل JSON', _testResults!['jsonParsingTest']),
//             _buildTaskSampleSection('عينة المهام', _testResults!['taskSampleTest']),
//           ],
//         ),
//       ),
//     );
//   }

//   /// بناء قسم اختبار الترميز
//   Widget _buildEncodingTestSection(String title, Map<String, dynamic>? data) {
//     if (data == null) return const SizedBox.shrink();

//     final isSuccess = data['success'] == true;
    
//     return ExpansionTile(
//       leading: Icon(
//         isSuccess ? Icons.check_circle : Icons.error,
//         color: isSuccess ? Colors.green : Colors.red,
//       ),
//       title: Text(title),
//       subtitle: Text(isSuccess ? 'نجح' : 'فشل'),
//       children: [
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: data.entries.map((entry) {
//               return Padding(
//                 padding: const EdgeInsets.symmetric(vertical: 2.0),
//                 child: Text('${entry.key}: ${entry.value}'),
//               );
//             }).toList(),
//           ),
//         ),
//       ],
//     );
//   }

//   /// بناء قسم عينة المهام
//   Widget _buildTaskSampleSection(String title, Map<String, dynamic>? data) {
//     if (data == null) return const SizedBox.shrink();

//     final isSuccess = data['success'] == true;
    
//     return ExpansionTile(
//       leading: Icon(
//         isSuccess ? Icons.check_circle : Icons.error,
//         color: isSuccess ? Colors.green : Colors.red,
//       ),
//       title: Text(title),
//       subtitle: Text(isSuccess ? 'نجح' : 'فشل'),
//       children: [
//         if (isSuccess && data['taskSamples'] != null)
//           ...((data['taskSamples'] as List).map((sample) {
//             final sampleData = sample as Map<String, dynamic>;
//             final hasArabic = sampleData['hasArabicChars'] == true;
//             final hasQuestionMarks = sampleData['isQuestionMarks'] == true;
            
//             return Card(
//               margin: const EdgeInsets.all(8.0),
//               color: hasQuestionMarks ? Colors.red.shade50 : 
//                      hasArabic ? Colors.green.shade50 : Colors.grey.shade50,
//               child: Padding(
//                 padding: const EdgeInsets.all(12.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text('المعرف: ${sampleData['id']}'),
//                     Text('العنوان: "${sampleData['title']}"'),
//                     Row(
//                       children: [
//                         Icon(
//                           hasArabic ? Icons.check : Icons.close,
//                           color: hasArabic ? Colors.green : Colors.red,
//                           size: 16,
//                         ),
//                         const SizedBox(width: 4),
//                         Text('يحتوي على عربي: ${hasArabic ? 'نعم' : 'لا'}'),
//                       ],
//                     ),
//                     Row(
//                       children: [
//                         Icon(
//                           hasQuestionMarks ? Icons.error : Icons.check,
//                           color: hasQuestionMarks ? Colors.red : Colors.green,
//                           size: 16,
//                         ),
//                         const SizedBox(width: 4),
//                         Text('علامات استفهام: ${hasQuestionMarks ? 'نعم' : 'لا'}'),
//                       ],
//                     ),
//                     Text('طول العنوان: ${sampleData['titleLength']}'),
//                     Text('حجم البايتات: ${sampleData['titleBytes']}'),
//                   ],
//                 ),
//               ),
//             );
//           })),
//         if (!isSuccess)
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Text('خطأ: ${data['error']}'),
//           ),
//       ],
//     );
//   }

//   /// بناء نتائج تشخيص API
//   Widget _buildApiDiagnostics() {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text(
//               'نتائج تشخيص API',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             Text('تم تشغيل التشخيص - راجع وحدة التحكم للتفاصيل'),
//           ],
//         ),
//       ),
//     );
//   }

//   /// تشغيل اختبار الترميز
//   Future<void> _runEncodingTest() async {
//     setState(() {
//       _isRunningTest = true;
//       _testResults = null;
//     });

//     try {
//       final results = await EncodingDiagnostics.runEncodingTest();
//       EncodingDiagnostics.printDiagnosticsReport(results);
      
//       setState(() {
//         _testResults = results;
//       });
      
//       Get.snackbar(
//         'اكتمل الاختبار',
//         'تم تشغيل اختبار الترميز بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//         backgroundColor: Colors.green.shade100,
//         colorText: Colors.green.shade800,
//       );
//     } catch (e) {
//       Get.snackbar(
//         'خطأ',
//         'فشل في تشغيل اختبار الترميز: $e',
//         snackPosition: SnackPosition.BOTTOM,
//         backgroundColor: Colors.red.shade100,
//         colorText: Colors.red.shade800,
//       );
//     } finally {
//       setState(() {
//         _isRunningTest = false;
//       });
//     }
//   }

//   /// تشغيل تشخيص API
//   Future<void> _runApiDiagnostics() async {
//     setState(() {
//       _isRunningTest = true;
//       _apiDiagnostics = null;
//     });

//     try {
//       final results = await ApiDiagnostics.runFullDiagnostics();
//       ApiDiagnostics.printDiagnosticsReport(results);
      
//       setState(() {
//         _apiDiagnostics = results;
//       });
      
//       Get.snackbar(
//         'اكتمل التشخيص',
//         'تم تشغيل تشخيص API بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//         backgroundColor: Colors.blue.shade100,
//         colorText: Colors.blue.shade800,
//       );
//     } catch (e) {
//       Get.snackbar(
//         'خطأ',
//         'فشل في تشغيل تشخيص API: $e',
//         snackPosition: SnackPosition.BOTTOM,
//         backgroundColor: Colors.red.shade100,
//         colorText: Colors.red.shade800,
//       );
//     } finally {
//       setState(() {
//         _isRunningTest = false;
//       });
//     }
//   }

//   /// تشغيل جميع الاختبارات
//   Future<void> _runFullTest() async {
//     await _runEncodingTest();
//     if (!_isRunningTest) {
//       await _runApiDiagnostics();
//     }
//   }

//   /// مسح النتائج
//   void _clearResults() {
//     setState(() {
//       _testResults = null;
//       _apiDiagnostics = null;
//     });
//   }
// }
