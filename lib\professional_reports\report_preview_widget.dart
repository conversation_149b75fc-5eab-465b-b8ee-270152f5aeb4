/// واجهة معاينة تقرير المستخدمين
/// 
/// واجهة بسيطة لعرض قائمة المستخدمين وطباعتها

library;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/user_model.dart';
import '../../services/api/user_api_service.dart';

/// واجهة معاينة تقرير المستخدمين
class ReportPreviewWidget extends StatefulWidget {
  const ReportPreviewWidget({super.key});

  @override
  State<ReportPreviewWidget> createState() => _ReportPreviewWidgetState();
}

class _ReportPreviewWidgetState extends State<ReportPreviewWidget> {
  /// خدمة API للمستخدمين
  final UserApiService _userApiService = UserApiService();
  
  /// قائمة المستخدمين
  final RxList<User> users = <User>[].obs;
  
  /// حالة التحميل
  final RxBool isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  /// تحميل قائمة المستخدمين
  Future<void> _loadUsers() async {
    try {
      isLoading.value = true;
      
      // جلب المستخدمين من API
      final usersList = await _userApiService.getAllUsers();
      users.assignAll(usersList);
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحميل المستخدمين: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// طباعة التقرير
  void _printReport() {
    // هنا يمكن إضافة منطق الطباعة لاحقاً
    Get.snackbar(
      'طباعة',
      'تم إرسال التقرير للطباعة',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تقرير المستخدمين',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        actions: [
          // زر الطباعة
          IconButton(
            icon: const Icon(Icons.print, color: Colors.white),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadUsers,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (users.isEmpty) {
          return const Center(
            child: Text(
              'لا توجد بيانات مستخدمين',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          );
        }

        return Column(
          children: [
            // عنوان التقرير
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.blue.withValues(alpha: 0.1),
              child: Column(
                children: [
                  const Text(
                    'تقرير المستخدمين',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تاريخ التقرير: ${DateTime.now().toString().split(' ')[0]}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إجمالي المستخدمين: ${users.length}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
            
            // قائمة المستخدمين
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: users.length,
                itemBuilder: (context, index) {
                  final user = users[index];
                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.blue,
                        child: Text(
                          user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        user.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('البريد الإلكتروني: ${user.email}'),
                          Text('الدور: ${user.role?.name ?? 'غير محدد'}'),
                          if (user.departmentId != null)
                            Text('معرف القسم: ${user.departmentId}'),
                        ],
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: user.isActive ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          user.isActive ? 'نشط' : 'غير نشط',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}