using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// متحكم علامات الأرشيف
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ArchiveTagsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ArchiveTagsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع علامات الأرشيف
        /// </summary>
        /// <returns>قائمة بجميع علامات الأرشيف</returns>
        /// <response code="200">إرجاع قائمة علامات الأرشيف</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> GetArchiveTags()
        {
            return await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Include(at => at.Documents)
                .Where(at => !at.IsDeleted)
                .OrderBy(at => at.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على علامة أرشيف محددة
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>علامة الأرشيف المطلوبة</returns>
        /// <response code="200">إرجاع علامة الأرشيف</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ArchiveTag>> GetArchiveTag(int id)
        {
            var archiveTag = await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Include(at => at.Documents)
                .FirstOrDefaultAsync(at => at.Id == id && !at.IsDeleted);

            if (archiveTag == null)
            {
                return NotFound();
            }

            return archiveTag;
        }

        /// <summary>
        /// الحصول على مستندات علامة محددة
        /// </summary>
        /// <param name="id">معرف العلامة</param>
        /// <returns>قائمة مستندات العلامة</returns>
        /// <response code="200">إرجاع قائمة مستندات العلامة</response>
        /// <response code="404">العلامة غير موجودة</response>
        [HttpGet("{id}/documents")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetTagDocuments(int id)
        {
            var tag = await _context.ArchiveTags
                .Include(at => at.Documents)
                .ThenInclude(d => d.Category)
                .FirstOrDefaultAsync(at => at.Id == id && !at.IsDeleted);

            if (tag == null)
            {
                return NotFound();
            }

            return Ok(tag.Documents.Where(d => !d.IsDeleted));
        }

        /// <summary>
        /// إنشاء علامة أرشيف جديدة
        /// </summary>
        /// <param name="archiveTag">بيانات علامة الأرشيف</param>
        /// <returns>علامة الأرشيف المُنشأة</returns>
        /// <response code="201">تم إنشاء علامة الأرشيف بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveTag>> PostArchiveTag(ArchiveTag archiveTag)
        {
            // تعيين القيم التلقائية
            archiveTag.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveTag.IsActive = true;
            archiveTag.IsDeleted = false;

            _context.ArchiveTags.Add(archiveTag);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveTag", new { id = archiveTag.Id }, archiveTag);
        }

        /// <summary>
        /// تحديث علامة أرشيف
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <param name="archiveTag">بيانات علامة الأرشيف المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث علامة الأرشيف بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutArchiveTag(int id, ArchiveTag archiveTag)
        {
            if (id != archiveTag.Id)
            {
                return BadRequest();
            }

            var existingTag = await _context.ArchiveTags.FindAsync(id);
            if (existingTag == null || existingTag.IsDeleted)
            {
                return NotFound();
            }

            // تحديث الحقول القابلة للتعديل
            existingTag.Name = archiveTag.Name;
            existingTag.Description = archiveTag.Description;
            existingTag.Color = archiveTag.Color;
            existingTag.IsActive = archiveTag.IsActive;
            existingTag.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ArchiveTagExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف علامة أرشيف (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف علامة الأرشيف بنجاح</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteArchiveTag(int id)
        {
            var archiveTag = await _context.ArchiveTags.FindAsync(id);
            if (archiveTag == null || archiveTag.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي
            archiveTag.IsDeleted = true;
            archiveTag.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// البحث في علامات الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة علامات الأرشيف المطابقة</returns>
        /// <response code="200">إرجاع قائمة علامات الأرشيف المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> SearchTags([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetArchiveTags();
            }

            return await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Include(at => at.Documents)
                .Where(at => !at.IsDeleted && 
                    (at.Name.Contains(searchTerm) || 
                     at.Description.Contains(searchTerm)))
                .OrderBy(at => at.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على العلامات النشطة فقط
        /// </summary>
        /// <returns>قائمة العلامات النشطة</returns>
        /// <response code="200">إرجاع قائمة العلامات النشطة</response>
        [HttpGet("active")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> GetActiveTags()
        {
            return await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Where(at => !at.IsDeleted && at.IsActive)
                .OrderBy(at => at.Name)
                .ToListAsync();
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل علامة أرشيف
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <param name="active">حالة التفعيل</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث حالة العلامة بنجاح</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpPatch("{id}/toggle-active")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleActive(int id, [FromQuery] bool active)
        {
            var archiveTag = await _context.ArchiveTags.FindAsync(id);
            if (archiveTag == null || archiveTag.IsDeleted)
            {
                return NotFound();
            }

            archiveTag.IsActive = active;
            archiveTag.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool ArchiveTagExists(int id)
        {
            return _context.ArchiveTags.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
