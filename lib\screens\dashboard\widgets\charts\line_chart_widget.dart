// مخطط خطي بسيط باستخدام Syncfusion
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';

/// مخطط خطي بسيط باستخدام Syncfusion
class LineChartWidget extends BaseChartWidget {
  const LineChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    return SfCartesianChart(
      title: ChartTitle(text: item.title),
      primaryXAxis: CategoryAxis(),
      primaryYAxis: NumericAxis(),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries<ChartData, String>>[
        LineSeries<ChartData, String>(
          dataSource: data,
          xValueMapper: (ChartData data, _) => data.label,
          yValueMapper: (ChartData data, _) => data.value,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
          enableTooltip: true,
          markerSettings: const MarkerSettings(isVisible: true),
        )
      ],
    );
  }
}
