import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../models/contribution_report_model.dart';
import '../../config/api_config.dart';
import '../storage_service.dart';

/// خدمة API لتقارير المساهمات
/// متوافقة مع ASP.NET Core API
class ContributionReportsApiService {
  static const String _baseEndpoint = '/api/contribution-reports';
  late String _baseUrl;
  final StorageService _storageService = StorageService.instance;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _baseUrl = '${ApiConfig.baseUrl}$_baseEndpoint';
    debugPrint('تم تهيئة ContributionReportsApiService: $_baseUrl');
  }

  /// الحصول على رؤوس HTTP
  Future<Map<String, String>> _getHeaders() async {
    final token = await _storageService.getToken();
    return {
      'Content-Type': 'application/json; charset=UTF-8',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// معالجة الاستجابة
  T _handleResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final Map<String, dynamic> data = json.decode(utf8.decode(response.bodyBytes));
      return fromJson(data);
    } else {
      throw HttpException(
        'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        uri: response.request?.url,
      );
    }
  }

  /// معالجة استجابة القائمة
  List<T> _handleListResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final List<dynamic> data = json.decode(utf8.decode(response.bodyBytes));
      return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } else {
      throw HttpException(
        'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        uri: response.request?.url,
      );
    }
  }

  /// الحصول على جميع التقارير
  Future<List<ContributionReport>> getAllReports() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: headers,
      );

      return _handleListResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      rethrow;
    }
  }

  /// الحصول على تقارير مستخدم محدد
  Future<List<ContributionReport>> getReportsByUserId(String userId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/user/$userId'),
        headers: headers,
      );

      return _handleListResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المستخدم: $e');
      rethrow;
    }
  }

  /// الحصول على تقارير مهمة محددة
  Future<List<ContributionReport>> getReportsByTaskId(String taskId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/task/$taskId'),
        headers: headers,
      );

      return _handleListResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المهمة: $e');
      rethrow;
    }
  }

  /// الحصول على تقارير قسم محدد
  Future<List<ContributionReport>> getReportsByDepartmentId(String departmentId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/department/$departmentId'),
        headers: headers,
      );

      return _handleListResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير القسم: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير محدد
  Future<ContributionReport> getReportById(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$reportId'),
        headers: headers,
      );

      return _handleResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير: $e');
      rethrow;
    }
  }

  /// إنشاء تقرير جديد
  Future<ContributionReport> createReport(ContributionReport report) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: headers,
        body: json.encode(report.toJson()),
      );

      return _handleResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  /// تحديث تقرير
  Future<ContributionReport> updateReport(ContributionReport report) async {
    try {
      final headers = await _getHeaders();
      final response = await http.put(
        Uri.parse('$_baseUrl/${report.id}'),
        headers: headers,
        body: json.encode(report.toJson()),
      );

      return _handleResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في تحديث التقرير: $e');
      rethrow;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/$reportId'),
        headers: headers,
      );

      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات التقرير
  Future<ContributionStatistics> getReportStatistics(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$reportId/statistics'),
        headers: headers,
      );

      return _handleResponse(response, ContributionStatistics.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات التقرير: $e');
      rethrow;
    }
  }

  /// الحصول على مساهمات التقرير
  Future<List<ContributionData>> getReportContributions(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$reportId/contributions'),
        headers: headers,
      );

      return _handleListResponse(response, ContributionData.fromJson);
    } catch (e) {
      debugPrint('خطأ في الحصول على مساهمات التقرير: $e');
      rethrow;
    }
  }

  /// تصدير التقرير بتنسيق PDF
  Future<String> exportReportToPdf(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$reportId/export/pdf'),
        headers: headers,
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(utf8.decode(response.bodyBytes));
        return data['filePath'] as String;
      } else {
        throw HttpException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          uri: response.request?.url,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير PDF: $e');
      rethrow;
    }
  }

  /// تصدير التقرير بتنسيق Excel
  Future<String> exportReportToExcel(String reportId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$reportId/export/excel'),
        headers: headers,
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(utf8.decode(response.bodyBytes));
        return data['filePath'] as String;
      } else {
        throw HttpException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          uri: response.request?.url,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير Excel: $e');
      rethrow;
    }
  }

  /// البحث في التقارير
  Future<List<ContributionReport>> searchReports({
    String? query,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final headers = await _getHeaders();
      final queryParams = <String, String>{};
      
      if (query != null) queryParams['query'] = query;
      if (type != null) queryParams['type'] = type;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final uri = Uri.parse('$_baseUrl/search').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      return _handleListResponse(response, ContributionReport.fromJson);
    } catch (e) {
      debugPrint('خطأ في البحث في التقارير: $e');
      rethrow;
    }
  }
}
