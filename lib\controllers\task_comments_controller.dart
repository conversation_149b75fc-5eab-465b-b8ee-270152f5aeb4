import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_comment_models.dart';
import '../services/api/task_comments_api_service.dart';

/// متحكم تعليقات المهام
class TaskCommentsController extends GetxController {
  final TaskCommentsApiService _apiService = TaskCommentsApiService();

  // قوائم التعليقات
  final RxList<TaskComment> _allComments = <TaskComment>[].obs;
  final RxList<TaskComment> _filteredComments = <TaskComment>[].obs;
  final RxList<TaskComment> _taskComments = <TaskComment>[].obs;

  // التعليق الحالي
  final Rx<TaskComment?> _currentComment = Rx<TaskComment?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _taskFilter = Rx<int?>(null);
  final Rx<int?> _authorFilter = Rx<int?>(null);

  // Getters
  List<TaskComment> get allComments => _allComments;
  List<TaskComment> get filteredComments => _filteredComments;
  List<TaskComment> get taskComments => _taskComments;
  TaskComment? get currentComment => _currentComment.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get taskFilter => _taskFilter.value;
  int? get authorFilter => _authorFilter.value;

  @override
  void onInit() {
    super.onInit();
    loadAllComments();
  }

  /// تحميل جميع تعليقات المهام
  Future<void> loadAllComments() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final comments = await _apiService.getAllTaskComments();
      _allComments.assignAll(comments);
      _applyFilters();
      debugPrint('تم تحميل ${comments.length} تعليق مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل تعليقات المهام: $e';
      debugPrint('خطأ في تحميل تعليقات المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تعليق مهمة بالمعرف
  Future<void> getCommentById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final comment = await _apiService.getTaskCommentById(id);
      if (comment != null) {
        _currentComment.value = comment;
        debugPrint('تم تحميل تعليق المهمة: ${comment.content}');
      } else {
        _error.value = 'التعليق غير موجود';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل تعليق المهمة: $e';
      debugPrint('خطأ في تحميل تعليق المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء تعليق مهمة جديد
  Future<bool> createComment(TaskComment comment) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newComment = await _apiService.createTaskComment(comment);
      _allComments.add(newComment);
      _applyFilters();
      debugPrint('تم إنشاء تعليق مهمة جديد');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء تعليق المهمة: $e';
      debugPrint('خطأ في إنشاء تعليق المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث تعليق مهمة
  Future<bool> updateComment(int id, TaskComment comment) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateTaskComment(id, comment);
      final index = _allComments.indexWhere((c) => c.id == id);
      if (index != -1) {
        _allComments[index] = comment;
        _applyFilters();
      }
      debugPrint('تم تحديث تعليق المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث تعليق المهمة: $e';
      debugPrint('خطأ في تحديث تعليق المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف تعليق مهمة
  Future<bool> deleteComment(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteTaskComment(id);
      _allComments.removeWhere((c) => c.id == id);
      _applyFilters();
      debugPrint('تم حذف تعليق المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف تعليق المهمة: $e';
      debugPrint('خطأ في حذف تعليق المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تعليقات المهمة
  Future<void> getCommentsByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final comments = await _apiService.getCommentsByTask(taskId);
      _taskComments.assignAll(comments);
      debugPrint('تم تحميل ${comments.length} تعليق للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل تعليقات المهمة: $e';
      debugPrint('خطأ في تحميل تعليقات المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تعليقات المؤلف
  Future<void> getCommentsByAuthor(int authorId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final comments = await _apiService.getCommentsByUser(authorId);
      _allComments.assignAll(comments);
      _applyFilters();
      debugPrint('تم تحميل ${comments.length} تعليق للمؤلف $authorId');
    } catch (e) {
      _error.value = 'خطأ في تحميل تعليقات المؤلف: $e';
      debugPrint('خطأ في تحميل تعليقات المؤلف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allComments.where((comment) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!comment.content.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المهمة
      if (_taskFilter.value != null && comment.taskId != _taskFilter.value) {
        return false;
      }

      // مرشح المؤلف
      if (_authorFilter.value != null && comment.userId != _authorFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredComments.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskFilter.value = taskId;
    _applyFilters();
  }

  /// تعيين مرشح المؤلف
  void setAuthorFilter(int? authorId) {
    _authorFilter.value = authorId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _taskFilter.value = null;
    _authorFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllComments();
  }
}
