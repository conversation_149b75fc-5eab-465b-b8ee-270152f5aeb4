/// خدمة الرسوم البيانية للتقارير الاحترافية
/// 
/// هذه الخدمة مسؤولة عن إنشاء وتخصيص الرسوم البيانية باستخدام Syncfusion Charts
/// مع دعم كامل للغة العربية والتصميم الاحترافي

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:flutter_application_2/professional_reports/models/report_data_models.dart';

/// نموذج بيانات الرسم البياني
class ChartDataPoint {
  /// التسمية
  final String label;
  
  /// القيمة
  final double value;
  
  /// اللون
  final Color color;
  
  /// معلومات إضافية
  final Map<String, dynamic>? metadata;

  const ChartDataPoint({
    required this.label,
    required this.value,
    required this.color,
    this.metadata,
  });
}

/// خدمة الرسوم البيانية
class ChartService {
  /// الألوان الافتراضية للرسوم البيانية
  static const List<Color> defaultColors = [
    Color(0xFF2196F3), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFFF9800), // برتقالي
    Color(0xFFF44336), // أحمر
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFF00BCD4), // سماوي
    Color(0xFFFFEB3B), // أصفر
    Color(0xFF795548), // بني
    Color(0xFF607D8B), // رمادي مزرق
    Color(0xFFE91E63), // وردي
  ];

  /// إنشاء رسم بياني دائري لحالة المهام
  Widget createTaskStatusPieChart(List<TaskReportData> tasks) {
    // حساب عدد المهام لكل حالة
    final statusCounts = <String, int>{};
    for (final task in tasks) {
      statusCounts[task.status] = (statusCounts[task.status] ?? 0) + 1;
    }

    // تحويل البيانات إلى نقاط رسم بياني
    final chartData = <ChartDataPoint>[];
    int colorIndex = 0;
    
    statusCounts.forEach((status, count) {
      chartData.add(ChartDataPoint(
        label: status,
        value: count.toDouble(),
        color: defaultColors[colorIndex % defaultColors.length],
      ));
      colorIndex++;
    });

    return SfCircularChart(
      title: ChartTitle(
        text: 'توزيع المهام حسب الحالة',
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        textStyle: const TextStyle(fontSize: 12),
      ),
      series: <PieSeries<ChartDataPoint, String>>[
        PieSeries<ChartDataPoint, String>(
          dataSource: chartData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
      ),
    );
  }

  /// إنشاء رسم بياني عمودي لأولوية المهام
  Widget createTaskPriorityColumnChart(List<TaskReportData> tasks) {
    // حساب عدد المهام لكل أولوية
    final priorityCounts = <String, int>{};
    for (final task in tasks) {
      priorityCounts[task.priority] = (priorityCounts[task.priority] ?? 0) + 1;
    }

    // تحويل البيانات إلى نقاط رسم بياني
    final chartData = <ChartDataPoint>[];
    
    priorityCounts.forEach((priority, count) {
      chartData.add(ChartDataPoint(
        label: priority,
        value: count.toDouble(),
        color: _getPriorityColor(priority),
      ));
    });

    return SfCartesianChart(
      title: ChartTitle(
        text: 'توزيع المهام حسب الأولوية',
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'الأولوية'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام'),
      ),
      series: <ColumnSeries<ChartDataPoint, String>>[
        ColumnSeries<ChartDataPoint, String>(
          dataSource: chartData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          pointColorMapper: (ChartDataPoint data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
      ),
    );
  }

  /// إنشاء رسم بياني خطي لتقدم المهام عبر الوقت
  Widget createTaskProgressLineChart(List<TaskReportData> tasks) {
    // تجميع المهام حسب تاريخ الإنشاء
    final Map<DateTime, int> dailyTasks = {};
    
    for (final task in tasks) {
      final date = DateTime(
        task.createdAt.year,
        task.createdAt.month,
        task.createdAt.day,
      );
      dailyTasks[date] = (dailyTasks[date] ?? 0) + 1;
    }

    // ترتيب التواريخ وتحويلها إلى نقاط رسم بياني
    final sortedDates = dailyTasks.keys.toList()..sort();
    final chartData = <ChartDataPoint>[];
    
    for (final date in sortedDates) {
      chartData.add(ChartDataPoint(
        label: '${date.day}/${date.month}',
        value: dailyTasks[date]!.toDouble(),
        color: defaultColors[0],
        metadata: {'date': date},
      ));
    }

    return SfCartesianChart(
      title: ChartTitle(
        text: 'إنشاء المهام عبر الوقت',
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'التاريخ'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام'),
      ),
      series: <LineSeries<ChartDataPoint, String>>[
        LineSeries<ChartDataPoint, String>(
          dataSource: chartData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: defaultColors[0],
          width: 3,
          markerSettings: const MarkerSettings(
            isVisible: true,
            shape: DataMarkerType.circle,
            width: 6,
            height: 6,
          ),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
      ),
    );
  }

  /// إنشاء رسم بياني مساحي لمعدل الإنجاز
  Widget createCompletionRateAreaChart(List<TaskReportData> tasks) {
    // حساب معدل الإنجاز اليومي
    final Map<DateTime, double> dailyCompletion = {};
    
    // تجميع المهام المكتملة حسب تاريخ الإكمال
    final completedTasks = tasks.where((task) => task.completedAt != null).toList();
    
    for (final task in completedTasks) {
      final date = DateTime(
        task.completedAt!.year,
        task.completedAt!.month,
        task.completedAt!.day,
      );
      dailyCompletion[date] = (dailyCompletion[date] ?? 0) + 1;
    }

    // ترتيب التواريخ وتحويلها إلى نقاط رسم بياني
    final sortedDates = dailyCompletion.keys.toList()..sort();
    final chartData = <ChartDataPoint>[];
    
    for (final date in sortedDates) {
      chartData.add(ChartDataPoint(
        label: '${date.day}/${date.month}',
        value: dailyCompletion[date]!,
        color: defaultColors[1],
        metadata: {'date': date},
      ));
    }

    return SfCartesianChart(
      title: ChartTitle(
        text: 'معدل إكمال المهام اليومي',
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'التاريخ'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام المكتملة'),
      ),
      series: <AreaSeries<ChartDataPoint, String>>[
        AreaSeries<ChartDataPoint, String>(
          dataSource: chartData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: defaultColors[1].withValues(alpha: 0.7),
          borderColor: defaultColors[1],
          borderWidth: 2,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
      ),
    );
  }

  /// إنشاء رسم بياني عمودي لأداء الأقسام
  Widget createDepartmentPerformanceChart(List<TaskReportData> tasks) {
    // حساب عدد المهام لكل قسم
    final departmentCounts = <String, int>{};
    final departmentCompleted = <String, int>{};
    
    for (final task in tasks) {
      final deptName = task.department?.name ?? 'غير محدد';
      departmentCounts[deptName] = (departmentCounts[deptName] ?? 0) + 1;
      
      if (task.status == 'مكتملة') {
        departmentCompleted[deptName] = (departmentCompleted[deptName] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى نقاط رسم بياني
    final totalTasksData = <ChartDataPoint>[];
    final completedTasksData = <ChartDataPoint>[];
    
    departmentCounts.forEach((dept, total) {
      final completed = departmentCompleted[dept] ?? 0;
      
      totalTasksData.add(ChartDataPoint(
        label: dept,
        value: total.toDouble(),
        color: defaultColors[0],
      ));
      
      completedTasksData.add(ChartDataPoint(
        label: dept,
        value: completed.toDouble(),
        color: defaultColors[1],
      ));
    });

    return SfCartesianChart(
      title: ChartTitle(
        text: 'أداء الأقسام',
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      legend: Legend(
        isVisible: true,
        position: LegendPosition.bottom,
      ),
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'القسم'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام'),
      ),
      series: <ColumnSeries<ChartDataPoint, String>>[
        ColumnSeries<ChartDataPoint, String>(
          name: 'إجمالي المهام',
          dataSource: totalTasksData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: defaultColors[0],
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
        ),
        ColumnSeries<ChartDataPoint, String>(
          name: 'المهام المكتملة',
          dataSource: completedTasksData,
          xValueMapper: (ChartDataPoint data, _) => data.label,
          yValueMapper: (ChartDataPoint data, _) => data.value,
          color: defaultColors[1],
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(fontSize: 10),
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x - point.seriesName: point.y مهمة',
      ),
    );
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'عالية':
      case 'high':
        return Colors.red;
      case 'متوسطة':
      case 'medium':
        return Colors.orange;
      case 'منخفضة':
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// تحليل الأداء وإرجاع تقييم نصي
  /// يأخذ إحصائيات التقرير ويعيد تقييماً نصياً بناءً على نسبة الإنجاز
  String analyzePerformance(ReportStatistics statistics) {
    // الحصول على نسبة الإنجاز من الإحصائيات
    final completionRate = statistics.completionRate;
    
    // تحديد التقييم بناءً على نسبة الإنجاز
    if (completionRate >= 90) {
      return 'ممتاز';
    } else if (completionRate >= 75) {
      return 'جيد جداً';
    } else if (completionRate >= 60) {
      return 'جيد';
    } else if (completionRate >= 40) {
      return 'مقبول';
    } else {
      return 'ضعيف';
    }
  }
}