import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../constants/app_styles.dart';
import '../../models/task_progress_models.dart';

/// تبويب مساهمات المستخدم - مبسط
class SimpleContributorContributionsTab extends StatelessWidget {
  final String taskId;
  final VoidCallback? onContributorDeselected;

  const SimpleContributorContributionsTab({
    super.key,
    required this.taskId,
    this.onContributorDeselected,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TaskController>(
      builder: (controller) {
        final userId = controller.selectedContributorId;
        
        if (userId.isEmpty) {
          return _buildNoSelectionState();
        }

        return _buildContributionsView(userId);
      },
    );
  }

  /// عرض المساهمات
  Widget _buildContributionsView(String userId) {

    return FutureBuilder<List<TaskProgressTracker>>(
      future: _loadContributions(userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorState(snapshot.error.toString());
        }

        final contributions = snapshot.data ?? [];
        if (contributions.isEmpty) {
          return _buildEmptyState();
        }

        return _buildContributionsList(contributions);
      },
    );
  }

  /// تحميل المساهمات
  Future<List<TaskProgressTracker>> _loadContributions(String userId) async {
    final controller = Get.find<TaskController>();
    return await controller.getProgressTrackersForUserInTask(taskId, userId);
  }

  /// حالة عدم اختيار مساهم
  Widget _buildNoSelectionState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.person_search, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text('اختر مساهم من تبويب المساهمين لعرض مساهماته'),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // الانتقال إلى تبويب المساهمين
              final tabController = Get.find<TabController>(tag: 'task_detail_tabs');
              tabController.animateTo(10); // تبويب المساهمين
            },
            icon: const Icon(Icons.people),
            label: const Text('انتقل إلى تبويب المساهمين'),
          ),
        ],
      ),
    );
  }

  /// حالة الخطأ
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('حدث خطأ: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Get.find<TaskController>().update(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('لا توجد مساهمات مسجلة لهذا المستخدم'),
        ],
      ),
    );
  }

  /// قائمة المساهمات
  Widget _buildContributionsList(List<TaskProgressTracker> contributions) {
    return Column(
      children: [
        _buildHeader(contributions),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: contributions.length,
            itemBuilder: (context, index) {
              return _buildContributionCard(contributions[index]);
            },
          ),
        ),
      ],
    );
  }

  /// رأس القائمة
  Widget _buildHeader(List<TaskProgressTracker> contributions) {
    final totalContribution = contributions.fold<double>(
      0.0, 
      (sum, c) => sum + c.contributionPercentage,
    );

    // الحصول على اسم المساهم
    final controller = Get.find<TaskController>();
    final contributorId = int.tryParse(controller.selectedContributorId);
    final contributor = contributorId != null 
        ? controller.getContributorByUserId(contributorId)
        : null;
    final contributorName = contributor?.userName ?? 'مستخدم غير معروف';

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade50,
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: onContributorDeselected,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('مساهمات $contributorName', style: AppStyles.titleMedium),
                Text('${contributions.length} مساهمة - إجمالي ${totalContribution.toStringAsFixed(1)}%'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة المساهمة
  Widget _buildContributionCard(TaskProgressTracker contribution) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getContributionColor(contribution),
          child: Icon(
            _getContributionIcon(contribution),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: contribution.evidenceType == 'transfer'
            ? _buildTransferTitle(contribution)
            : Text(_getContributionTitle(contribution)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_formatDate(contribution.updatedAt)),
            if (contribution.evidenceType == 'transfer')
              _buildTransferSubtitle(contribution)
            else if (contribution.notes?.isNotEmpty == true)
              Text(contribution.notes!, maxLines: 2, overflow: TextOverflow.ellipsis),
          ],
        ),
        trailing: Text(
          '${contribution.contributionPercentage.toStringAsFixed(1)}%',
          style: AppStyles.titleSmall.copyWith(
            color: _getContributionColor(contribution),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// لون المساهمة
  Color _getContributionColor(TaskProgressTracker contribution) {
    final type = contribution.evidenceType ?? 'activity';
    switch (type) {
      case 'comment': return Colors.blue;
      case 'file': return Colors.orange;
      case 'transfer': return Colors.purple;
      case 'completion': return Colors.green;
      case 'progress': return Colors.teal;
      default: return Colors.grey;
    }
  }

  /// أيقونة المساهمة
  IconData _getContributionIcon(TaskProgressTracker contribution) {
    final type = contribution.evidenceType ?? 'activity';
    switch (type) {
      case 'comment': return Icons.comment;
      case 'file': return Icons.attach_file;
      case 'transfer': return Icons.swap_horiz;
      case 'completion': return Icons.check_circle;
      case 'progress': return Icons.trending_up;
      default: return Icons.timeline;
    }
  }

  /// عنوان المساهمة
  String _getContributionTitle(TaskProgressTracker contribution) {
    final type = contribution.evidenceType ?? 'activity';
    switch (type) {
      case 'comment': return 'إضافة تعليق';
      case 'file': return 'إرفاق ملف';
      case 'transfer': return _getTransferTitle(contribution);
      case 'completion': return 'إكمال المهمة';
      case 'progress': return 'تحديث التقدم';
      default: return 'نشاط في المهمة';
    }
  }

  /// الحصول على عنوان التحويل البسيط
  String _getTransferTitle(TaskProgressTracker contribution) {
    // التحقق من نوع التحويل (مرسل أم مستقبل)
    if (contribution.notes?.contains('تم تحويل المهمة إلى مستخدم آخر') == true) {
      return 'تحويل المهمة';
    } else if (contribution.notes?.contains('تم استلام المهمة من مستخدم آخر') == true) {
      return 'استلام المهمة';
    }
    return 'تحويل المهمة';
  }

  /// الحصول على معرف المستخدم المحول إليه من TaskHistory
  Future<String> _getTransferTargetUserId(TaskProgressTracker contribution) async {
    try {
      final controller = Get.find<TaskController>();

      // البحث في TaskHistory عن سجل التحويل في نفس الوقت
      final taskHistory = controller.taskHistory;

      for (final history in taskHistory) {
        // التحقق من أن هذا سجل تحويل في نفس الوقت تقريباً
        final timeDiff = (history.timestamp - contribution.updatedAt).abs();
        if (timeDiff <= 30 && // خلال 30 ثانية
            history.action == 'transferred' &&
            history.details != null) {

          try {
            // تحليل JSON في details
            final detailsMap = json.decode(history.details!) as Map<String, dynamic>;

            // إذا كان هذا سجل المرسل، أرجع معرف المستقبل
            if (contribution.notes?.contains('تم تحويل المهمة إلى مستخدم آخر') == true) {
              return detailsMap['newAssigneeId']?.toString() ?? '';
            }

            // إذا كان هذا سجل المستقبل، أرجع معرف المرسل
            if (contribution.notes?.contains('تم استلام المهمة من مستخدم آخر') == true) {
              return detailsMap['previousAssigneeId']?.toString() ?? '';
            }
          } catch (jsonError) {
            debugPrint('خطأ في تحليل JSON: $jsonError');
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدم المحول إليه: $e');
    }

    return '';
  }

  /// بناء عنوان التحويل مع اسم المستخدم
  Widget _buildTransferTitle(TaskProgressTracker contribution) {
    return FutureBuilder<String>(
      future: _getTransferTargetUserId(contribution),
      builder: (context, targetSnapshot) {
        if (targetSnapshot.connectionState == ConnectionState.waiting) {
          return const Text('تحويل المهمة...');
        }

        final targetUserId = targetSnapshot.data ?? '';

        if (targetUserId.isNotEmpty) {
          return FutureBuilder<String>(
            future: Get.find<UserController>().getUserNameById(targetUserId),
            builder: (context, nameSnapshot) {
              if (nameSnapshot.connectionState == ConnectionState.waiting) {
                return const Text('تحويل المهمة...');
              }

              final targetUserName = nameSnapshot.data ?? 'مستخدم غير معروف';

              // تحديد نوع التحويل
              if (contribution.notes?.contains('تم تحويل المهمة إلى مستخدم آخر') == true) {
                return Text('تحويل المهمة إلى $targetUserName');
              } else if (contribution.notes?.contains('تم استلام المهمة من مستخدم آخر') == true) {
                return Text('استلام المهمة من $targetUserName');
              } else {
                return const Text('تحويل المهمة');
              }
            },
          );
        }

        // في حالة عدم وجود معرف المستخدم، عرض النص الأصلي
        return Text(_getTransferTitle(contribution));
      },
    );
  }

  /// بناء subtitle للتحويل (مبسط)
  Widget _buildTransferSubtitle(TaskProgressTracker contribution) {
    // عرض الملاحظات إذا كانت موجودة ومختلفة عن النص الافتراضي
    if (contribution.notes?.isNotEmpty == true) {
      final notes = contribution.notes!;
      // تجاهل النصوص الافتراضية وعرض الملاحظات المخصصة فقط
      if (!notes.contains('تم تحويل المهمة إلى مستخدم آخر') &&
          !notes.contains('تم استلام المهمة من مستخدم آخر')) {
        return Text(notes, maxLines: 2, overflow: TextOverflow.ellipsis);
      }
    }

    return const SizedBox.shrink(); // لا نعرض شيء إضافي
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return DateFormat('yyyy/MM/dd HH:mm').format(date);
  }
}