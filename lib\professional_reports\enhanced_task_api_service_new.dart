import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/task_report_models.dart';

import 'dart:convert';
import 'package:flutter_application_2/services/api/api_service.dart';

/// خدمة API محسنة للمهام - تستخدم لجلب البيانات الشاملة للتقارير
class EnhancedTaskApiService {
  
  /// جلب البيانات الشاملة للمهمة من جميع المصادر
  /// 
  /// [taskId] - معرف المهمة
  /// يحاول جلب البيانات من API، وفي حالة الفشل يرجع قوائم فارغة
  Future<Map<String, dynamic>> getTaskComprehensiveReportData(dynamic taskId) async {
    try {
      // محاولة جلب البيانات من API الحقيقي
      if (kDebugMode) {
        print('🔄 محاولة جلب البيانات الشاملة للمهمة $taskId');
      }
      
      // استدعاء API الحقيقي للحصول على البيانات الشاملة للمهمة
      final apiService = ApiService();
      final response = await apiService.get('/api/Tasks/$taskId/comprehensive-report-data');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (kDebugMode) {
          print('✅ تم جلب البيانات الشاملة للمهمة بنجاح');
          print('📊 البيانات تحتوي على:');
          print('   - المساهمون (accessUsers): ${data['accessUsers']?.length ?? 0}');
          print('   - التحويلات (taskHistories): ${data['taskHistories']?.length ?? 0}');
          print('   - التعليقات: ${data['comments']?.length ?? 0}');
          print('   - المرفقات: ${data['attachments']?.length ?? 0}');
          print('   - المهام الفرعية: ${data['subtasks']?.length ?? 0}');
        }
        
        return data;
      } else {
        if (kDebugMode) {
          print('⚠️ فشل في جلب البيانات الشاملة للمهمة: ${response.statusCode}');
          print('⚠️ الرسالة: ${response.body}');
        }
        
        // في حالة فشل استدعاء API، نحاول جلب بيانات المساهمين من جدول task_access_users
        final Map<String, dynamic> contributorsData = await _fetchTaskAccessUsers(taskId);
        
        if (kDebugMode) {
          print('📊 تم جلب ${contributorsData['contributors']?.length ?? 0} مساهم من جدول task_access_users');
          if (contributorsData['contributors'] != null) {
            for (var contributor in contributorsData['contributors']) {
              print('   - ${contributor['userName']}: ${contributor['totalContributions']} مساهمة');
            }
          }
        }
        
        return contributorsData;
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في جلب بيانات التقرير: $e');
      }
      
      // في حالة الخطأ، إرجاع بيانات احتياطية أساسية
      return _generateBasicReportData(taskId);
    }
  }
  
  /// جلب بيانات المساهمين من جدول task_access_users
  Future<Map<String, dynamic>> _fetchTaskAccessUsers(dynamic taskId) async {
    try {
      // ملاحظة: سيتم استبدال هذا بالاستدعاء الحقيقي لقاعدة البيانات
      // مثال:
      // final db = await DatabaseHelper.instance.database;
      // final results = await db.query(
      //   'task_access_users',
      //   where: 'task_id = ?',
      //   whereArgs: [taskId],
      // );
      
      // لا نرجع أي بيانات افتراضية - نستخدم البيانات الحقيقية فقط
      if (kDebugMode) {
        print('ℹ️ لا توجد بيانات حقيقية في قاعدة البيانات - إرجاع قائمة فارغة');
      }
      
      return {
        'contributors': [],
        'transfers': []
      };
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في جلب بيانات المساهمين: $e');
      }
      return {};
    }
  }
  
  /// إنشاء بيانات احتياطية أساسية - بدون بيانات افتراضية
  Map<String, dynamic> _generateBasicReportData(dynamic taskId) {
    if (kDebugMode) {
      print('ℹ️ إرجاع بيانات فارغة - لا نستخدم بيانات افتراضية');
    }
    
    return {
      'contributors': [],
      'transfers': [],
      'progressTrackers': [],
      'timeTrackingEntries': [],
    };
  }
  
  /// إنشاء نموذج تقرير المهمة من البيانات المحسنة
  /// استخدام البيانات الحقيقية فقط بدون الطريقة البديلة
  TaskReportModel createEnhancedTaskReportModel(Map<String, dynamic> reportData) {
    try {
      if (kDebugMode) {
        print('🔄 إنشاء نموذج تقرير المهمة من البيانات المحسنة');
        print('   - المساهمون (accessUsers): ${reportData['accessUsers']?.length ?? 0}');
        print('   - التحويلات (taskHistories): ${reportData['taskHistories']?.length ?? 0}');
        print('   - التعليقات: ${reportData['comments']?.length ?? 0}');
        print('   - المرفقات: ${reportData['attachments']?.length ?? 0}');
        print('   - المهام الفرعية: ${reportData['subtasks']?.length ?? 0}');
      }
      
      // استخراج المهمة من البيانات
      Task task;
      if (reportData.containsKey('task') && reportData['task'] != null) {
        task = Task.fromJson(reportData['task'] as Map<String, dynamic>);
      } else {
        // إنشاء مهمة من البيانات الأساسية مع معالجة آمنة للقيم
        int id = 0;
        int creatorId = 1;
        int? assigneeId;
        int createdAt = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        int completionPercentage = 0;
        
        try {
          id = reportData['id'] is int ? reportData['id'] as int : (int.tryParse(reportData['id']?.toString() ?? '') ?? 0);
        } catch (e) {
          if (kDebugMode) print('⚠️ خطأ في معالجة id: $e');
        }
        
        try {
          creatorId = reportData['creatorId'] is int ? reportData['creatorId'] as int : (int.tryParse(reportData['creatorId']?.toString() ?? '') ?? 1);
        } catch (e) {
          if (kDebugMode) print('⚠️ خطأ في معالجة creatorId: $e');
        }
        
        try {
          assigneeId = reportData['assigneeId'] is int ? reportData['assigneeId'] as int : 
                      reportData['assigneeId'] != null ? int.tryParse(reportData['assigneeId']?.toString() ?? '') : null;
        } catch (e) {
          if (kDebugMode) print('⚠️ خطأ في معالجة assigneeId: $e');
        }
        
        try {
          createdAt = reportData['createdAt'] is int ? reportData['createdAt'] as int : (int.tryParse(reportData['createdAt']?.toString() ?? '') ?? (DateTime.now().millisecondsSinceEpoch ~/ 1000));
        } catch (e) {
          if (kDebugMode) print('⚠️ خطأ في معالجة createdAt: $e');
        }
        
        try {
          completionPercentage = reportData['completionPercentage'] is int ? reportData['completionPercentage'] as int : (int.tryParse(reportData['completionPercentage']?.toString() ?? '') ?? 0);
        } catch (e) {
          if (kDebugMode) print('⚠️ خطأ في معالجة completionPercentage: $e');
        }
        
        task = Task(
          id: id,
          title: reportData['title']?.toString() ?? 'مهمة غير محددة',
          description: reportData['description']?.toString(),
          creatorId: creatorId,
          assigneeId: assigneeId,
          createdAt: createdAt,
          status: reportData['status']?.toString() ?? 'نشطة',
          priority: reportData['priority']?.toString() ?? 'متوسطة',
          completionPercentage: completionPercentage,
        );
      }
      
      // استخراج المساهمين من accessUsers الحقيقية
      List<TaskContributor> contributors = [];
      
      // استخدام accessUsers دائماً كمصدر أساسي للمساهمين
      if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
        if (kDebugMode) {
          print('🔍 استخراج المساهمين من accessUsers مباشرة');
          print('   - عدد المساهمين في البيانات: ${(reportData['accessUsers'] as List).length}');
        }
        
        final accessUsers = reportData['accessUsers'] as List;
        
        // حساب إحصائيات حقيقية لكل مساهم
        for (var userJson in accessUsers) {
          if (userJson is Map<String, dynamic>) {
            try {
              // معالجة آمنة لمعرف المستخدم
              int userId = 0;
              try {
                if (userJson['id'] is int) {
                  userId = userJson['id'] as int;
                } else if (userJson['id'] is String) {
                  userId = int.tryParse(userJson['id'] as String) ?? 0;
                } else if (userJson['id'] != null) {
                  userId = int.tryParse(userJson['id'].toString()) ?? 0;
                }
              } catch (e) {
                if (kDebugMode) {
                  print('⚠️ خطأ في معالجة معرف المستخدم: $e');
                  print('⚠️ قيمة المعرف: ${userJson['id']} (نوع: ${userJson['id'].runtimeType})');
                }
                userId = 0;
              }
              
              final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
              final userEmail = userJson['email']?.toString() ?? '';
              
              // حساب المساهمات الحقيقية من البيانات المتوفرة
              int commentsCount = 0;
              int attachmentsCount = 0;
              int transfersCount = 0;
              
              try {
                // حساب التعليقات
                if (reportData.containsKey('comments') && reportData['comments'] is List) {
                  final comments = reportData['comments'] as List;
                  commentsCount = comments.where((comment) {
                    try {
                      if (comment is Map<String, dynamic> && comment.containsKey('user')) {
                        final commentUser = comment['user'];
                        if (commentUser is Map<String, dynamic>) {
                          final commentUserId = commentUser['id'];
                          return commentUserId == userId;
                        }
                      }
                      return false;
                    } catch (e) {
                      return false;
                    }
                  }).length;
                }
                
                // حساب المرفقات
                if (reportData.containsKey('attachments') && reportData['attachments'] is List) {
                  final attachments = reportData['attachments'] as List;
                  attachmentsCount = attachments.where((attachment) {
                    try {
                      if (attachment is Map<String, dynamic> && attachment.containsKey('uploadedByUser')) {
                        final uploadedByUser = attachment['uploadedByUser'];
                        if (uploadedByUser is Map<String, dynamic>) {
                          final uploadedByUserId = uploadedByUser['id'];
                          return uploadedByUserId == userId;
                        }
                      }
                      return false;
                    } catch (e) {
                      return false;
                    }
                  }).length;
                }
                
                // حساب التحويلات
                if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
                  final taskHistories = reportData['taskHistories'] as List;
                  transfersCount = taskHistories.where((history) {
                    try {
                      if (history is Map<String, dynamic>) {
                        final changedBy = history['changedByNavigation'] ?? history['user'];
                        if (changedBy is Map<String, dynamic>) {
                          final changedById = changedBy['id'];
                          return changedById == userId;
                        }
                      }
                      return false;
                    } catch (e) {
                      return false;
                    }
                  }).length;
                }
              } catch (e) {
                if (kDebugMode) {
                  print('⚠️ خطأ في حساب المساهمات: $e');
                }
                commentsCount = 0;
                attachmentsCount = 0;
                transfersCount = 0;
              }
              
              final totalContributions = commentsCount + attachmentsCount + transfersCount;
              
              // إضافة المساهم فقط إذا كان لديه مساهمات حقيقية
              if (totalContributions > 0) {
                final contributor = TaskContributor(
                  userId: userId,
                  userName: userName,
                  userEmail: userEmail,
                  role: 'مساهم',
                  commentsCount: commentsCount,
                  attachmentsCount: attachmentsCount,
                  activitiesCount: transfersCount,
                  totalContributions: totalContributions, // استخدام القيمة الحقيقية فقط
                  level: ContributorLevel.basic,
                );
                
                contributors.add(contributor);
                
                if (kDebugMode) {
                  print('   - تمت إضافة مساهم: $userName ($totalContributions مساهمة)');
                }
              } else {
                if (kDebugMode) {
                  print('   - تم تجاهل مساهم بدون مساهمات: $userName');
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في إنشاء مساهم: $e');
                print('⚠️ تفاصيل الخطأ: ${e.toString()}');
                print('⚠️ بيانات المستخدم: $userJson');
              }
            }
          }
        }
        
        if (kDebugMode) {
          print('✅ تم استخراج ${contributors.length} مساهم من accessUsers');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ لم يتم العثور على accessUsers في البيانات المستلمة');
        }
      }
      
          // لا نضيف المنشئ والمكلف - نستخدم البيانات الحقيقية فقط
      if (kDebugMode) {
        print('ℹ️ عدم إضافة المنشئ والمكلف يدوياً - استخدام البيانات الحقيقية فقط');
        print('ℹ️ عدد المساهمين الحالي: ${contributors.length}');
      }
      
      // ترتيب المساهمين حسب عدد المساهمات (الأعلى أولاً)
      try {
        contributors.sort((a, b) => b.totalContributions.compareTo(a.totalContributions));
        
        // تعيين النسبة المئوية لكل مساهم إذا لم تكن موجودة
        for (var contributor in contributors) {
          try {
            if (contributor.percentage <= 0) {
              contributor.setPercentage(5.0); // تعيين نسبة افتراضية
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في تعيين النسبة المئوية للمساهم: $e');
            }
            contributor.setPercentage(5.0); // تعيين نسبة افتراضية في حالة الخطأ
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في ترتيب المساهمين: $e');
        }
      }
      
      // استخراج التحويلات من taskHistories - تحسين معالجة البيانات
      List<TaskTransfer> transfers = [];
      if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
        final histories = reportData['taskHistories'] as List;
        
        try {
          if (kDebugMode) {
            print('🔍 استخراج التحويلات من taskHistories: ${histories.length} سجل');
            // طباعة نموذج من البيانات للتشخيص
            if (histories.isNotEmpty && histories.first is Map<String, dynamic>) {
              print('   - نموذج من بيانات التاريخ: ${histories.first}');
            }
          }
          
          // معالجة محسنة للتحويلات - استخراج جميع التحويلات بدون تصفية
          for (var historyJson in histories) {
          if (historyJson is Map<String, dynamic>) {
            try {
              // التحقق مما إذا كان الإجراء متعلق بالتحويل
              final action = (historyJson['action'] ?? '').toString().toLowerCase();
              
              // تحسين شرط التحويل ليشمل المزيد من الإجراءات
              if (action.contains('assign') || 
                  action.contains('transfer') || 
                  action.contains('تحويل') || 
                  action.contains('تكليف') || 
                  action.contains('إسناد') || 
                  action == 'created' || 
                  action == 'transferred') {
                
                // إنشاء تحويل مباشرة من البيانات
                if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                  final fromUserId = historyJson['oldValue']?.toString() ?? '';
                  final toUserId = historyJson['newValue']?.toString() ?? '';
                  
                  if (fromUserId.isNotEmpty && toUserId.isNotEmpty && fromUserId != toUserId) {
                    // تحويل IDs إلى أسماء من accessUsers
                    String fromUserName = fromUserId;
                    String toUserName = toUserId;
                    
                    // البحث عن أسماء المستخدمين من accessUsers
                    if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
                      final accessUsers = reportData['accessUsers'] as List;
                      for (var user in accessUsers) {
                        if (user is Map<String, dynamic>) {
                          final userId = user['id']?.toString();
                          if (userId == fromUserId) {
                            fromUserName = user['name']?.toString() ?? fromUserId;
                          }
                          if (userId == toUserId) {
                            toUserName = user['name']?.toString() ?? toUserId;
                          }
                        }
                      }
                    }
                    
                    // معالجة آمنة للتاريخ
                    int timestamp = 0;
                    try {
                      if (historyJson['timestamp'] is int) {
                        timestamp = historyJson['timestamp'];
                      } else if (historyJson['changedAt'] is int) {
                        timestamp = historyJson['changedAt'];
                      } else if (historyJson['timestamp'] != null) {
                        timestamp = int.tryParse(historyJson['timestamp'].toString()) ?? 0;
                      } else if (historyJson['changedAt'] != null) {
                        timestamp = int.tryParse(historyJson['changedAt'].toString()) ?? 0;
                      }
                    } catch (e) {
                      if (kDebugMode) {
                        print('⚠️ خطأ في معالجة التاريخ: $e');
                      }
                      timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
                    }
                    
                    // استخراج سبب التحويل من details
                    String reason = 'تحويل مهمة';
                    if (historyJson['details'] != null) {
                      final details = historyJson['details'];
                      if (details is Map<String, dynamic> && details.containsKey('note')) {
                        final note = details['note']?.toString() ?? '';
                        if (note.isNotEmpty) {
                          reason = note;
                        }
                      } else if (details is String && details.isNotEmpty) {
                        reason = details;
                      }
                    }
                    
                    // استخراج منفذ التحويل
                    String executor = 'النظام';
                    if (historyJson['changedByNavigation'] != null && historyJson['changedByNavigation'] is Map) {
                      executor = historyJson['changedByNavigation']['name']?.toString() ?? 'النظام';
                    } else if (historyJson['user'] != null && historyJson['user'] is Map) {
                      executor = historyJson['user']['name']?.toString() ?? 'النظام';
                    }
                    
                    final transfer = TaskTransfer(
                      fromUser: fromUserName,
                      toUser: toUserName,
                      timestamp: timestamp,
                      reason: reason,
                      executor: executor,
                      type: TransferType.manual,
                    );
                    
                    transfers.add(transfer);
                    
                    if (kDebugMode) {
                      print('   - تم استخراج تحويل: ${transfer.fromUser} -> ${transfer.toUser}');
                    }
                  }
                } else {
                  // استخدام TaskTransfer.fromHistory كاحتياطي
                  final transfer = TaskTransfer.fromHistory(historyJson);
                  if (transfer != null) {
                    transfers.add(transfer);
                    
                    if (kDebugMode) {
                      print('   - تم استخراج تحويل (طريقة احتياطية): ${transfer.fromUser} -> ${transfer.toUser}');
                    }
                  }
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في استخراج تحويل من سجل التاريخ: $e');
                print('⚠️ تفاصيل الخطأ: ${e.toString()}');
              }
              
              // محاولة استخراج التحويل بطريقة أبسط
              try {
                if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                  final fromUser = historyJson['oldValue']?.toString() ?? '';
                  final toUser = historyJson['newValue']?.toString() ?? '';
                  
                  if (fromUser.isNotEmpty && toUser.isNotEmpty && fromUser != toUser) {
                    final transfer = TaskTransfer(
                      fromUser: fromUser,
                      toUser: toUser,
                      timestamp: 0,
                      reason: 'تحويل مهمة',
                      executor: 'النظام',
                      type: TransferType.manual,
                    );
                    
                    transfers.add(transfer);
                    
                    if (kDebugMode) {
                      print('   - تم استخراج تحويل (طريقة مبسطة): ${transfer.fromUser} -> ${transfer.toUser}');
                    }
                  }
                }
              } catch (e2) {
                if (kDebugMode) {
                  print('⚠️ فشل في استخراج التحويل بالطريقة المبسطة: $e2');
                }
              }
            }
          }
        }
        
        if (kDebugMode) {
          print('✅ تم استخراج ${transfers.length} تحويل من taskHistories');
        }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ خطأ في استخراج التحويلات من taskHistories: $e');
          }
        }
      } else if (reportData.containsKey('transfers') && reportData['transfers'] is List) {
        // استخدام transfers إذا كانت موجودة
        final transfersList = reportData['transfers'] as List;
        if (kDebugMode) {
          print('🔍 استخراج التحويلات من transfers: ${transfersList.length} تحويل');
          // طباعة نموذج من البيانات للتشخيص
          if (transfersList.isNotEmpty) {
            print('   - نموذج من بيانات التحويل: ${transfersList.first}');
          }
        }
        
        for (var transferData in transfersList) {
          try {
            if (transferData is Map<String, dynamic>) {
              final transfer = TaskTransfer.fromGenericData(transferData);
              if (transfer != null) {
                transfers.add(transfer);
                if (kDebugMode) {
                  print('   - تم استخراج تحويل: ${transfer.fromUser} -> ${transfer.toUser}');
                }
              }
            } else if (transferData is String && transferData.startsWith('{')) {
              // محاولة تحليل JSON string
              try {
                final transferJson = jsonDecode(transferData);
                if (transferJson is Map<String, dynamic>) {
                  final transfer = TaskTransfer.fromGenericData(transferJson);
                  if (transfer != null) {
                    transfers.add(transfer);
                    if (kDebugMode) {
                      print('   - تم استخراج تحويل من JSON string: ${transfer.fromUser} -> ${transfer.toUser}');
                    }
                  }
                }
              } catch (jsonError) {
                if (kDebugMode) {
                  print('⚠️ خطأ في تحليل JSON string: $jsonError');
                }
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في استخراج تحويل من بيانات عامة: $e');
              print('⚠️ بيانات التحويل: $transferData');
            }
          }
        }
      }
      
      // لا نضيف التحويل الأولي - نستخدم البيانات الحقيقية فقط
      if (kDebugMode) {
        print('ℹ️ عدد التحويلات المستخرجة: ${transfers.length}');
        if (transfers.isNotEmpty) {
          print('ℹ️ أول تحويل: ${transfers.first.fromUser} -> ${transfers.first.toUser}');
          print('ℹ️ آخر تحويل: ${transfers.last.fromUser} -> ${transfers.last.toUser}');
        }
      }
      
      // ترتيب التحويلات حسب التاريخ (الأقدم أولاً)
      transfers.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      // حساب الإحصائيات مع معالجة محسنة للأخطاء
      TaskReportStatistics statistics;
      try {
        if (kDebugMode) {
          print('🔍 حساب إحصائيات التقرير');
        }
        
        // تعيين النسبة المئوية لكل مساهم بناءً على مساهماته الحقيقية
        if (contributors.isNotEmpty) {
          final totalContributions = contributors.fold<int>(0, (sum, c) => sum + c.totalContributions);
          
          for (var contributor in contributors) {
            try {
              if (totalContributions > 0) {
                final percentage = (contributor.totalContributions / totalContributions) * 100;
                contributor.setPercentage(percentage);
              } else {
                contributor.setPercentage(0.0);
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في تعيين النسبة المئوية للمساهم: $e');
              }
              contributor.setPercentage(0.0);
            }
          }
        }
        
        // حساب الإحصائيات
        try {
          statistics = TaskReportStatistics.calculate(task, contributors, transfers);
            
            if (kDebugMode) {
              print('✅ تم حساب إحصائيات التقرير بنجاح');
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في حساب الإحصائيات: $e');
              print('⚠️ استخدام إحصائيات فارغة');
            }
            statistics = TaskReportStatistics.empty();
          }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب إحصائيات التقرير: $e');
          print('⚠️ تفاصيل الخطأ: ${e.toString()}');
          print('ℹ️ استخدام إحصائيات فارغة بدلاً من حسابها');
        }
        
        // استخدام إحصائيات فارغة بدلاً من محاولة حسابها
        statistics = TaskReportStatistics.empty();
      }
      
      // استخراج متتبعات التقدم
      List<TaskProgressTracker> progressTrackers = [];
      try {
        if (reportData['progressTrackers'] is List) {
          progressTrackers = (reportData['progressTrackers'] as List).map((e) {
            if (e is Map<String, dynamic>) {
              // معالجة آمنة للنسبة المئوية
              int percentage = 0;
              if (e['percentage'] is int) {
                percentage = e['percentage'];
              } else if (e['percentage'] != null) {
                percentage = int.tryParse(e['percentage'].toString()) ?? 0;
              }
              
              return TaskProgressTracker(
                id: e['id'] ?? 0,
                taskId: e['taskId'] ?? 0,
                progress: percentage.toDouble(),
                updatedAt: DateTime.tryParse(e['updatedAt']?.toString() ?? '') ?? DateTime.now(),
                updatedBy: e['updatedBy'] ?? 0,
                notes: e['notes']?.toString() ?? '',
                date: DateTime.tryParse(e['date']?.toString() ?? '') ?? DateTime.now(),
              );
            }
            return null;
          }).whereType<TaskProgressTracker>().toList();
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في استخراج متتبعات التقدم: $e');
        }
      }
      
      // استخراج سجلات الوقت - استخدام البيانات الحقيقية فقط
      List<TimeTrackingEntry> timeTrackingEntries = [];
      try {
        if (reportData.containsKey('timeTrackingEntries')) {
          if (reportData['timeTrackingEntries'] is List && (reportData['timeTrackingEntries'] as List).isNotEmpty) {
            timeTrackingEntries = (reportData['timeTrackingEntries'] as List).map((e) {
              if (e is Map<String, dynamic>) {
                // معالجة آمنة لمدة الوقت بالدقائق
                int durationMinutes = 0;
                if (e['durationMinutes'] is int) {
                  durationMinutes = e['durationMinutes'];
                } else if (e['durationMinutes'] != null) {
                  durationMinutes = int.tryParse(e['durationMinutes'].toString()) ?? 0;
                }
                
                return TimeTrackingEntry(
                  id: e['id'] ?? 0,
                  taskId: e['taskId'] ?? 0,
                  userId: e['userId'] ?? 0,
                  description: e['description']?.toString() ?? '',
                  userName: e['userName']?.toString() ?? '',
                  startTime: DateTime.tryParse(e['startTime']?.toString() ?? '') ?? DateTime.now(),
                  endTime: DateTime.tryParse(e['endTime']?.toString() ?? '') ?? DateTime.now(),
                  durationMinutes: durationMinutes,
                );
              }
              return null;
            }).whereType<TimeTrackingEntry>().toList();
          } else {
            if (kDebugMode) {
              print('ℹ️ لا توجد بيانات لتتبع الوقت');
            }
          }
        } else {
          if (kDebugMode) {
            print('ℹ️ لا توجد بيانات لتتبع الوقت في البيانات المستلمة');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في استخراج سجلات الوقت: $e');
        }
      }
      
      // إنشاء النموذج النهائي - تحسين معالجة البيانات
      try {
        // تأكد من أن البيانات صحيحة قبل إنشاء النموذج
        if (kDebugMode) {
          print('📊 التحقق من البيانات قبل إنشاء النموذج:');
          print('   - المساهمون: ${contributors.length}');
          print('   - التحويلات: ${transfers.length}');
        }
        
        // التحقق من وجود بيانات كافية
        if (contributors.isEmpty) {
          if (kDebugMode) {
            print('⚠️ قائمة المساهمين فارغة، محاولة استخراج المساهمين مرة أخرى');
          }
          
          // محاولة استخراج المساهمين مرة أخرى من accessUsers
          if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
            final accessUsers = reportData['accessUsers'] as List;
            
            if (kDebugMode) {
              print('🔍 محاولة استخراج المساهمين مرة أخرى من accessUsers: ${accessUsers.length} مستخدم');
            }
            
            for (var userJson in accessUsers) {
              if (userJson is Map<String, dynamic>) {
                try {
                  final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
                  
                  final contributor = TaskContributor(
                    userId: 0, // استخدام معرف افتراضي
                    userName: userName,
                    userEmail: '',
                    role: 'مساهم',
                    commentsCount: 0,
                    attachmentsCount: 0,
                    activitiesCount: 0,
                    totalContributions: 1,
                    level: ContributorLevel.basic,
                    percentage: 5.0,
                  );
                  
                  contributors.add(contributor);
                  
                  if (kDebugMode) {
                    print('   - تمت إضافة مساهم: $userName');
                  }
                } catch (e) {
                  if (kDebugMode) {
                    print('⚠️ خطأ في إنشاء مساهم: $e');
                  }
                }
              }
            }
          }
        }
        
        // التحقق من وجود تحويلات كافية
        if (transfers.isEmpty && reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
          if (kDebugMode) {
            print('⚠️ قائمة التحويلات فارغة، محاولة استخراج التحويلات بطريقة مبسطة');
          }
          
          final histories = reportData['taskHistories'] as List;
          
          for (var historyJson in histories) {
            if (historyJson is Map<String, dynamic>) {
              try {
                if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                  final fromUser = historyJson['oldValue']?.toString() ?? '';
                  final toUser = historyJson['newValue']?.toString() ?? '';
                  
                  if (fromUser.isNotEmpty && toUser.isNotEmpty) {
                    final transfer = TaskTransfer(
                      fromUser: fromUser,
                      toUser: toUser,
                      timestamp: 0,
                      reason: 'تحويل مهمة',
                      executor: 'النظام',
                      type: TransferType.manual,
                    );
                    
                    transfers.add(transfer);
                    
                    if (kDebugMode) {
                      print('   - تم استخراج تحويل (طريقة مبسطة): ${transfer.fromUser} -> ${transfer.toUser}');
                    }
                  }
                }
              } catch (e) {
                // تجاهل الأخطاء
              }
            }
          }
        }
        
        // إنشاء النموذج النهائي
        final reportModel = TaskReportModel(
          task: task,
          contributors: contributors,
          transfers: transfers,
          statistics: statistics,
          progressTrackers: progressTrackers,
          timeTrackingEntries: timeTrackingEntries,
        );
        
        if (kDebugMode) {
          print('📊 تفاصيل النموذج المُنشأ:');
          print('   - المساهمون: ${reportModel.contributors.length}');
          print('   - التحويلات: ${reportModel.transfers.length}');
          print('   - أول مساهم: ${reportModel.contributors.isNotEmpty ? reportModel.contributors.first.userName : "لا يوجد"}');
          print('   - أول تحويل: ${reportModel.transfers.isNotEmpty ? "${reportModel.transfers.first.fromUser} -> ${reportModel.transfers.first.toUser}" : "لا يوجد"}');
        }
        
        return reportModel;
      } catch (e) {
        if (kDebugMode) {
          print('❌ خطأ في إنشاء نموذج التقرير النهائي: $e');
          print('❌ تفاصيل الخطأ: ${e.toString()}');
        }
        
        // إنشاء نموذج بسيط في حالة الخطأ - استخدام البيانات الحقيقية أولاً
        if (kDebugMode) {
          print('ℹ️ إنشاء نموذج بسيط باستخدام البيانات الأساسية');
          print('ℹ️ المساهمون: ${contributors.length}');
          print('ℹ️ التحويلات: ${transfers.length}');
          print('ℹ️ محاولة الحفاظ على البيانات الحقيقية المستخرجة');
        }
        
        // محاولة أخيرة لاستخراج البيانات - الحفاظ على البيانات الحقيقية
        List<TaskContributor> finalContributors = List.from(contributors);
        List<TaskTransfer> finalTransfers = List.from(transfers);
        
        // محاولة استخراج البيانات مرة أخرى إذا كانت القوائم فارغة
        if (finalContributors.isEmpty && reportData.containsKey('accessUsers')) {
          if (kDebugMode) {
            print('🔍 استخراج المساهمين من task_access_users للمهمة ${reportData['id']}');
            print('   - هل يوجد accessUsers في البيانات؟ ${reportData.containsKey('accessUsers')}');
          }
          
          try {
            if (reportData['accessUsers'] is List) {
              final accessUsers = reportData['accessUsers'] as List;
              for (var userJson in accessUsers) {
                if (userJson is Map<String, dynamic>) {
                  final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
                  
                  // لا نضيف مساهمين بدون مساهمات حقيقية
                  if (kDebugMode) {
                    print('   - تم تجاهل مساهم بدون مساهمات حقيقية: $userName');
                  }
                }
              }
              
              if (kDebugMode) {
                print('✅ تم استخراج ${finalContributors.length} مساهم من البيانات الحقيقية');
              }
            } else {
              if (kDebugMode) {
                print('ℹ️ لم يتم العثور على accessUsers، إرجاع قائمة فارغة');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في استخراج المساهمين: $e');
            }
          }
        }
        
        // لا نضيف أي بيانات افتراضية - نستخدم البيانات الحقيقية فقط
        if (finalContributors.isEmpty) {
          if (kDebugMode) {
            print('ℹ️ لا توجد مساهمين في البيانات الحقيقية - لن نضيف بيانات افتراضية');
          }
        }
        
        // محاولة استخراج التحويلات مرة أخرى إذا كانت القائمة فارغة
        if (finalTransfers.isEmpty && reportData.containsKey('taskHistories')) {
          if (kDebugMode) {
            print('🔍 محاولة استخراج التحويلات مرة أخرى من taskHistories');
          }
          
          try {
            if (reportData['taskHistories'] is List) {
              final histories = reportData['taskHistories'] as List;
              for (var historyJson in histories) {
                if (historyJson is Map<String, dynamic>) {
                  if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                    final fromUser = historyJson['oldValue']?.toString() ?? '';
                    final toUser = historyJson['newValue']?.toString() ?? '';
                    
                    if (fromUser.isNotEmpty && toUser.isNotEmpty && fromUser != toUser) {
                      // تحويل IDs إلى أسماء من accessUsers
                      String fromUserName = fromUser;
                      String toUserName = toUser;
                      
                      if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
                        final accessUsers = reportData['accessUsers'] as List;
                        for (var user in accessUsers) {
                          if (user is Map<String, dynamic>) {
                            final userId = user['id']?.toString();
                            if (userId == fromUser) {
                              fromUserName = user['name']?.toString() ?? fromUser;
                            }
                            if (userId == toUser) {
                              toUserName = user['name']?.toString() ?? toUser;
                            }
                          }
                        }
                      }
                      
                      final transfer = TaskTransfer(
                        fromUser: fromUserName,
                        toUser: toUserName,
                        timestamp: 0,
                        reason: 'تحويل مهمة',
                        executor: 'النظام',
                        type: TransferType.manual,
                      );
                      
                      finalTransfers.add(transfer);
                      
                      if (kDebugMode) {
                        print('   - تم استخراج تحويل: ${transfer.fromUser} -> ${transfer.toUser}');
                      }
                    }
                  }
                }
              }
              
              if (kDebugMode) {
                print('✅ تم استخراج ${finalTransfers.length} تحويل من البيانات الحقيقية');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في استخراج التحويلات: $e');
            }
          }
        }
        
        // لا نضيف أي تحويلات افتراضية - نستخدم البيانات الحقيقية فقط
        if (finalTransfers.isEmpty) {
          if (kDebugMode) {
            print('ℹ️ لا توجد تحويلات في البيانات الحقيقية - لن نضيف بيانات افتراضية');
          }
        }
        
        // حساب الإحصائيات الحقيقية
        final statistics = TaskReportStatistics(
          totalContributors: finalContributors.length,
          totalTransfers: finalTransfers.length,
          totalComments: task.comments.length,
          totalAttachments: task.attachments.length,
          totalSubtasks: task.subtasks.length,
          // completedSubtasks: task.taskType.((s) => s. == 'completed').length,
          completedSubtasks: 0,
          completionRate: task.completionPercentage.toDouble(),
          daysActive: _calculateDaysActive(task.createdAt, task.completedAt),
          mostActiveContributor: finalContributors.isNotEmpty 
              ? finalContributors.reduce((a, b) => a.totalContributions > b.totalContributions ? a : b).userName
              : 'لا يوجد',
          latestTransfer: finalTransfers.isNotEmpty 
              ? '${finalTransfers.last.fromUser} -> ${finalTransfers.last.toUser}'
              : 'لا يوجد',
        );

        return TaskReportModel(
          task: task,
          contributors: finalContributors,
          transfers: finalTransfers,
          statistics: statistics,
          progressTrackers: [],
          timeTrackingEntries: [],
        );
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء نموذج التقرير: $e');
        print('❌ تفاصيل الخطأ: ${e.toString()}');
      }
      
      // إنشاء نموذج بسيط في حالة الخطأ - استخدام البيانات الأساسية فقط
      if (kDebugMode) {
        print('ℹ️ إنشاء نموذج بسيط باستخدام البيانات الأساسية فقط');
      }
      
      // معالجة آمنة للقيم
      final id = reportData['id'] is int ? reportData['id'] as int : (int.tryParse(reportData['id']?.toString() ?? '') ?? 0);
      final creatorId = reportData['creatorId'] is int ? reportData['creatorId'] as int : (int.tryParse(reportData['creatorId']?.toString() ?? '') ?? 1);
      final assigneeId = reportData['assigneeId'] is int ? reportData['assigneeId'] as int : int.tryParse(reportData['assigneeId']?.toString() ?? '');
      final createdAt = reportData['createdAt'] is int ? reportData['createdAt'] as int : (int.tryParse(reportData['createdAt']?.toString() ?? '') ?? (DateTime.now().millisecondsSinceEpoch ~/ 1000));
      final completionPercentage = reportData['completionPercentage'] is int ? reportData['completionPercentage'] as int : (int.tryParse(reportData['completionPercentage']?.toString() ?? '') ?? 0);
      
      // تحقق من وجود المساهمين والتحويلات في البيانات الأصلية
      if (kDebugMode) {
        print('ℹ️ التحقق من البيانات الأصلية:');
        print('   - المساهمون (accessUsers): ${reportData['accessUsers'] is List ? (reportData['accessUsers'] as List).length : 0}');
        print('   - التحويلات (taskHistories): ${reportData['taskHistories'] is List ? (reportData['taskHistories'] as List).length : 0}');
      }
      
      // محاولة استخراج المساهمين والتحويلات من البيانات الأصلية
      List<TaskContributor> contributors = [];
      List<TaskTransfer> transfers = [];
      
      // استخراج المساهمين
      if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
        final accessUsers = reportData['accessUsers'] as List;
        
        if (kDebugMode) {
          print('🔍 محاولة استخراج المساهمين من accessUsers: ${accessUsers.length} مستخدم');
        }
        
        for (var userJson in accessUsers) {
          if (userJson is Map<String, dynamic>) {
            try {
              final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
              
              // لا نضيف مساهمين بدون مساهمات حقيقية
              if (kDebugMode) {
                print('   - تم تجاهل مساهم بدون مساهمات حقيقية: $userName');
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في إنشاء مساهم: $e');
              }
            }
          }
        }
      }
      
      // استخراج التحويلات
      if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
        final histories = reportData['taskHistories'] as List;
        
        if (kDebugMode) {
          print('🔍 محاولة استخراج التحويلات من taskHistories: ${histories.length} سجل');
        }
        
        for (var historyJson in histories) {
          if (historyJson is Map<String, dynamic>) {
            try {
              if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                final fromUser = historyJson['oldValue']?.toString() ?? '';
                final toUser = historyJson['newValue']?.toString() ?? '';
                
                if (fromUser.isNotEmpty && toUser.isNotEmpty) {
                  final transfer = TaskTransfer(
                    fromUser: fromUser,
                    toUser: toUser,
                    timestamp: 0,
                    reason: 'تحويل مهمة',
                    executor: 'النظام',
                    type: TransferType.manual,
                  );
                  
                  transfers.add(transfer);
                  
                  if (kDebugMode) {
                    print('   - تم استخراج تحويل: ${transfer.fromUser} -> ${transfer.toUser}');
                  }
                }
              }
            } catch (e) {
              // تجاهل الأخطاء
            }
          }
        }
      }
      
      // لا نضيف أي بيانات افتراضية - نستخدم البيانات الحقيقية فقط
      if (contributors.isEmpty) {
        if (kDebugMode) {
          print('ℹ️ لا توجد مساهمين في البيانات الحقيقية - لن نضيف بيانات افتراضية');
        }
      }
      
      // لا نضيف أي تحويلات افتراضية - نستخدم البيانات الحقيقية فقط
      if (transfers.isEmpty) {
        if (kDebugMode) {
          print('ℹ️ لا توجد تحويلات في البيانات الحقيقية - لن نضيف بيانات افتراضية');
        }
      }
      
      // إنشاء نموذج بسيط مع البيانات المستخرجة
      return TaskReportModel(
        task: Task(
          id: id,
          title: reportData['title']?.toString() ?? 'مهمة غير محددة',
          description: reportData['description']?.toString(),
          creatorId: creatorId,
          assigneeId: assigneeId,
          createdAt: createdAt,
          status: reportData['status']?.toString() ?? 'نشطة',
          priority: reportData['priority']?.toString() ?? 'متوسطة',
          completionPercentage: completionPercentage,
        ),
        contributors: contributors,
        transfers: transfers,
        statistics: TaskReportStatistics(
          totalContributors: contributors.length,
          totalTransfers: transfers.length,
          totalComments: 0, // لا توجد تعليقات في النموذج البسيط
          totalAttachments: 0, // لا توجد مرفقات في النموذج البسيط
          totalSubtasks: 0,
          completedSubtasks: 0,
          completionRate: completionPercentage.toDouble(),
          daysActive: _calculateDaysActive(createdAt, null),
          mostActiveContributor: contributors.isNotEmpty ? contributors.first.userName : 'لا يوجد',
          latestTransfer: transfers.isNotEmpty ? '${transfers.last.fromUser} -> ${transfers.last.toUser}' : 'لا يوجد',
        ),
        progressTrackers: [],
        timeTrackingEntries: [],
      );
    }
  }
  
  /// إنشاء نموذج تقرير المهمة بطريقة آمنة مع معالجة محسنة للأخطاء
  /// هذه الدالة تحاول استخدام البيانات المُجلبة حتى لو حدث خطأ في الدالة الأساسية
  TaskReportModel createEnhancedTaskReportModelSafe(Map<String, dynamic> reportData, Task task) {
    try {
      if (kDebugMode) {
        print('🔄 إنشاء نموذج تقرير المهمة بالطريقة الآمنة');
        print('   - المساهمون (accessUsers): ${reportData['accessUsers']?.length ?? 0}');
        print('   - التحويلات (taskHistories): ${reportData['taskHistories']?.length ?? 0}');
      }
      
      // استخراج المساهمين من accessUsers بطريقة آمنة
      List<TaskContributor> contributors = [];
      
      if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
        final accessUsers = reportData['accessUsers'] as List;
        
        if (kDebugMode) {
          print('🔍 استخراج المساهمين من accessUsers بالطريقة الآمنة: ${accessUsers.length} مستخدم');
        }
        
        for (var userJson in accessUsers) {
          if (userJson is Map<String, dynamic>) {
            try {
              final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
              final userEmail = userJson['email']?.toString() ?? '';
              
              // حساب المساهمات بطريقة آمنة
              int totalContributions = 0; // البدء بصفر
              int commentsCount = 0;
              int attachmentsCount = 0;
              int transfersCount = 0;
              
              // محاولة حساب المساهمات الحقيقية
              try {
                
                // حساب التعليقات
                if (reportData.containsKey('comments') && reportData['comments'] is List) {
                  final comments = reportData['comments'] as List;
                  commentsCount = comments.where((comment) {
                    if (comment is Map<String, dynamic> && comment.containsKey('user')) {
                      final commentUser = comment['user'];
                      if (commentUser is Map<String, dynamic>) {
                        final commentUserName = commentUser['name']?.toString();
                        return commentUserName == userName;
                      }
                    }
                    return false;
                  }).length;
                }
                
                // حساب المرفقات
                if (reportData.containsKey('attachments') && reportData['attachments'] is List) {
                  final attachments = reportData['attachments'] as List;
                  attachmentsCount = attachments.where((attachment) {
                    if (attachment is Map<String, dynamic> && attachment.containsKey('uploadedByUser')) {
                      final uploadedByUser = attachment['uploadedByUser'];
                      if (uploadedByUser is Map<String, dynamic>) {
                        final uploaderName = uploadedByUser['name']?.toString();
                        return uploaderName == userName;
                      }
                    }
                    return false;
                  }).length;
                }
                
                // حساب التحويلات
                if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
                  final taskHistories = reportData['taskHistories'] as List;
                  transfersCount = taskHistories.where((history) {
                    if (history is Map<String, dynamic>) {
                      final changedBy = history['changedByNavigation'] ?? history['user'];
                      if (changedBy is Map<String, dynamic>) {
                        final changedByName = changedBy['name']?.toString();
                        return changedByName == userName;
                      }
                    }
                    return false;
                  }).length;
                }
                
                totalContributions = commentsCount + attachmentsCount + transfersCount;
                // لا نضيف قيمة افتراضية - نستخدم الصفر إذا لم توجد مساهمات
                
              } catch (e) {
                if (kDebugMode) {
                  print('⚠️ خطأ في حساب المساهمات لـ $userName: $e');
                }
                totalContributions = 0; // لا نستخدم قيم افتراضية
              }
              
              // تحديد الدور وإضافة نقاط إضافية للأدوار المهمة
              final userId = userJson['id'] is int ? userJson['id'] : int.tryParse(userJson['id']?.toString() ?? '') ?? 0;
              String role = 'مساهم';
              int roleBonus = 0;
              
              if (userId == task.creatorId) {
                role = 'المنشئ';
                roleBonus = 2;
              } else if (userId == task.assigneeId) {
                role = 'المكلف';
                roleBonus = 1;
              }
              
              final finalContributions = totalContributions + roleBonus;
              
              // إضافة جميع المساهمين مع إعطاء قيمة افتراضية 1 للجميع
              final contributor = TaskContributor(
                userId: userId,
                userName: userName,
                userEmail: userEmail,
                role: role,
                commentsCount: commentsCount,
                attachmentsCount: attachmentsCount,
                activitiesCount: transfersCount,
                totalContributions: finalContributions > 0 ? finalContributions : 1, // قيمة افتراضية 1
                level: ContributorLevel.fromContributions(finalContributions > 0 ? finalContributions : 1),
                percentage: 0.0, // سيتم حسابها لاحقاً
              );
              
              contributors.add(contributor);
              
              if (kDebugMode) {
                if (finalContributions > 0) {
                  print('   - تمت إضافة مساهم: $userName ($finalContributions مساهمة)');
                } else {
                  print('   - تمت إضافة مساهم: $userName (1 مساهمة افتراضية)');
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في إنشاء مساهم: $e');
              }
            }
          }
        }
      }
      
      // استخراج التحويلات من taskHistories بطريقة آمنة
      List<TaskTransfer> transfers = [];
      
      if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
        final histories = reportData['taskHistories'] as List;
        
        if (kDebugMode) {
          print('🔍 استخراج التحويلات من taskHistories بالطريقة الآمنة: ${histories.length} سجل');
        }
        
        for (var historyJson in histories) {
          if (historyJson is Map<String, dynamic>) {
            try {
              // التحقق من وجود oldValue و newValue
              if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
                final fromUserId = historyJson['oldValue']?.toString() ?? '';
                final toUserId = historyJson['newValue']?.toString() ?? '';
                
                if (fromUserId.isNotEmpty && toUserId.isNotEmpty && fromUserId != toUserId) {
                  // تحويل IDs إلى أسماء من accessUsers
                  String fromUserName = fromUserId;
                  String toUserName = toUserId;
                  
                  // البحث عن أسماء المستخدمين من accessUsers
                  if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
                    final accessUsers = reportData['accessUsers'] as List;
                    for (var user in accessUsers) {
                      if (user is Map<String, dynamic>) {
                        final userId = user['id']?.toString();
                        if (userId == fromUserId) {
                          fromUserName = user['name']?.toString() ?? fromUserId;
                        }
                        if (userId == toUserId) {
                          toUserName = user['name']?.toString() ?? toUserId;
                        }
                      }
                    }
                  }
                  
                  // استخراج سبب التحويل
                  String reason = 'تحويل مهمة';
                  if (historyJson['details'] != null) {
                    final details = historyJson['details'];
                    if (details is Map<String, dynamic> && details.containsKey('note')) {
                      final note = details['note']?.toString() ?? '';
                      if (note.isNotEmpty) {
                        reason = note;
                      }
                    }
                  }
                  
                  // استخراج منفذ التحويل
                  String executor = 'النظام';
                  if (historyJson['changedByNavigation'] != null && historyJson['changedByNavigation'] is Map) {
                    executor = historyJson['changedByNavigation']['name']?.toString() ?? 'النظام';
                  } else if (historyJson['user'] != null && historyJson['user'] is Map) {
                    executor = historyJson['user']['name']?.toString() ?? 'النظام';
                  }
                  
                  // معالجة آمنة للتاريخ
                  int timestamp = 0;
                  try {
                    if (historyJson['timestamp'] is int) {
                      timestamp = historyJson['timestamp'];
                    } else if (historyJson['changedAt'] is int) {
                      timestamp = historyJson['changedAt'];
                    } else if (historyJson['timestamp'] != null) {
                      timestamp = int.tryParse(historyJson['timestamp'].toString()) ?? 0;
                    } else if (historyJson['changedAt'] != null) {
                      timestamp = int.tryParse(historyJson['changedAt'].toString()) ?? 0;
                    }
                  } catch (e) {
                    timestamp = 0;
                  }
                  
                  final transfer = TaskTransfer(
                    fromUser: fromUserName,
                    toUser: toUserName,
                    timestamp: timestamp,
                    reason: reason,
                    executor: executor,
                    type: TransferType.manual,
                  );
                  
                  transfers.add(transfer);
                  
                  if (kDebugMode) {
                    print('   - تم استخراج تحويل: ${transfer.fromUser} -> ${transfer.toUser}');
                  }
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ خطأ في استخراج تحويل: $e');
              }
            }
          }
        }
      }
      
      // ترتيب المساهمين حسب عدد المساهمات
      contributors.sort((a, b) => b.totalContributions.compareTo(a.totalContributions));
      
      // حساب النسب المئوية للمساهمين
      if (contributors.isNotEmpty) {
        final totalContributions = contributors.fold<int>(0, (sum, c) => sum + c.totalContributions);
        
        for (var contributor in contributors) {
          try {
            if (totalContributions > 0) {
              final percentage = (contributor.totalContributions / totalContributions) * 100;
              contributor.setPercentage(percentage);
            } else {
              contributor.setPercentage(0.0);
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ خطأ في تعيين النسبة المئوية: $e');
            }
            contributor.setPercentage(0.0);
          }
        }
      }
      
      // ترتيب التحويلات حسب التاريخ
      transfers.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      if (kDebugMode) {
        print('✅ تم إنشاء النموذج بالطريقة الآمنة:');
        print('   - المساهمون: ${contributors.length}');
        print('   - التحويلات: ${transfers.length}');
      }
      
      // إنشاء النموذج النهائي
      // حساب الإحصائيات الحقيقية
      final statistics = TaskReportStatistics(
        totalContributors: contributors.length,
        totalTransfers: transfers.length,
        totalComments: task.comments.length,
        totalAttachments: task.attachments.length,
        totalSubtasks: task.subtasks.length,
        completedSubtasks: task.subtasks.where((s) => s.isCompleted == true).length,
        completionRate: task.completionPercentage.toDouble(),
        daysActive: _calculateDaysActive(task.createdAt, task.completedAt),
        mostActiveContributor: contributors.isNotEmpty 
            ? contributors.reduce((a, b) => a.totalContributions > b.totalContributions ? a : b).userName
            : 'لا يوجد',
        latestTransfer: transfers.isNotEmpty 
            ? '${transfers.last.fromUser} -> ${transfers.last.toUser}'
            : 'لا يوجد',
      );

      return TaskReportModel(
        task: task,
        contributors: contributors,
        transfers: transfers,
        statistics: statistics,
        progressTrackers: [],
        timeTrackingEntries: [],
      );
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الطريقة الآمنة: $e');
      }
      
      // إرجاع نموذج أساسي في حالة الخطأ
      return TaskReportModel.fromTask(task);
    }
  }

  /// حساب عدد الأيام النشطة للمهمة
  int _calculateDaysActive(int createdAt, int? completedAt) {
    try {
      final startDate = DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);
      final endDate = completedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(completedAt * 1000)
          : DateTime.now();
      
      final difference = endDate.difference(startDate).inDays;
      return difference > 0 ? difference : 1; // على الأقل يوم واحد
    } catch (e) {
      return 1; // قيمة افتراضية في حالة الخطأ
    }
  }
}