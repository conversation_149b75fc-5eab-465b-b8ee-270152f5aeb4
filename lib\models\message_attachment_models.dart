import 'user_model.dart';
import 'message_models.dart';

/// نموذج مرفق الرسالة - متطابق مع ASP.NET Core API
class MessageAttachment {
  final int id;
  final int messageId;
  final String fileName;
  final String filePath;
  final int fileSize; // long في API
  final String fileType;
  final int uploadedAt; // long في API
  final bool isDeleted;
  final int? uploadedBy;

  // Navigation properties
  final Message? message;
  final User? uploadedByNavigation;

  const MessageAttachment({
    required this.id,
    required this.messageId,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.fileType,
    required this.uploadedAt,
    this.isDeleted = false,
    this.uploadedBy,
    this.message,
    this.uploadedByNavigation,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] as int,
      messageId: json['messageId'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: json['fileSize'] as int,
      fileType: json['fileType'] as String,
      uploadedAt: json['uploadedAt'] as int,
      isDeleted: json['isDeleted'] as bool? ?? false,
      uploadedBy: json['uploadedBy'] as int?,
      message: json['message'] != null
          ? Message.fromJson(json['message'] as Map<String, dynamic>)
          : null,
      uploadedByNavigation: json['uploadedByNavigation'] != null
          ? User.fromJson(json['uploadedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'fileType': fileType,
      'uploadedAt': uploadedAt,
      'isDeleted': isDeleted,
      'uploadedBy': uploadedBy,
    };
  }

  MessageAttachment copyWith({
    int? id,
    int? messageId,
    String? fileName,
    String? filePath,
    int? fileSize,
    String? fileType,
    int? uploadedAt,
    bool? isDeleted,
    int? uploadedBy,
    Message? message,
    User? uploadedByNavigation,
  }) {
    return MessageAttachment(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      fileType: fileType ?? this.fileType,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      message: message ?? this.message,
      uploadedByNavigation: uploadedByNavigation ?? this.uploadedByNavigation,
    );
  }

  /// الحصول على تاريخ الرفع كـ DateTime
  DateTime get uploadedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(uploadedAt * 1000);

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// التحقق من نوع الملف
  bool get isImage => fileType.startsWith('image/');
  bool get isVideo => fileType.startsWith('video/');
  bool get isAudio => fileType.startsWith('audio/');
  bool get isDocument => fileType.contains('pdf') || 
                        fileType.contains('doc') || 
                        fileType.contains('txt') ||
                        fileType.contains('excel') ||
                        fileType.contains('powerpoint');

  /// الحصول على أيقونة الملف حسب النوع
  String get fileIcon {
    if (isImage) return '🖼️';
    if (isVideo) return '🎥';
    if (isAudio) return '🎵';
    if (isDocument) return '📄';
    return '📎';
  }

  @override
  String toString() {
    return 'MessageAttachment(id: $id, fileName: $fileName, size: $fileSizeFormatted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageAttachment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب رفع مرفق رسالة
class UploadMessageAttachmentRequest {
  final int messageId;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String base64Data; // البيانات مُرمزة بـ base64

  const UploadMessageAttachmentRequest({
    required this.messageId,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.base64Data,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'fileName': fileName,
      'fileType': fileType,
      'fileSize': fileSize,
      'base64Data': base64Data,
    };
  }
}

/// نموذج استجابة رفع مرفق
class UploadMessageAttachmentResponse {
  final bool success;
  final String? message;
  final MessageAttachment? attachment;
  final String? error;

  const UploadMessageAttachmentResponse({
    required this.success,
    this.message,
    this.attachment,
    this.error,
  });

  factory UploadMessageAttachmentResponse.fromJson(Map<String, dynamic> json) {
    return UploadMessageAttachmentResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      attachment: json['attachment'] != null
          ? MessageAttachment.fromJson(json['attachment'] as Map<String, dynamic>)
          : null,
      error: json['error'] as String?,
    );
  }
}

/// إحصائيات مرفقات الرسائل
class MessageAttachmentStatistics {
  final int totalAttachments;
  final int totalSize;
  final int imageCount;
  final int videoCount;
  final int audioCount;
  final int documentCount;
  final int otherCount;

  const MessageAttachmentStatistics({
    required this.totalAttachments,
    required this.totalSize,
    required this.imageCount,
    required this.videoCount,
    required this.audioCount,
    required this.documentCount,
    required this.otherCount,
  });

  factory MessageAttachmentStatistics.fromJson(Map<String, dynamic> json) {
    return MessageAttachmentStatistics(
      totalAttachments: json['totalAttachments'] as int,
      totalSize: json['totalSize'] as int,
      imageCount: json['imageCount'] as int,
      videoCount: json['videoCount'] as int,
      audioCount: json['audioCount'] as int,
      documentCount: json['documentCount'] as int,
      otherCount: json['otherCount'] as int,
    );
  }

  /// الحصول على الحجم الإجمالي بصيغة قابلة للقراءة
  String get totalSizeFormatted {
    if (totalSize < 1024) {
      return '$totalSize B';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  @override
  String toString() {
    return 'MessageAttachmentStatistics(total: $totalAttachments, size: $totalSizeFormatted)';
  }
}
