import 'package:get/get.dart';
import '../services/unified_search_service.dart';
import '../models/search_models.dart';

/// متحكم البحث الموحد
class UnifiedSearchController extends GetxController {
  final UnifiedSearchService _searchService = Get.find<UnifiedSearchService>();

  // حالة البحث
  final RxBool isSearching = false.obs;
  final RxString searchQuery = ''.obs;
  final RxList<SearchResult> searchResults = <SearchResult>[].obs;
  final RxList<String> searchHistory = <String>[].obs;

  // إعدادات البحث
  final RxList<SearchResultType> selectedTypes = <SearchResultType>[].obs;
  final RxInt resultsPerType = 10.obs;
  final RxBool includeDeleted = false.obs;

  @override
  void onInit() {
    super.onInit();
    // تحميل سجل البحث
    loadSearchHistory();
    // تحديد جميع الأنواع افتراضياً
    selectAllTypes();
  }

  /// تنفيذ البحث
  Future<void> search(String query) async {
    if (query.trim().isEmpty) {
      searchQuery.value = '';
      searchResults.clear();
      return;
    }

    searchQuery.value = query.trim();
    isSearching.value = true;

    try {
      final results = await _searchService.search(
        query: searchQuery.value,
        types: selectedTypes,
        limit: resultsPerType.value,
        includeDeleted: includeDeleted.value,
      );

      searchResults.assignAll(results);
      
      // إضافة إلى سجل البحث
      addToSearchHistory(searchQuery.value);
    } catch (e) {
      Get.snackbar('خطأ', 'حدث خطأ أثناء البحث: $e');
    } finally {
      isSearching.value = false;
    }
  }

  /// تحديد جميع أنواع البحث
  void selectAllTypes() {
    selectedTypes.assignAll(SearchResultType.values);
  }

  /// إلغاء تحديد جميع أنواع البحث
  void clearAllTypes() {
    selectedTypes.clear();
  }

  /// تبديل نوع بحث
  void toggleSearchType(SearchResultType type) {
    if (selectedTypes.contains(type)) {
      selectedTypes.remove(type);
    } else {
      selectedTypes.add(type);
    }
  }

  /// إضافة إلى سجل البحث
  void addToSearchHistory(String query) {
    if (query.isEmpty || searchHistory.contains(query)) return;
    
    searchHistory.insert(0, query);
    
    // الاحتفاظ بآخر 10 عمليات بحث فقط
    if (searchHistory.length > 10) {
      searchHistory.removeRange(10, searchHistory.length);
    }
    
    saveSearchHistory();
  }

  /// حفظ سجل البحث
  void saveSearchHistory() {
    // يمكن حفظ السجل في التخزين المحلي هنا
    // GetStorage().write('search_history', searchHistory);
  }

  /// تحميل سجل البحث
  void loadSearchHistory() {
    // يمكن تحميل السجل من التخزين المحلي هنا
    // final history = GetStorage().read<List>('search_history');
    // if (history != null) {
    //   searchHistory.assignAll(history.cast<String>());
    // }
  }

  /// مسح سجل البحث
  void clearSearchHistory() {
    searchHistory.clear();
    saveSearchHistory();
  }

  /// مسح البحث الحالي
  void clearSearch() {
    searchQuery.value = '';
    searchResults.clear();
  }

  /// تصفية النتائج حسب النوع
  List<SearchResult> getResultsByType(SearchResultType type) {
    return searchResults.where((result) => result.type == type).toList();
  }

  /// الحصول على عدد النتائج لكل نوع
  Map<SearchResultType, int> getResultsCountByType() {
    final counts = <SearchResultType, int>{};
    for (final type in SearchResultType.values) {
      counts[type] = getResultsByType(type).length;
    }
    return counts;
  }
}
