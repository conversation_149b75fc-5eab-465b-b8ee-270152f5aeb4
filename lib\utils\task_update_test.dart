import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/task_controller.dart';

/// فئة اختبار تحديث المهام
class TaskUpdateTest {
  static final TaskController _taskController = Get.find<TaskController>();

  /// اختبار تحديث التقدم
  static Future<void> testProgressUpdate(int taskId, double progress) async {
    debugPrint('🧪 اختبار تحديث التقدم...');
    debugPrint('   المهمة: $taskId');
    debugPrint('   التقدم: $progress%');
    
    final success = await _taskController.updateTaskProgress(
      taskId, 
      1, // معرف المستخدم التجريبي
      progress,
      notes: 'اختبار تحديث التقدم',
    );
    
    if (success) {
      debugPrint('✅ نجح اختبار تحديث التقدم');
      await _verifyUpdate(taskId, 'progress', progress.toString());
    } else {
      debugPrint('❌ فشل اختبار تحديث التقدم');
    }
  }

  /// اختبار إضافة تعليق
  static Future<void> testCommentAdd(int taskId, String content) async {
    debugPrint('🧪 اختبار إضافة تعليق...');
    debugPrint('   المهمة: $taskId');
    debugPrint('   المحتوى: $content');
    
    final success = await _taskController.addTaskComment(taskId, content);
    
    if (success) {
      debugPrint('✅ نجح اختبار إضافة التعليق');
      await _verifyUpdate(taskId, 'comments', _taskController.taskComments.length.toString());
    } else {
      debugPrint('❌ فشل اختبار إضافة التعليق');
    }
  }

  /// التحقق من التحديث
  static Future<void> _verifyUpdate(int taskId, String type, String value) async {
    await Future.delayed(const Duration(milliseconds: 500)); // انتظار قصير
    
    final task = _taskController.currentTask;
    if (task != null && task.id == taskId) {
      switch (type) {
        case 'progress':
          final currentProgress = task.completionPercentage.toString();
          if (currentProgress == value) {
            debugPrint('✅ تم التحقق من تحديث التقدم: $currentProgress%');
          } else {
            debugPrint('❌ فشل التحقق من تحديث التقدم: متوقع $value، الحالي $currentProgress');
          }
          break;
        case 'comments':
          final commentsCount = _taskController.taskComments.length.toString();
          debugPrint('📊 عدد التعليقات الحالي: $commentsCount');
          break;
      }
    } else {
      debugPrint('❌ لم يتم العثور على المهمة للتحقق');
    }
  }

  /// اختبار شامل
  static Future<void> runFullTest(int taskId) async {
    debugPrint('🚀 بدء الاختبار الشامل للمهمة $taskId');
    
    // اختبار تحديث التقدم
    await testProgressUpdate(taskId, 75.0);
    await Future.delayed(const Duration(seconds: 1));
    
    // اختبار إضافة تعليق
    await testCommentAdd(taskId, 'تعليق اختبار - ${DateTime.now().millisecondsSinceEpoch}');
    await Future.delayed(const Duration(seconds: 1));
    
    // التحديث الشامل
    debugPrint('🔄 تشغيل التحديث الشامل...');
    await _taskController.refreshTaskDetails(taskId);
    _taskController.updateUI(source: 'اختبار شامل');
    
    // التحقق النهائي
    await Future.delayed(const Duration(milliseconds: 500));
    final finalTask = _taskController.currentTask;
    if (finalTask != null) {
      debugPrint('📊 الحالة النهائية:');
      debugPrint('   - التقدم: ${finalTask.completionPercentage}%');
      debugPrint('   - التعليقات: ${finalTask.comments.length}');
      debugPrint('   - المرفقات: ${finalTask.attachments.length}');
      debugPrint('   - المساهمون: ${_taskController.taskContributors.length}');
    }
    
    debugPrint('✅ انتهى الاختبار الشامل');
  }
}