import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// عنصر حالة فارغة
/// يعرض رسالة وأيقونة عندما لا توجد بيانات للعرض
class EmptyStateWidget extends StatelessWidget {
  /// العنوان الرئيسي (اختياري)
  final String? title;

  /// الرسالة المعروضة
  final String message;

  /// الوصف الإضافي (اختياري)
  final String? description;

  /// الأيقونة المعروضة
  final IconData icon;

  /// حجم الأيقونة
  final double iconSize;

  /// لون الأيقونة
  final Color? iconColor;

  /// نص الزر (اختياري)
  final String? buttonText;

  /// دالة يتم استدعاؤها عند الضغط على الزر
  final VoidCallback? onButtonPressed;

  /// نص زر الإجراء (اختياري)
  final String? actionText;

  /// دالة يتم استدعاؤها عند الضغط على زر الإجراء
  final VoidCallback? onActionPressed;
  
  /// إنشاء عنصر حالة فارغة
  const EmptyStateWidget({
    super.key,
    this.title,
    required this.message,
    this.description,
    this.icon = Icons.inbox_outlined,
    this.iconSize = 64.0,
    this.iconColor,
    this.buttonText,
    this.onButtonPressed,
    this.actionText,
    this.onActionPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppColors.textSecondary,
            ),
            const SizedBox(height: 24),
            if (title != null) ...[
              Text(
                title!,
                style: AppStyles.titleLarge.copyWith(
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            Text(
              message,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description!,
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onButtonPressed,
                icon: const Icon(Icons.add),
                label: Text(buttonText!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
            if (actionText != null && onActionPressed != null) ...[
              const SizedBox(height: 16),
              TextButton(
                onPressed: onActionPressed,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
