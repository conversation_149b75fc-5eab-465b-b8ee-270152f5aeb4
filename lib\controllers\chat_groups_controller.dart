import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/chat_models.dart';
import '../services/api/chat_groups_api_service.dart';
import '../controllers/auth_controller.dart';



/// متحكم مجموعات الدردشة
class ChatGroupsController extends GetxController {
  final ChatGroupsApiService _apiService = ChatGroupsApiService();

  // قوائم المجموعات
  final RxList<ChatGroup> _allGroups = <ChatGroup>[].obs;
  final RxList<ChatGroup> _filteredGroups = <ChatGroup>[].obs;
  final RxList<ChatGroup> _myGroups = <ChatGroup>[].obs;

  // المجموعة الحالية
  final Rx<ChatGroup?> _currentGroup = Rx<ChatGroup?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;
  final RxBool _showMyGroupsOnly = false.obs;

  // Getters
  List<ChatGroup> get allGroups => _allGroups;
  List<ChatGroup> get filteredGroups => _filteredGroups;
  List<ChatGroup> get myGroups => _myGroups;
  ChatGroup? get currentGroup => _currentGroup.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;
  bool get showMyGroupsOnly => _showMyGroupsOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllGroups();
    loadMyGroups();
  }

  /// تحميل جميع مجموعات الدردشة
  Future<void> loadAllGroups() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final groups = await _apiService.getAllGroups();
      _allGroups.assignAll(groups);
      _applyFilters();
      debugPrint('تم تحميل ${groups.length} مجموعة دردشة');
    } catch (e) {
      _error.value = 'خطأ في تحميل مجموعات الدردشة: $e';
      debugPrint('خطأ في تحميل مجموعات الدردشة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مجموعاتي
  Future<void> loadMyGroups() async {
    try {
      // الحصول على المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        debugPrint('المستخدم غير مسجل الدخول - تخطي تحميل المجموعات');
        _myGroups.clear();
        return;
      }

      // استخدام getUserGroups مع معرف المستخدم الحقيقي
      final groups = await _apiService.getUserGroups(currentUser.id);
      if (groups.isNotEmpty) {
        _myGroups.assignAll(groups);
        debugPrint('تم تحميل ${groups.length} من مجموعاتي');
      } else {
        _myGroups.clear();
        debugPrint('لا توجد مجموعات للمستخدم ${currentUser.id}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مجموعاتي: $e');
      // تنظيف القائمة في حالة الخطأ
      _myGroups.clear();
    }
  }

  /// الحصول على مجموعة دردشة بالمعرف
  Future<void> getGroupById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final group = await _apiService.getGroupById(id);
      _currentGroup.value = group;
      debugPrint('تم تحميل مجموعة الدردشة: ${group?.name ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل مجموعة الدردشة: $e';
      debugPrint('خطأ في تحميل مجموعة الدردشة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مجموعة دردشة جديدة
  Future<bool> createGroup(CreateChatGroupRequest request) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // التحقق من صحة البيانات
      if (request.name.trim().isEmpty) {
        _error.value = 'اسم المجموعة مطلوب';
        return false;
      }

      if (request.name.length > 100) {
        _error.value = 'اسم المجموعة يجب أن يكون أقل من 100 حرف';
        return false;
      }

      final newGroup = await _apiService.createGroup(request);
      if (newGroup != null) {
        _allGroups.add(newGroup);
        _applyFilters();
        await loadMyGroups();
        debugPrint('تم إنشاء مجموعة دردشة جديدة: ${newGroup.name}');

        // إظهار رسالة نجاح
        Get.snackbar(
          'نجح',
          'تم إنشاء المجموعة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );

        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء مجموعة الدردشة: $e';
      debugPrint('خطأ في إنشاء مجموعة الدردشة: $e');

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء المجموعة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );

      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مجموعة دردشة
  Future<bool> updateGroup(int id, ChatGroup group) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedGroup = await _apiService.updateGroup(group);
      if (updatedGroup != null) {
        final index = _allGroups.indexWhere((g) => g.id == id);
        if (index != -1) {
          _allGroups[index] = updatedGroup;
          _applyFilters();
        }
      }
      await loadMyGroups();
      debugPrint('تم تحديث مجموعة الدردشة: ${group.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث مجموعة الدردشة: $e';
      debugPrint('خطأ في تحديث مجموعة الدردشة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مجموعة دردشة
  Future<bool> deleteGroup(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteGroup(id);
      _allGroups.removeWhere((g) => g.id == id);
      _applyFilters();
      await loadMyGroups();
      debugPrint('تم حذف مجموعة الدردشة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف مجموعة الدردشة: $e';
      debugPrint('خطأ في حذف مجموعة الدردشة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الانضمام إلى مجموعة
  Future<bool> joinGroup(int groupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        _error.value = 'يجب تسجيل الدخول أولاً';
        return false;
      }

      await _apiService.joinGroup(groupId, currentUser.id);
      await loadMyGroups();
      debugPrint('تم الانضمام إلى المجموعة');

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجح',
        'تم الانضمام للمجموعة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      return true;
    } catch (e) {
      _error.value = 'خطأ في الانضمام إلى المجموعة: $e';
      debugPrint('خطأ في الانضمام إلى المجموعة: $e');

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'فشل في الانضمام للمجموعة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );

      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// مغادرة مجموعة
  Future<bool> leaveGroup(int groupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // الحصول على المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        _error.value = 'يجب تسجيل الدخول أولاً';
        return false;
      }

      await _apiService.leaveGroup(groupId, currentUser.id);
      await loadMyGroups();
      debugPrint('تم مغادرة المجموعة');

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجح',
        'تم مغادرة المجموعة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      return true;
    } catch (e) {
      _error.value = 'خطأ في مغادرة المجموعة: $e';
      debugPrint('خطأ في مغادرة المجموعة: $e');

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'فشل في مغادرة المجموعة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );

      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allGroups.where((group) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!group.name.toLowerCase().contains(query) &&
            !(group.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط (تحقق من أن المجموعة نشطة)
      if (_showActiveOnly.value) {
        // يمكن إضافة منطق للتحقق من نشاط المجموعة
        // مثل التحقق من آخر رسالة أو عدد الأعضاء النشطين
      }

      // مرشح مجموعاتي فقط
      if (_showMyGroupsOnly.value) {
        if (!_myGroups.any((myGroup) => myGroup.id == group.id)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredGroups.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// تعيين مرشح مجموعاتي فقط
  void setMyGroupsFilter(bool showMyGroupsOnly) {
    _showMyGroupsOnly.value = showMyGroupsOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _showMyGroupsOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllGroups(),
      loadMyGroups(),
    ]);
  }
}
