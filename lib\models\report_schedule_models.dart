import 'user_model.dart';
import 'report_models.dart';

/// تكرار التقارير
enum ReportFrequency {
  daily('daily', 'يومي'),
  weekly('weekly', 'أسبوعي'),
  monthly('monthly', 'شهري'),
  quarterly('quarterly', 'ربع سنوي'),
  yearly('yearly', 'سنوي'),
  custom('custom', 'مخصص');

  const ReportFrequency(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static ReportFrequency fromValue(String value) {
    return ReportFrequency.values.firstWhere(
      (freq) => freq.value == value,
      orElse: () => ReportFrequency.daily,
    );
  }
}

/// نموذج جدولة التقارير - متطابق مع ASP.NET Core API
class ReportSchedule {
  final int id;
  final int reportId;
  final String title;
  final String frequency;
  final int? dayOfWeek; // 0-6 (الأحد = 0)
  final int? dayOfMonth; // 1-31
  final int hour; // 0-23
  final int minute; // 0-59
  final String recipients; // قائمة البريد الإلكتروني مفصولة بفواصل
  final bool isActive;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final int? lastExecutionAt;
  final int nextExecutionAt;

  // Navigation properties
  final User? createdByUser;
  final Report? report;

  const ReportSchedule({
    required this.id,
    required this.reportId,
    required this.title,
    required this.frequency,
    this.dayOfWeek,
    this.dayOfMonth,
    required this.hour,
    required this.minute,
    required this.recipients,
    this.isActive = true,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.lastExecutionAt,
    required this.nextExecutionAt,
    this.createdByUser,
    this.report,
  });

  /// إنشاء ReportSchedule من JSON (من API)
  factory ReportSchedule.fromJson(Map<String, dynamic> json) {
    return ReportSchedule(
      id: json['id'] as int,
      reportId: json['reportId'] as int,
      title: json['title'] as String,
      frequency: json['frequency'] as String,
      dayOfWeek: json['dayOfWeek'] as int?,
      dayOfMonth: json['dayOfMonth'] as int?,
      hour: json['hour'] as int,
      minute: json['minute'] as int,
      recipients: json['recipients'] as String,
      isActive: json['isActive'] as bool? ?? true,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      lastExecutionAt: json['lastExecutionAt'] as int?,
      nextExecutionAt: json['nextExecutionAt'] as int,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      report: json['report'] != null
          ? Report.fromJson(json['report'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل ReportSchedule إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reportId': reportId,
      'title': title,
      'frequency': frequency,
      'dayOfWeek': dayOfWeek,
      'dayOfMonth': dayOfMonth,
      'hour': hour,
      'minute': minute,
      'recipients': recipients,
      'isActive': isActive,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'lastExecutionAt': lastExecutionAt,
      'nextExecutionAt': nextExecutionAt,
    };
  }

  /// إنشاء نسخة معدلة من ReportSchedule
  ReportSchedule copyWith({
    int? id,
    int? reportId,
    String? title,
    String? frequency,
    int? dayOfWeek,
    int? dayOfMonth,
    int? hour,
    int? minute,
    String? recipients,
    bool? isActive,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    int? lastExecutionAt,
    int? nextExecutionAt,
    User? createdByUser,
    Report? report,
  }) {
    return ReportSchedule(
      id: id ?? this.id,
      reportId: reportId ?? this.reportId,
      title: title ?? this.title,
      frequency: frequency ?? this.frequency,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
      hour: hour ?? this.hour,
      minute: minute ?? this.minute,
      recipients: recipients ?? this.recipients,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastExecutionAt: lastExecutionAt ?? this.lastExecutionAt,
      nextExecutionAt: nextExecutionAt ?? this.nextExecutionAt,
      createdByUser: createdByUser ?? this.createdByUser,
      report: report ?? this.report,
    );
  }

  /// الحصول على تكرار التقرير كـ enum
  ReportFrequency get frequencyEnum => ReportFrequency.fromValue(frequency);

  /// الحصول على قائمة المستلمين
  List<String> get recipientsList => recipients.split(',').map((e) => e.trim()).toList();

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على تاريخ آخر تنفيذ كـ DateTime
  DateTime? get lastExecutionAtDateTime => lastExecutionAt != null
      ? DateTime.fromMillisecondsSinceEpoch(lastExecutionAt! * 1000)
      : null;

  /// الحصول على تاريخ التنفيذ التالي كـ DateTime
  DateTime get nextExecutionAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(nextExecutionAt * 1000);

  /// التحقق من كون الجدولة متأخرة
  bool get isOverdue => DateTime.now().isAfter(nextExecutionAtDateTime);

  /// الحصول على وقت التنفيذ كنص
  String get executionTime => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReportSchedule && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ReportSchedule(id: $id, title: $title, frequency: $frequency, isActive: $isActive)';
  }
}

/// نموذج طلب إنشاء جدولة تقرير
class CreateReportScheduleRequest {
  final int reportId;
  final String title;
  final String frequency;
  final int? dayOfWeek;
  final int? dayOfMonth;
  final int hour;
  final int minute;
  final List<String> recipients;
  final bool isActive;

  const CreateReportScheduleRequest({
    required this.reportId,
    required this.title,
    required this.frequency,
    this.dayOfWeek,
    this.dayOfMonth,
    required this.hour,
    required this.minute,
    required this.recipients,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'title': title,
      'frequency': frequency,
      'dayOfWeek': dayOfWeek,
      'dayOfMonth': dayOfMonth,
      'hour': hour,
      'minute': minute,
      'recipients': recipients.join(', '),
      'isActive': isActive,
    };
  }
}

/// نموذج طلب تحديث جدولة تقرير
class UpdateReportScheduleRequest {
  final String? title;
  final String? frequency;
  final int? dayOfWeek;
  final int? dayOfMonth;
  final int? hour;
  final int? minute;
  final List<String>? recipients;
  final bool? isActive;

  const UpdateReportScheduleRequest({
    this.title,
    this.frequency,
    this.dayOfWeek,
    this.dayOfMonth,
    this.hour,
    this.minute,
    this.recipients,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (title != null) data['title'] = title;
    if (frequency != null) data['frequency'] = frequency;
    if (dayOfWeek != null) data['dayOfWeek'] = dayOfWeek;
    if (dayOfMonth != null) data['dayOfMonth'] = dayOfMonth;
    if (hour != null) data['hour'] = hour;
    if (minute != null) data['minute'] = minute;
    if (recipients != null) data['recipients'] = recipients!.join(', ');
    if (isActive != null) data['isActive'] = isActive;
    return data;
  }
}
