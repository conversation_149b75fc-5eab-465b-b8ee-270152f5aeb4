import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import '../../config/api_config.dart';
import '../../models/user_model.dart';
import '../../models/role_model.dart';
import '../../models/permission_models.dart';
import '../../models/system_models.dart';
import '../../models/activity_log_models.dart';
import '../../models/system_setting_models.dart';
import '../../services/storage_service.dart';
import '../../models/screen_model.dart';

/// خدمة API موحدة ومحسنة للوحة التحكم الإدارية
/// تجمع جميع العمليات الإدارية في مكان واحد مع معالجة محسنة للأخطاء
class UnifiedAdminApiService extends GetxService {
  static UnifiedAdminApiService get instance => Get.find<UnifiedAdminApiService>();
  
  final StorageService _storageService = Get.find<StorageService>();
  late final http.Client _httpClient;
  String? _authToken;

  @override
  void onInit() {
    super.onInit();
    _httpClient = http.Client();
    _initializeToken();
  }

  @override
  void onClose() {
    _httpClient.close();
    super.onClose();
  }

  /// تهيئة التوكن
  Future<void> _initializeToken() async {
    _authToken = await _storageService.getToken();
  }

  /// تحديث التوكن
  Future<void> updateToken(String? token) async {
    _authToken = token;
    if (token != null) {
      await _storageService.saveToken(token);
    }
  }

  /// الحصول على الرؤوس مع التوكن
  Map<String, String> get _headers {
    final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
    if (_authToken != null && _authToken!.isNotEmpty) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  /// معالج الطلبات العام مع معالجة الأخطاء
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    try {
      // بناء URL
      var uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');
      if (queryParams != null && queryParams.isNotEmpty) {
        uri = uri.replace(queryParameters: queryParams);
      }

      // إعداد الطلب
      late http.Response response;
      final headers = _headers;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _httpClient.get(uri, headers: headers)
              .timeout(const Duration(seconds: 30));
          break;
        case 'POST':
          response = await _httpClient.post(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(const Duration(seconds: 30));
          break;
        case 'PUT':
          response = await _httpClient.put(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(const Duration(seconds: 30));
          break;
        case 'DELETE':
          response = await _httpClient.delete(uri, headers: headers)
              .timeout(const Duration(seconds: 30));
          break;
        default:
          throw Exception('HTTP method not supported: $method');
      }

      // معالجة الاستجابة
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response;
      } else {
        // معالجة خاصة لخطأ 401 (غير مصرح)
        if (response.statusCode == 401) {
          await _handleUnauthorized();
        }
        
        throw ApiException(
          statusCode: response.statusCode,
          message: _extractErrorMessage(response),
        );
      }
    } catch (e) {
      debugPrint('خطأ في الطلب: $e');
      
      // معالجة أخطاء الشبكة
      if (e.toString().contains('SocketException') || 
          e.toString().contains('HandshakeException') ||
          e.toString().contains('TimeoutException')) {
        throw ApiException(
          statusCode: 0,
          message: 'خطأ في الاتصال بالخادم، تحقق من الإنترنت',
        );
      }
      
      rethrow;
    }
  }

  /// استخراج رسالة الخطأ من الاستجابة
  String _extractErrorMessage(http.Response response) {
    try {
      // التحقق من وجود محتوى في الاستجابة
      if (response.body.isEmpty) {
        return 'حدث خطأ في الخادم (${response.statusCode})';
      }
      
      final data = jsonDecode(response.body);
      if (data is Map<String, dynamic>) {
        return data['message'] ?? data['error'] ?? 'حدث خطأ غير متوقع';
      }
    } catch (e) {
      debugPrint('خطأ في تحليل رسالة الخطأ: $e');
      // إذا فشل تحليل JSON، نعرض محتوى الاستجابة إذا كان نصاً
      if (response.body.isNotEmpty && response.body.length < 200) {
        return response.body;
      }
    }
    return 'حدث خطأ في الخادم (${response.statusCode})';
  }

  /// معالجة خطأ عدم التصريح (401)
  Future<void> _handleUnauthorized() async {
    try {
      // مسح التوكن المحفوظ
      await _storageService.removeAuthToken();
      _authToken = null;
      
      // إظهار رسالة للمستخدم
      Get.snackbar(
        'انتهت جلسة العمل',
        'يرجى تسجيل الدخول مرة أخرى',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Get.theme.colorScheme.errorContainer,
        colorText: Get.theme.colorScheme.onErrorContainer,
        duration: const Duration(seconds: 5),
      );
      
      // إعادة توجيه لصفحة تسجيل الدخول
      Get.offAllNamed('/login');
    } catch (e) {
      debugPrint('خطأ في معالجة عدم التصريح: $e');
    }
  }

  // ==================== إدارة المستخدمين ====================

  /// جلب جميع المستخدمين
  Future<List<User>> getAllUsers({
    int page = 1,
    int pageSize = 20,
    String? search,
    String? sortBy,
    bool sortAscending = true,
  }) async {
    debugPrint('🔵 [UnifiedAdminApiService] getAllUsers: استدعاء السيرفر page=$page, pageSize=$pageSize, search=$search');
    final queryParams = <String, String>{
      'page': page.toString(),
      'pageSize': pageSize.toString(),
      'sortAscending': sortAscending.toString(),
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (sortBy != null && sortBy.isNotEmpty) {
      queryParams['sortBy'] = sortBy;
    }

    final response = await _makeRequest('GET', '/api/users', queryParams: queryParams);
    debugPrint('🟡 [UnifiedAdminApiService] API Response (users): ${response.body}');
    final data = jsonDecode(response.body) as List;
    final users = data.map((json) => User.fromJson(json)).toList();
    debugPrint('🟢 [UnifiedAdminApiService] عدد المستخدمين المسترجعين: \\${users.length}');
    return users;
  }

  /// إنشاء مستخدم جديد
  Future<User> createUser(User user) async {
    final response = await _makeRequest('POST', '/api/users', body: user.toJson());
    return User.fromJson(jsonDecode(response.body));
  }

  /// تحديث مستخدم
  Future<User> updateUser(User user) async {
    final response = await _makeRequest('PUT', '/api/users/${user.id}', body: user.toJson());

    // التعامل مع استجابة 204 (No Content)
    if (response.statusCode == 204) {
      debugPrint('✅ تم تحديث المستخدم بنجاح (204 No Content)');
      return user; // إرجاع البيانات المحدثة
    }

    return User.fromJson(jsonDecode(response.body));
  }

  /// حذف مستخدم
  Future<void> deleteUser(int userId) async {
    await _makeRequest('DELETE', '/api/users/$userId');
  }

  /// تحديث حالة نشاط المستخدم
  Future<void> updateUserActiveStatus(int userId, bool isActive) async {
    final response = await _makeRequest('PUT', '/api/users/$userId/status', body: {'isActive': isActive});

    // التحقق من نجاح العملية
    if (response.statusCode == 204 || response.statusCode == 200) {
      debugPrint('✅ تم تحديث حالة نشاط المستخدم بنجاح');
    }
  }

  // ==================== إدارة الأدوار ====================

  /// جلب جميع الأدوار
  Future<List<Role>> getAllRoles() async {
    final response = await _makeRequest('GET', '/api/roles');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => Role.fromJson(json)).toList();
  }

  /// إنشاء دور جديد
  Future<Role> createRole(Role role) async {
    final response = await _makeRequest('POST', '/api/roles', body: role.toJson());
    return Role.fromJson(jsonDecode(response.body));
  }

  /// تحديث دور
  Future<Role> updateRole(Role role) async {
    final response = await _makeRequest('PUT', '/api/roles/${role.id}', body: role.toJson());
    return Role.fromJson(jsonDecode(response.body));
  }

  /// حذف دور
  Future<void> deleteRole(int roleId) async {
    await _makeRequest('DELETE', '/api/roles/$roleId');
  }

  // ==================== إدارة الصلاحيات ====================

  /// جلب جميع الصلاحيات
  Future<List<Permission>> getAllPermissions() async {
    final response = await _makeRequest('GET', '/api/permissions');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => Permission.fromJson(json)).toList();
  }

  /// تحديث صلاحيات المستخدم
  Future<void> updateUserPermissions(int userId, List<int> permissionIds) async {
    await _makeRequest('PUT', '/api/users/$userId/permissions', body: {
      'permissionIds': permissionIds,
    });
  }

  /// تحديث صلاحيات الدور
  Future<void> updateRolePermissions(int roleId, List<int> permissionIds) async {
    await _makeRequest('PUT', '/api/roles/$roleId/permissions', body: {
      'permissionIds': permissionIds,
    });
  }

  /// جلب صلاحيات المستخدم
  Future<List<Permission>> getUserPermissions(int userId) async {
    final response = await _makeRequest('GET', '/api/users/$userId/permissions');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => Permission.fromJson(json)).toList();
  }

  // ==================== سجلات النظام ====================

  /// جلب سجلات النظام
  Future<List<SystemLog>> getSystemLogs({
    int page = 1,
    int pageSize = 50,
    String? logLevel,
    String? logType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'pageSize': pageSize.toString(),
    };

    if (logLevel != null) queryParams['logLevel'] = logLevel;
    if (logType != null) queryParams['logType'] = logType;
    if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
    if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

    final response = await _makeRequest('GET', '/api/system-logs', queryParams: queryParams);
    final data = jsonDecode(response.body) as List;
    return data.map((json) => SystemLog.fromJson(json)).toList();
  }

  /// جلب سجلات النشاط
  Future<List<ActivityLog>> getActivityLogs({
    int page = 1,
    int pageSize = 50,
    int? userId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'pageSize': pageSize.toString(),
    };

    if (userId != null) queryParams['userId'] = userId.toString();
    if (action != null) queryParams['action'] = action;
    if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
    if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

    final response = await _makeRequest('GET', '/api/activity-logs', queryParams: queryParams);
    final data = jsonDecode(response.body) as List;
    return data.map((json) => ActivityLog.fromJson(json)).toList();
  }

  // ==================== النسخ الاحتياطية ====================

  /// جلب النسخ الاحتياطية
  Future<List<Backup>> getBackups() async {
    final response = await _makeRequest('GET', '/api/backups');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => Backup.fromJson(json)).toList();
  }

  /// إنشاء نسخة احتياطية
  Future<Backup> createBackup(String description) async {
    final response = await _makeRequest('POST', '/api/backups', body: {
      'description': description,
    });
    return Backup.fromJson(jsonDecode(response.body));
  }

  /// استعادة نسخة احتياطية
  Future<void> restoreBackup(int backupId) async {
    await _makeRequest('POST', '/api/backups/$backupId/restore');
  }

  /// حذف نسخة احتياطية
  Future<void> deleteBackup(int backupId) async {
    await _makeRequest('DELETE', '/api/backups/$backupId');
  }

  // ==================== إعدادات النظام ====================

  /// جلب إعدادات النظام
  Future<List<SystemSetting>> getSystemSettings() async {
    final response = await _makeRequest('GET', '/api/system-settings');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => SystemSetting.fromJson(json)).toList();
  }

  /// تحديث إعداد النظام
  Future<void> updateSystemSetting(String key, String value) async {
    await _makeRequest('PUT', '/api/system-settings/$key', body: {
      'value': value,
    });
  }

  /// إنشاء إعداد نظام جديد
  Future<SystemSetting> createSystemSetting(dynamic setting) async {
    final response = await _makeRequest('POST', '/api/system-settings', body: {
      'settingKey': setting.key,
      'settingValue': setting.value,
      'description': setting.description,
      'settingGroup': setting.category,
    });
    return SystemSetting.fromJson(jsonDecode(response.body));
  }

  /// حذف إعداد النظام
  Future<void> deleteSystemSetting(String key) async {
    await _makeRequest('DELETE', '/api/system-settings/$key');
  }

  /// إعادة تعيين إعدادات النظام للافتراضية
  Future<void> resetSystemSettings() async {
    await _makeRequest('POST', '/api/system-settings/reset');
  }

  /// استيراد إعدادات النظام
  Future<void> importSystemSettings(Map<String, dynamic> data) async {
    await _makeRequest('POST', '/api/system-settings/import', body: data);
  }

  /// تصدير إعدادات النظام
  Future<Map<String, dynamic>> exportSystemSettings() async {
    final response = await _makeRequest('GET', '/api/system-settings/export');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  /// إنهاء جميع الجلسات النشطة
  Future<void> terminateAllSessions() async {
    await _makeRequest('POST', '/api/sessions/terminate-all');
  }

  /// الحصول على إحصائيات النظام
  Future<Map<String, dynamic>> getSystemStatistics() async {
    final response = await _makeRequest('GET', '/api/system/statistics');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  /// اختبار الاتصال بقاعدة البيانات
  Future<void> testDatabaseConnection() async {
    await _makeRequest('GET', '/api/system/test-database-connection');
  }

  // ==================== الإحصائيات ====================

  /// جلب إحصائيات المستخدمين
  Future<Map<String, dynamic>> getUserStatistics() async {
    final response = await _makeRequest('GET', '/api/admin/user-statistics');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  // ==================== إدارة قاعدة البيانات ====================

  /// جلب جداول قاعدة البيانات
  Future<List<String>> getDatabaseTables() async {
    final response = await _makeRequest('GET', '/api/admin/database/tables');
    final data = jsonDecode(response.body) as List;
    return data.cast<String>();
  }

  /// تصدير جدول إلى CSV
  Future<String> exportTableToCsv(String tableName) async {
    final response = await _makeRequest('POST', '/api/admin/database/export', body: {
      'tableName': tableName,
      'format': 'csv',
    });
    final data = jsonDecode(response.body);
    return data['downloadUrl'] as String;
  }

  /// تحسين قاعدة البيانات
  Future<void> optimizeDatabase() async {
    await _makeRequest('POST', '/api/admin/database/optimize');
  }

  /// جلب جميع الشاشات
  Future<List<Screen>> getAllScreens() async {
    final response = await _makeRequest('GET', '/api/screens');
    final data = jsonDecode(response.body) as List;
    return data.map((json) => Screen.fromJson(json)).toList();
  }

  /// إنشاء صلاحية جديدة
  Future<void> createPermission({
    required String name,
    String? description,
    required int? screenId,
  }) async {
    final body = {
      'name': name,
      'description': description,
      'screenId': screenId,
    };
    await _makeRequest('POST', '/api/permissions', body: body);
  }

  /// تعديل صلاحية
  Future<void> updatePermission({
    required int id,
    required String name,
    String? description,
    required int? screenId,
  }) async {
    final body = {
      'name': name,
      'description': description,
      'screenId': screenId,
    };
    await _makeRequest('PUT', '/api/permissions/$id', body: body);
  }

  /// حذف صلاحية
  Future<void> deletePermission(int id) async {
    await _makeRequest('DELETE', '/api/permissions/$id');
  }
}

/// استثناء API مخصص
class ApiException implements Exception {
  final int statusCode;
  final String message;

  ApiException({required this.statusCode, required this.message});

  @override
  String toString() => 'ApiException($statusCode): $message';
}