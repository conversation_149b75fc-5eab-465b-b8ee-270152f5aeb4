import 'package:flutter/foundation.dart';
import '../../models/report_schedule_models.dart';
import 'api_service.dart';

/// خدمة API لجداول التقارير - متطابقة مع ASP.NET Core API
class ReportSchedulesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع جداول التقارير
  Future<List<ReportSchedule>> getAllReportSchedules() async {
    try {
      final response = await _apiService.get('/ReportSchedules');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على جداول التقارير: $e');
      rethrow;
    }
  }

  /// الحصول على جدول تقرير بواسطة المعرف
  Future<ReportSchedule?> getReportScheduleById(int id) async {
    try {
      final response = await _apiService.get('/ReportSchedules/$id');
      return _apiService.handleResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على جدول التقرير $id: $e');
      return null;
    }
  }

  /// الحصول على الجداول النشطة
  Future<List<ReportSchedule>> getActiveSchedules() async {
    try {
      final response = await _apiService.get('/ReportSchedules/active');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الجداول النشطة: $e');
      rethrow;
    }
  }

  /// الحصول على جداول مستخدم محدد
  Future<List<ReportSchedule>> getUserSchedules(int userId) async {
    try {
      final response = await _apiService.get('/ReportSchedules/user/$userId');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على جداول المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على الجداول المجدولة للتنفيذ اليوم
  Future<List<ReportSchedule>> getTodaySchedules() async {
    try {
      final response = await _apiService.get('/ReportSchedules/today');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على جداول اليوم: $e');
      rethrow;
    }
  }

  /// الحصول على الجداول المتأخرة
  Future<List<ReportSchedule>> getOverdueSchedules() async {
    try {
      final response = await _apiService.get('/ReportSchedules/overdue');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الجداول المتأخرة: $e');
      rethrow;
    }
  }

  /// إنشاء جدول تقرير جديد
  Future<ReportSchedule> createReportSchedule(ReportSchedule schedule) async {
    try {
      final response = await _apiService.post(
        '/ReportSchedules',
        schedule.toJson(),
      );
      return _apiService.handleResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء جدول التقرير: $e');
      rethrow;
    }
  }

  /// تحديث جدول تقرير
  Future<ReportSchedule> updateReportSchedule(int id, ReportSchedule schedule) async {
    try {
      final response = await _apiService.put(
        '/ReportSchedules/$id',
        schedule.toJson(),
      );
      return _apiService.handleResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث جدول التقرير $id: $e');
      rethrow;
    }
  }

  /// حذف جدول تقرير
  Future<bool> deleteReportSchedule(int id) async {
    try {
      final response = await _apiService.delete('/ReportSchedules/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف جدول التقرير $id: $e');
      return false;
    }
  }

  /// تفعيل جدول تقرير
  Future<bool> activateSchedule(int id) async {
    try {
      final response = await _apiService.put(
        '/ReportSchedules/$id/activate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل جدول التقرير: $e');
      return false;
    }
  }

  /// إلغاء تفعيل جدول تقرير
  Future<bool> deactivateSchedule(int id) async {
    try {
      final response = await _apiService.put(
        '/ReportSchedules/$id/deactivate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل جدول التقرير: $e');
      return false;
    }
  }

  /// تنفيذ جدول تقرير فوراً
  Future<bool> executeScheduleNow(int id) async {
    try {
      final response = await _apiService.post(
        '/ReportSchedules/$id/execute',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تنفيذ جدول التقرير: $e');
      return false;
    }
  }

  /// إيقاف تنفيذ جدول تقرير مؤقتاً
  Future<bool> pauseSchedule(int id) async {
    try {
      final response = await _apiService.put(
        '/ReportSchedules/$id/pause',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إيقاف جدول التقرير: $e');
      return false;
    }
  }

  /// استئناف تنفيذ جدول تقرير
  Future<bool> resumeSchedule(int id) async {
    try {
      final response = await _apiService.put(
        '/ReportSchedules/$id/resume',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استئناف جدول التقرير: $e');
      return false;
    }
  }

  /// البحث في جداول التقارير
  Future<List<ReportSchedule>> searchSchedules(String query) async {
    try {
      final response = await _apiService.get('/ReportSchedules/search?q=$query');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في جداول التقارير: $e');
      return [];
    }
  }

  /// الحصول على سجل تنفيذ جدول تقرير
  Future<List<Map<String, dynamic>>> getScheduleExecutionHistory(int id) async {
    try {
      final response = await _apiService.get('/ReportSchedules/$id/execution-history');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل التنفيذ: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات جدول تقرير محدد
  Future<Map<String, dynamic>> getScheduleStatistics(int id) async {
    try {
      final response = await _apiService.get('/ReportSchedules/$id/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات جدول التقرير: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات جميع جداول التقارير
  Future<Map<String, dynamic>> getAllSchedulesStatistics() async {
    try {
      final response = await _apiService.get('/ReportSchedules/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات جداول التقارير: $e');
      return {};
    }
  }

  /// نسخ جدول تقرير
  Future<ReportSchedule> duplicateSchedule(int id, String newName) async {
    try {
      final response = await _apiService.post(
        '/ReportSchedules/$id/duplicate',
        {
          'newName': newName,
        },
      );
      return _apiService.handleResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في نسخ جدول التقرير: $e');
      rethrow;
    }
  }

  /// تصدير جدول تقرير
  Future<Map<String, dynamic>> exportSchedule(int id) async {
    try {
      final response = await _apiService.get('/ReportSchedules/$id/export');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير جدول التقرير: $e');
      return {};
    }
  }

  /// استيراد جدول تقرير
  Future<ReportSchedule> importSchedule(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post(
        '/ReportSchedules/import',
        data,
      );
      return _apiService.handleResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في استيراد جدول التقرير: $e');
      rethrow;
    }
  }

  /// الحصول على الجداول بحسب التكرار
  Future<List<ReportSchedule>> getSchedulesByFrequency(String frequency) async {
    try {
      final response = await _apiService.get('/ReportSchedules/frequency/$frequency');
      return _apiService.handleListResponse<ReportSchedule>(
        response,
        (json) => ReportSchedule.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الجداول بحسب التكرار: $e');
      return [];
    }
  }

  /// تحديث موعد التنفيذ التالي
  Future<bool> updateNextExecutionTime(int id, DateTime nextExecution) async {
    try {
      final timestamp = nextExecution.millisecondsSinceEpoch ~/ 1000;
      final response = await _apiService.put(
        '/ReportSchedules/$id/next-execution',
        {
          'nextExecutionTime': timestamp,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث موعد التنفيذ التالي: $e');
      return false;
    }
  }

  /// الحصول على تاريخ التشغيل التالي لجدول محدد
  Future<DateTime?> getNextExecutionTime(int id) async {
    try {
      final schedule = await getReportScheduleById(id);
      return schedule?.nextExecutionAtDateTime;
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ التشغيل التالي: $e');
      return null;
    }
  }
}
