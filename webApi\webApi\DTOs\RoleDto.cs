using System.ComponentModel.DataAnnotations;

namespace webApi.DTOs
{
    /// <summary>
    /// DTO لإنشاء دور جديد
    /// </summary>
    public class CreateRoleDto
    {
        /// <summary>
        /// اسم الدور (مفتاح فريد)
        /// </summary>
        [Required(ErrorMessage = "اسم الدور مطلوب")]
        [StringLength(50, ErrorMessage = "اسم الدور يجب أن يكون أقل من 50 حرف")]
        public string Name { get; set; } = null!;

        /// <summary>
        /// الاسم المعروض للدور
        /// </summary>
        [Required(ErrorMessage = "الاسم المعروض مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم المعروض يجب أن يكون أقل من 100 حرف")]
        public string DisplayName { get; set; } = null!;

        /// <summary>
        /// وصف الدور
        /// </summary>
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// مستوى الدور (1-100)
        /// </summary>
        [Range(1, 100, ErrorMessage = "مستوى الدور يجب أن يكون بين 1 و 100")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// هل الدور نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        [Required(ErrorMessage = "معرف المنشئ مطلوب")]
        public int CreatedBy { get; set; }

        /// <summary>
        /// قائمة معرفات الصلاحيات الافتراضية للدور
        /// </summary>
        public List<int> DefaultPermissionIds { get; set; } = new List<int>();
    }

    /// <summary>
    /// DTO لتحديث دور موجود
    /// </summary>
    public class UpdateRoleDto
    {
        /// <summary>
        /// الاسم المعروض للدور
        /// </summary>
        [StringLength(100, ErrorMessage = "الاسم المعروض يجب أن يكون أقل من 100 حرف")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// وصف الدور
        /// </summary>
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// مستوى الدور (1-100)
        /// </summary>
        [Range(1, 100, ErrorMessage = "مستوى الدور يجب أن يكون بين 1 و 100")]
        public int? Level { get; set; }

        /// <summary>
        /// هل الدور نشط
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// معرف المستخدم المحدث
        /// </summary>
        [Required(ErrorMessage = "معرف المحدث مطلوب")]
        public int UpdatedBy { get; set; }
    }

    /// <summary>
    /// DTO لمنح أو سحب صلاحية من دور
    /// </summary>
    public class GrantRevokePermissionDto
    {
        /// <summary>
        /// معرف الصلاحية
        /// </summary>
        [Required(ErrorMessage = "معرف الصلاحية مطلوب")]
        public int PermissionId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي يقوم بالعملية
        /// </summary>
        [Required(ErrorMessage = "معرف المستخدم مطلوب")]
        public int UserId { get; set; }
    }

    /// <summary>
    /// DTO لمنح صلاحيات متعددة لدور
    /// </summary>
    public class GrantMultiplePermissionsDto
    {
        /// <summary>
        /// قائمة معرفات الصلاحيات
        /// </summary>
        [Required(ErrorMessage = "قائمة الصلاحيات مطلوبة")]
        [MinLength(1, ErrorMessage = "يجب تحديد صلاحية واحدة على الأقل")]
        public List<int> PermissionIds { get; set; } = new List<int>();

        /// <summary>
        /// معرف المستخدم الذي يقوم بالعملية
        /// </summary>
        [Required(ErrorMessage = "معرف المستخدم مطلوب")]
        public int UserId { get; set; }

        /// <summary>
        /// هل يتم استبدال الصلاحيات الحالية أم إضافة إليها
        /// </summary>
        public bool ReplaceExisting { get; set; } = false;
    }

    /// <summary>
    /// DTO لاستجابة تفاصيل الدور
    /// </summary>
    public class RoleDetailsDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string DisplayName { get; set; } = null!;
        public string? Description { get; set; }
        public int Level { get; set; }
        public bool IsSystemRole { get; set; }
        public bool IsActive { get; set; }
        public long CreatedAt { get; set; }
        public long? UpdatedAt { get; set; }
        public int UsersCount { get; set; }
        public int PermissionsCount { get; set; }
        public List<PermissionSummaryDto> Permissions { get; set; } = new List<PermissionSummaryDto>();
        public List<UserSummaryDto> Users { get; set; } = new List<UserSummaryDto>();
    }

    /// <summary>
    /// DTO لملخص الصلاحية
    /// </summary>
    public class PermissionSummaryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string PermissionGroup { get; set; } = null!;
        public int Level { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// DTO لملخص المستخدم
    /// </summary>
    public class UserSummaryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string Email { get; set; } = null!;
        public bool IsActive { get; set; }
    }
}
