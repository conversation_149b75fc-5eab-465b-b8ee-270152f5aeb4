import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/user_permission_model.dart';

import 'api_service.dart';

/// خدمة API لصلاحيات المستخدمين - متطابقة مع ASP.NET Core API
class UserPermissionsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع صلاحيات المستخدمين
  Future<List<UserPermission>> getAllUserPermissions() async {
    try {
      final response = await _apiService.get('/api/UserPermissions');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدمين: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحية مستخدم بواسطة المعرف
  Future<UserPermission?> getUserPermissionById(int id) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/$id');
      return _apiService.handleResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحية المستخدم $id: $e');
      return null;
    }
  }

  /// الحصول على صلاحيات مستخدم محدد
  Future<List<UserPermission>> getUserPermissions(int userId) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/user/$userId');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحيات المستخدم الحالي
  Future<List<UserPermission>> getCurrentUserPermissions() async {
    try {
      final response = await _apiService.get('/api/UserPermissions/current-user');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدم الحالي: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين الذين لديهم صلاحية محددة
  Future<List<UserPermission>> getUsersWithPermission(int permissionId) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/permission/$permissionId');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين بالصلاحية $permissionId: $e');
      rethrow;
    }
  }

  /// منح صلاحية لمستخدم
  Future<UserPermission> grantPermission(int userId, int permissionId, {int? grantedBy, int? expiresAt}) async {
    try {
      // التأكد من وجود grantedBy
      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final actualGrantedBy = grantedBy ?? 1; // قيمة افتراضية

      final requestData = {
        'id': 0,
        'userId': userId,
        'permissionId': permissionId,
        'grantedBy': actualGrantedBy,
        'grantedAt': currentTime,
        'isActive': true,
        'expiresAt': expiresAt,
        'isDeleted': false,
      };

      debugPrint('🔍 إرسال طلب منح صلاحية: $requestData');

      final response = await _apiService.post('/api/UserPermissions/simple', requestData);
      return _apiService.handleResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية للمستخدم: $e');
      rethrow;
    }
  }

  /// تحديث صلاحية مستخدم
  Future<UserPermission> updateUserPermission(int id, UserPermission userPermission) async {
    try {
      final response = await _apiService.put(
        '/api/UserPermissions/$id',
        userPermission.toJson(),
      );
      return _apiService.handleResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث صلاحية المستخدم $id: $e');
      rethrow;
    }
  }

  /// إلغاء صلاحية من مستخدم
  Future<bool> revokePermission(int userId, int permissionId) async {
    try {
      final response = await _apiService.delete('/api/UserPermissions/revoke/$userId/$permissionId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية من المستخدم: $e');
      return false;
    }
  }

  /// حذف صلاحية مستخدم
  Future<bool> deleteUserPermission(int id) async {
    try {
      final response = await _apiService.delete('/api/UserPermissions/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف صلاحية المستخدم $id: $e');
      return false;
    }
  }

  /// تفعيل صلاحية مستخدم
  Future<bool> activateUserPermission(int id) async {
    try {
      final response = await _apiService.put(
        '/api/UserPermissions/$id/activate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل صلاحية المستخدم: $e');
      return false;
    }
  }

  /// إلغاء تفعيل صلاحية مستخدم
  Future<bool> deactivateUserPermission(int id) async {
    try {
      final response = await _apiService.put(
        '/api/UserPermissions/$id/deactivate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل صلاحية المستخدم: $e');
      return false;
    }
  }

  /// التحقق من صلاحية مستخدم محدد
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/check/$userId/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['hasPermission'] as bool? ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية المستخدم: $e');
      return false;
    }
  }

  /// التحقق من وجود صلاحية محددة لمستخدم (بالمعرف)
  Future<Map<String, dynamic>> checkUserPermissionById(int userId, int permissionId) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/check/$userId/$permissionId');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الصلاحية: $e');
      return {
        'exists': false,
        'canAdd': true,
        'error': e.toString(),
      };
    }
  }

  /// التحقق من صلاحية المستخدم الحالي
  Future<bool> checkCurrentUserPermission(String permissionName) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/check-current/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['hasPermission'] as bool? ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية المستخدم الحالي: $e');
      return false;
    }
  }

  /// الحصول على الصلاحيات المنتهية الصلاحية
  Future<List<UserPermission>> getExpiredPermissions() async {
    try {
      final response = await _apiService.get('/api/UserPermissions/expired');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات المنتهية الصلاحية: $e');
      return [];
    }
  }

  /// الحصول على الصلاحيات التي ستنتهي قريباً
  Future<List<UserPermission>> getExpiringPermissions({int daysAhead = 7}) async {
    try {
      final response = await _apiService.get('/api/UserPermissions/expiring?days=$daysAhead');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات التي ستنتهي قريباً: $e');
      return [];
    }
  }

  /// تجديد صلاحية مستخدم
  Future<bool> renewUserPermission(int id, int newExpiryDate) async {
    try {
      final response = await _apiService.put(
        '/api/UserPermissions/$id/renew',
        {
          'expiresAt': newExpiryDate,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تجديد صلاحية المستخدم: $e');
      return false;
    }
  }

  /// منح صلاحيات متعددة لمستخدم
  Future<List<UserPermission>> grantMultiplePermissions(
    int userId,
    List<int> permissionIds, {
    int? grantedBy,
    int? expiresAt,
  }) async {
    try {
      final response = await _apiService.post(
        '/api/UserPermissions/grant-multiple',
        {
          'userId': userId,
          'permissionIds': permissionIds,
          'grantedBy': grantedBy,
          'expiresAt': expiresAt,
        },
      );
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في منح صلاحيات متعددة للمستخدم: $e');
      rethrow;
    }
  }

  /// إلغاء صلاحيات متعددة من مستخدم
  Future<bool> revokeMultiplePermissions(int userId, List<int> permissionIds) async {
    try {
      final response = await _apiService.post(
        '/api/UserPermissions/revoke-multiple',
        {
          'userId': userId,
          'permissionIds': permissionIds,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء صلاحيات متعددة من المستخدم: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات صلاحيات المستخدمين
  Future<Map<String, dynamic>> getUserPermissionsStatistics() async {
    try {
      final response = await _apiService.get('/api/UserPermissions/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات صلاحيات المستخدمين: $e');
      return {};
    }
  }
}
