// افترضنا وجود نموذج لسجل النظام باسم SystemActivityLog
// يجب عليك إنشاء هذا النموذج إذا لم يكن موجودًا
// import 'package:flutter_application_2/models/system_activity_log_model.dart';

class SystemActivityLogReport {
  // final List<SystemActivityLog> logs;


  // يتم إنشاء هذا التقرير عادة من جدول سجلات النظام مباشرة
  // وليس من قائمة المهام، لذا قد تحتاج إلى تعديل هذه الدالة
  // لتناسب طريقة تخزين سجلات النظام لديك.
  // factory SystemActivityLogReport.fromTasks(List<Task> tasks) {
  //   // هذا مثال افتراضي ويجب تعديله ليناسب بياناتك
  //   final logs = <SystemActivityLog>[];
  //   return SystemActivityLogReport(logs: logs);
  // }
}