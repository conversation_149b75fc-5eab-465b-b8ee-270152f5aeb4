import 'user_model.dart';
import 'chat_group_models.dart';

/// أنواع محتوى الرسائل
enum MessageContentType {
  text(1, 'نص'),
  image(2, 'صورة'),
  file(3, 'ملف'),
  voice(4, 'صوت'),
  video(5, 'فيديو'),
  location(6, 'موقع');

  const MessageContentType(this.value, this.displayName);

  final int value;
  final String displayName;

  static MessageContentType fromValue(int value) {
    return MessageContentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MessageContentType.text,
    );
  }
}

/// أولوية الرسائل
enum MessagePriority {
  normal(0, 'عادية'),
  important(1, 'مهمة'),
  urgent(2, 'عاجلة');

  const MessagePriority(this.value, this.displayName);

  final int value;
  final String displayName;

  static MessagePriority fromValue(int value) {
    return MessagePriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => MessagePriority.normal,
    );
  }
}

/// نموذج الرسالة
class Message {
  final int id;
  final int groupId;
  final int senderId;
  final String content;
  final MessageContentType contentType;
  final int? replyToMessageId;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isRead;
  final bool isPinned;
  final int? pinnedAt;
  final int? pinnedBy;
  final MessagePriority priority;
  final bool isMarkedForFollowUp;
  final int? followUpAt;
  final int? markedForFollowUpBy;
  final bool isEdited;
  final int? receiverId;
  final int? sentAt;
  final List<String> mentionedUserIds; // للتوافق مع الشاشات
  final String? senderName; // للتوافق مع الشاشات

  // Navigation properties
  final ChatGroup? group;
  final User? sender;
  final User? receiver;
  final Message? replyToMessage;
  final List<dynamic>? attachments; // Will be MessageAttachment from message_attachment_models.dart
  final List<dynamic>? reactions; // Will be MessageReaction from message_reaction_models.dart

  const Message({
    required this.id,
    required this.groupId,
    required this.senderId,
    required this.content,
    this.contentType = MessageContentType.text,
    this.replyToMessageId,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isRead = false,
    this.isPinned = false,
    this.pinnedAt,
    this.pinnedBy,
    this.priority = MessagePriority.normal,
    this.isMarkedForFollowUp = false,
    this.followUpAt,
    this.markedForFollowUpBy,
    this.isEdited = false,
    this.receiverId,
    this.sentAt,
    this.mentionedUserIds = const [],
    this.senderName,
    this.group,
    this.sender,
    this.receiver,
    this.replyToMessage,
    this.attachments,
    this.reactions,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as int,
      groupId: json['groupId'] as int,
      senderId: json['senderId'] as int,
      content: json['content'] as String,
      contentType: MessageContentType.fromValue(json['contentType'] as int),
      replyToMessageId: json['replyToMessageId'] as int?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isRead: json['isRead'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      pinnedAt: json['pinnedAt'] as int?,
      pinnedBy: json['pinnedBy'] as int?,
      priority: MessagePriority.fromValue(json['priority'] as int? ?? 0),
      isMarkedForFollowUp: json['isMarkedForFollowUp'] as bool? ?? false,
      followUpAt: json['followUpAt'] as int?,
      markedForFollowUpBy: json['markedForFollowUpBy'] as int?,
      isEdited: json['isEdited'] as bool? ?? false,
      receiverId: json['receiverId'] as int?,
      sentAt: json['sentAt'] as int?,
      mentionedUserIds: json['mentionedUserIds'] != null
          ? List<String>.from(json['mentionedUserIds'] as List)
          : const [],
      senderName: json['senderName'] as String?,
      group: json['group'] != null
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
      sender: json['sender'] != null
          ? User.fromJson(json['sender'] as Map<String, dynamic>)
          : null,
      receiver: json['receiver'] != null
          ? User.fromJson(json['receiver'] as Map<String, dynamic>)
          : null,
      replyToMessage: json['replyToMessage'] != null
          ? Message.fromJson(json['replyToMessage'] as Map<String, dynamic>)
          : null,
      attachments: json['messageAttachments'] as List?,
      reactions: json['messageReactions'] as List?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'senderId': senderId,
      'content': content,
      'contentType': contentType.value,
      'replyToMessageId': replyToMessageId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isRead': isRead,
      'isPinned': isPinned,
      'pinnedAt': pinnedAt,
      'pinnedBy': pinnedBy,
      'priority': priority.value,
      'isMarkedForFollowUp': isMarkedForFollowUp,
      'followUpAt': followUpAt,
      'markedForFollowUpBy': markedForFollowUpBy,
      'isEdited': isEdited,
      'receiverId': receiverId,
      'sentAt': sentAt,
      'mentionedUserIds': mentionedUserIds,
      'senderName': senderName,
    };
  }

  Message copyWith({
    int? id,
    int? groupId,
    int? senderId,
    String? content,
    MessageContentType? contentType,
    int? replyToMessageId,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    bool? isRead,
    bool? isPinned,
    int? pinnedAt,
    int? pinnedBy,
    MessagePriority? priority,
    bool? isMarkedForFollowUp,
    int? followUpAt,
    int? markedForFollowUpBy,
    bool? isEdited,
    int? receiverId,
    int? sentAt,
    List<String>? mentionedUserIds,
    String? senderName,
    ChatGroup? group,
    User? sender,
    User? receiver,
    Message? replyToMessage,
    List<dynamic>? attachments,
    List<dynamic>? reactions,
  }) {
    return Message(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      contentType: contentType ?? this.contentType,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isRead: isRead ?? this.isRead,
      isPinned: isPinned ?? this.isPinned,
      pinnedAt: pinnedAt ?? this.pinnedAt,
      pinnedBy: pinnedBy ?? this.pinnedBy,
      priority: priority ?? this.priority,
      isMarkedForFollowUp: isMarkedForFollowUp ?? this.isMarkedForFollowUp,
      followUpAt: followUpAt ?? this.followUpAt,
      markedForFollowUpBy: markedForFollowUpBy ?? this.markedForFollowUpBy,
      isEdited: isEdited ?? this.isEdited,
      receiverId: receiverId ?? this.receiverId,
      sentAt: sentAt ?? this.sentAt,
      mentionedUserIds: mentionedUserIds ?? this.mentionedUserIds,
      senderName: senderName ?? this.senderName,
      group: group ?? this.group,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
      replyToMessage: replyToMessage ?? this.replyToMessage,
      attachments: attachments ?? this.attachments,
      reactions: reactions ?? this.reactions,
    );
  }

  /// التحقق من كون الرسالة من المستخدم الحالي
  bool isFromUser(int userId) => senderId == userId;

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    final displayContent = content.length > 50 ? '${content.substring(0, 50)}...' : content;
    return 'Message(id: $id, groupId: $groupId, senderId: $senderId, content: $displayContent)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إرسال رسالة
class SendMessageRequest {
  final int groupId;
  final String content;
  final int contentType;
  final int? replyToMessageId;
  final int? receiverId;
  final int priority;

  const SendMessageRequest({
    required this.groupId,
    required this.content,
    this.contentType = 1, // text by default
    this.replyToMessageId,
    this.receiverId,
    this.priority = 0, // normal priority
  });

  Map<String, dynamic> toJson() {
    return {
      'groupId': groupId,
      'content': content,
      'contentType': contentType,
      'replyToMessageId': replyToMessageId,
      'receiverId': receiverId,
      'priority': priority,
    };
  }
}

/// نموذج طلب تحديث رسالة
class UpdateMessageRequest {
  final int messageId;
  final String content;

  const UpdateMessageRequest({
    required this.messageId,
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'content': content,
    };
  }
}

/// نموذج طلب حذف رسالة
class DeleteMessageRequest {
  final int messageId;
  final bool softDelete;

  const DeleteMessageRequest({
    required this.messageId,
    this.softDelete = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'softDelete': softDelete,
    };
  }
}
