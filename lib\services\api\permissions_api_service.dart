import 'package:flutter/foundation.dart';
import 'dart:convert';
import '../../models/permission_models.dart';
import '../../models/user_permission_model.dart';
import 'api_service.dart';

/// خدمة API موحدة للصلاحيات - متطابقة مع ASP.NET Core API
/// تجمع جميع وظائف إدارة الصلاحيات والأدوار في خدمة واحدة محسنة
class PermissionsApiService {
  final ApiService _apiService = ApiService();

  // ===== الصلاحيات الأساسية =====

  /// الحصول على جميع الصلاحيات
  Future<List<Permission>> getAllPermissions() async {
    try {
      debugPrint('🔄 جاري جلب جميع الصلاحيات...');
      final response = await _apiService.get('/api/Permissions');
      final permissions = _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
      debugPrint('✅ تم جلب ${permissions.length} صلاحية');
      return permissions;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الصلاحيات: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحية بواسطة المعرف
  Future<Permission?> getPermissionById(int id) async {
    try {
      final response = await _apiService.get('/api/Permissions/$id');
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحية $id: $e');
      return null;
    }
  }

  /// إنشاء صلاحية جديدة
  Future<Permission> createPermission(Permission permission) async {
    try {
      final response = await _apiService.post(
        '/api/Permissions',
        permission.toJson(),
      );
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الصلاحية: $e');
      rethrow;
    }
  }

  /// تحديث صلاحية
  Future<Permission> updatePermission(int id, Permission permission) async {
    try {
      final response = await _apiService.put(
        '/api/Permissions/$id',
        permission.toJson(),
      );
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الصلاحية $id: $e');
      rethrow;
    }
  }

  /// حذف صلاحية
  Future<bool> deletePermission(int id) async {
    try {
      final response = await _apiService.delete('/api/Permissions/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الصلاحية $id: $e');
      return false;
    }
  }



  /// الحصول على جميع صلاحيات مستخدم
  Future<List<String>> getAllUserPermissions(int userId) async {
    try {
      final response = await _apiService.get('/api/Permissions/user/$userId');
      final decoded = jsonDecode(response.body);
      return List<String>.from(decoded);
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدم: $e');
      return [];
    }
  }

  /// مسح ذاكرة التخزين المؤقت لصلاحيات المستخدم
  Future<bool> clearUserPermissionsCache(int userId) async {
    try {
      final response = await _apiService.post(
        '/api/Permissions/clear-cache',
        {'userId': userId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مسح ذاكرة التخزين المؤقت: $e');
      return false;
    }
  }

  // ===== التحقق من الصلاحيات المحسن =====

  /// التحقق من صلاحية مستخدم محدد
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية $permissionName للمستخدم $userId');
      final response = await _apiService.get('/api/Permissions/check/$userId/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      final hasPermission = data['hasPermission'] as bool? ?? false;
      debugPrint('✅ نتيجة التحقق: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// التحقق من صلاحية المستخدم الحالي
  Future<bool> checkCurrentUserPermission(String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية المستخدم الحالي: $permissionName');
      final response = await _apiService.get('/api/Permissions/check-current/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      final hasPermission = data['hasPermission'] as bool? ?? false;
      debugPrint('✅ نتيجة التحقق: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// التحقق من صلاحيات متعددة دفعة واحدة
  Future<Map<String, bool>> checkMultiplePermissions(int userId, List<String> permissionNames) async {
    try {
      debugPrint('🔍 التحقق من ${permissionNames.length} صلاحية للمستخدم $userId');
      final response = await _apiService.post(
        '/api/Permissions/check-multiple',
        {
          'userId': userId,
          'permissionNames': permissionNames,
        },
      );
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      final results = Map<String, bool>.from(data);
      debugPrint('✅ تم التحقق من ${results.length} صلاحية');
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
      return Map.fromIterable(permissionNames, value: (_) => false);
    }
  }

  /// الحصول على صلاحيات مستخدم مخصصة
  Future<List<UserPermission>> getCustomUserPermissions(int userId) async {
    try {
      final response =
          await _apiService.get('/api/UserPermissions/user/$userId');
      return _apiService.handleListResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المستخدم المخصصة: $e');
      return [];
    }
  }

  /// إضافة صلاحية مخصصة لمستخدم
  Future<UserPermission> addCustomUserPermission(
      UserPermission userPermission) async {
    try {
      final jsonData = userPermission.toJsonForApi();
      debugPrint('🔍 إرسال بيانات الصلاحية: $jsonData');

      // التحقق من صحة البيانات قبل الإرسال
      if (jsonData['userId'] == null || jsonData['userId'] <= 0) {
        throw Exception('معرف المستخدم غير صحيح');
      }
      if (jsonData['permissionId'] == null || jsonData['permissionId'] <= 0) {
        throw Exception('معرف الصلاحية غير صحيح');
      }
      if (jsonData['grantedBy'] == null || jsonData['grantedBy'] <= 0) {
        throw Exception('معرف المانح غير صحيح');
      }
      if (jsonData['grantedAt'] == null || jsonData['grantedAt'] <= 0) {
        throw Exception('تاريخ المنح غير صحيح');
      }

      final response = await _apiService.post('/api/UserPermissions/simple', jsonData);
      debugPrint('✅ استجابة الخادم: $response');
      return _apiService.handleResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('❌ خطأ في إضافة صلاحية مخصصة: $e');
      rethrow;
    }
  }

  /// تحديث صلاحية مخصصة لمستخدم
  Future<UserPermission> updateCustomUserPermission(
      int id, UserPermission userPermission) async {
    try {
      final response = await _apiService.put(
        '/api/UserPermissions/$id',
        userPermission.toJson(),
      );
      return _apiService.handleResponse<UserPermission>(
        response,
        (json) => UserPermission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث صلاحية مخصصة: $e');
      rethrow;
    }
  }

  /// حذف صلاحية مخصصة لمستخدم
  Future<bool> deleteCustomUserPermission(int id) async {
    try {
      debugPrint('🗑️ محاولة حذف الصلاحية المخصصة: $id');
      final response = await _apiService.delete('/api/UserPermissions/$id');

      debugPrint('📡 API Response: DELETE /api/UserPermissions/$id - Status: ${response.statusCode}');

      if (response.statusCode == 404) {
        debugPrint('⚠️ الصلاحية غير موجودة (404) - ربما تم حذفها مسبقاً');
        return true; // نعتبرها نجحت لأن الهدف تحقق (الصلاحية غير موجودة)
      }

      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم حذف الصلاحية المخصصة بنجاح: $id');
      } else {
        debugPrint('❌ فشل حذف الصلاحية المخصصة: $id - Status: ${response.statusCode}');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في حذف صلاحية مخصصة: $e');
      return false;
    }
  }

  /// حذف منطقي لصلاحية مخصصة لمستخدم
  Future<bool> softDeleteCustomUserPermission(int id) async {
    try {
      debugPrint('🗑️ محاولة الحذف المنطقي للصلاحية المخصصة: $id');
      final response = await _apiService.delete('/api/UserPermissions/$id/soft');

      debugPrint('📡 API Response: DELETE /api/UserPermissions/$id/soft - Status: ${response.statusCode}');

      if (response.statusCode == 404) {
        debugPrint('⚠️ الصلاحية غير موجودة (404) - ربما تم حذفها مسبقاً');
        return true; // نعتبرها نجحت لأن الهدف تحقق
      }

      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم الحذف المنطقي للصلاحية المخصصة بنجاح: $id');
      } else {
        debugPrint('❌ فشل الحذف المنطقي للصلاحية المخصصة: $id - Status: ${response.statusCode}');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في الحذف المنطقي للصلاحية المخصصة: $e');
      return false;
    }
  }

  /// جلب الصلاحيات حسب المجموعة
  Future<List<Permission>> getPermissionsByGroup(String group) async {
    try {
      final response = await _apiService.get('/api/Permissions/group/$group');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في جلب الصلاحيات حسب المجموعة: $e');
      rethrow;
    }
  }

  /// جلب صلاحيات الدور الافتراضي
  Future<List<Permission>> getRolePermissions(int roleId) async {
    try {
      final response = await _apiService.get('/api/Permissions/by-role/$roleId');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في جلب صلاحيات الدور: $e');
      rethrow;
    }
  }

  /// جلب صلاحيات الدور المخصص
  Future<List<Permission>> getCustomRolePermissions(int customRoleId) async {
    try {
      final response = await _apiService.get('/api/Permissions/by-custom-role/$customRoleId');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في جلب صلاحيات الدور المخصص: $e');
      rethrow;
    }
  }

  /// التحقق من صلاحية مستخدم (حسب userId واسم الصلاحية)
  Future<bool> checkPermission(int userId, String permissionName) async {
    try {
      final response = await _apiService.get('/api/Permissions/check/$userId/$permissionName');
      final data = response.body;
      // تحليل body كـ JSON Map
      // ignore: unnecessary_type_check
      final decoded = data is String ? (data.isNotEmpty ? jsonDecode(data) : null) : data;
      if (decoded is Map<String, dynamic> && decoded.containsKey('hasPermission')) {
        return decoded['hasPermission'] == true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// منح صلاحية لدور (مقترح: تحتاج endpoint خاص أو استخدام UserPermissionsController)
  Future<void> grantPermissionToRole(int roleId, int permissionId) async {
    // يجب إنشاء endpoint خاص لهذا الغرض في الـ API
    throw UnimplementedError('يجب تنفيذ endpoint لمنح صلاحية لدور');
  }

  /// إلغاء صلاحية من دور (مقترح: تحتاج endpoint خاص أو استخدام UserPermissionsController)
  Future<void> revokePermissionFromRole(int roleId, int permissionId) async {
    // يجب إنشاء endpoint خاص لهذا الغرض في الـ API
    throw UnimplementedError('يجب تنفيذ endpoint لإلغاء صلاحية من دور');
  }
}