import 'package:flutter_application_2/models/user_model.dart';
import 'package:flutter_application_2/services/api/users_api_service.dart';

/// خدمة إدارة المستخدمين
/// 
/// توفر هذه الخدمة واجهة مبسطة للتعامل مع المستخدمين
/// وتستخدم UsersApiService للتواصل مع الـ API
class UserService {
  final UsersApiService _usersApiService = UsersApiService();

  /// الحصول على مستخدم بواسطة المعرف
  /// 
  /// [userId] معرف المستخدم (يمكن أن يكون String أو int)
  /// يرجع بيانات المستخدم أو null في حالة عدم وجوده
  Future<User?> getUserById(dynamic userId) async {
    try {
      // تحويل userId إلى int إذا كان String
      int userIdInt;
      if (userId is String) {
        userIdInt = int.parse(userId);
      } else if (userId is int) {
        userIdInt = userId;
      } else {
        return null;
      }
      
      return await _usersApiService.getUserById(userIdInt);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المستخدم الحالي
  /// 
  /// يرجع بيانات المستخدم الحالي أو null في حالة عدم تسجيل الدخول
  Future<User?> getCurrentUser() async {
    try {
      return await _usersApiService.getCurrentUser();
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع المستخدمين
  /// 
  /// يرجع قائمة بجميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      return await _usersApiService.getAllUsers();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على المستخدمين النشطين
  /// 
  /// يرجع قائمة بالمستخدمين النشطين فقط
  Future<List<User>> getActiveUsers() async {
    try {
      return await _usersApiService.getActiveUsers();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على المستخدمين المتصلين حالياً
  /// 
  /// يرجع قائمة بالمستخدمين المتصلين حالياً
  Future<List<User>> getOnlineUsers() async {
    try {
      return await _usersApiService.getOnlineUsers();
    } catch (e) {
      return [];
    }
  }

  /// البحث في المستخدمين
  /// 
  /// [query] نص البحث
  /// يرجع قائمة بالمستخدمين الذين يطابقون البحث
  Future<List<User>> searchUsers(String query) async {
    try {
      return await _usersApiService.searchUsers(query);
    } catch (e) {
      return [];
    }
  }

  /// الحصول على مستخدمي قسم محدد
  /// 
  /// [departmentId] معرف القسم
  /// يرجع قائمة بمستخدمي القسم
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      return await _usersApiService.getUsersByDepartment(departmentId);
    } catch (e) {
      return [];
    }
  }

  /// الحصول على المستخدمين بحسب الدور
  /// 
  /// [role] الدور المطلوب
  /// يرجع قائمة بالمستخدمين الذين لديهم هذا الدور
  Future<List<User>> getUsersByRole(String role) async {
    try {
      return await _usersApiService.getUsersByRole(role);
    } catch (e) {
      return [];
    }
  }

  /// تحديث آخر نشاط للمستخدم الحالي
  /// 
  /// يرجع true في حالة النجاح، false في حالة الفشل
  Future<bool> updateLastActivity() async {
    try {
      return await _usersApiService.updateLastActivity();
    } catch (e) {
      return false;
    }
  }

  /// الحصول على إحصائيات مستخدم محدد
  /// 
  /// [userId] معرف المستخدم
  /// يرجع خريطة بإحصائيات المستخدم
  Future<Map<String, dynamic>> getUserStatistics(int userId) async {
    try {
      return await _usersApiService.getUserStatisticsById(userId);
    } catch (e) {
      return {};
    }
  }

  /// الحصول على إعدادات مستخدم محدد
  /// 
  /// [userId] معرف المستخدم
  /// يرجع خريطة بإعدادات المستخدم
  Future<Map<String, dynamic>> getUserSettings(int userId) async {
    try {
      return await _usersApiService.getUserSettings(userId);
    } catch (e) {
      return {};
    }
  }

  /// تحديث إعدادات مستخدم محدد
  /// 
  /// [userId] معرف المستخدم
  /// [settings] الإعدادات الجديدة
  /// يرجع true في حالة النجاح، false في حالة الفشل
  Future<bool> updateUserSettings(int userId, Map<String, dynamic> settings) async {
    try {
      return await _usersApiService.updateUserSettings(userId, settings);
    } catch (e) {
      return false;
    }
  }
}
