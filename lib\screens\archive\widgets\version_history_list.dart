import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';

/// نموذج مؤقت لإصدار الوثيقة
class ArchiveDocumentVersion {
  final String id;
  final String documentId;
  final int versionNumber;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String? changeNotes;
  final String creatorId;
  final DateTime createdAt;

  ArchiveDocumentVersion({
    required this.id,
    required this.documentId,
    required this.versionNumber,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    this.changeNotes,
    required this.creatorId,
    required this.createdAt,
  });
}

/// قائمة تاريخ إصدارات الوثيقة
class VersionHistoryList extends StatelessWidget {
  /// معرف الوثيقة
  final String documentId;

  /// دالة يتم استدعاؤها عند النقر على إصدار
  final Function(ArchiveDocumentVersion)? onVersionTap;

  /// دالة يتم استدعاؤها عند استعادة إصدار
  final Function(ArchiveDocumentVersion)? onVersionRestore;

  /// دالة يتم استدعاؤها عند حذف إصدار
  final Function(ArchiveDocumentVersion)? onVersionDelete;

  /// دالة يتم استدعاؤها عند تنزيل إصدار
  final Function(ArchiveDocumentVersion)? onVersionDownload;

  /// عدد الإصدارات المعروضة (إذا كانت القيمة null، يتم عرض جميع الإصدارات)
  final int? limit;

  const VersionHistoryList({
    super.key,
    required this.documentId,
    this.onVersionTap,
    this.onVersionRestore,
    this.onVersionDelete,
    this.onVersionDownload,
    this.limit,
  });

  @override
  Widget build(BuildContext context) {
    // بيانات تجريبية للإصدارات
    final List<ArchiveDocumentVersion> versions = [
      ArchiveDocumentVersion(
        id: '1',
        documentId: documentId,
        versionNumber: 1,
        fileName: 'document_v1.pdf',
        fileType: 'pdf',
        fileSize: 1024000,
        changeNotes: 'الإصدار الأولي',
        creatorId: '1',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      ArchiveDocumentVersion(
        id: '2',
        documentId: documentId,
        versionNumber: 2,
        fileName: 'document_v2.pdf',
        fileType: 'pdf',
        fileSize: 1124000,
        changeNotes: 'تحديث المحتوى',
        creatorId: '1',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ];

    if (versions.isEmpty) {
      return _buildEmptyState();
    }

    // تحديد الإصدارات التي سيتم عرضها
    final displayVersions = limit != null && versions.length > limit!
        ? versions.sublist(0, limit!)
        : versions;

    final showViewMoreButton = limit != null && versions.length > limit!;

    return Column(
      children: [
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: displayVersions.length,
          separatorBuilder: (context, index) => const Divider(),
          itemBuilder: (context, index) {
            final version = displayVersions[index];
            return _buildVersionListItem(version, context);
          },
        ),
        if (showViewMoreButton)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Center(
              child: TextButton(
                onPressed: () {
                  // يمكن إضافة منطق لعرض المزيد من الإصدارات
                },
                child: const Text('عرض المزيد من الإصدارات'),
              ),
            ),
          ),
      ],
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إصدارات سابقة',
            style: AppStyles.subtitle1.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 8),
          Text(
            'هذا هو الإصدار الأول من الوثيقة',
            style: AppStyles.body2.copyWith(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إصدار
  Widget _buildVersionListItem(
    ArchiveDocumentVersion version,
    BuildContext context,
  ) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withAlpha(25),
          child: Text(
            'v${version.versionNumber}',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          version.fileName,
          style: AppStyles.subtitle1,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (version.changeNotes != null && version.changeNotes!.isNotEmpty)
              Text(
                version.changeNotes!,
                style: AppStyles.body2,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  '${version.createdAt.year}/${version.createdAt.month}/${version.createdAt.day}',
                  style: AppStyles.caption,
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.file_present,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatFileSize(version.fileSize),
                  style: AppStyles.caption,
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) => _handleVersionAction(action, version),
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 16),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'download',
              child: Row(
                children: [
                  Icon(Icons.download, size: 16),
                  SizedBox(width: 8),
                  Text('تنزيل'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'restore',
              child: Row(
                children: [
                  Icon(Icons.restore, size: 16),
                  SizedBox(width: 8),
                  Text('استعادة'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => onVersionTap?.call(version),
      ),
    );
  }

  /// التعامل مع إجراءات الإصدار
  void _handleVersionAction(String action, ArchiveDocumentVersion version) {
    switch (action) {
      case 'view':
        onVersionTap?.call(version);
        break;
      case 'download':
        onVersionDownload?.call(version);
        break;
      case 'restore':
        onVersionRestore?.call(version);
        break;
      case 'delete':
        onVersionDelete?.call(version);
        break;
    }
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
