
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/archive_documents_controller.dart';
import '../../controllers/archive_categories_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/archive_models.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/date_formatter.dart';
import '../../utils/file_processor.dart';
import '../widgets/common/loading_indicator.dart';
import '../../services/unified_permission_service.dart';

import 'widgets/document_metadata_card.dart';
import 'widgets/document_tag_chip.dart';
import 'edit_document_screen.dart';

/// شاشة عرض تفاصيل وثيقة الأرشيف
class DocumentDetailScreen extends StatefulWidget {
  /// وثيقة الأرشيف المراد عرضها
  final ArchiveDocument document;

  const DocumentDetailScreen({
    super.key,
    required this.document,
  });

  @override
  State<DocumentDetailScreen> createState() => _DocumentDetailScreenState();
}

class _DocumentDetailScreenState extends State<DocumentDetailScreen> with SingleTickerProviderStateMixin {
  final ArchiveDocumentsController _documentsController = Get.find<ArchiveDocumentsController>();
  final ArchiveCategoriesController _categoriesController = Get.find<ArchiveCategoriesController>();
  final UserController _userController = Get.find<UserController>();
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  late TabController _tabController;
  late ArchiveDocument _document;

  bool _isLoading = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  ArchiveCategory? _category;
  List<ArchiveTag> _tags = [];
  String _creatorName = '';

  @override
  void initState() {
    super.initState();
    _document = widget.document;
    _tabController = TabController(length: 3, vsync: this);
    _loadDocumentDetails();
    _loadDocumentVersions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDocumentDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل التصنيف
      if (_document.categoryId != null) {
        await _categoriesController.getCategoryById(_document.categoryId!);
        _category = _categoriesController.currentCategory;
      }

      // تحميل الوسوم (إذا كانت الوثيقة تحتوي على وسوم)
      if (_document.tags != null && _document.tags!.isNotEmpty) {
        setState(() {
          _tags = _document.tags!;
        });
      }

      // تحميل اسم المنشئ
      final creator = _userController.getUserById(_document.createdBy);
      if (creator != null) {
        setState(() {
          _creatorName = creator.name;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل الوثيقة: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل إصدارات الوثيقة
  Future<void> _loadDocumentVersions() async {
    try {
      // يمكن إضافة تحميل إصدارات الوثيقة هنا إذا لزم الأمر
      debugPrint('تحميل إصدارات الوثيقة: ${_document.id}');
    } catch (e) {
      debugPrint('خطأ في تحميل إصدارات الوثيقة: $e');
    }
  }

  Future<void> _downloadDocument() async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      // محاكاة عملية التنزيل
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        setState(() {
          _downloadProgress = i / 100;
        });
      }

      Get.snackbar(
        'تم التنزيل',
        'تم تنزيل الملف بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.primary.withAlpha(178),
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في تنزيل الوثيقة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تنزيل الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isDownloading = false;
        _downloadProgress = 0.0;
      });
    }
  }

  /// فتح الملف داخل التطبيق
  void _openFileInApp() {
    Get.snackbar(
      'معلومات',
      'سيتم فتح الملف: ${_document.fileName}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.primary.withAlpha(178),
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// تعديل الوثيقة
  Future<void> _editDocument() async {
    final result = await Get.to(() => EditDocumentScreen(document: _document));

    if (result == true) {
      // إعادة تحميل الوثيقة بعد التعديل
      await _loadDocumentDetails();
      // إعادة تحميل إصدارات الوثيقة
      await _loadDocumentVersions();
    }
  }

  Future<void> _deleteDocument() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الوثيقة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await _documentsController.deleteDocument(_document.id);
      if (success) {
        Get.back(); // العودة إلى الشاشة السابقة
        Get.snackbar(
          'تم الحذف',
          'تم حذف الوثيقة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.primary.withAlpha(178),
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف الوثيقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_document.title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // زر التنزيل
          IconButton(
            icon: _isDownloading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.download),
            tooltip: 'تنزيل',
            onPressed: _isDownloading ? null : _downloadDocument,
          ),
          // زر التحرير
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'تحرير',
            onPressed: _editDocument,
          ),
          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'حذف',
            onPressed: _deleteDocument,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _buildBody(context, isSmallScreen),
    );
  }

  Widget _buildBody(BuildContext context, bool isSmallScreen) {
    if (isSmallScreen) {
      return Column(
        children: [
          // شريط التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'الملف', icon: Icon(Icons.description)),
              Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
              Tab(text: 'الإصدارات', icon: Icon(Icons.history)),
            ],
          ),
          // محتوى التبويب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFileViewer(),
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDocumentDetails(),
                ),
                _buildVersionsTab(),
              ],
            ),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          // شريط التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'الملف', icon: Icon(Icons.description)),
              Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
              Tab(text: 'الإصدارات', icon: Icon(Icons.history)),
            ],
          ),
          // محتوى التبويب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFileViewer(),
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDocumentDetails(),
                ),
                _buildVersionsTab(),
              ],
            ),
          ),
        ],
      );
    }
  }

  Widget _buildFileViewer() {
    final fileType = FileProcessor.getFileType(_document.filePath);
    final canViewInApp = false; // تبسيط - لا يوجد دعم للعرض داخل التطبيق حالياً

    // استخدام عارض الملفات المناسب حسب نوع الملف
    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getFileIcon(fileType),
              size: 64,
              color: _getFileColor(fileType),
            ),
            const SizedBox(height: 16),
            Text(
              _document.fileName,
              style: AppStyles.subtitle1,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              FileProcessor.formatFileSize(_document.fileSize),
              style: AppStyles.body2,
            ),
            const SizedBox(height: 24),
            // أزرار العمليات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر فتح الملف
                ElevatedButton.icon(
                  onPressed: _openFileInApp,
                  icon: const Icon(Icons.visibility),
                  label: const Text('فتح الملف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.buttonSecondary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),
                const SizedBox(width: 16),
                // زر تنزيل الملف
                if (_permissionService.canDownloadFiles())
                  ElevatedButton.icon(
                    onPressed: _isDownloading ? null : _downloadDocument,
                    icon: _isDownloading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.download),
                    label: Text(_isDownloading ? 'جاري التنزيل...' : 'تنزيل الملف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
              ],
            ),
            if (_isDownloading) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: _downloadProgress,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(_downloadProgress * 100).toStringAsFixed(0)}%',
                style: AppStyles.body2,
              ),
            ],
            // معلومات حول دعم العرض داخل التطبيق
            if (!canViewInApp) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(51), // 0.2 * 255 = 51
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.info_outline, color: Colors.amber, size: 20),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        'هذا النوع من الملفات سيتم فتحه باستخدام التطبيق الافتراضي',
                        style: AppStyles.body2.copyWith(color: Colors.amber.shade900),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // معلومات الوثيقة الأساسية
        DocumentMetadataCard(
          title: 'معلومات الوثيقة',
          icon: Icons.description,
          items: [
            {'العنوان': _document.title},
            {'الوصف': _document.description ?? 'لا يوجد وصف'},
            {'التصنيف': _category?.name ?? 'غير محدد'},
            {'اسم الملف': _document.fileName},
            {'حجم الملف': FileProcessor.formatFileSize(_document.fileSize)},
          ],
        ),

        const SizedBox(height: 16),

        // معلومات الملف
        DocumentMetadataCard(
          title: 'معلومات الملف',
          icon: Icons.insert_drive_file,
          items: [
            {'اسم الملف': _document.fileName},
            {'نوع الملف': _document.fileType},
            {'حجم الملف': FileProcessor.formatFileSize(_document.fileSize)},
          ],
        ),

        const SizedBox(height: 16),

        // معلومات إضافية
        DocumentMetadataCard(
          title: 'معلومات إضافية',
          icon: Icons.info,
          items: [
            {'تاريخ الإضافة': DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(_document.createdAt))},
            {'بواسطة': _creatorName.isNotEmpty ? _creatorName : 'غير محدد'},
            {'آخر تحديث': _document.updatedAt != null
                ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(_document.updatedAt!))
                : 'غير محدد'},
          ],
        ),

        const SizedBox(height: 16),

        // الوسوم
        if (_tags.isNotEmpty) ...[
          Text(
            'الوسوم',
            style: AppStyles.subtitle1,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _tags.map((tag) => DocumentTagChip(tag: tag)).toList(),
          ),
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }



  /// بناء علامة تبويب الإصدارات
  Widget _buildVersionsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تاريخ إصدارات الوثيقة',
                style: AppStyles.headline6,
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Get.snackbar(
                    'إصدارات الوثيقة',
                    'سيتم إضافة هذه الميزة قريباً',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                },
                icon: const Icon(Icons.history),
                label: const Text('عرض كل الإصدارات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.history,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'إصدارات الوثيقة',
                    style: AppStyles.subtitle1,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'سيتم عرض إصدارات الوثيقة هنا عند توفرها',
                    style: AppStyles.body2,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
