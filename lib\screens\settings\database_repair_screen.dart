import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/app_drawer.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/database_repair_service.dart';
import '../../services/unified_permission_service.dart';

/// شاشة إصلاح قاعدة البيانات
///
/// تتيح للمستخدم إصلاح مشاكل قاعدة البيانات
class DatabaseRepairScreen extends StatefulWidget {
  const DatabaseRepairScreen({Key? key}) : super(key: key);

  @override
  State<DatabaseRepairScreen> createState() => _DatabaseRepairScreenState();
}

class _DatabaseRepairScreenState extends State<DatabaseRepairScreen> {
  bool _isRepairing = false;
  String _repairStatus = '';
  bool _repairSuccess = false;

  // خدمة الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'إصلاح قاعدة البيانات',
        automaticallyImplyLeading: true,
      ),
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildInfoCard(),
            const SizedBox(height: 24),
            _buildRepairButton(),
            const SizedBox(height: 16),
            if (_repairStatus.isNotEmpty) _buildStatusMessage(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إصلاح قاعدة البيانات',
          style: AppStyles.titleLarge,
        ),
        const SizedBox(height: 8),
        Text(
          'استخدم هذه الأداة لإصلاح مشاكل قاعدة البيانات مثل الجداول المفقودة أو الأعمدة المفقودة.',
          style: AppStyles.bodyMedium,
        ),
      ],
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات',
                  style: AppStyles.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'تقوم هذه الأداة بإصلاح المشاكل التالية:',
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(height: 8),
            _buildInfoItem('إنشاء جدول sync_changes المفقود'),
            _buildInfoItem('إنشاء جدول sync_locks المفقود'),
            _buildInfoItem('إضافة حقل isRead المفقود في جدول messages'),
            _buildInfoItem('إصلاح جدول time_tracking_entries'),
            _buildInfoItem('إصلاح جدول power_bi_reports'),
            const SizedBox(height: 16),
            Text(
              'ملاحظة: قد يستغرق الإصلاح بضع ثوانٍ. يرجى الانتظار حتى اكتمال العملية.',
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Text(
              text,
              style: AppStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإصلاح
  Widget _buildRepairButton() {
    return Center(
      child: ElevatedButton.icon(
        onPressed: (_isRepairing || !_permissionService.canAccessDatabaseRepair()) ? null : _repairDatabase,
        icon: _isRepairing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.build),
        label: Text(_isRepairing ? 'جاري الإصلاح...' : 'إصلاح قاعدة البيانات'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          minimumSize: const Size(200, 48),
        ),
      ),
    );
  }

  /// بناء رسالة الحالة
  Widget _buildStatusMessage() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _repairSuccess ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _repairSuccess ? Colors.green.shade300 : Colors.red.shade300,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _repairSuccess ? Icons.check_circle : Icons.error,
            color: _repairSuccess ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _repairStatus,
              style: AppStyles.bodyMedium.copyWith(
                color: _repairSuccess ? Colors.green.shade800 : Colors.red.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إصلاح قاعدة البيانات
  Future<void> _repairDatabase() async {
    setState(() {
      _isRepairing = true;
      _repairStatus = '';
    });

    try {
      final databaseRepair = DatabaseRepair();
      final result = await databaseRepair.repairDatabase();

      setState(() {
        _isRepairing = false;
        _repairSuccess = result;
        _repairStatus = result
            ? 'تم إصلاح قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.'
            : 'فشل في إصلاح قاعدة البيانات. يرجى المحاولة مرة أخرى.';
      });

      if (result) {
        Get.snackbar(
          'تم الإصلاح',
          'تم إصلاح قاعدة البيانات بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 3),
        );
      } else {
        Get.snackbar(
          'فشل الإصلاح',
          'فشل في إصلاح قاعدة البيانات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      setState(() {
        _isRepairing = false;
        _repairSuccess = false;
        _repairStatus = 'حدث خطأ أثناء إصلاح قاعدة البيانات: $e';
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إصلاح قاعدة البيانات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
