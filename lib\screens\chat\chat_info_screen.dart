import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/chat_group_models.dart';
import '../../models/group_member_models.dart';
import '../../services/api/group_members_api_service.dart';
import '../../services/api/chat_groups_api_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/app_colors.dart';
import 'add_members_screen.dart';

/// شاشة معلومات المحادثة
class ChatInfoScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const ChatInfoScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<ChatInfoScreen> createState() => _ChatInfoScreenState();
}

class _ChatInfoScreenState extends State<ChatInfoScreen> {
  final GroupMembersApiService _groupMembersApiService = GroupMembersApiService();
  final ChatGroupsApiService _chatGroupsApiService = ChatGroupsApiService();
  final AuthController _authController = Get.find<AuthController>();

  final RxList<GroupMember> _members = <GroupMember>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isUpdating = false.obs;
  final Rx<ChatGroup> _chatGroup = Rx<ChatGroup>(ChatGroup(
    id: 0,
    name: '',
    createdBy: 0,
    createdAt: 0,
  ));

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _chatGroup.value = widget.chatGroup;
    _nameController.text = widget.chatGroup.name;
    _descriptionController.text = widget.chatGroup.description ?? '';
    _loadMembers();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// تحميل أعضاء المجموعة
  Future<void> _loadMembers() async {
    _isLoading.value = true;
    try {
      final members = await _groupMembersApiService.getMembersByGroup(widget.chatGroup.id);
      _members.value = members;
    } catch (e) {
      _showErrorSnackbar('خطأ في تحميل الأعضاء: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث معلومات المجموعة
  Future<void> _updateGroupInfo() async {
    if (_nameController.text.trim().isEmpty) {
      _showWarningSnackbar('يرجى إدخال اسم المجموعة');
      return;
    }

    _isUpdating.value = true;
    try {
      final updatedGroup = _chatGroup.value.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      final result = await _chatGroupsApiService.updateGroup(updatedGroup);

      if (result != null) {
        _chatGroup.value = result;
        _showSuccessSnackbar('تم تحديث معلومات المجموعة بنجاح');
        Get.back(result: result);
      } else {
        _showErrorSnackbar('فشل في تحديث معلومات المجموعة');
      }
    } catch (e) {
      _showErrorSnackbar('خطأ في تحديث المجموعة: $e');
    } finally {
      _isUpdating.value = false;
    }
  }

  /// إزالة عضو من المجموعة
  Future<void> _removeMember(GroupMember member) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الإزالة'),
        content: Text('هل تريد إزالة ${member.user?.name ?? 'هذا العضو'} من المجموعة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _groupMembersApiService.removeMemberFromGroup(member.id);
        _members.removeWhere((m) => m.id == member.id);
        _showSuccessSnackbar('تم إزالة العضو بنجاح');
      } catch (e) {
        _showErrorSnackbar('خطأ في إزالة العضو: $e');
      }
    }
  }

  /// تغيير دور العضو
  Future<void> _changeRole(GroupMember member, String newRole) async {
    try {
      await _groupMembersApiService.updateMemberRole(member.id, newRole);

      // تحديث القائمة المحلية
      final index = _members.indexWhere((m) => m.id == member.id);
      if (index != -1) {
        final roleValue = _getRoleValue(newRole);
        _members[index] = member.copyWith(role: roleValue);
      }

      _showSuccessSnackbar('تم تغيير دور العضو بنجاح');
    } catch (e) {
      _showErrorSnackbar('خطأ في تغيير دور العضو: $e');
    }
  }

  /// الحصول على قيمة الدور
  int _getRoleValue(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return 3;
      case 'moderator':
        return 2;
      case 'member':
      default:
        return 1;
    }
  }

  /// الحصول على اسم الدور
  String _getRoleName(int role) {
    switch (role) {
      case 3:
        return 'مدير';
      case 2:
        return 'مشرف';
      case 1:
      default:
        return 'عضو';
    }
  }

  /// التحقق من صلاحيات المستخدم الحالي
  bool _canManageMembers() {
    final currentUserId = _authController.currentUser.value?.id;
    if (currentUserId == null) return false;

    final currentMember = _members.firstWhereOrNull(
      (member) => member.userId == currentUserId,
    );

    return currentMember?.role == 3 || // مدير
           _chatGroup.value.createdBy == currentUserId; // منشئ المجموعة
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      icon: const Icon(Icons.check_circle, color: Colors.green),
    );
  }

  /// عرض رسالة تحذير
  void _showWarningSnackbar(String message) {
    Get.snackbar(
      'تنبيه',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.shade100,
      colorText: Colors.orange.shade800,
      icon: const Icon(Icons.warning, color: Colors.orange),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      icon: const Icon(Icons.error, color: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('معلومات المحادثة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_canManageMembers())
            IconButton(
              onPressed: () async {
                final result = await Get.to(() => AddMembersScreen(chatGroup: _chatGroup.value));
                if (result == true) {
                  _loadMembers(); // إعادة تحميل الأعضاء
                }
              },
              icon: const Icon(Icons.person_add),
              tooltip: 'إضافة أعضاء',
            ),
        ],
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل معلومات المجموعة...'),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المجموعة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'معلومات المجموعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // اسم المجموعة
                      TextField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المجموعة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.group),
                        ),
                        enabled: _canManageMembers(),
                      ),
                      const SizedBox(height: 16),

                      // وصف المجموعة
                      TextField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف المجموعة (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.description),
                        ),
                        maxLines: 3,
                        enabled: _canManageMembers(),
                      ),

                      if (_canManageMembers()) ...[
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isUpdating.value ? null : _updateGroupInfo,
                            icon: _isUpdating.value
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.save),
                            label: const Text('حفظ التغييرات'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // إحصائيات المجموعة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.analytics_outlined,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'إحصائيات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              'الأعضاء',
                              '${_members.length}',
                              Icons.people,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildStatCard(
                              'النوع',
                              _chatGroup.value.isDirectMessage ? 'محادثة مباشرة' : 'مجموعة',
                              _chatGroup.value.isDirectMessage ? Icons.person : Icons.group,
                              Colors.green,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              'تاريخ الإنشاء',
                              _formatDate(_chatGroup.value.createdAt),
                              Icons.calendar_today,
                              Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildStatCard(
                              'الخصوصية',
                              _chatGroup.value.isPrivate ? 'خاصة' : 'عامة',
                              _chatGroup.value.isPrivate ? Icons.lock : Icons.public,
                              Colors.purple,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // قائمة الأعضاء
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.people_outline,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'الأعضاء (${_members.length})',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      if (_members.isEmpty)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(32),
                            child: Text(
                              'لا توجد أعضاء في هذه المجموعة',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ),
                        )
                      else
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _members.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final member = _members[index];
                            final user = member.user;
                            final isCurrentUser = user?.id == _authController.currentUser.value?.id;
                            final canManage = _canManageMembers() && !isCurrentUser;

                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: AppColors.primary,
                                backgroundImage: user?.profileImage != null
                                    ? NetworkImage(user!.profileImage!)
                                    : null,
                                child: user?.profileImage == null
                                    ? Text(
                                        user?.name.substring(0, 1).toUpperCase() ?? '?',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                              ),
                              title: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      user?.name ?? 'مستخدم غير معروف',
                                      style: TextStyle(
                                        fontWeight: isCurrentUser
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                  if (isCurrentUser)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade100,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        'أنت',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue.shade800,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (user?.email != null)
                                    Text(user!.email!),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _getRoleColor(member.role),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          _getRoleName(member.role),
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'انضم ${_formatDate(member.joinedAt)}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              trailing: canManage
                                  ? PopupMenuButton<String>(
                                      onSelected: (value) {
                                        switch (value) {
                                          case 'change_role':
                                            _showChangeRoleDialog(member);
                                            break;
                                          case 'remove':
                                            _removeMember(member);
                                            break;
                                        }
                                      },
                                      itemBuilder: (context) => [
                                        const PopupMenuItem(
                                          value: 'change_role',
                                          child: Row(
                                            children: [
                                              Icon(Icons.admin_panel_settings),
                                              SizedBox(width: 8),
                                              Text('تغيير الدور'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'remove',
                                          child: Row(
                                            children: [
                                              Icon(Icons.remove_circle, color: Colors.red),
                                              SizedBox(width: 8),
                                              Text('إزالة من المجموعة'),
                                            ],
                                          ),
                                        ),
                                      ],
                                    )
                                  : null,
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else {
      return 'الآن';
    }
  }

  /// الحصول على لون الدور
  Color _getRoleColor(int role) {
    switch (role) {
      case 3:
        return Colors.red; // مدير
      case 2:
        return Colors.orange; // مشرف
      case 1:
      default:
        return Colors.blue; // عضو
    }
  }

  /// عرض حوار تغيير الدور
  void _showChangeRoleDialog(GroupMember member) {
    Get.dialog(
      AlertDialog(
        title: Text('تغيير دور ${member.user?.name ?? 'العضو'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('عضو'),
              leading: const Icon(Icons.person, color: Colors.blue),
              onTap: () {
                Get.back();
                _changeRole(member, 'member');
              },
            ),
            ListTile(
              title: const Text('مشرف'),
              leading: const Icon(Icons.supervisor_account, color: Colors.orange),
              onTap: () {
                Get.back();
                _changeRole(member, 'moderator');
              },
            ),
            ListTile(
              title: const Text('مدير'),
              leading: const Icon(Icons.admin_panel_settings, color: Colors.red),
              onTap: () {
                Get.back();
                _changeRole(member, 'admin');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
