import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_categories_controller.dart';
import 'package:flutter_application_2/models/archive_models.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/screens/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';

/// شاشة إدارة تصنيفات الأرشيف مبسطة
class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen> {
  final ArchiveCategoriesController _controller = Get.find<ArchiveCategoriesController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // حالة التحميل
  bool _isLoading = false;
  bool _isSaving = false;

  // حالة التحرير
  bool _isEditing = false;
  ArchiveCategory? _selectedCategory;

  // وحدات التحكم بالنصوص
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// تحميل التصنيفات
  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _controller.loadAllCategories();
    } catch (e) {
      debugPrint('خطأ في تحميل التصنيفات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// اختيار تصنيف للتحرير
  void _selectCategory(ArchiveCategory category) {
    setState(() {
      _selectedCategory = category;
      _isEditing = true;
      _nameController.text = category.name;
      _descriptionController.text = category.description ?? '';
    });
  }

  /// إلغاء التحرير
  void _cancelEditing() {
    setState(() {
      _isEditing = false;
      _selectedCategory = null;
      _nameController.clear();
      _descriptionController.clear();
    });
  }

  /// حفظ التصنيف
  Future<void> _saveCategory() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });

      try {
        if (_isEditing && _selectedCategory != null) {
          // تحديث تصنيف موجود
          final updatedCategory = ArchiveCategory(
            id: _selectedCategory!.id,
            name: _nameController.text,
            description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
            parentId: _selectedCategory!.parentId,
            color: _selectedCategory!.color,
            icon: _selectedCategory!.icon,
            createdBy: _selectedCategory!.createdBy,
            createdAt: _selectedCategory!.createdAt,
            isActive: _selectedCategory!.isActive,
          );

          await _controller.updateCategory(updatedCategory);
        } else {
          // إنشاء تصنيف جديد
          final newCategory = ArchiveCategory(
            id: 0, // سيتم تعيينه من قبل الخادم
            name: _nameController.text,
            description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
            parentId: null,
            color: '#3498db',
            icon: 'folder',
            createdBy: 1, // معرف المستخدم الحالي
            createdAt: DateTime.now().millisecondsSinceEpoch,
            isActive: true,
          );

          await _controller.createCategory(newCategory);
        }

        // إعادة تحميل التصنيفات
        await _loadCategories();

        // إلغاء التحرير
        _cancelEditing();

        Get.snackbar(
          'تم بنجاح',
          _isEditing ? 'تم تحديث التصنيف بنجاح' : 'تم إنشاء التصنيف بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        debugPrint('خطأ في حفظ التصنيف: $e');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حفظ التصنيف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// حذف تصنيف
  Future<void> _deleteCategory(ArchiveCategory category) async {
    // تبسيط - لا نتحقق من الاستخدام حالياً
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التصنيف "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              try {
                await _controller.deleteCategory(category.id);
                await _loadCategories();
                Get.snackbar(
                  'تم الحذف',
                  'تم حذف التصنيف بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } catch (e) {
                Get.snackbar(
                  'خطأ',
                  'حدث خطأ أثناء حذف التصنيف',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إدارة التصنيفات',
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : isSmallScreen
              ? _buildMobileLayout()
              : _buildDesktopLayout(),
    );
  }

  /// تخطيط الجوال
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // نموذج إضافة/تحرير
        _buildCategoryForm(),
        const Divider(),
        // قائمة التصنيفات
        Expanded(child: _buildCategoriesList()),
      ],
    );
  }

  /// تخطيط الحاسوب
  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // نموذج إضافة/تحرير
        Expanded(flex: 1, child: _buildCategoryForm()),
        const VerticalDivider(),
        // قائمة التصنيفات
        Expanded(flex: 2, child: _buildCategoriesList()),
      ],
    );
  }

  /// نموذج إضافة/تحرير التصنيف
  Widget _buildCategoryForm() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _isEditing ? 'تحرير التصنيف' : 'إضافة تصنيف جديد',
                style: AppStyles.headline6,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم التصنيف',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم التصنيف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف التصنيف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (_isEditing)
                    TextButton(
                      onPressed: _cancelEditing,
                      child: const Text('إلغاء'),
                    ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _isSaving ? null : _saveCategory,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: _isSaving
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(_isEditing ? 'تحديث' : 'إضافة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// قائمة التصنيفات
  Widget _buildCategoriesList() {
    return Obx(() {
      if (_controller.allCategories.isEmpty) {
        return const Center(
          child: Text('لا توجد تصنيفات'),
        );
      }

      return ListView.builder(
        itemCount: _controller.allCategories.length,
        itemBuilder: (context, index) {
          final category = _controller.allCategories[index];
          return ListTile(
            title: Text(category.name),
            subtitle: category.description != null
                ? Text(
                    category.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            leading: const Icon(Icons.folder),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _selectCategory(category),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _deleteCategory(category),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}
