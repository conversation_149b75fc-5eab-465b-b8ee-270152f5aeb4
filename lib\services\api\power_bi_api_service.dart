import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../models/power_bi_models.dart';
import 'base_api_service.dart';

/// خدمة API لـ Power BI
class PowerBIApiService extends BaseApiService {
  PowerBIApiService() : super();

  /// الحصول على جميع التقارير للمستخدم الحالي
  Future<List<PowerBIReport>> getMyReports() async {
    try {
      final response = await get('/api/PowerBI/my-reports');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => PowerBIReport.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل تقاريري: $e');
      return [];
    }
  }

  /// الحصول على التقارير المشتركة
  Future<List<PowerBIReport>> getSharedReports() async {
    try {
      final response = await get('/api/PowerBI/shared-reports');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => PowerBIReport.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل التقارير المشتركة: $e');
      return [];
    }
  }

  /// الحصول على تقرير محدد
  Future<PowerBIReport?> getReport(String reportId) async {
    try {
      final response = await get('/api/PowerBI/$reportId');
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return PowerBIReport.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل التقرير: $e');
      return null;
    }
  }

  /// إنشاء تقرير جديد
  Future<PowerBIReport?> createReport(PowerBIReport report) async {
    try {
      final response = await post('/api/PowerBI', report.toJson());
      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return PowerBIReport.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      return null;
    }
  }

  /// تحديث تقرير
  Future<PowerBIReport?> updateReport(PowerBIReport report) async {
    try {
      final response = await put('/api/PowerBI/${report.id}', report.toJson());
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return PowerBIReport.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحديث التقرير: $e');
      return null;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      final response = await delete('/api/PowerBI/$reportId');
      return response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    }
  }

  /// الحصول على الجداول المتاحة
  Future<List<String>> getAvailableTables() async {
    try {
      final response = await get('/api/PowerBI/tables');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((table) => table.toString()).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل الجداول: $e');
      return [];
    }
  }

  /// الحصول على أعمدة جدول محدد
  Future<List<Map<String, dynamic>>> getTableColumns(String tableName) async {
    try {
      final response = await get('/api/PowerBI/tables/$tableName/columns');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((column) => Map<String, dynamic>.from(column)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل أعمدة الجدول: $e');
      return [];
    }
  }

  /// الحصول على بيانات الرسم البياني
  Future<Map<String, dynamic>> getChartData(PowerBIReport report) async {
    try {
      final response = await post('/api/PowerBI/chart-data', report.toJson());
      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return {};
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الرسم البياني: $e');
      return {};
    }
  }

  /// اقتراح علاقات بين الجداول
  Future<List<Map<String, String>>> suggestTableRelations(String table1, String table2) async {
    try {
      final response = await get('/api/PowerBI/suggest-relations', queryParams: {
        'table1': table1,
        'table2': table2,
      });
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((relation) => Map<String, String>.from(relation)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في اقتراح العلاقات: $e');
      return [];
    }
  }

  /// تنفيذ استعلام SQL مخصص
  Future<Map<String, dynamic>> executeCustomQuery(String query) async {
    try {
      final response = await post('/api/PowerBI/custom-query', {'query': query});
      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return {};
    } catch (e) {
      debugPrint('خطأ في تنفيذ الاستعلام: $e');
      return {};
    }
  }

  /// مشاركة تقرير مع مستخدم
  Future<bool> shareReport(String reportId, String userEmail) async {
    try {
      final response = await post('/api/PowerBI/share', {
        'reportId': reportId,
        'userEmail': userEmail,
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في مشاركة التقرير: $e');
      return false;
    }
  }

  /// إلغاء مشاركة تقرير
  Future<bool> unshareReport(String reportId, String userId) async {
    try {
      final response = await delete('/api/PowerBI/share/$reportId/$userId');
      return response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في إلغاء مشاركة التقرير: $e');
      return false;
    }
  }

  /// تصدير تقرير إلى PDF
  Future<String?> exportToPdf(String reportId) async {
    try {
      final response = await get('/api/PowerBI/export/$reportId/pdf');
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['filePath'];
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير PDF: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى Excel
  Future<String?> exportToExcel(String reportId) async {
    try {
      final response = await get('/api/PowerBI/export/$reportId/excel');
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['filePath'];
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير Excel: $e');
      return null;
    }
  }
}
