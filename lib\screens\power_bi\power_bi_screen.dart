import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../routes/app_routes.dart';
import '../../models/power_bi_models.dart';
import '../../controllers/power_bi_controller.dart';
import '../../services/unified_permission_service.dart';
import '../widgets/app_drawer.dart';
import 'create_power_bi_report_screen.dart';
import 'power_bi_report_details_screen.dart';

/// شاشة باور بي آي الرئيسية
///
/// توفر واجهة لعرض وإدارة تقارير باور بي آي
class PowerBIScreen extends StatefulWidget {
  const PowerBIScreen({super.key});

  @override
  State<PowerBIScreen> createState() => _PowerBIScreenState();
}

class _PowerBIScreenState extends State<PowerBIScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final PowerBIController _powerBIController = Get.put(PowerBIController());
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _powerBIController.loadMyReports();
    await _powerBIController.loadSharedReports();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AppDrawer(),
      appBar: AppBar(
        title: const Text('باور بي آي'),
        bottom: _permissionService.canAccessPowerBI()
            ? TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'تقاريري'),
                  Tab(text: 'التقارير المشتركة'),
                ],
              )
            : null,
        actions: [
          if (_permissionService.canAccessDynamicReports())
            IconButton(
              icon: const Icon(Icons.analytics),
              tooltip: 'تقارير ديناميكية',
              onPressed: () {
                Get.toNamed(AppRoutes.dynamicPowerBI);
              },
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadData,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMyReportsTab(),
          _buildSharedReportsTab(),
        ],
      ),
      floatingActionButton: _permissionService.canCreatePowerBIReports()
          ? FloatingActionButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CreatePowerBIReportScreen(),
                  ),
                ).then((_) => _loadData());
              },
              tooltip: 'إنشاء تقرير جديد',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  /// بناء تبويب تقاريري
  Widget _buildMyReportsTab() {
    return Obx(() {
      if (_powerBIController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_powerBIController.errorMessage.value.isNotEmpty) {
        return Center(
          child: Text(
            _powerBIController.errorMessage.value,
            style: const TextStyle(color: Colors.red),
          ),
        );
      }

      if (_powerBIController.myReports.isEmpty) {
        return const Center(
          child: Text('لا توجد تقارير. قم بإنشاء تقرير جديد.'),
        );
      }

      return ListView.builder(
        itemCount: _powerBIController.myReports.length,
        itemBuilder: (context, index) {
          final report = _powerBIController.myReports[index];
          return _buildReportCard(report);
        },
      );
    });
  }

  /// بناء تبويب التقارير المشتركة
  Widget _buildSharedReportsTab() {
    return Obx(() {
      if (_powerBIController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_powerBIController.errorMessage.value.isNotEmpty) {
        return Center(
          child: Text(
            _powerBIController.errorMessage.value,
            style: const TextStyle(color: Colors.red),
          ),
        );
      }

      if (_powerBIController.sharedReports.isEmpty) {
        return const Center(
          child: Text('لا توجد تقارير مشتركة معك.'),
        );
      }

      return ListView.builder(
        itemCount: _powerBIController.sharedReports.length,
        itemBuilder: (context, index) {
          final report = _powerBIController.sharedReports[index];
          return _buildReportCard(report, isShared: true);
        },
      );
    });
  }

  /// بناء بطاقة تقرير
  Widget _buildReportCard(PowerBIReport report, {bool isShared = false}) {
    // التحقق من حجم الشاشة للتصميم المتجاوب
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PowerBIReportDetailsScreen(reportId: report.id),
            ),
          ).then((_) => _loadData());
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان التقرير والأيقونات
            isSmallScreen
                ? _buildSmallScreenHeader(report, isShared)
                : _buildLargeScreenHeader(report, isShared),

            // تفاصيل التقرير
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Wrap(
                spacing: 16,
                runSpacing: 8,
                children: [
                  _buildInfoChip(
                    Icons.insert_chart,
                    'نوع الرسم البياني: ${powerBIChartTypeToString(report.chartType)}',
                    Colors.blue,
                  ),
                  _buildInfoChip(
                    Icons.table_chart,
                    'الجدول: ${report.tableName}',
                    Colors.green,
                  ),
                  _buildInfoChip(
                    Icons.calendar_today,
                    'تاريخ الإنشاء: ${_formatDate(report.createdAt)}',
                    Colors.orange,
                  ),
                  if (report.updatedAt != null)
                    _buildInfoChip(
                      Icons.update,
                      'آخر تحديث: ${_formatDate(report.updatedAt!)}',
                      Colors.purple,
                    ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  /// بناء رأس البطاقة للشاشات الصغيرة
  Widget _buildSmallScreenHeader(PowerBIReport report, bool isShared) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          title: Text(
            report.title,
            style: AppStyles.titleMedium,
          ),
          subtitle: Text(
            report.description ?? 'لا يوجد وصف',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          leading: _buildChartTypeIcon(report.chartType),
        ),
        if (!isShared)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (_permissionService.canSharePowerBIReports())
                  IconButton(
                    icon: const Icon(Icons.share, size: 20),
                    tooltip: 'مشاركة',
                    onPressed: () => _showShareReportDialog(report),
                  ),
                if (_permissionService.canEditPowerBIReports())
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    tooltip: 'تعديل',
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => CreatePowerBIReportScreen(report: report),
                        ),
                      ).then((_) => _loadData());
                    },
                  ),
                if (_permissionService.canDeletePowerBIReports())
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    tooltip: 'حذف',
                    onPressed: () => _showDeleteReportDialog(report),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء رأس البطاقة للشاشات الكبيرة
  Widget _buildLargeScreenHeader(PowerBIReport report, bool isShared) {
    return ListTile(
      title: Text(
        report.title,
        style: AppStyles.titleMedium,
      ),
      subtitle: Text(
        report.description ?? 'لا يوجد وصف',
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      leading: _buildChartTypeIcon(report.chartType),
      trailing: !isShared
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.share),
                  tooltip: 'مشاركة',
                  onPressed: () => _showShareReportDialog(report),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: 'تعديل',
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => CreatePowerBIReportScreen(report: report),
                      ),
                    ).then((_) => _loadData());
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  tooltip: 'حذف',
                  onPressed: () => _showDeleteReportDialog(report),
                ),
              ],
            )
          : null,
    );
  }

  /// بناء رقاقة معلومات
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    // استخدام ألوان محددة مسبقًا
    Color lightColor;
    Color borderColor;

    // تحديد الألوان بناءً على اللون الرئيسي
    if (color == Colors.blue) {
      lightColor = Colors.blue.shade50;
      borderColor = Colors.blue.shade200;
    } else if (color == Colors.green) {
      lightColor = Colors.green.shade50;
      borderColor = Colors.green.shade200;
    } else if (color == Colors.orange) {
      lightColor = Colors.orange.shade50;
      borderColor = Colors.orange.shade200;
    } else if (color == Colors.purple) {
      lightColor = Colors.purple.shade50;
      borderColor = Colors.purple.shade200;
    } else if (color == Colors.red) {
      lightColor = Colors.red.shade50;
      borderColor = Colors.red.shade200;
    } else if (color == Colors.teal) {
      lightColor = Colors.teal.shade50;
      borderColor = Colors.teal.shade200;
    } else {
      lightColor = Colors.grey.shade50;
      borderColor = Colors.grey.shade200;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: lightColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أيقونة نوع الرسم البياني
  Widget _buildChartTypeIcon(PowerBIChartType chartType) {
    IconData iconData;
    Color iconColor;

    switch (chartType) {
      case PowerBIChartType.bar:
        iconData = Icons.bar_chart;
        iconColor = Colors.blue;
        break;
      case PowerBIChartType.line:
        iconData = Icons.show_chart;
        iconColor = Colors.green;
        break;
      case PowerBIChartType.pie:
        iconData = Icons.pie_chart;
        iconColor = Colors.orange;
        break;
      case PowerBIChartType.scatter:
        iconData = Icons.scatter_plot;
        iconColor = Colors.purple;
        break;
      case PowerBIChartType.bubble:
        iconData = Icons.bubble_chart;
        iconColor = Colors.teal;
        break;
      case PowerBIChartType.radar:
        iconData = Icons.radar;
        iconColor = Colors.indigo;
        break;
      case PowerBIChartType.table:
        iconData = Icons.table_chart;
        iconColor = Colors.brown;
        break;
      case PowerBIChartType.heatmap:
        iconData = Icons.grid_on;
        iconColor = Colors.red;
        break;
      case PowerBIChartType.treemap:
        iconData = Icons.account_tree;
        iconColor = Colors.deepPurple;
        break;
      case PowerBIChartType.gauge:
        iconData = Icons.speed;
        iconColor = Colors.amber;
        break;
      default:
        iconData = Icons.insert_chart;
        iconColor = Colors.grey;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withAlpha(51), // 0.2 * 255 = 51
      child: Icon(iconData, color: iconColor),
    );
  }

  /// عرض حوار مشاركة التقرير
  void _showShareReportDialog(PowerBIReport report) {
    final TextEditingController emailController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.share, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('مشاركة التقرير'),
          ],
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مشاركة التقرير "${report.title}"',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('أدخل البريد الإلكتروني للمستخدم الذي تريد مشاركة التقرير معه:'),
              const SizedBox(height: 8),
              TextFormField(
                controller: emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@') || !value.contains('.')) {
                    return 'الرجاء إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'ملاحظة: سيتمكن المستخدم من عرض التقرير فقط ولن يتمكن من تعديله أو حذفه.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
        actions: [
          TextButton.icon(
            icon: const Icon(Icons.cancel),
            label: const Text('إلغاء'),
            onPressed: () => Get.back(),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.send),
            label: const Text('مشاركة'),
            onPressed: () {
              if (formKey.currentState!.validate()) {
                // هنا سيتم تنفيذ عملية المشاركة
                Get.back();
                Get.snackbar(
                  'تمت المشاركة',
                  'تمت مشاركة التقرير مع ${emailController.text} بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                  duration: const Duration(seconds: 3),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  /// عرض حوار حذف التقرير
  void _showDeleteReportDialog(PowerBIReport report) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف التقرير'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await _powerBIController.deleteReport(report.id);
              if (success) {
                Get.snackbar(
                  'نجاح',
                  'تم حذف التقرير بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل في حذف التقرير',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }

  /// تحويل نوع الرسم البياني إلى نص
  String powerBIChartTypeToString(PowerBIChartType chartType) {
    return chartType.displayName;
  }
}
