
  import 'package:flutter/material.dart';
import 'package:flutter_application_2/professional_reports/screens/pdf_preview_screen_fixed.dart';
//mport 'package:flutter_quill/flutter_quill.dart' as pw show Document;
import 'package:get/get.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:get/get_navigation/src/routes/transitions_type.dart';

/// دالة عامة لعرض أي تقرير PDF بنفس أسلوب عرض تقرير المستخدمين
  Future<void> showPdfReport({
    required Future<pw.Document> Function() generatePdf,
    String? fileNamePrefix,
  }) async {
    try {
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري إنشاء التقرير...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      final pdf = await generatePdf();
      final pdfData = await pdf.save();

      Get.back();

      final fileName = '${fileNamePrefix ?? "تقرير"}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      Get.to(
        () => PdfPreviewScreen(
          pdfData: pdfData,
          fileName: fileName,
        ),
        transition: Transition.rightToLeft,
        duration: const Duration(milliseconds: 300),
      );
    } catch (e) {
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء التقرير: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  
  /// تحويل Unix timestamp إلى تاريخ قابل للقراءة
  String formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

