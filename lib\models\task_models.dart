import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/attachment_model.dart';

import 'user_model.dart';
import 'department_model.dart';
import 'task_type_models.dart';
import 'task_priority_models.dart';
import 'subtask_models.dart';
import 'task_comment_models.dart';


// تم نقل TaskStatus إلى task_type_models.dart لتجنب التضارب

// تم نقل TaskPriority إلى task_priority_models.dart لتجنب التكرار

/// نموذج المهمة
class Task {
  final int id;
  final String title;
  final String? description;
  final int? taskTypeId;
  final int creatorId;
  final int? assigneeId;
  final int? departmentId;
  final int createdAt; // Unix timestamp in seconds
  final int? startDate; // Unix timestamp in seconds
  final int? dueDate; // Unix timestamp in seconds
  final int? completedAt; // Unix timestamp in seconds
  final String status; // اسم الحالة (string)
  final String priority; // اسم الأولوية (string)
  final int completionPercentage;
  final int? estimatedTime; // بالدقائق
  final int? actualTime; // بالدقائق
  final bool isDeleted;
  
  // الحقول الجديدة - New fields
  final String? incoming; // الوارد - معلومات إضافية حول مصدر المهمة
  final String? note; // ملاحظات - ملاحظات إضافية حول المهمة

  // Navigation properties
  final User? creator;
  final User? assignee;
  final Department? department;
  final TaskType? taskType;
  final TaskStatus? statusNavigation;
  final TaskPriority? priorityNavigation;
  final List<Subtask> subtasks;
  final List<TaskComment> comments;
  final List<Attachment> attachments;

  // Additional properties for compatibility
  final List<String>? accessUserIds;

  const Task({
    required this.id,
    required this.title,
    this.description,
    this.taskTypeId,
    required this.creatorId,
    this.assigneeId,
    this.departmentId,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.completedAt,
    this.status = 'pending', // Default: pending
    this.priority = 'medium', // Default: medium
    this.completionPercentage = 0,
    this.estimatedTime,
    this.actualTime,
    this.isDeleted = false,
    this.incoming, // الحقل الجديد - الوارد
    this.note, // الحقل الجديد - الملاحظات
    this.creator,
    this.assignee,
    this.department,
    this.taskType,
    this.statusNavigation,
    this.priorityNavigation,
    this.subtasks = const [],
    this.comments = const [],
    this.attachments = const [],
    this.accessUserIds,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    final attachmentsList = json['attachments'] != null ? (json['attachments'] as List) : [];
    debugPrint('🟢 [Task.fromJson] المهمة ${json['id']} attachments count: ${attachmentsList.length}');
    return Task(
      // معالجة آمنة للحقول المطلوبة - Safe handling of required fields
      id: json['id'] != null ? (json['id'] as num).toInt() : 0,
      title: json['title']?.toString() ?? 'مهمة بدون عنوان',
      description: json['description']?.toString(),
      taskTypeId: json['taskTypeId'] != null ? (json['taskTypeId'] as num).toInt() : null,
      creatorId: json['creatorId'] != null ? (json['creatorId'] as num).toInt() : 0,
      assigneeId: json['assigneeId'] != null ? (json['assigneeId'] as num).toInt() : null,
      departmentId: json['departmentId'] != null ? (json['departmentId'] as num).toInt() : null,
      // معالجة آمنة للتواريخ - Safe handling of timestamps
      createdAt: json['createdAt'] != null ? (json['createdAt'] as num).toInt() : DateTime.now().millisecondsSinceEpoch ~/ 1000,
      startDate: json['startDate'] != null ? (json['startDate'] as num).toInt() : null,
      dueDate: json['dueDate'] != null ? (json['dueDate'] as num).toInt() : null,
      completedAt: json['completedAt'] != null ? (json['completedAt'] as num).toInt() : null,
      status: json['status'] as String? ?? 'pending',
      priority: json['priority'] as String? ?? 'medium',
      // معالجة آمنة لنسبة الإنجاز
      completionPercentage: json['completionPercentage'] != null 
          ? (json['completionPercentage'] is int 
              ? json['completionPercentage'] 
              : int.tryParse(json['completionPercentage'].toString()) ?? 0)
          : 0,
      estimatedTime: json['estimatedTime'] as int?,
      actualTime: json['actualTime'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      // الحقول الجديدة - New fields
      incoming: json['incoming'] as String?,
      note: json['note'] as String?,
      // Navigation properties from backend
      creator: json['creator'] != null
          ? User.fromJson(json['creator'] as Map<String, dynamic>)
          : null,
      assignee: json['assignee'] != null
          ? User.fromJson(json['assignee'] as Map<String, dynamic>)
          : null,
      department: json['department'] != null
          ? Department.fromJson(json['department'] as Map<String, dynamic>)
          : null,
      taskType: json['taskType'] != null
          ? TaskType.fromJson(json['taskType'] as Map<String, dynamic>)
          : null,
      statusNavigation: json['statusNavigation'] != null
          ? TaskStatus.fromJson(json['statusNavigation'] as Map<String, dynamic>)
          : null,
      priorityNavigation: json['priorityNavigation'] != null
          ? TaskPriority.fromJson(json['priorityNavigation'] as Map<String, dynamic>)
          : null,
      subtasks: json['subtasks'] != null
          ? (json['subtasks'] as List).map((i) => Subtask.fromJson(i)).toList()
          : [],
      comments: json['taskComments'] != null
          ? (json['taskComments'] as List)
              .where((i) => i != null)
              .map((i) => TaskComment.fromJson(i as Map<String, dynamic>))
              .toList()
          : [],
      attachments: attachmentsList.isNotEmpty
          ? attachmentsList.map((i) => Attachment.fromJson(i)).toList()
          : <Attachment>[],
      accessUserIds: json['accessUserIds'] != null
          ? List<String>.from(json['accessUserIds'] as List)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'taskTypeId': taskTypeId,
      'creatorId': creatorId,
      'assigneeId': assigneeId,
      'departmentId': departmentId,
      'createdAt': createdAt,
      'startDate': startDate,
      'dueDate': dueDate,
      'completedAt': completedAt,
      'status': status,
      'priority': priority,
      'completionPercentage': completionPercentage,
      'estimatedTime': estimatedTime,
      'actualTime': actualTime,
      'isDeleted': isDeleted,
      // الحقول الجديدة - New fields
      'incoming': incoming,
      'note': note,
      'accessUserIds': accessUserIds,
    };
  }

  Task copyWith({
    int? id,
    String? title,
    String? description,
    int? taskTypeId,
    int? creatorId,
    int? assigneeId,
    int? departmentId,
    int? createdAt,
    int? startDate,
    int? dueDate,
    int? completedAt,
    String? status,
    String? priority,
    int? completionPercentage,
    int? estimatedTime,
    int? actualTime,
    bool? isDeleted,
    String? incoming, // الحقل الجديد - الوارد
    String? note, // الحقل الجديد - الملاحظات
    User? creator,
    User? assignee,
    Department? department,
    TaskType? taskType,
    TaskStatus? statusNavigation,
    TaskPriority? priorityNavigation,
    List<Subtask>? subtasks,
    List<TaskComment>? comments,
    List<Attachment>? attachments,
    List<String>? accessUserIds,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      taskTypeId: taskTypeId ?? this.taskTypeId,
      creatorId: creatorId ?? this.creatorId,
      assigneeId: assigneeId ?? this.assigneeId,
      departmentId: departmentId ?? this.departmentId,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      actualTime: actualTime ?? this.actualTime,
      isDeleted: isDeleted ?? this.isDeleted,
      incoming: incoming ?? this.incoming, // الحقل الجديد - الوارد
      note: note ?? this.note, // الحقل الجديد - الملاحظات
      creator: creator ?? this.creator,
      assignee: assignee ?? this.assignee,
      department: department ?? this.department,
      taskType: taskType ?? this.taskType,
      statusNavigation: statusNavigation ?? this.statusNavigation,
      priorityNavigation: priorityNavigation ?? this.priorityNavigation,
      subtasks: subtasks ?? this.subtasks,
      comments: comments ?? this.comments,
      attachments: attachments ?? this.attachments,
      accessUserIds: accessUserIds ?? this.accessUserIds,
    );
  }

  /// التحقق من كون المهمة متأخرة
  bool isOverdue() {
    if (dueDate == null || status == 'completed') return false;
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now > dueDate!;
  }

  /// الحصول على عدد الأيام المتبقية
  int? get daysRemaining {
    if (dueDate == null) return null;
    final now = DateTime.now();
    final due = dueDateDateTime!;
    return due.difference(now).inDays;
  }

  /// التحقق من كون المهمة مكتملة
  bool get isCompleted => completionPercentage >= 100;

  /// الحصول على تاريخ الاستحقاق كـ DateTime
  DateTime? get dueDateDateTime => dueDate != null
      ? DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000)
      : null;

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime? get startDateDateTime => startDate != null
      ? DateTime.fromMillisecondsSinceEpoch(startDate! * 1000)
      : null;

  /// الحصول على تاريخ الإكمال كـ DateTime
  DateTime? get completedAtDateTime => completedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(completedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Task(id: $id, title: $title, status: $status, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Task && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج نوع المهمة - سيتم استيراده من task_type_models.dart
// تم نقل TaskType إلى ملف منفصل لتجنب التكرار

/// نموذج طلب إنشاء مهمة جديدة
class CreateTaskRequest {
  final String title;
  final String? description;
  final int? taskTypeId;
  final int? assigneeId;
  final int? departmentId;
  final int? startDate;
  final int? dueDate;
  final String priority;
  final int? estimatedTime;
  // الحقول الجديدة - New fields
  final String? incoming; // الوارد
  final String? note; // الملاحظات

  const CreateTaskRequest({
    required this.title,
    this.description,
    this.taskTypeId,
    this.assigneeId,
    this.departmentId,
    this.startDate,
    this.dueDate,
    this.priority = 'medium', // Default: medium
    this.estimatedTime,
    this.incoming, // الحقل الجديد - الوارد
    this.note, // الحقل الجديد - الملاحظات
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'taskTypeId': taskTypeId,
      'assigneeId': assigneeId,
      'departmentId': departmentId,
      'startDate': startDate,
      'dueDate': dueDate,
      'priority': priority,
      'estimatedTime': estimatedTime,
      // الحقول الجديدة - New fields
      'incoming': incoming,
      'note': note,
    };
  }
}

/// نموذج طلب تحديث مهمة
class UpdateTaskRequest {
  final String? title;
  final String? description;
  final int? taskTypeId;
  final int? assigneeId;
  final int? departmentId;
  final int? startDate;
  final int? dueDate;
  final String? status;
  final String? priority;
  final int? completionPercentage;
  final int? estimatedTime;
  final int? actualTime;
  // الحقول الجديدة - New fields
  final String? incoming; // الوارد
  final String? note; // الملاحظات

  const UpdateTaskRequest({
    this.title,
    this.description,
    this.taskTypeId,
    this.assigneeId,
    this.departmentId,
    this.startDate,
    this.dueDate,
    this.status,
    this.priority,
    this.completionPercentage,
    this.estimatedTime,
    this.actualTime,
    this.incoming, // الحقل الجديد - الوارد
    this.note, // الحقل الجديد - الملاحظات
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (description != null) json['description'] = description;
    if (taskTypeId != null) json['taskTypeId'] = taskTypeId;
    if (assigneeId != null) json['assigneeId'] = assigneeId;
    if (departmentId != null) json['departmentId'] = departmentId;
    if (startDate != null) json['startDate'] = startDate;
    if (dueDate != null) json['dueDate'] = dueDate;
    if (status != null) json['status'] = status;
    if (priority != null) json['priority'] = priority;
    if (completionPercentage != null) json['completionPercentage'] = completionPercentage;
    if (estimatedTime != null) json['estimatedTime'] = estimatedTime;
    if (actualTime != null) json['actualTime'] = actualTime;
    // الحقول الجديدة - New fields
    if (incoming != null) json['incoming'] = incoming;
    if (note != null) json['note'] = note;
    return json;
  }
}

// تم نقل TaskComment إلى task_comment_models.dart لتجنب التكرار

