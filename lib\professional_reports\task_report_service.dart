/// خدمة تقارير بسيطة للمستخدمين
/// 
/// خدمة بسيطة لجلب بيانات المستخدمين

library;

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../models/user_model.dart';
import '../../services/api/user_api_service.dart';

/// خدمة تقارير المستخدمين
class TaskReportService extends GetxService {
  /// خدمة API للمستخدمين
  final UserApiService _userApiService = UserApiService();

  /// جلب بيانات المستخدمين للتقارير
  Future<List<User>> getUsersReportData() async {
    try {
      debugPrint('🔄 بدء جلب بيانات المستخدمين للتقارير...');
      
      // جلب المستخدمين من API
      final users = await _userApiService.getAllUsers();
      
      debugPrint('✅ تم جلب ${users.length} مستخدم من قاعدة البيانات');
      
      return users;
      
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المستخدمين للتقارير: $e');
      rethrow;
    }
  }

  /// حساب إحصائيات المستخدمين
  Map<String, int> calculateUserStatistics(List<User> users) {
    try {
      debugPrint('📊 بدء حساب إحصائيات المستخدمين لـ ${users.length} مستخدم');
      
      final totalUsers = users.length;
      final activeUsers = users.where((user) => user.isActive).length;
      final inactiveUsers = totalUsers - activeUsers;
      
      // تجميع المستخدمين حسب الأدوار
      final usersByRole = <String, int>{};
      for (final user in users) {
        final roleName = user.roleName;
        usersByRole[roleName] = (usersByRole[roleName] ?? 0) + 1;
      }
      
      final statistics = {
        'total': totalUsers,
        'active': activeUsers,
        'inactive': inactiveUsers,
        ...usersByRole,
      };
      
      debugPrint('✅ تم حساب إحصائيات المستخدمين بنجاح');
      debugPrint('📈 الإحصائيات: إجمالي: $totalUsers، نشط: $activeUsers، غير نشط: $inactiveUsers');
      
      return statistics;
      
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات المستخدمين: $e');
      return {};
    }
  }

  /// تجميع المستخدمين حسب الدور
  Map<String, int> groupUsersByRole(List<User> users) {
    try {
      debugPrint('📊 تجميع المستخدمين حسب الدور...');
      
      final groupedUsers = <String, int>{};
      
      for (final user in users) {
        final roleName = user.role?.name ?? 'غير محدد';
        groupedUsers[roleName] = (groupedUsers[roleName] ?? 0) + 1;
      }
      
      debugPrint('✅ تم تجميع المستخدمين حسب الدور: $groupedUsers');
      return groupedUsers;
      
    } catch (e) {
      debugPrint('❌ خطأ في تجميع المستخدمين حسب الدور: $e');
      return {};
    }
  }

  /// تجميع المستخدمين حسب الحالة
  Map<String, int> groupUsersByStatus(List<User> users) {
    try {
      debugPrint('📊 تجميع المستخدمين حسب الحالة...');
      
      final groupedUsers = <String, int>{};
      
      for (final user in users) {
        final status = user.isActive ? 'نشط' : 'غير نشط';
        groupedUsers[status] = (groupedUsers[status] ?? 0) + 1;
      }
      
      debugPrint('✅ تم تجميع المستخدمين حسب الحالة: $groupedUsers');
      return groupedUsers;
      
    } catch (e) {
      debugPrint('❌ خطأ في تجميع المستخدمين حسب الحالة: $e');
      return {};
    }
  }
}

