class RoleDefaultPermission {
  final int id;
  final int roleId;
  final int permissionId;
  final bool isActive;
  final int createdAt;
  // يمكن ربط كائن الدور والصلاحية إذا لزم الأمر
  final dynamic role;
  final dynamic permission;

  RoleDefaultPermission({
    required this.id,
    required this.roleId,
    required this.permissionId,
    required this.isActive,
    required this.createdAt,
    this.role,
    this.permission,
  });

  factory RoleDefaultPermission.fromJson(Map<String, dynamic> json) {
    return RoleDefaultPermission(
      id: json['id'] as int,
      roleId: json['roleId'] as int,
      permissionId: json['permissionId'] as int,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as int,
      role: json['role'], // يمكن تعديلها لاحقًا
      permission: json['permission'], // يمكن تعديلها لاحقًا
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'roleId': roleId,
      'permissionId': permissionId,
      'isActive': isActive,
      'createdAt': createdAt,
      'role': role,
      'permission': permission,
    };
  }
}
