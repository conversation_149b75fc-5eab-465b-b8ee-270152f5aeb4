import 'package:flutter/material.dart';

import '../../../../constants/app_styles.dart';

/// مكون عرض البيانات في جدول
///
/// يعرض البيانات في شكل جدول
class TableVisualization extends StatefulWidget {
  /// عنوان الجدول
  final String title;

  /// وصف الجدول
  final String? description;

  /// بيانات الجدول
  final List<Map<String, dynamic>> data;

  /// حقول الجدول
  final List<String> fields;

  /// تسميات الحقول
  final List<String>? fieldLabels;

  /// إظهار رأس الجدول
  final bool showHeader;

  /// إظهار الصفوف المتناوبة
  final bool showAlternatingRows;

  /// إظهار حدود الخلايا
  final bool showBorders;

  /// إظهار أرقام الصفوف
  final bool showRowNumbers;

  /// عدد الصفوف في الصفحة
  final int rowsPerPage;

  /// العرض
  final double? width;

  /// الارتفاع
  final double? height;

  const TableVisualization({
    super.key,
    required this.title,
    this.description,
    required this.data,
    required this.fields,
    this.fieldLabels,
    this.showHeader = true,
    this.showAlternatingRows = true,
    this.showBorders = true,
    this.showRowNumbers = true,
    this.rowsPerPage = 10,
    this.width,
    this.height,
  });

  @override
  State<TableVisualization> createState() => _TableVisualizationState();
}

class _TableVisualizationState extends State<TableVisualization> {
  int _currentPage = 0;
  int _sortColumnIndex = -1;
  bool _sortAscending = true;
  List<Map<String, dynamic>> _sortedData = [];

  @override
  void initState() {
    super.initState();
    _sortedData = List.from(widget.data);
  }

  @override
  void didUpdateWidget(TableVisualization oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.data != oldWidget.data) {
      _sortedData = List.from(widget.data);
      _sortData();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty || widget.fields.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    // حساب عدد الصفحات
    final totalPages = (widget.data.length / widget.rowsPerPage).ceil();
    
    // حساب الصفوف المعروضة في الصفحة الحالية
    final startIndex = _currentPage * widget.rowsPerPage;
    final endIndex = (startIndex + widget.rowsPerPage > _sortedData.length)
        ? _sortedData.length
        : startIndex + widget.rowsPerPage;
    
    final displayedData = _sortedData.sublist(startIndex, endIndex);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الجدول
            Text(
              widget.title,
              style: AppStyles.titleMedium,
            ),
            if (widget.description != null && widget.description!.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                widget.description!,
                style: AppStyles.bodySmall,
              ),
            ],
            const SizedBox(height: 16),
            // الجدول
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowHeight: widget.showHeader ? 56 : 0,
                dataRowMinHeight: 48,
                dataRowMaxHeight: 64,
                columnSpacing: 16,
                horizontalMargin: 16,
                headingRowColor: WidgetStateProperty.resolveWith<Color>(
                  (states) => Colors.grey.shade100,
                ),
                decoration: widget.showBorders
                    ? BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                      )
                    : null,
                dividerThickness: widget.showBorders ? 1 : 0,
                showBottomBorder: widget.showBorders,
                columns: _buildColumns(),
                rows: _buildRows(displayedData),
              ),
            ),
            // التنقل بين الصفحات
            if (totalPages > 1) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.first_page),
                    onPressed: _currentPage > 0
                        ? () {
                            setState(() {
                              _currentPage = 0;
                            });
                          }
                        : null,
                    tooltip: 'الصفحة الأولى',
                  ),
                  IconButton(
                    icon: const Icon(Icons.navigate_before),
                    onPressed: _currentPage > 0
                        ? () {
                            setState(() {
                              _currentPage--;
                            });
                          }
                        : null,
                    tooltip: 'الصفحة السابقة',
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'الصفحة ${_currentPage + 1} من $totalPages',
                    style: AppStyles.bodyMedium,
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    icon: const Icon(Icons.navigate_next),
                    onPressed: _currentPage < totalPages - 1
                        ? () {
                            setState(() {
                              _currentPage++;
                            });
                          }
                        : null,
                    tooltip: 'الصفحة التالية',
                  ),
                  IconButton(
                    icon: const Icon(Icons.last_page),
                    onPressed: _currentPage < totalPages - 1
                        ? () {
                            setState(() {
                              _currentPage = totalPages - 1;
                            });
                          }
                        : null,
                    tooltip: 'الصفحة الأخيرة',
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أعمدة الجدول
  List<DataColumn> _buildColumns() {
    final columns = <DataColumn>[];
    
    // عمود رقم الصف
    if (widget.showRowNumbers) {
      columns.add(
        DataColumn(
          label: const Text('#'),
          tooltip: 'رقم الصف',
        ),
      );
    }
    
    // أعمدة البيانات
    for (int i = 0; i < widget.fields.length; i++) {
      final field = widget.fields[i];
      final label = widget.fieldLabels != null && i < widget.fieldLabels!.length
          ? widget.fieldLabels![i]
          : _formatFieldName(field);
      
      columns.add(
        DataColumn(
          label: Text(label),
          tooltip: label,
          onSort: (columnIndex, ascending) {
            setState(() {
              _sortColumnIndex = columnIndex;
              _sortAscending = ascending;
              _sortData();
            });
          },
        ),
      );
    }
    
    return columns;
  }

  /// بناء صفوف الجدول
  List<DataRow> _buildRows(List<Map<String, dynamic>> displayedData) {
    final rows = <DataRow>[];
    
    for (int i = 0; i < displayedData.length; i++) {
      final rowIndex = _currentPage * widget.rowsPerPage + i;
      final rowData = displayedData[i];
      final cells = <DataCell>[];
      
      // خلية رقم الصف
      if (widget.showRowNumbers) {
        cells.add(
          DataCell(
            Text(
              (rowIndex + 1).toString(),
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ),
        );
      }
      
      // خلايا البيانات
      for (final field in widget.fields) {
        cells.add(
          DataCell(
            Text(
              rowData[field]?.toString() ?? '',
              style: AppStyles.bodyMedium,
            ),
          ),
        );
      }
      
      rows.add(
        DataRow(
          color: widget.showAlternatingRows && i % 2 == 1
              ? WidgetStateProperty.all(Colors.grey.shade50)
              : null,
          cells: cells,
        ),
      );
    }
    
    return rows;
  }

  /// ترتيب البيانات
  void _sortData() {
    if (_sortColumnIndex < 0) return;
    
    int actualColumnIndex = _sortColumnIndex;
    if (widget.showRowNumbers) {
      actualColumnIndex--;
    }
    
    if (actualColumnIndex < 0 || actualColumnIndex >= widget.fields.length) return;
    
    final field = widget.fields[actualColumnIndex];
    
    _sortedData.sort((a, b) {
      final aValue = a[field];
      final bValue = b[field];
      
      // التعامل مع القيم الفارغة
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return _sortAscending ? -1 : 1;
      if (bValue == null) return _sortAscending ? 1 : -1;
      
      // المقارنة حسب نوع البيانات
      if (aValue is num && bValue is num) {
        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      } else if (aValue is String && bValue is String) {
        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      } else if (aValue is DateTime && bValue is DateTime) {
        return _sortAscending ? aValue.compareTo(bValue) : bValue.compareTo(aValue);
      } else {
        return _sortAscending
            ? aValue.toString().compareTo(bValue.toString())
            : bValue.toString().compareTo(aValue.toString());
      }
    });
  }

  /// تنسيق اسم الحقل
  String _formatFieldName(String field) {
    // تحويل camelCase إلى كلمات منفصلة
    final formattedField = field.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(0)}',
    );
    
    // تحويل الحرف الأول إلى حرف كبير
    return formattedField.substring(0, 1).toUpperCase() + formattedField.substring(1);
  }
}
