import 'package:flutter/material.dart';
import '../models/enhanced_report_model.dart';
import 'api/reports_api_service.dart';

/// خدمة تصدير التقارير
class ReportExportService {
  final ReportsApiService _apiService = ReportsApiService();

  /// تصدير تقرير إلى PDF
  Future<String?> exportReportToPdf(int reportId) async {
    try {
      return await _apiService.exportReportToPdf(reportId);
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى PDF: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى Excel
  Future<String?> exportReportToExcel(int reportId) async {
    try {
      return await _apiService.exportReportToExcel(reportId);
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى Excel: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى CSV
  Future<String?> exportReportToCsv(int reportId) async {
    try {
      return await _apiService.exportReportToCsv(reportId);
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى CSV: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى JSON
  Future<String?> exportReportToJson(int reportId) async {
    try {
      final result = await _apiService.exportReport(reportId, 'json', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى JSON: $e');
      return null;
    }
  }

  /// تصدير تقرير بتنسيق محدد
  Future<String?> exportReport({
    required int reportId,
    required ReportFormat format,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      String formatString;
      switch (format) {
        case ReportFormat.pdf:
          formatString = 'pdf';
          break;
        case ReportFormat.excel:
          formatString = 'excel';
          break;
        case ReportFormat.csv:
          formatString = 'csv';
          break;
        case ReportFormat.json:
          formatString = 'json';
          break;
        case ReportFormat.xml:
          formatString = 'xml';
          break;
        case ReportFormat.html:
          formatString = 'html';
          break;
      }

      final result = await _apiService.exportReport(reportId, formatString, parameters);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// الحصول على تنسيقات التصدير المدعومة
  List<ReportFormat> getSupportedFormats() {
    return [
      ReportFormat.pdf,
      ReportFormat.excel,
      ReportFormat.csv,
      ReportFormat.json,
    ];
  }

  /// الحصول على اسم التنسيق
  String getFormatName(ReportFormat format) {
    switch (format) {
      case ReportFormat.pdf:
        return 'PDF';
      case ReportFormat.excel:
        return 'Excel';
      case ReportFormat.csv:
        return 'CSV';
      case ReportFormat.json:
        return 'JSON';
      case ReportFormat.xml:
        return 'XML';
      case ReportFormat.html:
        return 'HTML';
    }
  }

  /// الحصول على أيقونة التنسيق
  IconData getFormatIcon(ReportFormat format) {
    switch (format) {
      case ReportFormat.pdf:
        return Icons.picture_as_pdf;
      case ReportFormat.excel:
        return Icons.table_chart;
      case ReportFormat.csv:
        return Icons.description;
      case ReportFormat.json:
        return Icons.code;
      case ReportFormat.xml:
        return Icons.code;
      case ReportFormat.html:
        return Icons.web;
    }
  }
}
