import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart' as charts;

import '../../../models/reporting/report_result_model.dart';
import '../../../constants/app_styles.dart';
import '../../../constants/app_colors.dart';

/// نموذج بيانات المخطط
class ChartData {
  /// المحور السيني
  final String x;

  /// المحور الصادي
  final double y;

  /// اللون
  final Color color;

  /// إنشاء نموذج بيانات المخطط
  const ChartData(this.x, this.y, this.color);
}

/// ويدجت التصور المرئي للتقارير
/// يعرض البيانات بأشكال مختلفة حسب نوع التصور
class ReportVisualizationWidget extends StatelessWidget {
  final ReportVisualization visualization;
  final List<Map<String, dynamic>> data;

  const ReportVisualizationWidget({
    super.key,
    required this.visualization,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    switch (visualization.type) {
      case VisualizationType.pieChart:
        return _buildPieChart();
      case VisualizationType.barChart:
        return _buildBarChart();
      case VisualizationType.lineChart:
        return _buildLineChart();
      case VisualizationType.table:
        return _buildTable();
      case VisualizationType.kpiCard:
        return _buildCard();
      default:
        return _buildTable();
    }
  }

  /// بناء الرسم البياني الدائري
  Widget _buildPieChart() {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    // إعداد البيانات للمخطط الدائري
    final List<ChartData> chartData = [];
    for (int i = 0; i < data.length && i < colors.length; i++) {
      final item = data[i];
      final value = _getNumericValue(item, visualization.dataFields.isNotEmpty ? visualization.dataFields[1] : 'value');
      final label = _getStringValue(item, visualization.dataFields.isNotEmpty ? visualization.dataFields[0] : 'label');

      chartData.add(ChartData(label, value, colors[i]));
    }

    return Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.blue.shade400, width: 2),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(8),
            child: charts.SfCircularChart(
              tooltipBehavior: charts.TooltipBehavior(enable: true),
              legend: charts.Legend(
                isVisible: true,
                position: charts.LegendPosition.bottom,
                overflowMode: charts.LegendItemOverflowMode.wrap,
              ),
              series: <charts.CircularSeries>[
                charts.PieSeries<ChartData, String>(
                  dataSource: chartData,
                  xValueMapper: (ChartData data, _) => data.x,
                  yValueMapper: (ChartData data, _) => data.y,
                  pointColorMapper: (ChartData data, _) => data.color,
                  dataLabelSettings: const charts.DataLabelSettings(
                    isVisible: true,
                    labelPosition: charts.ChartDataLabelPosition.outside,
                  ),
                  enableTooltip: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء الرسم البياني العمودي
  Widget _buildBarChart() {
    // إعداد البيانات للمخطط الشريطي
    final List<ChartData> chartData = [];
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final value = _getNumericValue(item, visualization.yAxisField ?? 'value');
      final label = _getStringValue(item, visualization.xAxisField ?? 'label');

      chartData.add(ChartData(label, value, AppColors.primary));
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: charts.SfCartesianChart(
        tooltipBehavior: charts.TooltipBehavior(enable: true),
        legend: charts.Legend(
          isVisible: false,
        ),
        primaryXAxis: charts.CategoryAxis(
          title: charts.AxisTitle(text: visualization.xAxisLabel ?? 'الفئة'),
          majorGridLines: const charts.MajorGridLines(width: 0),
          labelStyle: AppStyles.bodySmall,
        ),
        primaryYAxis: charts.NumericAxis(
          title: charts.AxisTitle(text: visualization.yAxisLabel ?? 'القيمة'),
          majorGridLines: const charts.MajorGridLines(width: 1),
          labelStyle: AppStyles.bodySmall,
        ),
        series: <charts.CartesianSeries>[
          charts.ColumnSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            color: AppColors.primary,
            dataLabelSettings: const charts.DataLabelSettings(
              isVisible: true,
              labelPosition: charts.ChartDataLabelPosition.outside,
            ),
            enableTooltip: true,
            animationDuration: 1000,
          ),
        ],
      ),
    );
  }

  /// بناء الرسم البياني الخطي
  Widget _buildLineChart() {
    // إعداد البيانات للمخطط الخطي
    final List<ChartData> chartData = [];
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final value = _getNumericValue(item, visualization.yAxisField ?? 'value');
      final label = _getStringValue(item, visualization.xAxisField ?? 'label');

      chartData.add(ChartData(label, value, AppColors.primary));
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: charts.SfCartesianChart(
        tooltipBehavior: charts.TooltipBehavior(enable: true),
        legend: charts.Legend(
          isVisible: false,
        ),
        primaryXAxis: charts.CategoryAxis(
          title: charts.AxisTitle(text: visualization.xAxisLabel ?? 'الفئة'),
          majorGridLines: const charts.MajorGridLines(width: 0),
          labelStyle: AppStyles.bodySmall,
        ),
        primaryYAxis: charts.NumericAxis(
          title: charts.AxisTitle(text: visualization.yAxisLabel ?? 'القيمة'),
          majorGridLines: const charts.MajorGridLines(width: 1),
          labelStyle: AppStyles.bodySmall,
        ),
        series: <charts.CartesianSeries>[
          charts.LineSeries<ChartData, String>(
            dataSource: chartData,
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            color: AppColors.primary,
            width: 3,
            markerSettings: const charts.MarkerSettings(
              isVisible: true,
              height: 6,
              width: 6,
              borderColor: Colors.white,
              borderWidth: 1,
            ),
            dataLabelSettings: const charts.DataLabelSettings(
              isVisible: true,
              labelPosition: charts.ChartDataLabelPosition.outside,
            ),
            enableTooltip: true,
            animationDuration: 1000,
          ),
        ],
      ),
    );
  }

  /// بناء الجدول
  Widget _buildTable() {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    final columns = data.first.keys.toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: columns
            .map((column) => DataColumn(
                  label: Text(
                    column,
                    style: AppStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ))
            .toList(),
        rows: data
            .map((row) => DataRow(
                  cells: columns
                      .map((column) => DataCell(
                            Text(
                              row[column]?.toString() ?? '',
                              style: AppStyles.bodyMedium,
                            ),
                          ))
                      .toList(),
                ))
            .toList(),
      ),
    );
  }

  /// بناء البطاقة
  Widget _buildCard() {
    if (data.isEmpty) {
      return const Center(child: Text('لا توجد بيانات'));
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        final title = _getStringValue(item, 'title').isNotEmpty
            ? _getStringValue(item, 'title')
            : _getStringValue(item, 'label').isNotEmpty
                ? _getStringValue(item, 'label')
                : 'عنصر ${index + 1}';
        final value = _getStringValue(item, 'value').isNotEmpty
            ? _getStringValue(item, 'value')
            : _getNumericValue(item, 'count').toString();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: AppStyles.titleLarge.copyWith(
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: AppStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }



  /// الحصول على قيمة رقمية من البيانات
  double _getNumericValue(Map<String, dynamic> item, String key) {
    final value = item[key];
    if (value is num) {
      return value.toDouble();
    }
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// الحصول على قيمة نصية من البيانات
  String _getStringValue(Map<String, dynamic> item, String key) {
    final value = item[key];
    return value?.toString() ?? '';
  }
}
