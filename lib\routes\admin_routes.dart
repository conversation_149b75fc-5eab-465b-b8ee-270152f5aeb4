// import 'package:flutter/widgets.dart';
// import 'package:get/get.dart';

// import '../screens/admin/enhanced_admin_dashboard_screen.dart';
// import '../bindings/enhanced_admin_binding.dart';

// /// مسارات لوحة التحكم الإدارية
// class AdminRoutes {
//   static const String adminDashboard = '/admin/dashboard';
//   static const String userManagement = '/admin/users';
//   static const String roleManagement = '/admin/roles';
//   static const String permissionManagement = '/admin/permissions';
//   static const String systemLogs = '/admin/logs';
//   static const String backupManagement = '/admin/backups';
//   static const String systemSettings = '/admin/settings';
//   static const String analytics = '/admin/analytics';
//   static const String databaseManagement = '/admin/database';

//   /// قائمة صفحات لوحة التحكم الإدارية
//   static List<GetPage> get pages => [
//     GetPage(
//       name: adminDashboard,
//       page: () => const EnhancedAdminDashboardScreen(),
//       binding: EnhancedAdminBinding(),
//       middlewares: [AdminMiddleware()],
//     ),
//   ];
// }

// /// وسطاء التحقق من صلاحيات الإدارة
// class AdminMiddleware extends GetMiddleware {
//   @override
//   RouteSettings? redirect(String? route) {
//     // التحقق من صلاحيات الوصول للوحة التحكم الإدارية
//     // سيتم تنفيذ منطق التحقق هنا
//     return null;
//   }
// }