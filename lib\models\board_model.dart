/// نموذج اللوحة - متوافق مع ASP.NET Core API
class Board {
  final int id;
  final String name;
  final String description;
  final int ownerId;
  final int createdAt;
  final int? updatedAt;
  final bool isActive;
  final bool isPublic;
  final String? color;
  final String? icon;
  final String? settings;

  const Board({
    required this.id,
    required this.name,
    required this.description,
    required this.ownerId,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isPublic = false,
    this.color,
    this.icon,
    this.settings,
  });

  /// إنشاء من JSON
  factory Board.fromJson(Map<String, dynamic> json) {
    return Board(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      ownerId: json['ownerId'] as int? ?? 0,
      createdAt: json['createdAt'] as int? ?? 0,
      updatedAt: json['updatedAt'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      isPublic: json['isPublic'] as bool? ?? false,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      settings: json['settings'] as String?,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'ownerId': ownerId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isActive': isActive,
      'isPublic': isPublic,
      'color': color,
      'icon': icon,
      'settings': settings,
    };
  }

  /// نسخ مع تعديل
  Board copyWith({
    int? id,
    String? name,
    String? description,
    int? ownerId,
    int? createdAt,
    int? updatedAt,
    bool? isActive,
    bool? isPublic,
    String? color,
    String? icon,
    String? settings,
  }) {
    return Board(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isPublic: isPublic ?? this.isPublic,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      settings: settings ?? this.settings,
    );
  }

  @override
  String toString() {
    return 'Board(id: $id, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Board && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
