import 'dart:convert';
import 'package:http/http.dart' as http;

/// خدمة موحدة لإدارة المستخدمين، الأدوار، الصلاحيات، الشاشات، وربطها ببعض
class AdminAccessApiService {
  final String baseUrl;
  final Map<String, String> headers;

  AdminAccessApiService({required this.baseUrl, required this.headers});

  // جلب جميع المستخدمين مع أدوارهم وصلاحياتهم
  Future<List<dynamic>> getAllUsers() async {
    final response = await http.get(Uri.parse('$baseUrl/Users/<USER>'), headers: headers);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب المستخدمين');
    }
  }

  // جلب جميع الأدوار
  Future<List<dynamic>> getAllRoles() async {
    final response = await http.get(Uri.parse('$baseUrl/Roles'), headers: headers);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب الأدوار');
    }
  }

  // جلب جميع الصلاحيات
  Future<List<dynamic>> getAllPermissions() async {
    final response = await http.get(Uri.parse('$baseUrl/Permissions'), headers: headers);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب الصلاحيات');
    }
  }

  // جلب جميع الشاشات
  Future<List<dynamic>> getAllScreens() async {
    final response = await http.get(Uri.parse('$baseUrl/Screens'), headers: headers);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب الشاشات');
    }
  }

  // تحديث أدوار وصلاحيات مستخدم
  Future<void> updateUserRolesAndPermissions(int userId, List<int> roleIds, List<int> permissionIds) async {
    final response = await http.put(
      Uri.parse('$baseUrl/Users/<USER>/UpdateRolesAndPermissions'),
      headers: headers,
      body: jsonEncode({
        'roleIds': roleIds,
        'permissionIds': permissionIds,
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('فشل في تحديث أدوار وصلاحيات المستخدم');
    }
  }

  // تحديث صلاحيات دور
  Future<void> updateRolePermissions(int roleId, List<int> permissionIds) async {
    final response = await http.put(
      Uri.parse('$baseUrl/Roles/$roleId/UpdatePermissions'),
      headers: headers,
      body: jsonEncode({'permissionIds': permissionIds}),
    );
    if (response.statusCode != 200) {
      throw Exception('فشل في تحديث صلاحيات الدور');
    }
  }

  // تحديث صلاحيات الوصول لواجهة
  Future<void> updateScreenPermissions(int screenId, List<int> permissionIds) async {
    final response = await http.put(
      Uri.parse('$baseUrl/Screens/$screenId/UpdatePermissions'),
      headers: headers,
      body: jsonEncode({'permissionIds': permissionIds}),
    );
    if (response.statusCode != 200) {
      throw Exception('فشل في تحديث صلاحيات الشاشة');
    }
  }

  // جلب صلاحيات مستخدم والشاشات التي يمكنه الوصول لها
  Future<Map<String, dynamic>> getUserPermissionsAndScreens(int userId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/Users/<USER>/PermissionsAndScreens'),
      headers: headers,
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body) as Map<String, dynamic>;
    } else {
      throw Exception('فشل في جلب صلاحيات المستخدم والشاشات');
    }
  }
}
