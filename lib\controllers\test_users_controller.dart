import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../services/api/test_api_service.dart';

/// متحكم لاختبار بيانات المستخدمين بدون تسجيل دخول
class TestUsersController extends GetxController {
  final TestApiService _testApiService = TestApiService();

  // قوائم البيانات
  final RxList<User> _users = <User>[].obs;
  final RxList<Map<String, dynamic>> _testResults = <Map<String, dynamic>>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isTestingConnection = false.obs;
  final RxBool _isSeedingData = false.obs;
  final RxBool _isRunningSystemTest = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // معلومات الخادم
  final Rx<Map<String, dynamic>?> _serverInfo = Rx<Map<String, dynamic>?>(null);
  final Rx<Map<String, dynamic>?> _systemTestResults = Rx<Map<String, dynamic>?>(null);

  // Getters
  List<User> get users => _users;
  List<Map<String, dynamic>> get testResults => _testResults;
  bool get isLoading => _isLoading.value;
  bool get isTestingConnection => _isTestingConnection.value;
  bool get isSeedingData => _isSeedingData.value;
  bool get isRunningSystemTest => _isRunningSystemTest.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  Map<String, dynamic>? get serverInfo => _serverInfo.value;
  Map<String, dynamic>? get systemTestResults => _systemTestResults.value;

  @override
  void onInit() {
    super.onInit();
    _initializeService();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    try {
      await _testApiService.initialize();
      debugPrint('تم تهيئة TestApiService بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة TestApiService: $e');
      _error.value = 'خطأ في تهيئة الخدمة: $e';
    }
  }

  /// مسح الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// اختبار الاتصال مع الخادم
  Future<void> testConnection() async {
    _isTestingConnection.value = true;
    clearMessages();

    try {
      final result = await _testApiService.testConnection();
      if (result != null) {
        _serverInfo.value = result;
        _successMessage.value = 'تم الاتصال بالخادم بنجاح!';
        _addTestResult('اختبار الاتصال', true, 'نجح الاتصال مع الخادم');
      } else {
        _error.value = 'فشل في الاتصال بالخادم';
        _addTestResult('اختبار الاتصال', false, 'فشل في الاتصال بالخادم');
      }
    } catch (e) {
      _error.value = 'خطأ في اختبار الاتصال: $e';
      _addTestResult('اختبار الاتصال', false, 'خطأ: $e');
    } finally {
      _isTestingConnection.value = false;
    }
  }

  /// إضافة البيانات التجريبية
  Future<void> seedTestData() async {
    _isSeedingData.value = true;
    clearMessages();

    try {
      // إضافة المستخدمين التجريبيين
      final usersResult = await _testApiService.seedUsers();
      if (usersResult) {
        _addTestResult('إضافة المستخدمين', true, 'تم إضافة المستخدمين التجريبيين بنجاح');
      } else {
        _addTestResult('إضافة المستخدمين', false, 'فشل في إضافة المستخدمين التجريبيين');
      }

      // إضافة الأقسام التجريبية
      final departmentsResult = await _testApiService.seedDepartments();
      if (departmentsResult) {
        _addTestResult('إضافة الأقسام', true, 'تم إضافة الأقسام التجريبية بنجاح');
      } else {
        _addTestResult('إضافة الأقسام', false, 'فشل في إضافة الأقسام التجريبية');
      }

      if (usersResult && departmentsResult) {
        _successMessage.value = 'تم إضافة جميع البيانات التجريبية بنجاح!';
      } else {
        _error.value = 'فشل في إضافة بعض البيانات التجريبية';
      }
    } catch (e) {
      _error.value = 'خطأ في إضافة البيانات التجريبية: $e';
      _addTestResult('إضافة البيانات', false, 'خطأ: $e');
    } finally {
      _isSeedingData.value = false;
    }
  }

  /// محاولة جلب المستخدمين (متوقع أن تفشل)
  Future<void> tryFetchUsers() async {
    _isLoading.value = true;
    clearMessages();

    try {
      final users = await _testApiService.tryGetUsers();
      _users.value = users;
      
      if (users.isNotEmpty) {
        _successMessage.value = 'تم جلب ${users.length} مستخدم بنجاح!';
        _addTestResult('جلب المستخدمين', true, 'تم جلب ${users.length} مستخدم');
      } else {
        _error.value = 'لا يمكن جلب المستخدمين - يتطلب تسجيل دخول';
        _addTestResult('جلب المستخدمين', false, 'يتطلب مصادقة');
      }
    } catch (e) {
      _error.value = 'خطأ في جلب المستخدمين: $e';
      _addTestResult('جلب المستخدمين', false, 'خطأ: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تشغيل اختبار شامل للنظام
  Future<void> runSystemTest() async {
    _isRunningSystemTest.value = true;
    clearMessages();

    try {
      final results = await _testApiService.runSystemTest();
      _systemTestResults.value = results;
      
      final errors = results['errors'] as List<dynamic>;
      if (errors.isEmpty) {
        _successMessage.value = 'تم تشغيل جميع الاختبارات بنجاح!';
      } else {
        _error.value = 'بعض الاختبارات فشلت: ${errors.join(', ')}';
      }
      
      _addTestResult('اختبار شامل', errors.isEmpty, 
          errors.isEmpty ? 'نجحت جميع الاختبارات' : 'فشل في ${errors.length} اختبار');
    } catch (e) {
      _error.value = 'خطأ في تشغيل الاختبار الشامل: $e';
      _addTestResult('اختبار شامل', false, 'خطأ: $e');
    } finally {
      _isRunningSystemTest.value = false;
    }
  }

  /// مسح جميع البيانات
  Future<void> clearAllData() async {
    _isLoading.value = true;
    clearMessages();

    try {
      final result = await _testApiService.clearAllData();
      if (result) {
        _users.clear();
        _successMessage.value = 'تم مسح جميع البيانات بنجاح!';
        _addTestResult('مسح البيانات', true, 'تم مسح جميع البيانات');
      } else {
        _error.value = 'فشل في مسح البيانات';
        _addTestResult('مسح البيانات', false, 'فشل في مسح البيانات');
      }
    } catch (e) {
      _error.value = 'خطأ في مسح البيانات: $e';
      _addTestResult('مسح البيانات', false, 'خطأ: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة نتيجة اختبار
  void _addTestResult(String testName, bool success, String message) {
    _testResults.insert(0, {
      'test_name': testName,
      'success': success,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    // الاحتفاظ بآخر 20 نتيجة فقط
    if (_testResults.length > 20) {
      _testResults.removeRange(20, _testResults.length);
    }
  }

  /// مسح نتائج الاختبارات
  void clearTestResults() {
    _testResults.clear();
    _systemTestResults.value = null;
    clearMessages();
  }

  /// تحديث البيانات
  Future<void> refreshData() async {
    await tryFetchUsers();
  }

  /// الحصول على معلومات الخادم
  Future<void> getServerInfo() async {
    _isLoading.value = true;
    clearMessages();

    try {
      final info = await _testApiService.getServerInfo();
      if (info != null) {
        _serverInfo.value = info;
        _successMessage.value = 'تم الحصول على معلومات الخادم';
        _addTestResult('معلومات الخادم', true, 'تم الحصول على المعلومات');
      } else {
        _error.value = 'فشل في الحصول على معلومات الخادم';
        _addTestResult('معلومات الخادم', false, 'فشل في الحصول على المعلومات');
      }
    } catch (e) {
      _error.value = 'خطأ في الحصول على معلومات الخادم: $e';
      _addTestResult('معلومات الخادم', false, 'خطأ: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// محاولة الحصول على مستخدم واحد
  Future<void> tryGetSingleUser(int userId) async {
    _isLoading.value = true;
    clearMessages();

    try {
      final user = await _testApiService.tryGetUserById(userId);
      if (user != null) {
        // إضافة المستخدم للقائمة إذا لم يكن موجوداً
        final existingIndex = _users.indexWhere((u) => u.id == user.id);
        if (existingIndex >= 0) {
          _users[existingIndex] = user;
        } else {
          _users.add(user);
        }
        
        _successMessage.value = 'تم جلب المستخدم: ${user.name}';
        _addTestResult('جلب مستخدم واحد', true, 'تم جلب ${user.name}');
      } else {
        _error.value = 'لا يمكن جلب المستخدم $userId';
        _addTestResult('جلب مستخدم واحد', false, 'فشل في جلب المستخدم $userId');
      }
    } catch (e) {
      _error.value = 'خطأ في جلب المستخدم: $e';
      _addTestResult('جلب مستخدم واحد', false, 'خطأ: $e');
    } finally {
      _isLoading.value = false;
    }
  }
}
