// افترضنا وجود نموذج لتتبع الوقت باسم TimeTrackingEntry
// يجب عليك إنشاء هذا النموذج إذا لم يكن موجودًا
// import 'package:flutter_application_2/models/time_tracking_entry_model.dart';

class TimeLogReport {
  final List<TimeLogEntry> timeLogEntries;

  TimeLogReport({
    required this.timeLogEntries,
  });

  // يتم إنشاء هذا التقرير عادة من جدول تتبع الوقت مباشرة
  // وليس من قائمة المهام، لذا قد تحتاج إلى تعديل هذه الدالة
  // لتناسب طريقة تخزين بيانات تتبع الوقت لديك.
  // factory TimeLogReport.fromTasks(List<Task> tasks) {
  //   final timeLogEntries = <TimeLogEntry>[];
  //   for (var task in tasks) {
  //     // هنا يجب جلب سجلات تتبع الوقت المرتبطة بالمهمة
  //     // وإضافتها إلى قائمة timeLogEntries
  //     // هذا مثال افتراضي ويجب تعديله ليناسب بياناتك
  //     timeLogEntries.add(TimeLogEntry(
  //       taskId: task.id,
  //       totalHours: 0, // قيمة افتراضية
  //     ));
  //   }
  //   return TimeLogReport(timeLogEntries: timeLogEntries);
  // }
}

// نموذج افتراضي لسجل تتبع الوقت، يجب تعديله ليناسب بياناتك
class TimeLogEntry {
  final int taskId;
  final double totalHours;

  TimeLogEntry({
    required this.taskId,
    required this.totalHours,
  });
}