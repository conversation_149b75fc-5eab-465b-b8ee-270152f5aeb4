import 'package:flutter/material.dart';
import 'package:flutter_application_2/services/report_service.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../models/report_models.dart';
import '../../models/report_criteria_model.dart';
import '../../models/reporting/report_result_model.dart';
import '../../controllers/auth_controller.dart';
import 'enhanced_charts_screen.dart';

/// شاشة التقارير المحسنة
///
/// توفر واجهة لإنشاء وعرض وتصدير التقارير المختلفة
class EnhancedReportsScreen extends StatefulWidget {
  const EnhancedReportsScreen({super.key});

  @override
  State<EnhancedReportsScreen> createState() => _EnhancedReportsScreenState();
}

class _EnhancedReportsScreenState extends State<EnhancedReportsScreen> {
  final ReportService _reportService = ReportService();
  final AuthController _authController = Get.find<AuthController>();

  List<Report> _reports = [];
  bool _isLoading = true;
  String? _errorMessage;

  // فلاتر التقارير
  ReportType? _selectedReportType;
  bool _showOnlyMyReports = true;

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  /// تحميل التقارير
  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      List<Report> reports;

      if (_showOnlyMyReports) {
        reports = await _reportService.getReportsByCreator(_authController.currentUser.value!.id);
      } else {
        reports = await _reportService.getAllReports();
      }

      // تطبيق الفلاتر
      if (_selectedReportType != null) {
        reports = reports.where((report) => report.type == _selectedReportType).toList();
      }

      // ترتيب التقارير حسب تاريخ الإنشاء (الأحدث أولاً)
      reports.sort((a, b) => b.createdAtDateTime.compareTo(a.createdAtDateTime));

      setState(() {
        _reports = reports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل التقارير: $e';
      });
    }
  }

  /// إنشاء تقرير جديد
  Future<void> _createNewReport() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _NewReportDialog(),
    );

    if (result != null) {
      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      try {
        final criteria = ReportCriteria(
          startDate: result['startDate'],
          endDate: result['endDate'],
          userIds: result['userIds'],
          departmentIds: result['departmentIds'],
          taskIds: result['taskIds'],
        );

        await _reportService.createReport(
          title: result['title'],
          description: result['description'],
          type: result['type'],
          createdById: _authController.currentUser.value!.id,
          criteria: criteria,
        );

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء التقرير بنجاح'),
            duration: Duration(seconds: 2),
          ),
        );

        _loadReports();
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
          _errorMessage = 'حدث خطأ أثناء إنشاء التقرير: $e';
        });
      }
    }
  }

  /// تنفيذ تقرير
  Future<void> _executeReport(Report report) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _reportService.executeReport(report.id);

      if (!mounted) return;

      if (result.isSuccess) {
        _showReportResult(report, result);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل تنفيذ التقرير: ${result.errorMessages?.join(', ')}'),
            backgroundColor: Colors.red,
          ),
        );
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تنفيذ التقرير: $e';
      });
    }
  }

  /// عرض نتيجة التقرير
  void _showReportResult(Report report, ReportResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(report.title),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('تاريخ التنفيذ: ${DateFormat('yyyy-MM-dd HH:mm').format(result.generatedAt)}'),
              const Divider(),
              if (result.summary != null && result.summary!.isNotEmpty) ...[
                const Text(
                  'ملخص التقرير:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: result.summary!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text('${entry.key}: ${entry.value}'),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ] else ...[
                const Text('لا يوجد ملخص متاح للتقرير.'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportReport(report);
            },
            child: const Text('تصدير التقرير'),
          ),
        ],
      ),
    );
  }

  /// تصدير تقرير
  void _exportReport(Report report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _exportToPdf(report);
                  },
                  child: const Text('PDF'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _exportToExcel(report);
                  },
                  child: const Text('Excel'),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تصدير إلى PDF
  Future<void> _exportToPdf(Report report) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى PDF...'),
        duration: Duration(seconds: 2),
      ),
    );

    try {
      final filePath = await _reportService.exportReportToPdf(report.id);

      if (!mounted) return;

      if (filePath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'فتح',
              onPressed: () {
                // هنا يمكن إضافة وظيفة فتح الملف
              },
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// تصدير إلى Excel
  Future<void> _exportToExcel(Report report) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        duration: Duration(seconds: 2),
      ),
    );

    try {
      final filePath = await _reportService.exportReportToExcel(report.id);

      if (!mounted) return;

      if (filePath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'فتح',
              onPressed: () {
                // هنا يمكن إضافة وظيفة فتح الملف
              },
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المحسنة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.assignment_outlined),
            onPressed: () {
              Get.toNamed('/report/comprehensive-test');
            },
            tooltip: 'التقرير الشامل الجديد',
          ),
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const EnhancedChartsScreen(),
                ),
              );
            },
            tooltip: 'الرسوم البيانية',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(child: Text(_errorMessage!, style: const TextStyle(color: Colors.red)))
                    : _reports.isEmpty
                        ? const Center(child: Text('لا توجد تقارير متاحة'))
                        : _buildReportsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewReport,
        tooltip: 'إنشاء تقرير جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// بناء فلاتر التقارير
  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فلترة التقارير',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<ReportType?>(
                    decoration: const InputDecoration(
                      labelText: 'نوع التقرير',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    value: _selectedReportType,
                    items: [
                      const DropdownMenuItem<ReportType?>(
                        value: null,
                        child: Text('جميع الأنواع'),
                      ),
                      ...ReportType.values.map((type) {
                        return DropdownMenuItem<ReportType>(
                          value: type,
                          child: Text(_getReportTypeName(type)),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedReportType = value;
                      });
                      _loadReports();
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('تقاريري فقط'),
                    value: _showOnlyMyReports,
                    onChanged: (value) {
                      setState(() {
                        _showOnlyMyReports = value ?? true;
                      });
                      _loadReports();
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة التقارير
  Widget _buildReportsList() {
    return ListView.builder(
      itemCount: _reports.length,
      itemBuilder: (context, index) {
        final report = _reports[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            title: Text(report.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('النوع: ${_getReportTypeName(report.type)}'),
                Text('تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(report.createdAtDateTime)}'),
                if (report.lastExportedAtDateTime != null)
                  Text('آخر تصدير: ${DateFormat('yyyy-MM-dd HH:mm').format(report.lastExportedAtDateTime!)}'),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () => _executeReport(report),
                  tooltip: 'تنفيذ التقرير',
                ),
                IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: () => _exportReport(report),
                  tooltip: 'تصدير التقرير',
                ),
              ],
            ),
            onTap: () => _executeReport(report),
          ),
        );
      },
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدمين';
      case ReportType.departmentPerformance:
        return 'أداء الأقسام';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'غير معروف';
    }
  }
}

/// حوار إنشاء تقرير جديد
class _NewReportDialog extends StatefulWidget {
  @override
  State<_NewReportDialog> createState() => _NewReportDialogState();
}

class _NewReportDialogState extends State<_NewReportDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  ReportType _reportType = ReportType.taskStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  List<String>? _selectedUserIds;
  List<String>? _selectedDepartmentIds;
  List<String>? _selectedTaskIds;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء تقرير جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان التقرير',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان للتقرير';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف التقرير',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<ReportType>(
                decoration: const InputDecoration(
                  labelText: 'نوع التقرير',
                  border: OutlineInputBorder(),
                ),
                value: _reportType,
                items: ReportType.values.map((type) {
                  return DropdownMenuItem<ReportType>(
                    value: type,
                    child: Text(_getReportTypeName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _reportType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, true),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ البداية',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _startDate != null
                              ? DateFormat('yyyy-MM-dd').format(_startDate!)
                              : 'اختر التاريخ',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, false),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ النهاية',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _endDate != null
                              ? DateFormat('yyyy-MM-dd').format(_endDate!)
                              : 'اختر التاريخ',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // هنا يمكن إضافة حقول إضافية حسب نوع التقرير
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _submitForm,
          child: const Text('إنشاء'),
        ),
      ],
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  /// إرسال النموذج
  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      Navigator.of(context).pop({
        'title': _titleController.text,
        'description': _descriptionController.text,
        'type': _reportType,
        'startDate': _startDate,
        'endDate': _endDate,
        'userIds': _selectedUserIds,
        'departmentIds': _selectedDepartmentIds,
        'taskIds': _selectedTaskIds,
      });
    }
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدمين';
      case ReportType.departmentPerformance:
        return 'أداء الأقسام';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'غير معروف';
    }
  }
}
