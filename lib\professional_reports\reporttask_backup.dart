import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:flutter_application_2/models/subtask_models.dart';
import 'package:flutter_application_2/models/task_comment_models.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/task_report_models.dart';
import 'package:flutter_application_2/models/task_history_models.dart';

import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// إنشاء تقرير PDF احترافي شامل لمهمة واحدة
/// 
/// يتضمن التقرير:
/// - صفحة غلاف احترافية
/// - البيانات الكاملة للمهمة
/// - المساهمون وعدد مساهمات كل شخص
/// - التحويلات والتخويلات للمهمة
/// - إحصائيات شاملة للمهمة
/// - سجل الأحداث التفصيلي
/// - المرفقات والمهام الفرعية
/// - التعليقات والملاحظات
/// 
/// [task] - المهمة المراد إنشاء تقرير لها
/// [onProgress] - دالة لتتبع تقدم العملية
/// [username] - اسم المستخدم الذي طلب التقرير
/// [includeFullHistory] - تضمين التاريخ الكامل (افتراضي: true)
Future<pw.Document> generateSingleTaskReportPdf(
  Task task, {
  Function(String)? onProgress,
  String? username,
  bool includeFullHistory = true,
}) async {
  final pdf = pw.Document();
  final enhancedApiService = EnhancedTaskApiService();
  
  try {
    onProgress?.call('🚀 بدء إنشاء التقرير الاحترافي الشامل للمهمة...');
    
    // تحميل الخطوط العربية المحسنة
    onProgress?.call('📝 تحميل الخطوط العربية المحسنة...');
    final fonts = await _loadEnhancedArabicFonts();
    
    // جلب البيانات الشاملة للمهمة من جميع المصادر
    onProgress?.call('📊 جلب البيانات الشاملة للمهمة من جميع المصادر...');
    final reportData = await enhancedApiService.getTaskComprehensiveReportData(task.id);

    // تسجيل معلومات البيانات المستلمة للمراجعة
    if (kDebugMode) {
      print('📊 بيانات التقرير الشامل للمهمة ${task.id}:');
      print('   - المساهمون: ${reportData['contributors']?.length ?? 0}');
      print('   - متتبعات التقدم: ${reportData['progressTrackers']?.length ?? 0}');
      print('   - سجلات الوقت: ${reportData['timeTrackingEntries']?.length ?? 0}');
      print('   - السجل التاريخي: ${reportData['taskHistories']?.length ?? 0}');
      print('   - التعليقات: ${task.comments.length}');
      print('   - المرفقات: ${task.attachments.length}');
      print('   - المهام الفرعية: ${task.subtasks.length}');
    }

    // بناء نموذج التقرير الشامل مع معالجة محسنة للأخطاء
    TaskReportModel reportModel;
    if (reportData.isNotEmpty) {
      try {
        // محاولة إنشاء النموذج من البيانات المحسنة
        reportModel = enhancedApiService.createEnhancedTaskReportModel(reportData);
        onProgress?.call('✅ تم إنشاء نموذج التقرير الشامل من البيانات المحسنة');
        
        // تسجيل إضافي للتحقق من البيانات
        if (kDebugMode) {
          print('📊 تفاصيل النموذج المُنشأ:');
          print('   - المساهمون: ${reportModel.contributors.length}');
          print('   - التحويلات: ${reportModel.transfers.length}');
          if (reportModel.contributors.isNotEmpty) {
            print('   - أول مساهم: ${reportModel.contributors.first.userName}');
          }
          if (reportModel.transfers.isNotEmpty) {
            print('   - أول تحويل: ${reportModel.transfers.first.fromUser} -> ${reportModel.transfers.first.toUser}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل التحويل من reportData إلى TaskReportModel: $e');
        }
        onProgress?.call('⚠️ استخدام البيانات الأساسية للمهمة مع التحسينات');
        reportModel = TaskReportModel.fromTask(task);
      }
    } else {
      onProgress?.call('ℹ️ استخدام البيانات الأساسية للمهمة مع التحسينات');
      reportModel = TaskReportModel.fromTask(task);
    }

    // إنشاء مساعد التقرير المحسن
    final reportHelper = _EnhancedReportHelper(fonts);

    // صفحة الغلاف الاحترافية المحسنة
    onProgress?.call('📄 إنشاء صفحة الغلاف الاحترافية...');
    pdf.addPage(_buildEnhancedCoverPage(task, username, reportHelper));
    
    // صفحة فهرس المحتويات
    onProgress?.call('📋 إنشاء فهرس المحتويات...');
    pdf.addPage(_buildTableOfContents(task, reportModel, reportHelper));
    
    // الصفحات التفصيلية الشاملة
    onProgress?.call('📊 إنشاء المحتوى التفصيلي الشامل...');
    pdf.addPage(_buildComprehensiveContentPages(task, reportModel, reportData, reportHelper));
    
    onProgress?.call('🎉 تم إنشاء التقرير الاحترافي الشامل بنجاح!');
    return pdf;
    
  } catch (e) {
    // تسجيل الخطأ مع التفاصيل
    if (kDebugMode) {
      print('❌ خطأ في إنشاء التقرير الاحترافي الشامل للمهمة ${task.id}: $e');
      print('❌ تفاصيل الخطأ: ${e.toString()}');
    }
    
    onProgress?.call('❌ خطأ في إنشاء التقرير الاحترافي، جاري إنشاء تقرير احتياطي محسن...');
    
    // إنشاء تقرير احتياطي محسن
    return await _generateEnhancedFallbackReport(task, onProgress, username, e.toString());
  }
}

/// إنشاء صفحة الغلاف الاحترافية المحسنة
pw.Page _buildEnhancedCoverPage(Task task, String? username, _EnhancedReportHelper helper) {
  return pw.Page(
    pageFormat: PdfPageFormat.a4,
    textDirection: pw.TextDirection.rtl,
    build: (pw.Context context) {
      return pw.Container(
        decoration: pw.BoxDecoration(
          gradient: pw.LinearGradient(
            begin: pw.Alignment.topCenter,
            end: pw.Alignment.bottomCenter,
            colors: [PdfColors.teal50, PdfColors.white],
          ),
        ),
        child: pw.Padding(
          padding: const pw.EdgeInsets.all(40),
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // شعار النظام مع تصميم احترافي
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.teal800,
                  borderRadius: pw.BorderRadius.circular(15),
                  boxShadow: [
                    pw.BoxShadow(
                      color: PdfColors.grey400,
                      offset: const PdfPoint(0, 4),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: pw.Column(
                  children: [
                    helper.buildTitle('بسم الله الرحمن الرحيم', fontSize: 16, color: PdfColors.white),
                    pw.SizedBox(height: 8),
                    helper.buildTitle('نظام إدارة المهام المتقدم', fontSize: 24, color: PdfColors.white),
                    pw.SizedBox(height: 4),
                    helper.buildTitle('تقرير المهمة الاحترافي الشامل', fontSize: 18, color: PdfColors.teal100),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // معلومات المهمة الأساسية في تصميم احترافي
              helper.buildEnhancedInfoContainer(
                title: 'بيانات المهمة الأساسية',
                color: PdfColors.white,
                borderColor: PdfColors.teal300,
                shadowColor: PdfColors.grey300,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    helper.buildInfoRow('العنوان:', task.title, isTitle: true),
                    helper.buildInfoRow('رقم المهمة:', '#${task.id}'),
                    helper.buildInfoRow('القسم:', task.department?.name ?? 'غير محدد'),
                    helper.buildInfoRow('النوع:', task.taskType?.name ?? 'غير محدد'),
                    helper.buildInfoRow('الحالة:', task.status),
                    helper.buildInfoRow('الأولوية:', task.priority),
                    helper.buildInfoRow('نسبة الإنجاز:', '${task.completionPercentage}%'),
                    helper.buildInfoRow('تاريخ الإنشاء:', _formatDate(DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000))),
                    if (task.dueDate != null)
                      helper.buildInfoRow('تاريخ الاستحقاق:', _formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))),
                    if (username != null)
                      helper.buildInfoRow('طالب التقرير:', username),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // معلومات إصدار التقرير
              pw.Container(
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(10),
                  border: pw.Border.all(color: PdfColors.grey300),
                ),
                child: pw.Column(
                  children: [
                    helper.buildInfoText('تاريخ إصدار التقرير: ${_formatDateTime(DateTime.now())}', 
                        fontSize: 12, color: PdfColors.grey700, isBold: true),
                    pw.SizedBox(height: 4),
                    helper.buildInfoText('نوع التقرير: تقرير مهمة شامل ومفصل', 
                        fontSize: 10, color: PdfColors.grey600),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

/// إنشاء صفحة فهرس المحتويات
pw.Page _buildTableOfContents(Task task, TaskReportModel reportModel, _EnhancedReportHelper helper) {
  return pw.Page(
    pageFormat: PdfPageFormat.a4,
    textDirection: pw.TextDirection.rtl,
    build: (pw.Context context) {
      return pw.Padding(
        padding: const pw.EdgeInsets.all(30),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            // عنوان الفهرس
            helper.buildSectionHeader('فهرس المحتويات', PdfColors.teal800),
            pw.SizedBox(height: 20),
            
            // عناصر الفهرس
            helper.buildTableOfContentsItem('1. ملخص المهمة الشامل', '3'),
            helper.buildTableOfContentsItem('2. المساهمون ومساهماتهم', '4'),
            helper.buildTableOfContentsItem('3. التحويلات والتخويلات', '5'),
            helper.buildTableOfContentsItem('4. متتبعات التقدم', '6'),
            helper.buildTableOfContentsItem('5. سجلات الوقت', '7'),
            helper.buildTableOfContentsItem('6. التعليقات والملاحظات', '8'),
            helper.buildTableOfContentsItem('7. المرفقات والوثائق', '9'),
            helper.buildTableOfContentsItem('8. المهام الفرعية', '10'),
            helper.buildTableOfContentsItem('9. الإحصائيات الشاملة', '11'),
            helper.buildTableOfContentsItem('10. سجل الأحداث التفصيلي', '12'),
            
            pw.SizedBox(height: 30),
            
            // ملخص سريع للمحتوى
            helper.buildEnhancedInfoContainer(
              title: 'ملخص سريع',
              color: PdfColors.blue50,
              borderColor: PdfColors.blue200,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  helper.buildInfoText('• عدد المساهمين: ${reportModel.contributors.length}'),
                  helper.buildInfoText('• عدد التحويلات: ${reportModel.transfers.length}'),
                  helper.buildInfoText('• عدد التعليقات: ${task.comments.length}'),
                  helper.buildInfoText('• عدد المرفقات: ${task.attachments.length}'),
                  helper.buildInfoText('• عدد المهام الفرعية: ${task.subtasks.length}'),
                  helper.buildInfoText('• نسبة الإنجاز: ${task.completionPercentage}%'),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

/// إنشاء الصفحات التفصيلية الشاملة
pw.MultiPage _buildComprehensiveContentPages(
  Task task, 
  TaskReportModel reportModel, 
  Map<String, dynamic> reportData,
  _EnhancedReportHelper helper
) {
  return pw.MultiPage(
    pageFormat: PdfPageFormat.a4,
    textDirection: pw.TextDirection.rtl,
    header: (context) => helper.buildPageHeader('تقرير المهمة الشامل: ${task.title}'),
    footer: (context) => helper.buildPageFooter(context),
    build: (pw.Context context) {
      return [
        // 1. ملخص المهمة الشامل
        _buildComprehensiveTaskSummary(task, reportModel, helper),
        pw.NewPage(),
        
        // 2. المساهمون ومساهماتهم التفصيلية
        _buildDetailedContributorsSection(reportModel.contributors, task, helper),
        pw.NewPage(),
        
        // 3. التحويلات والتخويلات الشاملة
        _buildComprehensiveTransfersSection(reportModel.transfers, task, helper),
        pw.NewPage(),
        
        // 4. متتبعات التقدم المفصلة
        if (reportModel.progressTrackers.isNotEmpty) ...[
          _buildDetailedProgressTrackersSection(reportModel.progressTrackers, helper),
          pw.NewPage(),
        ],
        
        // 5. سجلات الوقت التفصيلية
        if (reportModel.timeTrackingEntries.isNotEmpty) ...[
          _buildDetailedTimeTrackingSection(reportModel.timeTrackingEntries, helper),
          pw.NewPage(),
        ],
        
        // 6. التعليقات والملاحظات الشاملة
        if (task.comments.isNotEmpty) ...[
          _buildComprehensiveCommentsSection(task.comments, helper),
          pw.NewPage(),
        ],
        
        // 7. المرفقات والوثائق التفصيلية
        if (task.attachments.isNotEmpty) ...[
          _buildDetailedAttachmentsSection(task.attachments, helper),
          pw.NewPage(),
        ],
        
        // 8. المهام الفرعية الشاملة
        if (task.subtasks.isNotEmpty) ...[
          _buildComprehensiveSubtasksSection(task.subtasks, helper),
          pw.NewPage(),
        ],
        
        // 9. الإحصائيات الشاملة والتحليلات
        _buildComprehensiveStatisticsSection(reportModel.statistics, task, reportModel, helper),
        pw.NewPage(),
        
        // 10. سجل الأحداث التفصيلي الشامل
        _buildComprehensiveTaskHistorySection(task, reportData, helper),
      ];
    },
  );
}

/// بناء ملخص المهمة الشامل
pw.Widget _buildComprehensiveTaskSummary(Task task, TaskReportModel reportModel, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('1. ملخص المهمة الشامل', PdfColors.teal800),
      pw.SizedBox(height: 15),
      
      // البيانات الأساسية
      helper.buildEnhancedInfoContainer(
        title: 'البيانات الأساسية',
        color: PdfColors.teal50,
        borderColor: PdfColors.teal200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('العنوان:', task.title, isTitle: true),
            helper.buildInfoRow('الوصف:', task.description ?? 'لا يوجد وصف'),
            helper.buildInfoRow('الملاحظات:', task.note ?? 'لا توجد ملاحظات'),
            helper.buildInfoRow('الوارد:', task.incoming ?? 'غير محدد'),
            helper.buildInfoRow('رقم المهمة:', '#${task.id}'),
            helper.buildInfoRow('المنشئ:', task.creator?.name ?? 'غير محدد'),
            helper.buildInfoRow('المكلف:', task.assignee?.name ?? 'غير محدد'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // التواريخ والأوقات
      helper.buildEnhancedInfoContainer(
        title: 'التواريخ والأوقات',
        color: PdfColors.blue50,
        borderColor: PdfColors.blue200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('تاريخ الإنشاء:', _formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000))),
            helper.buildInfoRow('تاريخ البداية:', task.startDate != null ? _formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000)) : 'غير محدد'),
            helper.buildInfoRow('تاريخ الاستحقاق:', task.dueDate != null ? _formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)) : 'غير محدد'),
            helper.buildInfoRow('تاريخ الإنجاز:', task.completedAt != null ? _formatDateTime(DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000)) : 'لم يتم الإنجاز بعد'),
            helper.buildInfoRow('الوقت المقدر:', task.estimatedTime != null ? '${task.estimatedTime} دقيقة' : 'غير محدد'),
            helper.buildInfoRow('الوقت الفعلي:', task.actualTime != null ? '${task.actualTime} دقيقة' : 'غير محدد'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // الحالة والأولوية
      helper.buildEnhancedInfoContainer(
        title: 'الحالة والأولوية',
        color: PdfColors.orange50,
        borderColor: PdfColors.orange200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('الحالة الحالية:', task.status),
            helper.buildInfoRow('الأولوية:', task.priority),
            helper.buildInfoRow('نسبة الإنجاز:', '${task.completionPercentage}%'),
            helper.buildInfoRow('القسم:', task.department?.name ?? 'غير محدد'),
            helper.buildInfoRow('نوع المهمة:', task.taskType?.name ?? 'غير محدد'),
            helper.buildInfoRow('حالة الحذف:', task.isDeleted ? 'محذوفة' : 'نشطة'),
          ],
        ),
      ),
    ],
  );
}

/// بناء قسم المساهمين التفصيلي
pw.Widget _buildDetailedContributorsSection(List<TaskContributor> contributors, Task task, _EnhancedReportHelper helper) {
  // تسجيل تشخيصي
  if (kDebugMode) {
    print('🔍 بناء قسم المساهمين: ${contributors.length} مساهم');
    for (var contributor in contributors) {
      print('   - ${contributor.userName}: ${contributor.totalContributions} مساهمة');
    }
  }
  
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('2. المساهمون ومساهماتهم التفصيلية', PdfColors.blue800),
      pw.SizedBox(height: 15),
      
      // ملخص المساهمين
      helper.buildEnhancedInfoContainer(
        title: 'ملخص المساهمين',
        color: PdfColors.blue50,
        borderColor: PdfColors.blue200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي المساهمين:', '${contributors.length} مساهم'),
            helper.buildInfoRow('أكثر المساهمين نشاطاً:', contributors.isNotEmpty ? contributors.first.userName : 'لا يوجد'),
            helper.buildInfoRow('إجمالي المساهمات:', '${contributors.fold(0, (sum, c) => sum + c.totalContributions)} مساهمة'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // تفاصيل كل مساهم
      if (contributors.isNotEmpty) ...[
        helper.buildSubSectionHeader('تفاصيل المساهمين'),
        pw.SizedBox(height: 10),
        
        // جدول المساهمين
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.blue300, width: 1),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
            4: const pw.FlexColumnWidth(2),
            5: const pw.FlexColumnWidth(2),
          },
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.blue100),
              children: [
                helper.buildTableHeader('اسم المساهم'),
                helper.buildTableHeader('الدور'),
                helper.buildTableHeader('التعليقات'),
                helper.buildTableHeader('المرفقات'),
                helper.buildTableHeader('المساهمات'),
                helper.buildTableHeader('النسبة'),
              ],
            ),
            // بيانات المساهمين
            ...contributors.map((contributor) => pw.TableRow(
              children: [
                helper.buildTableCell(contributor.userName, isBold: true),
                helper.buildTableCell(contributor.role),
                helper.buildTableCell('${contributor.commentsCount}'),
                helper.buildTableCell('${contributor.attachmentsCount}'),
                helper.buildTableCell('${contributor.totalContributions}'),
                helper.buildTableCell('${contributor.percentage.toStringAsFixed(1)}%'),
              ],
            )),
          ],
        ),
      ] else ...[
        helper.buildInfoText('لا يوجد مساهمون مسجلون في هذه المهمة', 
            color: PdfColors.grey600, fontSize: 12),
      ],
      
      pw.SizedBox(height: 15),
      
      // تحليل المساهمات
      if (contributors.isNotEmpty) ...[
        helper.buildEnhancedInfoContainer(
          title: 'تحليل المساهمات',
          color: PdfColors.green50,
          borderColor: PdfColors.green200,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              helper.buildInfoRow('المساهم الأكثر تعليقاً:', 
                  contributors.isNotEmpty ? contributors.reduce((a, b) => a.commentsCount > b.commentsCount ? a : b).userName : 'لا يوجد'),
              helper.buildInfoRow('المساهم الأكثر رفعاً للملفات:', 
                  contributors.isNotEmpty ? contributors.reduce((a, b) => a.attachmentsCount > b.attachmentsCount ? a : b).userName : 'لا يوجد'),
              helper.buildInfoRow('متوسط المساهمات لكل شخص:', 
                  contributors.isNotEmpty ? '${(contributors.fold(0, (sum, c) => sum + c.totalContributions) / contributors.length).toStringAsFixed(1)} مساهمة' : '0'),
            ],
          ),
        ),
      ],
    ],
  );
}

/// بناء قسم التحويلات والتخويلات الشامل
pw.Widget _buildComprehensiveTransfersSection(List<TaskTransfer> transfers, Task task, _EnhancedReportHelper helper) {
  // تسجيل تشخيصي
  if (kDebugMode) {
    print('🔍 بناء قسم التحويلات: ${transfers.length} تحويل');
    for (var transfer in transfers) {
      print('   - ${transfer.fromUser} -> ${transfer.toUser}: ${transfer.reason}');
    }
  }
  
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('3. التحويلات والتخويلات الشاملة', PdfColors.orange800),
      pw.SizedBox(height: 15),
      
      // ملخص التحويلات
      helper.buildEnhancedInfoContainer(
        title: 'ملخص التحويلات',
        color: PdfColors.orange50,
        borderColor: PdfColors.orange200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي التحويلات:', '${transfers.length} تحويل'),
            helper.buildInfoRow('أول تحويل:', transfers.isNotEmpty ? 
                _formatDateTime(DateTime.fromMillisecondsSinceEpoch(transfers.first.timestamp * 1000)) : 'لا يوجد'),
            helper.buildInfoRow('آخر تحويل:', transfers.isNotEmpty ? 
                _formatDateTime(DateTime.fromMillisecondsSinceEpoch(transfers.last.timestamp * 1000)) : 'لا يوجد'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // تفاصيل التحويلات
      if (transfers.isNotEmpty) ...[
        helper.buildSubSectionHeader('سجل التحويلات التفصيلي'),
        pw.SizedBox(height: 10),
        
        // جدول التحويلات
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.orange300, width: 1),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(3),
            3: const pw.FlexColumnWidth(2),
            4: const pw.FlexColumnWidth(2),
          },
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.orange100),
              children: [
                helper.buildTableHeader('من'),
                helper.buildTableHeader('إلى'),
                helper.buildTableHeader('السبب'),
                helper.buildTableHeader('المنفذ'),
                helper.buildTableHeader('التاريخ'),
              ],
            ),
            // بيانات التحويلات
            ...transfers.map((transfer) => pw.TableRow(
              children: [
                helper.buildTableCell(transfer.fromUser),
                helper.buildTableCell(transfer.toUser),
                helper.buildTableCell(transfer.reason),
                helper.buildTableCell(transfer.executor),
                helper.buildTableCell(_formatDate(DateTime.fromMillisecondsSinceEpoch(transfer.timestamp * 1000))),
              ],
            )),
          ],
        ),
        
        pw.SizedBox(height: 15),
        
        // تحليل التحويلات
        helper.buildEnhancedInfoContainer(
          title: 'تحليل التحويلات',
          color: PdfColors.purple50,
          borderColor: PdfColors.purple200,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              helper.buildInfoRow('أكثر المستخدمين تحويلاً للمهام:', 
                  _getMostTransferringUser(transfers)),
              helper.buildInfoRow('أكثر المستخدمين استقبالاً للمهام:', 
                  _getMostReceivingUser(transfers)),
              helper.buildInfoRow('متوسط فترة بقاء المهمة مع كل مستخدم:', 
                  _calculateAverageStayDuration(transfers)),
            ],
          ),
        ),
      ] else ...[
        helper.buildInfoText('لا توجد تحويلات مسجلة لهذه المهمة', 
            color: PdfColors.grey600, fontSize: 12),
      ],
    ],
  );
}

// تم إزالة الدوال القديمة التي تستخدم _ReportHelper لتجنب التضارب
// جميع الدوال الجديدة تستخدم _EnhancedReportHelper

/// فئة مساعدة محسنة لبناء عناصر التقرير الاحترافي
class _EnhancedReportHelper {
  final Map<String, pw.Font> fonts;
  
  _EnhancedReportHelper(this.fonts);
  
  /// بناء عنوان رئيسي
  pw.Text buildTitle(String text, {double fontSize = 16, PdfColor? color, bool isBold = true}) {
    return pw.Text(
      text,
      style: pw.TextStyle(
        font: isBold ? fonts['bold'] : fonts['regular'],
        fontSize: fontSize,
        color: color ?? PdfColors.black,
      ),
      textAlign: pw.TextAlign.center,
    );
  }
  
  /// بناء رأس قسم احترافي
  pw.Widget buildSectionHeader(String title, PdfColor color) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: pw.BoxDecoration(
        color: color,
        borderRadius: pw.BorderRadius.circular(8),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey400,
            offset: const PdfPoint(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fonts['bold'],
          fontSize: 18,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  /// بناء رأس قسم فرعي
  pw.Widget buildSubSectionHeader(String title) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey200,
        borderRadius: pw.BorderRadius.circular(6),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          font: fonts['bold'],
          fontSize: 14,
          color: PdfColors.grey800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  /// بناء نص معلوماتي
  pw.Text buildInfoText(String text, {double fontSize = 12, PdfColor? color, bool isBold = false}) {
    return pw.Text(
      text,
      style: pw.TextStyle(
        font: isBold ? fonts['bold'] : fonts['regular'],
        fontSize: fontSize,
        color: color ?? PdfColors.grey800,
      ),
    );
  }
  
  /// بناء صف معلومات (مفتاح: قيمة)
  pw.Widget buildInfoRow(String key, String value, {bool isTitle = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: isTitle ? fonts['bold'] : fonts['regular'],
                fontSize: isTitle ? 14 : 12,
                color: isTitle ? PdfColors.teal800 : PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              key,
              style: pw.TextStyle(
                font: fonts['bold'],
                fontSize: 12,
                color: PdfColors.grey800,
              ),
              textAlign: pw.TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
  
  /// بناء حاوية معلوماتية محسنة
  pw.Container buildEnhancedInfoContainer({
    required String title,
    required pw.Widget child,
    PdfColor? color,
    PdfColor? borderColor,
    PdfColor? shadowColor,
    double padding = 16,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: pw.EdgeInsets.all(padding),
      decoration: pw.BoxDecoration(
        color: color ?? PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(color: borderColor ?? PdfColors.grey300, width: 1.5),
        boxShadow: shadowColor != null ? [
          pw.BoxShadow(
            color: shadowColor,
            offset: const PdfPoint(0, 2),
            blurRadius: 4,
          ),
        ] : null,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 14,
              color: PdfColors.grey800,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 8),
          child,
        ],
      ),
    );
  }
  
  /// بناء رأس جدول
  pw.Widget buildTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: fonts['bold'],
          fontSize: 11,
          color: PdfColors.grey800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  /// بناء خلية جدول
  pw.Widget buildTableCell(String text, {bool isBold = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: isBold ? fonts['bold'] : fonts['regular'],
          fontSize: 10,
          color: PdfColors.grey700,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  /// بناء عنصر فهرس المحتويات
  pw.Widget buildTableOfContentsItem(String title, String pageNumber) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            pageNumber,
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 12,
              color: PdfColors.grey600,
            ),
          ),
          pw.Expanded(
            child: pw.Container(
              margin: const pw.EdgeInsets.symmetric(horizontal: 8),
              child: pw.Text(
                '.' * 50,
                style: pw.TextStyle(
                  font: fonts['regular'],
                  fontSize: 8,
                  color: PdfColors.grey400,
                ),
                maxLines: 1,
              ),
            ),
          ),
          pw.Text(
            title,
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 12,
              color: PdfColors.grey800,
            ),
          ),
        ],
      ),
    );
  }
  
  /// بناء رأس الصفحة
  pw.Widget buildPageHeader(String title) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey300, width: 1)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            _formatDateTime(DateTime.now()),
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            title,
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 12,
              color: PdfColors.teal800,
            ),
          ),
        ],
      ),
    );
  }
  
  /// بناء تذييل الصفحة
  pw.Widget buildPageFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300, width: 1)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'نظام إدارة المهام المتقدم',
            style: pw.TextStyle(
              font: fonts['regular'],
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: pw.TextStyle(
              font: fonts['bold'],
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
        ],
      ),
    );
  }
}

/// بناء الأقسام المحسنة الإضافية

/// بناء قسم متتبعات التقدم المفصل
pw.Widget _buildDetailedProgressTrackersSection(List<TaskProgressTracker> progressTrackers, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('4. متتبعات التقدم المفصلة', PdfColors.amber800),
      pw.SizedBox(height: 15),
      
      // جدول متتبعات التقدم
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.amber300, width: 1),
        columnWidths: {
          0: const pw.FlexColumnWidth(3),
          1: const pw.FlexColumnWidth(2),
          2: const pw.FlexColumnWidth(2),
          3: const pw.FlexColumnWidth(2),
        },
        children: [
          pw.TableRow(
            decoration: const pw.BoxDecoration(color: PdfColors.amber100),
            children: [
              helper.buildTableHeader('العنوان'),
              helper.buildTableHeader('النسبة'),
              helper.buildTableHeader('التاريخ'),
              helper.buildTableHeader('المحدث بواسطة'),
            ],
          ),
          ...progressTrackers.map((tracker) => pw.TableRow(
            children: [
              helper.buildTableCell(tracker.notes),
              helper.buildTableCell('${tracker.progress}%'),
              helper.buildTableCell(_formatDate(tracker.date)),
              helper.buildTableCell(tracker.updatedBy.toString()),
            ],
          )),
        ],
      ),
    ],
  );
}

/// بناء قسم سجلات الوقت التفصيلي
pw.Widget _buildDetailedTimeTrackingSection(List<TimeTrackingEntry> timeEntries, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('5. سجلات الوقت التفصيلية', PdfColors.deepOrange800),
      pw.SizedBox(height: 15),
      
      // ملخص الوقت
      helper.buildEnhancedInfoContainer(
        title: 'ملخص الوقت',
        color: PdfColors.deepOrange50,
        borderColor: PdfColors.deepOrange200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي الوقت المسجل:', '${timeEntries.fold(0, (sum, e) => sum + e.durationMinutes)} دقيقة'),
            helper.buildInfoRow('عدد جلسات العمل:', '${timeEntries.length} جلسة'),
            helper.buildInfoRow('متوسط مدة الجلسة:', '${timeEntries.isNotEmpty ? (timeEntries.fold(0, (sum, e) => sum + e.durationMinutes) / timeEntries.length).toStringAsFixed(1) : 0} دقيقة'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // جدول سجلات الوقت
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.deepOrange300, width: 1),
        columnWidths: {
          0: const pw.FlexColumnWidth(2),
          1: const pw.FlexColumnWidth(2),
          2: const pw.FlexColumnWidth(2),
          3: const pw.FlexColumnWidth(1),
          4: const pw.FlexColumnWidth(3),
        },
        children: [
          pw.TableRow(
            decoration: const pw.BoxDecoration(color: PdfColors.deepOrange100),
            children: [
              helper.buildTableHeader('المستخدم'),
              helper.buildTableHeader('وقت البدء'),
              helper.buildTableHeader('وقت الانتهاء'),
              helper.buildTableHeader('المدة'),
              helper.buildTableHeader('الملاحظة'),
            ],
          ),
          ...timeEntries.map((entry) => pw.TableRow(
            children: [
              helper.buildTableCell(entry.userName),
              helper.buildTableCell(_formatDateTime(entry.startTime)),
              helper.buildTableCell(_formatDateTime(entry.endTime)),
              helper.buildTableCell('${entry.durationMinutes}د'),
              helper.buildTableCell(entry.description),
            ],
          )),
        ],
      ),
    ],
  );
}

/// بناء قسم التعليقات الشامل
pw.Widget _buildComprehensiveCommentsSection(List<TaskComment> comments, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('6. التعليقات والملاحظات الشاملة', PdfColors.green800),
      pw.SizedBox(height: 15),
      
      // ملخص التعليقات
      helper.buildEnhancedInfoContainer(
        title: 'ملخص التعليقات',
        color: PdfColors.green50,
        borderColor: PdfColors.green200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي التعليقات:', '${comments.length} تعليق'),
            helper.buildInfoRow('أول تعليق:', comments.isNotEmpty ? 
                _formatDateTime(DateTime.fromMillisecondsSinceEpoch(comments.first.createdAt * 1000)) : 'لا يوجد'),
            helper.buildInfoRow('آخر تعليق:', comments.isNotEmpty ? 
                _formatDateTime(DateTime.fromMillisecondsSinceEpoch(comments.last.createdAt * 1000)) : 'لا يوجد'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // التعليقات التفصيلية
      ...comments.map((comment) => pw.Container(
        margin: const pw.EdgeInsets.only(bottom: 10),
        padding: const pw.EdgeInsets.all(12),
        decoration: pw.BoxDecoration(
          color: PdfColors.green50,
          borderRadius: pw.BorderRadius.circular(8),
          border: pw.Border.all(color: PdfColors.green200),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                helper.buildInfoText(_formatDateTime(DateTime.fromMillisecondsSinceEpoch(comment.createdAt * 1000)), 
                    fontSize: 10, color: PdfColors.grey600),
                helper.buildInfoText(comment.user?.name ?? 'مستخدم غير معروف', 
                    fontSize: 12, isBold: true, color: PdfColors.green800),
              ],
            ),
            pw.SizedBox(height: 5),
            helper.buildInfoText(comment.content, fontSize: 11, color: PdfColors.grey700),
          ],
        ),
      )),
    ],
  );
}

/// بناء قسم المرفقات التفصيلي
pw.Widget _buildDetailedAttachmentsSection(List<Attachment> attachments, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('7. المرفقات والوثائق التفصيلية', PdfColors.purple800),
      pw.SizedBox(height: 15),
      
      // ملخص المرفقات
      helper.buildEnhancedInfoContainer(
        title: 'ملخص المرفقات',
        color: PdfColors.purple50,
        borderColor: PdfColors.purple200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي المرفقات:', '${attachments.length} ملف'),
            helper.buildInfoRow('إجمالي الحجم:', _calculateTotalSize(attachments)),
            helper.buildInfoRow('أنواع الملفات:', _getFileTypes(attachments)),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // جدول المرفقات
      pw.Table(
        border: pw.TableBorder.all(color: PdfColors.purple300, width: 1),
        columnWidths: {
          0: const pw.FlexColumnWidth(3),
          1: const pw.FlexColumnWidth(1),
          2: const pw.FlexColumnWidth(2),
          3: const pw.FlexColumnWidth(2),
        },
        children: [
          pw.TableRow(
            decoration: const pw.BoxDecoration(color: PdfColors.purple100),
            children: [
              helper.buildTableHeader('اسم الملف'),
              helper.buildTableHeader('الحجم'),
              helper.buildTableHeader('رافع الملف'),
              helper.buildTableHeader('تاريخ الرفع'),
            ],
          ),
          ...attachments.map((attachment) => pw.TableRow(
            children: [
              helper.buildTableCell(attachment.fileName),
              helper.buildTableCell(attachment.fileSizeFormatted),
              helper.buildTableCell(attachment.uploadedByUser?.name ?? 'غير معروف'),
              helper.buildTableCell(_formatDate(DateTime.fromMillisecondsSinceEpoch(attachment.uploadedAt * 1000))),
            ],
          )),
        ],
      ),
    ],
  );
}

/// بناء قسم المهام الفرعية الشامل
pw.Widget _buildComprehensiveSubtasksSection(List<Subtask> subtasks, _EnhancedReportHelper helper) {
  final completedCount = subtasks.where((s) => s.isCompleted).length;
  final completionRate = subtasks.isNotEmpty ? (completedCount / subtasks.length * 100) : 0;
  
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('8. المهام الفرعية الشاملة', PdfColors.indigo800),
      pw.SizedBox(height: 15),
      
      // ملخص المهام الفرعية
      helper.buildEnhancedInfoContainer(
        title: 'ملخص المهام الفرعية',
        color: PdfColors.indigo50,
        borderColor: PdfColors.indigo200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('إجمالي المهام الفرعية:', '${subtasks.length} مهمة'),
            helper.buildInfoRow('المهام المكتملة:', '$completedCount مهمة'),
            helper.buildInfoRow('المهام المتبقية:', '${subtasks.length - completedCount} مهمة'),
            helper.buildInfoRow('معدل الإنجاز:', '${completionRate.toStringAsFixed(1)}%'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // قائمة المهام الفرعية
      ...subtasks.map((subtask) => pw.Container(
        margin: const pw.EdgeInsets.only(bottom: 8),
        padding: const pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          color: subtask.isCompleted ? PdfColors.green50 : PdfColors.orange50,
          borderRadius: pw.BorderRadius.circular(6),
          border: pw.Border.all(
            color: subtask.isCompleted ? PdfColors.green300 : PdfColors.orange300,
          ),
        ),
        child: pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            helper.buildInfoText(subtask.isCompleted ? '✓ مكتملة' : '○ غير مكتملة', 
                fontSize: 10, color: subtask.isCompleted ? PdfColors.green700 : PdfColors.orange700),
            pw.Expanded(
              child: helper.buildInfoText(subtask.title, fontSize: 11, color: PdfColors.grey800),
            ),
          ],
        ),
      )),
    ],
  );
}

/// بناء قسم الإحصائيات الشاملة
pw.Widget _buildComprehensiveStatisticsSection(TaskReportStatistics statistics, Task task, TaskReportModel reportModel, _EnhancedReportHelper helper) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('9. الإحصائيات الشاملة والتحليلات', PdfColors.cyan800),
      pw.SizedBox(height: 15),
      
      // الإحصائيات الأساسية
      helper.buildEnhancedInfoContainer(
        title: 'الإحصائيات الأساسية',
        color: PdfColors.cyan50,
        borderColor: PdfColors.cyan200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('عدد المساهمين:', '${statistics.totalContributors}'),
            helper.buildInfoRow('عدد التحويلات:', '${statistics.totalTransfers}'),
            helper.buildInfoRow('عدد التعليقات:', '${statistics.totalComments}'),
            helper.buildInfoRow('عدد المرفقات:', '${statistics.totalAttachments}'),
            helper.buildInfoRow('عدد المهام الفرعية:', '${statistics.totalSubtasks}'),
            helper.buildInfoRow('المهام الفرعية المكتملة:', '${statistics.completedSubtasks}'),
          ],
        ),
      ),
      
      pw.SizedBox(height: 15),
      
      // تحليل الأداء
      helper.buildEnhancedInfoContainer(
        title: 'تحليل الأداء',
        color: PdfColors.lime50,
        borderColor: PdfColors.lime200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('معدل الإنجاز:', '${statistics.completionRate.toStringAsFixed(1)}%'),
            helper.buildInfoRow('الأيام النشطة:', '${statistics.daysActive} يوم'),
            helper.buildInfoRow('أكثر المساهمين نشاطاً:', statistics.mostActiveContributor),
            helper.buildInfoRow('آخر تحويل:', statistics.latestTransfer),
            helper.buildInfoRow('متوسط التعليقات يومياً:', statistics.daysActive > 0 ? (statistics.totalComments / statistics.daysActive).toStringAsFixed(1) : '0'),
          ],
        ),
      ),
    ],
  );
}

/// بناء قسم سجل الأحداث الشامل
pw.Widget _buildComprehensiveTaskHistorySection(Task task, Map<String, dynamic> reportData, _EnhancedReportHelper helper) {
  List<TaskHistory> histories = [];
  
  if (reportData['taskHistories'] != null && reportData['taskHistories'] is List) {
    histories = (reportData['taskHistories'] as List)
      .whereType<Map<String, dynamic>>()
      .map((e) => TaskHistory.fromJson(e))
      .toList();
  }
  
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('10. سجل الأحداث التفصيلي الشامل', PdfColors.grey800),
      pw.SizedBox(height: 15),
      
      if (histories.isNotEmpty) ...[
        // ملخص الأحداث
        helper.buildEnhancedInfoContainer(
          title: 'ملخص الأحداث',
          color: PdfColors.grey50,
          borderColor: PdfColors.grey300,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              helper.buildInfoRow('إجمالي الأحداث:', '${histories.length} حدث'),
              helper.buildInfoRow('أول حدث:', _formatDateTime(histories.first.timestampDateTime)),
              helper.buildInfoRow('آخر حدث:', _formatDateTime(histories.last.timestampDateTime)),
            ],
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        // سجل الأحداث التفصيلي
        ...histories.map((history) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 8),
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            borderRadius: pw.BorderRadius.circular(6),
            border: pw.Border.all(color: PdfColors.grey300),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  helper.buildInfoText(_formatDateTime(history.timestampDateTime), 
                      fontSize: 10, color: PdfColors.grey600),
                  helper.buildInfoText(history.changedByNavigation?.name ?? history.user?.name ?? 'النظام', 
                      fontSize: 11, isBold: true, color: PdfColors.grey800),
                ],
              ),
              pw.SizedBox(height: 3),
              helper.buildInfoText(history.actionDescription, fontSize: 11, color: PdfColors.grey700),
              if (history.details != null && history.details!.isNotEmpty) ...[
                pw.SizedBox(height: 2),
                helper.buildInfoText('التفاصيل: ${history.details}', fontSize: 10, color: PdfColors.grey600),
              ],
            ],
          ),
        )),
      ] else ...[
        helper.buildInfoText('لا يوجد سجل أحداث متاح لهذه المهمة', 
            color: PdfColors.grey600, fontSize: 12),
      ],
    ],
  );
}

/// دوال مساعدة إضافية

/// الحصول على أكثر المستخدمين تحويلاً للمهام
String _getMostTransferringUser(List<TaskTransfer> transfers) {
  if (transfers.isEmpty) return 'لا يوجد';
  
  final Map<String, int> transferCounts = {};
  for (var transfer in transfers) {
    transferCounts[transfer.fromUser] = (transferCounts[transfer.fromUser] ?? 0) + 1;
  }
  
  var maxCount = 0;
  var mostTransferringUser = 'لا يوجد';
  transferCounts.forEach((user, count) {
    if (count > maxCount) {
      maxCount = count;
      mostTransferringUser = user;
    }
  });
  
  return mostTransferringUser;
}

/// الحصول على أكثر المستخدمين استقبالاً للمهام
String _getMostReceivingUser(List<TaskTransfer> transfers) {
  if (transfers.isEmpty) return 'لا يوجد';
  
  final Map<String, int> receiveCounts = {};
  for (var transfer in transfers) {
    receiveCounts[transfer.toUser] = (receiveCounts[transfer.toUser] ?? 0) + 1;
  }
  
  var maxCount = 0;
  var mostReceivingUser = 'لا يوجد';
  receiveCounts.forEach((user, count) {
    if (count > maxCount) {
      maxCount = count;
      mostReceivingUser = user;
    }
  });
  
  return mostReceivingUser;
}

/// حساب متوسط فترة بقاء المهمة مع كل مستخدم
String _calculateAverageStayDuration(List<TaskTransfer> transfers) {
  if (transfers.length < 2) return 'غير محدد';
  
  var totalDuration = 0;
  for (int i = 1; i < transfers.length; i++) {
    totalDuration += transfers[i].timestamp - transfers[i-1].timestamp;
  }
  
  final averageDurationSeconds = totalDuration / (transfers.length - 1);
  final averageDurationDays = (averageDurationSeconds / 86400).round();
  
  return '$averageDurationDays يوم';
}

/// حساب إجمالي حجم المرفقات
String _calculateTotalSize(List<Attachment> attachments) {
  final totalBytes = attachments.fold(0, (sum, attachment) => sum + attachment.fileSize);
  
  if (totalBytes < 1024) return '$totalBytes بايت';
  if (totalBytes < 1024 * 1024) return '${(totalBytes / 1024).toStringAsFixed(1)} كيلوبايت';
  if (totalBytes < 1024 * 1024 * 1024) return '${(totalBytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
  return '${(totalBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
}

/// الحصول على أنواع الملفات
String _getFileTypes(List<Attachment> attachments) {
  final types = attachments.map((a) => a.fileType).whereType<String>().toSet().toList();
  if (types.isEmpty) return 'غير معروف';
  return types.join(', ');
}

/// إنشاء تقرير احتياطي محسن للمهمة الواحدة في حالة فشل الخدمة المحسنة
Future<pw.Document> _generateEnhancedFallbackReport(
  Task task,
  Function(String)? onProgress,
  String? username,
  String error,
) async {
  final pdf = pw.Document();

  // تحميل الخطوط العربية
  final fonts = await _loadEnhancedArabicFonts();
  
  onProgress?.call('إنشاء تقرير احتياطي محسن للمهمة...');
  
  // إنشاء نموذج التقرير الأساسي
  onProgress?.call('استخراج بيانات المهمة الأساسية...');
  final reportModel = TaskReportModel.fromTask(task);
  
  onProgress?.call('تم استخراج ${reportModel.contributors.length} مساهم و ${reportModel.transfers.length} تحويل');

  // إنشاء مساعد التقرير المحسن للنسخة الاحتياطية
  final helper = _EnhancedReportHelper(fonts);

  // صفحة الغلاف
  pdf.addPage(_buildEnhancedCoverPage(task, username, helper));
  
  // المحتوى الأساسي
  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      header: (context) => helper.buildPageHeader('تقرير المهمة الاحتياطي: ${task.title}'),
      footer: (context) => helper.buildPageFooter(context),
      build: (pw.Context context) {
        return [
          // رسالة التنبيه
          pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.orange100,
              borderRadius: pw.BorderRadius.circular(8),
              border: pw.Border.all(color: PdfColors.orange300),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                helper.buildInfoText('تنبيه: هذا تقرير احتياطي', 
                    fontSize: 14, isBold: true, color: PdfColors.orange800),
                helper.buildInfoText('تم إنشاء هذا التقرير بسبب خطأ في النظام المحسن: $error', 
                    fontSize: 10, color: PdfColors.orange700),
              ],
            ),
          ),
          
          pw.SizedBox(height: 20),
          
          // بيانات المهمة الأساسية
          _buildComprehensiveTaskSummary(task, reportModel, helper),
          pw.SizedBox(height: 20),
          
          // المساهمون
          if (reportModel.contributors.isNotEmpty) ...[
            _buildDetailedContributorsSection(reportModel.contributors, task, helper),
            pw.SizedBox(height: 20),
          ],
          
          // التحويلات
          if (reportModel.transfers.isNotEmpty) ...[
            _buildComprehensiveTransfersSection(reportModel.transfers, task, helper),
            pw.SizedBox(height: 20),
          ],
          
          // التعليقات
          if (task.comments.isNotEmpty) ...[
            _buildComprehensiveCommentsSection(task.comments, helper),
            pw.SizedBox(height: 20),
          ],
          
          // المرفقات
          if (task.attachments.isNotEmpty) ...[
            _buildDetailedAttachmentsSection(task.attachments, helper),
            pw.SizedBox(height: 20),
          ],
          
          // المهام الفرعية
          if (task.subtasks.isNotEmpty) ...[
            _buildComprehensiveSubtasksSection(task.subtasks, helper),
            pw.SizedBox(height: 20),
          ],
          
          // الإحصائيات
          _buildComprehensiveStatisticsSection(reportModel.statistics, task, reportModel, helper),
        ];
      },
    ),
  );

  return pdf;
}

// تم إزالة الفئة القديمة _FallbackReportHelper لتجنب التكرار

// ====== HELPER FUNCTIONS ======

/// تحميل الخطوط العربية للـ PDF
Future<Map<String, pw.Font>> _loadEnhancedArabicFonts() async {
  try {
    final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
    return {
      'regular': pw.Font.ttf(fontData),
      'bold': pw.Font.ttf(boldFontData),
    };
  } catch (e) {
    return {
      'regular': pw.Font.helvetica(),
      'bold': pw.Font.helveticaBold(),
    };
  }
}

/// تنسيق التاريخ
String _formatDate(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
}

/// تنسيق التاريخ والوقت
String _formatDateTime(DateTime dateTime) {
  return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
}

// ====== STUB CLASSES/ENUMS ======

/// إحصائيات التقرير
class ReportStatistics {
  int totalComments = 0;
  int totalAttachments = 0;
  int totalContributors = 0;
  int totalTransfers = 0;
  int daysActive = 0;
  String mostActiveContributor = '-';
}

/// مستويات المساهمين
enum ContributorLevel {
  veryActive,
  active,
  medium,
  low;

  String get displayName {
    switch (this) {
      case ContributorLevel.veryActive:
        return 'نشط جداً';
      case ContributorLevel.active:
        return 'نشط';
      case ContributorLevel.medium:
        return 'متوسط';
      case ContributorLevel.low:
        return 'منخفض';
    }
  }
}

/// خدمة API المحسنة للمهام - محسنة ومطورة
class EnhancedTaskApiService {
  
  /// جلب بيانات شاملة لمهمة واحدة لإنشاء التقرير المفصل
  /// 
  /// [taskId] - معرف المهمة
  /// يحاول جلب البيانات من API، وفي حالة الفشل يرجع بيانات تجريبية
  Future<Map<String, dynamic>> getTaskComprehensiveReportData(dynamic taskId) async {
    try {
      // محاولة جلب البيانات من API الحقيقي
      // في حالة عدم توفر API، سيتم استخدام البيانات التجريبية
      
      if (kDebugMode) {
        print('🔄 محاولة جلب البيانات الشاملة للمهمة $taskId');
      }
      
      // TODO: إضافة استدعاء API حقيقي لاحقاً
      // final response = await http.get(Uri.parse('$baseUrl/api/tasks/$taskId/comprehensive-report-data'));
      // if (response.statusCode == 200) {
      //   return json.decode(response.body);
      // }
      
      // إرجاع بيانات فارغة لاستخدام البيانات الأساسية للمهمة
      return <String, dynamic>{};
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في جلب بيانات التقرير: $e');
      }
      
      // في حالة الخطأ، إرجاع بيانات احتياطية أساسية
      return _generateBasicReportData(taskId);
    }
  }
  
  /// إنشاء نموذج TaskReportModel محسن من البيانات المجلبة
  /// 
  /// [data] - البيانات المجلبة من API أو البيانات التجريبية
  TaskReportModel createEnhancedTaskReportModel(Map<String, dynamic> data) {
    try {
      // التحقق من صحة البيانات الأساسية
      if (data.isEmpty || !data.containsKey('id')) {
        throw Exception('بيانات المهمة غير صحيحة أو مفقودة');
      }
      
      // محاولة إنشاء TaskReportModel من البيانات
      return TaskReportModel.fromJson(data);
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ فشل في تحويل البيانات إلى TaskReportModel: $e');
      }
      
      // في حالة الفشل، إنشاء نموذج أساسي
      final basicTask = _createBasicTaskFromData(data);
      return TaskReportModel.fromTask(basicTask);
    }
  }
  
  /// إنشاء بيانات احتياطية أساسية
  Map<String, dynamic> _generateBasicReportData(dynamic taskId) {
    final now = DateTime.now();
    final taskIdInt = taskId is int ? taskId : int.tryParse(taskId.toString()) ?? 1;
    
    return {
      'id': taskIdInt,
      'title': 'مهمة تجريبية رقم $taskIdInt',
      'description': 'وصف تفصيلي للمهمة التجريبية رقم $taskIdInt',
      'note': 'ملاحظات مهمة حول المهمة رقم $taskIdInt',
      'incoming': 'وارد رقم ${taskIdInt + 100}/2024',
      'status': 'في التقدم',
      'priority': 'عالية',
      'completionPercentage': 60 + (taskIdInt % 30),
      'createdAt': now.subtract(Duration(days: 10)).millisecondsSinceEpoch ~/ 1000,
      'startDate': now.subtract(Duration(days: 8)).millisecondsSinceEpoch ~/ 1000,
      'dueDate': now.add(Duration(days: 5)).millisecondsSinceEpoch ~/ 1000,
      'estimatedTime': 480, // 8 ساعات
      'actualTime': 360, // 6 ساعات
      
      // بيانات المنشئ والمكلف
      'creator': {
        'id': 1,
        'name': 'أحمد محمد',
        'email': '<EMAIL>',
        'role': 'admin',
        'createdAt': now.subtract(Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
      },
      'assignee': {
        'id': 2,
        'name': 'سارة أحمد',
        'email': '<EMAIL>',
        'role': 'user',
        'createdAt': now.subtract(Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
      },
      
      // بيانات القسم ونوع المهمة
      'department': {
        'id': 1,
        'name': 'قسم تطوير البرمجيات',
        'description': 'قسم مختص بتطوير التطبيقات',
      },
      'taskType': {
        'id': 1,
        'name': 'مهمة تطوير',
        'description': 'مهمة تطوير ميزة جديدة',
      },
      
      // التعليقات التجريبية
      'taskComments': List.generate(3, (index) => {
        'id': index + 1,
        'content': 'تعليق تجريبي رقم ${index + 1} للمهمة $taskIdInt',
        'createdAt': now.subtract(Duration(days: index + 1)).millisecondsSinceEpoch ~/ 1000,
        'user': {
          'id': index + 1,
          'name': 'مستخدم ${index + 1}',
          'email': 'user${index + 1}@example.com',
          'role': index == 0 ? 'admin' : 'user', // إضافة حقل role المطلوب
          'createdAt': now.subtract(Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
        }
      }),
      
      // المرفقات التجريبية
      'attachments': List.generate(2, (index) => {
        'id': index + 1,
        'fileName': 'ملف_تجريبي_${index + 1}.pdf',
        'fileSize': 1024 * (index + 1) * 100,
        'fileType': 'pdf',
        'uploadedAt': now.subtract(Duration(days: index + 2)).millisecondsSinceEpoch ~/ 1000,
        'uploadedByNavigation': {
          'id': index + 1,
          'name': 'مستخدم ${index + 1}',
          'email': 'user${index + 1}@example.com',
          'role': index == 0 ? 'admin' : 'user', // إضافة حقل role المطلوب
          'createdAt': now.subtract(Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
        }
      }),
      
      // المهام الفرعية التجريبية
      'subtasks': List.generate(3, (index) => {
        'id': index + 1,
        'title': 'مهمة فرعية ${index + 1}',
        'isCompleted': index < 2, // أول مهمتين مكتملتان
        'createdAt': now.subtract(Duration(days: index + 3)).millisecondsSinceEpoch ~/ 1000,
      }),
      
      // المستخدمون المرتبطون
      'accessUsers': List.generate(3, (index) => {
        'id': index + 1,
        'name': 'مستخدم ${index + 1}',
        'email': 'user${index + 1}@example.com',
        'role': index == 0 ? 'مدير' : 'مطور',
      }),
      
      // متتبعات التقدم التجريبية
      'progressTrackers': List.generate(4, (index) => {
        'id': index + 1,
        'title': 'نقطة تقدم ${index + 1}',
        'description': 'وصف نقطة التقدم ${index + 1}',
        'percentage': (index + 1) * 25,
        'date': now.subtract(Duration(days: 8 - index * 2)).toIso8601String(),
        'updatedBy': 'مستخدم ${(index % 3) + 1}',
      }),
      
      // سجلات الوقت التجريبية
      'timeTrackingEntries': List.generate(3, (index) => {
        'id': index + 1,
        'userName': 'مستخدم ${index + 1}',
        'startTime': now.subtract(Duration(days: index + 1, hours: 2)).toIso8601String(),
        'endTime': now.subtract(Duration(days: index + 1)).toIso8601String(),
        'durationMinutes': 120,
        'note': 'عمل على المهمة لمدة ساعتين',
      }),
      
      // المساهمون التجريبيون
      'contributors': List.generate(3, (index) => {
        'id': index + 1,
        'userId': index + 1,
        'userName': 'مساهم ${index + 1}',
        'role': index == 0 ? 'مدير المشروع' : (index == 1 ? 'مطور رئيسي' : 'مطور'),
        'commentsCount': [5, 3, 2][index],
        'attachmentsCount': [2, 1, 1][index],
        'totalContributions': [7, 4, 3][index],
        'percentage': [50.0, 28.6, 21.4][index],
      }),
      
      // التحويلات التجريبية
      'transfers': List.generate(2, (index) => {
        'id': index + 1,
        'fromUserId': index + 1,
        'toUserId': index + 2,
        'fromUserName': 'مستخدم ${index + 1}',
        'toUserName': 'مستخدم ${index + 2}',
        'reason': 'تحويل لتوزيع العبء',
        'timestamp': now.subtract(Duration(days: 5 - index * 2)).millisecondsSinceEpoch ~/ 1000,
        'notes': 'ملاحظات التحويل ${index + 1}',
      }),
      
      // السجل التاريخي التجريبي
      'taskHistories': List.generate(5, (index) => {
        'id': index + 1,
        'taskId': taskIdInt,
        'userId': (index % 3) + 1,
        'action': ['created', 'updated', 'assigned', 'status_changed', 'comment_added'][index],
        'details': 'تفاصيل الإجراء ${index + 1}',
        'timestamp': now.subtract(Duration(days: 9 - index)).millisecondsSinceEpoch ~/ 1000,
        'changedByNavigation': {
          'id': (index % 3) + 1,
          'name': 'مستخدم ${(index % 3) + 1}',
          'email': 'user${(index % 3) + 1}@example.com',
          'role': (index % 3) == 0 ? 'admin' : 'user', // إضافة حقل role المطلوب
          'createdAt': now.subtract(Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
        }
      }),
    };
    
  }
  
  
  /// إنشاء مهمة أساسية من البيانات المتاحة
  Task _createBasicTaskFromData(Map<String, dynamic> data) {
    return Task(
      id: data['id'] ?? 0,
      title: data['title'] ?? 'مهمة غير محددة',
      description: data['description'],
      creatorId: data['creatorId'] ?? 1,
      assigneeId: data['assigneeId'],
      createdAt: data['createdAt'] ?? DateTime.now().millisecondsSinceEpoch ~/ 1000,
      status: data['status'] ?? 'نشطة',
      priority: data['priority'] ?? 'متوسطة',
      completionPercentage: data['completionPercentage'] ?? 0,
    );
  }
}