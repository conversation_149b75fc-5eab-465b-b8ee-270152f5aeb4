import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/notification_models.dart';
import '../services/api/notification_settings_api_service.dart';

/// متحكم إعدادات الإشعارات
class NotificationSettingsController extends GetxController {
  final NotificationSettingsApiService _apiService = NotificationSettingsApiService();

  // قوائم الإعدادات
  final RxList<NotificationSettingModel> _allSettings = <NotificationSettingModel>[].obs;
  final RxList<NotificationSettingModel> _filteredSettings = <NotificationSettingModel>[].obs;
  final RxList<NotificationSettingModel> _userSettings = <NotificationSettingModel>[].obs;

  // الإعداد الحالي
  final Rx<NotificationSettingModel?> _currentSetting = Rx<NotificationSettingModel?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<String?> _categoryFilter = Rx<String?>(null);
  final RxBool _showEnabledOnly = false.obs;

  // إعدادات سريعة
  final RxBool _emailNotifications = true.obs;
  final RxBool _pushNotifications = true.obs;
  final RxBool _smsNotifications = false.obs;
  final RxBool _inAppNotifications = true.obs;

  // Getters
  List<NotificationSettingModel> get allSettings => _allSettings;
  List<NotificationSettingModel> get filteredSettings => _filteredSettings;
  List<NotificationSettingModel> get userSettings => _userSettings;
  NotificationSettingModel? get currentSetting => _currentSetting.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get userFilter => _userFilter.value;
  String? get categoryFilter => _categoryFilter.value;
  bool get showEnabledOnly => _showEnabledOnly.value;
  bool get emailNotifications => _emailNotifications.value;
  bool get pushNotifications => _pushNotifications.value;
  bool get smsNotifications => _smsNotifications.value;
  bool get inAppNotifications => _inAppNotifications.value;

  @override
  void onInit() {
    super.onInit();
    loadAllSettings();
    loadUserSettings();
  }

  /// تحميل جميع إعدادات الإشعارات
  Future<void> loadAllSettings() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final settings = await _apiService.getAllSettings();
      _allSettings.assignAll(settings);
      _applyFilters();
      debugPrint('تم تحميل ${settings.length} إعداد إشعار');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعدادات الإشعارات: $e';
      debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل إعدادات المستخدم الحالي
  Future<void> loadUserSettings() async {
    try {
      final settings = await _apiService.getCurrentUserSettings();
      _userSettings.assignAll(settings);
      _updateQuickSettings(settings.cast<NotificationSetting>());
      debugPrint('تم تحميل ${settings.length} إعداد للمستخدم');
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات المستخدم: $e');
    }
  }

  /// الحصول على إعداد إشعار بالمعرف
  Future<void> getSettingById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final setting = await _apiService.getSettingById(id);
      _currentSetting.value = setting;
      debugPrint('تم تحميل إعداد الإشعار');
    } catch (e) {
      _error.value = 'خطأ في تحميل إعداد الإشعار: $e';
      debugPrint('خطأ في تحميل إعداد الإشعار: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء إعداد إشعار جديد
  Future<bool> createSetting(NotificationSettingModel setting) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newSetting = await _apiService.createSetting(setting);
      _allSettings.add(newSetting);
      _applyFilters();
      debugPrint('تم إنشاء إعداد إشعار جديد');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء إعداد الإشعار: $e';
      debugPrint('خطأ في إنشاء إعداد الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إعداد إشعار
  Future<bool> updateSetting(int id, NotificationSettingModel setting) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedSetting = await _apiService.updateSetting(id, setting);
      final index = _allSettings.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allSettings[index] = updatedSetting;
        _applyFilters();
      }
      debugPrint('تم تحديث إعداد الإشعار');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث إعداد الإشعار: $e';
      debugPrint('خطأ في تحديث إعداد الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف إعداد إشعار
  Future<bool> deleteSetting(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteSetting(id);
      _allSettings.removeWhere((s) => s.id == id);
      _applyFilters();
      debugPrint('تم حذف إعداد الإشعار');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف إعداد الإشعار: $e';
      debugPrint('خطأ في حذف إعداد الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إعداد سريع
  Future<bool> updateQuickSetting(String settingType, bool enabled) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // استخدام الطريقة المناسبة حسب نوع الإعداد
      bool success = false;
      switch (settingType) {
        case 'email':
          success = await _apiService.toggleEmailNotifications(enabled);
          if (success) _emailNotifications.value = enabled;
          break;
        case 'push':
          success = await _apiService.togglePushNotifications(enabled);
          if (success) _pushNotifications.value = enabled;
          break;
        case 'sms':
          success = await _apiService.toggleSmsNotifications(enabled);
          if (success) _smsNotifications.value = enabled;
          break;
        case 'in_app':
          success = await _apiService.toggleNotificationType('in_app', enabled);
          if (success) _inAppNotifications.value = enabled;
          break;
        default:
          success = await _apiService.toggleNotificationType(settingType, enabled);
          break;
      }

      if (success) {
        debugPrint('تم تحديث الإعداد السريع: $settingType = $enabled');
      }
      return success;
    } catch (e) {
      _error.value = 'خطأ في تحديث الإعداد السريع: $e';
      debugPrint('خطأ في تحديث الإعداد السريع: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تفعيل جميع الإشعارات
  Future<bool> enableAllNotifications() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تفعيل كل نوع من الإشعارات بشكل منفصل
      final results = await Future.wait([
        _apiService.toggleEmailNotifications(true),
        _apiService.togglePushNotifications(true),
        _apiService.toggleSmsNotifications(true),
        _apiService.toggleNotificationType('in_app', true),
      ]);

      final allSuccess = results.every((result) => result);
      if (allSuccess) {
        _emailNotifications.value = true;
        _pushNotifications.value = true;
        _smsNotifications.value = true;
        _inAppNotifications.value = true;
        await loadUserSettings();
        debugPrint('تم تفعيل جميع الإشعارات');
      }
      return allSuccess;
    } catch (e) {
      _error.value = 'خطأ في تفعيل جميع الإشعارات: $e';
      debugPrint('خطأ في تفعيل جميع الإشعارات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إيقاف جميع الإشعارات
  Future<bool> disableAllNotifications() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // إيقاف كل نوع من الإشعارات بشكل منفصل
      final results = await Future.wait([
        _apiService.toggleEmailNotifications(false),
        _apiService.togglePushNotifications(false),
        _apiService.toggleSmsNotifications(false),
        _apiService.toggleNotificationType('in_app', false),
      ]);

      final allSuccess = results.every((result) => result);
      if (allSuccess) {
        _emailNotifications.value = false;
        _pushNotifications.value = false;
        _smsNotifications.value = false;
        _inAppNotifications.value = false;
        await loadUserSettings();
        debugPrint('تم إيقاف جميع الإشعارات');
      }
      return allSuccess;
    } catch (e) {
      _error.value = 'خطأ في إيقاف جميع الإشعارات: $e';
      debugPrint('خطأ في إيقاف جميع الإشعارات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعادة تعيين الإعدادات للقيم الافتراضية
  Future<bool> resetToDefaults() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.resetToDefaults();
      await loadUserSettings();
      debugPrint('تم إعادة تعيين الإعدادات للقيم الافتراضية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إعادة تعيين الإعدادات: $e';
      debugPrint('خطأ في إعادة تعيين الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث الإعدادات السريعة من البيانات المحملة
  void _updateQuickSettings(List<NotificationSetting> settings) {
    for (final setting in settings) {
      switch (setting.notificationType.value) {
        case 'email':
          _emailNotifications.value = setting.isEnabled;
          break;
        case 'push':
          _pushNotifications.value = setting.isEnabled;
          break;
        case 'sms':
          _smsNotifications.value = setting.isEnabled;
          break;
        case 'in_app':
          _inAppNotifications.value = setting.isEnabled;
          break;
      }
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allSettings.where((setting) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!setting.notificationType.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && setting.userId != _userFilter.value) {
        return false;
      }

      // مرشح الفئة (نوع الإشعار)
      if (_categoryFilter.value != null && setting.notificationType != _categoryFilter.value) {
        return false;
      }

      // مرشح المفعل فقط
      if (_showEnabledOnly.value && !setting.isEnabled) {
        return false;
      }

      return true;
    }).toList();

    _filteredSettings.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح الفئة
  void setCategoryFilter(String? category) {
    _categoryFilter.value = category;
    _applyFilters();
  }

  /// تعيين مرشح المفعل فقط
  void setEnabledFilter(bool showEnabledOnly) {
    _showEnabledOnly.value = showEnabledOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _userFilter.value = null;
    _categoryFilter.value = null;
    _showEnabledOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllSettings(),
      loadUserSettings(),
    ]);
  }
}
