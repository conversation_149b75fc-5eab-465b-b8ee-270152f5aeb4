import 'package:get/get.dart';
import '../screens/dashboard/core/dashboard_controller.dart';
import '../screens/dashboard/core/dashboard_repository.dart';
import '../controllers/auth_controller.dart';
import '../controllers/task_controller.dart';
import '../controllers/user_controller.dart';
import '../controllers/department_controller.dart';
import '../services/unified_permission_service.dart';

/// ربط التبعيات للوحة المعلومات الموحدة
class UnifiedDashboardBinding extends Bindings {
  @override
  void dependencies() {
    // التأكد من وجود المتحكمات الأساسية
    Get.lazyPut<AuthController>(() => AuthController(), fenix: true);
    Get.lazyPut<TaskController>(() => TaskController(), fenix: true);
    Get.lazyPut<UserController>(() => UserController(), fenix: true);
    Get.lazyPut<DepartmentController>(() => DepartmentController(), fenix: true);
    Get.lazyPut<UnifiedPermissionService>(() => UnifiedPermissionService(), fenix: true);
    
    // مستودع لوحة المعلومات
    Get.lazyPut<DashboardRepository>(() => DashboardRepository(), fenix: true);
    
    // متحكم لوحة المعلومات الموحد
    Get.lazyPut<UnifiedDashboardController>(() => UnifiedDashboardController(), fenix: true);
  }
}
