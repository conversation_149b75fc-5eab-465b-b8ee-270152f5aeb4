import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_comment_models.dart';
import '../services/api/simple_task_comments_api_service.dart';
// import '../services/simple_signalr_service.dart'; // معلق مؤقتاً
import '../services/unified_signalr_service.dart'; // الخدمة الموحدة الجديدة

/// متحكم مبسط لتعليقات المهام مع دعم SignalR
class SimpleTaskCommentsController extends GetxController {
  final SimpleTaskCommentsApiService _apiService = SimpleTaskCommentsApiService();
  UnifiedSignalRService? _signalRService;

  // قائمة التعليقات للمهمة الحالية
  final RxList<TaskComment> _comments = <TaskComment>[].obs;
  
  // حالة التحميل
  final RxBool _isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString _error = ''.obs;
  
  // معرف المهمة الحالية
  final RxInt _currentTaskId = 0.obs;
  
  // حالة اتصال SignalR
  final RxBool _isSignalRConnected = false.obs;

  // مؤقت SignalR
  Timer? _signalRTimer;

  // متغير لتتبع ScrollController من الواجهة
  ScrollController? _scrollController;

  /// تعيين ScrollController من الواجهة
  void setScrollController(ScrollController? controller) {
    _scrollController = controller;
  }

  // Getters
  RxList<TaskComment> get comments => _comments;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  int get currentTaskId => _currentTaskId.value;
  bool get isSignalRConnected => _isSignalRConnected.value;

  @override
  void onInit() {
    super.onInit();
    // تفعيل SignalR للتحديثات الفورية
    debugPrint('🔌 تفعيل SignalR للتحديثات الفورية');
    _initializeSignalR();
  }

  @override
  void onClose() {
    _cleanupSignalR();
    super.onClose();
  }

  /// تهيئة SignalR
  void _initializeSignalR() {
    try {
      _signalRService = Get.find<UnifiedSignalRService>();
    } catch (e) {
      // إذا لم تكن الخدمة مسجلة، قم بإنشائها
      _signalRService = Get.put(UnifiedSignalRService());
    }

    // تحديث حالة الاتصال
    _isSignalRConnected.value = _signalRService?.isTaskCommentsHubConnected ?? false;

    _setupSignalRListeners();
  }

  /// إعداد مستمعي SignalR للتحديثات الفورية
  void _setupSignalRListeners() {
    if (_signalRService == null) return;

    // تحديث حالة الاتصال
    _isSignalRConnected.value = _signalRService!.isTaskCommentsHubConnected;

    // مراقبة التعليقات الجديدة فورياً مع تحديث أقل تكراراً (كل 3 ثوانٍ بدلاً من 500ms)
    _signalRTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_signalRService == null || !Get.isRegistered<SimpleTaskCommentsController>()) {
        timer.cancel();
        return;
      }

      // تحديث حالة الاتصال
      _isSignalRConnected.value = _signalRService!.isTaskCommentsHubConnected;

      // فحص التعليقات الجديدة
      final newComments = _signalRService!.newComments;
      for (final comment in newComments) {
        if (comment.taskId == _currentTaskId.value) {
          if (!_comments.any((c) => c.id == comment.id)) {
            _comments.add(comment);
            // ترتيب التعليقات حسب التاريخ
            _comments.sort((a, b) => a.createdAt.compareTo(b.createdAt));
            update(); // تحديث الواجهة فوراً
            // التمرير التلقائي إلى أسفل عند وصول تعليق جديد
            // تأخير إضافي للتأكد من اكتمال تحديث الواجهة
            Future.delayed(const Duration(milliseconds: 50), () {
              _scrollToBottomAfterNewComment();
            });
            debugPrint('📨 تم إضافة تعليق جديد من SignalR: ${comment.id}');
          }
        }
      }

      // فحص التعليقات المحدثة
      final updatedComments = _signalRService!.updatedComments;
      for (final comment in updatedComments) {
        if (comment.taskId == _currentTaskId.value) {
          final index = _comments.indexWhere((c) => c.id == comment.id);
          if (index != -1) {
            _comments[index] = comment;
            update(); // تحديث الواجهة فوراً
            debugPrint('✏️ تم تحديث تعليق من SignalR: ${comment.id}');
          }
        }
      }

      // فحص التعليقات المحذوفة
      final deletedIds = _signalRService!.deletedCommentIds;
      for (final commentId in deletedIds) {
        final initialLength = _comments.length;
        _comments.removeWhere((c) => c.id == commentId);
        if (_comments.length < initialLength) {
          update(); // تحديث الواجهة فوراً
          debugPrint('🗑️ تم حذف تعليق من SignalR: $commentId');
        }
      }

      // مسح الأحداث بعد المعالجة
      if (newComments.isNotEmpty || updatedComments.isNotEmpty || deletedIds.isNotEmpty) {
        _signalRService!.clearEvents();
      }
    });
  }

  /// تنظيف SignalR
  void _cleanupSignalR() {
    // إلغاء المؤقت لمنع الاستعلامات المتكررة
    _signalRTimer?.cancel();

    if (_currentTaskId.value > 0) {
      _signalRService?.leaveTaskCommentsGroup(_currentTaskId.value);
    }
    _isSignalRConnected.value = false;
  }

  /// تحميل تعليقات مهمة محددة
  Future<void> loadComments(int taskId) async {
    try {
      _isLoading.value = true;
      _error.value = '';
      
      debugPrint('🔄 تحميل تعليقات المهمة: $taskId');
      
      // تحديث معرف المهمة الحالية
      _currentTaskId.value = taskId;

      // الانضمام لمجموعة تعليقات المهمة عبر SignalR
      await _signalRService?.joinTaskCommentsGroup(taskId);
      
      // تحميل التعليقات من API
      final comments = await _apiService.getCommentsByTask(taskId);
      _comments.assignAll(comments);
      
      // تحديث الواجهة
      update();
      
      debugPrint('✅ تم تحميل ${comments.length} تعليق');
    } catch (e) {
      _error.value = 'خطأ في تحميل التعليقات: $e';
      debugPrint('❌ خطأ في تحميل التعليقات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة تعليق جديد
  Future<bool> addComment(int taskId, int userId, String content) async {
    if (content.trim().isEmpty) {
      _error.value = 'لا يمكن إرسال تعليق فارغ';
      return false;
    }

    try {
      _isLoading.value = true;
      _error.value = '';
      
      debugPrint('📝 إضافة تعليق جديد للمهمة: $taskId');
      
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final comment = TaskComment(
        id: 0, // سيتم تعيينه من الخادم
        taskId: taskId,
        userId: userId,
        content: content.trim(),
        createdAt: now,
        updatedAt: now,
        isDeleted: false,
      );

      final newComment = await _apiService.createComment(comment);
      
      // إضافة التعليق محلياً (SignalR معطل مؤقتاً)
      _comments.add(newComment);

      // تحديث الواجهة
      update();

      // التمرير التلقائي إلى أسفل بعد إضافة تعليق جديد (دائماً للمرسل)
      Future.delayed(const Duration(milliseconds: 150), () {
        _scrollToBottomAfterSendingComment();
      });
      
      // إرسال التعليق عبر SignalR للتحديث الفوري
      await _signalRService?.sendTaskComment(taskId, newComment);
      
      debugPrint('✅ تم إضافة التعليق بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة التعليق: $e';
      debugPrint('❌ خطأ في إضافة التعليق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث تعليق
  Future<bool> updateComment(int commentId, String newContent) async {
    if (newContent.trim().isEmpty) {
      _error.value = 'لا يمكن حفظ تعليق فارغ';
      return false;
    }

    try {
      _isLoading.value = true;
      _error.value = '';
      
      debugPrint('✏️ تحديث التعليق: $commentId');
      
      final commentIndex = _comments.indexWhere((c) => c.id == commentId);
      if (commentIndex == -1) {
        _error.value = 'التعليق غير موجود';
        return false;
      }

      final oldComment = _comments[commentIndex];
      final updatedComment = oldComment.copyWith(
        content: newContent.trim(),
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      final result = await _apiService.updateComment(commentId, updatedComment);
      _comments[commentIndex] = result;
      
      // تحديث الواجهة
      update();
      
      debugPrint('✅ تم تحديث التعليق بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث التعليق: $e';
      debugPrint('❌ خطأ في تحديث التعليق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف تعليق
  Future<bool> deleteComment(int commentId) async {
    try {
      _isLoading.value = true;
      _error.value = '';
      
      debugPrint('🗑️ حذف التعليق: $commentId');
      
      final success = await _apiService.deleteComment(commentId);
      if (success) {
        _comments.removeWhere((c) => c.id == commentId);
        
        // تحديث الواجهة
        update();
        
        debugPrint('✅ تم حذف التعليق بنجاح');
        return true;
      } else {
        _error.value = 'فشل في حذف التعليق';
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في حذف التعليق: $e';
      debugPrint('❌ خطأ في حذف التعليق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث حالة اتصال SignalR
  void updateSignalRConnectionStatus(bool isConnected) {
    _isSignalRConnected.value = isConnected;
  }

  /// إعادة تحميل التعليقات
  Future<void> refreshComments(int taskId) async {
    await loadComments(taskId);
  }

  /// التمرير التلقائي إلى أسفل عند وصول تعليق جديد
  /// محسن ليعمل مثل محادثات المهام
  void _scrollToBottomAfterNewComment() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      debugPrint('ScrollController غير متاح للتمرير التلقائي في التعليقات');
      return;
    }

    // تأخير قصير للتأكد من اكتمال تحديث القائمة
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController != null && _scrollController!.hasClients) {
        try {
          // التحقق من أن المستخدم قريب من أسفل القائمة قبل التمرير التلقائي
          final position = _scrollController!.position;
          final maxExtent = position.maxScrollExtent;
          final currentPosition = position.pixels;
          final threshold = 100.0; // المسافة المسموحة من الأسفل

          // إذا كان المستخدم قريب من الأسفل، قم بالتمرير التلقائي
          if (maxExtent - currentPosition <= threshold) {
            _scrollController!.animateTo(
              maxExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
            debugPrint('تم التمرير التلقائي إلى أسفل بعد وصول تعليق جديد - المسافة: $maxExtent');
          } else {
            debugPrint('المستخدم بعيد عن الأسفل، لم يتم التمرير التلقائي للتعليقات');
          }
        } catch (e) {
          debugPrint('خطأ في التمرير التلقائي للتعليقات: $e');
          // محاولة بديلة باستخدام jumpTo
          try {
            final maxExtent = _scrollController!.position.maxScrollExtent;
            _scrollController!.jumpTo(maxExtent);
            debugPrint('تم التمرير البديل للتعليقات إلى: $maxExtent');
          } catch (e2) {
            debugPrint('خطأ في التمرير البديل للتعليقات: $e2');
          }
        }
      }
    });
  }

  /// التمرير التلقائي إلى أسفل بعد إرسال تعليق (دائماً للمرسل)
  void _scrollToBottomAfterSendingComment() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      debugPrint('ScrollController غير متاح للتمرير بعد إرسال التعليق');
      return;
    }

    try {
      // التمرير إلى أسفل دائماً عند إرسال تعليق
      final maxExtent = _scrollController!.position.maxScrollExtent;
      _scrollController!.animateTo(
        maxExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      debugPrint('تم التمرير التلقائي بعد إرسال تعليق - المسافة: $maxExtent');
    } catch (e) {
      debugPrint('خطأ في التمرير بعد إرسال التعليق: $e');
      // محاولة بديلة باستخدام jumpTo
      try {
        final maxExtent = _scrollController!.position.maxScrollExtent;
        _scrollController!.jumpTo(maxExtent);
      } catch (e2) {
        debugPrint('خطأ في التمرير البديل بعد إرسال التعليق: $e2');
      }
    }
  }

  /// تنفيذ refresh من GetxController
  @override
  void refresh() {
    super.refresh();
  }
}