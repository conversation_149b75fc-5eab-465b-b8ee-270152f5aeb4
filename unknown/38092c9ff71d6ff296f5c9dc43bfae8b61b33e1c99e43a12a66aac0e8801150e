import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/department_model.dart';

/// فلتر مخصص لتقرير المهام الشامل
class TaskReportFilterDialog extends StatefulWidget {
  final List<User> users;
  final List<Department> departments;
  final List<String>? initialSelectedUserIds;
  final List<String>? initialSelectedDepartmentIds;
  final List<String>? initialSelectedStatuses;
  final DateTime? initialDueDateFrom;
  final DateTime? initialDueDateTo;
  final Function(List<String> userIds, List<String> departmentIds, List<String> statuses, DateTime? dueDateFrom, DateTime? dueDateTo) onFilter;

  const TaskReportFilterDialog({
    super.key,
    required this.users,
    required this.departments,
    this.initialSelectedUserIds,
    this.initialSelectedDepartmentIds,
    this.initialSelectedStatuses,
    this.initialDueDateFrom,
    this.initialDueDateTo,
    required this.onFilter,
  });

  @override
  State<TaskReportFilterDialog> createState() => _TaskReportFilterDialogState();
}

class _TaskReportFilterDialogState extends State<TaskReportFilterDialog> {
  late Set<String> _selectedUserIds;
  late Set<String> _selectedDepartmentIds;
  late Set<String> _selectedStatuses;
  late TextEditingController _searchController;
  DateTime? _dueDateFrom;
  DateTime? _dueDateTo;

  final List<String> _allStatuses = [
    'pending', 'in_progress', 'completed', 'delayed', 'cancelled', 'waiting_for_info',
  ];

  @override
  void initState() {
    super.initState();
    _selectedUserIds = Set<String>.from(widget.initialSelectedUserIds ?? []);
    _selectedDepartmentIds = Set<String>.from(widget.initialSelectedDepartmentIds ?? []);
    _selectedStatuses = Set<String>.from(widget.initialSelectedStatuses ?? []);
    _searchController = TextEditingController();
    _dueDateFrom = widget.initialDueDateFrom;
    _dueDateTo = widget.initialDueDateTo;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<User> _getFilteredUsers() {
    if (_searchController.text.isEmpty) return widget.users;
    final searchText = _searchController.text.toLowerCase();
    return widget.users.where((user) =>
      user.name.toLowerCase().contains(searchText) ||
      (user.email?.toLowerCase().contains(searchText) ?? false)
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    final filteredUsers = _getFilteredUsers();
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('فلترة تقرير المهام', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'بحث عن مستخدم',
                      prefixIcon: Icon(Icons.search),
                    ),
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: null,
                    decoration: const InputDecoration(labelText: 'القسم'),
                    onChanged: (value) {
                      setState(() {
                        if (value == null) {
                          _selectedDepartmentIds.clear();
                        } else {
                          _selectedDepartmentIds = {value};
                        }
                      });
                    },
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('جميع الأقسام'),
                      ),
                      ...widget.departments.map((department) => DropdownMenuItem<String?>(
                        value: department.id.toString(),
                        child: Text(department.name),
                      )),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // اختيار تاريخ الاستحقاق
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _dueDateFrom ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() {
                          _dueDateFrom = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(labelText: 'تاريخ الاستحقاق من'),
                      child: Text(_dueDateFrom != null ? _dueDateFrom!.toString().split(' ')[0] : 'غير محدد'),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _dueDateTo ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() {
                          _dueDateTo = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(labelText: 'تاريخ الاستحقاق إلى'),
                      child: Text(_dueDateTo != null ? _dueDateTo!.toString().split(' ')[0] : 'غير محدد'),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // اختيار المستخدمين
            Expanded(
              child: filteredUsers.isEmpty
                  ? const Center(child: Text('لا يوجد مستخدمون'))
                  : ListView.builder(
                      itemCount: filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = filteredUsers[index];
                        final isSelected = _selectedUserIds.contains(user.id.toString());
                        return CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedUserIds.add(user.id.toString());
                              } else {
                                _selectedUserIds.remove(user.id.toString());
                              }
                            });
                          },
                          title: Text(user.name),
                          subtitle: Text(user.email ?? 'غير محدد'),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),
            // اختيار الحالات
            Wrap(
              spacing: 8,
              children: _allStatuses.map((status) => FilterChip(
                label: Text(_statusLabel(status)),
                selected: _selectedStatuses.contains(status),
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedStatuses.add(status);
                    } else {
                      _selectedStatuses.remove(status);
                    }
                  });
                },
              )).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    widget.onFilter(
                      _selectedUserIds.toList(),
                      _selectedDepartmentIds.toList(),
                      _selectedStatuses.toList(),
                      _dueDateFrom,
                      _dueDateTo,
                    );
                    Navigator.of(context).pop();
                  },
                  child: const Text('تأكيد الفلترة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _statusLabel(String status) {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'in_progress': return 'قيد التنفيذ';
      case 'completed': return 'منجزة';
      case 'delayed': return 'متأخرة';
      case 'cancelled': return 'ملغاة';
      case 'waiting_for_info': return 'في انتظار معلومات';
      default: return status;
    }
  }
}

/// دالة مساعدة لعرض فلتر تقرير المهام
Future<Map<String, dynamic>?> showTaskReportFilterDialog({
  required BuildContext context,
  required List<User> users,
  required List<Department> departments,
  DateTime? initialDueDateFrom,
  DateTime? initialDueDateTo,
}) async {
  Map<String, dynamic>? result;
  await showDialog(
    context: context,
    builder: (context) => TaskReportFilterDialog(
      users: users,
      departments: departments,
      initialDueDateFrom: initialDueDateFrom,
      initialDueDateTo: initialDueDateTo,
      onFilter: (userIds, departmentIds, statuses, dueDateFrom, dueDateTo) {
        result = {
          'userIds': userIds,
          'departmentIds': departmentIds,
          'statuses': statuses,
          'dueDateFrom': dueDateFrom,
          'dueDateTo': dueDateTo,
        };
      },
    ),
  );
  return result;
}
