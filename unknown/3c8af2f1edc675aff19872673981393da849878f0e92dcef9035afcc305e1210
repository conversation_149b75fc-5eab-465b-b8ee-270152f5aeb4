import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/notifications_controller.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../routes/app_routes.dart';
import '../../services/unified_permission_service.dart';
import '../../utils/permission_debug_helper.dart';

/// قائمة جانبية موحدة للتطبيق
class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationsController>();
    final permissionService = Get.find<UnifiedPermissionService>();
    final currentUser = authController.currentUser.value;

    if (currentUser == null) {
      return const SizedBox.shrink();
    }

    return Drawer(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // رأس القائمة
            UserAccountsDrawerHeader(
              accountName: Text(
                currentUser.name,
                style: AppStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              accountEmail: Text(
                currentUser.email,
                style: AppStyles.bodyMedium.copyWith(
                  color: Colors.white,
                ),
              ),
              currentAccountPicture: CircleAvatar(
                backgroundColor: Colors.white,
                child: Text(
                  currentUser.name.isNotEmpty
                      ? currentUser.name[0].toUpperCase()
                      : '?',
                  style: AppStyles.headingLarge.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
              ),
            ),

            // البحث الشامل
            if (permissionService.canAccessSearch())
              ListTile(
                leading: const Icon(Icons.search),
                title: const Text('البحث الشامل'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.unifiedSearch);
                },
              ),

            // الصفحة الرئيسية
            // if (permissionService.canAccessDashboard())
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text('home'.tr),
              onTap: () {
                Get.offAllNamed(AppRoutes.home);
              },
            ),

            // المهام - مع تشخيص مفصل
            Builder(
              builder: (context) {
                final canAccess = permissionService.canAccessTasks();
                final canViewAll = permissionService.canViewAllTasks();

                // // طباعة معلومات التشخيص المفصلة
                // debugPrint('🔍 تشخيص المهام في القائمة الجانبية:');
                // debugPrint('  canAccessTasks(): $canAccess');
                // debugPrint('  canViewAllTasks(): $canViewAll');
                // debugPrint('  hasPermission("tasks.view"): ${permissionService.hasPermission("tasks.view")}');
                // debugPrint('  hasPermission("tasks.view_all"): ${permissionService.hasPermission("tasks.view_all")}');
                // debugPrint('  إجمالي الصلاحيات: ${permissionService.userPermissions.length}');

                // فحص مباشر للصلاحية في القاموس
                final userPerms = permissionService.userPermissions;
                final hasViewAll = userPerms.containsKey('tasks.view_all');
                final viewAllValue = userPerms['tasks.view_all'];
                debugPrint('  tasks.view_all في القاموس: $hasViewAll');
                debugPrint('  قيمة tasks.view_all: $viewAllValue');
                debugPrint('  نوع القيمة: ${viewAllValue.runtimeType}');

                if (!canAccess) {
                  // إذا لم تكن هناك صلاحية، أظهر عنصر تشخيص
                  return ExpansionTile(
                    leading: const Icon(Icons.task, color: Colors.grey),
                    title: const Text('المهام (غير متاح)'),
                    subtitle: const Text('لا توجد صلاحية للوصول'),
                    children: [
                      if (permissionService.canTestPermissions())
                        ListTile(
                          contentPadding: const EdgeInsets.only(right: 32),
                          leading: const Icon(Icons.bug_report, color: Colors.orange),
                          title: const Text('تشخيص المشكلة'),
                          subtitle: const Text('فحص سبب عدم ظهور المهام'),
                          onTap: () {
                            PermissionDebugHelper.diagnoseDrawerIssue();
                            PermissionDebugHelper.fixMissingTaskPermissions();
                            Get.snackbar(
                              'تشخيص المهام',
                              'تم تشخيص المشكلة. تحقق من وحدة التحكم للتفاصيل.',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: Colors.orange.shade100,
                              colorText: Colors.orange.shade800,
                              duration: const Duration(seconds: 4),
                            );
                          },
                        ),
                    ],
                  );
                }

                return ExpansionTile(
                  leading: const Icon(Icons.task),
                  title: Text('tasks'.tr),
                  children: [
                    // المهام - يظهر إذا كان لديك أي صلاحية مهام
                    if (canAccess)
                      ListTile(
                        contentPadding: const EdgeInsets.only(right: 32),
                        leading: const Icon(Icons.list),
                        title: Text(canViewAll ? 'جميع المهام' : 'المهام'),
                        subtitle: canViewAll ? null : const Text('المهام المتاحة لك'),
                        onTap: () {
                          Get.back();
                          Get.toNamed(AppRoutes.tasks);
                        },
                      ),
                    // تذكيرات المهام
                    if (canAccess)
                      ListTile(
                        contentPadding: const EdgeInsets.only(right: 32),
                        leading: const Icon(Icons.schedule_outlined),
                        title: const Text('تذكيرات المهام'),
                        onTap: () {
                          Get.back();
                          Get.toNamed(AppRoutes.taskReminders);
                        },
                      ),
                    // إنشاء مهمة جديدة
                    if (permissionService.canCreateTask())
                      ListTile(
                        contentPadding: const EdgeInsets.only(right: 32),
                        leading: const Icon(Icons.add_task),
                        title: const Text('إنشاء مهمة جديدة'),
                        onTap: () {
                          Get.back();
                          Get.toNamed(AppRoutes.createTask);
                        },
                      ),
                  ],
                );
              },
            ),

            // الأقسام
            if (permissionService.canViewDepartments())
              ListTile(
                leading: const Icon(Icons.business),
                title: Text('departments'.tr),
                onTap: permissionService.canViewDepartments()
                    ? () {
                        Get.back();
                        // التنقل إلى شاشة الأقسام
                        Get.toNamed(AppRoutes.departmentDashboard);
                        // تغيير التبويب إلى الأقسام (index 2)
                        if (Get.isRegistered(tag: 'HomeScreenState')) {
                          final homeScreenState = Get.find(tag: 'HomeScreenState');
                          homeScreenState.changeTab(2);
                        }
                      }
                    : null,
              ),

            // المحادثات
            if (permissionService.canAccessChat())
              ListTile(
                leading: const Icon(Icons.chat),
                title: Text('messages'.tr),
                onTap: permissionService.canAccessChat()
                    ? () {
                        Get.back();
                        Get.toNamed(AppRoutes.unifiedChatList);
                      }
                    : null,
              ),
   
            // التقارير
            if (permissionService.canAccessReports())
              ExpansionTile(
                leading: const Icon(Icons.bar_chart),
                title: Text('reports'.tr),
                children: [
                  // نظام التقارير الجديد
                  if (permissionService.canAccessReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.dashboard),
                      title: const Text('نظام التقارير الجديد'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.reportingDashboard);
                      },
                    ),
                  // تقارير Monday.com
                  if (permissionService.canAccessReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.insights),
                      title: const Text('تقارير Monday.com'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.mondayStyleReports);
                      },
                    ),
                  // التقارير الرئيسية
                  if (permissionService.canAccessReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.summarize),
                      title: const Text('التقارير الرئيسية'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.reports);
                      },
                    ),
                  // التقارير الثابتة
                  if (permissionService.canAccessReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.description),
                      title: const Text('التقارير الثابتة'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.staticReports);
                      },
                    ),
                  // التقارير المحسنة
                  if (permissionService.canAccessAdvancedReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.analytics_outlined),
                      title: const Text('التقارير المحسنة'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.enhancedReports);
                      },
                    ),
                  // الرسوم البيانية المحسنة
                  if (permissionService.canCreateCustomReport())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.area_chart),
                      title: const Text('الرسوم البيانية المحسنة'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.enhancedCharts);
                      },
                    ),
                  // تقارير PDF
                  if (permissionService.canPrintReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.picture_as_pdf),
                      title: const Text('تقارير PDF'),
                      onTap: () {
                        Get.back();
                        Get.toNamed('/task-pdf-report');
                      },
                    ),
                  // نظام التقارير الاحترافية
                  if (permissionService.canAccessAdvancedReports())
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.shade400,
                            Colors.blue.shade600,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            Get.back();
                            Get.toNamed(AppRoutes.professionalReports);
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: const Icon(
                                    Icons.analytics,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Expanded(
                                  child: Text(
                                    '🚀 التقارير الاحترافية',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Text(
                                    'جديد',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),

            // التقويم - تصميم محسن
            if (permissionService.canManageCalendar())
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withAlpha(179), // 0.7 * 255 = 179
                      AppColors.primary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withAlpha(77), // 0.3 * 255 = 77
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.calendar);
                    },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.calendar_month,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'التقويم',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              _buildCalendarEventsText(),
                            ],
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // لوحة المعلومات
            if (permissionService.canAccessDashboard())
              ExpansionTile(
                leading: const Icon(Icons.dashboard_customize),
                title: const Text('لوحة المعلومات'),
                children: [
                  // لوحة المعلومات القابلة للتخصيص
                  if (permissionService.canCustomizeDashboard())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.dashboard),
                      title: const Text('لوحة المعلومات التقليدية'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.customizableDashboard);
                      },
                    ),
                  // لوحة المعلومات الجديدة
                  if (permissionService.canAccessDashboard())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.dashboard_customize),
                      title: const Text('لوحة المعلومات الجديدة'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.newDashboard);
                      },
                    ),
                  // لوحة معلومات Monday.com
                  if (permissionService.canAccessDashboard())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.view_module),
                      title: const Text('لوحة معلومات Monday.com'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.mondayDashboard);
                      },
                    ),
                  // لوحة معلومات المهام
                  if (permissionService.canAccessTasks())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.task_outlined),
                      title: const Text('لوحة معلومات المهام'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.taskStats);
                      },
                    ),
                ],
              ),

            // باور بي آي
            if (permissionService.canAccessPowerBI())
              ExpansionTile(
                leading: const Icon(Icons.analytics),
                title: const Text('باور بي آي'),
                children: [
                  // تقارير باور بي آي
                  if (permissionService.canAccessPowerBI())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.dashboard),
                      title: const Text('تقارير باور بي آي'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.powerBI);
                      },
                    ),
                  // التقارير الديناميكية
                  if (permissionService.canCreatePowerBIReports())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.auto_graph),
                      title: const Text('التقارير الديناميكية'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.dynamicPowerBI);
                      },
                    ),
                ],
              ),

            // الإشعارات
            if (permissionService.canViewNotifications())
              ListTile(
                leading: Stack(
                  alignment: Alignment.center,
                  children: [
                    const Icon(Icons.notifications),
                    Obx(() {
                      if (notificationController.unreadCount > 0) {
                        return Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              color: AppColors.error,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 10,
                              minHeight: 10,
                            ),
                            child: Text(
                              notificationController.unreadCount > 9
                                  ? '9+'
                                  : notificationController.unreadCount.toString(),
                              style: TextStyle(
                                color:
                                    Get.isDarkMode ? Colors.black : Colors.white,
                                fontSize: 8,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    }),
                  ],
                ),
                title: Text('notifications'.tr),
                onTap: () {
                  Get.back();
                  // التنقل إلى شاشة الإشعارات
                  Get.toNamed(AppRoutes.notifications);
                  // تغيير التبويب إلى الإشعارات (index 5)
                  if (Get.isRegistered(tag: 'HomeScreenState')) {
                    final homeScreenState = Get.find(tag: 'HomeScreenState');
                    homeScreenState.changeTab(5);
                  }
                },
              ),

            // المكونات المتقدمة
            if (permissionService.canAccessAdmin())
              ListTile(
                leading: const Icon(Icons.widgets),
                title: const Text('المكونات المتقدمة'),
                onTap: () {
                  Get.back();
                  Get.toNamed('/syncfusion-showcase');
                },
              ),

            // المستندات النصية
            if (permissionService.canViewDocuments())
              ExpansionTile(
                leading: const Icon(Icons.description),
                title: const Text('المستندات النصية'),
                children: [
                  // قائمة المستندات
                  if (permissionService.canViewDocuments())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.list),
                      title: const Text('قائمة المستندات'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.textDocumentsList);
                      },
                    ),
                  // إنشاء مستند جديد
                  if (permissionService.canCreateDocuments())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.add),
                      title: const Text('إنشاء مستند جديد'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.textDocumentEditor);
                      },
                    ),
                ],
              ),

            // نظام الأرشفة الإلكترونية
            if (permissionService.canAccessArchive())
              ExpansionTile(
                leading: const Icon(Icons.archive),
                title: const Text('الأرشفة الإلكترونية'),
                children: [
                  // الصفحة الرئيسية للأرشيف
                  if (permissionService.canAccessArchive())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.home),
                      title: const Text('الرئيسية'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.archiveHome);
                      },
                    ),
                  // تصفح الوثائق
                  if (permissionService.canAccessArchive())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.search),
                      title: const Text('تصفح الوثائق'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.documentBrowser);
                      },
                    ),
                  // رفع وثيقة جديدة
                  if (permissionService.canUploadToArchive())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.upload_file),
                      title: const Text('رفع وثيقة جديدة'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.documentUpload);
                      },
                    ),
                  // إدارة التصنيفات
                  if (permissionService.canManageArchiveCategories())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.category),
                      title: const Text('إدارة التصنيفات'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.categoryManagement);
                      },
                    ),
                  // إدارة الوسوم
                  if (permissionService.canManageTags())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.local_offer),
                      title: const Text('إدارة الوسوم'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.tagManagement);
                      },
                    ),
                ],
              ),

            const Divider(),

            // إعدادات التزامن
            if (permissionService.canManageSettings())
              ListTile(
                leading: const Icon(Icons.sync),
                title: const Text('إعدادات التزامن'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.syncSettings);
                },
              ),


            // لوحة التحكم الإدارية
            if (permissionService.canAccessAdmin())
              ListTile(
                leading: const Icon(Icons.admin_panel_settings),
                title: Text('adminDashboard'.tr),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.admin);
                },
              ),


            // إصلاح قاعدة البيانات
            if (permissionService.canManageDatabase())
              ListTile(
                leading: const Icon(Icons.build),
                title: const Text('إصلاح قاعدة البيانات'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.databaseRepair);
                },
              ),

            // تشخيص المشاكل
            if (permissionService.canAccessAdmin())
              ListTile(
                leading: const Icon(Icons.medical_services),
                title: const Text('تشخيص المشاكل'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.diagnostics);
                },
              ),

            // أدوات الاختبار
            if (permissionService.canTestPermissions())
              ExpansionTile(
                leading: const Icon(Icons.science, color: Colors.orange),
                title: const Text('🧪 أدوات الاختبار'),
                children: [
                  // اختبار نظام التخزين المحسن
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.cloud_upload, color: Colors.blue),
                      title: const Text('نظام التخزين المحسن'),
                      subtitle: const Text('اختبار رفع الملفات والإحصائيات'),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.fileStorageTest);
                      },
                    ),

                  // اختبار الصلاحيات
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.security, color: Colors.purple),
                      title: const Text('🔐 اختبار الصلاحيات'),
                      subtitle: const Text('اختبار الصلاحيات الـ 174 الجديدة'),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          'جديد',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      onTap: () {
                        Get.back();
                        Get.toNamed(AppRoutes.permissionsTest);
                      },
                    ),

                  // تشخيص صلاحيات المهام
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.bug_report, color: Colors.red),
                      title: const Text('🔍 تشخيص الصلاحيات'),
                      subtitle: const Text('فحص صلاحيات المهام والتنقل'),
                      onTap: () {
                        Get.back();
                        // تشغيل التشخيص الشامل
                        PermissionDebugHelper.fullDiagnostic();

                        // عرض رسالة للمستخدم
                        Get.snackbar(
                          'تشخيص الصلاحيات',
                          'تم تشغيل التشخيص الشامل. تحقق من وحدة التحكم للنتائج.',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.blue.shade100,
                          colorText: Colors.blue.shade800,
                          duration: const Duration(seconds: 3),
                        );
                      },
                    ),

                  // تشخيص مشكلة زر تحويل المهمة
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.compare_arrows, color: Colors.orange),
                      title: const Text('🔧 تشخيص زر التحويل'),
                      subtitle: const Text('فحص مشكلة عدم ظهور زر تحويل المهمة'),
                      onTap: () async {
                        Get.back();

                        // تشغيل التشخيص المخصص
                        debugPrint('🔍 === تشخيص مشكلة زر تحويل المهمة ===');

                        // فحص الصلاحيات
                        final canTransfer = permissionService.canTransferTask();
                        final canViewDetails = permissionService.canViewTaskDetails();

                        debugPrint('🔑 canTransferTask(): $canTransfer');
                        debugPrint('🔑 canViewTaskDetails(): $canViewDetails');

                        // فحص الصلاحيات الخام
                        final hasTransfer = permissionService.hasPermission('tasks.transfer');
                        final hasViewDetails = permissionService.hasPermission('tasks.view_details');

                        debugPrint('🔑 hasPermission("tasks.transfer"): $hasTransfer');
                        debugPrint('🔑 hasPermission("tasks.view_details"): $hasViewDetails');

                        // اختبار عبر API
                        final apiTransfer = await permissionService.checkPermissionAsync('tasks.transfer');
                        final apiViewDetails = await permissionService.checkPermissionAsync('tasks.view_details');

                        debugPrint('🌐 API tasks.transfer: $apiTransfer');
                        debugPrint('🌐 API tasks.view_details: $apiViewDetails');

                        // عرض جميع الصلاحيات المتعلقة بالمهام
                        debugPrint('📋 صلاحيات المهام المحملة:');
                        permissionService.userPermissions.forEach((key, value) {
                          if (key.contains('task')) {
                            debugPrint('  ${value ? "✅" : "❌"} $key');
                          }
                        });

                        // عرض النتيجة
                        String message;
                        Color backgroundColor;

                        if (canTransfer && canViewDetails) {
                          message = 'جميع الصلاحيات متوفرة! يجب أن تعمل الأزرار بشكل طبيعي.';
                          backgroundColor = Colors.green.shade100;
                        } else {
                          message = 'توجد مشكلة في الصلاحيات. تحقق من وحدة التحكم للتفاصيل.';
                          backgroundColor = Colors.red.shade100;
                        }

                        Get.snackbar(
                          'تشخيص زر التحويل',
                          message,
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: backgroundColor,
                          colorText: canTransfer && canViewDetails ? Colors.green.shade800 : Colors.red.shade800,
                          duration: const Duration(seconds: 5),
                        );
                      },
                    ),

                  // تشخيص مشكلة الانتقال لتفاصيل المهمة
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.visibility_off, color: Colors.purple),
                      title: const Text('🔍 تشخيص الانتقال للمهام'),
                      subtitle: const Text('فحص مشكلة عدم القدرة على فتح تفاصيل المهمة'),
                      onTap: () async {
                        Get.back();

                        debugPrint('🔍 === تشخيص مشكلة الانتقال لتفاصيل المهمة ===');

                        // فحص المسار الحالي
                        final currentRoute = Get.currentRoute;
                        debugPrint('📍 المسار الحالي: $currentRoute');

                        // فحص صلاحية عرض تفاصيل المهمة
                        final canViewDetails = permissionService.canViewTaskDetails();
                        debugPrint('🔑 canViewTaskDetails(): $canViewDetails');

                        // فحص الصلاحية الخام
                        final hasViewDetails = permissionService.hasPermission('tasks.view_details');
                        debugPrint('🔑 hasPermission("tasks.view_details"): $hasViewDetails');

                        // اختبار عبر API
                        final apiViewDetails = await permissionService.checkPermissionAsync('tasks.view_details');
                        debugPrint('🌐 API tasks.view_details: $apiViewDetails');

                        // فحص صلاحيات أخرى متعلقة بالعرض
                        final canViewAllTasks = permissionService.canViewAllTasks();
                        debugPrint('🔑 canViewAllTasks(): $canViewAllTasks');

                        // فحص TaskController
                        try {
                          if (Get.isRegistered<dynamic>()) {
                            debugPrint('📝 Controllers متوفرة في GetX');
                          }
                          debugPrint('📝 فحص Controllers مكتمل');
                        } catch (e) {
                          debugPrint('📝 خطأ في فحص Controllers: $e');
                        }

                        // اختبار الانتقال
                        debugPrint('🧪 اختبار الانتقال لشاشة تفاصيل المهمة...');

                        try {
                          // محاولة الانتقال لشاشة تفاصيل مهمة وهمية
                          Get.toNamed('/task-detail', arguments: {'taskId': 1});
                          debugPrint('✅ الانتقال نجح');
                        } catch (e) {
                          debugPrint('❌ فشل الانتقال: $e');
                        }

                        // عرض النتيجة
                        String message;
                        Color backgroundColor;

                        if (canViewDetails) {
                          message = 'صلاحية العرض متوفرة. تحقق من وحدة التحكم للتفاصيل.';
                          backgroundColor = Colors.green.shade100;
                        } else {
                          message = 'صلاحية العرض مفقودة! تحقق من وحدة التحكم للتفاصيل.';
                          backgroundColor = Colors.red.shade100;
                        }

                        Get.snackbar(
                          'تشخيص الانتقال للمهام',
                          message,
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: backgroundColor,
                          colorText: canViewDetails ? Colors.green.shade800 : Colors.red.shade800,
                          duration: const Duration(seconds: 5),
                        );
                      },
                    ),

                  // فحص تفصيلي لصلاحية view_all
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.search, color: Colors.purple),
                      title: const Text('🔬 فحص tasks.view_all'),
                      subtitle: const Text('فحص تفصيلي لصلاحية عرض جميع المهام'),
                      onTap: () {
                        Get.back();
                        // تشغيل الفحص التفصيلي
                        PermissionDebugHelper.deepCheckViewAllTasks();

                        // عرض رسالة للمستخدم
                        Get.snackbar(
                          'فحص تفصيلي',
                          'تم فحص صلاحية tasks.view_all بالتفصيل. تحقق من وحدة التحكم.',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.purple.shade100,
                          colorText: Colors.purple.shade800,
                          duration: const Duration(seconds: 3),
                        );
                      },
                    ),

                  // اختبار الإصلاح
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.check_circle, color: Colors.green),
                      title: const Text('✅ اختبار الإصلاح'),
                      subtitle: const Text('اختبار إصلاح مشكلة المسافات في الصلاحيات'),
                      onTap: () {
                        Get.back();
                        // تشغيل اختبار الإصلاح
                        PermissionDebugHelper.testPermissionFix();

                        // عرض رسالة للمستخدم
                        Get.snackbar(
                          'اختبار الإصلاح',
                          'تم اختبار الإصلاح. تحقق من وحدة التحكم والقائمة الجانبية.',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.green.shade100,
                          colorText: Colors.green.shade800,
                          duration: const Duration(seconds: 4),
                        );
                      },
                    ),

                  // فحص شامل للصلاحيات
                  if (permissionService.canTestPermissions())
                    ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.scanner, color: Colors.indigo),
                    title: const Text('🔍 فحص شامل للصلاحيات'),
                    subtitle: const Text('البحث عن جميع المشاكل المحتملة'),
                    onTap: () {
                      Get.back();
                      // تشغيل الفحص الشامل
                      PermissionDebugHelper.scanAllPermissionsForIssues();

                      // عرض رسالة للمستخدم
                      Get.snackbar(
                        'فحص شامل',
                        'تم فحص جميع الصلاحيات. تحقق من وحدة التحكم للنتائج التفصيلية.',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.indigo.shade100,
                        colorText: Colors.indigo.shade800,
                        duration: const Duration(seconds: 4),
                      );
                    },
                  ),

                  // فحص الصلاحيات المتوقعة
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.checklist, color: Colors.teal),
                      title: const Text('📋 فحص الصلاحيات المتوقعة'),
                      subtitle: const Text('مقارنة الصلاحيات الموجودة مع المتوقعة'),
                      onTap: () {
                        Get.back();
                        // تشغيل فحص الصلاحيات المتوقعة
                        PermissionDebugHelper.checkExpectedPermissions();

                        // عرض رسالة للمستخدم
                        Get.snackbar(
                          'فحص الصلاحيات المتوقعة',
                          'تم فحص الصلاحيات المتوقعة. تحقق من وحدة التحكم.',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.teal.shade100,
                          colorText: Colors.teal.shade800,
                          duration: const Duration(seconds: 4),
                        );
                      },
                    ),

                  // فحص شامل متقدم
                  if (permissionService.canTestPermissions())
                    Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.deepPurple.shade400,
                          Colors.deepPurple.shade600,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.deepPurple.withAlpha(77),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Get.back();
                          // تشغيل الفحص الشامل المتقدم
                          PermissionDebugHelper.comprehensivePermissionScan();

                          // عرض رسالة للمستخدم
                          Get.snackbar(
                            'فحص شامل متقدم',
                            'تم تشغيل الفحص الشامل المتقدم. تحقق من وحدة التحكم للنتائج الكاملة.',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: Colors.deepPurple.shade100,
                            colorText: Colors.deepPurple.shade800,
                            duration: const Duration(seconds: 5),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(51),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: const Icon(
                                  Icons.analytics,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  '🚀 فحص شامل متقدم',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Text(
                                  'متقدم',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // إعادة تحميل الصلاحيات
                  if (permissionService.canTestPermissions())
                    ListTile(
                      contentPadding: const EdgeInsets.only(right: 32),
                      leading: const Icon(Icons.refresh, color: Colors.green),
                      title: const Text('🔄 إعادة تحميل الصلاحيات'),
                      subtitle: const Text('إعادة تحميل صلاحيات المستخدم'),
                    onTap: () async {
                      Get.back();

                      // عرض مؤشر التحميل
                      Get.dialog(
                        const Center(child: CircularProgressIndicator()),
                        barrierDismissible: false,
                      );

                      try {
                        // إعادة تحميل الصلاحيات
                        final authController = Get.find<AuthController>();
                        await authController.reloadUserPermissions();

                        // إغلاق مؤشر التحميل
                        Get.back();

                        // عرض رسالة نجاح
                        Get.snackbar(
                          'تم بنجاح',
                          'تم إعادة تحميل الصلاحيات بنجاح',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.green.shade100,
                          colorText: Colors.green.shade800,
                          duration: const Duration(seconds: 2),
                        );

                        // تشغيل تشخيص سريع
                        PermissionDebugHelper.checkTaskPermissions();
                      } catch (e) {
                        // إغلاق مؤشر التحميل
                        Get.back();

                        // عرض رسالة خطأ
                        Get.snackbar(
                          'خطأ',
                          'فشل في إعادة تحميل الصلاحيات: $e',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.red.shade100,
                          colorText: Colors.red.shade800,
                          duration: const Duration(seconds: 3),
                        );
                      }
                    },
                  ),
                ],
              ),

            const Divider(),

            // تسجيل الخروج
            ListTile(
              leading: const Icon(Icons.logout),
              title: Text('logout'.tr),
              onTap: () async {
                Get.back();
                await authController.logout();
                Get.offAllNamed(AppRoutes.login);
              },
            ),

          
             
             
          
          ],
        ),
      ),
    );
  }

  /// بناء نص أحداث التقويم مع معالجة آمنة للأخطاء
  Widget _buildCalendarEventsText() {
    // التحقق من وجود متحكم التقويم
    if (!Get.isRegistered<app_calendar.CalendarController>()) {
      return const Text(
        'استكشف التقويم',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      );
    }

    try {
      final calendarController = Get.find<app_calendar.CalendarController>();

      // استخدام Obx فقط مع المتغيرات التفاعلية
      return Obx(() {
        final today = DateTime.now();
        // الوصول إلى القائمة التفاعلية للأحداث
        final allEvents = calendarController.events;
        final todayEvents = allEvents.where((event) {
          final eventDate = event.startDateTime;
          return eventDate.year == today.year &&
                 eventDate.month == today.month &&
                 eventDate.day == today.day;
        }).toList();

        return Text(
          todayEvents.isNotEmpty
              ? '${todayEvents.length} أحداث اليوم'
              : 'لا توجد أحداث اليوم',
          style: TextStyle(
            color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
            fontSize: 12,
          ),
        );
      });
    } catch (e) {
      // في حالة حدوث خطأ
      return const Text(
        'استكشف التقويم',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      );
    }
  }
}
