import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../../models/auth_models.dart';
import '../../models/user_model.dart';
import 'api_service.dart';
import 'base_api_service.dart';

/// خدمة API للمصادقة - متوافقة مع ASP.NET Core
class AuthApiService extends GetxService {
  late final BaseApiService _apiService;

  AuthApiService() {
    // محاولة الحصول على BaseApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<BaseApiService>();
    } catch (e) {
      _apiService = BaseApiService();
    }
  }

  /// تسجيل الدخول
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      debugPrint('محاولة تسجيل الدخول للمستخدم: ${request.usernameOrEmail}');

      // استخدام post مع requireAuth = false لأن تسجيل الدخول لا يحتاج مصادقة مسبقة
      final response = await _apiService.post(
        '/api/Auth/login',
        request.toJson(),
        requireAuth: false,
      );

      final loginResponse = _apiService.handleResponse<LoginResponse>(
        response,
        (json) => LoginResponse.fromJson(json),
      );

      debugPrint('نجح تسجيل الدخول: ${loginResponse.success}');
      return loginResponse;
    } on ApiException catch (e) {
      debugPrint('ApiException في تسجيل الدخول: ${e.message} (${e.statusCode})');
      // إعادة رمي ApiException كما هو للحفاظ على رسالة الخطأ الأصلية
      rethrow;
    } catch (e) {
      debugPrint('خطأ عام في تسجيل الدخول: $e');
      String errorMessage = 'حدث خطأ أثناء معالجة استجابة الخادم.';
      // حاول استخراج رسالة أكثر تفصيلاً من الخطأ الأصلي
      if (e.toString().contains('AuthResponse:') || e.toString().contains('UserInfo:')) {
        errorMessage = 'خطأ في تحليل بيانات المستخدم من الخادم: ${e.toString()}';
      }
      throw Exception(errorMessage);
    }
  }

  /// تسجيل مستخدم جديد
  Future<RegisterResponse> register(RegisterRequest request) async {
    try {
      debugPrint('محاولة تسجيل مستخدم جديد: ${request.email}');

      // استخدام post مع requireAuth = false لأن التسجيل لا يحتاج مصادقة مسبقة
      final response = await _apiService.post(
        '/api/Auth/register',
        request.toJson(),
        requireAuth: false,
      );

      final registerResponse = _apiService.handleResponse<RegisterResponse>(
        response,
        (json) => RegisterResponse.fromJson(json),
      );

      debugPrint('نجح التسجيل: ${registerResponse.success}');

      // حفظ رمز المصادقة إذا كان التسجيل ناجحاً
      if (registerResponse.success && registerResponse.token.isNotEmpty) {
        await _apiService.saveAuthResponse(registerResponse);
        debugPrint('تم حفظ رمز المصادقة بعد التسجيل');
      }

      return registerResponse;
    } on ApiException catch (e) {
      debugPrint('ApiException في التسجيل: ${e.message} (${e.statusCode})');
      // إعادة رمي ApiException كما هو للحفاظ على رسالة الخطأ الأصلية
      rethrow;
    } catch (e) {
      debugPrint('خطأ عام في التسجيل: $e');
      throw ApiException('حدث خطأ أثناء التسجيل', 0);
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      await _apiService.post('/api/Auth/logout', {});
    } catch (e) {
      throw Exception('خطأ في تسجيل الخروج: $e');
    }
  }

  /// تحديث الرمز المميز
  Future<RefreshTokenResponse> refreshToken(String refreshToken) async {
    try {
      final response = await _apiService.post('/api/Auth/refresh-token', {
        'refreshToken': refreshToken,
      });
      return _apiService.handleResponse<RefreshTokenResponse>(
        response,
        (json) => RefreshTokenResponse.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحديث الرمز المميز: $e');
    }
  }

  /// الحصول على معلومات المستخدم الحالي
  Future<UserInfo> getCurrentUser() async {
    try {
      final response = await _apiService.get('/api/Auth/profile');
      return _apiService.handleResponse<UserInfo>(
        response,
        (json) => UserInfo.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على معلومات المستخدم: $e');
    }
  }

  /// تغيير كلمة المرور
  Future<void> changePassword(ChangePasswordRequest request) async {
    try {
      await _apiService.post('/api/Auth/change-password', request.toJson());
    } catch (e) {
      throw Exception('خطأ في تغيير كلمة المرور: $e');
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<void> resetPassword(ResetPasswordRequest request) async {
    try {
      await _apiService.post('/api/Auth/reset-password', request.toJson());
    } catch (e) {
      throw Exception('خطأ في إعادة تعيين كلمة المرور: $e');
    }
  }

  /// طلب إعادة تعيين كلمة المرور
  Future<void> forgotPassword(String email) async {
    try {
      await _apiService.post('/api/Auth/forgot-password', {'email': email});
    } catch (e) {
      throw Exception('خطأ في طلب إعادة تعيين كلمة المرور: $e');
    }
  }

  /// تأكيد البريد الإلكتروني
  Future<void> confirmEmail(String token, String email) async {
    try {
      await _apiService.post('/api/Auth/confirm-email', {
        'token': token,
        'email': email,
      });
    } catch (e) {
      throw Exception('خطأ في تأكيد البريد الإلكتروني: $e');
    }
  }

  /// إعادة إرسال رمز التأكيد
  Future<void> resendConfirmation(String email) async {
    try {
      await _apiService.post('/api/Auth/resend-confirmation', {'email': email});
    } catch (e) {
      throw Exception('خطأ في إعادة إرسال رمز التأكيد: $e');
    }
  }

  /// التحقق من صحة الرمز المميز
  Future<bool> validateToken(String token) async {
    try {
      final response = await _apiService.get('/api/Auth/validate-token?token=$token');
      return _apiService.handleResponse<bool>(
        response,
        (json) => json['isValid'] ?? false,
      );
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود مستخدم بدور SuperAdmin
  Future<bool> checkSuperAdminExists() async {
    try {
      final response = await _apiService.get('/api/Auth/check-super-admin-exists');
      return _apiService.handleResponse<bool>(
        response,
        (json) => json['exists'] ?? false,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود Super Admin: $e');
      return false;
    }
  }

  /// إنشاء المستخدم الافتراضي (Super Admin)
  Future<RegisterResponse> createDefaultSuperAdmin(RegisterRequest request) async {
    try {
      debugPrint('إنشاء المستخدم الافتراضي Super Admin...');

      final response = await _apiService.post(
        '/api/Auth/create-default-super-admin',
        request.toJson(),
        requireAuth: false,
      );

      final registerResponse = _apiService.handleResponse<RegisterResponse>(
        response,
        (json) => RegisterResponse.fromJson(json),
      );

      debugPrint('تم إنشاء المستخدم الافتراضي: ${registerResponse.success}');
      return registerResponse;
    } on ApiException catch (e) {
      debugPrint('ApiException في إنشاء المستخدم الافتراضي: ${e.message} (${e.statusCode})');
      rethrow;
    } catch (e) {
      debugPrint('خطأ عام في إنشاء المستخدم الافتراضي: $e');
      throw ApiException('حدث خطأ أثناء إنشاء المستخدم الافتراضي', 0);
    }
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    // تهيئة BaseApiService
    await _apiService.initialize();
  }
}
