import 'package:flutter/material.dart';

/// أنواع المخططات
enum ChartType {
  pie('pie', 'مخطط دائري'),
  donut('donut', 'مخطط حلقي'),
  bar('bar', 'مخطط شريطي'),
  line('line', 'مخطط خطي'),
  area('area', 'مخطط مساحي'),
  scatter('scatter', 'مخطط انتشاري'),
  bubble('bubble', 'مخطط فقاعي'),
  radar('radar', 'مخطط راداري'),
  gauge('gauge', 'مخطط مقياس'),
  funnel('funnel', 'مخطط قمعي'),
  treemap('treemap', 'خريطة شجرية'),
  heatmap('heatmap', 'خريطة حرارية'),
  gantt('gantt', 'مخطط جانت'),
  table('table', 'جدول'),
  waterfall('waterfall', 'مخطط شلال'),
  candlestick('candlestick', 'مخطط شموع'),
  boxplot('boxplot', 'مخطط صندوقي'),
  network('network', 'مخطط العلاقات'),
   sankey('sankey', 'مخطط سانكي'),
  stackedBar('stackedBar', 'مخطط شريطي تراكمي');

  const ChartType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static ChartType fromValue(String value) {
    return ChartType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ChartType.bar,
    );
  }
}

/// أنواع تصفية الوقت
enum TimeFilterType {
  day('day', 'اليوم'),
  week('week', 'الأسبوع'),
  month('month', 'الشهر'),
  quarter('quarter', 'الربع'),
  year('year', 'السنة'),
  custom('custom', 'فترة مخصصة'),
  all('all', 'الكل');

  const TimeFilterType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static TimeFilterType fromValue(String value) {
    return TimeFilterType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TimeFilterType.month,
    );
  }
}

/// نطاق عرض مخطط جانت
enum GanttViewRange {
  day('day', 'يومي'),
  week('week', 'أسبوعي'),
  month('month', 'شهري'),
  quarter('quarter', 'ربع سنوي'),
  year('year', 'سنوي');

  const GanttViewRange(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static GanttViewRange fromValue(String value) {
    return GanttViewRange.values.firstWhere(
      (range) => range.value == value,
      orElse: () => GanttViewRange.month,
    );
  }
}

/// خيارات التصفية المتقدمة للمخططات
class ChartFilterOptions {
  final bool enableDateFilter;
  final bool enableCategoryFilter;
  final bool enableValueFilter;
  final bool enableCustomFilter;
  final Map<String, dynamic>? customFilters;

  const ChartFilterOptions({
    this.enableDateFilter = true,
    this.enableCategoryFilter = true,
    this.enableValueFilter = true,
    this.enableCustomFilter = false,
    this.customFilters,
  });

  factory ChartFilterOptions.fromJson(Map<String, dynamic> json) {
    return ChartFilterOptions(
      enableDateFilter: json['enableDateFilter'] as bool? ?? true,
      enableCategoryFilter: json['enableCategoryFilter'] as bool? ?? true,
      enableValueFilter: json['enableValueFilter'] as bool? ?? true,
      enableCustomFilter: json['enableCustomFilter'] as bool? ?? false,
      customFilters: json['customFilters'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableDateFilter': enableDateFilter,
      'enableCategoryFilter': enableCategoryFilter,
      'enableValueFilter': enableValueFilter,
      'enableCustomFilter': enableCustomFilter,
      'customFilters': customFilters,
    };
  }

  ChartFilterOptions copyWith({
    bool? enableDateFilter,
    bool? enableCategoryFilter,
    bool? enableValueFilter,
    bool? enableCustomFilter,
    Map<String, dynamic>? customFilters,
  }) {
    return ChartFilterOptions(
      enableDateFilter: enableDateFilter ?? this.enableDateFilter,
      enableCategoryFilter: enableCategoryFilter ?? this.enableCategoryFilter,
      enableValueFilter: enableValueFilter ?? this.enableValueFilter,
      enableCustomFilter: enableCustomFilter ?? this.enableCustomFilter,
      customFilters: customFilters ?? this.customFilters,
    );
  }
}

// ===== النماذج الجديدة للداشبورد المحسن =====

/// نموذج بيانات المخطط المبسط
class ChartData {
  final String id;
  final String title;
  final ChartType type;
  final Map<String, dynamic> data;
  final ChartFilterOptions filterOptions;
  final DateTime? startDate;
  final DateTime? endDate;
  final TimeFilterType filterType;

  const ChartData({
    required this.id,
    required this.title,
    required this.type,
    required this.data,
    required this.filterOptions,
    this.startDate,
    this.endDate,
    this.filterType = TimeFilterType.month,
  });

  ChartData copyWith({
    String? id,
    String? title,
    ChartType? type,
    Map<String, dynamic>? data,
    ChartFilterOptions? filterOptions,
    DateTime? startDate,
    DateTime? endDate,
    TimeFilterType? filterType,
  }) {
    return ChartData(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      data: data ?? this.data,
      filterOptions: filterOptions ?? this.filterOptions,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      filterType: filterType ?? this.filterType,
    );
  }
}

/// نموذج عنصر الداشبورد المبسط
class DashboardItemModel {
  final String id;
  final String title;
  final ChartType chartType;
  final ChartFilterOptions filterOptions;
  final Offset position;
  final Size size;
  final bool isVisible;

  const DashboardItemModel({
    required this.id,
    required this.title,
    required this.chartType,
    required this.filterOptions,
    this.position = Offset.zero,
    this.size = const Size(400, 300),
    this.isVisible = true,
  });

  DashboardItemModel copyWith({
    String? id,
    String? title,
    ChartType? chartType,
    ChartFilterOptions? filterOptions,
    Offset? position,
    Size? size,
    bool? isVisible,
  }) {
    return DashboardItemModel(
      id: id ?? this.id,
      title: title ?? this.title,
      chartType: chartType ?? this.chartType,
      filterOptions: filterOptions ?? this.filterOptions,
      position: position ?? this.position,
      size: size ?? this.size,
      isVisible: isVisible ?? this.isVisible,
    );
  }
}

/// نموذج إعدادات المخطط
class ChartSettings {
  final Map<String, Color> colors;
  final Map<String, bool> displayOptions;
  final bool showLegend;
  final bool showGrid;
  final bool showValues;

  const ChartSettings({
    this.colors = const {},
    this.displayOptions = const {},
    this.showLegend = true,
    this.showGrid = true,
    this.showValues = true,
  });

  ChartSettings copyWith({
    Map<String, Color>? colors,
    Map<String, bool>? displayOptions,
    bool? showLegend,
    bool? showGrid,
    bool? showValues,
  }) {
    return ChartSettings(
      colors: colors ?? this.colors,
      displayOptions: displayOptions ?? this.displayOptions,
      showLegend: showLegend ?? this.showLegend,
      showGrid: showGrid ?? this.showGrid,
      showValues: showValues ?? this.showValues,
    );
  }
}
