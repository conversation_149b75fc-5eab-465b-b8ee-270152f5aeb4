import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/chart_enums.dart';


/// أداة مساعدة للتصفية
///
/// توفر دوال مساعدة لتصفية البيانات وتخزينها مؤقتًا
class FilterUtils {
  /// تخزين مؤقت للبيانات المصفاة
  static final Map<String, List<Task>> _filteredTasksCache = {};

  /// تصفية المهام حسب الفترة الزمنية
  ///
  /// [tasks] قائمة المهام الأصلية
  /// [startDate] تاريخ البداية
  /// [endDate] تاريخ النهاية
  /// [dateField] حقل التاريخ المستخدم للتصفية (createdAt, completedAt, dueDate, startDate)
  static List<Task> filterTasksByDateRange(
    List<Task> tasks,
    DateTime? startDate,
    DateTime? endDate, {
    String dateField = 'createdAt',
  }) {
    if (startDate == null || endDate == null) {
      return tasks; // إذا لم يتم تحديد فترة زمنية، إرجاع جميع المهام
    }

    return tasks.where((task) {
      // تحديد التاريخ المناسب للتصفية بناءً على حقل التاريخ
      DateTime? taskDate;

      switch (dateField) {
        case 'completedAt':
          taskDate = task.completedAt != null
            ? DateTime.fromMillisecondsSinceEpoch(task.completedAt!)
            : null;
          break;
        case 'dueDate':
          taskDate = task.dueDate != null
            ? DateTime.fromMillisecondsSinceEpoch(task.dueDate!)
            : null;
          break;
        case 'startDate':
          taskDate = task.startDate != null
            ? DateTime.fromMillisecondsSinceEpoch(task.startDate!)
            : null;
          break;
        case 'createdAt':
        default:
          taskDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt);
      }

      // إذا كان التاريخ غير محدد، استخدم تاريخ الإنشاء
      if (taskDate == null) {
        if (dateField != 'createdAt') {
          return false; // إذا كان الحقل المطلوب غير موجود، استبعد المهمة
        }
        taskDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt);
      }

      return taskDate.isAfter(startDate) &&
          taskDate.isBefore(endDate.add(const Duration(seconds: 1)));
    }).toList();
  }

  /// الحصول على المهام المصفاة مع تخزين مؤقت
  ///
  /// [tasks] قائمة المهام الأصلية
  /// [chartType] نوع المخطط (مفتاح للتخزين المؤقت)
  /// [startDate] تاريخ البداية
  /// [endDate] تاريخ النهاية
  /// [dateField] حقل التاريخ المستخدم للتصفية
  static List<Task> getFilteredTasks(
    List<Task> tasks,
    String chartType,
    DateTime? startDate,
    DateTime? endDate, {
    String dateField = 'createdAt',
  }) {
    // إذا لم يتم تحديد فترة زمنية، إرجاع جميع المهام
    if (startDate == null || endDate == null) {
      return tasks;
    }

    // إنشاء مفتاح للتخزين المؤقت
    final cacheKey =
        '$chartType-${startDate.toIso8601String()}-${endDate.toIso8601String()}-$dateField';

    // التحقق من وجود البيانات في التخزين المؤقت
    if (_filteredTasksCache.containsKey(cacheKey)) {
      debugPrint('استخدام البيانات المخزنة مؤقتًا لـ $chartType');
      return _filteredTasksCache[cacheKey]!;
    }

    // تصفية البيانات وتخزينها في التخزين المؤقت
    final filteredTasks =
        filterTasksByDateRange(tasks, startDate, endDate, dateField: dateField);
    _filteredTasksCache[cacheKey] = filteredTasks;

    debugPrint(
        'تم تصفية ${filteredTasks.length} مهمة من أصل ${tasks.length} لـ $chartType');
    return filteredTasks;
  }

  /// إعادة تعيين التخزين المؤقت
  static void resetFilterCache() {
    _filteredTasksCache.clear();
    debugPrint('تم إعادة تعيين التخزين المؤقت للتصفية');
  }

  /// تحديث نطاق التاريخ بناءً على نوع الفلتر
  ///
  /// [filterType] نوع الفلتر
  static Map<String, DateTime?> getDateRangeForFilterType(
      TimeFilterType filterType) {
    final now = DateTime.now();
    DateTime? startDate;
    DateTime? endDate;

    switch (filterType) {
      case TimeFilterType.day:
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case TimeFilterType.week:
        // بداية الأسبوع (السبت)
        final weekStart = now.subtract(Duration(days: now.weekday % 7));
        startDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
        // نهاية الأسبوع (الجمعة)
        endDate = startDate
            .add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
        break;
      case TimeFilterType.month:
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case TimeFilterType.quarter:
        final quarterStart = (now.month - 1) ~/ 3 * 3 + 1;
        startDate = DateTime(now.year, quarterStart, 1);
        endDate = DateTime(now.year, quarterStart + 3, 0, 23, 59, 59);
        break;
      case TimeFilterType.year:
        startDate = DateTime(now.year, 1, 1);
        endDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case TimeFilterType.all:
        startDate = null;
        endDate = null;
        break;
      case TimeFilterType.custom:
        // لا تغير التواريخ للتصفية المخصصة
        break;
    }

    return {
      'startDate': startDate,
      'endDate': endDate,
    };
  }
}
