import 'user_model.dart';

/// أنواع التقارير - متوافقة مع ASP.NET Core API
enum ReportType {
  taskSummary('task_summary', 'ملخص المهام'),
  taskStatus('task_status', 'حالة المهام'),
  taskProgress('task_progress', 'تقدم المهام'),
  taskDetails('task_details', 'تفاصيل المهام'),
  taskCompletion('task_completion', 'إكمال المهام'),
  userActivity('user_activity', 'نشاط المستخدمين'),
  userPerformance('user_performance', 'أداء المستخدمين'),
  departmentPerformance('department_performance', 'أداء الأقسام'),
  departmentWorkload('department_workload', 'عبء العمل للأقسام'),
  timeTracking('time_tracking', 'تتبع الوقت'),
  projectProgress('project_progress', 'تقدم المشاريع'),
  projectStatus('project_status', 'حالة المشاريع'),
  systemUsage('system_usage', 'استخدام النظام'),
  custom('custom', 'تقرير مخصص'),
  performanceReport('performance_report', 'تقرير الأداء');

  const ReportType(this.value, this.displayName);

  final String value;
  final String displayName;

  static ReportType fromValue(String value) {
    return ReportType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ReportType.custom,
    );
  }

  /// للتوافق مع الكود القديم
  static ReportType fromName(String name) {
    return ReportType.values.firstWhere(
      (type) => type.name == name,
      orElse: () => ReportType.custom,
    );
  }
}

/// تكرار التقارير المجدولة
enum ReportScheduleFrequency {
  daily('daily', 'يومي'),
  weekly('weekly', 'أسبوعي'),
  monthly('monthly', 'شهري'),
  quarterly('quarterly', 'ربع سنوي'),
  yearly('yearly', 'سنوي');

  const ReportScheduleFrequency(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static ReportScheduleFrequency fromValue(String value) {
    return ReportScheduleFrequency.values.firstWhere(
      (freq) => freq.value == value,
      orElse: () => ReportScheduleFrequency.monthly,
    );
  }
}

/// نموذج التقرير - متوافق مع ASP.NET Core API
class Report {
  final int id;
  final String title;
  final String? description;
  final ReportType reportType;
  final String query;
  final String? parameters;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final int? lastExportedAt;
  final bool isPublic;
  final bool isDeleted;
  final bool isShared;
  final List<int>? sharedWithUserIds;

  // Navigation properties
  final User? createdByUser;
  final List<ReportSchedule>? schedules;

  /// خاصية للوصول إلى نوع التقرير (للتوافق مع الكود القديم)
  ReportType get type => reportType;

  /// خاصية للوصول إلى معرف المنشئ (للتوافق مع الكود القديم)
  int get createdById => createdBy;

  const Report({
    required this.id,
    required this.title,
    this.description,
    required this.reportType,
    required this.query,
    this.parameters,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.lastExportedAt,
    this.isPublic = false,
    this.isDeleted = false,
    this.isShared = false,
    this.sharedWithUserIds,
    this.createdByUser,
    this.schedules,
  });

  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      reportType: ReportType.fromValue(json['reportType'] as String),
      query: json['query'] as String,
      parameters: json['parameters'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      lastExportedAt: json['lastExportedAt'] as int?,
      isPublic: json['isPublic'] as bool? ?? false,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isShared: json['isShared'] as bool? ?? false,
      sharedWithUserIds: json['sharedWithUserIds'] != null
          ? List<int>.from(json['sharedWithUserIds'] as List)
          : null,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      schedules: json['reportSchedules'] != null
          ? (json['reportSchedules'] as List)
              .map((s) => ReportSchedule.fromJson(s as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'reportType': reportType.value,
      'query': query,
      'parameters': parameters,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'lastExportedAt': lastExportedAt,
      'isPublic': isPublic,
      'isDeleted': isDeleted,
      'isShared': isShared,
      'sharedWithUserIds': sharedWithUserIds,
    };
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على تاريخ آخر تصدير كـ DateTime
  DateTime? get lastExportedAtDateTime => lastExportedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(lastExportedAt! * 1000)
      : null;
}

/// نموذج جدولة التقرير
class ReportSchedule {
  final int id;
  final int reportId;
  final String name;
  final ReportScheduleFrequency frequency;
  final int nextRunTime;
  final int? lastRunTime;
  final bool isActive;
  final String? emailRecipients;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;

  // Navigation properties
  final Report? report;
  final User? createdByUser;

  const ReportSchedule({
    required this.id,
    required this.reportId,
    required this.name,
    required this.frequency,
    required this.nextRunTime,
    this.lastRunTime,
    this.isActive = true,
    this.emailRecipients,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.report,
    this.createdByUser,
  });

  factory ReportSchedule.fromJson(Map<String, dynamic> json) {
    return ReportSchedule(
      id: json['id'] as int,
      reportId: json['reportId'] as int,
      name: json['name'] as String,
      frequency: ReportScheduleFrequency.fromValue(json['frequency'] as String),
      nextRunTime: json['nextRunTime'] as int,
      lastRunTime: json['lastRunTime'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      emailRecipients: json['emailRecipients'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      report: json['report'] != null 
          ? Report.fromJson(json['report'] as Map<String, dynamic>)
          : null,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reportId': reportId,
      'name': name,
      'frequency': frequency.value,
      'nextRunTime': nextRunTime,
      'lastRunTime': lastRunTime,
      'isActive': isActive,
      'emailRecipients': emailRecipients,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// الحصول على تاريخ التشغيل التالي كـ DateTime
  DateTime get nextRunDateTime => 
      DateTime.fromMillisecondsSinceEpoch(nextRunTime * 1000);

  /// الحصول على تاريخ التشغيل الأخير كـ DateTime
  DateTime? get lastRunDateTime => lastRunTime != null 
      ? DateTime.fromMillisecondsSinceEpoch(lastRunTime! * 1000)
      : null;
}

/// نموذج بيانات التقرير
class ReportData {
  final String reportTitle;
  final ReportType reportType;
  final DateTime generatedAt;
  final Map<String, dynamic> parameters;
  final List<Map<String, dynamic>> data;
  final Map<String, dynamic>? summary;

  const ReportData({
    required this.reportTitle,
    required this.reportType,
    required this.generatedAt,
    required this.parameters,
    required this.data,
    this.summary,
  });

  factory ReportData.fromJson(Map<String, dynamic> json) {
    return ReportData(
      reportTitle: json['reportTitle'] as String,
      reportType: ReportType.fromValue(json['reportType'] as String),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      parameters: json['parameters'] as Map<String, dynamic>,
      data: (json['data'] as List).cast<Map<String, dynamic>>(),
      summary: json['summary'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reportTitle': reportTitle,
      'reportType': reportType.value,
      'generatedAt': generatedAt.toIso8601String(),
      'parameters': parameters,
      'data': data,
      'summary': summary,
    };
  }

  /// عدد الصفوف في التقرير
  int get rowCount => data.length;

  /// التحقق من وجود ملخص
  bool get hasSummary => summary != null && summary!.isNotEmpty;
}

/// نموذج طلب إنشاء تقرير
class CreateReportRequest {
  final String title;
  final String? description;
  final ReportType reportType;
  final String query;
  final String? parameters;
  final bool isPublic;

  const CreateReportRequest({
    required this.title,
    this.description,
    required this.reportType,
    required this.query,
    this.parameters,
    this.isPublic = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'reportType': reportType.value,
      'query': query,
      'parameters': parameters,
      'isPublic': isPublic,
    };
  }
}

/// نموذج طلب تشغيل تقرير
class RunReportRequest {
  final int reportId;
  final Map<String, dynamic>? parameters;
  final String? format; // pdf, excel, csv

  const RunReportRequest({
    required this.reportId,
    this.parameters,
    this.format,
  });

  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'parameters': parameters,
      'format': format,
    };
  }
}

/// نموذج طلب جدولة تقرير
class CreateReportScheduleRequest {
  final int reportId;
  final String name;
  final ReportScheduleFrequency frequency;
  final DateTime nextRunTime;
  final String? emailRecipients;

  const CreateReportScheduleRequest({
    required this.reportId,
    required this.name,
    required this.frequency,
    required this.nextRunTime,
    this.emailRecipients,
  });

  Map<String, dynamic> toJson() {
    return {
      'reportId': reportId,
      'name': name,
      'frequency': frequency.value,
      'nextRunTime': nextRunTime.millisecondsSinceEpoch ~/ 1000,
      'emailRecipients': emailRecipients,
    };
  }
}
