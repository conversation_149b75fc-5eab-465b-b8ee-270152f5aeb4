import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_models.dart';
import 'package:get/get.dart';
import '../../../services/unified_permission_service.dart';

/// رقاقة وسم وثيقة
///
/// تعرض وسم وثيقة في شكل رقاقة
class DocumentTagChip extends StatelessWidget {
  /// وسم الأرشيف
  final ArchiveTag tag;

  /// دالة يتم استدعاؤها عند النقر على الرقاقة
  final VoidCallback? onTap;

  /// دالة يتم استدعاؤها عند النقر على زر الحذف
  final VoidCallback? onDelete;

  /// هل يمكن حذف الوسم
  final bool isDeletable;

   DocumentTagChip({
    super.key,
    required this.tag,
    this.onTap,
    this.onDelete,
    this.isDeletable = false,
  });
  //الصلاحيات
    final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  Widget build(BuildContext context) {
    // تحويل لون الوسم من سلسلة نصية إلى كائن Color
    Color tagColor;
    try {
      tagColor = Color(int.parse((tag.color ?? '#3498db').replaceAll('#', '0xff')));
    } catch (e) {
      // استخدام لون افتراضي في حالة حدوث خطأ
      tagColor = Colors.blue;
    }

    return _permissionService.canViewTags()
        ? GestureDetector(
            onTap: onTap,
            child: Chip(
        label: Text(
          tag.name,
          style: TextStyle(
            color: _getTextColor(tagColor),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: tagColor.withAlpha(51),
        side: BorderSide(
          color: tagColor,
          width: 1,
        ),
        deleteIcon: isDeletable ? const Icon(Icons.close, size: 16) : null,
        onDeleted: isDeletable ? onDelete : null,
        deleteIconColor: tagColor,
      ),
    )
        : Chip(
            label: Text(
              tag.name,
              style: TextStyle(
                color: _getTextColor(tagColor),
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: tagColor.withAlpha(51),
            side: BorderSide(
              color: tagColor,
              width: 1,
            ),
          );
  }

  /// الحصول على لون النص المناسب (أبيض أو أسود) بناءً على لون الخلفية
  Color _getTextColor(Color backgroundColor) {
    // حساب درجة سطوع اللون
    final brightness = backgroundColor.computeLuminance();

    // إذا كان اللون فاتحًا، استخدم النص الأسود، وإلا استخدم النص الأبيض
    return brightness > 0.5 ? Colors.black : Colors.white;
  }
}
