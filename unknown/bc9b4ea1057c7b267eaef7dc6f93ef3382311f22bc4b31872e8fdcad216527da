import 'custom_role_model.dart';
import 'user_model.dart';

/// نموذج ربط المستخدم بالدور المخصص
class UserCustomRole {
  final int id;
  final int userId;
  final int customRoleId;
  final int assignedAt;
  final bool isDeleted;

  // العلاقات البرمجية
  final CustomRole? customRole;
  final User? user;

  const UserCustomRole({
    required this.id,
    required this.userId,
    required this.customRoleId,
    required this.assignedAt,
    this.isDeleted = false,
    this.customRole,
    this.user,
  });

  factory UserCustomRole.fromJson(Map<String, dynamic> json) {
    return UserCustomRole(
      id: json['id'] as int,
      userId: json['userId'] as int,
      customRoleId: json['customRoleId'] as int,
      assignedAt: json['assignedAt'] as int,
      isDeleted: json['isDeleted'] as bool? ?? false,
      customRole: json['customRole'] != null ? CustomRole.fromJson(json['customRole']) : null,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'customRoleId': customRoleId,
      'assignedAt': assignedAt,
      'isDeleted': isDeleted,
      'customRole': customRole?.toJson(),
      'user': user?.toJson(),
    };
  }
}
