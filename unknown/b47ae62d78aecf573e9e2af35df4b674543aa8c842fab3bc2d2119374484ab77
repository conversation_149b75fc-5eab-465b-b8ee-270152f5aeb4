import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../config/api_config.dart';
import '../../helpers/user_helper.dart';
import '../../models/task_contributor_model.dart';

/// خدمة API لإدارة صلاحيات الوصول للمهام
class TaskAccessApiService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/api/TaskAccess';

  /// الحصول على المستخدمين الذين لديهم صلاحية الوصول لمهمة معينة
  Future<List<int>> getTaskAccessUsers(int taskId) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/task/$taskId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map<int>((item) => item['userId'] as int).toList();
      } else if (response.statusCode == 404) {
        // لا توجد صلاحيات وصول للمهمة
        return [];
      } else {
        throw Exception('فشل في تحميل صلاحيات الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل صلاحيات الوصول: $e');
      rethrow;
    }
  }

  /// الحصول على جميع المساهمين المحتملين للمهمة (المستخدمين الذين لديهم صلاحية وصول)
  Future<List<TaskContributor>> getTaskContributors(int taskId) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/task/$taskId/contributors'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map<TaskContributor>((item) => TaskContributor.fromJson(item as Map<String, dynamic>)).toList();
      } else if (response.statusCode == 404) {
        // لا توجد مساهمين للمهمة
        return [];
      } else {
        throw Exception('فشل في تحميل المساهمين: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل صلاحيات الوصول: $e');
      rethrow;
    }
  }

  /// إضافة مستخدم إلى صلاحيات الوصول للمهمة
  Future<void> addUserToTaskAccess(int taskId, int userId) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'taskId': taskId,
          'userId': userId,
        }),
      );

      if (response.statusCode != 201 && response.statusCode != 200) {
        throw Exception('فشل في إضافة المستخدم لصلاحيات الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة المستخدم لصلاحيات الوصول: $e');
      rethrow;
    }
  }

  /// إزالة مستخدم من صلاحيات الوصول للمهمة
  Future<void> removeUserFromTaskAccess(int taskId, int userId) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.delete(
        Uri.parse('$_baseUrl/task/$taskId/user/$userId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('فشل في إزالة المستخدم من صلاحيات الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة المستخدم من صلاحيات الوصول: $e');
      rethrow;
    }
  }

  /// إضافة عدة مستخدمين إلى صلاحيات الوصول للمهمة
  Future<void> addMultipleUsersToTaskAccess(int taskId, List<int> userIds) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/bulk'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'taskId': taskId,
          'userIds': userIds,
        }),
      );

      if (response.statusCode != 201 && response.statusCode != 200) {
        throw Exception('فشل في إضافة المستخدمين لصلاحيات الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة المستخدمين لصلاحيات الوصول: $e');
      rethrow;
    }
  }

  /// تحديث صلاحيات الوصول للمهمة (استبدال القائمة الحالية)
  Future<void> updateTaskAccess(int taskId, List<int> userIds) async {
    try {
      final token = UserHelper.getToken();
      if (token == null) {
        throw Exception('لا يوجد رمز مصادقة');
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/task/$taskId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'userIds': userIds,
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث صلاحيات الوصول: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث صلاحيات الوصول: $e');
      rethrow;
    }
  }
}
