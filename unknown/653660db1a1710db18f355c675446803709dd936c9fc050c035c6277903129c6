import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/unified_permission_service.dart';
import '../../../services/system_settings_service.dart';
import '../../../services/admin/unified_admin_api_service.dart';
import '../../../models/system_settings_model.dart';
import '../shared/admin_card_widget.dart';
import '../shared/admin_dialog_widget.dart';
import 'backup_management_screen.dart';

/// شاشة إعدادات النظام
class SystemSettingsScreen extends StatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  State<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends State<SystemSettingsScreen> {
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final AdminController _adminController = Get.find<AdminController>();
  final SystemSettingsService _settingsService = Get.find<SystemSettingsService>();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// تهيئة البيانات بشكل محسن
  Future<void> _initializeData() async {
    // تحميل البيانات بشكل متوازي لتحسين الأداء
    try {
      await Future.wait([
        _settingsService.loadSettings(),
        _adminController.loadUsers(),
        _adminController.loadRoles(),
      ]);
    } catch (e) {
      _handleError('تحميل البيانات', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات النظام'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الأمان
            _buildSecuritySection(),
            
            const SizedBox(height: 24),
            
            // قسم النسخ الاحتياطية
            _buildBackupSection(),
            
            const SizedBox(height: 24),
            
            // قسم قاعدة البيانات
            _buildDatabaseSection(),
            
            const SizedBox(height: 24),
            
            // قسم التقارير
            _buildReportsSection(),
            
            const SizedBox(height: 24),
            
            // قسم النظام العام
            _buildGeneralSection(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الأمان
  Widget _buildSecuritySection() {
    return _buildSection(
      title: 'الأمان والحماية',
      subtitle: 'إعدادات الأمان وحماية النظام',
      icon: Icons.security,
      children: [
        AdminCardWidget(
          title: 'إعدادات كلمات المرور',
          subtitle: 'تحديد متطلبات كلمات المرور وسياسات الأمان',
          icon: Icons.lock,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showPasswordSettings(),
        ),
        AdminCardWidget(
          title: 'جلسات المستخدمين',
          subtitle: 'إدارة الجلسات النشطة ومدة انتهاء الصلاحية',
          icon: Icons.access_time,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showSessionSettings(),
        ),
        AdminCardWidget(
          title: 'سجل النشاط',
          subtitle: 'عرض وإدارة سجل أنشطة المستخدمين',
          icon: Icons.history,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showActivityLogs(),
        ),
      ],
    );
  }

  /// بناء قسم النسخ الاحتياطية
  Widget _buildBackupSection() {
    return _buildSection(
      title: 'النسخ الاحتياطية',
      subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
      icon: Icons.backup,
      children: [
        // AdminCardWidget(
        //   title: 'إنشاء نسخة احتياطية',
        //   subtitle: 'إنشاء نسخة احتياطية من قاعدة البيانات',
        //   icon: Icons.save,
        //   iconColor: Colors.green,
        //   isEnabled: _permissionService.canAccessAdmin(),
        //   onTap: () => _createBackup(),
        // ),
        // AdminCardWidget(
        //   title: 'استعادة نسخة احتياطية',
        //   subtitle: 'استعادة البيانات من نسخة احتياطية سابقة',
        //   icon: Icons.restore,
        //   iconColor: Colors.orange,
        //   isEnabled: _permissionService.canAccessAdmin(),
        //   onTap: () => _restoreBackup(),
        // ),
        AdminCardWidget(
          title: 'إدارة النسخ الاحتياطية',
          subtitle: 'عرض وحذف النسخ الاحتياطية المحفوظة',
          icon: Icons.folder,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _manageBackups(),
        ),
      ],
    );
  }

  /// بناء قسم قاعدة البيانات
  Widget _buildDatabaseSection() {
    return _buildSection(
      title: 'قاعدة البيانات',
      subtitle: 'إدارة وصيانة قاعدة البيانات',
      icon: Icons.storage,
      children: [
        AdminCardWidget(
          title: 'معلومات قاعدة البيانات',
          subtitle: 'عرض معلومات وإحصائيات قاعدة البيانات',
          icon: Icons.info,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showDatabaseInfo(),
        ),
        AdminCardWidget(
          title: 'تحسين قاعدة البيانات',
          subtitle: 'تشغيل عمليات التحسين والصيانة',
          icon: Icons.tune,
          iconColor: Colors.blue,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _optimizeDatabase(),
        ),
        AdminCardWidget(
          title: 'إصلاح قاعدة البيانات',
          subtitle: 'إصلاح المشاكل والأخطاء في قاعدة البيانات',
          icon: Icons.build,
          iconColor: Colors.red,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _repairDatabase(),
        ),
      ],
    );
  }

  /// بناء قسم التقارير
  Widget _buildReportsSection() {
    return _buildSection(
      title: 'التقارير والإحصائيات',
      subtitle: 'تقارير النظام والاستخدام',
      icon: Icons.analytics,
      children: [
        AdminCardWidget(
          title: 'تقرير استخدام النظام',
          subtitle: 'إحصائيات استخدام النظام والمستخدمين',
          icon: Icons.bar_chart,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showSystemUsageReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأخطاء',
          subtitle: 'عرض الأخطاء والمشاكل المسجلة',
          icon: Icons.error_outline,
          iconColor: Colors.red,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showErrorReport(),
        ),
        AdminCardWidget(
          title: 'تقرير الأداء',
          subtitle: 'مراقبة أداء النظام والخادم',
          icon: Icons.speed,
          iconColor: Colors.green,
          isEnabled: _permissionService.canViewReports(),
          onTap: () => _showPerformanceReport(),
        ),
      ],
    );
  }

  /// بناء قسم النظام العام
  Widget _buildGeneralSection() {
    return _buildSection(
      title: 'الإعدادات العامة',
      subtitle: 'إعدادات النظام العامة',
      icon: Icons.settings,
      children: [
        AdminCardWidget(
          title: 'إعدادات التطبيق',
          subtitle: 'إعدادات عامة للتطبيق والواجهة',
          icon: Icons.app_settings_alt,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showAppSettings(),
        ),
        AdminCardWidget(
          title: 'إعدادات الإشعارات',
          subtitle: 'تكوين إعدادات الإشعارات والتنبيهات',
          icon: Icons.notifications_active,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _showNotificationSettings(),
        ),
        AdminCardWidget(
          title: 'إعادة تشغيل النظام',
          subtitle: 'إعادة تشغيل الخدمات والتطبيق',
          icon: Icons.restart_alt,
          iconColor: Colors.orange,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: () => _restartSystem(),
        ),
      ],
    );
  }

  /// بناء قسم
  Widget _buildSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس القسم
        Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // محتوى القسم
        ...children,
      ],
    );
  }

  /// عرض إعدادات كلمات المرور
  void _showPasswordSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8),
            Text('إعدادات كلمات المرور'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // الحد الأدنى لطول كلمة المرور
              _buildSettingRow(
                title: 'الحد الأدنى لطول كلمة المرور',
                value: _settingsService.getStringValue('password_min_length', defaultValue: '6') + ' أحرف',
                icon: Icons.straighten,
                iconColor: Colors.orange,
                onTap: () => _editPasswordMinLength(),
              ),
              const Divider(),

              // تعقيد كلمة المرور
              _buildSettingRow(
                title: 'تعقيد كلمة المرور',
                value: _settingsService.getStringValue('password_complexity', defaultValue: 'أحرف كبيرة وصغيرة ورقم ورمز'),
                icon: Icons.security,
                iconColor: Colors.orange,
                onTap: () => _editPasswordComplexity(),
              ),
              const Divider(),

              // انتهاء صلاحية كلمة المرور
              _buildSettingRow(
                title: 'انتهاء صلاحية كلمة المرور',
                value: _settingsService.getStringValue('password_expiry_days', defaultValue: '90') + ' يوم',
                icon: Icons.schedule,
                iconColor: Colors.orange,
                onTap: () => _editPasswordExpiry(),
              ),
              const Divider(),

              // تاريخ كلمات المرور
              _buildSettingRow(
                title: 'تذكر كلمات المرور السابقة',
                value: _settingsService.getStringValue('password_history_count', defaultValue: '5') + ' كلمات مرور',
                icon: Icons.history,
                iconColor: Colors.orange,
                onTap: () => _editPasswordHistory(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الجلسات
  void _showSessionSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.timer, color: Colors.blue),
            SizedBox(width: 8),
            Text('إعدادات الجلسات'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مهلة انتهاء الجلسة
              _buildSettingRow(
                title: 'مهلة انتهاء الجلسة',
                value: _settingsService.getStringValue('session_timeout', defaultValue: '30') + ' دقيقة',
                icon: Icons.timer_off,
                iconColor: Colors.blue,
                onTap: () => _editSessionTimeout(),
              ),
              const Divider(),

              // الحد الأقصى للجلسات المتزامنة
              _buildSettingRow(
                title: 'الحد الأقصى للجلسات المتزامنة',
                value: _settingsService.getStringValue('max_concurrent_sessions', defaultValue: '3') + ' جلسات',
                icon: Icons.devices,
                iconColor: Colors.blue,
                onTap: () => _editMaxConcurrentSessions(),
              ),
              const Divider(),

              // تذكر تسجيل الدخول
              _buildSettingRow(
                title: 'تذكر تسجيل الدخول',
                value: _settingsService.getBoolValue('remember_login', defaultValue: true)
                    ? 'مفعل لمدة ${_settingsService.getStringValue('remember_login_days', defaultValue: '7')} أيام'
                    : 'معطل',
                icon: Icons.remember_me,
                iconColor: Colors.blue,
                onTap: () => _editRememberLogin(),
              ),
              const Divider(),

              // إنهاء الجلسات النشطة
              _buildSettingRow(
                title: 'إنهاء جميع الجلسات النشطة',
                value: 'إنهاء فوري',
                icon: Icons.logout,
                iconColor: Colors.red,
                onTap: () => _terminateAllSessions(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض سجل النشاط
  void _showActivityLogs() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.history, color: Colors.green),
            SizedBox(width: 8),
            Text('سجل النشاط'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final logs = _adminController.activityLogs;
            if (logs.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.info_outline, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('لا توجد سجلات نشاط'),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: logs.length,
              itemBuilder: (context, index) {
                final log = logs[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getLogTypeColor(log.action),
                      child: Icon(
                        _getLogTypeIcon(log.action),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    title: Text(log.action),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(log.details ?? log.changeDescription ?? 'لا يوجد وصف'),
                        Text(
                          'المستخدم: ${log.userId} | ${_formatDateTime(log.timestamp)}',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.info_outline),
                      onPressed: () => _showLogDetails(log),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _adminController.loadActivityLogs(),
            child: const Text('تحديث'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  Future<void> _createBackup() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟',
      confirmText: 'إنشاء',
      icon: Icons.backup,
      confirmColor: Colors.green,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنشاء النسخة الاحتياطية...');
      
      try {
        // استدعاء API لإنشاء النسخة الاحتياطية
        await _adminController.createBackup('نسخة احتياطية من إعدادات النظام');
        await _adminController.loadBackups(); // إعادة تحميل النسخ الاحتياطية
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إنشاء النسخة الاحتياطية: $e',
        );
      }
    }
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.restore, color: Colors.orange),
            SizedBox(width: 8),
            Text('استعادة نسخة احتياطية'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 300,
          child: Obx(() {
            final backups = _adminController.backups;
            if (backups.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.backup_outlined, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('لا توجد نسخ احتياطية متاحة'),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: backups.length,
              itemBuilder: (context, index) {
                final backup = backups[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.backup, color: Colors.white),
                    ),
                    title: Text(backup.description ?? 'نسخة احتياطية'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('الحجم: ${_formatFileSize(backup.size ?? 0)}'),
                        Text('التاريخ: ${_formatDateTime(backup.createdAt)}'),
                      ],
                    ),
                    trailing: ElevatedButton(
                      onPressed: () => _confirmRestoreBackup(backup),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('استعادة'),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _adminController.loadBackups(),
            child: const Text('تحديث'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إدارة النسخ الاحتياطية
  void _manageBackups() {
    Get.to(() => const BackupManagementScreen());
  }

  /// عرض معلومات قاعدة البيانات
  void _showDatabaseInfo() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.storage, color: Colors.blue),
            SizedBox(width: 8),
            Text('معلومات قاعدة البيانات'),
          ],
        ),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDatabaseInfoRow('نوع قاعدة البيانات', 'SQL Server'),
              _buildDatabaseInfoRow('إصدار الخادم', '2019 Express'),
              _buildDatabaseInfoRow('اسم قاعدة البيانات', 'TaskManagementDB'),
              _buildDatabaseInfoRow('حجم قاعدة البيانات', '125.5 MB'),
              _buildDatabaseInfoRow('عدد الجداول', '25'),
              _buildDatabaseInfoRow('عدد السجلات', '15,432'),
              _buildDatabaseInfoRow('آخر نسخة احتياطية', 'منذ 2 ساعة'),
              _buildDatabaseInfoRow('حالة الاتصال', 'متصل', isStatus: true),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _testDatabaseConnection(),
            child: const Text('اختبار الاتصال'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تحسين قاعدة البيانات
  Future<void> _optimizeDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تحسين قاعدة البيانات',
      message: 'هل تريد تشغيل عملية تحسين قاعدة البيانات؟\nقد تستغرق هذه العملية بعض الوقت.',
      confirmText: 'تحسين',
      icon: Icons.tune,
      confirmColor: Colors.blue,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري تحسين قاعدة البيانات...');
      
      try {
        // استدعاء API لتحسين قاعدة البيانات
        await _adminController.optimizeDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم تحسين قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في تحسين قاعدة البيانات: $e',
        );
      }
    }
  }

  /// إصلاح قاعدة البيانات
  Future<void> _repairDatabase() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إصلاح قاعدة البيانات',
      message: 'هل تريد تشغيل عملية إصلاح قاعدة البيانات؟\nتحذير: قد تؤثر هذه العملية على البيانات.',
      confirmText: 'إصلاح',
      icon: Icons.build,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إصلاح قاعدة البيانات...');
      
      try {
        // استدعاء API لإصلاح قاعدة البيانات
        await _adminController.repairDatabase();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إصلاح قاعدة البيانات بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إصلاح قاعدة البيانات: $e',
        );
      }
    }
  }

  /// عرض تقرير استخدام النظام
  void _showSystemUsageReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.green),
            SizedBox(width: 8),
            Text('تقرير استخدام النظام'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // سيتم تحميل البيانات الحقيقية من الخادم
                Obx(() {
                  if (_adminController.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final stats = _adminController.statistics;
                  return Column(
                    children: [
                      _buildStatCard(
                        title: 'المستخدمون النشطون',
                        value: '${stats['activeUsers'] ?? 0}',
                        icon: Icons.people,
                        color: Colors.blue,
                      ),
                      _buildStatCard(
                        title: 'إجمالي المستخدمين',
                        value: '${stats['totalUsers'] ?? 0}',
                        icon: Icons.group,
                        color: Colors.green,
                      ),
                      _buildStatCard(
                        title: 'الأدوار النشطة',
                        value: '${stats['activeRoles'] ?? 0}',
                        icon: Icons.admin_panel_settings,
                        color: Colors.orange,
                      ),
                      _buildStatCard(
                        title: 'إجمالي الصلاحيات',
                        value: '${stats['totalPermissions'] ?? 0}',
                        icon: Icons.security,
                        color: Colors.purple,
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تقرير الأخطاء
  void _showErrorReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('تقرير الأخطاء'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Column(
            children: [
              _buildErrorSummary(),
              const SizedBox(height: 16),
              Expanded(
                child: _buildErrorList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _clearErrorLogs(),
            child: const Text('مسح السجلات'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض تقرير الأداء
  void _showPerformanceReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.speed, color: Colors.blue),
            SizedBox(width: 8),
            Text('تقرير الأداء'),
          ],
        ),
        content: SizedBox(
          width: 500,
          height: 400,
          child: SingleChildScrollView(
            child: FutureBuilder<Map<String, dynamic>>(
              future: Get.find<UnifiedAdminApiService>().getSystemStatistics(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text('خطأ في تحميل إحصائيات الأداء: ${snapshot.error}'),
                  );
                }

                final stats = snapshot.data ?? {};
                return Column(
                  children: [
                    _buildStatCard(
                      title: 'وقت الاستجابة',
                      value: '${stats['responseTime'] ?? 'غير متاح'}',
                      icon: Icons.timer,
                      color: Colors.green,
                    ),
                    _buildStatCard(
                      title: 'استخدام المعالج',
                      value: '${stats['cpuUsage'] ?? 'غير متاح'}',
                      icon: Icons.memory,
                      color: Colors.blue,
                    ),
                    _buildStatCard(
                      title: 'استخدام الذاكرة',
                      value: '${stats['memoryUsage'] ?? 'غير متاح'}',
                      icon: Icons.storage,
                      color: Colors.orange,
                    ),
                    _buildStatCard(
                      title: 'استخدام القرص',
                      value: '${stats['diskUsage'] ?? 'غير متاح'}',
                      icon: Icons.storage_outlined,
                      color: Colors.purple,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات التطبيق
  void _showAppSettings() {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.settings, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('إعدادات التطبيق'),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_settingsService.settings.length} إعداد',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildAppSettingRow('app_name', 'اسم التطبيق', Icons.apps),
              _buildAppSettingRow('app_version', 'إصدار التطبيق', Icons.info),
              _buildAppSettingRow('default_language', 'اللغة الافتراضية', Icons.language),
              _buildAppSettingRow('timezone', 'المنطقة الزمنية', Icons.schedule),
              _buildAppSettingRow('date_format', 'تنسيق التاريخ', Icons.date_range),
              _buildAppSettingRow('default_currency', 'العملة الافتراضية', Icons.attach_money),

              const Divider(),

              // أزرار تصدير واستيراد الإعدادات
              if (_permissionService.canAccessAdmin()) ...[
                ListTile(
                  leading: const Icon(Icons.download, color: Colors.green),
                  title: const Text('تصدير الإعدادات'),
                  subtitle: const Text('تصدير جميع الإعدادات إلى ملف'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _exportSettings,
                ),
                ListTile(
                  leading: const Icon(Icons.upload, color: Colors.blue),
                  title: const Text('استيراد الإعدادات'),
                  subtitle: const Text('استيراد الإعدادات من ملف'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _importSettings,
                ),
                ListTile(
                  leading: const Icon(Icons.restore, color: Colors.orange),
                  title: const Text('إعادة تعيين للافتراضية'),
                  subtitle: const Text('إعادة جميع الإعدادات للقيم الافتراضية'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _resetSettingsToDefaults,
                ),
              ],
            ],
          ),
        ),
        actions: [
          if (_permissionService.canAccessAdmin())
            TextButton(
              onPressed: _showAddSettingDialog,
              child: const Text('إضافة إعداد'),
            ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض إعدادات الإشعارات
  void _showNotificationSettings() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.notifications, color: Colors.orange),
            SizedBox(width: 8),
            Text('إعدادات الإشعارات'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildNotificationSettingRow('enable_email_notifications', 'إشعارات البريد الإلكتروني', Icons.email),
              _buildNotificationSettingRow('enable_push_notifications', 'الإشعارات الفورية', Icons.notifications_active),
              _buildNotificationSettingRow('enable_task_notifications', 'إشعارات المهام', Icons.task),
              _buildNotificationSettingRow('enable_message_notifications', 'إشعارات الرسائل', Icons.message),
              _buildNotificationSettingRow('enable_system_notifications', 'إشعارات النظام', Icons.system_update),
              _buildNotificationSettingRow('enable_sound_notifications', 'الإشعارات الصوتية', Icons.volume_up),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _saveNotificationSettings(),
            child: const Text('حفظ'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إعادة تشغيل النظام
  Future<void> _restartSystem() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إعادة تشغيل النظام',
      message: 'هل تريد إعادة تشغيل النظام؟\nسيتم قطع الاتصال مع جميع المستخدمين.',
      confirmText: 'إعادة تشغيل',
      icon: Icons.restart_alt,
      confirmColor: Colors.orange,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إعادة تشغيل النظام...');
      
      try {
        // استدعاء API لإعادة تشغيل النظام
        await _adminController.restartSystem();
        await _adminController.refreshAllData(); // تحديث البيانات
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم إعادة تشغيل النظام بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في إعادة تشغيل النظام: $e',
        );
      }
    }
  }

  // ===== طرق مساعدة موحدة =====

  /// معالجة الأخطاء بشكل موحد
  void _handleError(String operation, dynamic error) {
    String errorMessage;
    if (error.toString().contains('Connection refused') ||
        error.toString().contains('Network is unreachable')) {
      errorMessage = 'لا يمكن الاتصال بالخادم. تحقق من الاتصال بالإنترنت.';
    } else if (error.toString().contains('Unauthorized') ||
               error.toString().contains('401')) {
      errorMessage = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
    } else if (error.toString().contains('Forbidden') ||
               error.toString().contains('403')) {
      errorMessage = 'ليس لديك صلاحية لتنفيذ هذه العملية.';
    } else if (error.toString().contains('Not Found') ||
               error.toString().contains('404')) {
      errorMessage = 'الخدمة المطلوبة غير متاحة حالياً.';
    } else if (error.toString().contains('Timeout')) {
      errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
    } else {
      errorMessage = 'حدث خطأ غير متوقع: ${error.toString()}';
    }

    Get.snackbar(
      'خطأ في $operation',
      errorMessage,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 5),
      margin: const EdgeInsets.all(16),
    );
  }

  /// عرض رسالة نجاح موحدة
  void _showSuccess(String operation, String message) {
    Get.snackbar(
      'نجح $operation',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
    );
  }

  /// بناء صف إعداد موحد
  Widget _buildSettingRow({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
    bool isEditable = true,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title),
      subtitle: Text(value, style: const TextStyle(color: Colors.grey)),
      trailing: isEditable ? const Icon(Icons.edit) : const Icon(Icons.lock, color: Colors.grey),
      onTap: isEditable ? onTap : null,
      enabled: isEditable,
    );
  }

  /// تحرير الحد الأدنى لطول كلمة المرور
  Future<void> _editPasswordMinLength() async {
    final currentValue = _settingsService.getStringValue('password_min_length', defaultValue: '6');
    final controller = TextEditingController(text: currentValue);

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير الحد الأدنى لطول كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الحد الأدنى (أحرف)',
                hintText: 'أدخل رقم بين 6 و 20',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بحد أدنى 8 أحرف للأمان',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      final value = int.tryParse(result);
      if (value != null && value >= 6 && value <= 20) {
        try {
          final success = await _settingsService.updateSetting('password_min_length', result);
          if (success) {
            _showSuccess('تحديث الإعداد', 'تم تحديث الحد الأدنى لطول كلمة المرور');
            setState(() {}); // تحديث الواجهة
          } else {
            _handleError('تحديث الإعداد', _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد');
          }
        } catch (e) {
          _handleError('تحديث الإعداد', e);
        }
      } else {
        Get.snackbar(
          'خطأ',
          'يجب أن يكون الرقم بين 6 و 20',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحرير تعقيد كلمة المرور
  Future<void> _editPasswordComplexity() async {
    final currentValue = _settingsService.getStringValue('password_complexity', defaultValue: 'medium');
    String selectedComplexity = currentValue;

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير متطلبات تعقيد كلمة المرور'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('بسيط'),
                  subtitle: const Text('أحرف وأرقام فقط'),
                  value: 'simple',
                  groupValue: selectedComplexity,
                  onChanged: (value) {
                    setState(() {
                      selectedComplexity = value!;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('متوسط'),
                  subtitle: const Text('أحرف كبيرة وصغيرة وأرقام'),
                  value: 'medium',
                  groupValue: selectedComplexity,
                  onChanged: (value) {
                    setState(() {
                      selectedComplexity = value!;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('معقد'),
                  subtitle: const Text('أحرف كبيرة وصغيرة وأرقام ورموز'),
                  value: 'complex',
                  groupValue: selectedComplexity,
                  onChanged: (value) {
                    setState(() {
                      selectedComplexity = value!;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: selectedComplexity),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result != currentValue) {
      try {
        final success = await _settingsService.updateSetting('password_complexity', result);
        if (success) {
          _showSuccess('تحديث الإعداد', 'تم تحديث متطلبات تعقيد كلمة المرور');
          setState(() {}); // تحديث الواجهة
        } else {
          _handleError('تحديث الإعداد', _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد');
        }
      } catch (e) {
        _handleError('تحديث الإعداد', e);
      }
    }
  }

  /// تحرير انتهاء صلاحية كلمة المرور
  Future<void> _editPasswordExpiry() async {
    final currentValue = _settingsService.getStringValue('password_expiry_days', defaultValue: '90');
    final controller = TextEditingController(text: currentValue);

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير مدة انتهاء صلاحية كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'عدد الأيام',
                hintText: 'أدخل رقم بين 30 و 365',
                border: OutlineInputBorder(),
                suffixText: 'يوم',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بفترة 90 يوم للأمان المتوازن',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      final value = int.tryParse(result);
      if (value != null && value >= 30 && value <= 365) {
        final success = await _settingsService.updateSetting('password_expiry_days', result);
        if (success) {
          Get.snackbar(
            'نجح',
            'تم تحديث مدة انتهاء صلاحية كلمة المرور',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          setState(() {}); // تحديث الواجهة
        } else {
          Get.snackbar(
            'خطأ',
            _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          'خطأ',
          'يجب أن يكون الرقم بين 30 و 365',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحرير تاريخ كلمات المرور
  Future<void> _editPasswordHistory() async {
    final currentValue = _settingsService.getStringValue('password_history_count', defaultValue: '5');
    final controller = TextEditingController(text: currentValue);

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير عدد كلمات المرور المحفوظة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'عدد كلمات المرور',
                hintText: 'أدخل رقم بين 3 و 10',
                border: OutlineInputBorder(),
                suffixText: 'كلمة مرور',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بحفظ 5 كلمات مرور سابقة',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      final value = int.tryParse(result);
      if (value != null && value >= 3 && value <= 10) {
        final success = await _settingsService.updateSetting('password_history_count', result);
        if (success) {
          Get.snackbar(
            'نجح',
            'تم تحديث عدد كلمات المرور المحفوظة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          setState(() {}); // تحديث الواجهة
        } else {
          Get.snackbar(
            'خطأ',
            _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          'خطأ',
          'يجب أن يكون الرقم بين 3 و 10',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحرير مهلة انتهاء الجلسة
  Future<void> _editSessionTimeout() async {
    final currentValue = _settingsService.getStringValue('session_timeout', defaultValue: '30');
    final controller = TextEditingController(text: currentValue);

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير مهلة انتهاء الجلسة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'المهلة بالدقائق',
                hintText: 'أدخل رقم بين 5 و 120',
                border: OutlineInputBorder(),
                suffixText: 'دقيقة',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بمهلة 30 دقيقة للأمان',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      final value = int.tryParse(result);
      if (value != null && value >= 5 && value <= 120) {
        final success = await _settingsService.updateSetting('session_timeout', result);
        if (success) {
          Get.snackbar(
            'نجح',
            'تم تحديث مهلة انتهاء الجلسة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          setState(() {}); // تحديث الواجهة
        } else {
          Get.snackbar(
            'خطأ',
            _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          'خطأ',
          'يجب أن يكون الرقم بين 5 و 120',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحرير الحد الأقصى للجلسات المتزامنة
  Future<void> _editMaxConcurrentSessions() async {
    final currentValue = _settingsService.getStringValue('max_concurrent_sessions', defaultValue: '3');
    final controller = TextEditingController(text: currentValue);

    final result = await Get.dialog<String>(
      AlertDialog(
        title: const Text('تحرير الحد الأقصى للجلسات المتزامنة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'عدد الجلسات',
                hintText: 'أدخل رقم بين 1 و 10',
                border: OutlineInputBorder(),
                suffixText: 'جلسة',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بحد أقصى 3 جلسات متزامنة',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      final value = int.tryParse(result);
      if (value != null && value >= 1 && value <= 10) {
        final success = await _settingsService.updateSetting('max_concurrent_sessions', result);
        if (success) {
          Get.snackbar(
            'نجح',
            'تم تحديث الحد الأقصى للجلسات المتزامنة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          setState(() {}); // تحديث الواجهة
        } else {
          Get.snackbar(
            'خطأ',
            _settingsService.error.isNotEmpty ? _settingsService.error : 'فشل في تحديث الإعداد',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          'خطأ',
          'يجب أن يكون الرقم بين 1 و 10',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تحرير تذكر تسجيل الدخول
  Future<void> _editRememberLogin() async {
    final currentEnabled = _settingsService.getBoolValue('remember_login', defaultValue: true);
    final currentDays = _settingsService.getStringValue('remember_login_days', defaultValue: '7');

    bool isEnabled = currentEnabled;
    final daysController = TextEditingController(text: currentDays);

    final result = await Get.dialog<Map<String, dynamic>>(
      AlertDialog(
        title: const Text('تحرير إعدادات تذكر تسجيل الدخول'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SwitchListTile(
                  title: const Text('تفعيل تذكر تسجيل الدخول'),
                  value: isEnabled,
                  onChanged: (value) {
                    setState(() {
                      isEnabled = value;
                    });
                  },
                ),
                if (isEnabled) ...[
                  const SizedBox(height: 16),
                  TextField(
                    controller: daysController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'مدة التذكر بالأيام',
                      hintText: 'أدخل رقم بين 1 و 30',
                      border: OutlineInputBorder(),
                      suffixText: 'يوم',
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'يُنصح بمدة 7 أيام للأمان',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: {
              'enabled': isEnabled,
              'days': daysController.text,
            }),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null) {
      bool success = true;

      // تحديث حالة التفعيل
      if (result['enabled'] != currentEnabled) {
        success = await _settingsService.updateSetting('remember_login', result['enabled'].toString());
      }

      // تحديث عدد الأيام إذا كان مفعلاً
      if (success && result['enabled'] == true && result['days'] != currentDays) {
        final days = int.tryParse(result['days']);
        if (days != null && days >= 1 && days <= 30) {
          success = await _settingsService.updateSetting('remember_login_days', result['days']);
        } else {
          success = false;
          Get.snackbar(
            'خطأ',
            'يجب أن يكون عدد الأيام بين 1 و 30',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }

      if (success) {
        Get.snackbar(
          'نجح',
          'تم تحديث إعدادات تذكر تسجيل الدخول',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        setState(() {}); // تحديث الواجهة
      } else if (_settingsService.error.isNotEmpty) {
        Get.snackbar(
          'خطأ',
          _settingsService.error,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// إنهاء جميع الجلسات النشطة
  Future<void> _terminateAllSessions() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إنهاء جميع الجلسات',
      message: 'هل تريد إنهاء جميع الجلسات النشطة؟\nسيتم تسجيل خروج جميع المستخدمين.',
      confirmText: 'إنهاء',
      cancelText: 'إلغاء',
      icon: Icons.logout,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إنهاء الجلسات...');

      try {
        // استدعاء API لإنهاء جميع الجلسات
        await Get.find<UnifiedAdminApiService>().terminateAllSessions();

        AdminLoadingDialog.hide();
        _showSuccess('إنهاء الجلسات', 'تم إنهاء جميع الجلسات النشطة');
      } catch (e) {
        AdminLoadingDialog.hide();
        _handleError('إنهاء الجلسات', e);
      }
    }
  }

  /// الحصول على لون نوع السجل
  Color _getLogTypeColor(String action) {
    switch (action.toLowerCase()) {
      case 'create':
      case 'إنشاء':
        return Colors.green;
      case 'update':
      case 'تحديث':
        return Colors.blue;
      case 'delete':
      case 'حذف':
        return Colors.red;
      case 'login':
      case 'تسجيل دخول':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع السجل
  IconData _getLogTypeIcon(String action) {
    switch (action.toLowerCase()) {
      case 'create':
      case 'إنشاء':
        return Icons.add;
      case 'update':
      case 'تحديث':
        return Icons.edit;
      case 'delete':
      case 'حذف':
        return Icons.delete;
      case 'login':
      case 'تسجيل دخول':
        return Icons.login;
      default:
        return Icons.info;
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(dynamic dateTime) {
    if (dateTime == null) return 'غير محدد';

    DateTime dt;
    if (dateTime is DateTime) {
      dt = dateTime;
    } else if (dateTime is String) {
      dt = DateTime.tryParse(dateTime) ?? DateTime.now();
    } else if (dateTime is int) {
      dt = DateTime.fromMillisecondsSinceEpoch(dateTime);
    } else {
      return 'تاريخ غير صالح';
    }

    return '${dt.day}/${dt.month}/${dt.year} ${dt.hour}:${dt.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق حجم الملف
  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toStringAsFixed(0)} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// عرض تفاصيل السجل
  void _showLogDetails(dynamic log) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل السجل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الإجراء: ${log.action}'),
            Text('التفاصيل: ${log.details ?? 'لا يوجد'}'),
            Text('وصف التغيير: ${log.changeDescription ?? 'لا يوجد'}'),
            Text('المستخدم: ${log.userId}'),
            Text('المهمة: ${log.taskId > 0 ? log.taskId : 'عام'}'),
            Text('التاريخ: ${_formatDateTime(log.timestamp)}'),
            if (log.oldValue != null) Text('القيمة القديمة: ${log.oldValue}'),
            if (log.newValue != null) Text('القيمة الجديدة: ${log.newValue}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تأكيد استعادة النسخة الاحتياطية
  Future<void> _confirmRestoreBackup(dynamic backup) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'تأكيد الاستعادة',
      message: 'هل تريد استعادة هذه النسخة الاحتياطية؟\n\nتحذير: سيتم استبدال البيانات الحالية!',
      confirmText: 'استعادة',
      icon: Icons.warning,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري استعادة النسخة الاحتياطية...');

      try {
        await _adminController.restoreBackup(backup.id);
        await _adminController.refreshAllData();

        AdminLoadingDialog.hide();
        Get.back(); // إغلاق الحوار
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم استعادة النسخة الاحتياطية بنجاح',
        );
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في استعادة النسخة الاحتياطية: $e',
        );
      }
    }
  }

  // ===== طرق مساعدة إضافية =====

  /// بناء صف معلومات قاعدة البيانات
  Widget _buildDatabaseInfoRow(String label, String value, {bool isStatus = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          isStatus
              ? Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(value, style: const TextStyle(color: Colors.white, fontSize: 12)),
                )
              : Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// اختبار اتصال قاعدة البيانات
  Future<void> _testDatabaseConnection() async {
    AdminLoadingDialog.show(message: 'جاري اختبار الاتصال...');

    try {
      // استدعاء API لاختبار الاتصال
      await Get.find<UnifiedAdminApiService>().testDatabaseConnection();

      AdminLoadingDialog.hide();
      _showSuccess('اختبار الاتصال', 'الاتصال بقاعدة البيانات ناجح');
    } catch (e) {
      AdminLoadingDialog.hide();
      _handleError('اختبار الاتصال', e);
    }
  }

  /// بناء بطاقة إحصائية موحدة
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(title),
        trailing: Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  /// بناء ملخص الأخطاء
  Widget _buildErrorSummary() {
    return Row(
      children: [
        Expanded(
          child: _buildErrorSummaryCard('أخطاء اليوم', '3', Colors.red),
        ),
        Expanded(
          child: _buildErrorSummaryCard('تحذيرات', '12', Colors.orange),
        ),
        Expanded(
          child: _buildErrorSummaryCard('معلومات', '45', Colors.blue),
        ),
      ],
    );
  }

  /// بناء بطاقة ملخص الأخطاء
  Widget _buildErrorSummaryCard(String title, String count, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              count,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(title, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الأخطاء
  Widget _buildErrorList() {
    final errors = [
      {'level': 'خطأ', 'message': 'فشل في الاتصال بقاعدة البيانات', 'time': '10:30'},
      {'level': 'تحذير', 'message': 'استخدام مرتفع للذاكرة', 'time': '09:15'},
      {'level': 'معلومات', 'message': 'تم تسجيل دخول مستخدم جديد', 'time': '08:45'},
    ];

    return ListView.builder(
      itemCount: errors.length,
      itemBuilder: (context, index) {
        final error = errors[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getErrorLevelColor(error['level']!),
            child: Icon(
              _getErrorLevelIcon(error['level']!),
              color: Colors.white,
              size: 16,
            ),
          ),
          title: Text(error['message']!),
          subtitle: Text('الوقت: ${error['time']}'),
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _deleteError(index),
          ),
        );
      },
    );
  }

  /// الحصول على لون مستوى الخطأ
  Color _getErrorLevelColor(String level) {
    switch (level) {
      case 'خطأ': return Colors.red;
      case 'تحذير': return Colors.orange;
      case 'معلومات': return Colors.blue;
      default: return Colors.grey;
    }
  }

  /// الحصول على أيقونة مستوى الخطأ
  IconData _getErrorLevelIcon(String level) {
    switch (level) {
      case 'خطأ': return Icons.error;
      case 'تحذير': return Icons.warning;
      case 'معلومات': return Icons.info;
      default: return Icons.help;
    }
  }

  /// حذف خطأ
  void _deleteError(int index) {
    Get.snackbar('حذف', 'تم حذف الخطأ');
  }

  /// مسح سجلات الأخطاء
  void _clearErrorLogs() {
    Get.snackbar('مسح', 'تم مسح جميع سجلات الأخطاء');
  }



  /// بناء صف إعداد التطبيق
  Widget _buildAppSettingRow(String settingKey, String title, IconData icon) {
    final setting = _settingsService.getSetting(settingKey);
    final value = setting?.value ?? 'غير محدد';

    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(value),
      trailing: _permissionService.canAccessAdmin() && (setting?.isEditable ?? false)
          ? const Icon(Icons.edit)
          : setting?.isEditable == false
              ? const Icon(Icons.lock, color: Colors.grey)
              : null,
      onTap: _permissionService.canAccessAdmin() && (setting?.isEditable ?? false)
          ? () => _editAppSetting(settingKey)
          : null,
    );
  }

  /// تحرير إعداد التطبيق
  void _editAppSetting(String settingKey) {
    final setting = _settingsService.getSetting(settingKey);
    if (setting == null) {
      Get.snackbar('خطأ', 'الإعداد غير موجود');
      return;
    }

    if (!setting.isEditable) {
      Get.snackbar('تحذير', 'هذا الإعداد غير قابل للتعديل');
      return;
    }

    final controller = TextEditingController(text: setting.value);

    Get.dialog(
      AlertDialog(
        title: Text('تعديل ${setting.description ?? setting.key}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المفتاح: ${setting.key}'),
            const SizedBox(height: 8),
            Text('النوع: ${setting.dataType}'),
            const SizedBox(height: 16),
            if (setting.dataType == SettingsDataType.boolean)
              DropdownButtonFormField<String>(
                value: setting.boolValue ? 'true' : 'false',
                decoration: const InputDecoration(labelText: 'القيمة'),
                items: const [
                  DropdownMenuItem(value: 'true', child: Text('مفعل')),
                  DropdownMenuItem(value: 'false', child: Text('معطل')),
                ],
                onChanged: (value) => controller.text = value ?? 'false',
              )
            else
              TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: 'القيمة الجديدة',
                  hintText: setting.description,
                ),
                keyboardType: setting.dataType == SettingsDataType.integer ||
                        setting.dataType == SettingsDataType.decimal
                    ? TextInputType.number
                    : TextInputType.text,
                obscureText: setting.dataType == SettingsDataType.password,
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _updateSetting(setting, controller.text),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// تحديث إعداد
  Future<void> _updateSetting(SystemSettings setting, String newValue) async {
    if (!_settingsService.validateSettingValue(setting, newValue)) {
      Get.snackbar('خطأ', 'القيمة المدخلة غير صحيحة');
      return;
    }

    final success = await _settingsService.updateSetting(setting.key, newValue);

    Get.back(); // إغلاق الحوار

    if (success) {
      Get.snackbar(
        'نجح',
        'تم تحديث الإعداد بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      setState(() {}); // تحديث الواجهة
    } else {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث الإعداد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// بناء صف إعداد الإشعارات
  Widget _buildNotificationSettingRow(String settingKey, String title, IconData icon) {
    final setting = _settingsService.getSetting(settingKey);
    final value = setting?.boolValue ?? false;

    return ListTile(
      leading: Icon(icon, color: Colors.orange),
      title: Text(title),
      trailing: _permissionService.canAccessAdmin()
          ? Switch(
              value: value,
              onChanged: (newValue) => _toggleNotificationSetting(settingKey, newValue),
            )
          : Switch(
              value: value,
              onChanged: null, // معطل
            ),
    );
  }

  /// تبديل إعداد الإشعارات
  Future<void> _toggleNotificationSetting(String settingKey, bool value) async {
    final success = await _settingsService.updateSetting(settingKey, value.toString());

    if (success) {
      Get.snackbar(
        'تحديث',
        'تم ${value ? 'تفعيل' : 'إلغاء'} $settingKey',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      setState(() {}); // تحديث الواجهة
    } else {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث الإعداد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// حفظ إعدادات الإشعارات
  void _saveNotificationSettings() {
    Get.back();
    Get.snackbar('حفظ', 'تم حفظ إعدادات الإشعارات');
  }

  /// إضافة إعداد جديد
  void _showAddSettingDialog() {
    final keyController = TextEditingController();
    final valueController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedCategory = SettingsCategory.general;
    String selectedDataType = SettingsDataType.string;
    bool isEditable = true;
    bool isVisible = true;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة إعداد جديد'),
        content: SizedBox(
          width: 400,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: keyController,
                  decoration: const InputDecoration(
                    labelText: 'مفتاح الإعداد *',
                    hintText: 'مثال: new_feature_enabled',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: valueController,
                  decoration: const InputDecoration(
                    labelText: 'القيمة *',
                    hintText: 'القيمة الافتراضية للإعداد',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    hintText: 'وصف مختصر للإعداد',
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return Column(
                      children: [
                        DropdownButtonFormField<String>(
                          value: selectedCategory,
                          decoration: const InputDecoration(labelText: 'الفئة'),
                          items: const [
                            DropdownMenuItem(value: SettingsCategory.general, child: Text('عام')),
                            DropdownMenuItem(value: SettingsCategory.security, child: Text('الأمان')),
                            DropdownMenuItem(value: SettingsCategory.notifications, child: Text('الإشعارات')),
                            DropdownMenuItem(value: SettingsCategory.appearance, child: Text('المظهر')),
                            DropdownMenuItem(value: SettingsCategory.performance, child: Text('الأداء')),
                            DropdownMenuItem(value: SettingsCategory.backup, child: Text('النسخ الاحتياطية')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedCategory = value ?? SettingsCategory.general;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: selectedDataType,
                          decoration: const InputDecoration(labelText: 'نوع البيانات'),
                          items: const [
                            DropdownMenuItem(value: SettingsDataType.string, child: Text('نص')),
                            DropdownMenuItem(value: SettingsDataType.boolean, child: Text('منطقي')),
                            DropdownMenuItem(value: SettingsDataType.integer, child: Text('رقم صحيح')),
                            DropdownMenuItem(value: SettingsDataType.decimal, child: Text('رقم عشري')),
                            DropdownMenuItem(value: SettingsDataType.password, child: Text('كلمة مرور')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedDataType = value ?? SettingsDataType.string;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        CheckboxListTile(
                          title: const Text('قابل للتعديل'),
                          value: isEditable,
                          onChanged: (value) {
                            setState(() {
                              isEditable = value ?? true;
                            });
                          },
                        ),
                        CheckboxListTile(
                          title: const Text('مرئي'),
                          value: isVisible,
                          onChanged: (value) {
                            setState(() {
                              isVisible = value ?? true;
                            });
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _addNewSetting(
              keyController.text,
              valueController.text,
              descriptionController.text,
              selectedCategory,
              selectedDataType,
              isEditable,
              isVisible,
            ),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// إضافة إعداد جديد
  Future<void> _addNewSetting(
    String key,
    String value,
    String description,
    String category,
    String dataType,
    bool isEditable,
    bool isVisible,
  ) async {
    if (key.trim().isEmpty || value.trim().isEmpty) {
      Get.snackbar('خطأ', 'يجب ملء الحقول المطلوبة');
      return;
    }

    // التحقق من عدم وجود إعداد بنفس المفتاح
    if (_settingsService.getSetting(key) != null) {
      Get.snackbar('خطأ', 'يوجد إعداد بنفس المفتاح بالفعل');
      return;
    }

    final newSetting = SystemSettings(
      id: 0, // سيتم تعيينه من الخادم
      key: key.trim(),
      value: value.trim(),
      description: description.trim().isEmpty ? null : description.trim(),
      category: category,
      dataType: dataType,
      isEditable: isEditable,
      isVisible: isVisible,
    );

    final success = await _settingsService.addSetting(newSetting);

    Get.back(); // إغلاق الحوار

    if (success) {
      Get.snackbar(
        'نجح',
        'تم إضافة الإعداد بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      setState(() {}); // تحديث الواجهة
    } else {
      Get.snackbar(
        'خطأ',
        'فشل في إضافة الإعداد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// تصدير الإعدادات
  Future<void> _exportSettings() async {
    try {
      AdminLoadingDialog.show(message: 'جاري تصدير الإعدادات...');

      final exportData = await _settingsService.exportSettings();

      // TODO: إضافة وظيفة حفظ الملف
      // يمكن استخدام file_picker أو path_provider لحفظ الملف

      AdminLoadingDialog.hide();

      Get.dialog(
        AlertDialog(
          title: const Text('تصدير الإعدادات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('تم تصدير الإعدادات بنجاح'),
              const SizedBox(height: 16),
              SelectableText(
                'عدد الإعدادات: ${exportData['settings']?.length ?? 0}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              SelectableText(
                'تاريخ التصدير: ${exportData['exported_at']}',
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      AdminLoadingDialog.hide();
      Get.snackbar(
        'خطأ',
        'فشل في تصدير الإعدادات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// استيراد الإعدادات
  Future<void> _importSettings() async {
    // TODO: إضافة وظيفة اختيار الملف
    // يمكن استخدام file_picker لاختيار ملف JSON

    Get.dialog(
      AlertDialog(
        title: const Text('استيراد الإعدادات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.info, size: 48, color: Colors.blue),
            SizedBox(height: 16),
            Text('هذه الوظيفة قيد التطوير'),
            SizedBox(height: 8),
            Text(
              'سيتم إضافة إمكانية اختيار ملف JSON واستيراد الإعدادات منه',
              style: TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// إعادة تعيين الإعدادات للافتراضية
  Future<void> _resetSettingsToDefaults() async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'إعادة تعيين الإعدادات',
      message: 'هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\nسيتم فقدان جميع التخصيصات الحالية.',
      confirmText: 'إعادة تعيين',
      icon: Icons.restore,
      confirmColor: Colors.orange,
    );

    if (confirmed) {
      AdminLoadingDialog.show(message: 'جاري إعادة تعيين الإعدادات...');

      try {
        final success = await _settingsService.resetToDefaults();

        AdminLoadingDialog.hide();

        if (success) {
          AdminMessageDialog.showSuccess(
            title: 'نجح العملية',
            message: 'تم إعادة تعيين الإعدادات للقيم الافتراضية بنجاح',
          );
          setState(() {}); // تحديث الواجهة
        } else {
          AdminMessageDialog.showError(
            title: 'فشل العملية',
            message: 'فشل في إعادة تعيين الإعدادات',
          );
        }
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'حدث خطأ أثناء إعادة تعيين الإعدادات: $e',
        );
      }
    }
  }
}
