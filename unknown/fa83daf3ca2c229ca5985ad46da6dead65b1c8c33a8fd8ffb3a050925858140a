import 'package:flutter/material.dart';

/// عرض المخطط بملء الشاشة
class FullscreenChartView extends StatelessWidget {
  final Widget chart;
  final String title;

  const FullscreenChartView({
    super.key,
    required this.chart,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: chart,
      ),
    );
  }
}
