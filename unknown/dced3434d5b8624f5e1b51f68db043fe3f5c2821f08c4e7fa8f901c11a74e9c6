import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/chat_group_models.dart';
import '../../models/notification_models.dart';
import '../../services/api/notification_settings_api_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/app_colors.dart';

/// فئة خيار الكتم
class MuteOption {
  final String title;
  final String subtitle;
  final Duration? duration; // null للكتم الدائم
  final IconData icon;

  const MuteOption({
    required this.title,
    required this.subtitle,
    required this.duration,
    required this.icon,
  });
}

/// شاشة كتم الإشعارات
class MuteNotificationsScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const MuteNotificationsScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<MuteNotificationsScreen> createState() => _MuteNotificationsScreenState();
}

class _MuteNotificationsScreenState extends State<MuteNotificationsScreen> {
  final NotificationSettingsApiService _settingsApiService = NotificationSettingsApiService();
  final AuthController _authController = Get.find<AuthController>();

  final RxBool _isLoading = false.obs;
  final RxBool _isSaving = false.obs;
  final Rx<NotificationSettingModel?> _currentSetting = Rx<NotificationSettingModel?>(null);

  // خيارات الكتم
  final List<MuteOption> _muteOptions = [
    MuteOption(
      title: 'كتم لمدة ساعة واحدة',
      subtitle: 'سيتم إعادة تفعيل الإشعارات تلقائياً',
      duration: const Duration(hours: 1),
      icon: Icons.schedule,
    ),
    MuteOption(
      title: 'كتم لمدة 8 ساعات',
      subtitle: 'حتى نهاية يوم العمل',
      duration: const Duration(hours: 8),
      icon: Icons.work_off,
    ),
    MuteOption(
      title: 'كتم لمدة 24 ساعة',
      subtitle: 'كتم ليوم كامل',
      duration: const Duration(days: 1),
      icon: Icons.today,
    ),
    MuteOption(
      title: 'كتم لمدة أسبوع',
      subtitle: 'كتم لمدة 7 أيام',
      duration: const Duration(days: 7),
      icon: Icons.date_range,
    ),
    MuteOption(
      title: 'كتم دائم',
      subtitle: 'لن تصلك إشعارات من هذه المحادثة',
      duration: null, // null يعني كتم دائم
      icon: Icons.notifications_off,
    ),
  ];

  final Rx<MuteOption?> _selectedOption = Rx<MuteOption?>(null);

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  /// تحميل الإعدادات الحالية
  Future<void> _loadCurrentSettings() async {
    _isLoading.value = true;
    try {
      final currentUserId = _authController.currentUser.value?.id;
      if (currentUserId != null) {
        // البحث عن إعداد كتم الإشعارات لهذه المجموعة
        final settings = await _settingsApiService.getSettingsByUserId(currentUserId);

        // البحث عن إعداد خاص بهذه المجموعة
        final groupSetting = settings.firstWhereOrNull(
          (setting) => setting.notificationType == 'group_${widget.chatGroup.id}',
        );

        _currentSetting.value = groupSetting;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق إعدادات الكتم
  Future<void> _applyMuteSetting() async {
    if (_selectedOption.value == null) {
      _showWarningSnackbar('يرجى اختيار خيار الكتم');
      return;
    }

    _isSaving.value = true;
    try {
      final currentUserId = _authController.currentUser.value?.id;
      if (currentUserId == null) {
        _showErrorSnackbar('خطأ في تحديد المستخدم الحالي');
        return;
      }

      final option = _selectedOption.value!;
      final notificationType = 'group_${widget.chatGroup.id}';

      // إنشاء أو تحديث إعداد الإشعارات
      final setting = NotificationSettingModel(
        id: _currentSetting.value?.id ?? 0,
        userId: currentUserId,
        notificationType: notificationType,
        isEnabled: false, // كتم الإشعارات
        deliveryMethod: 'none',
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isEmailEnabled: false,
        isPushEnabled: false,
        isSmsEnabled: false,
      );

      if (_currentSetting.value == null) {
        // إنشاء إعداد جديد
        await _settingsApiService.createSetting(setting);
      } else {
        // تحديث الإعداد الموجود
        await _settingsApiService.updateSetting(_currentSetting.value!.id, setting);
      }

      _showSuccessSnackbar(
        option.duration == null
            ? 'تم كتم الإشعارات نهائياً'
            : 'تم كتم الإشعارات لمدة ${_formatDuration(option.duration!)}',
      );

      // العودة إلى الشاشة السابقة
      Get.back(result: true);
    } catch (e) {
      _showErrorSnackbar('خطأ في حفظ الإعدادات: $e');
    } finally {
      _isSaving.value = false;
    }
  }

  /// إلغاء كتم الإشعارات
  Future<void> _unmute() async {
    _isSaving.value = true;
    try {
      if (_currentSetting.value != null) {
        // حذف الإعداد أو تعديله لتفعيل الإشعارات
        final updatedSetting = _currentSetting.value!.copyWith(
          isEnabled: true,
          isPushEnabled: true,
          deliveryMethod: 'push',
        );

        await _settingsApiService.updateSetting(_currentSetting.value!.id, updatedSetting);

        _showSuccessSnackbar('تم إلغاء كتم الإشعارات');
        Get.back(result: true);
      }
    } catch (e) {
      _showErrorSnackbar('خطأ في إلغاء كتم الإشعارات: $e');
    } finally {
      _isSaving.value = false;
    }
  }

  /// تنسيق مدة الكتم
  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} ${duration.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ${duration.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else {
      return '${duration.inMinutes} دقيقة';
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      icon: const Icon(Icons.check_circle, color: Colors.green),
    );
  }

  /// عرض رسالة تحذير
  void _showWarningSnackbar(String message) {
    Get.snackbar(
      'تنبيه',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.shade100,
      colorText: Colors.orange.shade800,
      icon: const Icon(Icons.warning, color: Colors.orange),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      icon: const Icon(Icons.error, color: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كتم الإشعارات - ${widget.chatGroup.name}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل الإعدادات...'),
              ],
            ),
          );
        }

        return Column(
          children: [
            // معلومات الحالة الحالية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _currentSetting.value?.isEnabled == false
                    ? Colors.orange.shade50
                    : Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _currentSetting.value?.isEnabled == false
                      ? Colors.orange.shade200
                      : Colors.green.shade200,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _currentSetting.value?.isEnabled == false
                            ? Icons.notifications_off
                            : Icons.notifications_active,
                        color: _currentSetting.value?.isEnabled == false
                            ? Colors.orange.shade700
                            : Colors.green.shade700,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الحالة الحالية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _currentSetting.value?.isEnabled == false
                              ? Colors.orange.shade700
                              : Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _currentSetting.value?.isEnabled == false
                        ? 'الإشعارات مكتومة حالياً'
                        : 'الإشعارات مفعلة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // إذا كانت الإشعارات مكتومة، عرض زر إلغاء الكتم
            if (_currentSetting.value?.isEnabled == false) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isSaving.value ? null : _unmute,
                    icon: _isSaving.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.notifications_active),
                    label: const Text('إلغاء كتم الإشعارات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(16),
                child: Divider(),
              ),
            ],

            // عنوان خيارات الكتم
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'خيارات الكتم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),

            // قائمة خيارات الكتم
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _muteOptions.length,
                itemBuilder: (context, index) {
                  final option = _muteOptions[index];
                  final isSelected = _selectedOption.value == option;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: RadioListTile<MuteOption>(
                      value: option,
                      groupValue: _selectedOption.value,
                      onChanged: (value) => _selectedOption.value = value,
                      title: Text(
                        option.title,
                        style: TextStyle(
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      subtitle: Text(option.subtitle),
                      secondary: Icon(
                        option.icon,
                        color: isSelected
                            ? AppColors.primary
                            : Colors.grey.shade600,
                      ),
                      activeColor: AppColors.primary,
                    ),
                  );
                },
              ),
            ),

            // زر تطبيق الإعدادات
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isSaving.value ? null : _applyMuteSetting,
                  icon: _isSaving.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.notifications_off),
                  label: const Text('تطبيق إعدادات الكتم'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
