// import 'dart:convert';
// import 'package:flutter/foundation.dart';
// import 'package:http/http.dart' as http;


// /// أداة تشخيص مشاكل الترميز العربي
// class EncodingDiagnostics {
//   // static const String _baseUrl = AppConfig.apiUrl;

//   /// اختبار شامل للترميز العربي
//   static Future<Map<String, dynamic>> runEncodingTest() async {
//     final results = <String, dynamic>{};
    
//     debugPrint('🔍 بدء اختبار الترميز العربي...');
    
//     try {
//       // اختبار 1: فحص استجابة API الخام
//       results['rawApiTest'] = await _testRawApiResponse();
      
//       // اختبار 2: فحص ترميز UTF-8
//       results['utf8Test'] = await _testUtf8Encoding();
      
//       // اختبار 3: فحص تحليل JSON
//       results['jsonParsingTest'] = await _testJsonParsing();
      
//       // اختبار 4: فحص عينة من المهام
//       results['taskSampleTest'] = await _testTaskSample();
      
//       debugPrint('✅ انتهى اختبار الترميز العربي');
//       return results;
      
//     } catch (e) {
//       debugPrint('❌ خطأ في اختبار الترميز: $e');
//       results['error'] = e.toString();
//       return results;
//     }
//   }

//   /// اختبار استجابة API الخام
//   static Future<Map<String, dynamic>> _testRawApiResponse() async {
//     try {
//       debugPrint('🌐 اختبار استجابة API الخام...');
      
//       final response = await http.get(
//         Uri.parse('$_baseUrl/api/Tasks?pageSize=1'),
//         headers: {
//           'Content-Type': 'application/json; charset=utf-8',
//           'Accept': 'application/json; charset=utf-8',
//         },
//       ).timeout(const Duration(seconds: 10));
      
//       return {
//         'success': true,
//         'statusCode': response.statusCode,
//         'contentType': response.headers['content-type'],
//         'contentLength': response.body.length,
//         'rawBodySample': response.body.length > 200 
//             ? response.body.substring(0, 200) + '...'
//             : response.body,
//         'bodyBytes': response.bodyBytes.take(50).toList(),
//         'headers': response.headers,
//       };
//     } catch (e) {
//       debugPrint('❌ فشل اختبار API الخام: $e');
//       return {
//         'success': false,
//         'error': e.toString(),
//       };
//     }
//   }

//   /// اختبار ترميز UTF-8
//   static Future<Map<String, dynamic>> _testUtf8Encoding() async {
//     try {
//       debugPrint('🔤 اختبار ترميز UTF-8...');
      
//       final response = await http.get(
//         Uri.parse('$_baseUrl/api/Tasks?pageSize=1'),
//         headers: {
//           'Content-Type': 'application/json; charset=utf-8',
//           'Accept': 'application/json; charset=utf-8',
//         },
//       );
      
//       if (response.statusCode == 200) {
//         // محاولة فك الترميز بطرق مختلفة
//         String utf8Decoded;
//         String rawBody = response.body;
        
//         try {
//           utf8Decoded = utf8.decode(response.bodyBytes);
//         } catch (e) {
//           utf8Decoded = 'فشل فك الترميز: $e';
//         }
        
//         return {
//           'success': true,
//           'rawBody': rawBody.length > 100 ? rawBody.substring(0, 100) + '...' : rawBody,
//           'utf8Decoded': utf8Decoded.length > 100 ? utf8Decoded.substring(0, 100) + '...' : utf8Decoded,
//           'areEqual': rawBody == utf8Decoded,
//           'rawBodyLength': rawBody.length,
//           'utf8DecodedLength': utf8Decoded.length,
//         };
//       } else {
//         return {
//           'success': false,
//           'statusCode': response.statusCode,
//           'error': 'HTTP Error: ${response.statusCode}',
//         };
//       }
//     } catch (e) {
//       debugPrint('❌ فشل اختبار UTF-8: $e');
//       return {
//         'success': false,
//         'error': e.toString(),
//       };
//     }
//   }

//   /// اختبار تحليل JSON
//   static Future<Map<String, dynamic>> _testJsonParsing() async {
//     try {
//       debugPrint('📄 اختبار تحليل JSON...');
      
//       final response = await http.get(
//         Uri.parse('$_baseUrl/api/Tasks?pageSize=1'),
//         headers: {
//           'Content-Type': 'application/json; charset=utf-8',
//           'Accept': 'application/json; charset=utf-8',
//         },
//       );
      
//       if (response.statusCode == 200) {
//         // تحليل JSON بطرق مختلفة
//         dynamic rawJsonParsed;
//         dynamic utf8JsonParsed;
        
//         try {
//           rawJsonParsed = jsonDecode(response.body);
//         } catch (e) {
//           rawJsonParsed = 'فشل تحليل JSON الخام: $e';
//         }
        
//         try {
//           final utf8Body = utf8.decode(response.bodyBytes);
//           utf8JsonParsed = jsonDecode(utf8Body);
//         } catch (e) {
//           utf8JsonParsed = 'فشل تحليل JSON بـ UTF-8: $e';
//         }
        
//         return {
//           'success': true,
//           'rawJsonType': rawJsonParsed.runtimeType.toString(),
//           'utf8JsonType': utf8JsonParsed.runtimeType.toString(),
//           'rawJsonSample': _extractSampleData(rawJsonParsed),
//           'utf8JsonSample': _extractSampleData(utf8JsonParsed),
//         };
//       } else {
//         return {
//           'success': false,
//           'statusCode': response.statusCode,
//           'error': 'HTTP Error: ${response.statusCode}',
//         };
//       }
//     } catch (e) {
//       debugPrint('❌ فشل اختبار تحليل JSON: $e');
//       return {
//         'success': false,
//         'error': e.toString(),
//       };
//     }
//   }

//   /// اختبار عينة من المهام
//   static Future<Map<String, dynamic>> _testTaskSample() async {
//     try {
//       debugPrint('📋 اختبار عينة من المهام...');
      
//       final response = await http.get(
//         Uri.parse('$_baseUrl/api/Tasks?pageSize=3'),
//         headers: {
//           'Content-Type': 'application/json; charset=utf-8',
//           'Accept': 'application/json; charset=utf-8',
//         },
//       );
      
//       if (response.statusCode == 200) {
//         final utf8Body = utf8.decode(response.bodyBytes);
//         final data = jsonDecode(utf8Body);
        
//         List<Map<String, dynamic>> taskSamples = [];
        
//         if (data is Map<String, dynamic> && data.containsKey('data')) {
//           final tasks = data['data'] as List;
//           for (var task in tasks.take(3)) {
//             if (task is Map<String, dynamic>) {
//               taskSamples.add({
//                 'id': task['id'],
//                 'title': task['title'],
//                 'titleLength': task['title']?.toString().length ?? 0,
//                 'titleBytes': utf8.encode(task['title']?.toString() ?? '').length,
//                 'hasArabicChars': _containsArabic(task['title']?.toString() ?? ''),
//                 'isQuestionMarks': (task['title']?.toString() ?? '').contains('?'),
//               });
//             }
//           }
//         } else if (data is List) {
//           for (var task in data.take(3)) {
//             if (task is Map<String, dynamic>) {
//               taskSamples.add({
//                 'id': task['id'],
//                 'title': task['title'],
//                 'titleLength': task['title']?.toString().length ?? 0,
//                 'titleBytes': utf8.encode(task['title']?.toString() ?? '').length,
//                 'hasArabicChars': _containsArabic(task['title']?.toString() ?? ''),
//                 'isQuestionMarks': (task['title']?.toString() ?? '').contains('?'),
//               });
//             }
//           }
//         }
        
//         return {
//           'success': true,
//           'totalTasks': data is Map ? (data['totalRecords'] ?? 0) : (data as List).length,
//           'taskSamples': taskSamples,
//           'dataType': data.runtimeType.toString(),
//         };
//       } else {
//         return {
//           'success': false,
//           'statusCode': response.statusCode,
//           'error': 'HTTP Error: ${response.statusCode}',
//         };
//       }
//     } catch (e) {
//       debugPrint('❌ فشل اختبار عينة المهام: $e');
//       return {
//         'success': false,
//         'error': e.toString(),
//       };
//     }
//   }

//   /// استخراج عينة من البيانات للعرض
//   static Map<String, dynamic> _extractSampleData(dynamic data) {
//     if (data is Map<String, dynamic>) {
//       if (data.containsKey('data') && data['data'] is List) {
//         final tasks = data['data'] as List;
//         if (tasks.isNotEmpty && tasks.first is Map<String, dynamic>) {
//           final firstTask = tasks.first as Map<String, dynamic>;
//           return {
//             'type': 'object_with_data_array',
//             'totalRecords': data['totalRecords'],
//             'firstTaskTitle': firstTask['title'],
//             'firstTaskId': firstTask['id'],
//           };
//         }
//       }
//       return {
//         'type': 'object',
//         'keys': data.keys.take(5).toList(),
//       };
//     } else if (data is List) {
//       if (data.isNotEmpty && data.first is Map<String, dynamic>) {
//         final firstTask = data.first as Map<String, dynamic>;
//         return {
//           'type': 'array',
//           'length': data.length,
//           'firstTaskTitle': firstTask['title'],
//           'firstTaskId': firstTask['id'],
//         };
//       }
//       return {
//         'type': 'array',
//         'length': data.length,
//       };
//     } else {
//       return {
//         'type': data.runtimeType.toString(),
//         'value': data.toString(),
//       };
//     }
//   }

//   /// فحص وجود أحرف عربية
//   static bool _containsArabic(String text) {
//     return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
//   }

//   /// طباعة تقرير التشخيص
//   static void printDiagnosticsReport(Map<String, dynamic> results) {
//     debugPrint('\n${'=' * 60}');
//     debugPrint('📊 تقرير تشخيص الترميز العربي');
//     debugPrint('=' * 60);
    
//     if (results.containsKey('error')) {
//       debugPrint('❌ خطأ عام: ${results['error']}');
//       return;
//     }
    
//     // تقرير اختبار API الخام
//     final rawApiTest = results['rawApiTest'] as Map<String, dynamic>?;
//     if (rawApiTest != null) {
//       debugPrint('\n🌐 اختبار API الخام:');
//       debugPrint('   الحالة: ${rawApiTest['success'] ? '✅ نجح' : '❌ فشل'}');
//       if (rawApiTest['success']) {
//         debugPrint('   رمز الحالة: ${rawApiTest['statusCode']}');
//         debugPrint('   نوع المحتوى: ${rawApiTest['contentType']}');
//         debugPrint('   طول المحتوى: ${rawApiTest['contentLength']}');
//       } else {
//         debugPrint('   الخطأ: ${rawApiTest['error']}');
//       }
//     }
    
//     // تقرير اختبار UTF-8
//     final utf8Test = results['utf8Test'] as Map<String, dynamic>?;
//     if (utf8Test != null) {
//       debugPrint('\n🔤 اختبار UTF-8:');
//       debugPrint('   الحالة: ${utf8Test['success'] ? '✅ نجح' : '❌ فشل'}');
//       if (utf8Test['success']) {
//         debugPrint('   متطابق: ${utf8Test['areEqual'] ? '✅ نعم' : '❌ لا'}');
//         debugPrint('   طول النص الخام: ${utf8Test['rawBodyLength']}');
//         debugPrint('   طول UTF-8: ${utf8Test['utf8DecodedLength']}');
//       } else {
//         debugPrint('   الخطأ: ${utf8Test['error']}');
//       }
//     }
    
//     // تقرير اختبار عينة المهام
//     final taskSampleTest = results['taskSampleTest'] as Map<String, dynamic>?;
//     if (taskSampleTest != null) {
//       debugPrint('\n📋 اختبار عينة المهام:');
//       debugPrint('   الحالة: ${taskSampleTest['success'] ? '✅ نجح' : '❌ فشل'}');
//       if (taskSampleTest['success']) {
//         debugPrint('   إجمالي المهام: ${taskSampleTest['totalTasks']}');
//         final samples = taskSampleTest['taskSamples'] as List;
//         for (var i = 0; i < samples.length; i++) {
//           final sample = samples[i] as Map<String, dynamic>;
//           debugPrint('   المهمة ${i + 1}:');
//           debugPrint('     المعرف: ${sample['id']}');
//           debugPrint('     العنوان: "${sample['title']}"');
//           debugPrint('     يحتوي على عربي: ${sample['hasArabicChars'] ? '✅ نعم' : '❌ لا'}');
//           debugPrint('     علامات استفهام: ${sample['isQuestionMarks'] ? '❌ نعم' : '✅ لا'}');
//           debugPrint('     طول العنوان: ${sample['titleLength']}');
//           debugPrint('     حجم البايتات: ${sample['titleBytes']}');
//         }
//       } else {
//         debugPrint('   الخطأ: ${taskSampleTest['error']}');
//       }
//     }
    
//     debugPrint('\n${'=' * 60}');
//     debugPrint('⏰ وقت التقرير: ${DateTime.now().toIso8601String()}');
//     debugPrint('${'=' * 60}\n');
//   }
// }
