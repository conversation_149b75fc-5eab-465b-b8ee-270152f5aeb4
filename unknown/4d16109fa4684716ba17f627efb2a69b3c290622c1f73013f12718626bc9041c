// import 'package:get/get.dart';

// import '../controllers/admin/enhanced_admin_controller.dart';
// import '../controllers/admin_controller.dart';
// import '../services/admin/unified_admin_api_service.dart';

// /// ربط التبعيات للوحة التحكم الإدارية المحسنة
// class EnhancedAdminBinding extends Bindings {
//   @override
//   void dependencies() {
//     // تسجيل خدمة API الموحدة
//     Get.lazyPut<UnifiedAdminApiService>(
//       () => UnifiedAdminApiService(),
//       fenix: true,
//     );

//     // تسجيل المتحكم التقليدي (للتوافق مع المكونات القديمة)
//     Get.lazyPut<AdminController>(
//       () => AdminController(),
//       fenix: true,
//     );

//     // تسجيل المتحكم المحسن
//     Get.lazyPut<EnhancedAdminController>(
//       () => EnhancedAdminController(),
//       fenix: true,
//     );
//   }
// }