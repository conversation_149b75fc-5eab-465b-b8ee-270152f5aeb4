import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/database_repair_service.dart';
import '../services/unified_permission_service.dart';


/// عرض مربع حوار لإصلاح قاعدة البيانات
class DatabaseRepairDialog {
  /// عرض مربع حوار لإصلاح قاعدة البيانات
  static Future<bool> showDatabaseRepairDialog(BuildContext context) async {
    final permissionService = Get.find<UnifiedPermissionService>();

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إصلاح قاعدة البيانات'),
          content: const Text(
              'هناك مشكلة في قاعدة البيانات. هل ترغب في محاولة إصلاحها؟'),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            if (permissionService.canAccessAdmin())
              TextButton(
                child: const Text('إصلاح'),
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
          ],
        );
      },
    );

    if (result == true) {
      return await _repairDatabase(context);
    }

    return false;
  }

  /// إصلاح قاعدة البيانات
  static Future<bool> _repairDatabase(BuildContext context) async {
    bool success = false;

    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري إصلاح قاعدة البيانات...'),
            ],
          ),
        );
      },
    );

    try {
      // إصلاح قاعدة البيانات
      final databaseRepair = DatabaseRepair();
      success = await databaseRepair.repairDatabase();

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      // عرض نتيجة الإصلاح
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(success ? 'تم الإصلاح بنجاح' : 'فشل الإصلاح'),
            content: Text(success
                ? 'تم إصلاح قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق.'
                : 'فشل في إصلاح قاعدة البيانات. يرجى الاتصال بالدعم الفني.'),
            actions: <Widget>[
              TextButton(
                child: const Text('موافق'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    } catch (e) {
      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      // عرض رسالة الخطأ
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ أثناء إصلاح قاعدة البيانات: $e'),
            actions: <Widget>[
              TextButton(
                child: const Text('موافق'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }

    return success;
  }

  /// عرض رسالة خطأ قاعدة البيانات مع خيار الإصلاح
  static Future<bool> showDatabaseErrorDialog(
      BuildContext context, String errorMessage) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('خطأ في قاعدة البيانات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('حدث خطأ في قاعدة البيانات:'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  errorMessage,
                  style: const TextStyle(
                    fontFamily: 'Courier',
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text('هل ترغب في محاولة إصلاح قاعدة البيانات؟'),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: const Text('إصلاح'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );

    if (result == true) {
      return await _repairDatabase(context);
    }

    return false;
  }
}
