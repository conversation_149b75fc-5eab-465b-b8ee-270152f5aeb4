import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart';

/// خدمة تصدير Excel محسنة
///
/// توفر وظائف متقدمة لتصدير التقارير بتنسيق Excel
class EnhancedExcelExportService extends GetxService {
  /// تصدير تقرير بتنسيق Excel
  ///
  /// [title] عنوان التقرير
  /// [data] بيانات التقرير (يمكن أن تكون Map أو List)
  /// [summary] ملخص التقرير (اختياري)
  /// [fileName] اسم الملف (اختياري)
  Future<String?> exportToExcel({
    required String title,
    required dynamic data,
    Map<String, dynamic>? summary,
    String? fileName,
  }) async {
    try {
      // إنشاء ملف Excel
      final excel = Excel.createExcel();

      // إنشاء ورقة المعلومات
      final infoSheet = excel['معلومات التقرير'];
      _appendExcelRow(infoSheet, ['عنوان التقرير', title]);
      _appendExcelRow(infoSheet, ['تاريخ التقرير', DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())]);

      // إضافة ملخص التقرير إذا كان متوفرًا
      if (summary != null && summary.isNotEmpty) {
        _appendExcelRow(infoSheet, []);
        _appendExcelRow(infoSheet, ['ملخص التقرير']);

        summary.forEach((key, value) {
          _appendExcelRow(infoSheet, [key, value.toString()]);
        });
      }

      // إنشاء ورقة البيانات
      final dataSheet = excel['بيانات التقرير'];

      // تحويل البيانات إلى Map إذا كانت List
      final Map<String, dynamic> processedData = data is Map<String, dynamic>
          ? data
          : {'items': data is List ? data : [data]};

      // إذا كانت البيانات تحتوي على أعمدة وصفوف، نستخدم تنسيق الجدول
      if (processedData.containsKey('columns') && processedData.containsKey('rows')) {
        // إضافة رأس الجدول
        _appendExcelRow(dataSheet, processedData['columns']);

        // إضافة صفوف البيانات
        for (final row in processedData['rows']) {
          _appendExcelRow(dataSheet, row);
        }
      } else if (processedData.containsKey('items') && processedData['items'] is List) {
        // معالجة البيانات كقائمة عناصر
        final items = processedData['items'] as List;

        if (items.isNotEmpty && items.first is Map) {
          // استخراج أسماء الأعمدة من المفاتيح في العنصر الأول
          final firstItem = items.first as Map;
          final columns = firstItem.keys.toList();
          _appendExcelRow(dataSheet, columns);

          // إضافة الصفوف
          for (final item in items) {
            if (item is Map) {
              final rowData = columns.map((col) => item[col]).toList();
              _appendExcelRow(dataSheet, rowData);
            }
          }
        } else {
          // قائمة بسيطة من العناصر
          _appendExcelRow(dataSheet, ['الرقم', 'القيمة']);
          for (int i = 0; i < items.length; i++) {
            _appendExcelRow(dataSheet, [i + 1, items[i].toString()]);
          }
        }
      } else {
        // إضافة البيانات كأزواج مفتاح-قيمة
        _appendExcelRow(dataSheet, ['المفتاح', 'القيمة']);

        processedData.forEach((key, value) {
          if (value is Map) {
            _appendExcelRow(dataSheet, [key, '']);
            value.forEach((subKey, subValue) {
              _appendExcelRow(dataSheet, ['  $subKey', subValue.toString()]);
            });
          } else if (value is List) {
            _appendExcelRow(dataSheet, [key, '']);
            for (int i = 0; i < value.length; i++) {
              _appendExcelRow(dataSheet, ['  العنصر ${i + 1}', value[i].toString()]);
            }
          } else {
            _appendExcelRow(dataSheet, [key, value.toString()]);
          }
        });
      }

      // تنسيق الأوراق
      _formatExcelSheet(infoSheet);
      _formatExcelSheet(dataSheet);

      // حفظ الملف
      final directory = await _getExportDirectory();
      final outputFileName = fileName ?? 'report_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx';
      final file = File('${directory.path}/$outputFileName');
      await file.writeAsBytes(excel.encode()!);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق Excel: $e');
      return null;
    }
  }

  /// إضافة صف إلى ورقة Excel
  void _appendExcelRow(Sheet sheet, List<dynamic> rowData) {
    final rowIndex = sheet.maxRows;

    for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex));
      cell.value = TextCellValue(rowData[colIndex].toString());
    }
  }

  /// تنسيق ورقة Excel
  void _formatExcelSheet(Sheet sheet) {
    // الحصول على عدد الأعمدة المستخدمة
    int colCount = 0;
    for (int i = 0; i < sheet.maxRows; i++) {
      int rowColCount = 0;
      for (int j = 0; j < 100; j++) { // نفترض أن أقصى عدد للأعمدة هو 100
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i));
        if (cell.value != null) {
          rowColCount = j + 1;
        }
      }
      colCount = rowColCount > colCount ? rowColCount : colCount;
    }

    // تعيين عرض الأعمدة
    for (int i = 0; i < colCount; i++) {
      sheet.setColumnWidth(i, 20);
    }

    // تنسيق الخلايا
    for (int rowIndex = 0; rowIndex < sheet.maxRows; rowIndex++) {
      for (int colIndex = 0; colIndex < colCount; colIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex));

        // تنسيق رأس الجدول
        if (rowIndex == 0) {
          cell.cellStyle = CellStyle(
            bold: true,
            horizontalAlign: HorizontalAlign.Center,
          );
          // تعيين لون الخلفية للخلية الأولى (لا يمكن تعيين لون الخلفية مباشرة في CellStyle)
          // ملاحظة: مكتبة Excel لا تدعم تعيين لون الخلفية بشكل مباشر في الإصدار الحالي
        } else {
          cell.cellStyle = CellStyle(
            horizontalAlign: colIndex == 0 ? HorizontalAlign.Right : HorizontalAlign.Left,
          );
        }
      }
    }
  }

  /// الحصول على مسار دليل التصدير
  Future<Directory> _getExportDirectory() async {
    Directory directory;

    try {
      // محاولة الحصول على دليل المستندات
      directory = await getApplicationDocumentsDirectory();

      // إنشاء مجلد التصدير إذا لم يكن موجودًا
      final exportDir = Directory('${directory.path}/exports');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      return exportDir;
    } catch (e) {
      debugPrint('خطأ في الحصول على دليل التصدير: $e');

      // استخدام دليل مؤقت كبديل
      directory = await getTemporaryDirectory();
      return directory;
    }
  }

  /// تصدير تقرير مساهمات المستخدمين
  Future<String?> exportUserContributionsReport({
    required String title,
    required Map<String, double> userContributions,
    required Map<String, Map<String, int>> contributionsByType,
    Map<String, String>? userNames,
    String? fileName,
  }) async {
    try {
      // تحضير البيانات
      final Map<String, dynamic> data = {};

      // إعداد الأعمدة والصفوف
      final List<String> columns = ['المستخدم', 'نسبة المساهمة'];
      final List<List<dynamic>> rows = [];

      // إضافة بيانات المساهمات
      userContributions.forEach((userId, contribution) {
        final userName = userNames?[userId] ?? userId;
        rows.add([userName, contribution]);
      });

      data['columns'] = columns;
      data['rows'] = rows;

      // إعداد ملخص التقرير
      final Map<String, dynamic> summary = {
        'عدد المستخدمين': userContributions.length,
        'متوسط المساهمة': userContributions.isEmpty ? 0 : userContributions.values.reduce((a, b) => a + b) / userContributions.length,
      };

      // تصدير التقرير
      return await exportToExcel(
        title: title,
        data: data,
        summary: summary,
        fileName: fileName ?? 'user_contributions_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx',
      );
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير مساهمات المستخدمين: $e');
      return null;
    }
  }

  /// تصدير تقرير مساهمات حسب النوع
  Future<String?> exportContributionsByTypeReport({
    required String title,
    required Map<String, Map<String, int>> contributionsByType,
    Map<String, String>? userNames,
    String? fileName,
  }) async {
    try {
      // تحضير البيانات
      final Map<String, dynamic> data = {};

      // إعداد الأعمدة
      final List<String> columns = ['المستخدم'];
      final Set<String> allTypes = {};

      // جمع جميع أنواع المساهمات
      contributionsByType.forEach((userId, types) {
        allTypes.addAll(types.keys);
      });

      // إضافة أنواع المساهمات إلى الأعمدة
      columns.addAll(allTypes);
      columns.add('المجموع');

      // إعداد الصفوف
      final List<List<dynamic>> rows = [];

      // إضافة بيانات المساهمات
      contributionsByType.forEach((userId, types) {
        final userName = userNames?[userId] ?? userId;
        final List<dynamic> row = [userName];

        int total = 0;
        for (final type in allTypes) {
          final count = types[type] ?? 0;
          row.add(count);
          total += count;
        }

        row.add(total);
        rows.add(row);
      });

      data['columns'] = columns;
      data['rows'] = rows;

      // تصدير التقرير
      return await exportToExcel(
        title: title,
        data: data,
        fileName: fileName ?? 'contributions_by_type_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx',
      );
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير المساهمات حسب النوع: $e');
      return null;
    }
  }
}
