import 'package:flutter_application_2/models/task_model.dart';

class OverdueTasksReport {
  final List<Task> overdueTasks;
  final double averageDelayDays;

  OverdueTasksReport({
    required this.overdueTasks,
    required this.averageDelayDays,
  });

  factory OverdueTasksReport.fromTasks(List<Task> tasks) {
    final overdueTasks = tasks.where((task) {
      if (task.dueDateDateTime == null) return false;
      return task.dueDateDateTime!.isBefore(DateTime.now()) && task.status != 'completed';
    }).toList();

    double totalDelayDays = 0;
    for (var task in overdueTasks) {
      if (task.dueDateDateTime != null) {
        totalDelayDays += DateTime.now().difference(task.dueDateDateTime!).inDays;
      }
    }

    final averageDelayDays = overdueTasks.isNotEmpty
        ? totalDelayDays / overdueTasks.length
        : 0.0;

    return OverdueTasksReport(
      overdueTasks: overdueTasks,
      averageDelayDays: averageDelayDays,
    );
  }
}