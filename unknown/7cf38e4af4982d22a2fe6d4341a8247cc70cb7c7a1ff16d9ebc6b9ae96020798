import 'user_model.dart';

/// نموذج لوحة التحكم - متطابق مع ASP.NET Core API
class Dashboard {
  final int id;
  final String title; // title في API
  final String? description;
  final int ownerId; // ownerId في API
  final int createdAt; // long في API
  final int? updatedAt; // long في API
  final bool isDefault;
  final bool isShared; // isShared في API
  final int gridRows;
  final int gridColumns;
  final String? settings; // JSON string
  final String? color;
  final String? icon;
  final bool isDeleted;

  // Navigation properties
  final User? owner;
  final List<DashboardWidget>? dashboardWidgets;

  const Dashboard({
    required this.id,
    required this.title,
    this.description,
    required this.ownerId,
    required this.createdAt,
    this.updatedAt,
    this.isDefault = false,
    this.isShared = false,
    this.gridRows = 12,
    this.gridColumns = 12,
    this.settings,
    this.color,
    this.icon,
    this.isDeleted = false,
    this.owner,
    this.dashboardWidgets,
  });

  factory Dashboard.fromJson(Map<String, dynamic> json) {
    return Dashboard(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      ownerId: json['ownerId'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDefault: json['isDefault'] as bool? ?? false,
      isShared: json['isShared'] as bool? ?? false,
      gridRows: json['gridRows'] as int? ?? 12,
      gridColumns: json['gridColumns'] as int? ?? 12,
      settings: json['settings'] as String?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      owner: json['owner'] != null
          ? User.fromJson(json['owner'] as Map<String, dynamic>)
          : null,
      dashboardWidgets: json['dashboardWidgets'] != null
          ? (json['dashboardWidgets'] as List)
              .map((w) => DashboardWidget.fromJson(w as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'ownerId': ownerId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDefault': isDefault,
      'isShared': isShared,
      'gridRows': gridRows,
      'gridColumns': gridColumns,
      'settings': settings,
      'color': color,
      'icon': icon,
      'isDeleted': isDeleted,
    };
  }

  Dashboard copyWith({
    int? id,
    String? title,
    String? description,
    int? ownerId,
    int? createdAt,
    int? updatedAt,
    bool? isDefault,
    bool? isShared,
    int? gridRows,
    int? gridColumns,
    String? settings,
    String? color,
    String? icon,
    bool? isDeleted,
    User? owner,
    List<DashboardWidget>? dashboardWidgets,
  }) {
    return Dashboard(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
      isShared: isShared ?? this.isShared,
      gridRows: gridRows ?? this.gridRows,
      gridColumns: gridColumns ?? this.gridColumns,
      settings: settings ?? this.settings,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isDeleted: isDeleted ?? this.isDeleted,
      owner: owner ?? this.owner,
      dashboardWidgets: dashboardWidgets ?? this.dashboardWidgets,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على عدد الويدجت
  int get widgetCount => dashboardWidgets?.length ?? 0;

  /// التحقق من كون اللوحة مشتركة
  bool get isPublic => isShared;

  /// الحصول على اسم المالك
  String get ownerName => owner?.name ?? 'غير محدد';

  @override
  String toString() {
    return 'Dashboard(id: $id, title: $title, widgetCount: $widgetCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dashboard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج ويدجت لوحة التحكم - متطابق مع ASP.NET Core API
class DashboardWidget {
  final int id;
  final int dashboardId;
  final String widgetType; // widgetType في API
  final String title;
  final String dataSource;
  final String? query;
  final int positionX;
  final int positionY;
  final int width;
  final int height;
  final String? settings; // JSON string
  final int createdAt; // long في API
  final int? updatedAt; // long في API
  final bool isDeleted;
  final String? configuration; // JSON string
  final String? position; // JSON string
  final String? size; // JSON string
  final int? orderIndex;
  final bool isVisible;

  // Navigation properties
  final Dashboard? dashboard;

  const DashboardWidget({
    required this.id,
    required this.dashboardId,
    required this.widgetType,
    required this.title,
    required this.dataSource,
    this.query,
    required this.positionX,
    required this.positionY,
    required this.width,
    required this.height,
    this.settings,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.configuration,
    this.position,
    this.size,
    this.orderIndex,
    this.isVisible = true,
    this.dashboard,
  });

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
      id: json['id'] as int,
      dashboardId: json['dashboardId'] as int,
      widgetType: json['widgetType'] as String,
      title: json['title'] as String,
      dataSource: json['dataSource'] as String,
      query: json['query'] as String?,
      positionX: json['positionX'] as int,
      positionY: json['positionY'] as int,
      width: json['width'] as int,
      height: json['height'] as int,
      settings: json['settings'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      configuration: json['configuration'] as String?,
      position: json['position'] as String?,
      size: json['size'] as String?,
      orderIndex: json['orderIndex'] as int?,
      isVisible: json['isVisible'] as bool? ?? true,
      dashboard: json['dashboard'] != null 
          ? Dashboard.fromJson(json['dashboard'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'widgetType': widgetType,
      'title': title,
      'dataSource': dataSource,
      'query': query,
      'positionX': positionX,
      'positionY': positionY,
      'width': width,
      'height': height,
      'settings': settings,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'configuration': configuration,
      'position': position,
      'size': size,
      'orderIndex': orderIndex,
      'isVisible': isVisible,
    };
  }

  DashboardWidget copyWith({
    int? id,
    int? dashboardId,
    String? widgetType,
    String? title,
    String? dataSource,
    String? query,
    int? positionX,
    int? positionY,
    int? width,
    int? height,
    String? settings,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    String? configuration,
    String? position,
    String? size,
    int? orderIndex,
    bool? isVisible,
    Dashboard? dashboard,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      dashboardId: dashboardId ?? this.dashboardId,
      widgetType: widgetType ?? this.widgetType,
      title: title ?? this.title,
      dataSource: dataSource ?? this.dataSource,
      query: query ?? this.query,
      positionX: positionX ?? this.positionX,
      positionY: positionY ?? this.positionY,
      width: width ?? this.width,
      height: height ?? this.height,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      configuration: configuration ?? this.configuration,
      position: position ?? this.position,
      size: size ?? this.size,
      orderIndex: orderIndex ?? this.orderIndex,
      isVisible: isVisible ?? this.isVisible,
      dashboard: dashboard ?? this.dashboard,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'DashboardWidget(id: $id, type: $widgetType, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardWidget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
