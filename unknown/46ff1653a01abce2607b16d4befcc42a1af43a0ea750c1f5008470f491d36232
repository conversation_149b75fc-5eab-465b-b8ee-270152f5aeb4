import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

import '../../models/department_model.dart';
import '../../models/task_models.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';
import '../../services/api/departments_api_service.dart';
import '../../services/api/user_api_service.dart';

import '../../services/unified_permission_service.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/auth_controller.dart';

import 'department_users_management_screen.dart';

/// فئة بيانات للرسم البياني الشريطي
class TaskStatusData {
  final String status;
  final int count;
  final Color color;

  TaskStatusData(this.status, this.count, this.color);
}

/// شاشة تفاصيل القسم
/// تعرض تفاصيل القسم وأعضاءه والمهام المرتبطة به
class DepartmentDetailScreen extends StatefulWidget {
  final int departmentId;
  final Department? department; // إضافة كائن القسم اختياري

  const DepartmentDetailScreen({
    super.key,
    required this.departmentId,
    this.department,
  });

  @override
  State<DepartmentDetailScreen> createState() => _DepartmentDetailScreenState();
}

class _DepartmentDetailScreenState extends State<DepartmentDetailScreen> with SingleTickerProviderStateMixin {
  final _departmentApiService = DepartmentsApiService();
  final _userApiService = UserApiService();
  final _permissionService = Get.find<UnifiedPermissionService>();

  late TabController _tabController;
  Department? _department;
  User? _manager;
  List<User> _members = [];
  List<Task> _departmentTasks = [];
  List<Task> _filteredTasks = [];
  Map<String, int> _taskStatusCounts = {};
  Map<int, double> _userProductivity = {};
  bool _isLoading = true;
  bool _isLoadingTasks = false;
  String? _errorMessage;

  // متغير لتخزين إحصائيات القسم من API
  Map<String, dynamic>? _departmentStats;
  bool _isLoadingStats = false;

  // متغيرات الفلترة
  String? _filterStatus;
  int? _filterAssigneeId;
  double _filterMinCompletion = 0.0;
  double _filterMaxCompletion = 100.0;
  bool _isFiltered = false;

  // تنسيق التاريخ
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    Get.put(_tabController, tag: 'department_detail_tab_controller');

    // إذا تم تمرير كائن القسم استخدمه مباشرة
    if (widget.department != null) {
      _department = widget.department;
      // إذا كان فيه مدير استخدمه مباشرة
      if (_department!.manager != null) {
        _manager = _department!.manager;
      }
    }
    // تحميل بيانات القسم من API (لتحديث البيانات)
    _loadDepartmentDetails();
    _loadDepartmentStats();
    _tabController.addListener(() {
      if (_tabController.index == 1 && _departmentTasks.isEmpty && _department != null && _department!.id != 0) {
        _loadDepartmentTasks();
      }
    });
  }

  /// تحميل إحصائيات القسم من API
  Future<void> _loadDepartmentStats() async {
    if (widget.departmentId == 0) return;

    setState(() {
      _isLoadingStats = true;
    });

    try {
      final stats = await _departmentApiService.getDepartmentStatistics(widget.departmentId);
      setState(() {
        _departmentStats = stats;
      });
    } catch (e) {
      setState(() {
        _departmentStats = null;
      });
    } finally {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }

  /// تحميل مهام القسم
  Future<void> _loadDepartmentTasks() async {
    if (_department == null) return;

    setState(() {
      _isLoadingTasks = true;
    });

    try {
      // استخدام النظام الموحد بدلاً من API مباشرة
      final taskController = Get.find<TaskController>();
      final permissionService = Get.find<UnifiedPermissionService>();

      // فحص الصلاحيات أولاً - استخدام الصلاحيات الموجودة
      if (!permissionService.canAccessTasks() &&
          !permissionService.canAdminDepartments()) {
        debugPrint('❌ المستخدم لا يملك صلاحية عرض مهام الأقسام');
        _departmentTasks = [];
        return;
      }

      // تحميل المهام باستخدام النظام الموحد حسب الصلاحيات
      final authController = Get.find<AuthController>();
      if (authController.currentUser.value != null) {
        await taskController.loadTasksByUserPermissions(
          authController.currentUser.value!.id,
          forceRefresh: true
        );
      }

      // فلترة المهام لتظهر فقط مهام هذا القسم
      _departmentTasks = taskController.allTasks
          .where((task) => task.departmentId == _department!.id)
          .toList();

      // تطبيق الفلترة إذا كانت مفعلة
      _applyFilters();

      // حساب عدد المهام حسب الحالة
      _taskStatusCounts = {};
      for (var task in _departmentTasks) {
        final status = task.status.toString();
        _taskStatusCounts[status] = (_taskStatusCounts[status] ?? 0) + 1;
      }

      // حساب إنتاجية المستخدمين
      _userProductivity = {};
      for (var member in _members) {
        // حساب عدد المهام المكتملة لكل مستخدم
        final userTasks = _departmentTasks.where((task) => task.assigneeId == member.id).toList();
        final completedTasks = userTasks.where((task) => task.status == 'completed').length;

        if (userTasks.isNotEmpty) {
          _userProductivity[member.id] = (completedTasks / userTasks.length) * 100;
        } else {
          _userProductivity[member.id] = 0;
        }
      }
    } catch (e) {
      debugPrint('Error loading department tasks: $e');
    } finally {
      setState(() {
        _isLoadingTasks = false;
      });
    }
  }

  /// تطبيق الفلترة على المهام
  void _applyFilters() {
    if (!_isFiltered) {
      _filteredTasks = List.from(_departmentTasks);
      return;
    }

    _filteredTasks = _departmentTasks.where((task) {
      // فلترة حسب الحالة
      if (_filterStatus != null && task.status != _filterStatus) {
        return false;
      }

      // فلترة حسب المستخدم المسند إليه
      if (_filterAssigneeId != null && task.assigneeId != _filterAssigneeId) {
        return false;
      }

      // فلترة حسب نسبة الإكمال
      if (task.completionPercentage < _filterMinCompletion ||
          task.completionPercentage > _filterMaxCompletion) {
        return false;
      }

      return true;
    }).toList();
  }

  /// عرض مربع حوار الفلترة
  void _showTaskFilterDialog() {
    // نسخ قيم الفلترة الحالية لاستخدامها في الحوار
    String? tempFilterStatus = _filterStatus;
    int? tempFilterAssigneeId = _filterAssigneeId;
    double tempMinCompletion = _filterMinCompletion;
    double tempMaxCompletion = _filterMaxCompletion;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة المهام'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلترة حسب الحالة
                    const Text('حالة المهمة:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    SegmentedButton<String?>(
                      segments: [
                        ButtonSegment<String?>(
                          value: null,
                          label: const Text('الكل'),
                        ),
                        ButtonSegment<String?>(
                          value: 'pending',
                          label: const Text('قيد الانتظار'),
                        ),
                        ButtonSegment<String?>(
                          value: 'in_progress',
                          label: const Text('قيد التنفيذ'),
                        ),
                        ButtonSegment<String?>(
                          value: 'completed',
                          label: const Text('مكتملة'),
                        ),
                        ButtonSegment<String?>(
                          value: 'cancelled',
                          label: const Text('ملغاة'),
                        ),
                        ButtonSegment<String?>(
                          value: 'waiting_for_info',
                          label: const Text('في انتظار معلومات'),
                        ),
                      ],
                      selected: <String?>{tempFilterStatus},
                      onSelectionChanged: (Set<String?> newSelection) {
                        setState(() {
                          tempFilterStatus = newSelection.isNotEmpty ? newSelection.first : null;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلترة حسب المستخدم المسند إليه
                    const Text('المسند إليه:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    SegmentedButton<int?>(
                      segments: [
                        const ButtonSegment<int?>(
                          value: null,
                          label: Text('الكل'),
                        ),
                        ..._members.map((member) {
                          return ButtonSegment<int?>(
                            value: member.id,
                            label: Text(member.name),
                          );
                        }),
                      ],
                      selected: <int?>{tempFilterAssigneeId},
                      onSelectionChanged: (Set<int?> newSelection) {
                        setState(() {
                          tempFilterAssigneeId = newSelection.isNotEmpty ? newSelection.first : null;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلترة حسب نسبة الإكمال
                    const Text('نسبة الإكمال:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text('${tempMinCompletion.toInt()}%'),
                        Expanded(
                          child: RangeSlider(
                            values: RangeValues(tempMinCompletion, tempMaxCompletion),
                            min: 0,
                            max: 100,
                            divisions: 10,
                            labels: RangeLabels(
                              '${tempMinCompletion.toInt()}%',
                              '${tempMaxCompletion.toInt()}%',
                            ),
                            onChanged: (values) {
                              setState(() {
                                tempMinCompletion = values.start;
                                tempMaxCompletion = values.end;
                              });
                            },
                          ),
                        ),
                        Text('${tempMaxCompletion.toInt()}%'),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // إعادة تعيين الفلترة
                    Navigator.of(context).pop();

                    this.setState(() {
                      _filterStatus = null;
                      _filterAssigneeId = null;
                      _filterMinCompletion = 0.0;
                      _filterMaxCompletion = 100.0;
                      _isFiltered = false;
                      _applyFilters();
                    });
                  },
                  child: const Text('إعادة تعيين'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();

                    this.setState(() {
                      _filterStatus = tempFilterStatus;
                      _filterAssigneeId = tempFilterAssigneeId;
                      _filterMinCompletion = tempMinCompletion;
                      _filterMaxCompletion = tempMaxCompletion;
                      _isFiltered = true;
                      _applyFilters();
                    });
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    // إزالة متحكم التبويبات من GetX
    if (Get.isRegistered<TabController>(tag: 'department_detail_tab_controller')) {
      Get.delete<TabController>(tag: 'department_detail_tab_controller');
    }

    _tabController.dispose();
    super.dispose();
  }

  /// تحميل تفاصيل القسم
  Future<void> _loadDepartmentDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      // تحميل بيانات القسم من API (سيتم تحديث البيانات حتى لو كانت متوفرة مسبقاً)
      final departmentFromApi = await _departmentApiService.getDepartmentById(widget.departmentId);
      
      if (departmentFromApi == null) {
        _errorMessage = 'القسم غير موجود';
        return;
      }
      
      _department = departmentFromApi;
      debugPrint('بيانات القسم من API: ${_department!.toJson()}');
      
      // تحميل مدير القسم
      if (_department!.manager != null) {
        _manager = _department!.manager;
        debugPrint('مدير القسم من كائن القسم: ${_manager!.name}');
      } else if (_department!.managerId != null) {
        _manager = await _userApiService.getUserById(_department!.managerId!);
        debugPrint('مدير القسم من API المستخدمين: ${_manager?.name ?? "لم يتم العثور عليه"}');
      } else {
        _manager = null;
        debugPrint('لا يوجد مدير للقسم');
      }
      
      // تحميل أعضاء القسم
      await _loadDepartmentMembers();
      
      // تحميل مهام القسم إذا كان التبويب الحالي هو تبويب المهام
      if (_tabController.index == 1 && _department != null && _department!.id != 0) {
        await _loadDepartmentTasks();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل القسم: $e');
      _errorMessage = 'حدث خطأ أثناء تحميل تفاصيل القسم: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل أعضاء القسم
  Future<void> _loadDepartmentMembers() async {
    if (_department == null || _department!.id == 0) {
      _members = [];
      return;
    }

    try {
      debugPrint('تحميل أعضاء القسم ${_department!.id}...');
      final departmentUsersResponse = await _departmentApiService.getDepartmentEmployees(_department!.id);
      debugPrint('استجابة أعضاء القسم: $departmentUsersResponse');
      
      if (departmentUsersResponse.isNotEmpty) {
        _members = departmentUsersResponse.map((json) {
          if (json is Map<String, dynamic>) {
            return User.fromJson(json);
          } else {
            debugPrint('تحذير: عنصر غير متوقع في قائمة الأعضاء: $json');
            return null;
          }
        }).where((user) => user != null).cast<User>().toList();
        
        debugPrint('تم تحميل ${_members.length} عضو للقسم');
        
        // التحقق من صحة بيانات الأعضاء
        for (final user in _members) {
          if (user.name.isEmpty) {
            debugPrint('⚠️ عضو بدون اسم: ${user.toJson()}');
          }
        }
      } else {
        _members = [];
        debugPrint('لا يوجد أعضاء في هذا القسم');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الأعضاء من API: $e');
      // محاولة بديلة: تحميل جميع المستخدمين وفلترتهم
      try {
        final allUsers = await _userApiService.getAllUsers();
        _members = allUsers.where((user) => 
          user.departmentId == _department!.id && 
          user.isActive && 
          !user.isDeleted
        ).toList();
        debugPrint('تم تحميل ${_members.length} عضو من المحاولة البديلة');
      } catch (e2) {
        debugPrint('فشل في المحاولة البديلة لتحميل الأعضاء: $e2');
        _members = [];
      }
    }
  }

  /// تعيين مدير للقسم
  Future<void> _assignManager(User user) async {
    if (_department == null) return;

    // 🔒 التحقق من صلاحية تعيين مدير القسم
    if (!_permissionService.canAssignDepartmentManager()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لتعيين مدير للقسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    try {
      // تحديث القسم بمدير جديد
      final updatedDepartment = _department!.copyWith(managerId: user.id);
      await _departmentApiService.updateDepartment(updatedDepartment.id, updatedDepartment);

      Get.snackbar(
        'تم بنجاح',
        'تم تعيين ${user.name} كمدير للقسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      _loadDepartmentDetails();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تعيين المدير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إزالة مدير القسم
  Future<void> _removeManager() async {
    if (_department == null || _manager == null) return;

    // 🔒 التحقق من صلاحية إزالة مدير القسم
    if (!_permissionService.canAssignDepartmentManager()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لإزالة مدير القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // التأكيد قبل الإزالة
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد إزالة المدير'),
        content: Text('هل أنت متأكد من رغبتك في إزالة ${_manager!.name} من منصب مدير القسم؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final managerName = _manager!.name;
      debugPrint('🔄 بدء عملية إزالة المدير: $managerName من القسم ${_department!.name}');

      // تحديث القسم بإزالة المدير باستخدام المعامل الجديد
      final updatedDepartment = _department!.copyWith(clearManagerId: true);
      debugPrint('📝 القسم المحدث - managerId: ${updatedDepartment.managerId}');

      await _departmentApiService.updateDepartment(updatedDepartment.id, updatedDepartment);
      debugPrint('✅ تم إرسال طلب إزالة المدير بنجاح');

      Get.snackbar(
        'تم بنجاح',
        'تم إزالة $managerName من منصب مدير القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      _loadDepartmentDetails();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إزالة المدير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إضافة مستخدم للقسم
  Future<void> _addUserToDepartment() async {
    if (_department == null) return;

    // 🔒 التحقق من صلاحية إضافة مستخدمين للقسم
    if (!_permissionService.canAddUsersToDepartment()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لإضافة مستخدمين للقسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تحميل جميع المستخدمين
      final allUsers = await _userApiService.getAllUsers();

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض المستخدمين الذين ليس لديهم قسم فقط
      final availableUsers = allUsers.where((user) =>
        user.departmentId == null && user.isActive && !user.isDeleted).toList();

      if (availableUsers.isEmpty) {
        Get.snackbar(
          'تنبيه',
          'لا يوجد مستخدمين بدون قسم متاحين للإضافة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.amber.shade100,
          colorText: Colors.amber.shade800,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // عرض قائمة المستخدمين المتاحين
      final selectedUser = await Get.dialog<User>(
        AlertDialog(
          title: const Text('إضافة مستخدم للقسم'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'اختر المستخدم المراد إضافته للقسم (المستخدمين بدون قسم فقط)',
                          style: TextStyle(color: Colors.blue),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: availableUsers.length,
                    itemBuilder: (context, index) {
                      final user = availableUsers[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppColors.primary,
                          child: Text(
                            user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(user.name),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(user.email ?? 'غير محدد'),
                            Text(
                              'غير مُعيَّن لأي قسم',
                              style: TextStyle(
                                color: Colors.green[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        onTap: () => Get.back(result: user),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (selectedUser == null) return;

      // استخدام API المخصص لتعيين المستخدم للقسم
      try {
        final result = await _departmentApiService.assignUsersToDepartment(
          widget.departmentId,
          [selectedUser.id],
        );

        if (result['success'] == true) {
          Get.snackbar(
            'تم بنجاح',
            'تم إضافة ${selectedUser.name} للقسم',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );
          _loadDepartmentDetails();
        } else {
          Get.snackbar(
            'خطأ',
            'فشل إضافة المستخدم للقسم',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء إضافة المستخدم للقسم: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المستخدمين: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إزالة مستخدم من القسم
  Future<void> _removeUserFromDepartment(User user) async {
    if (_department == null) return;

    // 🔒 التحقق من صلاحية إزالة مستخدمين من القسم
    if (!_permissionService.canRemoveUsersFromDepartment()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لإزالة مستخدمين من القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // التأكيد قبل الإزالة
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الإزالة'),
        content: Text('هل أنت متأكد من رغبتك في إزالة ${user.name} من القسم؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // تحديث المستخدم بإزالة معرف القسم
      final updatedUser = user.copyWith(departmentId: null);
      await _userApiService.updateUser(user.id, updatedUser);

      // إذا وصلنا هنا فالعملية نجحت (لأن updateUser ترمي exception في حالة الفشل)
      Get.snackbar(
        'تم بنجاح',
        'تم إزالة ${user.name} من القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      _loadDepartmentDetails();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إزالة المستخدم من القسم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تعديل بيانات القسم
  Future<void> _editDepartment() async {
    if (_department == null) return;

    // 🔒 التحقق من صلاحية تعديل بيانات القسم
    if (!_permissionService.canManageDepartments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لتعديل بيانات القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    final nameController = TextEditingController(text: _department!.name);
    final descriptionController = TextEditingController(text: _department!.description ?? '');
    bool isActive = _department!.isActive;

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تعديل بيانات القسم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'اسم القسم',
                  prefixIcon: const Icon(Icons.business),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'وصف القسم',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              StatefulBuilder(
                builder: (context, setState) {
                  return SwitchListTile(
                    title: const Text('نشط'),
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال اسم القسم',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != true) return;

    try {
      final updatedDepartment = _department!.copyWith(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        isActive: isActive,
      );

      await _departmentApiService.updateDepartment(updatedDepartment.id, updatedDepartment);

      Get.snackbar(
        'تم بنجاح',
        'تم تحديث بيانات القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      _loadDepartmentDetails();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث بيانات القسم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('build: isLoading=$_isLoading, errorMessage=$_errorMessage, department=${_department?.name}, id=${_department?.id}');
    return Scaffold(
      appBar: AppBar(
        title: Text(_department?.name ?? 'تفاصيل القسم'),
        actions: [
          // زر التحديث
          if (_permissionService.canRefreshAdminData())
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'تحديث البيانات',
              onPressed: _isLoading ? null : () {
                _loadDepartmentDetails();
                _loadDepartmentStats();
              },
            ),
          if (_department != null &&
              _permissionService.canManageDepartmentUsers() &&
              _permissionService.canAccessDepartment(_department!.id))
            IconButton(
              icon: const Icon(Icons.people_alt),
              tooltip: 'إدارة المستخدمين المتقدمة',
              onPressed: () async {
                // الانتقال إلى شاشة إدارة المستخدمين وانتظار العودة
                await Get.to(() => const DepartmentUsersManagementScreen());
                // تحديث البيانات عند العودة
                _loadDepartmentDetails();
              },
            ),
          if (_department != null &&
              _permissionService.canManageDepartments() &&
              _permissionService.canAccessDepartment(_department!.id))
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'تعديل القسم',
              onPressed: _editDepartment,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _department == null
                  ? const Center(child: Text('القسم غير موجود'))
                  : _buildDepartmentDetails(),
    );
  }

  /// بناء واجهة عرض الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'حدث خطأ غير معروف',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadDepartmentDetails,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة عرض تفاصيل القسم
  Widget _buildDepartmentDetails() {
    debugPrint('_buildDepartmentDetails: department={_department?.name}, id=${_department?.id}, children=${_department?.children.length}');
    return Scaffold(
      body: Column(
        children: [
          // الجزء العلوي - معلومات القسم (مساحة أكبر)
          Flexible(
            flex: 2,
            child: SingleChildScrollView(
              child: Column(
                children: [
                 
                  // تفاصيل القسم الكاملة
                  _buildDepartmentInfo(),
                ],
              ),
            ),
          ),

          // تبويبات تأخذ المساحة الرئيسية
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'الأعضاء'),
              Tab(text: 'المهام'),
              Tab(text: 'الإحصائيات'),
            ],
          ),

          // محتوى التبويبات - يأخذ المساحة المتبقية
          Expanded(
            flex: 3,
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب الأعضاء
                _buildMembersTab(),

              // تبويب المهام
              _buildTasksTab(),

              // تبويب الإحصائيات
              _buildStatsTab(),
            ],
          ),
        ),
      ],
      ),

      // Bottom Navigation Bar للأقسام الفرعية
      bottomNavigationBar: _department != null && _department!.children.isNotEmpty
          ? _buildSubDepartmentsBottomBar()
          : null,
    );
  }

 

  /// بناء Bottom Navigation Bar للأقسام الفرعية
  Widget _buildSubDepartmentsBottomBar() {
    if (_department == null || _department!.children.isEmpty) {
      return const SizedBox.shrink();
    }

    // إذا كان هناك أقسام فرعية كثيرة، نعرض أول 4 فقط مع زر "المزيد"
    final maxItems = 4;
    final children = _department!.children;
    final showMoreButton = children.length > maxItems;
    final displayedChildren = showMoreButton
        ? children.take(maxItems - 1).toList()
        : children;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // عنوان الأقسام الفرعية
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                 
                  children: [
                    const Icon(Icons.account_tree, size: 18, color: Color.fromARGB(255, 24, 11, 11)),
                    Text(
                      'الأقسام الفرعية',
                      style: TextStyle(
                        fontSize: 12,
                        color: const Color.fromARGB(255, 86, 18, 125),
                      ),
                    ),
                  ],
                ),
              ),

              // خط فاصل
              Container(
                width: 1,
                height: 30,
                color: Colors.grey.shade300,
                margin: const EdgeInsets.symmetric(horizontal: 8),
              ),

              // قائمة الأقسام الفرعية - توزيع تلقائي على كامل العرض
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // عرض الأقسام الفرعية
                    ...displayedChildren.map((child) => Expanded(
                      child: _buildSubDepartmentItem(child),
                    )),

                    // زر "المزيد" إذا كان هناك أقسام إضافية
                    if (showMoreButton)
                      Expanded(
                        child: _buildMoreButton(),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر قسم فرعي في Bottom Bar
  Widget _buildSubDepartmentItem(Department child) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (_) => DepartmentDetailScreen(departmentId: child.id, department: child),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.business,
                size: 16,
                color: child.isActive ? Colors.blue : Colors.grey,
              ),
              const SizedBox(height: 2),
              Text(
                child.name,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر "المزيد" في Bottom Bar
  Widget _buildMoreButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
      child: InkWell(
        onTap: _showSubDepartmentsBottomSheet,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.more_horiz,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(height: 2),
              Text(
                '+${_department!.children.length - 3}',
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض الأقسام الفرعية في Bottom Sheet
  void _showSubDepartmentsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle للسحب
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Icon(Icons.account_tree, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    'الأقسام الفرعية (${_department!.children.length})',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // قائمة الأقسام الفرعية
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _department!.children.length,
                itemBuilder: (context, index) {
                  final child = _department!.children[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: const Icon(Icons.account_tree, color: Colors.blue),
                      title: Text(child.name.isNotEmpty ? child.name : 'اسم القسم غير متوفر'),
                      subtitle: child.description != null && child.description!.isNotEmpty
                          ? Text(child.description!)
                          : const Text('لا يوجد وصف'),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => DepartmentDetailScreen(departmentId: child.id, department: child),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// بناء معلومات القسم (الدالة القديمة - للاستخدام في Bottom Sheet)
  Widget _buildDepartmentInfo() {
    if (_department == null) {
      return const Center(
        child: Text('لا توجد بيانات قسم متاحة'),
      );
    }
    
    // التأكد من أن اسم القسم موجود
    final departmentName = (_department!.name.isNotEmpty) ? _department!.name : 'اسم القسم غير متوفر';
    
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس القسم
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _department!.isActive ? AppColors.primary.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.business,
                    color: _department!.isActive ? AppColors.primary : Colors.grey,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        departmentName,
                        style: AppStyles.headingMedium.copyWith(
                          color: _department!.isActive ? Colors.black87 : Colors.grey,
                        ),
                      ),
                      if (_department!.description != null && _department!.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            _department!.description!,
                            style: AppStyles.bodyMedium.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                    ],
                  ),
                ),
                if (!_department!.isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.red.shade300),
                    ),
                    child: Text(
                      'غير نشط',
                      style: TextStyle(
                        color: Colors.red.shade800,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            
            const Divider(height: 32),
            
            // معلومات القسم
            Row(
              children: [
                // مدير القسم
                Expanded(
                  child: _buildInfoCard(
                    icon: Icons.person_outline,
                    title: 'مدير القسم',
                    content: _buildManagerInfo(),
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                // عدد الأعضاء
                Expanded(
                  child: _buildInfoCard(
                    icon: Icons.people_outline,
                    title: 'عدد الأعضاء',
                    content: Text(
                      '${_members.length} عضو',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            
            // معلومات إضافية
            if (_department!.parentId != null || _department!.children.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  children: [
                    if (_department!.parentId != null)
                      Expanded(
                        child: _buildInfoCard(
                          icon: Icons.account_tree_outlined,
                          title: 'قسم فرعي',
                          content: const Text(
                            'هذا قسم فرعي',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                          color: Colors.orange,
                        ),
                      ),
                    if (_department!.parentId != null && _department!.children.isNotEmpty)
                      const SizedBox(width: 16),
                    if (_department!.children.isNotEmpty)
                      Expanded(
                        child: _buildInfoCard(
                          icon: Icons.folder_outlined,
                          title: 'الأقسام الفرعية',
                          content: Text(
                            '${_department!.children.length} قسم فرعي',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          color: Colors.purple,
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required Widget content,
    required Color color,
  }) {
    return Card(
      elevation: 3,
      // color: color.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: color.withValues(alpha: 0.2)),
      ),
      child: Container(
        
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            content,
          ],
        ),
      ),
    );
  }

  /// بناء معلومات المدير
  Widget _buildManagerInfo() {
    if (_manager != null && _manager!.name.isNotEmpty) {
      return Row(
        children: [
          CircleAvatar(
            backgroundColor: AppColors.primary,
            radius: 16,
            child: Text(
              _manager!.name.substring(0, 1).toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _manager!.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                if (_manager!.email?.isNotEmpty == true)
                  Text(
                    _manager!.email!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          // زر إزالة المدير
          if (_permissionService.canAssignDepartmentManager())
            IconButton(
              icon: const Icon(Icons.person_remove, color: Colors.red, size: 18),
              tooltip: 'إزالة المدير',
              onPressed: _removeManager,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
        ],
      );
    } else {
      return Row(
        children: [
          CircleAvatar(
            backgroundColor: Colors.grey[300],
            radius: 16,
            child: Icon(
              Icons.person_off,
              color: Colors.grey[600],
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'لا يوجد مدير',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      );
    }
  }

  /// بناء تبويب الأعضاء
  Widget _buildMembersTab() {
    final canManageMembers = _permissionService.canManageDepartmentUsers();
    
    // إعادة تحميل الأعضاء إذا كانت القائمة فارغة والقسم موجود
    if (_members.isEmpty && _department != null && _department!.id != 0 && !_isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadDepartmentMembers();
      });
    }

    return Column(
      children: [
        if (canManageMembers)
          Padding(
            padding: const EdgeInsets.all(16),
            child: _permissionService.canAddUsersToDepartment()
                ? ElevatedButton.icon(
                    onPressed: _addUserToDepartment,
                    icon: const Icon(Icons.person_add,color: Colors.white,),
                    label: const Text('إضافة عضو'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        Expanded(
          child: _members.isEmpty
              ? const Center(child: Text('لا يوجد أعضاء في هذا القسم'))
              : ListView.builder(
                  itemCount: _members.length,
                  itemBuilder: (context, index) {
                    final member = _members[index];
                    final isManager = _manager != null && member.id == _manager!.id;
                    // توضيح بالعربي إذا كان الاسم أو البريد ناقص
                    final hasName = member.name.isNotEmpty;
                    final hasEmail = member.email?.isNotEmpty == true;
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isManager ? AppColors.accent : AppColors.primary,
                        child: Text(
                          hasName ? member.name.substring(0, 1).toUpperCase() : '?',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: hasName ? Text(member.name) : const Text('اسم غير متوفر', style: TextStyle(color: Colors.red)),
                      subtitle: hasEmail ? Text(member.email!) : const Text('بريد غير متوفر', style: TextStyle(color: Colors.red)),
                      trailing: canManageMembers
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!isManager && _permissionService.canAssignDepartmentManager() &&
                                    _permissionService.canAccessDepartment(_department!.id))
                                  IconButton(
                                    icon: const Icon(Icons.admin_panel_settings),
                                    tooltip: 'تعيين كمدير',
                                    onPressed: () => _assignManager(member),
                                  ),
                                if (_permissionService.canRemoveUsersFromDepartment() &&
                                    _permissionService.canAccessDepartment(_department!.id))
                                  IconButton(
                                    icon: const Icon(Icons.person_remove),
                                    tooltip: 'إزالة من القسم',
                                    onPressed: () => _removeUserFromDepartment(member),
                                  ),
                              ],
                            )
                          : null,
                    );
                  },
                ),
        ),
      ],
    );
  }

  /// بناء تبويب المهام
  Widget _buildTasksTab() {
    return _isLoadingTasks
        ? const Center(child: CircularProgressIndicator())
        : _departmentTasks.isEmpty
            ? _buildEmptyTasksWidget()
            : _buildTasksList();
  }

  /// بناء واجهة عرض القائمة الفارغة للمهام
  Widget _buildEmptyTasksWidget() {
    return SingleChildScrollView(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.task_alt,
                size: 80,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد مهام',
                style: AppStyles.headingMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد مهام مرتبطة بهذا القسم حالياً',
                style: AppStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              Text(
                _department?.name ?? 'القسم',
                style: AppStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  Get.toNamed(AppRoutes.createTask);
                },
                icon: const Icon(Icons.add),
                label: const Text('إنشاء مهمة جديدة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قائمة المهام
  Widget _buildTasksList() {
    return Column(
      children: [
        // شريط الفلترة
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      _isFiltered
                          ? 'المهام (${_filteredTasks.length} من ${_departmentTasks.length})'
                          : 'المهام (${_departmentTasks.length})',
                      style: AppStyles.titleMedium,
                    ),
                    if (_isFiltered)
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.filter_list, size: 16, color: Colors.blue.shade800),
                              const SizedBox(width: 4),
                              Text(
                                'مفلتر',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue.shade800,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              if (_permissionService.canFilterTasks())
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  tooltip: 'فلترة',
                  onPressed: _showTaskFilterDialog,
                ),
              if (_permissionService.canRefreshTasks())
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث',
                  onPressed: _loadDepartmentTasks,
                ),
            ],
          ),
        ),

        // قائمة المهام
        Expanded(
          child: _isFiltered && _filteredTasks.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.filter_list_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مهام تطابق الفلترة',
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _filterStatus = null;
                            _filterAssigneeId = null;
                            _filterMinCompletion = 0.0;
                            _filterMaxCompletion = 100.0;
                            _isFiltered = false;
                            _applyFilters();
                          });
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة تعيين الفلترة'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _isFiltered ? _filteredTasks.length : _departmentTasks.length,
                  itemBuilder: (context, index) {
                    final task = _isFiltered ? _filteredTasks[index] : _departmentTasks[index];
                    return _buildTaskItem(task);
                  },
                ),
        ),
      ],
    );
  }

  /// بناء عنصر المهمة
  Widget _buildTaskItem(Task task) {
    // الحصول على اسم المستخدم المسند إليه المهمة
    String assigneeName = 'غير مسند';
    if (task.assigneeId != null) {
      final assignee = _members.firstWhere(
        (member) => member.id == task.assigneeId,
        orElse: () => User(
          id: 0,
          name: 'غير معروف',
          email: '',
          password: '',
          role: null,
          isActive: true,
          createdAt: DateTime.now().millisecondsSinceEpoch,
        ),
      );
      assigneeName = assignee.name;
    }

    // تحديد لون حالة المهمة
    Color statusColor;
    switch (task.status) {
      case 'pending': // pending
        statusColor = Colors.grey;
          break;
      case 'inProgress': // inProgress
          statusColor = Colors.blue;
          break;
      case 'completed': // completed
          statusColor = Colors.green;
          break;
      case 'cancelled': // cancelled
          statusColor = Colors.red;
          break;
      case 'waitingForInfo': // waitingForInfo
          statusColor = Colors.orange;
          break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: () {
          // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
          if (!_permissionService.canViewTaskDetails()) {
            Get.snackbar(
              'غير مسموح',
              'ليس لديك صلاحية لعرض تفاصيل المهام',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
              duration: const Duration(seconds: 3),
            );
            return;
          }

          // الانتقال إلى شاشة تفاصيل المهمة
          Get.toNamed(AppRoutes.taskDetail, arguments: {'taskId': task.id});
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة الحالة
              CircleAvatar(
                backgroundColor: statusColor.withAlpha(50),
                radius: 20,
                child: Icon(
                  Icons.task_alt,
                  color: statusColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              
              // محتوى المهمة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رقم المهمة وعنوانها
                    Row(
                      children: [
                        // رقم المهمة
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue.shade200, width: 1),
                          ),
                          child: Text(
                            '#${task.id}',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // عنوان المهمة
                        Expanded(
                          child: Text(
                            task.title,
                            style: AppStyles.titleSmall,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    
                    // معلومات المهمة
                    Row(
                      children: [
                        Icon(Icons.person, size: 14, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            assigneeName,
                            style: const TextStyle(fontSize: 12),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.calendar_today, size: 14, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            task.dueDate != null
                                ? _dateFormat.format(DateTime.fromMillisecondsSinceEpoch(task.dueDate!))
                                : 'لا يوجد موعد نهائي',
                            style: const TextStyle(fontSize: 12),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // معلومات الحالة والأزرار
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // حالة المهمة
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(50),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(task.status),
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  
                  // نسبة الإكمال
                  Text(
                    '${task.completionPercentage.toInt()}%',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // زر نقل المهمة
                  if (_permissionService.canTransferTask())
                    SizedBox(
                      width: 32,
                      height: 32,
                      child: IconButton(
                        icon: const Icon(Icons.compare_arrows, size: 18),
                        color: Colors.blue,
                        tooltip: 'نقل المهمة إلى قسم آخر',
                        onPressed: () => _showTransferTaskToDepartmentDialog(task),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// حوار نقل المهمة إلى قسم آخر
  void _showTransferTaskToDepartmentDialog(Task task) async {
    final departmentsApi = DepartmentsApiService();
    List<Department> departments = [];
    int? selectedDepartmentId;
    bool isLoading = true;
    String? error;

    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            if (isLoading) {
              // تحميل الأقسام عند فتح الحوار
              departmentsApi.getAllDepartments().then((list) {
                setState(() {
                  departments = list;
                  isLoading = false;
                });
              }).catchError((e) {
                setState(() {
                  error = 'خطأ في تحميل الأقسام';
                  isLoading = false;
                });
              });
            }
            return AlertDialog(
              title: const Text('نقل المهمة إلى قسم آخر'),
              content: isLoading
                  ? const SizedBox(height: 80, child: Center(child: CircularProgressIndicator()))
                  : error != null
                      ? Text(error!)
                      : DropdownButtonFormField<int>(
                          value: selectedDepartmentId,
                          items: departments
                              .where((d) => d.id != _department?.id)
                              .map((dept) => DropdownMenuItem(
                                    value: dept.id,
                                    child: Text(dept.name),
                                  ))
                              .toList(),
                          onChanged: (val) => setState(() => selectedDepartmentId = val),
                          decoration: const InputDecoration(
                            labelText: 'اختر القسم الجديد',
                            prefixIcon: Icon(Icons.business),
                          ),
                        ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: !isLoading && selectedDepartmentId != null
                      ? () async {
                          Navigator.of(context).pop();
                          // تنفيذ النقل
                          Get.dialog(const Center(child: CircularProgressIndicator()), barrierDismissible: false);
                          final success = await departmentsApi.transferTask(task.id, selectedDepartmentId!);
                          Get.back();
                          if (success) {
                            Get.snackbar('تم النقل', 'تم نقل المهمة بنجاح', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green.shade100, colorText: Colors.green.shade800);
                            _loadDepartmentTasks();
                          } else {
                            Get.snackbar('خطأ', 'فشل نقل المهمة', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red.shade100, colorText: Colors.red.shade800);
                          }
                        }
                      : null,
                  child: const Text('نقل'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// الحصول على نص حالة المهمة
  String _getStatusText(String status) {
    switch (status) {
      case 'pending': // pending
        return 'قيد الانتظار';
      case 'inProgress': // inProgress
        return 'قيد التنفيذ';
      case 'completed': // completed
        return 'مكتملة';
      case 'cancelled': // cancelled
        return 'ملغاة';
      case 'waitingForInfo': // waitingForInfo
        return 'في انتظار معلومات';
      default:
        return 'غير معروف';
    }
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatsTab() {
    // إذا كان هناك تحميل للإحصائيات من API
    if (_isLoadingStats) {
      return const Center(child: CircularProgressIndicator());
    }
    // إذا كانت بيانات الإحصائيات متوفرة من API
    if (_departmentStats != null && _departmentStats!.isNotEmpty) {
      final totalTasks = _departmentStats!["totalTasks"] ?? 0;
      final completedTasks = _departmentStats!["completedTasks"] ?? 0;
      final overdueTasks = _departmentStats!["overdueTasks"] ?? 0;
      final avgCompletion = _departmentStats!["avgCompletion"] ?? 0.0;
      final totalEmployees = _departmentStats!["totalEmployees"] ?? 0;
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ملخص إحصائيات القسم من API
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('إحصائيات القسم', style: AppStyles.titleMedium),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('المهام', totalTasks.toString(), Icons.task, Colors.orange),
                        _buildStatItem('مكتملة', completedTasks.toString(), Icons.check_circle, Colors.green),
                        _buildStatItem('متأخرة', overdueTasks.toString(), Icons.warning, Colors.red),
                        _buildStatItem('متوسط الإنجاز', '${avgCompletion.toStringAsFixed(1)}%', Icons.percent, Colors.purple),
                        _buildStatItem('الأعضاء', totalEmployees.toString(), Icons.people, Colors.blue),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            // ملخص المهام المحلي (اختياري)
            _buildTasksSummary(),
            const SizedBox(height: 24),
            _buildTaskStatusDistribution(),
            const SizedBox(height: 24),
            _buildMemberProductivity(),
          ],
        ),
      );
    }
    // إذا لم تتوفر بيانات من API، استخدم الملخص المحلي
    return _isLoadingTasks
        ? const Center(child: CircularProgressIndicator())
        : _departmentTasks.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.bar_chart,
                      size: 80,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد إحصائيات',
                      style: AppStyles.headingMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد مهام لعرض إحصائياتها',
                      style: AppStyles.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTasksSummary(),
                    const SizedBox(height: 24),
                    _buildTaskStatusDistribution(),
                    const SizedBox(height: 24),
                    _buildMemberProductivity(),
                  ],
                ),
              );
  }

  /// عنصر إحصائي صغير
  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(value, style: TextStyle(fontWeight: FontWeight.bold, color: color)),
        Text(title, style: const TextStyle(fontSize: 11, color: Colors.black54)),
      ],
    );
  }

  /// بناء ملخص المهام
  Widget _buildTasksSummary() {
    // حساب عدد المهام حسب الحالة
    final pendingTasks = _departmentTasks.where((task) => task.status == 'pending').length;
    final inProgressTasks = _departmentTasks.where((task) => task.status == 'in_progress').length;
    final completedTasks = _departmentTasks.where((task) => task.status == 'completed').length;
    // حساب عدد المهام الملغاة (غير مستخدم حالياً في الواجهة)
    // final cancelledTasks = _departmentTasks.where((task) => task.status == 'cancelled').length;
    final waitingForInfoTasks = _departmentTasks.where((task) => task.status == 'waiting_for_info').length;

    // حساب متوسط نسبة الإكمال
    final avgCompletion = _departmentTasks.isEmpty
        ? 0.0
        : _departmentTasks.fold(0.0, (sum, task) => sum + task.completionPercentage) / _departmentTasks.length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المهام',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المهام',
                    _departmentTasks.length.toString(),
                    Icons.task_alt,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'متوسط الإكمال',
                    '${avgCompletion.toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'قيد التنفيذ',
                    inProgressTasks.toString(),
                    Icons.play_circle_outline,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'مكتملة',
                    completedTasks.toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'قيد الانتظار',
                    pendingTasks.toString(),
                    Icons.pending_outlined,
                    Colors.grey,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'في انتظار معلومات',
                    waitingForInfoTasks.toString(),
                    Icons.help_outline,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// بناء توزيع المهام حسب الحالة
  Widget _buildTaskStatusDistribution() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع المهام حسب الحالة',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildTaskStatusChart(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط توزيع المهام حسب الحالة باستخدام Syncfusion Charts
  Widget _buildTaskStatusChart() {
    // حساب عدد المهام حسب الحالة
    final pendingTasks = _departmentTasks.where((task) => task.status == 'pending').length;
    final inProgressTasks = _departmentTasks.where((task) => task.status == 'in_progress').length;
    final completedTasks = _departmentTasks.where((task) => task.status == 'completed').length;
    // حساب عدد المهام الملغاة (مستخدم في المخطط البياني)
    final cancelledTasks = _departmentTasks.where((task) => task.status == 'cancelled').length;
    final waitingForInfoTasks = _departmentTasks.where((task) => task.status == 'waiting_for_info').length;

    final List<TaskStatusData> chartData = [
      TaskStatusData('انتظار', pendingTasks, Colors.grey),
      TaskStatusData('تنفيذ', inProgressTasks, Colors.blue),
      TaskStatusData('مكتملة', completedTasks, Colors.green),
      TaskStatusData('ملغاة', cancelledTasks, Colors.red),
      TaskStatusData('معلقة', waitingForInfoTasks, Colors.orange),
    ];

    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: 'حالة المهام'),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: 'عدد المهام'),
        minimum: 0,
        interval: 1,
      ),
      series: <CartesianSeries>[
        ColumnSeries<TaskStatusData, String>(
          dataSource: chartData,
          xValueMapper: (TaskStatusData data, _) => data.status,
          yValueMapper: (TaskStatusData data, _) => data.count,
          pointColorMapper: (TaskStatusData data, _) => data.color,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            textStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
      ],
      tooltipBehavior: TooltipBehavior(
        enable: true,
        format: 'point.x: point.y مهمة',
      ),
    );
  }

  /// بناء إنتاجية الأعضاء
  Widget _buildMemberProductivity() {
    if (_members.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إنتاجية الأعضاء',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _members.length,
              itemBuilder: (context, index) {
                final member = _members[index];
                final productivity = _userProductivity[member.id] ?? 0.0;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: AppColors.primary,
                            radius: 16,
                            child: Text(
                              member.name.substring(0, 1).toUpperCase(),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              member.name,
                              style: AppStyles.titleSmall,
                            ),
                          ),
                          Text(
                            '${productivity.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getProductivityColor(productivity),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: productivity / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getProductivityColor(productivity),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون الإنتاجية
  Color _getProductivityColor(double productivity) {
    if (productivity >= 80) return Colors.green;
    if (productivity >= 50) return Colors.orange;
    return Colors.red;
  }
}
