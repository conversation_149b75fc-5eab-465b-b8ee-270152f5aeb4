import 'package:flutter/foundation.dart';

import '../../models/task_models.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';

/// تقرير توزيع عبء العمل
class WorkloadDistributionReport {
  final List<UserWorkload> userWorkloads;
  final List<DepartmentWorkload> departmentWorkloads;
  final List<TimeBasedWorkload> timeBasedWorkloads;
  final DateTime generatedAt;
  final String period;
  
  // إحصائيات عامة
  final int totalTasks;
  final int totalActiveUsers;
  final double averageWorkloadPerUser;
  final double systemUtilization;
  
  // تحليلات متقدمة
  final UserWorkload? mostOverloadedUser;
  final UserWorkload? leastLoadedUser;
  final DepartmentWorkload? busiestDepartment;

  const WorkloadDistributionReport({
    required this.userWorkloads,
    required this.departmentWorkloads,
    required this.timeBasedWorkloads,
    required this.generatedAt,
    required this.period,
    required this.totalTasks,
    required this.totalActiveUsers,
    required this.averageWorkloadPerUser,
    required this.systemUtilization,
    this.mostOverloadedUser,
    this.leastLoadedUser,
    this.busiestDepartment,
  });

  /// إنشاء تقرير من قائمة المهام والمستخدمين والأقسام
  static WorkloadDistributionReport fromTasks(
    List<Task> tasks,
    List<User> users,
    List<Department> departments, {
    DateTime? startDate,
    DateTime? endDate,
    String period = 'جميع المهام',
  }) {
    debugPrint('🔄 بدء معالجة تقرير توزيع عبء العمل');
    debugPrint('📋 إجمالي المهام المستلمة: ${tasks.length}');
    debugPrint('👥 إجمالي المستخدمين: ${users.length}');
    debugPrint('🏢 إجمالي الأقسام: ${departments.length}');

    // تصفية المهام حسب الفترة الزمنية
    List<Task> filteredTasks;
    if (startDate != null && endDate != null) {
      filteredTasks = tasks.where((task) {
        final taskDate = task.createdAtDateTime;
        return taskDate.isAfter(startDate.subtract(const Duration(days: 1))) && 
               taskDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
      debugPrint('🗓️ تم تطبيق تصفية زمنية محددة');
    } else {
      // إذا لم تكن هناك تواريخ محددة، استخدم جميع المهام
      filteredTasks = tasks;
      debugPrint('🗓️ لا توجد تصفية زمنية - استخدام جميع المهام');
    }

    debugPrint('🔄 المهام بعد التصفية الزمنية: ${filteredTasks.length}');

    // حساب عبء العمل للمستخدمين
    final userWorkloads = _calculateUserWorkloads(filteredTasks, users);
    
    // حساب عبء العمل للأقسام
    final departmentWorkloads = _calculateDepartmentWorkloads(filteredTasks, departments, users, userWorkloads);
    
    // حساب عبء العمل المبني على الوقت
    final timeBasedWorkloads = _calculateTimeBasedWorkloads(filteredTasks, 
      startDate ?? DateTime.now().subtract(const Duration(days: 30)), 
      endDate ?? DateTime.now());

    // حساب الإحصائيات العامة
    final totalTasks = filteredTasks.length;
    final totalActiveUsers = userWorkloads.where((u) => u.totalTasks > 0).length;
    final averageWorkloadPerUser = totalActiveUsers > 0 ? totalTasks / totalActiveUsers : 0.0;
    final systemUtilization = _calculateSystemUtilization(userWorkloads);

    // تحديد أكثر المستخدمين انشغالاً وأقلهم
    UserWorkload? mostOverloaded;
    UserWorkload? leastLoaded;
    if (userWorkloads.isNotEmpty) {
      final activeUsers = userWorkloads.where((u) => u.totalTasks > 0).toList();
      if (activeUsers.isNotEmpty) {
        activeUsers.sort((a, b) => b.totalTasks.compareTo(a.totalTasks));
        mostOverloaded = activeUsers.first;
        leastLoaded = activeUsers.last;
      }
    }

    // تحديد أكثر الأقسام انشغالاً
    DepartmentWorkload? busiestDepartment;
    if (departmentWorkloads.isNotEmpty) {
      departmentWorkloads.sort((a, b) => b.totalTasks.compareTo(a.totalTasks));
      busiestDepartment = departmentWorkloads.first;
    }

    debugPrint('✅ انتهاء إنشاء التقرير');
    debugPrint('📊 النتائج: $totalTasks مهمة، $totalActiveUsers مستخدم نشط، معدل الاستغلال: ${systemUtilization.toStringAsFixed(1)}%');

    return WorkloadDistributionReport(
      userWorkloads: userWorkloads,
      departmentWorkloads: departmentWorkloads,
      timeBasedWorkloads: timeBasedWorkloads,
      generatedAt: DateTime.now(),
      period: period,
      totalTasks: totalTasks,
      totalActiveUsers: totalActiveUsers,
      averageWorkloadPerUser: averageWorkloadPerUser,
      systemUtilization: systemUtilization,
      mostOverloadedUser: mostOverloaded,
      leastLoadedUser: leastLoaded,
      busiestDepartment: busiestDepartment,
    );
  }

  /// حساب عبء العمل للمستخدمين
  static List<UserWorkload> _calculateUserWorkloads(List<Task> tasks, List<User> users) {
    final userWorkloads = <UserWorkload>[];
    
    debugPrint('🔄 حساب عبء العمل للمستخدمين...');
    
    for (final user in users) {
      // البحث عن المهام المرتبطة بالمستخدم (منشئ أو مُسند إليه)
      final userTasks = tasks.where((task) => 
        task.assigneeId == user.id || task.creatorId == user.id).toList();
      
      debugPrint('👤 المستخدم ${user.name}: ${userTasks.length} مهمة');
      
      final totalTasks = userTasks.length;
      
      // حساب المهام النشطة (غير مكتملة وغير محذوفة)
      final activeTasks = userTasks.where((task) => 
        task.status != 'completed' && task.status != 'cancelled' && !task.isDeleted).length;
      
      // حساب المهام المكتملة
      final completedTasks = userTasks.where((task) => task.status == 'completed').length;
      
      // حساب المهام المتأخرة
      final overdueTasks = userTasks.where((task) {
        if (task.status == 'completed' || task.status == 'cancelled') return false;
        if (task.dueDateDateTime == null) return false;
        return task.dueDateDateTime!.isBefore(DateTime.now());
      }).length;
      
      // حساب المهام المعلقة
      final pendingTasks = userTasks.where((task) => 
        task.status == 'pending' || task.status == 'todo').length;
      
      // حساب المهام قيد التنفيذ
      final inProgressTasks = userTasks.where((task) => 
        task.status == 'in-progress' || task.status == 'doing').length;

      // حساب متوسط مدة المهام (بالساعات)
      final completedTasksWithTime = userTasks.where((task) => 
        task.completedAt != null && task.actualTime != null && task.actualTime! > 0).toList();
      final averageTaskDuration = completedTasksWithTime.isNotEmpty
          ? (completedTasksWithTime.map((task) => task.actualTime!).reduce((a, b) => a + b) / completedTasksWithTime.length / 60).round() // تحويل من دقائق إلى ساعات
          : 0;

      // حساب معدل الاستغلال (نسبة المهام النشطة إلى إجمالي المهام)
      final utilizationRate = totalTasks > 0 ? (activeTasks / totalTasks) * 100 : 0.0;
      
      debugPrint('   📊 المهام: إجمالي=$totalTasks, نشطة=$activeTasks, مكتملة=$completedTasks, متأخرة=$overdueTasks');

      userWorkloads.add(UserWorkload(
        user: user,
        totalTasks: totalTasks,
        activeTasks: activeTasks,
        completedTasks: completedTasks,
        overdueTasks: overdueTasks,
        pendingTasks: pendingTasks,
        inProgressTasks: inProgressTasks,
        workloadPercentage: 0.0, // سيتم حسابه لاحقاً
        averageTaskDuration: averageTaskDuration,
        utilizationRate: utilizationRate,
      ));
    }

    // حساب نسبة عبء العمل مقارنة بالمتوسط العام
    final averageTasks = userWorkloads.isNotEmpty 
        ? userWorkloads.map((u) => u.totalTasks).reduce((a, b) => a + b) / userWorkloads.length
        : 0.0;

    for (int i = 0; i < userWorkloads.length; i++) {
      final workloadPercentage = averageTasks > 0 
          ? (userWorkloads[i].totalTasks / averageTasks) * 100 
          : 0.0;
      
      userWorkloads[i] = UserWorkload(
        user: userWorkloads[i].user,
        totalTasks: userWorkloads[i].totalTasks,
        activeTasks: userWorkloads[i].activeTasks,
        completedTasks: userWorkloads[i].completedTasks,
        overdueTasks: userWorkloads[i].overdueTasks,
        pendingTasks: userWorkloads[i].pendingTasks,
        inProgressTasks: userWorkloads[i].inProgressTasks,
        workloadPercentage: workloadPercentage,
        averageTaskDuration: userWorkloads[i].averageTaskDuration,
        utilizationRate: userWorkloads[i].utilizationRate,
      );
    }

    final totalTasksForAllUsers = userWorkloads
        .map((u) => u.totalTasks)
        .fold(0, (sum, tasks) => sum + tasks);
    
    debugPrint('✅ انتهاء حساب عبء العمل للمستخدمين');
    debugPrint('📊 إجمالي المهام لجميع المستخدمين: $totalTasksForAllUsers');
    debugPrint('👥 عدد المستخدمين مع مهام: ${userWorkloads.where((u) => u.totalTasks > 0).length}');

    return userWorkloads;
  }

  /// حساب عبء العمل للأقسام
  static List<DepartmentWorkload> _calculateDepartmentWorkloads(
    List<Task> tasks, 
    List<Department> departments, 
    List<User> users,
    List<UserWorkload> userWorkloads,
  ) {
    final departmentWorkloads = <DepartmentWorkload>[];
    
    debugPrint('🔄 حساب عبء العمل للأقسام...');
    
    for (final department in departments) {
      // البحث عن المستخدمين في هذا القسم
      final departmentUsers = users.where((user) => user.departmentId == department.id).toList();
      final departmentUserWorkloads = userWorkloads.where((uw) => 
        departmentUsers.any((du) => du.id == uw.user.id)).toList();
      
      // حساب إجمالي المهام للقسم
      final totalTasks = departmentUserWorkloads.fold(0, (sum, uw) => sum + uw.totalTasks);
      final activeTasks = departmentUserWorkloads.fold(0, (sum, uw) => sum + uw.activeTasks);
      final completedTasks = departmentUserWorkloads.fold(0, (sum, uw) => sum + uw.completedTasks);
      final overdueTasks = departmentUserWorkloads.fold(0, (sum, uw) => sum + uw.overdueTasks);
      
      final totalUsers = departmentUsers.length;
      final averageTasksPerUser = totalUsers > 0 ? totalTasks / totalUsers : 0.0;
      final departmentUtilization = totalTasks > 0 ? (activeTasks / totalTasks) * 100 : 0.0;
      
      debugPrint('🏢 القسم ${department.name}: $totalTasks مهمة، $totalUsers مستخدم');

      departmentWorkloads.add(DepartmentWorkload(
        department: department,
        totalTasks: totalTasks,
        activeTasks: activeTasks,
        completedTasks: completedTasks,
        overdueTasks: overdueTasks,
        totalUsers: totalUsers,
        averageTasksPerUser: averageTasksPerUser,
        departmentUtilization: departmentUtilization,
        userWorkloads: departmentUserWorkloads,
      ));
    }
    
    debugPrint('✅ انتهاء حساب عبء العمل للأقسام');
    
    return departmentWorkloads;
  }

  /// حساب عبء العمل المبني على الوقت
  static List<TimeBasedWorkload> _calculateTimeBasedWorkloads(
    List<Task> tasks, 
    DateTime startDate, 
    DateTime endDate,
  ) {
    final timeBasedWorkloads = <TimeBasedWorkload>[];
    
    debugPrint('🔄 حساب التوزيع الزمني لعبء العمل...');
    
    // حساب البيانات يومياً
    final currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateDay = DateTime(endDate.year, endDate.month, endDate.day);
    
    var processingDate = currentDate;
    while (processingDate.isBefore(endDateDay.add(const Duration(days: 1)))) {
      final nextDay = processingDate.add(const Duration(days: 1));
      
      final dayTasks = tasks.where((task) {
        final taskDate = task.createdAtDateTime;
        return taskDate.isAfter(processingDate.subtract(const Duration(days: 1))) && 
               taskDate.isBefore(nextDay);
      }).toList();
      
      final tasksCreated = dayTasks.length;
      final tasksCompleted = dayTasks.where((task) => 
        task.completedAt != null && 
        task.completedAtDateTime != null && 
        task.completedAtDateTime!.isAfter(processingDate.subtract(const Duration(days: 1))) && 
        task.completedAtDateTime!.isBefore(nextDay)
      ).length;
      final tasksOverdue = dayTasks.where((task) => 
        task.dueDateDateTime != null && 
        task.dueDateDateTime!.isBefore(processingDate) && 
        task.status != 'completed'
      ).length;
      
      // حساب كثافة عبء العمل (نسبة مئوية بناءً على المهام النشطة)
      final workloadIntensity = tasksCreated > 0 ? 
        ((tasksCreated - tasksCompleted + tasksOverdue) / tasksCreated * 100).clamp(0.0, 100.0) : 0.0;
      
      timeBasedWorkloads.add(TimeBasedWorkload(
        date: processingDate,
        tasksCreated: tasksCreated,
        tasksCompleted: tasksCompleted,
        tasksOverdue: tasksOverdue,
        workloadIntensity: workloadIntensity,
      ));
      
      processingDate = processingDate.add(const Duration(days: 1));
    }
    
    debugPrint('✅ انتهاء حساب التوزيع الزمني - ${timeBasedWorkloads.length} يوم');
    
    return timeBasedWorkloads;
  }

  /// حساب معدل الاستغلال العام للنظام
  static double _calculateSystemUtilization(List<UserWorkload> userWorkloads) {
    if (userWorkloads.isEmpty) return 0.0;
    
    final totalTasks = userWorkloads.fold(0, (sum, u) => sum + u.totalTasks);
    final totalActiveTasks = userWorkloads.fold(0, (sum, u) => sum + u.activeTasks);
    
    return totalTasks > 0 ? (totalActiveTasks / totalTasks) * 100 : 0.0;
  }
}

/// عبء العمل للمستخدم
class UserWorkload {
  final User user;
  final int totalTasks;
  final int activeTasks;
  final int completedTasks;
  final int overdueTasks;
  final int pendingTasks;
  final int inProgressTasks;
  final double workloadPercentage; // نسبة عبء العمل مقارنة بالمتوسط
  final int averageTaskDuration; // متوسط مدة المهام بالساعات
  final double utilizationRate; // معدل الاستغلال

  const UserWorkload({
    required this.user,
    required this.totalTasks,
    required this.activeTasks,
    required this.completedTasks,
    required this.overdueTasks,
    required this.pendingTasks,
    required this.inProgressTasks,
    required this.workloadPercentage,
    required this.averageTaskDuration,
    required this.utilizationRate,
  });
}

/// عبء العمل للقسم
class DepartmentWorkload {
  final Department department;
  final int totalTasks;
  final int activeTasks;
  final int completedTasks;
  final int overdueTasks;
  final int totalUsers;
  final double averageTasksPerUser;
  final double departmentUtilization;
  final List<UserWorkload> userWorkloads;

  const DepartmentWorkload({
    required this.department,
    required this.totalTasks,
    required this.activeTasks,
    required this.completedTasks,
    required this.overdueTasks,
    required this.totalUsers,
    required this.averageTasksPerUser,
    required this.departmentUtilization,
    required this.userWorkloads,
  });
}

/// عبء العمل المبني على الوقت
class TimeBasedWorkload {
  final DateTime date;
  final int tasksCreated;
  final int tasksCompleted;
  final int tasksOverdue;
  final double workloadIntensity; // كثافة عبء العمل كنسبة مئوية

  const TimeBasedWorkload({
    required this.date,
    required this.tasksCreated,
    required this.tasksCompleted,
    required this.tasksOverdue,
    required this.workloadIntensity,
  });
}