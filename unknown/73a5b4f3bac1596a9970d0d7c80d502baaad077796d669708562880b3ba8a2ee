import 'package:get/get.dart';
import '../../models/message_reaction_models.dart';
import 'base_api_service.dart';

/// خدمة API لتفاعلات الرسائل - متوافقة مع ASP.NET Core
class MessageReactionsApiService extends GetxService {
  late final BaseApiService _apiService;

  MessageReactionsApiService() {
    // محاولة الحصول على BaseApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<BaseApiService>();
    } catch (e) {
      _apiService = BaseApiService();
    }
  }

  /// الحصول على جميع التفاعلات
  Future<List<MessageReaction>> getAllReactions() async {
    try {
      final response = await _apiService.get('/api/MessageReactions');
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل التفاعلات: $e');
    }
  }

  /// الحصول على تفاعلات رسالة محددة
  Future<List<MessageReaction>> getReactionsByMessage(int messageId) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/message/$messageId');
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل تفاعلات الرسالة: $e');
    }
  }

  /// الحصول على تفاعل بالمعرف
  Future<MessageReaction> getReactionById(int id) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/$id');
      return _apiService.handleResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل التفاعل: $e');
    }
  }

  /// إضافة تفاعل جديد
  Future<MessageReaction> addReaction(MessageReaction reaction) async {
    try {
      final response = await _apiService.post('/api/MessageReactions', reaction.toJson());
      return _apiService.handleResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في إضافة التفاعل: $e');
    }
  }

  /// تحديث تفاعل
  Future<MessageReaction> updateReaction(MessageReaction reaction) async {
    try {
      final response = await _apiService.put('/api/MessageReactions/${reaction.id}', reaction.toJson());
      return _apiService.handleResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحديث التفاعل: $e');
    }
  }

  /// حذف تفاعل
  Future<void> deleteReaction(int id) async {
    try {
      await _apiService.delete('/api/MessageReactions/$id');
    } catch (e) {
      throw Exception('خطأ في حذف التفاعل: $e');
    }
  }

  /// إضافة أو إزالة تفاعل (toggle)
  Future<MessageReaction?> toggleReaction(int messageId, int userId, String reaction) async {
    try {
      final response = await _apiService.post('/api/MessageReactions/toggle', {
        'messageId': messageId,
        'userId': userId,
        'reaction': reaction,
      });
      
      if (response.statusCode == 204) {
        // تم حذف التفاعل
        return null;
      }
      
      return _apiService.handleResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تبديل التفاعل: $e');
    }
  }

  /// الحصول على تفاعلات المستخدم
  Future<List<MessageReaction>> getReactionsByUser(int userId) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/user/$userId');
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل تفاعلات المستخدم: $e');
    }
  }

  /// الحصول على إحصائيات التفاعلات لرسالة
  Future<Map<String, int>> getReactionStats(int messageId) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/message/$messageId/statistics');
      return _apiService.handleResponse<Map<String, int>>(
        response,
        (json) => Map<String, int>.from(json),
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات التفاعلات: $e');
    }
  }

  /// الحصول على أشهر التفاعلات
  Future<List<Map<String, dynamic>>> getPopularReactions() async {
    try {
      final response = await _apiService.get('/api/MessageReactions/popular');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في تحميل التفاعلات الشائعة: $e');
    }
  }

  /// البحث في التفاعلات
  Future<List<MessageReaction>> searchReactions(String query, Map<String, dynamic>? filters) async {
    try {
      final queryParams = {
        'q': query,
        ...?filters?.map((key, value) => MapEntry(key, value.toString())),
      };
      final response = await _apiService.get('/api/MessageReactions/search', queryParams: queryParams);
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في البحث في التفاعلات: $e');
    }
  }

  /// الحصول على التفاعلات حسب النوع
  Future<List<MessageReaction>> getReactionsByType(String reactionType) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/type/$reactionType');
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل التفاعلات حسب النوع: $e');
    }
  }

  /// الحصول على التفاعلات في فترة زمنية
  Future<List<MessageReaction>> getReactionsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final response = await _apiService.get('/api/MessageReactions/date-range', queryParams: {
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
      });
      return _apiService.handleListResponse<MessageReaction>(
        response,
        (json) => MessageReaction.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل التفاعلات في الفترة المحددة: $e');
    }
  }

  /// حذف جميع تفاعلات رسالة
  Future<void> deleteAllReactionsForMessage(int messageId) async {
    try {
      await _apiService.delete('/api/MessageReactions/message/$messageId');
    } catch (e) {
      throw Exception('خطأ في حذف تفاعلات الرسالة: $e');
    }
  }

  /// حذف جميع تفاعلات المستخدم
  Future<void> deleteAllReactionsForUser(int userId) async {
    try {
      await _apiService.delete('/api/MessageReactions/user/$userId');
    } catch (e) {
      throw Exception('خطأ في حذف تفاعلات المستخدم: $e');
    }
  }

  /// الحصول على إحصائيات عامة للتفاعلات
  Future<Map<String, dynamic>> getGeneralReactionStats() async {
    try {
      final response = await _apiService.get('/api/MessageReactions/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات التفاعلات: $e');
    }
  }

  /// الحصول على أنواع التفاعلات المتاحة
  Future<List<String>> getAvailableReactionTypes() async {
    try {
      final response = await _apiService.get('/api/MessageReactions/types');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      throw Exception('خطأ في تحميل أنواع التفاعلات: $e');
    }
  }
}
