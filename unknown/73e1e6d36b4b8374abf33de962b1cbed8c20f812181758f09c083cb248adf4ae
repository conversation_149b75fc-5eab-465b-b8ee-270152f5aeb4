import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// مستويات التسجيل
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// خدمة تسجيل الأحداث والأخطاء
class AppLogger {
  // مستوى التسجيل الحالي
  static LogLevel _currentLevel = kDebugMode ? LogLevel.debug : LogLevel.info;

  // تنسيق التاريخ والوقت
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss.SSS');

  // تمكين أو تعطيل التسجيل
  static bool _enabled = true;

  /// تعيين مستوى التسجيل
  static void setLevel(LogLevel level) {
    _currentLevel = level;
  }

  /// تمكين أو تعطيل التسجيل
  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  /// تسجيل رسالة تصحيح
  static void debug(String message) {
    _log(LogLevel.debug, message);
  }

  /// تسجيل رسالة معلومات
  static void info(String message) {
    _log(LogLevel.info, message);
  }

  /// تسجيل رسالة تحذير
  static void warning(String message) {
    _log(LogLevel.warning, message);
  }

  /// تسجيل رسالة خطأ
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _log(LogLevel.error, message);

    if (error != null) {
      _log(LogLevel.error, 'Error details: $error');
    }

    if (stackTrace != null) {
      // تقصير تتبع المكدس لتجنب الإخراج الطويل جدًا
      final shortStackTrace =
          stackTrace.toString().split('\n').take(10).join('\n');
      _log(LogLevel.error, 'Stack trace: $shortStackTrace');
    }
  }

  /// تسجيل رسالة
  static void _log(LogLevel level, String message) {
    // التحقق من تمكين التسجيل ومستوى التسجيل
    if (!_enabled || level.index < _currentLevel.index) {
      return;
    }

    // الحصول على الوقت الحالي
    final timestamp = _dateFormat.format(DateTime.now());

    // الحصول على اسم المستوى
    final levelName = level.toString().split('.').last.toUpperCase();

    // تنسيق الرسالة
    final formattedMessage = '[$timestamp] $levelName: $message';

    // طباعة الرسالة
    switch (level) {
      case LogLevel.debug:
        debugPrint(formattedMessage);
        break;
      case LogLevel.info:
        debugPrint('\x1B[32m$formattedMessage\x1B[0m'); // أخضر
        break;
      case LogLevel.warning:
        debugPrint('\x1B[33m$formattedMessage\x1B[0m'); // أصفر
        break;
      case LogLevel.error:
        debugPrint('\x1B[31m$formattedMessage\x1B[0m'); // أحمر
        break;
    }
  }
}
