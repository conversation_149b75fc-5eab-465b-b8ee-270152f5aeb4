import 'package:flutter/material.dart';

import '../models/user_model.dart';
import '../services/api/permissions_api_service.dart';

/// أداة اختبار نظام الصلاحيات الموحد
///
/// تستخدم هذه الأداة للتحقق من عمل نظام الصلاحيات الموحد
/// وتوحيد نظام الصلاحيات ونظام الوصول للمهام
class PermissionTest {
  final PermissionsApiService _permissionService = PermissionsApiService();
  final User? currentUser;

  PermissionTest({this.currentUser});

  /// اختبار صلاحيات الواجهة
  Future<void> testInterfacePermissions() async {
    debugPrint('=== اختبار صلاحيات الواجهة ===');

    if (currentUser == null) {
      debugPrint('لا يوجد مستخدم حالي');
      return;
    }

    debugPrint(
        'المستخدم الحالي: ${currentUser!.name} (${currentUser!.id}), الدور: ${currentUser!.role}');

    // قائمة الواجهات للاختبار
    final interfaces = [
      'tasks',
      'dashboard',
      'messages',
      'notifications',
      'departments',
      'users',
      'reports',
      'settings',
      'admin',
    ];

    // اختبار كل واجهة
    for (final interface in interfaces) {
      try {
        final permissions = await _permissionService.getAllPermissions();
        final hasAccess = permissions.any((p) =>
          p.name.toLowerCase().contains(interface.toLowerCase()));
        debugPrint('الواجهة: $interface، الوصول: $hasAccess');
      } catch (e) {
        debugPrint('خطأ في فحص الواجهة $interface: $e');
      }
    }
  }

  /// اختبار صلاحيات المهام
  Future<void> testTaskPermissions(String taskId) async {
    debugPrint('=== اختبار صلاحيات المهام ===');

    if (currentUser == null) {
      debugPrint('لا يوجد مستخدم حالي');
      return;
    }

    debugPrint(
        'المستخدم الحالي: ${currentUser!.name} (${currentUser!.id}), الدور: ${currentUser!.role}');
    debugPrint('المهمة: $taskId');

    // قائمة أنواع الصلاحيات للاختبار
    final permissionTypes = [
      'view',
      'edit',
      'delete',
      'assign',
      'transfer',
      'comment',
      'attach',
    ];

    // اختبار كل نوع صلاحية
    for (final type in permissionTypes) {
      try {
        // محاولة الحصول على صلاحية المستخدم للمهمة
        final permissions = await _permissionService.getAllPermissions();
        final hasPermission = permissions.any((p) => p.name.toLowerCase().contains(type));
        debugPrint('نوع الصلاحية: $type، الوصول: $hasPermission');
      } catch (e) {
        debugPrint('خطأ في فحص الصلاحية $type: $e');
      }
    }

    debugPrint('تم اختبار صلاحيات المهام');
  }

  /// اختبار توحيد نظام الصلاحيات ونظام الوصول للمهام
  Future<void> testUnifiedPermissions(String taskId, String userId) async {
    debugPrint('=== اختبار توحيد نظام الصلاحيات ونظام الوصول للمهام ===');
    try {
      // التحقق من صلاحية المستخدم للوصول إلى المهمة
      final permissions = await _permissionService.getAllPermissions();
      final hasTaskPermission = permissions.any((p) => p.name.toLowerCase().contains('view'));
      debugPrint(
          'صلاحية المستخدم $userId للوصول إلى المهمة $taskId: $hasTaskPermission');
      // إضافة المستخدم إلى قائمة الوصول للمهمة
      if (currentUser == null) {
        debugPrint('لا يوجد مستخدم حالي');
        return;
      }
      // محاولة إنشاء صلاحية جديدة (مثال)
      debugPrint('محاولة إضافة المستخدم $userId إلى قائمة الوصول للمهمة $taskId');
      // التحقق مرة أخرى من صلاحية المستخدم للوصول إلى المهمة
      final hasTaskPermissionAfterAdd = permissions.any((p) => p.name.toLowerCase().contains('view'));
      debugPrint(
          'صلاحية المستخدم $userId للوصول إلى المهمة $taskId بعد الإضافة: $hasTaskPermissionAfterAdd');
    } catch (e) {
      debugPrint('خطأ في اختبار الصلاحيات الموحدة: $e');
    }
  }
  /// تشغيل جميع الاختبارات
  Future<void> runAllTests(String taskId, String userId) async {
    await testInterfacePermissions();
    debugPrint('');
    await testTaskPermissions(taskId);
    debugPrint('');
    await testUnifiedPermissions(taskId, userId);
  }
}
