import 'package:flutter/material.dart';
import '../../../models/archive_models.dart';

/// قائمة منسدلة لاختيار تصنيف
class CategoryDropdown extends StatelessWidget {
  /// قائمة التصنيفات المتاحة
  final List<ArchiveCategory> categories;

  /// التصنيف المحدد حاليًا
  final ArchiveCategory? selectedCategory;

  /// دالة يتم استدعاؤها عند تغيير التصنيف المحدد
  final Function(ArchiveCategory?) onChanged;

  const CategoryDropdown({
    super.key,
    required this.categories,
    this.selectedCategory,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام GetBuilder بدلاً من Obx لتجنب مشاكل التحديث المتداخلة
    return DropdownButtonFormField<int>(
      value: selectedCategory?.id,
      decoration: const InputDecoration(
        labelText: 'التصنيف *',
        hintText: 'اختر تصنيف الوثيقة',
        border: OutlineInputBorder(),
      ),
      items: [
        const DropdownMenuItem<int>(
          value: null,
          child: Text('اختر تصنيفًا...'),
        ),
        ...categories.map((category) => DropdownMenuItem<int>(
          value: category.id,
          child: Text(category.name),
        )),
      ],
      validator: (value) {
        if (value == null) {
          return 'يرجى اختيار تصنيف';
        }
        return null;
      },
      onChanged: (value) {
        if (value == null) {
          onChanged(null);
        } else {
          final category = categories.firstWhere(
            (category) => category.id == value,
            orElse: () => categories.first,
          );
          onChanged(category);
        }
      },
    );
  }
}
