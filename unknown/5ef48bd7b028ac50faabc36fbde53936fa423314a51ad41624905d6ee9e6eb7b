import 'permission_models.dart';
import 'user_model.dart';

/// نموذج قالب الصلاحيات
class PermissionTemplate {
  final int id;
  final String name;
  final String? description;
  final String type;
  final String? color;
  final String? icon;
  final bool isActive;
  final bool isDefault;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final int? updatedBy;
  final User? createdByNavigation;
  final User? updatedByNavigation;
  final List<PermissionTemplateItem> templateItems;

  PermissionTemplate({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    this.color,
    this.icon,
    required this.isActive,
    required this.isDefault,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.updatedBy,
    this.createdByNavigation,
    this.updatedByNavigation,
    required this.templateItems,
  });

  factory PermissionTemplate.fromJson(Map<String, dynamic> json) {
    return PermissionTemplate(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      type: json['type'] ?? '',
      color: json['color'],
      icon: json['icon'],
      isActive: json['isActive'] ?? true,
      isDefault: json['isDefault'] ?? false,
      createdBy: json['createdBy'] ?? 0,
      createdAt: json['createdAt'] ?? 0,
      updatedAt: json['updatedAt'],
      updatedBy: json['updatedBy'],
      createdByNavigation: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'])
          : null,
      updatedByNavigation: json['updatedByNavigation'] != null
          ? User.fromJson(json['updatedByNavigation'])
          : null,
      templateItems: json['templateItems'] != null
          ? (json['templateItems'] as List)
              .map((item) => PermissionTemplateItem.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'color': color,
      'icon': icon,
      'isActive': isActive,
      'isDefault': isDefault,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'updatedBy': updatedBy,
      'templateItems': templateItems.map((item) => item.toJson()).toList(),
    };
  }

  /// الحصول على عدد الصلاحيات المفعلة في القالب
  int get enabledPermissionsCount {
    return templateItems.where((item) => item.isEnabled).length;
  }

  /// الحصول على قائمة معرفات الصلاحيات المفعلة
  List<int> get enabledPermissionIds {
    return templateItems
        .where((item) => item.isEnabled)
        .map((item) => item.permissionId)
        .toList();
  }

  /// التحقق من وجود صلاحية معينة في القالب
  bool hasPermission(int permissionId) {
    return templateItems.any((item) => 
        item.permissionId == permissionId && item.isEnabled);
  }

  /// الحصول على لون القالب مع لون افتراضي
  String get displayColor {
    return color ?? _getDefaultColorForType(type);
  }

  /// الحصول على أيقونة القالب مع أيقونة افتراضية
  String get displayIcon {
    return icon ?? _getDefaultIconForType(type);
  }

  /// الحصول على اللون الافتراضي حسب النوع
  static String _getDefaultColorForType(String type) {
    switch (type) {
      case 'admin':
        return '#f44336';
      case 'manager':
        return '#ff9800';
      case 'employee':
        return '#2196f3';
      case 'viewer':
        return '#4caf50';
      default:
        return '#9e9e9e';
    }
  }

  /// الحصول على الأيقونة الافتراضية حسب النوع
  static String _getDefaultIconForType(String type) {
    switch (type) {
      case 'admin':
        return 'admin_panel_settings';
      case 'manager':
        return 'manage_accounts';
      case 'employee':
        return 'person';
      case 'viewer':
        return 'visibility';
      default:
        return 'security';
    }
  }
}

/// نموذج عنصر قالب الصلاحيات
class PermissionTemplateItem {
  final int id;
  final int templateId;
  final int permissionId;
  final bool isEnabled;
  final Permission? permission;

  PermissionTemplateItem({
    required this.id,
    required this.templateId,
    required this.permissionId,
    required this.isEnabled,
    this.permission,
  });

  factory PermissionTemplateItem.fromJson(Map<String, dynamic> json) {
    return PermissionTemplateItem(
      id: json['id'] ?? 0,
      templateId: json['templateId'] ?? 0,
      permissionId: json['permissionId'] ?? 0,
      isEnabled: json['isEnabled'] ?? true,
      permission: json['permission'] != null
          ? Permission.fromJson(json['permission'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'templateId': templateId,
      'permissionId': permissionId,
      'isEnabled': isEnabled,
    };
  }
}

/// نموذج طلب إنشاء قالب صلاحيات
class CreatePermissionTemplateRequest {
  final String name;
  final String? description;
  final String type;
  final String? color;
  final String? icon;
  final List<int> permissionIds;
  final int createdBy;

  CreatePermissionTemplateRequest({
    required this.name,
    this.description,
    required this.type,
    this.color,
    this.icon,
    required this.permissionIds,
    required this.createdBy,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'type': type,
      'color': color,
      'icon': icon,
      'permissionIds': permissionIds,
      'createdBy': createdBy,
    };
  }
}

/// نموذج تحديث قالب الصلاحيات
class UpdatePermissionTemplateRequest {
  final String? name;
  final String? description;
  final String? color;
  final String? icon;
  final bool? isActive;
  final List<int>? permissionIds;
  final int updatedBy;

  UpdatePermissionTemplateRequest({
    this.name,
    this.description,
    this.color,
    this.icon,
    this.isActive,
    this.permissionIds,
    required this.updatedBy,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'updatedBy': updatedBy,
    };

    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (color != null) data['color'] = color;
    if (icon != null) data['icon'] = icon;
    if (isActive != null) data['isActive'] = isActive;
    if (permissionIds != null) data['permissionIds'] = permissionIds;

    return data;
  }
}

/// نموذج تطبيق قالب على مستخدم
class ApplyTemplateToUserRequest {
  final int templateId;
  final int userId;
  final bool replaceExisting;
  final int appliedBy;

  ApplyTemplateToUserRequest({
    required this.templateId,
    required this.userId,
    this.replaceExisting = true,
    required this.appliedBy,
  });

  Map<String, dynamic> toJson() {
    return {
      'templateId': templateId,
      'userId': userId,
      'replaceExisting': replaceExisting,
      'appliedBy': appliedBy,
    };
  }
}
