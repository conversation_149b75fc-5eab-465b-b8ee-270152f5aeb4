import 'package:flutter/material.dart';
import '../models/department_model.dart';
import '../services/api/departments_api_service.dart';

/// مستودع الأقسام
class DepartmentRepository {
  final DepartmentsApiService _apiService = DepartmentsApiService();

  /// الحصول على قسم بالمعرف
  Future<Department?> getDepartmentById(int departmentId) async {
    try {
      return await _apiService.getDepartmentById(departmentId);
    } catch (e) {
      debugPrint('خطأ في الحصول على القسم $departmentId: $e');
      return null;
    }
  }

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      return await _apiService.getAllDepartments();
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام: $e');
      return [];
    }
  }

  /// الحصول على الأقسام النشطة فقط
  Future<List<Department>> getActiveDepartments() async {
    try {
      final departments = await _apiService.getAllDepartments();
      return departments.where((dept) => dept.isActive).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام النشطة: $e');
      return [];
    }
  }

  /// إنشاء قسم جديد
  Future<Department?> createDepartment(Department department) async {
    try {
      return await _apiService.createDepartment(department);
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      return null;
    }
  }

  /// تحديث قسم
  Future<Department?> updateDepartment(Department department) async {
    try {
      return await _apiService.updateDepartment(department.id, department);
    } catch (e) {
      debugPrint('خطأ في تحديث القسم: $e');
      return null;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int departmentId) async {
    try {
      return await _apiService.deleteDepartment(departmentId);
    } catch (e) {
      debugPrint('خطأ في حذف القسم: $e');
      return false;
    }
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllDepartments();
      }
      return await _apiService.searchDepartments(query);
    } catch (e) {
      debugPrint('خطأ في البحث في الأقسام: $e');
      return [];
    }
  }

  /// الحصول على أقسام مدير معين
  Future<List<Department>> getDepartmentsByManager(int managerId) async {
    try {
      final departments = await getAllDepartments();
      return departments.where((dept) => dept.managerId == managerId).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على أقسام المدير: $e');
      return [];
    }
  }

  /// تعيين مدير لقسم
  Future<bool> assignManager(int departmentId, int managerId) async {
    try {
      final department = await getDepartmentById(departmentId);
      if (department == null) return false;
      
      final updatedDepartment = Department(
        id: department.id,
        name: department.name,
        description: department.description,
        managerId: managerId,
        isActive: department.isActive,
        createdAt: department.createdAt,
      );
      
      final result = await updateDepartment(updatedDepartment);
      return result != null;
    } catch (e) {
      debugPrint('خطأ في تعيين مدير القسم: $e');
      return false;
    }
  }

  /// إزالة مدير من قسم
  Future<bool> removeManager(int departmentId) async {
    try {
      final department = await getDepartmentById(departmentId);
      if (department == null) return false;
      
      final updatedDepartment = Department(
        id: department.id,
        name: department.name,
        description: department.description,
        managerId: null,
        isActive: department.isActive,
        createdAt: department.createdAt,
      );
      
      final result = await updateDepartment(updatedDepartment);
      return result != null;
    } catch (e) {
      debugPrint('خطأ في إزالة مدير القسم: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل قسم
  Future<bool> toggleDepartmentStatus(int departmentId, bool isActive) async {
    try {
      final department = await getDepartmentById(departmentId);
      if (department == null) return false;
      
      final updatedDepartment = Department(
        id: department.id,
        name: department.name,
        description: department.description,
        managerId: department.managerId,
        isActive: isActive,
        createdAt: department.createdAt,
      );
      
      final result = await updateDepartment(updatedDepartment);
      return result != null;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة القسم: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات القسم
  Future<DepartmentStats?> getDepartmentStats(int departmentId) async {
    try {
      // استخدام API للحصول على الإحصائيات إذا كان متوفراً
      // في الوقت الحالي نعيد إحصائيات افتراضية
      final department = await getDepartmentById(departmentId);
      if (department == null) return null;

      return DepartmentStats(
        departmentId: departmentId,
        departmentName: department.name,
        totalEmployees: 0,
        activeEmployees: 0,
        totalTasks: 0,
        completedTasks: 0,
        pendingTasks: 0,
        overdueTasks: 0,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات القسم: $e');
      return null;
    }
  }

  /// التحقق من وجود قسم بالاسم
  Future<bool> departmentExistsByName(String name, {int? excludeId}) async {
    try {
      if (name.trim().isEmpty) return false;

      final departments = await getAllDepartments();
      return departments.any((dept) =>
        dept.name.toLowerCase().trim() == name.toLowerCase().trim() &&
        (excludeId == null || dept.id != excludeId)
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود القسم: $e');
      return false;
    }
  }

  /// التحقق من صحة بيانات القسم
  String? validateDepartment(Department department) {
    if (department.name.trim().isEmpty) {
      return 'اسم القسم مطلوب';
    }

    if (department.name.trim().length < 2) {
      return 'اسم القسم يجب أن يكون أكثر من حرفين';
    }

    if (department.name.trim().length > 100) {
      return 'اسم القسم يجب أن يكون أقل من 100 حرف';
    }

    if (department.description != null && department.description!.length > 500) {
      return 'وصف القسم يجب أن يكون أقل من 500 حرف';
    }

    return null; // لا توجد أخطاء
  }

  /// إنشاء قسم جديد مع التحقق من صحة البيانات
  Future<Department?> createDepartmentWithValidation(Department department) async {
    try {
      // التحقق من صحة البيانات
      final validationError = validateDepartment(department);
      if (validationError != null) {
        debugPrint('خطأ في التحقق من صحة البيانات: $validationError');
        return null;
      }

      // التحقق من عدم وجود قسم بنفس الاسم
      final exists = await departmentExistsByName(department.name);
      if (exists) {
        debugPrint('يوجد قسم بنفس الاسم مسبقاً');
        return null;
      }

      return await createDepartment(department);
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم مع التحقق: $e');
      return null;
    }
  }

  /// تحديث قسم مع التحقق من صحة البيانات
  Future<Department?> updateDepartmentWithValidation(Department department) async {
    try {
      // التحقق من صحة البيانات
      final validationError = validateDepartment(department);
      if (validationError != null) {
        debugPrint('خطأ في التحقق من صحة البيانات: $validationError');
        return null;
      }

      // التحقق من عدم وجود قسم بنفس الاسم (باستثناء القسم الحالي)
      final exists = await departmentExistsByName(department.name, excludeId: department.id);
      if (exists) {
        debugPrint('يوجد قسم آخر بنفس الاسم');
        return null;
      }

      return await updateDepartment(department);
    } catch (e) {
      debugPrint('خطأ في تحديث القسم مع التحقق: $e');
      return null;
    }
  }

  /// الحصول على عدد الأقسام
  Future<int> getDepartmentsCount() async {
    try {
      final departments = await getAllDepartments();
      return departments.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الأقسام: $e');
      return 0;
    }
  }

  /// الحصول على عدد الأقسام النشطة
  Future<int> getActiveDepartmentsCount() async {
    try {
      final departments = await getActiveDepartments();
      return departments.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الأقسام النشطة: $e');
      return 0;
    }
  }
}
