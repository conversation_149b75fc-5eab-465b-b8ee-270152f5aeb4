/// تعدادات حالة وأولوية المهام
///
/// تحتوي على تعدادات الأولوية والحالة للمهام مع دعم التحويل إلى strings
library;

import 'package:flutter/material.dart';

/// أولوية المهمة
enum TaskPriority {
  low('low', 1, 'منخفضة', 'Low'),
  medium('medium', 2, 'متوسطة', 'Medium'),
  high('high', 3, 'عالية', 'High'),
  urgent('urgent', 4, 'عاجلة', 'Urgent');

  const TaskPriority(this.stringValue, this.level, this.displayNameAr, this.displayNameEn);

  final String stringValue; // القيمة المستخدمة مع API
  final int level; // المستوى الرقمي للترتيب
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الأولوية من المستوى
  static TaskPriority fromLevel(int level) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.level == level,
      orElse: () => TaskPriority.medium,
    );
  }

  /// الحصول على الأولوية من الاسم (enum name أو string value)
  static TaskPriority fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskPriority.values.firstWhere(
      (priority) =>
        priority.displayNameEn.toLowerCase() == lowerName ||
        priority.displayNameAr == name ||
        priority.name == lowerName ||
        priority.stringValue == lowerName,
      orElse: () => TaskPriority.medium,
    );
  }

  /// الحصول على الأولوية من القيمة النصية (للاستخدام مع API)
  static TaskPriority fromString(String value) {
    final lowerValue = value.toLowerCase();
    return TaskPriority.values.firstWhere(
      (priority) => priority.stringValue == lowerValue,
      orElse: () => TaskPriority.medium,
    );
  }

  /// تحويل إلى string للاستخدام مع API
  String toApiString() => stringValue;

  /// التحقق من صحة القيمة النصية
  static bool isValid(String value) {
    final lowerValue = value.toLowerCase();
    return TaskPriority.values.any((priority) => priority.stringValue == lowerValue);
  }

  /// الحصول على لون الأولوية
  Color get color {
    switch (this) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.urgent:
        return Colors.deepPurple;
    }
  }

  /// الحصول على أيقونة الأولوية
  IconData get icon {
    switch (this) {
      case TaskPriority.low:
        return Icons.keyboard_arrow_down;
      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.high:
        return Icons.keyboard_arrow_up;
      case TaskPriority.urgent:
        return Icons.priority_high;
    }
  }
}

/// حالة المهمة
enum TaskStatus {
  pending('pending', 1, 'قيد الانتظار', 'Pending'),
  inProgress('in_progress', 2, 'قيد التنفيذ', 'In Progress'),
  waitingForInfo('waiting_for_info', 3, 'في انتظار معلومات', 'Waiting for Info'),
  completed('completed', 4, 'مكتملة', 'Completed'),
  cancelled('cancelled', 5, 'ملغاة', 'Cancelled'),
  news('news', 6, 'جديدة', 'News');

  const TaskStatus(this.stringValue, this.id, this.displayNameAr, this.displayNameEn);

  final String stringValue; // القيمة المستخدمة مع API
  final int id; // المعرف الرقمي للترتيب
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الحالة من المعرف
  static TaskStatus fromId(int id) {
    return TaskStatus.values.firstWhere(
      (status) => status.id == id,
      orElse: () => TaskStatus.pending,
    );
  }

  /// الحصول على الحالة من الاسم (enum name أو string value)
  static TaskStatus fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskStatus.values.firstWhere(
      (status) =>
        status.displayNameEn.toLowerCase() == lowerName ||
        status.displayNameAr == name ||
        status.name == lowerName ||
        status.stringValue == lowerName,
      orElse: () => TaskStatus.pending,
    );
  }

  /// الحصول على الحالة من القيمة النصية (للاستخدام مع API)
  static TaskStatus fromString(String value) {
    final lowerValue = value.toLowerCase();
    return TaskStatus.values.firstWhere(
      (status) => status.stringValue == lowerValue,
      orElse: () => TaskStatus.pending,
    );
  }

  /// تحويل إلى string للاستخدام مع API
  String toApiString() => stringValue;

  /// التحقق من صحة القيمة النصية
  static bool isValid(String value) {
    final lowerValue = value.toLowerCase();
    return TaskStatus.values.any((status) => status.stringValue == lowerValue);
  }

  /// الحصول على أيقونة الحالة
  IconData get icon {
    switch (this) {
      case TaskStatus.pending:
        return Icons.hourglass_empty;
      case TaskStatus.inProgress:
        return Icons.autorenew;
      case TaskStatus.waitingForInfo:
        return Icons.info_outline;
      case TaskStatus.completed:
        return Icons.check_circle_outline;
      case TaskStatus.cancelled:
        return Icons.cancel_outlined;
      case TaskStatus.news:
        return Icons.fiber_new;
    }
  }

  /// الحصول على لون الحالة
  Color get color {
    switch (this) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.waitingForInfo:
        return Colors.amber;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.purple;
    }
  }
}
