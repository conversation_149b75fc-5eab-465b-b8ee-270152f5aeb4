// import 'package:flutter/foundation.dart';
// import 'package:flutter_application_2/services/storage_service.dart';
// import 'package:get/get.dart';
// import '../../models/user_model.dart';
// import '../../models/role_model.dart';
// import '../../models/permission_models.dart';
// import '../../models/system_models.dart';
// import '../../models/activity_log_models.dart';
// import '../../models/system_setting_models.dart';
// import '../../services/admin/unified_admin_api_service.dart';
// import '../../models/screen_model.dart';

// /// متحكم محسن للوحة التحكم الإدارية
// /// يوفر إدارة شاملة ومحسنة لجميع العمليات الإدارية
// class EnhancedAdminController extends GetxController {
//   static EnhancedAdminController get instance => Get.find<EnhancedAdminController>();
  
//   final UnifiedAdminApiService _apiService = Get.find<UnifiedAdminApiService>();

//   // ==================== حالة التحميل والأخطاء ====================
//   final RxBool _isLoading = false.obs;
//   final RxString _error = ''.obs;
//   final RxBool _hasError = false.obs;

//   bool get isLoading => _isLoading.value;
//   String get error => _error.value;
//   bool get hasError => _hasError.value;

//   // ==================== بيانات المستخدمين ====================
//   final RxList<User> _users = <User>[].obs;
//   final RxInt _currentUserPage = 1.obs;
//   final RxBool _hasMoreUsers = true.obs;
//   final RxString _userSearchQuery = ''.obs;

//   List<User> get users => _users;
//   int get currentUserPage => _currentUserPage.value;
//   bool get hasMoreUsers => _hasMoreUsers.value;
//   String get userSearchQuery => _userSearchQuery.value;

//   // ==================== بيانات الأدوار ====================
//   final RxList<Role> _roles = <Role>[].obs;
//   List<Role> get roles => _roles;

//   // ==================== بيانات الصلاحيات ====================
//   final RxList<Permission> _permissions = <Permission>[].obs;
//   final RxList<Permission> _filteredPermissions = <Permission>[].obs;
//   List<Permission> get permissions => _permissions;
//   List<Permission> get filteredPermissions => _filteredPermissions.isEmpty ? _permissions : _filteredPermissions;

//   // ==================== بيانات السجلات ====================
//   final RxList<SystemLog> _systemLogs = <SystemLog>[].obs;
//   final RxList<ActivityLog> _activityLogs = <ActivityLog>[].obs;

//   List<SystemLog> get systemLogs => _systemLogs;
//   List<ActivityLog> get activityLogs => _activityLogs;

//   // ==================== بيانات النسخ الاحتياطية ====================
//   final RxList<Backup> _backups = <Backup>[].obs;
//   List<Backup> get backups => _backups;

//   // ==================== بيانات إعدادات النظام ====================
//   final RxList<SystemSetting> _systemSettings = <SystemSetting>[].obs;
//   List<SystemSetting> get systemSettings => _systemSettings;

//   // ==================== الإحصائيات ====================
//   final RxMap<String, dynamic> _systemStatistics = <String, dynamic>{}.obs;
//   final RxMap<String, dynamic> _userStatistics = <String, dynamic>{}.obs;

//   Map<String, dynamic> get systemStatistics => _systemStatistics;
//   Map<String, dynamic> get userStatistics => _userStatistics;

//   // ==================== التهيئة ====================
//   @override
//   void onInit() {
//     super.onInit();
//     debugPrint('تم تهيئة EnhancedAdminController');
//     _initializeData();
//   }

//   /// تهيئة البيانات الأساسية
//   Future<void> _initializeData() async {
//     try {
//       _setLoading(true);
      
//       // التحقق من وجود توكن المصادقة
//       final storageService = Get.find<StorageService>();
//       final token = await storageService.getToken();
      
//       if (token == null || token.isEmpty) {
//         _setError('لم يتم العثور على رمز المصادقة، يرجى تسجيل الدخول');
//         Get.offAllNamed('/login');
//         return;
//       }
      
//       await Future.wait([
//         loadUsers(),
//         loadRoles(),
//         loadPermissions(),
//         loadSystemStatistics(),
//       ]);
//       _clearError();
//     } catch (e) {
//       String errorMessage = 'فشل في تحميل البيانات الأساسية';
//       if (e.toString().contains('401')) {
//         errorMessage = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
//       }
//       _setError(errorMessage);
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== إدارة الحالة ====================
//   void _setLoading(bool loading) {
//     _isLoading.value = loading;
//   }

//   void _setError(String errorMessage) {
//     _error.value = errorMessage;
//     _hasError.value = errorMessage.isNotEmpty;
//     if (errorMessage.isNotEmpty) {
//       debugPrint('خطأ في AdminController: $errorMessage');
//     }
//   }

//   void _clearError() {
//     _error.value = '';
//     _hasError.value = false;
//   }

//   /// مسح الأخطاء (دالة عامة)
//   void clearError() {
//     _clearError();
//   }

//   /// إنشاء دور جديد ديناميكياً
//   Future<bool> createDynamicRole({
//     required String name,
//     String? description,
//     List<int>? permissionIds,
//   }) async {
//     try {
//       _setLoading(true);
      
//       // إنشاء دور جديد
//       final newRole = Role(
//         id: DateTime.now().millisecondsSinceEpoch, // معرف مؤقت
//         name: name,
//         description: description,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       );
      
//       // إضافة الدور للقائمة
//       _roles.add(newRole);
      
//       _clearError();
//       debugPrint('تم إنشاء دور جديد: $name');
//       return true;
//     } catch (e) {
//       _setError('فشل في إنشاء الدور: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// الحصول على جميع الأدوار المتاحة
//   List<Role> getAvailableRoles() {
//     return _roles.toList();
//   }

//   /// البحث عن دور بالمعرف
//   Role? getRoleById(int id) {
//     try {
//       return _roles.firstWhere((role) => role.id == id);
//     } catch (e) {
//       return null;
//     }
//   }

//   /// البحث عن دور بالاسم
//   Role? getRoleByName(String name) {
//     try {
//       return _roles.firstWhere((role) => role.name.toLowerCase() == name.toLowerCase());
//     } catch (e) {
//       return null;
//     }
//   }


//       Role(id: 5, name: 'مستخدم', description: 'صلاحيات أساسية', createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000),
//     ];

//     // حفظ الأدوار
//     _roles.assignAll(mockRoles);

//     // إنشاء مستخدمين وهميين
//     _users.assignAll([
//       User(
//         id: 1,
//         name: 'أحمد محمد',
//         email: '<EMAIL>',
//         role: mockRoles[0], // مدير النظام
//         isActive: true,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//       User(
//         id: 2,
//         name: 'فاطمة علي',
//         email: '<EMAIL>',
//         role: mockRoles[4], // مستخدم
//         isActive: true,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//       User(
//         id: 3,
//         name: 'محمد حسن',
//         email: '<EMAIL>',
//         role: mockRoles[2], // مدير قسم
//         isActive: false,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//       User(
//         id: 4,
//         name: 'سارة أحمد',
//         email: '<EMAIL>',
//         role: mockRoles[3], // مشرف
//         isActive: true,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//       User(
//         id: 5,
//         name: 'عبدالله خالد',
//         email: '<EMAIL>',
//         role: mockRoles[1], // مدير عام
//         isActive: true,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//       User(
//         id: 6,
//         name: 'نورا سالم',
//         email: '<EMAIL>',
//         role: mockRoles[4], // مستخدم
//         isActive: true,
//         createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       ),
//     ]);
    
//     _clearError();
//     debugPrint('تم تحميل البيانات الوهمية للاختبار (${_users.length} مستخدم، ${_roles.length} دور)');
//   }

//   // ==================== إدارة المستخدمين ====================

//   /// تحميل المستخدمين
//   Future<void> loadUsers({bool refresh = false}) async {
//     try {
//       if (refresh) {
//         _currentUserPage.value = 1;
//         _users.clear();
//         _hasMoreUsers.value = true;
//       }

//       if (!_hasMoreUsers.value) return;

//       _setLoading(true);
//       final newUsers = await _apiService.getAllUsers(
//         page: _currentUserPage.value,
//         search: _userSearchQuery.value.isEmpty ? null : _userSearchQuery.value,
//       );

//       if (newUsers.isEmpty) {
//         _hasMoreUsers.value = false;
//       } else {
//         if (refresh) {
//           _users.assignAll(newUsers);
//         } else {
//           _users.addAll(newUsers);
//         }
//         _currentUserPage.value++;
//       }

//       _clearError();
//     } catch (e) {
//       debugPrint('خطأ أثناء تحميل المستخدمين: ' + e.toString());
      
//       String errorMessage = 'فشل في تحميل المستخدمين';
//       if (e.toString().contains('401')) {
//         errorMessage = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
//       } else if (e.toString().contains('500')) {
//         errorMessage = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
//       } else if (e.toString().contains('network') || 
//                  e.toString().contains('connection') ||
//                  e.toString().contains('SocketException') ||
//                  e.toString().contains('statusCode: 0')) {
//         errorMessage = 'خطأ في الاتصال، تحقق من الإنترنت';
//       } else if (e.toString().contains('403')) {
//         errorMessage = 'ليس لديك صلاحية للوصول لهذه البيانات';
//       } else if (e.toString().contains('404')) {
//         errorMessage = 'الخدمة غير متاحة حالياً';
//       }
      
//       _setError(errorMessage);
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// البحث في المستخدمين
//   Future<void> searchUsers(String query) async {
//     _userSearchQuery.value = query;
//     await loadUsers(refresh: true);
//   }

//   /// إنشاء مستخدم جديد
//   Future<bool> createUser(User user) async {
//     try {
//       _setLoading(true);
//       final newUser = await _apiService.createUser(user);
//       _users.insert(0, newUser);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم إنشاء المستخدم بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في إنشاء المستخدم: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحديث مستخدم
//   Future<bool> updateUser(User user) async {
//     try {
//       _setLoading(true);
//       final updatedUser = await _apiService.updateUser(user);
      
//       final index = _users.indexWhere((u) => u.id == user.id);
//       if (index != -1) {
//         _users[index] = updatedUser;
//       }
      
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث المستخدم بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث المستخدم: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// حذف مستخدم
//   Future<bool> deleteUser(int userId) async {
//     try {
//       _setLoading(true);
//       await _apiService.deleteUser(userId);
//       _users.removeWhere((u) => u.id == userId);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم حذف المستخدم بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في حذف المستخدم: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تبديل حالة نشاط المستخدم
//   Future<bool> toggleUserActiveStatus(int userId, bool isActive) async {
//     try {
//       _setLoading(true);
//       await _apiService.updateUserActiveStatus(userId, isActive);
      
//       final index = _users.indexWhere((u) => u.id == userId);
//       if (index != -1) {
//         _users[index] = _users[index].copyWith(isActive: isActive);
//       }
      
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث حالة المستخدم بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث حالة المستخدم: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== إدارة الأدوار ====================

//   /// تحميل الأدوار
//   Future<void> loadRoles() async {
//     try {
//       _setLoading(true);
//       final roles = await _apiService.getAllRoles();
//       _roles.assignAll(roles);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل الأدوار: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// إنشاء دور جديد
//   Future<bool> createRole(Role role) async {
//     try {
//       _setLoading(true);
//       final newRole = await _apiService.createRole(role);
//       _roles.add(newRole);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم إنشاء الدور بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في إنشاء الدور: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحديث دور
//   Future<bool> updateRole(Role role) async {
//     try {
//       _setLoading(true);
//       final updatedRole = await _apiService.updateRole(role);
      
//       final index = _roles.indexWhere((r) => r.id == role.id);
//       if (index != -1) {
//         _roles[index] = updatedRole;
//       }
      
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث الدور بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث الدور: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// حذف دور
//   Future<bool> deleteRole(int roleId) async {
//     try {
//       _setLoading(true);
//       await _apiService.deleteRole(roleId);
//       _roles.removeWhere((r) => r.id == roleId);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم حذف الدور بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في حذف الدور: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== إدارة الصلاحيات ====================

//   /// تحميل الصلاحيات
//   Future<void> loadPermissions() async {
//     try {
//       _setLoading(true);
//       final permissions = await _apiService.getAllPermissions();
//       _permissions.assignAll(permissions);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل الصلاحيات: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحديث صلاحيات المستخدم
//   Future<bool> updateUserPermissions(int userId, List<int> permissionIds) async {
//     try {
//       _setLoading(true);
//       await _apiService.updateUserPermissions(userId, permissionIds);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث صلاحيات المستخدم بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث صلاحيات المستخدم: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحديث صلاحيات الدور
//   Future<bool> updateRolePermissions(int roleId, List<int> permissionIds) async {
//     try {
//       _setLoading(true);
//       await _apiService.updateRolePermissions(roleId, permissionIds);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث صلاحيات الدور بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث صلاحيات الدور: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== السجلات ====================

//   /// تحميل سجلات النظام
//   Future<void> loadSystemLogs({
//     String? logLevel,
//     String? logType,
//     DateTime? startDate,
//     DateTime? endDate,
//   }) async {
//     try {
//       _setLoading(true);
//       final logs = await _apiService.getSystemLogs(
//         logLevel: logLevel,
//         logType: logType,
//         startDate: startDate,
//         endDate: endDate,
//       );
//       _systemLogs.assignAll(logs);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل سجلات النظام: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحميل سجلات النشاط
//   Future<void> loadActivityLogs({
//     int? userId,
//     String? action,
//     DateTime? startDate,
//     DateTime? endDate,
//   }) async {
//     try {
//       _setLoading(true);
//       final logs = await _apiService.getActivityLogs(
//         userId: userId,
//         action: action,
//         startDate: startDate,
//         endDate: endDate,
//       );
//       _activityLogs.assignAll(logs);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل سجلات النشاط: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== النسخ الاحتياطية ====================

//   /// تحميل النسخ الاحتياطية
//   Future<void> loadBackups() async {
//     try {
//       _setLoading(true);
//       final backups = await _apiService.getBackups();
//       _backups.assignAll(backups);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل النسخ الاحتياطية: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// إنشاء نسخة احتياطية
//   Future<bool> createBackup(String description) async {
//     try {
//       _setLoading(true);
//       final backup = await _apiService.createBackup(description);
//       _backups.insert(0, backup);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم إنشاء النسخة الاحتياطية بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في إنشاء النسخة الاحتياطية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// استعادة نسخة احتياطية
//   Future<bool> restoreBackup(int backupId) async {
//     try {
//       _setLoading(true);
//       await _apiService.restoreBackup(backupId);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم استعادة النسخة الاحتياطية بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في استعادة النسخة الاحتياطية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// حذف نسخة احتياطية
//   Future<bool> deleteBackup(int backupId) async {
//     try {
//       _setLoading(true);
//       await _apiService.deleteBackup(backupId);
//       _backups.removeWhere((b) => b.id == backupId);
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم حذف النسخة الاحتياطية بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في حذف النسخة الاحتياطية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== إعدادات النظام ====================

//   /// تحميل إعدادات النظام
//   Future<void> loadSystemSettings() async {
//     try {
//       _setLoading(true);
//       final settings = await _apiService.getSystemSettings();
//       _systemSettings.assignAll(settings);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل إعدادات النظام: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحديث إعداد النظام
//   Future<bool> updateSystemSetting(String key, String value) async {
//     try {
//       _setLoading(true);
//       await _apiService.updateSystemSetting(key, value);
      
//       final index = _systemSettings.indexWhere((s) => s.settingKey == key);
//       if (index != -1) {
//         _systemSettings[index] = _systemSettings[index].copyWith(settingValue: value);
//       }
      
//       _clearError();
      
//       Get.snackbar(
//         'تم بنجاح',
//         'تم تحديث الإعداد بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
      
//       return true;
//     } catch (e) {
//       _setError('فشل في تحديث الإعداد: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== الإحصائيات ====================

//   /// تحميل إحصائيات النظام
//   Future<void> loadSystemStatistics() async {
//     try {
//       _setLoading(true);
//       final stats = await _apiService.getSystemStatistics();
//       _systemStatistics.assignAll(stats);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل إحصائيات النظام: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تحميل إحصائيات المستخدمين
//   Future<void> loadUserStatistics() async {
//     try {
//       _setLoading(true);
//       final stats = await _apiService.getUserStatistics();
//       _userStatistics.assignAll(stats);
//       _clearError();
//     } catch (e) {
//       _setError('فشل في تحميل إحصائيات المستخدمين: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // ==================== تحديث شامل ====================

//   /// تحديث جميع البيانات
//   Future<void> refreshAllData() async {
//     try {
//       _setLoading(true);
//       await Future.wait([
//         loadUsers(refresh: true),
//         loadRoles(),
//         loadPermissions(),
//         loadSystemStatistics(),
//         loadUserStatistics(),
//       ]);
//       _clearError();
      
//       Get.snackbar(
//         'تم التحديث',
//         'تم تحديث جميع البيانات بنجاح',
//         snackPosition: SnackPosition.BOTTOM,
//       );
//     } catch (e) {
//       _setError('فشل في تحديث البيانات: $e');
//     } finally {
//       _setLoading(false);
//     }
//   }

//   // فلترة الصلاحيات حسب نص البحث
//   void filterPermissions(String query) {
//     if (query.isEmpty) {
//       _filteredPermissions.clear();
//     } else {
//       _filteredPermissions.assignAll(
//         _permissions.where((perm) =>
//           perm.name.toLowerCase().contains(query.toLowerCase()) ||
//           (perm.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
//         ),
//       );
//     }
//   }

//   // ==================== إدارة الشاشات والصلاحيات (جديد) ====================

//   /// جلب جميع الشاشات
//   Future<List<Screen>> getScreens() async {
//     try {
//       _setLoading(true);
//       final screens = await _apiService.getAllScreens();
//       return screens;
//     } catch (e) {
//       _setError('فشل في جلب الشاشات: $e');
//       return [];
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// إنشاء صلاحية جديدة
//   Future<bool> createPermission({
//     required String name,
//     String? description,
//     required int? screenId,
//   }) async {
//     try {
//       _setLoading(true);
//       await _apiService.createPermission(name: name, description: description, screenId: screenId);
//       _clearError();
//       return true;
//     } catch (e) {
//       _setError('فشل في إضافة الصلاحية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// تعديل صلاحية
//   Future<bool> updatePermission({
//     required int id,
//     required String name,
//     String? description,
//     required int? screenId,
//   }) async {
//     try {
//       _setLoading(true);
//       await _apiService.updatePermission(id: id, name: name, description: description, screenId: screenId);
//       _clearError();
//       return true;
//     } catch (e) {
//       _setError('فشل في تعديل الصلاحية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }

//   /// حذف صلاحية
//   Future<bool> deletePermission(int id) async {
//     try {
//       _setLoading(true);
//       await _apiService.deletePermission(id);
//       _clearError();
//       return true;
//     } catch (e) {
//       _setError('فشل في حذف الصلاحية: $e');
//       return false;
//     } finally {
//       _setLoading(false);
//     }
//   }
// }