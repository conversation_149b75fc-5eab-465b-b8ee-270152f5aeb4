import 'package:flutter_application_2/models/task_model.dart';

class TaskCompletionReport {
  final int totalTasks;
  final int completedTasks;
  final double completionRate;
  final double averageCompletionTime;

  TaskCompletionReport({
    required this.totalTasks,
    required this.completedTasks,
    required this.completionRate,
    required this.averageCompletionTime,
  });

  factory TaskCompletionReport.fromTasks(List<Task> tasks) {
    final completedTasks = tasks.where((task) => task.status == 'completed').length;
    final totalTasks = tasks.length;
    final completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0;

    // حساب متوسط وقت الإنجاز (مثال بسيط، يحتاج إلى تحسين)
    double totalCompletionTime = 0;
    int tasksWithCompletionTime = 0;

    for (var task in tasks) {
      if (task.status == 'completed' && task.startDate != null && task.dueDateDateTime != null) {
        final startTime = DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000);
        final endTime = task.dueDateDateTime!;
        totalCompletionTime += endTime.difference(startTime).inHours;
        tasksWithCompletionTime++;
      }
    }

    final averageCompletionTime = tasksWithCompletionTime > 0
        ? totalCompletionTime / tasksWithCompletionTime
        : 0.0;

    return TaskCompletionReport(
      totalTasks: totalTasks,
      completedTasks: completedTasks,
      completionRate: completionRate,
      averageCompletionTime: averageCompletionTime,
    );
  }
}