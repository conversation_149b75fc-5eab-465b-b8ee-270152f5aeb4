import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../models/report_criteria_model.dart';
import '../models/reporting/report_result_model.dart';
import 'api/reports_api_service.dart';

/// خدمة التقارير
class ReportService {
  final ReportsApiService _apiService = ReportsApiService();

  /// الحصول على جميع التقارير
  Future<List<Report>> getAllReports() async {
    try {
      return await _apiService.getAllReports();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      rethrow;
    }
  }

  /// إنشاء تقرير جديد
  Future<Report> createReport({
    required String title,
    String? description,
    required ReportType type,
    required int createdById,
    required ReportCriteria criteria,
  }) async {
    try {
      final report = Report(
        id: 0, // سيتم تعيينه من الخادم
        title: title,
        description: description,
        reportType: type,
        query: _generateQueryFromCriteria(type, criteria),
        parameters: _serializeCriteria(criteria),
        createdBy: createdById,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isPublic: false,
        isDeleted: false,
      );
      return await _apiService.createReport(report);
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  /// تنفيذ تقرير
  Future<ReportResult> executeReport(int reportId) async {
    try {
      final result = await _apiService.runReport(reportId, {});
      return ReportResult.fromJson(result);
    } catch (e) {
      debugPrint('خطأ في تنفيذ التقرير: $e');
      return ReportResult.failure(
        generatedAt: DateTime.now(),
        errorMessages: [e.toString()],
      );
    }
  }

  /// تصدير تقرير إلى PDF
  Future<String?> exportReportToPdf(int reportId) async {
    try {
      final result = await _apiService.exportReport(reportId, 'pdf', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى PDF: $e');
      return null;
    }
  }

  /// تصدير تقرير إلى Excel
  Future<String?> exportReportToExcel(int reportId) async {
    try {
      final result = await _apiService.exportReport(reportId, 'excel', null);
      return result['filePath'] as String?;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى Excel: $e');
      return null;
    }
  }

  /// الحصول على تقارير تتبع الوقت المتقدمة (حالياً ترجع تقارير timeTracking فقط)
  Future<List<Report>> getAdvancedTimeTrackingReports() async {
    try {
      final allReports = await _apiService.getAllReports();
      return allReports.where((report) => report.reportType == ReportType.timeTracking).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير تتبع الوقت المتقدمة: $e');
      rethrow;
    }
  }

  /// الحصول على التقارير حسب المنشئ
  Future<List<Report>> getReportsByCreator(int creatorId) async {
    try {
      final allReports = await _apiService.getAllReports();
      return allReports.where((report) => report.createdBy == creatorId).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المنشئ: $e');
      rethrow;
    }
  }

  /// توليد استعلام من المعايير
  String _generateQueryFromCriteria(ReportType type, ReportCriteria criteria) {
    // هذا مثال بسيط - يجب تطويره حسب نوع التقرير
    switch (type) {
      case ReportType.taskSummary:
        return 'SELECT * FROM Tasks WHERE 1=1';
      case ReportType.taskStatus:
        return 'SELECT * FROM Tasks WHERE 1=1';
      case ReportType.taskProgress:
        return 'SELECT * FROM Tasks WHERE 1=1';
      case ReportType.taskDetails:
        return 'SELECT * FROM Tasks WHERE 1=1';
      case ReportType.taskCompletion:
        return 'SELECT * FROM Tasks WHERE 1=1';
      case ReportType.userActivity:
        return 'SELECT * FROM UserActivities WHERE 1=1';
      case ReportType.userPerformance:
        return 'SELECT * FROM Users WHERE 1=1';
      case ReportType.departmentPerformance:
        return 'SELECT * FROM Departments WHERE 1=1';
      case ReportType.departmentWorkload:
        return 'SELECT * FROM Departments WHERE 1=1';
      case ReportType.timeTracking:
        return 'SELECT * FROM TimeEntries WHERE 1=1';
      case ReportType.projectProgress:
        return 'SELECT * FROM Projects WHERE 1=1';
      case ReportType.projectStatus:
        return 'SELECT * FROM Projects WHERE 1=1';
      case ReportType.systemUsage:
        return 'SELECT * FROM SystemLogs WHERE 1=1';
      case ReportType.custom:
        return 'SELECT 1';
      default:
        return 'SELECT 1';
    }
  }

  /// تسلسل المعايير
  String? _serializeCriteria(ReportCriteria criteria) {
    try {
      return criteria.toJson().toString();
    } catch (e) {
      debugPrint('خطأ في تسلسل المعايير: $e');
      return null;
    }
  }
}
