import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// قائمة منسدلة مخصصة
///
/// توفر واجهة موحدة للقوائم المنسدلة في التطبيق
class CustomDropdown<T> extends StatelessWidget {
  final String label;
  final T value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?) onChanged;
  final String? hint;
  final bool isExpanded;
  final bool isDense;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Color? iconColor;
  final Color? dropdownColor;
  final Color? textColor;
  final Color? fillColor;
  final bool filled;
  final InputBorder? border;
  final InputBorder? focusedBorder;
  final InputBorder? enabledBorder;
  final double? borderRadius;
  final bool showLabel;

  const CustomDropdown({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    required this.onChanged,
    this.hint,
    this.isExpanded = true,
    this.isDense = true,
    this.contentPadding,
    this.icon,
    this.iconColor,
    this.dropdownColor,
    this.textColor,
    this.fillColor,
    this.filled = true,
    this.border,
    this.focusedBorder,
    this.enabledBorder,
    this.borderRadius = 8.0,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel) ...[
          Text(
            label,
            style: AppStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: textColor ?? Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
        ],
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          hint: hint != null
              ? Text(
                  hint!,
                  style: AppStyles.bodyMedium.copyWith(
                    color: Colors.grey.shade600,
                  ),
                )
              : null,
          isExpanded: isExpanded,
          isDense: isDense,
          icon: icon ?? const Icon(Icons.arrow_drop_down),
          iconEnabledColor: iconColor ?? AppColors.primary,
          dropdownColor: dropdownColor ?? Colors.white,
          style: AppStyles.bodyMedium.copyWith(
            color: textColor ?? Colors.black87,
          ),
          decoration: InputDecoration(
            contentPadding: contentPadding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: filled,
            fillColor: fillColor ?? Colors.grey.shade100,
            border: border ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius!),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
            focusedBorder: focusedBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius!),
                  borderSide: BorderSide(color: AppColors.primary, width: 2),
                ),
            enabledBorder: enabledBorder ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius!),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
          ),
        ),
      ],
    );
  }
}
