import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';
import 'package:flutter_application_2/screens/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';

import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';

/// نموذج مؤقت لإصدار الوثيقة
class ArchiveDocumentVersion {
  final String id;
  final String documentId;
  final int versionNumber;
  final String fileName;
  final String fileType;
  final int fileSize;
  final String? changeNotes;
  final String creatorId;
  final DateTime createdAt;

  ArchiveDocumentVersion({
    required this.id,
    required this.documentId,
    required this.versionNumber,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    this.changeNotes,
    required this.creatorId,
    required this.createdAt,
  });
}

/// شاشة تاريخ إصدارات الوثيقة
class DocumentVersionHistoryScreen extends StatefulWidget {
  /// معرف الوثيقة
  final String documentId;

  /// عنوان الوثيقة
  final String documentTitle;

  const DocumentVersionHistoryScreen({
    super.key,
    required this.documentId,
    required this.documentTitle,
  });

  @override
  State<DocumentVersionHistoryScreen> createState() => _DocumentVersionHistoryScreenState();
}

class _DocumentVersionHistoryScreenState extends State<DocumentVersionHistoryScreen> {
  final AuthController _authController = Get.find<AuthController>();

  bool _isLoading = false;
  List<ArchiveDocumentVersion> _documentVersions = [];

  @override
  void initState() {
    super.initState();
    _loadVersions();
  }

  /// تحميل إصدارات الوثيقة
  Future<void> _loadVersions() async {
    setState(() {
      _isLoading = true;
    });

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
      // بيانات تجريبية
      _documentVersions = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'تاريخ إصدارات الوثيقة: ${widget.documentTitle}',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadVersions,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _documentVersions.isEmpty
              ? _buildEmptyState()
              : _buildVersionsList(isSmallScreen),
    );
  }

  /// بناء حالة عدم وجود إصدارات
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.history,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إصدارات سابقة لهذه الوثيقة',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إنشاء إصدار جديد في كل مرة يتم فيها تعديل الوثيقة',
            style: AppStyles.body2,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الإصدارات
  Widget _buildVersionsList(bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إصدارات الوثيقة',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: isSmallScreen
                ? _buildMobileVersionsList()
                : _buildDesktopVersionsList(),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الإصدارات للجوال
  Widget _buildMobileVersionsList() {
    return ListView.separated(
      itemCount: _documentVersions.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final version = _documentVersions[index];
        return _buildVersionListItem(version);
      },
    );
  }

  /// بناء قائمة الإصدارات للحاسوب
  Widget _buildDesktopVersionsList() {
    return ListView.separated(
      itemCount: _documentVersions.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final version = _documentVersions[index];
        return _buildVersionListItem(version);
      },
    );
  }

  /// بناء عنصر إصدار
  Widget _buildVersionListItem(ArchiveDocumentVersion version) {
    final isCurrentUser = version.creatorId == _authController.currentUser.value?.id.toString();
    final creatorName = isCurrentUser ? 'أنت' : 'مستخدم آخر'; // يمكن استبدالها بالحصول على اسم المستخدم من قاعدة البيانات

    return Card(
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Row(
          children: [
            Text(
              'الإصدار ${version.versionNumber}',
              style: AppStyles.subtitle1.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Chip(
              label: Text(
                DateFormatter.formatDateTime(version.createdAt),
                style: AppStyles.caption.copyWith(
                  color: Colors.white,
                ),
              ),
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Text(
              'بواسطة: $creatorName',
              style: AppStyles.body2,
            ),
            if (version.changeNotes != null && version.changeNotes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'ملاحظات التغيير: ${version.changeNotes}',
                style: AppStyles.body2,
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _getFileIcon(version.fileType),
                  size: 16,
                  color: _getFileColor(version.fileType),
                ),
                const SizedBox(width: 8),
                Text(
                  version.fileName,
                  style: AppStyles.body2,
                ),
                const SizedBox(width: 16),
                Text(
                  FileProcessor.formatFileSize(version.fileSize),
                  style: AppStyles.caption,
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleVersionAction(value, version),
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'download',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تنزيل'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'restore',
              child: Row(
                children: [
                  Icon(Icons.restore),
                  SizedBox(width: 8),
                  Text('استعادة كإصدار حالي'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _handleVersionAction('view', version),
      ),
    );
  }

  /// التعامل مع إجراءات الإصدار
  void _handleVersionAction(String action, ArchiveDocumentVersion version) {
    switch (action) {
      case 'view':
        _viewVersion(version);
        break;
      case 'download':
        _downloadVersion(version);
        break;
      case 'restore':
        _restoreVersion(version);
        break;
      case 'delete':
        _deleteVersion(version);
        break;
    }
  }

  /// عرض الإصدار
  void _viewVersion(ArchiveDocumentVersion version) {
    // تنفيذ عرض الإصدار
    Get.snackbar(
      'عرض الإصدار',
      'جاري فتح الإصدار ${version.versionNumber}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// تنزيل الإصدار
  void _downloadVersion(ArchiveDocumentVersion version) {
    // تنفيذ تنزيل الإصدار
    Get.snackbar(
      'تنزيل الإصدار',
      'جاري تنزيل الإصدار ${version.versionNumber}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// استعادة الإصدار
  void _restoreVersion(ArchiveDocumentVersion version) {
    // تنفيذ استعادة الإصدار
    Get.dialog(
      AlertDialog(
        title: const Text('استعادة الإصدار'),
        content: Text('هل أنت متأكد من استعادة الإصدار ${version.versionNumber} كإصدار حالي للوثيقة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // تنفيذ استعادة الإصدار
              Get.snackbar(
                'استعادة الإصدار',
                'تم استعادة الإصدار ${version.versionNumber} بنجاح',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  /// حذف الإصدار
  void _deleteVersion(ArchiveDocumentVersion version) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الإصدار'),
        content: Text('هل أنت متأكد من حذف الإصدار ${version.versionNumber}؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Get.back();
              // محاكاة حذف الإصدار
              setState(() {
                _documentVersions.removeWhere((v) => v.id == version.id);
              });
              Get.snackbar(
                'حذف الإصدار',
                'تم حذف الإصدار ${version.versionNumber} بنجاح',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الملف
  IconData _getFileIcon(String fileType) {
    return FileProcessor.getFileIcon(fileType);
  }

  /// الحصول على لون الملف
  Color _getFileColor(String fileType) {
    return FileProcessor.getFileColor(fileType);
  }
}
