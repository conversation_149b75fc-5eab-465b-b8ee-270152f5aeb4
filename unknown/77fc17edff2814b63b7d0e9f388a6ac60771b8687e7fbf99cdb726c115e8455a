import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// عنصر التخزين المؤقت
class CacheItem<T> {
  final T data;
  final DateTime createdAt;
  final Duration? ttl; // Time To Live
  final String key;

  CacheItem({
    required this.data,
    required this.createdAt,
    required this.key,
    this.ttl,
  });

  /// التحقق من انتهاء صلاحية العنصر
  bool get isExpired {
    if (ttl == null) return false;
    return DateTime.now().difference(createdAt) > ttl!;
  }

  /// الوقت المتبقي قبل انتهاء الصلاحية
  Duration? get timeToExpire {
    if (ttl == null) return null;
    final elapsed = DateTime.now().difference(createdAt);
    final remaining = ttl! - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'ttl': ttl?.inMilliseconds,
      'key': key,
    };
  }

  /// إنشاء من JSON
  factory CacheItem.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJsonData) {
    return CacheItem<T>(
      data: fromJsonData(json['data']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      ttl: json['ttl'] != null ? Duration(milliseconds: json['ttl']) : null,
      key: json['key'],
    );
  }
}

/// خدمة التخزين المؤقت
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  // التخزين المؤقت في الذاكرة
  final Map<String, CacheItem> _memoryCache = {};
  
  // التخزين المؤقت المستمر
  SharedPreferences? _prefs;
  
  // مؤقت للتنظيف التلقائي
  Timer? _cleanupTimer;
  
  // إعدادات افتراضية
  static const Duration _defaultTTL = Duration(minutes: 15);
  static const Duration _cleanupInterval = Duration(minutes: 5);
  static const int _maxMemoryCacheSize = 100;
  static const String _persistentCachePrefix = 'cache_';

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _startCleanupTimer();
      await _loadPersistentCache();
      debugPrint('✅ تم تهيئة خدمة التخزين المؤقت');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة التخزين المؤقت: $e');
    }
  }

  /// بدء مؤقت التنظيف التلقائي
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) {
      _cleanupExpiredItems();
    });
  }

  /// تحميل التخزين المؤقت المستمر
  Future<void> _loadPersistentCache() async {
    if (_prefs == null) return;

    try {
      final keys = _prefs!.getKeys().where((key) => key.startsWith(_persistentCachePrefix));
      
      for (final key in keys) {
        final jsonString = _prefs!.getString(key);
        if (jsonString != null) {
          try {
            final json = jsonDecode(jsonString) as Map<String, dynamic>;
            final cacheKey = key.substring(_persistentCachePrefix.length);
            
            // إنشاء CacheItem مع البيانات الخام (سيتم تحويلها عند الاستخدام)
            final item = CacheItem<dynamic>(
              data: json['data'],
              createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
              ttl: json['ttl'] != null ? Duration(milliseconds: json['ttl']) : null,
              key: cacheKey,
            );

            if (!item.isExpired) {
              _memoryCache[cacheKey] = item;
            } else {
              // حذف العناصر المنتهية الصلاحية من التخزين المستمر
              await _prefs!.remove(key);
            }
          } catch (e) {
            debugPrint('خطأ في تحميل عنصر التخزين المؤقت $key: $e');
            await _prefs!.remove(key);
          }
        }
      }
      
      debugPrint('تم تحميل ${_memoryCache.length} عنصر من التخزين المؤقت المستمر');
    } catch (e) {
      debugPrint('خطأ في تحميل التخزين المؤقت المستمر: $e');
    }
  }

  /// حفظ عنصر في التخزين المؤقت
  Future<void> set<T>(
    String key,
    T data, {
    Duration? ttl,
    bool persistent = false,
  }) async {
    try {
      final item = CacheItem<T>(
        data: data,
        createdAt: DateTime.now(),
        ttl: ttl ?? _defaultTTL,
        key: key,
      );

      // حفظ في الذاكرة
      _memoryCache[key] = item;

      // حفظ في التخزين المستمر إذا كان مطلوباً
      if (persistent && _prefs != null) {
        await _saveToPersistentCache(key, item);
      }

      // تنظيف الذاكرة إذا تجاوزت الحد الأقصى
      _enforceMemoryLimit();

      debugPrint('💾 تم حفظ $key في التخزين المؤقت');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ $key في التخزين المؤقت: $e');
    }
  }

  /// الحصول على عنصر من التخزين المؤقت
  T? get<T>(String key, {T Function(dynamic)? fromJson}) {
    try {
      final item = _memoryCache[key];
      if (item == null) return null;

      // التحقق من انتهاء الصلاحية
      if (item.isExpired) {
        _memoryCache.remove(key);
        _removeFromPersistentCache(key);
        return null;
      }

      // تحويل البيانات إذا كان مطلوباً
      if (fromJson != null && item.data is! T) {
        try {
          return fromJson(item.data);
        } catch (e) {
          debugPrint('خطأ في تحويل البيانات للمفتاح $key: $e');
          return null;
        }
      }

      return item.data as T?;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على $key من التخزين المؤقت: $e');
      return null;
    }
  }

  /// الحصول على عنصر أو تنفيذ دالة لإنشائه
  Future<T?> getOrSet<T>(
    String key,
    Future<T> Function() factory, {
    Duration? ttl,
    bool persistent = false,
    T Function(dynamic)? fromJson,
  }) async {
    // محاولة الحصول على العنصر من التخزين المؤقت
    final cached = get<T>(key, fromJson: fromJson);
    if (cached != null) {
      debugPrint('🎯 تم العثور على $key في التخزين المؤقت');
      return cached;
    }

    try {
      // إنشاء العنصر إذا لم يكن موجوداً
      debugPrint('🔄 إنشاء $key وحفظه في التخزين المؤقت');
      final data = await factory();
      await set(key, data, ttl: ttl, persistent: persistent);
      return data;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء $key: $e');
      return null;
    }
  }

  /// حذف عنصر من التخزين المؤقت
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    await _removeFromPersistentCache(key);
    debugPrint('🗑️ تم حذف $key من التخزين المؤقت');
  }

  /// مسح جميع عناصر التخزين المؤقت
  Future<void> clear() async {
    _memoryCache.clear();
    await _clearPersistentCache();
    debugPrint('🧹 تم مسح جميع عناصر التخزين المؤقت');
  }

  /// مسح العناصر المنتهية الصلاحية
  Future<void> _cleanupExpiredItems() async {
    final expiredKeys = <String>[];
    
    for (final entry in _memoryCache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      await _removeFromPersistentCache(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 تم حذف ${expiredKeys.length} عنصر منتهي الصلاحية');
    }
  }

  /// فرض حد الذاكرة
  void _enforceMemoryLimit() {
    if (_memoryCache.length <= _maxMemoryCacheSize) return;

    // ترتيب العناصر حسب تاريخ الإنشاء (الأقدم أولاً)
    final sortedEntries = _memoryCache.entries.toList()
      ..sort((a, b) => a.value.createdAt.compareTo(b.value.createdAt));

    // حذف العناصر الزائدة
    final itemsToRemove = _memoryCache.length - _maxMemoryCacheSize;
    for (int i = 0; i < itemsToRemove; i++) {
      _memoryCache.remove(sortedEntries[i].key);
    }

    debugPrint('🧹 تم حذف $itemsToRemove عنصر لفرض حد الذاكرة');
  }

  /// حفظ في التخزين المستمر
  Future<void> _saveToPersistentCache(String key, CacheItem item) async {
    if (_prefs == null) return;

    try {
      final jsonString = jsonEncode(item.toJson());
      await _prefs!.setString('$_persistentCachePrefix$key', jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ $key في التخزين المستمر: $e');
    }
  }

  /// حذف من التخزين المستمر
  Future<void> _removeFromPersistentCache(String key) async {
    if (_prefs == null) return;
    await _prefs!.remove('$_persistentCachePrefix$key');
  }

  /// مسح التخزين المستمر
  Future<void> _clearPersistentCache() async {
    if (_prefs == null) return;

    final keys = _prefs!.getKeys().where((key) => key.startsWith(_persistentCachePrefix));
    for (final key in keys) {
      await _prefs!.remove(key);
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int expiredCount = 0;
    int validCount = 0;

    for (final item in _memoryCache.values) {
      if (item.isExpired) {
        expiredCount++;
      } else {
        validCount++;
      }
    }

    return {
      'totalItems': _memoryCache.length,
      'validItems': validCount,
      'expiredItems': expiredCount,
      'memoryUsage': '${_memoryCache.length}/$_maxMemoryCacheSize',
      'lastCleanup': now.toIso8601String(),
    };
  }

  /// تنظيف الموارد
  void dispose() {
    _cleanupTimer?.cancel();
    _memoryCache.clear();
  }
}

/// مفاتيح التخزين المؤقت
class CacheKeys {
  static const String tasks = 'tasks';
  static String taskDetails(int id) => 'task_$id';
  static const String users = 'users';
  static String userDetails(int id) => 'user_$id';
  static const String departments = 'departments';
  static String departmentDetails(int id) => 'department_$id';
  static const String taskStatuses = 'task_statuses';
  static const String taskPriorities = 'task_priorities';
  static const String taskTypes = 'task_types';
  static const String subtasks = 'subtasks';
  static String subtasksByTask(int taskId) => 'subtasks_task_$taskId';
  static const String comments = 'comments';
  static String commentsByTask(int taskId) => 'comments_task_$taskId';
  static const String history = 'history';
  static String historyByTask(int taskId) => 'history_task_$taskId';
  static const String attachments = 'attachments';
  static String attachmentsByTask(int taskId) => 'attachments_task_$taskId';
  static const String messages = 'messages';
  static String taskMessages(int taskId) => 'messages_task_$taskId';
  static String userTrackers(int userId, int taskId) => 'user_trackers_${userId}_task_$taskId';
}
