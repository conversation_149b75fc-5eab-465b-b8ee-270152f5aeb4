import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/department_model.dart';

/// خدمة التخزين المؤقت للأقسام
class DepartmentCacheService {
  static const String _allDepartmentsKey = 'cached_all_departments';
  static const String _activeDepartmentsKey = 'cached_active_departments';
  static const String _departmentHierarchyKey = 'cached_department_hierarchy';
  static const String _lastUpdateKey = 'departments_last_update';
  static const String _departmentStatsKey = 'cached_department_stats';

  // مدة انتهاء صلاحية التخزين المؤقت (5 دقائق)
  static const Duration _cacheExpiration = Duration(minutes: 5);

  /// حفظ جميع الأقسام في التخزين المؤقت
  static Future<void> cacheAllDepartments(List<Department> departments) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final departmentsJson = departments.map((d) => d.toJson()).toList();
      await prefs.setString(_allDepartments<PERSON>ey, json<PERSON>ncode(departmentsJson));
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
      debugPrint('تم حفظ ${departments.length} قسم في التخزين المؤقت');
    } catch (e) {
      debugPrint('خطأ في حفظ الأقسام في التخزين المؤقت: $e');
    }
  }

  /// حفظ الأقسام النشطة في التخزين المؤقت
  static Future<void> cacheActiveDepartments(List<Department> departments) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final departmentsJson = departments.map((d) => d.toJson()).toList();
      await prefs.setString(_activeDepartmentsKey, jsonEncode(departmentsJson));
      debugPrint('تم حفظ ${departments.length} قسم نشط في التخزين المؤقت');
    } catch (e) {
      debugPrint('خطأ في حفظ الأقسام النشطة في التخزين المؤقت: $e');
    }
  }

  /// حفظ التسلسل الهرمي في التخزين المؤقت
  static Future<void> cacheDepartmentHierarchy(List<Department> hierarchy) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hierarchyJson = hierarchy.map((d) => d.toJson()).toList();
      await prefs.setString(_departmentHierarchyKey, jsonEncode(hierarchyJson));
      debugPrint('تم حفظ التسلسل الهرمي في التخزين المؤقت');
    } catch (e) {
      debugPrint('خطأ في حفظ التسلسل الهرمي في التخزين المؤقت: $e');
    }
  }

  /// حفظ إحصائيات الأقسام في التخزين المؤقت
  static Future<void> cacheDepartmentStats(Map<String, dynamic> stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_departmentStatsKey, jsonEncode(stats));
      debugPrint('تم حفظ إحصائيات الأقسام في التخزين المؤقت');
    } catch (e) {
      debugPrint('خطأ في حفظ إحصائيات الأقسام في التخزين المؤقت: $e');
    }
  }

  /// استرجاع جميع الأقسام من التخزين المؤقت
  static Future<List<Department>?> getCachedAllDepartments() async {
    try {
      if (!await _isCacheValid()) return null;

      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_allDepartmentsKey);
      
      if (cachedData == null) return null;

      final List<dynamic> departmentsJson = jsonDecode(cachedData);
      final departments = departmentsJson
          .map((json) => Department.fromJson(json as Map<String, dynamic>))
          .toList();

      debugPrint('تم استرجاع ${departments.length} قسم من التخزين المؤقت');
      return departments;
    } catch (e) {
      debugPrint('خطأ في استرجاع الأقسام من التخزين المؤقت: $e');
      return null;
    }
  }

  /// استرجاع الأقسام النشطة من التخزين المؤقت
  static Future<List<Department>?> getCachedActiveDepartments() async {
    try {
      if (!await _isCacheValid()) return null;

      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_activeDepartmentsKey);
      
      if (cachedData == null) return null;

      final List<dynamic> departmentsJson = jsonDecode(cachedData);
      final departments = departmentsJson
          .map((json) => Department.fromJson(json as Map<String, dynamic>))
          .toList();

      debugPrint('تم استرجاع ${departments.length} قسم نشط من التخزين المؤقت');
      return departments;
    } catch (e) {
      debugPrint('خطأ في استرجاع الأقسام النشطة من التخزين المؤقت: $e');
      return null;
    }
  }

  /// استرجاع التسلسل الهرمي من التخزين المؤقت
  static Future<List<Department>?> getCachedDepartmentHierarchy() async {
    try {
      if (!await _isCacheValid()) return null;

      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_departmentHierarchyKey);
      
      if (cachedData == null) return null;

      final List<dynamic> hierarchyJson = jsonDecode(cachedData);
      final hierarchy = hierarchyJson
          .map((json) => Department.fromJson(json as Map<String, dynamic>))
          .toList();

      debugPrint('تم استرجاع التسلسل الهرمي من التخزين المؤقت');
      return hierarchy;
    } catch (e) {
      debugPrint('خطأ في استرجاع التسلسل الهرمي من التخزين المؤقت: $e');
      return null;
    }
  }

  /// استرجاع إحصائيات الأقسام من التخزين المؤقت
  static Future<Map<String, dynamic>?> getCachedDepartmentStats() async {
    try {
      if (!await _isCacheValid()) return null;

      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_departmentStatsKey);
      
      if (cachedData == null) return null;

      final stats = jsonDecode(cachedData) as Map<String, dynamic>;
      debugPrint('تم استرجاع إحصائيات الأقسام من التخزين المؤقت');
      return stats;
    } catch (e) {
      debugPrint('خطأ في استرجاع إحصائيات الأقسام من التخزين المؤقت: $e');
      return null;
    }
  }

  /// مسح التخزين المؤقت للأقسام
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_allDepartmentsKey);
      await prefs.remove(_activeDepartmentsKey);
      await prefs.remove(_departmentHierarchyKey);
      await prefs.remove(_departmentStatsKey);
      await prefs.remove(_lastUpdateKey);
      debugPrint('تم مسح التخزين المؤقت للأقسام');
    } catch (e) {
      debugPrint('خطأ في مسح التخزين المؤقت للأقسام: $e');
    }
  }

  /// مسح قسم محدد من التخزين المؤقت
  static Future<void> invalidateDepartment(int departmentId) async {
    try {
      // مسح جميع البيانات المؤقتة عند تغيير قسم
      await clearCache();
      debugPrint('تم إلغاء صلاحية التخزين المؤقت للقسم $departmentId');
    } catch (e) {
      debugPrint('خطأ في إلغاء صلاحية التخزين المؤقت: $e');
    }
  }

  /// التحقق من صلاحية التخزين المؤقت
  static Future<bool> _isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdate = prefs.getInt(_lastUpdateKey);
      
      if (lastUpdate == null) return false;

      final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
      final now = DateTime.now();
      final difference = now.difference(lastUpdateTime);

      final isValid = difference < _cacheExpiration;
      if (!isValid) {
        debugPrint('انتهت صلاحية التخزين المؤقت للأقسام');
      }
      
      return isValid;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية التخزين المؤقت: $e');
      return false;
    }
  }

  /// الحصول على وقت آخر تحديث
  static Future<DateTime?> getLastUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdate = prefs.getInt(_lastUpdateKey);
      
      if (lastUpdate == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(lastUpdate);
    } catch (e) {
      debugPrint('خطأ في الحصول على وقت آخر تحديث: $e');
      return null;
    }
  }

  /// التحقق من وجود بيانات مؤقتة
  static Future<bool> hasCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_allDepartmentsKey) || 
             prefs.containsKey(_activeDepartmentsKey);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود بيانات مؤقتة: $e');
      return false;
    }
  }
}
