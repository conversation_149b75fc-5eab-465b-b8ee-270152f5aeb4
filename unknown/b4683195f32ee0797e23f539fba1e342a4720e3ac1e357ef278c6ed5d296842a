// import 'package:flutter/foundation.dart';
// import 'api/api_service.dart';
// import 'api/user_api_service.dart';
// import 'api/task_api_service.dart';
// import 'api/department_api_service.dart';

// /// مساعد اختبار خدمات API
// /// يستخدم للتحقق من صحة الاتصال مع الباك إند
// class ApiTestHelper {
//   static final ApiService _apiService = ApiService();
//   static final UserApiService _userService = UserApiService();
//   static final TaskApiService _taskService = TaskApiService();
//   static final DepartmentApiService _departmentService = DepartmentApiService();

//   /// اختبار الاتصال الأساسي مع API
//   static Future<bool> testBasicConnection() async {
//     try {
//       debugPrint('🔍 اختبار الاتصال الأساسي مع API...');
      
//       // اختبار endpoint بسيط
//       final response = await _apiService.get('/api/Users');
      
//       if (response.statusCode == 200 || response.statusCode == 401) {
//         debugPrint('✅ الاتصال مع API يعمل بشكل صحيح');
//         return true;
//       } else {
//         debugPrint('❌ فشل الاتصال مع API - كود الحالة: ${response.statusCode}');
//         return false;
//       }
//     } catch (e) {
//       debugPrint('❌ خطأ في الاتصال مع API: $e');
//       return false;
//     }
//   }

//   /// اختبار خدمة المستخدمين
//   static Future<bool> testUserService() async {
//     try {
//       debugPrint('🔍 اختبار خدمة المستخدمين...');
      
//       await _userService.getAllUsers();
//       debugPrint('✅ خدمة المستخدمين تعمل بشكل صحيح');
//       return true;
//     } catch (e) {
//       debugPrint('❌ خطأ في خدمة المستخدمين: $e');
//       return false;
//     }
//   }

//   /// اختبار خدمة المهام
//   static Future<bool> testTaskService() async {
//     try {
//       debugPrint('🔍 اختبار خدمة المهام...');
      
//       await _taskService.getAllTasks();
//       // await _taskService.getTaskStatuses();
//       // await _taskService.getTaskPriorities();
      
//       debugPrint('✅ خدمة المهام تعمل بشكل صحيح');
//       return true;
//     } catch (e) {
//       debugPrint('❌ خطأ في خدمة المهام: $e');
//       return false;
//     }
//   }

//   /// اختبار خدمة الأقسام
//   static Future<bool> testDepartmentService() async {
//     try {
//       debugPrint('🔍 اختبار خدمة الأقسام...');
      
//       await _departmentService.getAllDepartments();
//       debugPrint('✅ خدمة الأقسام تعمل بشكل صحيح');
//       return true;
//     } catch (e) {
//       debugPrint('❌ خطأ في خدمة الأقسام: $e');
//       return false;
//     }
//   }

//   /// اختبار شامل لجميع الخدمات
//   static Future<Map<String, bool>> runAllTests() async {
//     debugPrint('🚀 بدء اختبار جميع خدمات API...');
    
//     final results = <String, bool>{};
    
//     // اختبار الاتصال الأساسي
//     results['basic_connection'] = await testBasicConnection();
    
//     // اختبار الخدمات الفردية
//     results['user_service'] = await testUserService();
//     results['task_service'] = await testTaskService();
//     results['department_service'] = await testDepartmentService();
    
//     // طباعة النتائج
//     debugPrint('\n📊 نتائج اختبار خدمات API:');
//     results.forEach((service, success) {
//       final status = success ? '✅' : '❌';
//       debugPrint('$status $service: ${success ? 'نجح' : 'فشل'}');
//     });
    
//     final successCount = results.values.where((v) => v).length;
//     final totalCount = results.length;
    
//     debugPrint('\n📈 الملخص: $successCount/$totalCount خدمات تعمل بشكل صحيح');
    
//     return results;
//   }

//   /// اختبار مسارات API المحددة
//   static Future<void> testSpecificEndpoints() async {
//     debugPrint('🔍 اختبار مسارات API المحددة...');
    
//     final endpoints = [
//       '/api/Users',
//       '/api/Tasks', 
//       '/api/Departments',
//       '/api/TaskStatuses',
//       '/api/TaskPriorities',
//       '/api/TaskTypes',
//     ];
    
//     for (final endpoint in endpoints) {
//       try {
//         final response = await _apiService.get(endpoint);
//         final status = response.statusCode == 200 || response.statusCode == 401 ? '✅' : '❌';
//         debugPrint('$status $endpoint - كود الحالة: ${response.statusCode}');
//       } catch (e) {
//         debugPrint('❌ $endpoint - خطأ: $e');
//       }
//     }
//   }

//   /// اختبار تهيئة الخدمات
//   static Future<bool> testServiceInitialization() async {
//     try {
//       debugPrint('🔍 اختبار تهيئة الخدمات...');
      
//       await _apiService.initialize();
//       debugPrint('✅ تم تهيئة ApiService بنجاح');
      
//       return true;
//     } catch (e) {
//       debugPrint('❌ خطأ في تهيئة الخدمات: $e');
//       return false;
//     }
//   }

//   /// طباعة معلومات التكوين
//   static void printConfigInfo() {
//     debugPrint('\n📋 معلومات تكوين API:');
//     debugPrint('🌐 عنوان API: ${_apiService.accessToken != null ? 'متصل' : 'غير متصل'}');
//     debugPrint('🔐 حالة المصادقة: ${_apiService.isLoggedIn ? 'مسجل دخول' : 'غير مسجل'}');
//     debugPrint('🔑 رمز الوصول: ${_apiService.accessToken != null ? 'موجود' : 'غير موجود'}');
//   }
// }

// /// استخدام المساعد:
// /// 
// /// ```dart
// /// // اختبار سريع
// /// final isConnected = await ApiTestHelper.testBasicConnection();
// /// 
// /// // اختبار شامل
// /// final results = await ApiTestHelper.runAllTests();
// /// 
// /// // اختبار مسارات محددة
// /// await ApiTestHelper.testSpecificEndpoints();
// /// 
// /// // طباعة معلومات التكوين
// /// ApiTestHelper.printConfigInfo();
// /// ```
