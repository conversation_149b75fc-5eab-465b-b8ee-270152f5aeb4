import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/scheduler.dart';

/// مساعد لمعالجة أحداث الماوس بشكل آمن
///
/// يساعد في تجنب مشاكل تتبع الماوس المتعلقة بـ _debugDuringDeviceUpdate
class MouseEventHandler {
  /// مثيل واحد (Singleton)
  static final MouseEventHandler _instance = MouseEventHandler._internal();

  /// الحصول على المثيل
  factory MouseEventHandler() => _instance;

  /// منشئ خاص
  MouseEventHandler._internal();

  /// قائمة الأحداث المعلقة
  final List<_PendingMouseEvent> _pendingEvents = [];

  /// مؤقت لمعالجة الأحداث
  Timer? _processingTimer;

  /// حالة قفل معالجة الأحداث أثناء التنقل بين الشاشات
  bool _isLocked = false;

  /// مؤقت لإلغاء القفل تلقائيًا
  Timer? _unlockTimer;

  /// قفل معالجة أحداث الماوس مؤقتًا
  ///
  /// يستخدم هذا عند التنقل بين الشاشات لتجنب مشاكل تتبع الماوس
  /// [durationMs] المدة بالمللي ثانية قبل إلغاء القفل تلقائيًا
  void lock([int durationMs = 500]) {
    _isLocked = true;

    // إلغاء المؤقت السابق إذا كان موجودًا
    _unlockTimer?.cancel();

    // إعداد مؤقت لإلغاء القفل تلقائيًا بعد المدة المحددة
    _unlockTimer = Timer(Duration(milliseconds: durationMs), unlock);

    debugPrint('تم قفل معالج أحداث الماوس لمدة $durationMs مللي ثانية');
  }

  /// إلغاء قفل معالجة أحداث الماوس
  void unlock() {
    _isLocked = false;
    _unlockTimer?.cancel();
    _unlockTimer = null;

    // معالجة الأحداث المعلقة بعد إلغاء القفل
    if (_pendingEvents.isNotEmpty) {
      _scheduleProcessing();
    }

    debugPrint('تم إلغاء قفل معالج أحداث الماوس');
  }

  /// إضافة حدث ماوس للمعالجة
  ///
  /// [eventType] نوع الحدث
  /// [callback] الدالة التي سيتم استدعاؤها
  void addEvent(MouseEventType eventType, VoidCallback callback) {
    // إذا كان هناك عدد كبير من الأحداث المعلقة، قم بتنظيفها
    if (_pendingEvents.length > 100) {
      debugPrint('تنظيف قائمة الأحداث المعلقة (${_pendingEvents.length} حدث)');
      _pendingEvents.clear();
    }

    _pendingEvents.add(_PendingMouseEvent(eventType, callback));

    // جدولة المعالجة فقط إذا لم يكن هناك قفل
    if (!_isLocked) {
      _scheduleProcessing();
    }
  }

  /// جدولة معالجة الأحداث
  void _scheduleProcessing() {
    // إذا كان هناك مؤقت نشط أو كان هناك قفل، لا تقم بجدولة معالجة جديدة
    if (_processingTimer != null || _isLocked) {
      return;
    }

    // استخدام جدولة الإطار التالي بدلاً من مؤقت ثابت
    // هذا يضمن أن المعالجة تحدث بعد اكتمال الإطار الحالي
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // تحقق مرة أخرى من حالة القفل قبل المعالجة
      if (!_isLocked) {
        _processEvents();
      }
      _processingTimer = null;
    });

    // تعيين المؤقت لمنع جدولة متعددة
    _processingTimer = Timer(Duration.zero, () {});
  }

  /// معالجة الأحداث المعلقة
  void _processEvents() {
    // لا تعالج الأحداث إذا كانت القائمة فارغة أو كان هناك قفل
    if (_pendingEvents.isEmpty || _isLocked) {
      return;
    }

    try {
      // تأخير المعالجة للإطار التالي لتجنب مشاكل تتبع الماوس
      SchedulerBinding.instance.scheduleFrameCallback((_) {
        try {
          // تحقق مرة أخرى من حالة القفل والأحداث المعلقة
          if (_isLocked || _pendingEvents.isEmpty) {
            return;
          }

          // نسخ القائمة لتجنب التعديل أثناء التكرار
          final events = List<_PendingMouseEvent>.from(_pendingEvents);
          _pendingEvents.clear();

          // معالجة الأحداث بشكل تدريجي لتجنب مشاكل الأداء
          _processEventsBatch(events, 0);
        } catch (e) {
          debugPrint('خطأ في جدولة معالجة أحداث الماوس: $e');
        }
      });
    } catch (e) {
      debugPrint('خطأ في معالجة أحداث الماوس: $e');
    }
  }

  /// معالجة دفعة من الأحداث
  void _processEventsBatch(List<_PendingMouseEvent> events, int startIndex) {
    // عدد الأحداث في كل دفعة
    const batchSize = 5;

    // تحديد نهاية الدفعة الحالية
    final endIndex = startIndex + batchSize < events.length
        ? startIndex + batchSize
        : events.length;

    // معالجة الأحداث في الدفعة الحالية
    for (int i = startIndex; i < endIndex; i++) {
      try {
        // تحقق من حالة القفل قبل معالجة كل حدث
        if (!_isLocked) {
          events[i].callback();
        }
      } catch (e) {
        debugPrint('خطأ في معالجة حدث الماوس: $e');
      }
    }

    // إذا كانت هناك المزيد من الأحداث، جدولة الدفعة التالية
    if (endIndex < events.length && !_isLocked) {
      // استخدام تأخير قصير بين الدفعات
      Future.delayed(const Duration(milliseconds: 10), () {
        _processEventsBatch(events, endIndex);
      });
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _processingTimer?.cancel();
    _unlockTimer?.cancel();
    _pendingEvents.clear();
    _isLocked = false;
  }

  /// تنظيف جميع الأحداث المعلقة
  void clearEvents() {
    _pendingEvents.clear();
    debugPrint('تم تنظيف جميع أحداث الماوس المعلقة');
  }
}

/// نوع حدث الماوس
enum MouseEventType { enter, exit, move, down, up, drag }

/// حدث ماوس معلق
class _PendingMouseEvent {
  final MouseEventType type;
  final VoidCallback callback;

  _PendingMouseEvent(this.type, this.callback);
}

/// مكون MouseRegion آمن
///
/// يستخدم MouseEventHandler لتجنب مشاكل تتبع الماوس
class SafeMouseRegion extends StatefulWidget {
  /// دالة عند دخول المؤشر
  final void Function(PointerEnterEvent)? onEnter;

  /// دالة عند خروج المؤشر
  final void Function(PointerExitEvent)? onExit;

  /// دالة عند تحريك المؤشر
  final void Function(PointerHoverEvent)? onHover;

  /// مؤشر الماوس
  final MouseCursor cursor;

  /// الطفل
  final Widget child;

  /// تأخير معالجة الأحداث (بالمللي ثانية)
  final int eventDelayMs;

  /// تعطيل معالجة الأحداث مؤقتًا
  final bool disabled;

  /// منشئ
  const SafeMouseRegion({
    super.key,
    this.onEnter,
    this.onExit,
    this.onHover,
    this.cursor = MouseCursor.defer,
    this.eventDelayMs = 0,
    this.disabled = false,
    required this.child,
  });

  /// قفل جميع أحداث الماوس مؤقتًا
  ///
  /// يستخدم هذا عند التنقل بين الشاشات لتجنب مشاكل تتبع الماوس
  static void lockEvents([int durationMs = 500]) {
    MouseEventHandler().lock(durationMs);
  }

  /// إلغاء قفل أحداث الماوس
  static void unlockEvents() {
    MouseEventHandler().unlock();
  }

  /// تنظيف جميع الأحداث المعلقة
  static void clearEvents() {
    MouseEventHandler().clearEvents();
  }

  @override
  State<SafeMouseRegion> createState() => _SafeMouseRegionState();
}

class _SafeMouseRegionState extends State<SafeMouseRegion> {
  // تم تبسيط الحالة لتجنب مشاكل mouse_tracker.dart
  bool _isReady = false;

  @override
  void initState() {
    super.initState();
    // تأخير تفعيل المنطقة لتجنب مشاكل تتبع الماوس أثناء التهيئة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isReady = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _isReady = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام Container بدلاً من MouseRegion إذا كان المكون معطلاً أو غير جاهز
    if (widget.disabled || !_isReady) {
      return widget.child;
    }

    // استخدام MouseRegion بشكل مباشر مع تعطيل opaque
    // هذا يحل مشكلة assertion errors في mouse_tracker.dart
    return MouseRegion(
      opaque: false, // مهم: جعل المنطقة غير معتمة لتجنب مشاكل تتبع الماوس
      cursor: widget.cursor,
      onEnter: widget.onEnter != null
          ? (event) {
              if (!MouseEventHandler()._isLocked) {
                MouseEventHandler().addEvent(
                  MouseEventType.enter,
                  () => widget.onEnter?.call(event),
                );
              }
            }
          : null,
      onExit: widget.onExit != null
          ? (event) {
              if (!MouseEventHandler()._isLocked) {
                MouseEventHandler().addEvent(
                  MouseEventType.exit,
                  () => widget.onExit?.call(event),
                );
              }
            }
          : null,
      onHover: widget.onHover != null
          ? (event) {
              if (!MouseEventHandler()._isLocked) {
                MouseEventHandler().addEvent(
                  MouseEventType.move,
                  () => widget.onHover?.call(event),
                );
              }
            }
          : null,
      child: widget.child,
    );
  }
}
