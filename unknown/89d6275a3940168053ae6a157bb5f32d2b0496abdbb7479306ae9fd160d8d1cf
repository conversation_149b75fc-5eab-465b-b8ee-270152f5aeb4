import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signalr_netcore/signalr_client.dart';
import '../models/message_models.dart';
import '../models/task_comment_models.dart';
import '../utils/logger.dart';
import '../controllers/auth_controller.dart';
import '../controllers/notifications_controller.dart';
import '../controllers/task_messages_controller.dart';
import '../controllers/unified_chat_controller.dart';
import '../controllers/notification_settings_controller.dart';
import '../services/notifications_service.dart';

/// خدمة SignalR موحدة لجميع أنواع الاتصالات الفورية
/// تدعم: المحادثات العامة، رسائل المهام، تعليقات المهام
class UnifiedSignalRService extends GetxService {
  // اتصالات Hub متعددة
  HubConnection? _chatHubConnection;
  HubConnection? _taskCommentsHubConnection;
  HubConnection? _notificationHubConnection;
  HubConnection? _taskHubConnection;

  // URLs للـ Hubs - تغيير من HTTPS إلى HTTP
  final String _chatHubUrl = "http://localhost:7111/chatHub";
  final String _taskCommentsHubUrl = "http://localhost:7111/taskCommentsHub";
  final String _notificationHubUrl = "http://localhost:7111/notificationHub";
  final String _taskHubUrl = "http://localhost:7111/taskHub";

  // حالة الاتصالات
  final RxBool _isChatHubConnected = false.obs;
  final RxBool _isTaskCommentsHubConnected = false.obs;
  final RxBool _isNotificationHubConnected = false.obs;
  final RxBool _isTaskHubConnected = false.obs;
  
  // متغيرات للمحادثات العامة
  final Rx<Message?> _newMessage = Rx<Message?>(null);
  
  // متغيرات لتعليقات المهام
  final RxList<TaskComment> _newComments = <TaskComment>[].obs;
  final RxList<TaskComment> _updatedComments = <TaskComment>[].obs;
  final RxList<int> _deletedCommentIds = <int>[].obs;
  
  // متغيرات لرسائل المهام
  final RxList<Map<String, dynamic>> _newTaskMessages = <Map<String, dynamic>>[].obs;
  
  // متغيرات للإشعارات
  final RxList<Map<String, dynamic>> _newNotifications = <Map<String, dynamic>>[].obs;
  final RxInt _unreadNotificationsCount = 0.obs;
  
  // Timers لإعادة الاتصال والتحديث الدوري
  Timer? _chatReconnectTimer;
  Timer? _commentsReconnectTimer;
  Timer? _notificationReconnectTimer;
  Timer? _periodicUpdateTimer;
  
  // متغيرات إعادة الاتصال
  bool _isChatReconnecting = false;
  bool _isCommentsReconnecting = false;
  bool _isNotificationReconnecting = false;
  int _chatReconnectAttempts = 0;
  int _commentsReconnectAttempts = 0;
  int _notificationReconnectAttempts = 0;
  int _taskReconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;

  // Getters للحالة
  bool get isChatHubConnected => _isChatHubConnected.value;
  bool get isTaskCommentsHubConnected => _isTaskCommentsHubConnected.value;
  bool get isNotificationHubConnected => _isNotificationHubConnected.value;
  bool get isTaskHubConnected => _isTaskHubConnected.value;
  bool get isFullyConnected => isChatHubConnected && isTaskCommentsHubConnected && isNotificationHubConnected && isTaskHubConnected;
  
  // Getters للبيانات
  Rx<Message?> get newMessageStream => _newMessage;
  List<TaskComment> get newComments => _newComments;
  List<TaskComment> get updatedComments => _updatedComments;
  List<int> get deletedCommentIds => _deletedCommentIds;
  List<Map<String, dynamic>> get newTaskMessages => _newTaskMessages;
  List<Map<String, dynamic>> get newNotifications => _newNotifications;
  int get unreadNotificationsCount => _unreadNotificationsCount.value;
  
  // Getters للاتصالات (للتوافق مع الكود الحالي)
  HubConnection? get hubConnection => _taskHubConnection; // تغيير للإشارة إلى TaskHub للمرفقات
  HubConnection? get chatHubConnection => _chatHubConnection;
  HubConnection? get taskCommentsHubConnection => _taskCommentsHubConnection;
  HubConnection? get taskHubConnection => _taskHubConnection;

  @override
  void onInit() {
    super.onInit();
    _initializeConnections();
  }

  @override
  void onClose() {
    _cleanup();
    super.onClose();
  }

  /// تهيئة جميع الاتصالات
  Future<void> _initializeConnections() async {
    debugPrint('🔗 بدء تهيئة خدمة SignalR الموحدة...');

    // إضافة تأخير قصير للتأكد من أن الـ backend جاهز
    await Future.delayed(const Duration(seconds: 2));

    // تهيئة اتصال المحادثات العامة
    await _initializeChatHub();

    // تهيئة اتصال تعليقات المهام
    await _initializeTaskCommentsHub();

    // تهيئة اتصال الإشعارات
    await _initializeNotificationHub();

    // تهيئة اتصال المهام (للمرفقات وتحديثات المهام)
    await _initializeTaskHub();

    // بدء فحص دوري للاتصال (كل دقيقة)
    _startPeriodicConnectionCheck();

    debugPrint('✅ تم تهيئة خدمة SignalR الموحدة');
  }

  /// بدء فحص دوري لحالة الاتصال (كل 5 دقائق لتقليل الحمل)
  void _startPeriodicConnectionCheck() {
    _periodicUpdateTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkAndReconnectIfNeeded();
    });
  }

  /// فحص الاتصال وإعادة الاتصال إذا لزم الأمر (مع تحسينات للأداء)
  void _checkAndReconnectIfNeeded() {
    try {
      // تجنب الفحص المتكرر إذا كانت جميع الاتصالات نشطة
      if (_chatHubConnection?.state == HubConnectionState.Connected &&
          _taskCommentsHubConnection?.state == HubConnectionState.Connected &&
          _notificationHubConnection?.state == HubConnectionState.Connected) {
        return; // جميع الاتصالات نشطة، لا حاجة للفحص
      }

      // فحص Chat Hub فقط إذا لم يكن متصلاً ولا يحاول الاتصال
      if (_chatHubConnection?.state != HubConnectionState.Connected &&
          !_isChatReconnecting && _chatReconnectAttempts < _maxReconnectAttempts) {
        debugPrint('🔄 إعادة اتصال Chat Hub...');
        _reconnectChatHub();
      }

      // فحص Task Comments Hub فقط إذا لم يكن متصلاً ولا يحاول الاتصال
      if (_taskCommentsHubConnection?.state != HubConnectionState.Connected &&
          !_isCommentsReconnecting && _commentsReconnectAttempts < _maxReconnectAttempts) {
        debugPrint('🔄 إعادة اتصال Task Comments Hub...');
        _reconnectTaskCommentsHub();
      }

      // فحص Notification Hub فقط إذا لم يكن متصلاً ولا يحاول الاتصال
      if (_notificationHubConnection?.state != HubConnectionState.Connected &&
          !_isNotificationReconnecting && _notificationReconnectAttempts < _maxReconnectAttempts) {
        debugPrint('🔄 إعادة اتصال Notification Hub...');
        _reconnectNotificationHub();
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص الاتصالات: $e');
    }
  }

  /// تهيئة Hub المحادثات العامة مع معالجة أخطاء محسنة
  Future<void> _initializeChatHub() async {
    try {
      debugPrint('🔗 تهيئة Chat Hub: $_chatHubUrl');

      _chatHubConnection = HubConnectionBuilder()
          .withUrl(_chatHubUrl)
          .build();

      // إضافة معالج عام للأخطاء غير المتوقعة
      _chatHubConnection!.onclose((error) {
        _isChatHubConnected.value = false;
        // تجاهل خطأ null subtype المعروف
        if (error != null && !error.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint("Chat Hub Connection closed: $error");
        }
      });

      _setupChatHubEventHandlers();
      await _connectChatHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Chat Hub: $e');
      // إعادة المحاولة بعد تأخير فقط إذا لم نتجاوز الحد الأقصى
      if (_chatReconnectAttempts < _maxReconnectAttempts) {
        Timer(const Duration(seconds: 10), () => _initializeChatHub());
      } else {
        debugPrint('❌ تم تجاوز الحد الأقصى لمحاولات الاتصال بـ Chat Hub');
      }
    }
  }

  /// تهيئة Hub تعليقات المهام مع معالجة أخطاء محسنة
  Future<void> _initializeTaskCommentsHub() async {
    try {
      debugPrint('🔗 تهيئة Task Comments Hub: $_taskCommentsHubUrl');

      _taskCommentsHubConnection = HubConnectionBuilder()
          .withUrl(_taskCommentsHubUrl)
          .build();

      // إضافة معالج عام للأخطاء غير المتوقعة
      _taskCommentsHubConnection!.onclose((error) {
        _isTaskCommentsHubConnected.value = false;
        // تجاهل خطأ null subtype المعروف
        if (error != null && !error.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint("Task Comments Hub Connection closed: $error");
        }
      });

      _setupTaskCommentsHubEventHandlers();
      await _connectTaskCommentsHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Task Comments Hub: $e');
      // إعادة المحاولة بعد تأخير فقط إذا لم نتجاوز الحد الأقصى
      if (_commentsReconnectAttempts < _maxReconnectAttempts) {
        Timer(const Duration(seconds: 10), () => _initializeTaskCommentsHub());
      } else {
        debugPrint('❌ تم تجاوز الحد الأقصى لمحاولات الاتصال بـ Task Comments Hub');
      }
    }
  }
  
  /// تهيئة Hub الإشعارات مع معالجة أخطاء محسنة
  Future<void> _initializeNotificationHub() async {
    try {
      debugPrint('🔗 تهيئة Notification Hub: $_notificationHubUrl');

      _notificationHubConnection = HubConnectionBuilder()
          .withUrl(_notificationHubUrl)
          .build();

      // إضافة معالج عام للأخطاء غير المتوقعة
      _notificationHubConnection!.onclose((error) {
        _isNotificationHubConnected.value = false;
        // تجاهل خطأ null subtype المعروف
        if (error != null && !error.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint("Notification Hub Connection closed: $error");
        }
      });

      _setupNotificationHubEventHandlers();
      await _connectNotificationHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Notification Hub: $e');
      // إعادة المحاولة بعد تأخير فقط إذا لم نتجاوز الحد الأقصى
      if (_notificationReconnectAttempts < _maxReconnectAttempts) {
        Timer(const Duration(seconds: 10), () => _initializeNotificationHub());
      } else {
        debugPrint('❌ تم تجاوز الحد الأقصى لمحاولات الاتصال بـ Notification Hub');
      }
    }
  }

  /// تهيئة Task Hub للمرفقات وتحديثات المهام
  Future<void> _initializeTaskHub() async {
    try {
      debugPrint('🔗 تهيئة Task Hub: $_taskHubUrl');

      _taskHubConnection = HubConnectionBuilder()
          .withUrl(_taskHubUrl)
          .build();

      // إضافة معالج خاص لأخطاء null subtype في Task Hub
      _taskHubConnection!.onclose((error) {
        _isTaskHubConnected.value = false;
        if (error != null && !error.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint("Task Hub Connection closed: $error");
        }
        // يمكن إضافة إعادة الاتصال هنا إذا لزم الأمر
      });

      _setupTaskHubEventHandlers();
      await _connectTaskHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Task Hub: $e');
      // إعادة المحاولة بعد تأخير فقط إذا لم نتجاوز الحد الأقصى
      if (_taskReconnectAttempts < _maxReconnectAttempts) {
        Timer(const Duration(seconds: 10), () => _initializeTaskHub());
      } else {
        debugPrint('❌ تم تجاوز الحد الأقصى لمحاولات الاتصال بـ Task Hub');
      }
    }
  }

  /// إعداد معالجات أحداث Chat Hub
  void _setupChatHubEventHandlers() {
    if (_chatHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _chatHubConnection!.onclose((error) {
      _isChatHubConnected.value = false;
      AppLogger.error("Chat Hub Connection closed: $error");

      if (!_isChatReconnecting) {
        _reconnectChatHub();
      }
    });

    // معالجة أخطاء SignalR
    _chatHubConnection!.on("Error", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final errorData = arguments[0];
          AppLogger.error("خطأ من SignalR Hub: $errorData");

          // يمكن إضافة معالجة خاصة للأخطاء هنا
          if (errorData is Map<String, dynamic>) {
            final message = errorData['Message'] ?? 'خطأ غير معروف';
            final groupId = errorData['GroupId'];
            AppLogger.error("خطأ في المجموعة $groupId: $message");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة رسالة الخطأ من SignalR: $e");
      }
    });

    // معالجة أخطاء إرسال الرسائل
    _chatHubConnection!.on("MessageSendError", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final errorData = arguments[0];
          AppLogger.error("خطأ في إرسال الرسالة: $errorData");
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة خطأ إرسال الرسالة: $e");
      }
    });

    // استقبال الرسائل العامة
    _chatHubConnection!.on("ReceiveMessage", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final messageData = arguments[0];

          // التحقق من نوع البيانات
          if (messageData is Map<String, dynamic>) {
            // التحقق من وجود البيانات المطلوبة
            if (messageData.containsKey('id') &&
                messageData.containsKey('groupId') &&
                messageData.containsKey('senderId') &&
                messageData.containsKey('content') &&
                messageData.containsKey('createdAt')) {

              final message = Message.fromJson(messageData);
              _newMessage.value = message;
              AppLogger.debug("تم استقبال رسالة جديدة: ${message.id}");
            } else {
              AppLogger.warning("رسالة مستقبلة تحتوي على بيانات ناقصة: $messageData");
            }
          } else {
            AppLogger.warning("نوع بيانات الرسالة غير صحيح: ${messageData.runtimeType}");
          }
        } else {
          AppLogger.warning("تم استقبال رسالة فارغة أو null");
        }
      } catch (e, stackTrace) {
        AppLogger.error("خطأ في معالجة الرسالة الجديدة: $e");
        AppLogger.error("Stack trace: $stackTrace");
      }
    });

    // استقبال رسائل المهام مع تحديث فوري
    _chatHubConnection!.on("ReceiveTaskMessage", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final messageData = arguments[0];

          // التحقق من أن البيانات صالحة
          if (messageData is Map<String, dynamic> && messageData.isNotEmpty) {
            // إضافة الرسالة للقائمة
            _newTaskMessages.add(messageData);

            // تحديث فوري للمتحكمات
            _notifyMessagesControllers(messageData);

            AppLogger.debug("تم استقبال رسالة مهمة جديدة فورياً: ${messageData['id'] ?? 'unknown'}");
          } else {
            AppLogger.warning("تم استقبال رسالة مهمة بتنسيق غير صالح");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة رسالة المهمة: $e");
      }
    });

    // باقي أحداث Chat Hub
    _setupChatHubAdditionalEvents();
  }

  /// إعداد أحداث إضافية لـ Chat Hub
  void _setupChatHubAdditionalEvents() {
    if (_chatHubConnection == null) return;

    // مستمع لتحديثات المهام
    _chatHubConnection!.on("TaskUpdated", (arguments) {
      AppLogger.debug("Task Updated: $arguments");
    });

    // مستمع لتحديثات حالة المهام
    _chatHubConnection!.on("TaskStatusUpdated", (arguments) {
      if (arguments != null && arguments.length >= 2) {
        int taskId = arguments[0];
        String newStatus = arguments[1];
        AppLogger.debug("Task $taskId status updated to $newStatus");
      }
    });

    // مستمع لإشعارات الكتابة في المهام
    _chatHubConnection!.on("UserTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final taskId = int.tryParse(data['TaskId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (taskId != null && userName.isNotEmpty) {
            _notifyTaskTypingStarted(taskId, userName);
            AppLogger.debug("User $userName started typing in Task: $taskId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار الكتابة في المهمة: $e");
      }
    });

    // مستمع لإشعارات توقف الكتابة في المهام
    _chatHubConnection!.on("UserStoppedTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final taskId = int.tryParse(data['TaskId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (taskId != null && userName.isNotEmpty) {
            _notifyTaskTypingStopped(taskId, userName);
            AppLogger.debug("User $userName stopped typing in Task: $taskId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار توقف الكتابة في المهمة: $e");
      }
    });

    // مستمع لإشعارات الكتابة في المجموعات
    _chatHubConnection!.on("GroupUserTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final groupId = int.tryParse(data['GroupId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (groupId != null && userName.isNotEmpty) {
            _notifyGroupTypingStarted(groupId, userName);
            AppLogger.debug("User $userName started typing in Group: $groupId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار الكتابة في المجموعة: $e");
      }
    });

    // مستمع لإشعارات توقف الكتابة في المجموعات
    _chatHubConnection!.on("GroupUserStoppedTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final groupId = int.tryParse(data['GroupId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (groupId != null && userName.isNotEmpty) {
            _notifyGroupTypingStopped(groupId, userName);
            AppLogger.debug("User $userName stopped typing in Group: $groupId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار توقف الكتابة في المجموعة: $e");
      }
    });

    // مستمع لقراءة الرسائل
    _chatHubConnection!.on("MessageRead", (arguments) {
      AppLogger.debug("Message Read: $arguments");
    });

    // مستمع لقراءة جميع رسائل المجموعة
    _chatHubConnection!.on("GroupMessagesRead", (arguments) {
      AppLogger.debug("Group Messages Read: $arguments");
    });
  }

  /// إعداد معالجات أحداث Task Comments Hub
  void _setupTaskCommentsHubEventHandlers() {
    if (_taskCommentsHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _taskCommentsHubConnection!.onclose((error) {
      _isTaskCommentsHubConnected.value = false;
      debugPrint('🔌 انقطع الاتصال مع Task Comments Hub: $error');
      
      if (!_isCommentsReconnecting) {
        _reconnectTaskCommentsHub();
      }
    });

    // استقبال تعليق جديد مع تحديث فوري
    _taskCommentsHubConnection!.on('ReceiveTaskComment', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final commentData = arguments[0];

          // التحقق من أن البيانات صالحة
          if (commentData is Map<String, dynamic> && commentData.isNotEmpty) {
            final comment = TaskComment.fromJson(commentData);

            // إضافة التعليق للقائمة
            _newComments.add(comment);

            // تحديث فوري للمتحكمات
            _notifyCommentsControllers(comment);

            debugPrint('📨 تم استقبال تعليق جديد فورياً: ${comment.id}');
          } else {
            debugPrint('⚠️ تم استقبال تعليق بتنسيق غير صالح');
          }
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تعليق جديد: $e');
      }
    });

    // استقبال تحديث تعليق
    _taskCommentsHubConnection!.on('TaskCommentUpdated', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final commentData = arguments[0] as Map<String, dynamic>;
          final comment = TaskComment.fromJson(commentData);
          _updatedComments.add(comment);
          debugPrint('✏️ تم استقبال تحديث تعليق: ${comment.id}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تحديث تعليق: $e');
      }
    });

    // استقبال حذف تعليق
    _taskCommentsHubConnection!.on('TaskCommentDeleted', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final commentId = data['CommentId'] as int;
          _deletedCommentIds.add(commentId);
          debugPrint('🗑️ تم استقبال حذف تعليق: $commentId');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة حذف تعليق: $e');
      }
    });
  }
  
  /// إعداد معالجات أحداث Notification Hub
  void _setupNotificationHubEventHandlers() {
    if (_notificationHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _notificationHubConnection!.onclose((error) {
      _isNotificationHubConnected.value = false;
      debugPrint('🔌 انقطع الاتصال مع Notification Hub: $error');
      
      if (!_isNotificationReconnecting) {
        _reconnectNotificationHub();
      }
    });

    // استقبال إشعار جديد
    _notificationHubConnection!.on('ReceiveNotification', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final notificationData = arguments[0] as Map<String, dynamic>;
          
          // إضافة الإشعار للقائمة
          _newNotifications.add(notificationData);
          
          // تحديث عدد الإشعارات غير المقروءة
          if (notificationData['isRead'] == false) {
            _unreadNotificationsCount.value++;
          }
          
          // إشعار المتحكمات بالإشعار الجديد
          _notifyNotificationControllers(notificationData);
          
          debugPrint('📨 تم استقبال إشعار جديد: ${notificationData['id']}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة إشعار جديد: $e');
      }
    });

    // تحديث عدد الإشعارات غير المقروءة
    _notificationHubConnection!.on('UnreadCountUpdated', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final count = data['Count'] as int;
          _unreadNotificationsCount.value = count;
          debugPrint('🔢 تم تحديث عدد الإشعارات غير المقروءة: $count');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تحديث عدد الإشعارات غير المقروءة: $e');
      }
    });
  }

  /// إعداد معالجات أحداث Task Hub
  void _setupTaskHubEventHandlers() {
    if (_taskHubConnection == null) return;

    // معالجة إغلاق الاتصال
    _taskHubConnection!.onclose((error) {
      _isTaskHubConnected.value = false;
      debugPrint("Task Hub Connection closed: $error");
      // يمكن إضافة إعادة الاتصال هنا إذا لزم الأمر
    });

    // معالجة إضافة مرفق جديد
    _taskHubConnection!.on('AttachmentAdded', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          // التحقق من صحة المعاملات وتجنب null values
          final safeArguments = arguments.where((arg) => arg != null && arg.toString().isNotEmpty).toList();
          if (safeArguments.isNotEmpty) {
            debugPrint('📎 تم استقبال إشعار إضافة مرفق: $safeArguments');
            // سيتم معالجة هذا في TaskAttachmentsTab
          } else {
            debugPrint('⚠️ تم استقبال إشعار إضافة مرفق بمعاملات فارغة');
          }
        }
      } catch (e) {
        // تصفية أخطاء null subtype المعروفة لتقليل تلوث السجلات
        if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint('❌ خطأ في معالجة إشعار إضافة مرفق: $e');
        }
        // تجاهل الخطأ لتجنب crash التطبيق
      }
    });

    // معالجة حذف مرفق
    _taskHubConnection!.on('AttachmentDeleted', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          // التحقق من صحة المعاملات وتجنب null values
          final safeArguments = arguments.where((arg) => arg != null).toList();
          debugPrint('🗑️ تم استقبال إشعار حذف مرفق: $safeArguments');
          // سيتم معالجة هذا في TaskAttachmentsTab
        }
      } catch (e) {
        // تصفية أخطاء null subtype المعروفة لتقليل تلوث السجلات
        if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint('❌ خطأ في معالجة إشعار حذف مرفق: $e');
        }
        // تجاهل الخطأ لتجنب crash التطبيق
      }
    });
  }

  /// الاتصال بـ Chat Hub
  Future<void> _connectChatHub() async {
    if (_chatHubConnection?.state == HubConnectionState.Connected) {
      _isChatHubConnected.value = true;
      return;
    }

    try {
      await _chatHubConnection?.start();
      _isChatHubConnected.value = true;
      _chatReconnectAttempts = 0;
      _isChatReconnecting = false;
      AppLogger.debug("Chat Hub Connection started successfully");
    } catch (e) {
      _isChatHubConnected.value = false;
      AppLogger.error("Error starting Chat Hub connection: $e");
      _reconnectChatHub();
    }
  }

  /// الاتصال بـ Task Comments Hub
  Future<void> _connectTaskCommentsHub() async {
    if (_taskCommentsHubConnection?.state == HubConnectionState.Connected) {
      _isTaskCommentsHubConnected.value = true;
      return;
    }

    try {
      await _taskCommentsHubConnection?.start();
      _isTaskCommentsHubConnected.value = true;
      _commentsReconnectAttempts = 0;
      _isCommentsReconnecting = false;
      debugPrint("✅ Task Comments Hub Connection started successfully");
    } catch (e) {
      _isTaskCommentsHubConnected.value = false;
      debugPrint("❌ Error starting Task Comments Hub connection: $e");
      _reconnectTaskCommentsHub();
    }
  }
  
  /// الاتصال بـ Notification Hub
  Future<void> _connectNotificationHub() async {
    if (_notificationHubConnection?.state == HubConnectionState.Connected) {
      _isNotificationHubConnected.value = true;
      return;
    }

    try {
      await _notificationHubConnection?.start();
      _isNotificationHubConnected.value = true;
      _notificationReconnectAttempts = 0;
      _isNotificationReconnecting = false;
      debugPrint("✅ Notification Hub Connection started successfully");

      // الانضمام لمجموعة الإشعارات الخاصة بالمستخدم الحالي
      await _joinUserNotificationGroup();
    } catch (e) {
      _isNotificationHubConnected.value = false;
      debugPrint("❌ Error starting Notification Hub connection: $e");
      _reconnectNotificationHub();
    }
  }

  /// الاتصال بـ Task Hub
  Future<void> _connectTaskHub() async {
    if (_taskHubConnection?.state == HubConnectionState.Connected) {
      _isTaskHubConnected.value = true;
      return;
    }

    try {
      await _taskHubConnection?.start();
      _isTaskHubConnected.value = true;
      debugPrint("✅ Task Hub Connection started successfully");
    } catch (e) {
      _isTaskHubConnected.value = false;
      debugPrint("❌ Error starting Task Hub connection: $e");
      // إعادة المحاولة بعد تأخير فقط إذا لم نتجاوز الحد الأقصى
      if (_taskReconnectAttempts < _maxReconnectAttempts) {
        _taskReconnectAttempts++;
        Timer(const Duration(seconds: 10), () => _connectTaskHub());
      } else {
        debugPrint('❌ تم تجاوز الحد الأقصى لمحاولات الاتصال بـ Task Hub');
      }
    }
  }

  /// إعادة الاتصال بـ Chat Hub
  void _reconnectChatHub() {
    if (_isChatReconnecting || _chatReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isChatReconnecting = true;
    _chatReconnectAttempts++;

    final delay = Duration(seconds: _chatReconnectAttempts * 2);
    _chatReconnectTimer = Timer(delay, () async {
      AppLogger.debug("محاولة إعادة الاتصال بـ Chat Hub (المحاولة $_chatReconnectAttempts)");
      await _connectChatHub();
    });
  }

  /// إعادة الاتصال بـ Task Comments Hub
  void _reconnectTaskCommentsHub() {
    if (_isCommentsReconnecting || _commentsReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isCommentsReconnecting = true;
    _commentsReconnectAttempts++;

    final delay = Duration(seconds: _commentsReconnectAttempts * 2);
    _commentsReconnectTimer = Timer(delay, () async {
      debugPrint("محاولة إعادة الاتصال بـ Task Comments Hub (المحاولة $_commentsReconnectAttempts)");
      await _connectTaskCommentsHub();
    });
  }
  
  /// إعادة الاتصال بـ Notification Hub
  void _reconnectNotificationHub() {
    if (_isNotificationReconnecting || _notificationReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isNotificationReconnecting = true;
    _notificationReconnectAttempts++;

    final delay = Duration(seconds: _notificationReconnectAttempts * 2);
    _notificationReconnectTimer = Timer(delay, () async {
      debugPrint("محاولة إعادة الاتصال بـ Notification Hub (المحاولة $_notificationReconnectAttempts)");
      await _connectNotificationHub();
    });
  }

  /// الانضمام لمجموعة الإشعارات الخاصة بالمستخدم الحالي
  Future<void> _joinUserNotificationGroup() async {
    try {
      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      
      if (userId == null) {
        debugPrint('⚠️ لا يمكن الانضمام لمجموعة الإشعارات: المستخدم غير مسجل الدخول');
        return;
      }
      
      // الانضمام لمجموعة الإشعارات باستخدام _safeInvoke
      await _safeInvoke(_notificationHubConnection, 'JoinUserNotificationGroup', [userId.toString()], "Notification Hub");
      debugPrint('👥 تم الانضمام لمجموعة إشعارات المستخدم: $userId');
      
      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount(userId);
    } catch (e) {
      debugPrint('❌ خطأ في الانضمام لمجموعة الإشعارات: $e');
    }
  }
  
  /// تحديث عدد الإشعارات غير المقروءة
  Future<void> _updateUnreadNotificationsCount(int userId) async {
    try {
      await _safeInvoke(_notificationHubConnection, 'UpdateUnreadCount', [userId.toString()], "Notification Hub");
      debugPrint('🔄 تم طلب تحديث عدد الإشعارات غير المقروءة للمستخدم: $userId');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث عدد الإشعارات غير المقروءة: $e');
    }
  }

  /// تنظيف الموارد
  void _cleanup() {
    // إلغاء وتنظيف المؤقتات
    _chatReconnectTimer?.cancel();
    _chatReconnectTimer = null;
    _commentsReconnectTimer?.cancel();
    _commentsReconnectTimer = null;
    _notificationReconnectTimer?.cancel();
    _notificationReconnectTimer = null;
    _periodicUpdateTimer?.cancel();
    _periodicUpdateTimer = null;

    // إيقاف الاتصالات
    _chatHubConnection?.stop();
    _taskCommentsHubConnection?.stop();

    // مسح القوائم
    _newComments.clear();
    _updatedComments.clear();
    _deletedCommentIds.clear();
    _newTaskMessages.clear();
  }

  // ==================== وظائف المحادثات العامة ====================

  /// الانضمام لمجموعة محادثة
  Future<void> joinChatGroup(String groupId) async {
    try {
      // التحقق من صحة معرف المجموعة
      if (groupId.isEmpty) {
        AppLogger.warning('محاولة انضمام بمعرف مجموعة فارغ');
        return;
      }

      // التأكد من الاتصال
      if (!isChatHubConnected) {
        AppLogger.debug('Chat Hub غير متصل، محاولة الاتصال...');
        await _connectChatHub();

        // انتظار قصير للتأكد من الاتصال
        await Future.delayed(const Duration(milliseconds: 500));

        if (!isChatHubConnected) {
          AppLogger.error('فشل في الاتصال بـ Chat Hub');
          return;
        }
      }

      // محاولة الانضمام مع معالجة الأخطاء باستخدام _safeInvoke
      await _safeInvoke(_chatHubConnection, "JoinChatGroup", [groupId], "Chat Hub");
      AppLogger.debug('تم الانضمام للمجموعة: $groupId');
    } catch (e) {
      AppLogger.error('خطأ في الانضمام للمجموعة $groupId: $e');

      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        AppLogger.debug('تم تجاهل خطأ null subtype في الانضمام للمجموعة');
        return;
      }

      // محاولة إعادة الاتصال في حالة فشل الانضمام
      if (e.toString().contains('connection') || e.toString().contains('Connection')) {
        AppLogger.debug('محاولة إعادة الاتصال بسبب خطأ في الاتصال...');
        _reconnectChatHub();
      }
    }
  }

  /// مغادرة مجموعة محادثة
  Future<void> leaveChatGroup(String groupId) async {
    await _safeInvoke(_chatHubConnection, "LeaveChatGroup", [groupId], "Chat Hub");
    AppLogger.debug('تم مغادرة المجموعة: $groupId');
  }

  /// إرسال رسالة للمجموعة
  Future<void> sendMessageToHub(String groupId, Message message) async {
    await _safeInvoke(_chatHubConnection, "SendMessageToHub", [groupId, message.toJson()], "Chat Hub");
  }

  // ==================== وظائف المهام (TaskHub) ====================

  /// الانضمام لمجموعة مهمة للحصول على إشعارات المرفقات والتحديثات
  Future<void> joinTaskGroupForAttachments(String taskId) async {
    if (!isTaskHubConnected) {
      await _connectTaskHub();
    }
    await _safeInvoke(_taskHubConnection, "JoinTaskGroup", [taskId], "Task Hub");
  }

  /// مغادرة مجموعة مهمة
  Future<void> leaveTaskGroupForAttachments(String taskId) async {
    await _safeInvoke(_taskHubConnection, "LeaveTaskGroup", [taskId], "Task Hub");
    debugPrint('✅ تم مغادرة مجموعة المهمة $taskId في TaskHub');
  }

  // ==================== وظائف رسائل المهام ====================

  /// الانضمام لمحادثة مهمة
  Future<void> joinTaskGroup(String taskId) async {
    if (!isChatHubConnected) {
      await _connectChatHub();
    }
    await _safeInvoke(_chatHubConnection, "JoinTaskGroup", [taskId], "Chat Hub");
  }

  /// مغادرة محادثة مهمة
  Future<void> leaveTaskGroup(String taskId) async {
    await _safeInvoke(_chatHubConnection, "LeaveTaskGroup", [taskId], "Chat Hub");
    AppLogger.debug('تم مغادرة محادثة المهمة: $taskId');
  }

  /// إرسال رسالة مهمة
  Future<void> sendTaskMessage(String taskId, Map<String, dynamic> message) async {
    await _safeInvoke(_chatHubConnection, "SendTaskMessage", [taskId, message], "Chat Hub");
  }

  /// إرسال إشعار الكتابة
  Future<void> sendTypingIndicator(String taskId, String userName) async {
    await _safeInvoke(_chatHubConnection, "UserTyping", [taskId, userName], "Chat Hub");
  }

  /// إرسال إشعار توقف الكتابة
  Future<void> sendStoppedTypingIndicator(String taskId, String userName) async {
    await _safeInvoke(_chatHubConnection, "UserStoppedTyping", [taskId, userName], "Chat Hub");
  }

  // ==================== وظائف المجموعات العامة ====================

  /// إرسال إشعار الكتابة للمجموعة العامة
  Future<void> sendGroupTypingIndicator(String groupId, String userName) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال إشعار الكتابة للمجموعة");
        return;
      }

      await _safeInvoke(_chatHubConnection, "GroupUserTyping", [groupId, userName], "Chat Hub");
      AppLogger.debug('تم إرسال إشعار بدء الكتابة للمجموعة $groupId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار الكتابة للمجموعة: $e');
    }
  }

  /// إرسال إشعار توقف الكتابة للمجموعة العامة
  Future<void> sendGroupStoppedTypingIndicator(String groupId, String userName) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال إشعار توقف الكتابة للمجموعة");
        return;
      }

      await _safeInvoke(_chatHubConnection, "GroupUserStoppedTyping", [groupId, userName], "Chat Hub");
      AppLogger.debug('تم إرسال إشعار توقف الكتابة للمجموعة $groupId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار توقف الكتابة للمجموعة: $e');
    }
  }



  // ==================== وظائف تعليقات المهام ====================

  /// الانضمام لمجموعة تعليقات مهمة
  Future<void> joinTaskCommentsGroup(int taskId) async {
    try {
      if (!isTaskCommentsHubConnected) {
        await _connectTaskCommentsHub();
      }

      // استخدام _safeInvoke مع معالجة أفضل للأخطاء
      await _safeInvoke(_taskCommentsHubConnection, 'JoinTaskCommentsGroup', [taskId.toString()], "Task Comments Hub");
      debugPrint('👥 تم الانضمام لمجموعة تعليقات المهمة: $taskId');
    } catch (e) {
      debugPrint('❌ خطأ في الانضمام لمجموعة التعليقات: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        rethrow;
      }
    }
  }

  /// مغادرة مجموعة تعليقات مهمة
  Future<void> leaveTaskCommentsGroup(int taskId) async {
    await _safeInvoke(_taskCommentsHubConnection, 'LeaveTaskCommentsGroup', [taskId.toString()], "Task Comments Hub");
    debugPrint('👋 تم مغادرة مجموعة تعليقات المهمة: $taskId');
  }

  /// إرسال تعليق جديد
  Future<void> sendTaskComment(int taskId, TaskComment comment) async {
    try {
      if (!isTaskCommentsHubConnected) {
        debugPrint('⚠️ Task Comments Hub غير متصل، لا يمكن إرسال التعليق');
        return;
      }

      await _safeInvoke(_taskCommentsHubConnection, 'SendTaskComment', [taskId.toString(), comment.toJson()], "Task Comments Hub");
      debugPrint('📤 تم إرسال تعليق عبر SignalR: ${comment.id}');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال التعليق: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        rethrow;
      }
    }
  }

  // ==================== وظائف التحديث الفوري ====================

  /// إشعار متحكمات التعليقات بتعليق جديد
  void _notifyCommentsControllers(TaskComment comment) {
    try {
      // تحديث فوري عبر GetX
      Get.find<GetxController>().update();
      debugPrint('تم إشعار المتحكمات بتعليق جديد: ${comment.id}');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم التعليقات: $e');
    }
  }

  /// إشعار متحكمات الرسائل برسالة جديدة
  void _notifyMessagesControllers(Map<String, dynamic> messageData) {
    try {
      // تحديث فوري عبر GetX
      Get.find<GetxController>().update();
      debugPrint('تم إشعار المتحكمات برسالة جديدة: ${messageData['id']}');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الرسائل: $e');
    }
  }
  
  /// إشعار متحكمات الإشعارات بإشعار جديد
  void _notifyNotificationControllers(Map<String, dynamic> notificationData) {
    try {
      // محاولة العثور على متحكم الإشعارات وتحديثه
      if (Get.isRegistered<NotificationsController>()) {
        final notificationsController = Get.find<NotificationsController>();
        notificationsController.addNewNotification(notificationData);
        notificationsController.update();
        // فحص إعدادات المستخدم قبل إظهار إشعار النظام
        bool showSystemNotification = true;
        if (Get.isRegistered<NotificationSettingsController>()) {
          final settingsController = Get.find<NotificationSettingsController>();
          if (!settingsController.pushNotifications) {
            showSystemNotification = false;
          }
        }
        if (showSystemNotification) {
          NotificationsService.showSimpleNotification(
            title: notificationData['title'] ?? 'إشعار جديد',
            body: notificationData['content'] ?? '',
            payload: notificationData['actionUrl'],
          );
        }
        // عرض إشعار منبثق (toast)
        _showNotificationToast(notificationData);
        debugPrint('تم إشعار متحكم الإشعارات بإشعار جديد: ${notificationData['id']}');
      } else {
        debugPrint('متحكم الإشعارات غير مسجل');
      }
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الإشعارات: $e');
    }
  }
  
  /// عرض إشعار منبثق (toast) للإشعار الجديد
  void _showNotificationToast(Map<String, dynamic> notificationData) {
    try {
      final title = notificationData['title'] as String? ?? 'إشعار جديد';
      final content = notificationData['content'] as String? ?? '';
      
      // استخدام GetX لعرض إشعار منبثق
      Get.snackbar(
        title,
        content,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue.shade100,
        colorText: Colors.black87,
        borderRadius: 10,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 4),
        isDismissible: true,
        icon: const Icon(Icons.notifications, color: Colors.blue),
        onTap: (_) {
          // عند النقر على الإشعار، انتقل إلى شاشة الإشعارات
          Get.toNamed('/notifications');
        },
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار منبثق: $e');
    }
  }

  /// إشعار متحكمات المهام ببدء الكتابة
  void _notifyTaskTypingStarted(int taskId, String userName) {
    try {
      if (Get.isRegistered<TaskMessagesController>()) {
        final controller = Get.find<TaskMessagesController>();
        controller.addTypingUser(taskId, userName);
      }
      debugPrint('تم إشعار المتحكمات ببدء الكتابة في المهمة $taskId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المهام بتوقف الكتابة
  void _notifyTaskTypingStopped(int taskId, String userName) {
    try {
      if (Get.isRegistered<TaskMessagesController>()) {
        final controller = Get.find<TaskMessagesController>();
        controller.removeTypingUser(taskId, userName);
      }
      debugPrint('تم إشعار المتحكمات بتوقف الكتابة في المهمة $taskId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المجموعات ببدء الكتابة
  void _notifyGroupTypingStarted(int groupId, String userName) {
    try {
      if (Get.isRegistered<UnifiedChatController>()) {
        final controller = Get.find<UnifiedChatController>();
        controller.addTypingUser(groupId, userName);
      }
      debugPrint('تم إشعار المتحكمات ببدء الكتابة في المجموعة $groupId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المجموعات بتوقف الكتابة
  void _notifyGroupTypingStopped(int groupId, String userName) {
    try {
      if (Get.isRegistered<UnifiedChatController>()) {
        final controller = Get.find<UnifiedChatController>();
        controller.removeTypingUser(groupId, userName);
      }
      debugPrint('تم إشعار المتحكمات بتوقف الكتابة في المجموعة $groupId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  // ==================== وظائف عامة ====================

  /// مسح الأحداث المستقبلة
  void clearEvents() {
    _newComments.clear();
    _updatedComments.clear();
    _deletedCommentIds.clear();
    _newTaskMessages.clear();
    _newMessage.value = null;
  }

  /// إعادة الاتصال يدوياً لجميع الـ Hubs
  Future<void> reconnectAll() async {
    debugPrint('🔄 إعادة الاتصال لجميع الـ Hubs...');

    // إعادة تعيين محاولات الاتصال
    _chatReconnectAttempts = 0;
    _commentsReconnectAttempts = 0;
    _notificationReconnectAttempts = 0;
    _isChatReconnecting = false;
    _isCommentsReconnecting = false;
    _isNotificationReconnecting = false;

    // إلغاء وتنظيف المؤقتات
    _chatReconnectTimer?.cancel();
    _chatReconnectTimer = null;
    _commentsReconnectTimer?.cancel();
    _commentsReconnectTimer = null;
    _notificationReconnectTimer?.cancel();
    _notificationReconnectTimer = null;

    // قطع الاتصالات الحالية
    try {
      await _chatHubConnection?.stop();
      await _taskCommentsHubConnection?.stop();
      await _notificationHubConnection?.stop();
      await _taskHubConnection?.stop();
    } catch (e) {
      debugPrint('⚠️ خطأ في قطع الاتصالات: $e');
    }

    // إعادة تهيئة جميع الاتصالات
    await _initializeConnections();

    debugPrint('✅ تم إعادة الاتصال بجميع الـ Hubs');
  }

  // ==================== وظائف للتوافق مع الكود الحالي ====================

  /// للتوافق مع SignalRService الحالية
  void on(String methodName, Function(dynamic) callback) {
    _chatHubConnection?.on(methodName, (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          // التحقق من صحة البيانات قبل استدعاء callback
          final data = arguments[0];
          if (data is Map<String, dynamic> && data.isNotEmpty) {
            callback(data);
          } else if (data is String && data.isNotEmpty) {
            callback(data);
          } else if (data is num || data is bool) {
            callback(data);
          } else {
            debugPrint('⚠️ تم تجاهل بيانات غير صالحة في $methodName: ${data.runtimeType}');
            callback(null);
          }
        } else {
          callback(null);
        }
      } catch (e) {
        // تصفية أخطاء null subtype المعروفة لتقليل تلوث السجلات
        if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
          debugPrint('❌ خطأ في معالجة SignalR callback لـ $methodName: $e');
        }
        // تجاهل الخطأ وعدم استدعاء callback لتجنب crash
        try {
          callback(null);
        } catch (_) {
          // تجاهل خطأ callback أيضاً
        }
      }
    });
  }

  /// للتوافق مع SignalRService الحالية
  Future<void> invoke(String methodName, {List<Object>? args}) async {
    await _safeInvoke(_chatHubConnection, methodName, args ?? [], "Chat Hub");
  }

  /// دالة مساعدة لاستدعاء SignalR مع معالجة آمنة للأخطاء
  Future<void> _safeInvoke(HubConnection? connection, String methodName, List<Object?> args, String hubName) async {
    try {
      // التحقق من حالة الاتصال
      if (connection?.state != HubConnectionState.Connected) {
        debugPrint('⚠️ $hubName غير متصل. لا يمكن استدعاء $methodName');
        return;
      }

      // التحقق من صحة المعاملات وتنظيفها
      if (args.isEmpty) {
        // استدعاء بدون معاملات
        try {
          await connection?.invoke(methodName);
          debugPrint('✅ تم استدعاء $methodName في $hubName بنجاح (بدون معاملات)');
          return;
        } catch (e) {
          if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
            debugPrint('⚠️ تم تجاهل خطأ null subtype في $methodName - $hubName');
            return;
          }
          rethrow;
        }
      }

      // تصفية وتنظيف المعاملات لتجنب خطأ null subtype
      final safeArgs = <Object>[];
      for (final arg in args) {
        if (arg != null) {
          // التأكد من أن المعامل ليس null وقابل للتسلسل
          if (arg is String || arg is int || arg is double || arg is bool || arg is Map || arg is List) {
            safeArgs.add(arg);
          } else {
            // محاولة تحويل الكائن إلى JSON إذا كان يحتوي على toJson
            try {
              if (arg.runtimeType.toString().contains('toJson')) {
                final jsonData = (arg as dynamic).toJson();
                safeArgs.add(jsonData);
              } else {
                safeArgs.add(arg.toString());
              }
            } catch (_) {
              safeArgs.add(arg.toString());
            }
          }
        }
      }

      // استدعاء الدالة مع المعاملات الآمنة ومعالجة خطأ null subtype
      try {
        await connection?.invoke(methodName, args: safeArgs);
        debugPrint('✅ تم استدعاء $methodName في $hubName بنجاح');
      } catch (invokeError) {
        // معالجة خاصة لخطأ null subtype
        final errorStr = invokeError.toString();
        if (errorStr.contains('Null\' is not a subtype of type \'Object\'') ||
            errorStr.contains('type \'Null\' is not a subtype of type \'Object\'')) {
          debugPrint('⚠️ تم تجاهل خطأ null subtype في $methodName - $hubName');
          return; // تجاهل الخطأ والمتابعة
        }

        // معالجة أخطاء أخرى محتملة
        if (errorStr.contains('Connection is not in the Connected state') ||
            errorStr.contains('WebSocket is not in the OPEN state')) {
          debugPrint('⚠️ مشكلة في الاتصال أثناء استدعاء $methodName - $hubName');
          return;
        }

        rethrow; // إعادة رمي الخطأ إذا لم يكن من الأخطاء المعروفة
      }

    } catch (e) {
      final errorMessage = e.toString();

      // معالجة خاصة لخطأ null subtype - تجاهل تماماً لتقليل التلوث
      if (errorMessage.contains('Null\' is not a subtype of type \'Object\'') ||
          errorMessage.contains('type \'Null\' is not a subtype of type \'Object\'')) {
        // تجاهل الخطأ تماماً - هذا خطأ معروف في مكتبة SignalR ولا يؤثر على الوظائف
        return;
      }

      // معالجة أخطاء الاتصال
      if (errorMessage.contains('Connection is not in the Connected state') ||
          errorMessage.contains('WebSocket is not in the OPEN state') ||
          errorMessage.contains('Connection closed')) {
        debugPrint('⚠️ مشكلة اتصال في $hubName - سيتم إعادة المحاولة تلقائياً');
        return;
      }

      // تسجيل الأخطاء المهمة فقط
      debugPrint('❌ خطأ مهم في استدعاء $methodName في $hubName: $errorMessage');

        // محاولة ثانية مع معاملات مبسطة
        try {
          final simplifiedArgs = args
              .where((arg) => arg != null && arg.toString().isNotEmpty)
              .map((arg) => arg.toString())
              .toList();

          if (simplifiedArgs.isNotEmpty && connection?.state == HubConnectionState.Connected) {
            debugPrint('🔄 محاولة ثانية مع معاملات مبسطة: $simplifiedArgs');
            await connection?.invoke(methodName, args: simplifiedArgs.cast<Object>());
            debugPrint('✅ نجحت المحاولة الثانية لاستدعاء $methodName في $hubName');
          } else {
            debugPrint('⚠️ لا توجد معاملات صالحة للمحاولة الثانية');
          }
        } catch (retryError) {
          debugPrint('❌ فشلت المحاولة الثانية لاستدعاء $methodName: $retryError');
        }
        return;
      }

      // عدم إعادة رمي الخطأ لتجنب crash التطبيق
      debugPrint('⚠️ تم تجاهل الخطأ لتجنب crash التطبيق');
    }
  }


