import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/chat_group_models.dart';
import '../../routes/app_routes.dart';

/// @deprecated استخدم UnifiedChatDetailScreen بدلاً من ذلك
/// تم استبداله بشاشة المحادثة الموحدة
/// انظر: lib/screens/chat/unified_chat_detail_screen.dart
@Deprecated('Use UnifiedChatDetailScreen instead')
class ChatScreen extends StatelessWidget {
  final ChatGroup group;

  const ChatScreen({super.key, required this.group});

  @override
  Widget build(BuildContext context) {
    // إعادة توجيه إلى شاشة المحادثة الموحدة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.offNamed(
        AppRoutes.unifiedChatDetail,
        arguments: {'chatGroup': group},
      );
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Redirecting...'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Redirecting to unified chat screen...',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              'This screen is deprecated',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
