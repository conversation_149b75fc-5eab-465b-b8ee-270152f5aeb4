class Validators {
  // Email validation
  static bool isValidEmail(String email) {
    final emailRegExp = RegExp(
      r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+',
    );
    return emailRegExp.hasMatch(email);
  }

  // Password validation (at least 6 characters, with at least one letter and one number)
  static bool isValidPassword(String password) {
    final passwordRegExp = RegExp(
      r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$',
    );
    return passwordRegExp.hasMatch(password);
  }

  // Name validation (at least 2 characters, only letters, spaces, and hyphens)
  static bool isValidName(String name) {
    final nameRegExp = RegExp(
      r'^[a-zA-Z\s\-]{2,}$',
    );
    return nameRegExp.hasMatch(name);
  }

  // Phone number validation
  static bool isValidPhoneNumber(String phoneNumber) {
    final phoneRegExp = RegExp(
      r'^\+?[0-9]{10,15}$',
    );
    return phoneRegExp.hasMatch(phoneNumber);
  }

  // URL validation
  static bool isValidUrl(String url) {
    final urlRegExp = RegExp(
      r'^(http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(\/\S*)?$',
    );
    return urlRegExp.hasMatch(url);
  }

  // Empty or null check
  static bool isNotEmpty(String? value) {
    return value != null && value.trim().isNotEmpty;
  }

  // Minimum length check
  static bool hasMinLength(String value, int minLength) {
    return value.length >= minLength;
  }

  // Maximum length check
  static bool hasMaxLength(String value, int maxLength) {
    return value.length <= maxLength;
  }

  // Numeric check
  static bool isNumeric(String value) {
    final numericRegExp = RegExp(r'^[0-9]+$');
    return numericRegExp.hasMatch(value);
  }

  // Decimal number check
  static bool isDecimal(String value) {
    final decimalRegExp = RegExp(r'^\d+(\.\d+)?$');
    return decimalRegExp.hasMatch(value);
  }

  // Date format validation (YYYY-MM-DD)
  static bool isValidDateFormat(String date) {
    final dateRegExp = RegExp(
      r'^\d{4}-\d{2}-\d{2}$',
    );
    return dateRegExp.hasMatch(date);
  }

  // Check if a string contains only alphanumeric characters
  static bool isAlphanumeric(String value) {
    final alphanumericRegExp = RegExp(r'^[a-zA-Z0-9]+$');
    return alphanumericRegExp.hasMatch(value);
  }
}
