// /// لوحة تحكم تقرير المستخدمين البسيط
// /// 
// /// شاشة بسيطة لعرض تقرير المستخدمين

// library;

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_application_2/professional_reports/core.dart';
// import 'package:flutter_application_2/professional_reports/reportarabic.dart';
// import 'package:flutter_application_2/professional_reports/user_reports.dart';
// import 'package:get/get.dart';
// import 'package:flutter/services.dart';
// import '../../models/user_model.dart';
// import '../../services/api/user_api_service.dart';
// import 'package:flutter_application_2/professional_reports/models/report_data_models.dart' as report_models;
// import 'package:pdf/widgets.dart' as pw;

// // استيراد التقارير المحسنة الجديدة
// import 'package:flutter_application_2/professional_reports/advanced_report_service.dart';


// // تأكد من استيراد الشاشة التجريبية

// // import '../widgets/report_preview_widget.dart'; // تم تعليق هذا الاستيراد لأننا لن نستخدمه

// /// تحكم في لوحة التقارير
// class ReportsDashboardController extends GetxController {
//   /// خدمة API للمستخدمين
//   final UserApiService _userApiService = UserApiService();
  
//   /// حالة التحميل
//   final isLoading = false.obs;
  
//   /// قائمة المستخدمين
//   final users = <User>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     _loadUsers();
//   }

//   /// تحميل قائمة المستخدمين
//   Future<void> _loadUsers() async {
//     try {
//       isLoading.value = true;
      
//       // جلب المستخدمين من API
//       final usersList = await _userApiService.getAllUsers();
//       users.assignAll(usersList);
      
//     } catch (e) {
//       Get.snackbar(
//         'خطأ',
//         'فشل في تحميل المستخدمين: $e',
//         backgroundColor: Colors.red,
//         colorText: Colors.white,
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   // /// عرض تقرير المستخدمين بصيغة PDF مع معاينة
//   // Future<void> showUsersReport() async {
//   //   try {
//   //     // عرض مؤشر التحميل
//   //     Get.dialog(
//   //       const Center(
//   //         child: Card(
//   //           child: Padding(
//   //             padding: EdgeInsets.all(20),
//   //             child: Column(
//   //               mainAxisSize: MainAxisSize.min,
//   //               children: [
//   //                 CircularProgressIndicator(),
//   //                 SizedBox(height: 16),
//   //                 Text(
//   //                   'جاري إنشاء التقرير...',
//   //                   style: TextStyle(fontSize: 16),
//   //                 ),
//   //               ],
//   //             ),
//   //           ),
//   //         ),
//   //       ),
//   //       barrierDismissible: false,
//   //     );
      
//   //     // إنشاء مستند PDF
//   //     final pdf = await _generateUsersPdfReport();
//   //     final pdfData = await pdf.save();
      
//   //     // إغلاق مؤشر التحميل
//   //     Get.back();
      
//   //     // اسم الملف مع الطابع الزمني
//   //     final fileName = 'تقرير_المستخدمين_${DateTime.now().millisecondsSinceEpoch}.pdf';
      
//   //     // فتح شاشة معاينة PDF
//   //     Get.to(
//   //       () => PdfPreviewScreen(
//   //         pdfData: pdfData,
//   //         fileName: fileName,
//   //       ),
//   //       transition: Transition.rightToLeft,
//   //       duration: const Duration(milliseconds: 300),
//   //     );
      
//   //   } catch (e) {
//   //     // إغلاق مؤشر التحميل في حالة الخطأ
//   //     if (Get.isDialogOpen ?? false) {
//   //       Get.back();
//   //     }
      
//   //     Get.snackbar(
//   //       'خطأ',
//   //       'فشل في إنشاء التقرير: $e',
//   //       backgroundColor: Colors.red,
//   //       colorText: Colors.white,
//   //       icon: const Icon(Icons.error, color: Colors.white),
//   //     );
//   //   }
//   // }

//   /// تحديث البيانات
//   Future<void> refreshData() async {
//     await _loadUsers();
//   }

//   // دالة مساعدة لتوليد تقرير المهام 2 ببيانات تجريبية
// Future<pw.Document> generateTask2ReportPdf() async {
//   // بيانات تجريبية
//   final tasks = <report_models.TaskReportData>[
//     report_models.TaskReportData(
//       taskId: 1,
//       title: 'مهمة 1',
//       status: 'مكتملة',
//       priority: 'عالية',
//       completionPercentage: 100,
//       department: null, // إذا كان الحقل يقبل null
//       createdAt: DateTime.now().subtract(const Duration(days: 2)),
//       creator: report_models.UserInfo(
//         id: 1,
//         name: 'أحمد',
//         email: '<EMAIL>',
//         role: 1, // رقم بدل نص
//       ),
//     ),
//     report_models.TaskReportData(
//       taskId: 2,
//       title: 'مهمة 2',
//       status: 'قيد التنفيذ',
//       priority: 'متوسطة',
//       completionPercentage: 60,
//       department: null, // إذا كان الحقل يقبل null
//       createdAt: DateTime.now().subtract(const Duration(days: 1)),
//       creator: report_models.UserInfo(
//         id: 2,
//         name: 'سارة',
//         email: '<EMAIL>',
//         role: 2, // رقم بدل نص
//       ),
//     ),
//   ];
//   final stats = report_models.ReportStatistics(
//     totalTasks: 2,
//     completedTasks: 1,
//     inProgressTasks: 1,
//     overdueTasks: 0,
//     cancelledTasks: 0,
//     averageCompletion: 80.0,
//     totalEstimatedTime: 10,
//     totalActualTime: 8,
//     activeUsers: 2,
//     activeDepartments: 2,
//   );
//   final filters = report_models.ReportFilters(
//     startDate: DateTime.now().subtract(const Duration(days: 7)),
//     endDate: DateTime.now(),
//     statuses: ['مكتملة', 'قيد التنفيذ'],
//     priorities: ['عالية', 'متوسطة'],
//     includeDeleted: false,
//     completedOnly: false,
//     overdueOnly: false,
//   );
//   return await TaskReportPdf.generateTaskReport(
//     tasks: tasks,
//     stats: stats,
//     filters: filters,
//   );
// }

//   /// إنشاء التقرير المحسن للمهام
//   Future<pw.Document> generateEnhancedTasksReport() async {
//     return await generateEnhancedTasksReportPdf(
//       onProgress: (message) {
//         // يمكن إضافة مؤشر تقدم هنا
//         _logProgress('تقدم التقرير: $message');
//       },
//       filters: report_models.ReportFilters(
//         startDate: DateTime.now().subtract(const Duration(days: 365)),
//         endDate: DateTime.now(),
//         includeDeleted: false,
//         overdueOnly: false,
//       ),
//     );
//   }

//   /// إنشاء تقرير المقارنة بين فترتين
//   Future<pw.Document> generateComparisonReport() async {
//     final now = DateTime.now();
//     final sixMonthsAgo = DateTime(now.year, now.month - 6, now.day);
//     final threeMonthsAgo = DateTime(now.year, now.month - 3, now.day);

//     return await AdvancedReportService.generateComparisonReport(
//       startDate1: sixMonthsAgo,
//       endDate1: threeMonthsAgo,
//       startDate2: threeMonthsAgo,
//       endDate2: now,
//       onProgress: (message) {
//         _logProgress('تقدم تقرير المقارنة: $message');
//       },
//     );
//   }

//   /// إنشاء تقرير أداء الأقسام
//   Future<pw.Document> generateDepartmentPerformanceReport() async {
//     return await AdvancedReportService.generateDepartmentPerformanceReport(
//       onProgress: (message) {
//         _logProgress('تقدم تقرير الأقسام: $message');
//       },
//     );
//   }

//   /// إنشاء تقرير الأداء الشخصي (للمستخدم الحالي)
//   Future<pw.Document> generateUserPerformanceReport() async {
//     // يمكن الحصول على معرف المستخدم الحالي من نظام المصادقة
//     const currentUserId = 1; // مؤقت - يجب استبداله بالمعرف الفعلي
    
//     return await AdvancedReportService.generateUserPerformanceReport(
//       userId: currentUserId,
//       onProgress: (message) {
//         _logProgress('تقدم تقرير الأداء الشخصي: $message');
//       },
//     );
//   }

//   /// إنشاء التقرير المصحح للمهام (بدلاً من الأصلي)
//   Future<pw.Document> generateFixedTasksReport() async {
//     return await fixed_report.generateTasksReportPdf(
//       onProgress: (message) {
//         _logProgress('تقدم التقرير المصحح: $message');
//       },
//     );
//   }

//   /// تسجيل رسائل التقدم
//   void _logProgress(String message) {
//     // يمكن استبدال هذا بنظام تسجيل أكثر تقدماً في المستقبل
//     if (kDebugMode) {
//       debugPrint('[تقارير] $message');
//     }
//   }
//  }

// /// شاشة لوحة تحكم التقارير
// class ReportsDashboardScreen extends StatelessWidget {
//   const ReportsDashboardScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(ReportsDashboardController());

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(
//           'لوحة تحكم التقارير',
//           style: TextStyle(
//             fontWeight: FontWeight.bold,
//             color: Colors.white,
//           ),
//         ),
//         backgroundColor: Colors.blue,
//         elevation: 0,
//         actions: [
//           // زر التحديث
//           IconButton(
//             icon: const Icon(Icons.refresh, color: Colors.white),
//             onPressed: controller.refreshData,
//             tooltip: 'تحديث البيانات',
//           ),
//         ],
//       ),
//       body: Obx(() {
//         if (controller.isLoading.value) {
//           return const Center(
//             child: CircularProgressIndicator(),
//           );
//         }

//         return SingleChildScrollView(
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
           
            
//               // بطاقة التقارير المتاحة
//               _buildAvailableReportsCard(controller),
//             ],
//           ),
//         );
//       }),
//     );
//   }


  
//   /// بناء بطاقة التقارير المتاحة
//   Widget _buildAvailableReportsCard(ReportsDashboardController controller) {
//     return Card(
//       elevation: 4,
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text(
//               'التقارير المتاحة',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.blue,
//               ),
//             ),
//             const SizedBox(height: 16),
            
//             // تقرير المستخدمين
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.blue,
//                 child: Icon(Icons.people, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير المستخدمين',
//                 style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               subtitle: const Text(
//                 'عرض قائمة شاملة بجميع المستخدمين في النظام',
//               ),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   // await controller.showUsersReport();
//                   await showPdfReport(generatePdf: generateUsersPdfReport, fileNamePrefix: 'تقرير_المستخدمين');
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.blue,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
            
//             // تقرير المهام المصحح
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.green,
//                 child: Icon(Icons.task, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير المهام (مصحح)',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('تقرير المهام مع إصلاح الأخطاء وتحسينات الأداء'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateFixedTasksReport,
//                     fileNamePrefix: 'تقرير_المهام_مصحح',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.green,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
            
//              // تقرير المهام المحسن
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.teal,
//                 child: Icon(Icons.analytics, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير المهام المحسن',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('تقرير شامل مع إحصائيات متقدمة ومؤشرات أداء'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateEnhancedTasksReport,
//                     fileNamePrefix: 'تقرير_المهام_محسن',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.teal,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
            
//             // تقرير المقارنة بين فترتين
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.purple,
//                 child: Icon(Icons.compare_arrows, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير المقارنة',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('مقارنة الأداء بين فترتين زمنيتين مختلفتين'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateComparisonReport,
//                     fileNamePrefix: 'تقرير_المقارنة',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.purple,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),

//             const Divider(),
            
//             // تقرير أداء الأقسام
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.indigo,
//                 child: Icon(Icons.business, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير أداء الأقسام',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('تحليل أداء جميع الأقسام ومقارنتها'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateDepartmentPerformanceReport,
//                     fileNamePrefix: 'تقرير_أداء_الأقسام',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.indigo,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
            
//             // تقرير الأداء الشخصي
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.amber,
//                 child: Icon(Icons.person_pin, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير الأداء الشخصي',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('تحليل أداء المستخدم الحالي مع تقييم شامل'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateUserPerformanceReport,
//                     fileNamePrefix: 'تقرير_الأداء_الشخصي',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.amber,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
            
//             // تقرير المهام الأصلي (للمقارنة)
//             ListTile(
//               leading: const CircleAvatar(
//                 backgroundColor: Colors.grey,
//                 child: Icon(Icons.assignment, color: Colors.white),
//               ),
//               title: const Text(
//                 'تقرير المهام الأصلي',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               subtitle: const Text('التقرير الأصلي للمقارنة مع التقارير المحسنة'),
//               trailing: ElevatedButton(
//                 onPressed: () async {
//                   await showPdfReport(
//                     generatePdf: controller.generateTask2ReportPdf,
//                     fileNamePrefix: 'تقرير_المهام_الأصلي',
//                   );
//                 },
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Colors.grey,
//                   foregroundColor: Colors.white,
//                 ),
//                 child: const Text('عرض التقرير'),
//               ),
//             ),
            
//             const Divider(),
//             // رابط لتجربة تقرير اللغة العربية
//             Builder(
//               builder: (context) => ListTile(
//                 leading: const CircleAvatar(
//                   backgroundColor: Colors.deepOrange,
//                   child: Icon(Icons.language, color: Colors.white),
//                 ),
//                 title: const Text(
//                   'تجربة اللغة العربية في PDF',
//                   style: TextStyle(fontWeight: FontWeight.bold),
//                 ),
//                 subtitle: const Text('عرض تقرير تجريبي لاختبار دعم النص العربي في PDF'),
//                 trailing: ElevatedButton(
//                   onPressed: () async {
//                 showPdfReport(generatePdf:  RreportAreab, fileNamePrefix: 'تقرير_اللغة_العربية');
//                     // استدعاء الدالة التي تعرض تقرير اللغة العربية 
//                     await  RreportAreab();
//                   },
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Colors.deepOrange,
//                     foregroundColor: Colors.white,
//                   ),
//                   child: const Text('تجربة الآن'),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

