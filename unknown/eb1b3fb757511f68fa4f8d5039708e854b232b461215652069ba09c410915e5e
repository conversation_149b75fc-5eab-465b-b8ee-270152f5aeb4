import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_tags_controller.dart';
import 'package:flutter_application_2/models/archive_models.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/screens/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';
import 'package:flutter_application_2/services/unified_permission_service.dart';

import 'package:flutter_colorpicker/flutter_colorpicker.dart';

/// شاشة إدارة وسوم الأرشيف
class TagManagementScreen extends StatefulWidget {
  const TagManagementScreen({super.key});

  @override
  State<TagManagementScreen> createState() => _TagManagementScreenState();
}

class _TagManagementScreenState extends State<TagManagementScreen> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final ArchiveTagsController _controller = Get.find<ArchiveTagsController>();


  // حالة التحميل
  bool _isLoading = false;

  // حالة التحرير
  ArchiveTag? _selectedTag;
  bool _isEditing = false;

  // متحكمات النموذج
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  Color _selectedColor = Colors.blue;

  @override
  void initState() {
    super.initState();
    _loadTags();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadTags() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _controller.loadAllTags();
    } catch (e) {
      debugPrint('خطأ في تحميل الوسوم: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectTag(ArchiveTag tag) {
    setState(() {
      _selectedTag = tag;
      _isEditing = true;
      _nameController.text = tag.name;
      _descriptionController.text = tag.description ?? '';
      _selectedColor = _getColorFromHex(tag.color ?? '#3498db');
    });
  }

  void _resetForm() {
    setState(() {
      _selectedTag = null;
      _isEditing = false;
      _nameController.clear();
      _descriptionController.clear();
      _selectedColor = Colors.blue;
    });
  }

  Future<void> _saveTag() async {
    if (_nameController.text.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال اسم الوسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // تحويل اللون إلى سلسلة نصية سداسية عشرية بطريقة آمنة
      final colorInt = _selectedColor.toARGB32();
      final hexString = colorInt.toRadixString(16).padLeft(8, '0');
      // استخراج الجزء الخاص بالألوان RGB فقط (بدون قناة الشفافية)
      final colorHex = '#${hexString.substring(2)}';

      if (_isEditing && _selectedTag != null) {
        // تحديث وسم موجود
        final updatedTag = ArchiveTag(
          id: _selectedTag!.id,
          name: _nameController.text,
          description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
          color: colorHex,
          createdBy: _selectedTag!.createdBy,
          createdAt: _selectedTag!.createdAt,
          isActive: _selectedTag!.isActive,
        );

        await _controller.updateTag(_selectedTag!.id, updatedTag);
      } else {
        // إنشاء وسم جديد
        final newTag = ArchiveTag(
          id: 0, // سيتم تعيينه من قبل الخادم
          name: _nameController.text,
          description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
          color: colorHex,
          createdBy: 1, // معرف المستخدم الحالي
          createdAt: DateTime.now().millisecondsSinceEpoch,
          isActive: true,
        );

        await _controller.createTag(newTag);
      }

      // إعادة تحميل الوسوم
      await _loadTags();

      // إعادة تعيين النموذج
      _resetForm();

      Get.snackbar(
        'تم الحفظ',
        'تم حفظ الوسم بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success.withAlpha(178),
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في حفظ الوسم: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ الوسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteTag(ArchiveTag tag) async {
    // تبسيط - لا نتحقق من الاستخدام حالياً
    // يمكن إضافة هذا التحقق لاحقاً

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف وسم "${tag.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _controller.deleteTag(tag.id);

        // إعادة تحميل الوسوم
        await _loadTags();

        // إعادة تعيين النموذج إذا كان الوسم المحذوف هو المحدد
        if (_selectedTag?.id == tag.id) {
          _resetForm();
        }

        Get.snackbar(
          'تم الحذف',
          'تم حذف الوسم بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withAlpha(178),
          colorText: Colors.white,
        );
      } catch (e) {
        debugPrint('خطأ في حذف الوسم: $e');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف الوسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'إدارة وسوم الأرشيف',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadTags,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : isSmallScreen
              ? _buildSmallScreenLayout()
              : _buildLargeScreenLayout(),
      floatingActionButton: _permissionService.canManageCategories()
          ? FloatingActionButton(
              onPressed: () {
                _resetForm();
                _showTagFormDialog();
              },
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildLargeScreenLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // قائمة الوسوم
        Expanded(
          flex: 2,
          child: Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوسوم',
                    style: AppStyles.headline6,
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _buildTagsList(),
                  ),
                ],
              ),
            ),
          ),
        ),

        // نموذج الوسم
        Expanded(
          flex: 3,
          child: Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildTagForm(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSmallScreenLayout() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الوسوم',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildTagsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsList() {
    return Obx(() {
      if (_controller.allTags.isEmpty) {
        return Center(
          child: Text(
            'لا توجد وسوم',
            style: AppStyles.subtitle1.copyWith(color: Colors.grey),
          ),
        );
      }

      return ListView.builder(
        itemCount: _controller.allTags.length,
        itemBuilder: (context, index) {
          final tag = _controller.allTags[index];
          return ListTile(
            title: Text(tag.name),
            subtitle: tag.description != null
                ? Text(
                    tag.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            leading: CircleAvatar(
              backgroundColor: _getColorFromHex(tag.color ?? '#3498db').withAlpha(51),
              child: Icon(
                Icons.local_offer,
                color: _getColorFromHex(tag.color ?? '#3498db'),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_permissionService.canManageCategories())
                  IconButton(
                    icon: const Icon(Icons.edit),
                    tooltip: 'تحرير',
                    onPressed: () {
                      _selectTag(tag);
                      if (ResponsiveHelper.isSmallScreen(context)) {
                        _showTagFormDialog();
                      }
                    },
                  ),
                if (_permissionService.canManageCategories())
                  IconButton(
                    icon: const Icon(Icons.delete),
                    tooltip: 'حذف',
                    onPressed: () => _deleteTag(tag),
                  ),
              ],
            ),
            onTap: () {
              _selectTag(tag);
              if (ResponsiveHelper.isSmallScreen(context)) {
                _showTagFormDialog();
              }
            },
          );
        },
      );
    });
  }

  Widget _buildTagForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _isEditing ? 'تحرير الوسم' : 'إضافة وسم جديد',
          style: AppStyles.headline6,
        ),
        const SizedBox(height: 24),

        // اسم الوسم
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'اسم الوسم *',
            hintText: 'أدخل اسم الوسم',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // وصف الوسم
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف الوسم',
            hintText: 'أدخل وصف الوسم',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        // لون الوسم
        Text(
          'لون الوسم',
          style: AppStyles.subtitle1,
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _showColorPicker,
          child: Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              color: _selectedColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
          ),
        ),
        const SizedBox(height: 24),

        // أزرار الإجراءات
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: _resetForm,
              child: const Text('إلغاء'),
            ),
            const SizedBox(width: 16),
            if (_permissionService.canManageCategories())
              ElevatedButton(
                onPressed: _saveTag,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
                child: Text(_isEditing ? 'تحديث' : 'إضافة'),
              ),
          ],
        ),
      ],
    );
  }

  void _showTagFormDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_isEditing ? 'تحرير الوسم' : 'إضافة وسم جديد'),
        content: SingleChildScrollView(
          child: _buildTagForm(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          if (_permissionService.canManageCategories())
            ElevatedButton(
              onPressed: () {
                if (mounted) {
                  _saveTag();
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text(_isEditing ? 'تحديث' : 'إضافة'),
            ),
        ],
      ),
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر لون الوسم'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _selectedColor,
            onColorChanged: (color) {
              setState(() {
                _selectedColor = color;
              });
            },
            pickerAreaHeightPercent: 0.8,
            enableAlpha: false,
            displayThumbColor: true,
            labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hex],
            paletteType: PaletteType.hsv,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  Color _getColorFromHex(String hexColor) {
    try {
      if (hexColor.startsWith('#')) {
        String hexValue = hexColor.substring(1);
        // إضافة FF للشفافية إذا كان الطول 6 أحرف فقط (بدون قناة ألفا)
        if (hexValue.length == 6) {
          hexValue = 'FF$hexValue';
        }
        return Color(int.parse('0x$hexValue'));
      }
      return Colors.blue;
    } catch (e) {
      debugPrint('خطأ في تحويل اللون: $e');
      return Colors.blue;
    }
  }
}
