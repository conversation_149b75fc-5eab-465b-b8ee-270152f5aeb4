using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Attributes;

namespace webApi.Controllers
{
    /// <summary>
    /// تحكم في قوالب الصلاحيات
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PermissionTemplatesController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<PermissionTemplatesController> _logger;

        public PermissionTemplatesController(TasksDbContext context, ILogger<PermissionTemplatesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع قوالب الصلاحيات
        /// </summary>
        [HttpGet]
        [HasPermission("permissions.view")]
        public async Task<ActionResult<IEnumerable<PermissionTemplate>>> GetPermissionTemplates()
        {
            try
            {
                var templates = await _context.PermissionTemplates
                    .Include(t => t.CreatedByNavigation)
                    .Include(t => t.UpdatedByNavigation)
                    .Include(t => t.TemplateItems)
                        .ThenInclude(ti => ti.Permission)
                    .Where(t => t.IsActive)
                    .OrderBy(t => t.Type)
                    .ThenBy(t => t.Name)
                    .ToListAsync();

                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على قوالب الصلاحيات");
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على قالب صلاحيات محدد
        /// </summary>
        [HttpGet("{id}")]
        [HasPermission("permissions.view")]
        public async Task<ActionResult<PermissionTemplate>> GetPermissionTemplate(int id)
        {
            try
            {
                var template = await _context.PermissionTemplates
                    .Include(t => t.CreatedByNavigation)
                    .Include(t => t.UpdatedByNavigation)
                    .Include(t => t.TemplateItems)
                        .ThenInclude(ti => ti.Permission)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (template == null)
                {
                    return NotFound(new { message = "القالب غير موجود" });
                }

                return Ok(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على القالب {TemplateId}", id);
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// إنشاء قالب صلاحيات جديد
        /// </summary>
        [HttpPost]
        [HasPermission("permissions.manage")]
        public async Task<ActionResult<PermissionTemplate>> CreatePermissionTemplate(CreatePermissionTemplateRequest request)
        {
            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var template = new PermissionTemplate
                {
                    Name = request.Name,
                    Description = request.Description,
                    Type = request.Type,
                    Color = request.Color,
                    Icon = request.Icon,
                    IsActive = true,
                    IsDefault = false,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = currentTime
                };

                _context.PermissionTemplates.Add(template);
                await _context.SaveChangesAsync();

                // إضافة عناصر القالب
                foreach (var permissionId in request.PermissionIds)
                {
                    var templateItem = new PermissionTemplateItem
                    {
                        TemplateId = template.Id,
                        PermissionId = permissionId,
                        IsEnabled = true
                    };
                    _context.PermissionTemplateItems.Add(templateItem);
                }

                await _context.SaveChangesAsync();

                // إعادة تحميل القالب مع البيانات المرتبطة
                var createdTemplate = await _context.PermissionTemplates
                    .Include(t => t.CreatedByNavigation)
                    .Include(t => t.TemplateItems)
                        .ThenInclude(ti => ti.Permission)
                    .FirstOrDefaultAsync(t => t.Id == template.Id);

                _logger.LogInformation("تم إنشاء قالب صلاحيات جديد: {TemplateName} بواسطة المستخدم {UserId}", 
                    template.Name, request.CreatedBy);

                return CreatedAtAction(nameof(GetPermissionTemplate), new { id = template.Id }, createdTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قالب الصلاحيات");
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تحديث قالب صلاحيات
        /// </summary>
        [HttpPut("{id}")]
        [HasPermission("permissions.manage")]
        public async Task<IActionResult> UpdatePermissionTemplate(int id, UpdatePermissionTemplateRequest request)
        {
            try
            {
                var template = await _context.PermissionTemplates
                    .Include(t => t.TemplateItems)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (template == null)
                {
                    return NotFound(new { message = "القالب غير موجود" });
                }

                if (template.IsDefault)
                {
                    return BadRequest(new { message = "لا يمكن تعديل القوالب الافتراضية" });
                }

                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // تحديث البيانات الأساسية
                if (!string.IsNullOrEmpty(request.Name))
                    template.Name = request.Name;
                
                if (request.Description != null)
                    template.Description = request.Description;
                
                if (!string.IsNullOrEmpty(request.Color))
                    template.Color = request.Color;
                
                if (!string.IsNullOrEmpty(request.Icon))
                    template.Icon = request.Icon;
                
                if (request.IsActive.HasValue)
                    template.IsActive = request.IsActive.Value;

                template.UpdatedAt = currentTime;
                template.UpdatedBy = request.UpdatedBy;

                // تحديث الصلاحيات إذا تم تمريرها
                if (request.PermissionIds != null)
                {
                    // حذف العناصر الحالية
                    _context.PermissionTemplateItems.RemoveRange(template.TemplateItems);

                    // إضافة العناصر الجديدة
                    foreach (var permissionId in request.PermissionIds)
                    {
                        var templateItem = new PermissionTemplateItem
                        {
                            TemplateId = template.Id,
                            PermissionId = permissionId,
                            IsEnabled = true
                        };
                        _context.PermissionTemplateItems.Add(templateItem);
                    }
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث قالب الصلاحيات {TemplateId} بواسطة المستخدم {UserId}", 
                    id, request.UpdatedBy);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث قالب الصلاحيات {TemplateId}", id);
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// حذف قالب صلاحيات
        /// </summary>
        [HttpDelete("{id}")]
        [HasPermission("permissions.manage")]
        public async Task<IActionResult> DeletePermissionTemplate(int id)
        {
            try
            {
                var template = await _context.PermissionTemplates.FindAsync(id);

                if (template == null)
                {
                    return NotFound(new { message = "القالب غير موجود" });
                }

                if (template.IsDefault)
                {
                    return BadRequest(new { message = "لا يمكن حذف القوالب الافتراضية" });
                }

                template.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف قالب الصلاحيات {TemplateId}", id);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف قالب الصلاحيات {TemplateId}", id);
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }

        /// <summary>
        /// تطبيق قالب على مستخدم
        /// </summary>
        [HttpPost("apply")]
        [HasPermission("permissions.manage")]
        public async Task<IActionResult> ApplyTemplateToUser(ApplyTemplateToUserRequest request)
        {
            try
            {
                var template = await _context.PermissionTemplates
                    .Include(t => t.TemplateItems)
                    .FirstOrDefaultAsync(t => t.Id == request.TemplateId && t.IsActive);

                if (template == null)
                {
                    return NotFound(new { message = "القالب غير موجود أو غير نشط" });
                }

                var user = await _context.Users.FindAsync(request.UserId);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إزالة الصلاحيات الحالية إذا كان مطلوباً
                if (request.ReplaceExisting)
                {
                    var existingPermissions = await _context.UserPermissions
                        .Where(up => up.UserId == request.UserId && !up.IsDeleted)
                        .ToListAsync();

                    foreach (var permission in existingPermissions)
                    {
                        permission.IsDeleted = true;
                    }
                }

                // إضافة الصلاحيات الجديدة من القالب
                foreach (var templateItem in template.TemplateItems.Where(ti => ti.IsEnabled))
                {
                    // التحقق من عدم وجود الصلاحية مسبقاً
                    var existingPermission = await _context.UserPermissions
                        .FirstOrDefaultAsync(up => up.UserId == request.UserId && 
                                                  up.PermissionId == templateItem.PermissionId && 
                                                  !up.IsDeleted);

                    if (existingPermission == null)
                    {
                        var userPermission = new UserPermission
                        {
                            UserId = request.UserId,
                            PermissionId = templateItem.PermissionId,
                            GrantedBy = request.AppliedBy,
                            GrantedAt = currentTime,
                            IsActive = true,
                            IsDeleted = false,
                            CreatedAt = currentTime
                        };

                        _context.UserPermissions.Add(userPermission);
                    }
                    else if (existingPermission.IsDeleted)
                    {
                        // إعادة تفعيل الصلاحية المحذوفة
                        existingPermission.IsDeleted = false;
                        existingPermission.IsActive = true;
                        existingPermission.GrantedBy = request.AppliedBy;
                        existingPermission.GrantedAt = currentTime;
                    }
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تطبيق قالب الصلاحيات {TemplateId} على المستخدم {UserId} بواسطة {AppliedBy}", 
                    request.TemplateId, request.UserId, request.AppliedBy);

                return Ok(new { message = "تم تطبيق القالب بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تطبيق قالب الصلاحيات");
                return StatusCode(500, new { message = "خطأ في الخادم", error = ex.Message });
            }
        }
    }
}
