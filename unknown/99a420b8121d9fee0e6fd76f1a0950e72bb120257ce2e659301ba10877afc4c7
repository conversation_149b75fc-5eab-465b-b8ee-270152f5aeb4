import 'package:get/get.dart';
import 'dashboard_repository.dart';

/// متحكم لوحة المعلومات الموحد
class UnifiedDashboardController extends GetxController {
  static UnifiedDashboardController get instance => Get.find<UnifiedDashboardController>();

  final DashboardRepository _repository = Get.find<DashboardRepository>();

  // المتغيرات التفاعلية
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<Map<String, dynamic>> dashboardItems = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  /// تحميل بيانات لوحة المعلومات
  Future<void> loadDashboardData() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // تحميل التخطيط الافتراضي
      final layout = await _repository.getDashboardLayout('default');
      dashboardItems.value = layout;

    } catch (e) {
      errorMessage.value = 'فشل في تحميل بيانات لوحة المعلومات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// حفظ تخطيط لوحة المعلومات
  Future<void> saveDashboardLayout(List<Map<String, dynamic>> layout) async {
    try {
      await _repository.saveDashboardLayout('default', layout);
      dashboardItems.value = layout;
    } catch (e) {
      errorMessage.value = 'فشل في حفظ التخطيط: $e';
    }
  }

  /// تحديث عنصر في لوحة المعلومات
  void updateDashboardItem(String itemId, Map<String, dynamic> updates) {
    final index = dashboardItems.indexWhere((item) => item['id'] == itemId);
    if (index != -1) {
      dashboardItems[index] = {...dashboardItems[index], ...updates};
    }
  }

  /// إضافة عنصر جديد
  void addDashboardItem(Map<String, dynamic> item) {
    dashboardItems.add(item);
  }

  /// حذف عنصر
  void removeDashboardItem(String itemId) {
    dashboardItems.removeWhere((item) => item['id'] == itemId);
  }
}
