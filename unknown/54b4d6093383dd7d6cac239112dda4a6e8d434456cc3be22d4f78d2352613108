import 'package:get/get.dart';
import '../controllers/unified_search_controller.dart';
import '../controllers/auth_controller.dart';
import '../services/unified_search_service.dart';

/// رابط البحث الموحد
/// يضمن تسجيل جميع الخدمات والمتحكمات اللازمة للبحث الموحد
class SearchBinding extends Bindings {
  @override
  void dependencies() {
    // التأكد من وجود متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }

    // تسجيل خدمة البحث الموحد
    if (!Get.isRegistered<UnifiedSearchService>()) {
      Get.put(UnifiedSearchService(), permanent: true);
    }

    // تسجيل متحكم البحث الموحد
    if (!Get.isRegistered<UnifiedSearchController>()) {
      Get.put(UnifiedSearchController());
    }
  }
}
