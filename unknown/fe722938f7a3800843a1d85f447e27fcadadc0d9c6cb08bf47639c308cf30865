import 'package:flutter/foundation.dart';

import 'api_service.dart';
import '../../models/activity_log_models.dart';

/// خدمة API لسجلات الأنشطة - متطابقة مع ASP.NET Core API
class ActivityLogsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع سجلات الأنشطة
  Future<List<ActivityLog>> getAllActivityLogs() async {
    try {
      final response = await _apiService.get('/api/ActivityLogs');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات الأنشطة: $e');
      rethrow;
    }
  }

  /// الحصول على سجل نشاط بواسطة المعرف
  Future<ActivityLog?> getActivityLogById(int id) async {
    try {
      final response = await _apiService.get('/api/ActivityLogs/$id');
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل النشاط $id: $e');
      return null;
    }
  }

  /// الحصول على سجلات أنشطة مستخدم محدد
  Future<List<ActivityLog>> getUserActivityLogs(int userId) async {
    try {
      final response = await _apiService.get('/api/ActivityLogs/user/$userId');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات أنشطة المستخدم الحالي
  Future<List<ActivityLog>> getCurrentUserActivityLogs() async {
    try {
      // نحتاج لتمرير userId من الجلسة الحالية
      // هذا endpoint غير متوفر في TaskHistory، سنستخدم getUserActivityLogs بدلاً منه
      throw UnimplementedError('استخدم getUserActivityLogs مع userId المحدد');
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المستخدم الحالي: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات أنشطة مهمة محددة
  Future<List<ActivityLog>> getTaskActivityLogs(int taskId) async {
    try {
      final response = await _apiService.get('/api/ActivityLogs/task/$taskId');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات الأنشطة بحسب النوع
  Future<List<ActivityLog>> getActivityLogsByType(String activityType) async {
    try {
      final response = await _apiService.get('/api/ActivityLogs/change-type/$activityType');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات النوع $activityType: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة الحديثة
  Future<List<ActivityLog>> getRecentActivityLogs({int limit = 50}) async {
    try {
      // الحصول على جميع السجلات وترتيبها حسب التاريخ
      final response = await _apiService.get('/api/ActivityLogs');
      final allLogs = _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
      // ترتيب حسب التاريخ وأخذ العدد المطلوب
      allLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return allLogs.take(limit).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على السجلات الحديثة: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة بحسب التاريخ
  Future<List<ActivityLog>> getActivityLogsByDate(DateTime date) async {
    try {
      final startTimestamp = date.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = date.add(const Duration(days: 1)).millisecondsSinceEpoch ~/ 1000;
      final response = await _apiService.get('/api/ActivityLogs/date-range?startDate=$startTimestamp&endDate=$endTimestamp');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات التاريخ: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة لفترة زمنية
  Future<List<ActivityLog>> getActivityLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      final response = await _apiService.get('/api/ActivityLogs/date-range?startDate=$startTimestamp&endDate=$endTimestamp');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات الفترة الزمنية: $e');
      return [];
    }
  }

  /// إنشاء سجل نشاط جديد
  Future<ActivityLog> createActivityLog(ActivityLog activityLog) async {
    try {
      final response = await _apiService.post(
        '/api/ActivityLogs',
        activityLog.toJson(),
      );
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل النشاط: $e');
      rethrow;
    }
  }

  /// تسجيل نشاط بسيط
  Future<ActivityLog> logActivity({
    required String action,
    required String entityType,
    required int entityId,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // بناء URL مع query parameters
      final queryParams = [
        'action=$action',
        'entityType=$entityType',
        'entityId=$entityId',
        if (description != null) 'description=${Uri.encodeComponent(description)}',
      ].join('&');

      final response = await _apiService.post('/api/ActivityLogs/log?$queryParams', {});
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل النشاط: $e');
      rethrow;
    }
  }

  /// تحديث سجل نشاط
  Future<ActivityLog> updateActivityLog(int id, ActivityLog activityLog) async {
    try {
      final response = await _apiService.put(
        '/api/ActivityLogs/$id',
        activityLog.toJson(),
      );
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث سجل النشاط $id: $e');
      rethrow;
    }
  }

  /// حذف سجل نشاط
  Future<bool> deleteActivityLog(int id) async {
    try {
      final response = await _apiService.delete('/api/ActivityLogs/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف سجل النشاط $id: $e');
      return false;
    }
  }

  /// البحث في سجلات الأنشطة
  Future<List<ActivityLog>> searchActivityLogs(String query) async {
    try {
      // TaskHistory لا يحتوي على endpoint للبحث، سنحصل على جميع السجلات ونبحث فيها محلياً
      final allLogs = await getAllActivityLogs();
      return allLogs.where((log) =>
        log.action.toLowerCase().contains(query.toLowerCase()) ||
        (log.details?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
        (log.changeDescription?.toLowerCase().contains(query.toLowerCase()) ?? false)
      ).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في سجلات الأنشطة: $e');
      return [];
    }
  }

  /// الحصول على أنواع الأنشطة المتاحة
  Future<List<String>> getAvailableActivityTypes() async {
    try {
      final response = await _apiService.get('/api/ActivityLogs/change-types');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع الأنشطة: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات الأنشطة
  Future<Map<String, dynamic>> getActivityStatistics() async {
    try {
      // TaskHistory لا يحتوي على endpoint للإحصائيات، سنحسبها محلياً
      final allLogs = await getAllActivityLogs();
      final statistics = <String, dynamic>{
        'totalActivities': allLogs.length,
        'activitiesByType': <String, int>{},
        'activitiesByAction': <String, int>{},
        'recentActivitiesCount': allLogs.where((log) =>
          DateTime.now().difference(log.timestampDateTime).inDays <= 7
        ).length,
      };

      // حساب الأنشطة حسب النوع
      for (final log in allLogs) {
        final type = log.changeType ?? 'غير محدد';
        statistics['activitiesByType'][type] =
          (statistics['activitiesByType'][type] ?? 0) + 1;

        statistics['activitiesByAction'][log.action] =
          (statistics['activitiesByAction'][log.action] ?? 0) + 1;
      }

      return statistics;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأنشطة: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات أنشطة مستخدم محدد
  Future<Map<String, dynamic>> getUserActivityStatistics(int userId) async {
    try {
      final userLogs = await getUserActivityLogs(userId);
      final statistics = <String, dynamic>{
        'totalActivities': userLogs.length,
        'activitiesByType': <String, int>{},
        'activitiesByAction': <String, int>{},
        'recentActivitiesCount': userLogs.where((log) =>
          DateTime.now().difference(log.timestampDateTime).inDays <= 7
        ).length,
      };

      // حساب الأنشطة حسب النوع
      for (final log in userLogs) {
        final type = log.changeType ?? 'غير محدد';
        statistics['activitiesByType'][type] =
          (statistics['activitiesByType'][type] ?? 0) + 1;

        statistics['activitiesByAction'][log.action] =
          (statistics['activitiesByAction'][log.action] ?? 0) + 1;
      }

      return statistics;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستخدم: $e');
      return {};
    }
  }

  /// الحصول على تقرير الأنشطة اليومي
  Future<Map<String, dynamic>> getDailyActivityReport(DateTime date) async {
    try {
      final dayLogs = await getActivityLogsByDate(date);
      return {
        'date': date.toIso8601String().split('T')[0],
        'totalActivities': dayLogs.length,
        'activitiesByHour': _groupActivitiesByHour(dayLogs),
        'topActions': _getTopActions(dayLogs, 5),
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير اليومي: $e');
      return {};
    }
  }

  /// الحصول على تقرير الأنشطة الأسبوعي
  Future<Map<String, dynamic>> getWeeklyActivityReport(DateTime weekStart) async {
    try {
      final weekEnd = weekStart.add(const Duration(days: 7));
      final weekLogs = await getActivityLogsByDateRange(weekStart, weekEnd);
      return {
        'weekStart': weekStart.toIso8601String().split('T')[0],
        'weekEnd': weekEnd.toIso8601String().split('T')[0],
        'totalActivities': weekLogs.length,
        'activitiesByDay': _groupActivitiesByDay(weekLogs),
        'topActions': _getTopActions(weekLogs, 10),
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير الأسبوعي: $e');
      return {};
    }
  }

  /// تصدير سجلات الأنشطة
  Future<Map<String, dynamic>> exportActivityLogs(
    DateTime startDate,
    DateTime endDate,
    String format,
  ) async {
    try {
      // TaskHistory لا يحتوي على endpoint للتصدير، سنحصل على البيانات ونعيدها
      final logs = await getActivityLogsByDateRange(startDate, endDate);
      return {
        'format': format,
        'startDate': startDate.toIso8601String().split('T')[0],
        'endDate': endDate.toIso8601String().split('T')[0],
        'totalRecords': logs.length,
        'data': logs.map((log) => log.toJson()).toList(),
      };
    } catch (e) {
      debugPrint('خطأ في تصدير سجلات الأنشطة: $e');
      return {};
    }
  }

  /// حذف سجلات الأنشطة القديمة
  Future<bool> cleanupOldActivityLogs(int daysToKeep) async {
    try {
      // TaskHistory لا يحتوي على endpoint للتنظيف
      // يمكن تنفيذ هذا عن طريق حذف السجلات القديمة واحداً تلو الآخر
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final oldLogs = await getActivityLogsByDateRange(
        DateTime(2000), // تاريخ قديم جداً
        cutoffDate,
      );

      int deletedCount = 0;
      for (final log in oldLogs) {
        if (await deleteActivityLog(log.id)) {
          deletedCount++;
        }
      }

      debugPrint('تم حذف $deletedCount سجل قديم');
      return true;
    } catch (e) {
      debugPrint('خطأ في تنظيف السجلات القديمة: $e');
      return false;
    }
  }

  /// الحصول على سجلات الأنشطة المجمعة بحسب النوع
  Future<Map<String, dynamic>> getActivityLogsSummaryByType() async {
    try {
      final allLogs = await getAllActivityLogs();
      final summary = <String, dynamic>{};

      for (final log in allLogs) {
        final type = log.changeType ?? 'غير محدد';
        summary[type] = (summary[type] ?? 0) + 1;
      }

      return summary;
    } catch (e) {
      debugPrint('خطأ في الحصول على ملخص الأنشطة بحسب النوع: $e');
      return {};
    }
  }

  /// الحصول على سجلات الأنشطة المجمعة بحسب المستخدم
  Future<Map<String, dynamic>> getActivityLogsSummaryByUser() async {
    try {
      final allLogs = await getAllActivityLogs();
      final summary = <String, dynamic>{};

      for (final log in allLogs) {
        final userName = log.user?.name ?? 'مستخدم غير معروف';
        summary[userName] = (summary[userName] ?? 0) + 1;
      }

      return summary;
    } catch (e) {
      debugPrint('خطأ في الحصول على ملخص الأنشطة بحسب المستخدم: $e');
      return {};
    }
  }

  /// تسجيل نشاط تسجيل الدخول
  Future<ActivityLog> logLoginActivity() async {
    return await logActivity(
      action: 'login',
      entityType: 'user',
      entityId: 0, // سيتم تحديده من الخادم
      description: 'تسجيل دخول المستخدم',
    );
  }

  /// تسجيل نشاط تسجيل الخروج
  Future<ActivityLog> logLogoutActivity() async {
    return await logActivity(
      action: 'logout',
      entityType: 'user',
      entityId: 0, // سيتم تحديده من الخادم
      description: 'تسجيل خروج المستخدم',
    );
  }

  // دوال مساعدة خاصة

  /// تجميع الأنشطة حسب الساعة
  Map<String, int> _groupActivitiesByHour(List<ActivityLog> logs) {
    final groupedByHour = <String, int>{};

    for (final log in logs) {
      final hour = log.timestampDateTime.hour.toString().padLeft(2, '0');
      final hourKey = '$hour:00';
      groupedByHour[hourKey] = (groupedByHour[hourKey] ?? 0) + 1;
    }

    return groupedByHour;
  }

  /// تجميع الأنشطة حسب اليوم
  Map<String, int> _groupActivitiesByDay(List<ActivityLog> logs) {
    final groupedByDay = <String, int>{};

    for (final log in logs) {
      final day = log.timestampDateTime.toIso8601String().split('T')[0];
      groupedByDay[day] = (groupedByDay[day] ?? 0) + 1;
    }

    return groupedByDay;
  }

  /// الحصول على أهم الإجراءات
  List<Map<String, dynamic>> _getTopActions(List<ActivityLog> logs, int limit) {
    final actionCounts = <String, int>{};

    for (final log in logs) {
      actionCounts[log.action] = (actionCounts[log.action] ?? 0) + 1;
    }

    final sortedActions = actionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedActions
        .take(limit)
        .map((entry) => {
              'action': entry.key,
              'count': entry.value,
            })
        .toList();
  }
}
