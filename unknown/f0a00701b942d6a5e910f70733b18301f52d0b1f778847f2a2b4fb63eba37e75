import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';
import '../../../../constants/app_styles.dart';
import '../../../../constants/app_colors.dart';

/// مخطط مقياس بسيط باستخدام BaseChartWidget
class GaugeChartWidget extends BaseChartWidget {
  const GaugeChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    // أخذ أول قيمة كمقياس (عادة نسبة مئوية)
    final value = data.first.value;
    final label = data.first.label;

    return Column(
      children: [
        // المقياس
        Expanded(
          flex: 3,
          child: SfRadialGauge(
            axes: <RadialAxis>[
              RadialAxis(
                minimum: 0,
                maximum: 100,
                ranges: <GaugeRange>[
                  GaugeRange(
                    startValue: 0,
                    endValue: 30,
                    color: Colors.red.shade300,
                    startWidth: 10,
                    endWidth: 10,
                  ),
                  GaugeRange(
                    startValue: 30,
                    endValue: 70,
                    color: Colors.orange.shade300,
                    startWidth: 10,
                    endWidth: 10,
                  ),
                  GaugeRange(
                    startValue: 70,
                    endValue: 100,
                    color: Colors.green.shade300,
                    startWidth: 10,
                    endWidth: 10,
                  ),
                ],
                pointers: <GaugePointer>[
                  NeedlePointer(
                    value: value,
                    needleColor: AppColors.primary,
                    knobStyle: KnobStyle(
                      color: AppColors.primary,
                      borderColor: Colors.white,
                      borderWidth: 2,
                    ),
                    tailStyle: TailStyle(
                      color: AppColors.primary.withOpacity(0.3),
                    ),
                  ),
                ],
                annotations: <GaugeAnnotation>[
                  GaugeAnnotation(
                    widget: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${value.toInt()}%',
                          style: AppStyles.headingLarge.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          label,
                          style: AppStyles.bodyMedium.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    angle: 90,
                    positionFactor: 0.8,
                  ),
                ],
                axisLabelStyle: GaugeTextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
                majorTickStyle: MajorTickStyle(
                  color: Colors.grey.shade400,
                  thickness: 2,
                ),
                minorTickStyle: MinorTickStyle(
                  color: Colors.grey.shade300,
                  thickness: 1,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // معلومات إضافية
        Expanded(
          flex: 1,
          child: _buildGaugeInfo(value),
        ),
      ],
    );
  }

  /// بناء معلومات المقياس
  Widget _buildGaugeInfo(double value) {
    String status;
    Color statusColor;
    IconData statusIcon;

    if (value >= 70) {
      status = 'ممتاز';
      statusColor = Colors.green;
      statusIcon = Icons.trending_up;
    } else if (value >= 30) {
      status = 'متوسط';
      statusColor = Colors.orange;
      statusIcon = Icons.trending_flat;
    } else {
      status = 'ضعيف';
      statusColor = Colors.red;
      statusIcon = Icons.trending_down;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'الحالة: $status',
            style: AppStyles.titleMedium.copyWith(
              color: statusColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
