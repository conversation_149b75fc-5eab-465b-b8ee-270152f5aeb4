import 'package:flutter_application_2/models/user_permission_model.dart';

import 'user_model.dart';
import 'screen_model.dart';
import 'action_entity_model.dart';

/// نموذج الصلاحية - متوافق 100% مع الباك اند
class Permission {
  final int id;
  final String name;
  final String? description;
  final String permissionGroup;
  final int? createdAt;
  final int? updatedAt;
  final String? category;
  final int level;
  final String? icon;
  final String? color;
  final bool isDefault;
  final int? screenId;
  final int? actionId;

  // العلاقات البرمجية (Navigation Properties)
  final Screen? screen;
  final ActionEntity? action;
  final List<UserPermission>? userPermissions;
  final List<User>? users; // المستخدمون المرتبطون

  const Permission({
    required this.id,
    required this.name,
    this.description,
    required this.permissionGroup,
    this.createdAt,
    this.updatedAt,
    this.category,
    required this.level,
    this.icon,
    this.color,
    this.isDefault = false,
    this.screen,
    this.action,
    this.users,
    this.userPermissions,
    this.screenId,
    this.actionId,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    return Permission(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      permissionGroup: json['permissionGroup'] as String,
      createdAt: json['createdAt'] as int?,
      updatedAt: json['updatedAt'] as int?,
      category: json['category'] as String?,
      level: json['level'] as int,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      screenId: json['screenId'] as int?,
      actionId: json['actionId'] as int?,
      screen: json['screen'] != null ? Screen.fromJson(json['screen']) : null,
      action:
          json['action'] != null ? ActionEntity.fromJson(json['action']) : null,
      userPermissions: json['userPermissions'] != null
          ? (json['userPermissions'] as List)
              .where((up) => up != null) // تصفية القيم الفارغة
              .map((up) => UserPermission.fromJson(up as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissionGroup': permissionGroup,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'category': category,
      'level': level,
      'icon': icon,
      'color': color,
      'isDefault': isDefault,
      'screen': screen?.toJson(),
      'action': action?.toJson(),
      'screenId': screenId,
      'actionId': actionId,
      'userPermissions': userPermissions?.map((up) => up.toJson()).toList(),
    };
  }

  Permission copyWith({
    int? id,
    String? name,
    String? description,
    String? permissionGroup,
    int? createdAt,
    int? updatedAt,
    String? category,
    int? level,
    String? icon,
    String? color,
    bool? isDefault,
    int? screenId,
    int? actionId,
    Screen? screen,
    ActionEntity? action,
    List<UserPermission>? userPermissions,
  
  }) {
    return Permission(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      permissionGroup: permissionGroup ?? this.permissionGroup,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      category: category ?? this.category,
      level: level ?? this.level,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isDefault: isDefault ?? this.isDefault,
     screenId: screenId ?? this.screenId,
      actionId: actionId ?? this.actionId,
      screen: screen ?? this.screen,
      action: action ?? this.action,
   
       userPermissions: userPermissions ?? this.userPermissions,

     );
   }
 

   

  @override
  String toString() {
    return 'Permission(id: $id, name: $name, group: $permissionGroup, level: $level)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Permission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج ملخص الصلاحية - للاستخدام في APIs الأدوار
class PermissionSummary {
  final int id;
  final String name;
  final String? description;
  final String permissionGroup;
  final int level;
  final bool isActive;

  const PermissionSummary({
    required this.id,
    required this.name,
    this.description,
    required this.permissionGroup,
    required this.level,
    required this.isActive,
  });

  factory PermissionSummary.fromJson(Map<String, dynamic> json) {
    return PermissionSummary(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      permissionGroup: json['permissionGroup'] as String,
      level: json['level'] as int,
      isActive: json['isActive'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissionGroup': permissionGroup,
      'level': level,
      'isActive': isActive,
    };
  }

  /// تحويل إلى Permission كامل
  Permission toPermission() {
    return Permission(
      id: id,
      name: name,
      description: description,
      permissionGroup: permissionGroup,
      level: level,
      isDefault: isActive,
    );
  }
}
