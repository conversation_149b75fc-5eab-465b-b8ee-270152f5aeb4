import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'api_service.dart';

import 'dart:io'; // Required for File type
/// خدمة API للمرفقات - متطابقة مع ASP.NET Core API
class AttachmentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المرفقات
  Future<List<Attachment>> getAllAttachments() async {
    try {
      final response = await _apiService.get('/api/Attachments');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المرفقات: $e');
      rethrow;
    }
  }

  /// الحصول على مرفق بواسطة المعرف
  Future<Attachment?> getAttachmentById(int id, {bool forceRefresh = false}) async {
    try {
      // يمكن هنا مستقبلاً استخدام forceRefresh لتجاهل الكاش إذا كان هناك كاش
      final response = await _apiService.get('/api/Attachments/$id');
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المرفق: $e');
      return null;
    }
  }

  /// إنشاء سجل مرفق (بدون رفع الملف الفعلي - قد تكون هذه دالة مختلفة أو جزء من عملية الرفع)
  Future<Attachment?> createAttachmentMetadata(Attachment attachment) async {
    try {
      final response = await _apiService.post(
        '/api/Attachments/metadata', // Example endpoint for metadata
        attachment.toJson(), // Sends metadata like description, original filename etc.
      );
      return _apiService.handleResponse<Attachment>(
        response, // The fromJson function should accept dynamic
        (json) => Attachment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المرفق: $e');
      rethrow;
    }
  }

  /// تحديث مرفق
  Future<Attachment?> updateAttachment(Attachment attachment) async {
    try {
      final response = await _apiService.put(
        '/api/Attachments/${attachment.id}',
        attachment.toJson(),
      );
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المرفق: $e');
      rethrow;
    }
  }

  /// حذف مرفق
  Future<bool> deleteAttachment(int id) async {
    try {
      final response = await _apiService.delete('/api/Attachments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المرفق: $e');
      return false;
    }
  }

  /// الحصول على مرفقات مهمة معينة
  Future<List<Attachment>> getAttachmentsByTask(int taskId) async {
    try {
      final response = await _apiService.get('/api/Attachments/task/$taskId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المهمة: $e');
      rethrow;
    }
  }

  /// الحصول على مرفقات مشروع معين
  Future<List<Attachment>> getAttachmentsByProject(int projectId) async {
    try {
      final response = await _apiService.get('/api/Attachments/project/$projectId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المشروع: $e');
      rethrow;
    }
  }

  /// الحصول على مرفقات مستخدم معين
  Future<List<Attachment>> getAttachmentsByUser(int userId) async {
    try {
      final response = await _apiService.get('/api/Attachments/user/$userId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المستخدم: $e');
      rethrow;
    }
  }

  /// البحث في المرفقات
  Future<List<Attachment>> searchAttachments(String query) async {
    try {
      final response = await _apiService.get('/api/Attachments/search?searchTerm=$query');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المرفقات: $e');
      rethrow;
    }
  }

  /// الحصول على المرفقات بنوع ملف معين
  Future<List<Attachment>> getAttachmentsByFileType(String fileType) async {
    try {
      final response = await _apiService.get('/api/Attachments/file-type/$fileType');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات نوع الملف: $e');
      rethrow;
    }
  }

  /// الحصول على المرفقات في فترة زمنية
  Future<List<Attachment>> getAttachmentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/api/Attachments/date-range?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات الفترة الزمنية: $e');
      rethrow;
    }
  }

  /// رفع ملف مرفق
  Future<Attachment?> uploadFile(
    String fileName,
    String filePath,
    int? taskId,
    int? projectId,
  ) async {
    try {
      // هذه الدالة ترسل بيانات وصفية فقط، وليس الملف الفعلي.
      // لرفع الملف الفعلي، استخدم uploadAttachmentFile.
      final response = await _apiService.post(
        '/api/Attachments/upload',
        {
          'fileName': fileName,
          'filePath': filePath,
          'taskId': taskId,
          'projectId': projectId,
        },
      );
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      debugPrint('خطأ في رفع الملف: $e');
      rethrow; // Or handle error appropriately
    }
  }

  /// رفع ملف مرفق جديد
  Future<Attachment?> uploadAttachmentFile(
    File file, {
    required int taskId,
    required int userId,
    String? description,
    Function(double)? onUploadProgress,
  }) async {
    try {
      // Endpoint for file upload, e.g., /api/Attachments/upload
      // This endpoint should handle multipart/form-data
      final response = await _apiService.postMultipart(
        '/api/Attachments/upload', // Make sure this endpoint exists and handles multipart
        file: file,
        fields: {
          'taskId': taskId.toString(),
          'uploadedBy': userId.toString(),
          if (description != null) 'description': description,
        },
        onUploadProgress: onUploadProgress,
      );
      // The server should return the created Attachment object upon successful upload
      // Ensure the fromJson function signature matches what handleResponse expects
      return _apiService.handleResponse<Attachment>(response, (json) => Attachment.fromJson(json as Map<String, dynamic>));
    } catch (e) {
      debugPrint('خطأ في رفع المرفق: $e');
      rethrow; // Or handle error appropriately
    }
  }

  /// تحميل ملف مرفق
  Future<String?> downloadFile(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/Attachments/$attachmentId/download');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل الملف: $e');
      return null;
    }
  }

  /// الحصول على رابط تحميل مؤقت
  Future<String?> getTemporaryDownloadLink(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/Attachments/$attachmentId/temp-link');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على رابط التحميل المؤقت: $e');
      return null;
    }
  }

  /// الحصول على معاينة الملف
  Future<String?> getFilePreview(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/Attachments/$attachmentId/preview');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معاينة الملف: $e');
      return null;
    }
  }

  /// الحصول على صورة مصغرة للملف
  Future<String?> getFileThumbnail(int attachmentId) async {
    try {
      final response = await _apiService.get('/api/Attachments/$attachmentId/thumbnail');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الصورة المصغرة: $e');
      return null;
    }
  }

  /// الحصول على إحصائيات المرفقات
  Future<Map<String, dynamic>?> getAttachmentStatistics() async {
    try {
      final response = await _apiService.get('/api/Attachments/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المرفقات: $e');
      return null;
    }
  }

  /// الحصول على أحجام الملفات
  Future<Map<String, dynamic>?> getFileSizes() async {
    try {
      final response = await _apiService.get('/api/Attachments/file-sizes');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على أحجام الملفات: $e');
      return null;
    }
  }

  /// حذف مرفقات متعددة
  Future<int> deleteMultipleAttachments(List<int> attachmentIds) async {
    try {
      final response = await _apiService.post(
        '/api/Attachments/bulk-delete',
        {'attachmentIds': attachmentIds},
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return int.tryParse(response.body.toString()) ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في حذف المرفقات المتعددة: $e');
      return 0;
    }
  }

  /// نقل مرفقات إلى مهمة أخرى
  Future<bool> moveAttachmentsToTask(List<int> attachmentIds, int newTaskId) async {
    try {
      final response = await _apiService.put(
        '/api/Attachments/move-to-task',
        {
          'attachmentIds': attachmentIds,
          'newTaskId': newTaskId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المرفقات: $e');
      return false;
    }
  }

  /// نسخ مرفقات إلى مهمة أخرى
  Future<bool> copyAttachmentsToTask(List<int> attachmentIds, int targetTaskId) async {
    try {
      final response = await _apiService.post(
        '/api/Attachments/copy-to-task',
        {
          'attachmentIds': attachmentIds,
          'targetTaskId': targetTaskId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نسخ المرفقات: $e');
      return false;
    }
  }
  
  /// ربط مرفق بمهمة (مفيد عند تحويل المهام)
  Future<bool> linkAttachmentToTask(int attachmentId, int taskId) async {
    try {
      if (attachmentId <= 0 || taskId <= 0) {
        debugPrint('❌ معرف المرفق أو المهمة غير صالح: attachmentId=$attachmentId, taskId=$taskId');
        return false;
      }
      
      final response = await _apiService.post(
        '/api/Attachments/link',
        {
          'attachmentId': attachmentId,
          'taskId': taskId,
        },
      );
      
      final success = response.statusCode >= 200 && response.statusCode < 300;
      if (success) {
        debugPrint('✅ تم ربط المرفق $attachmentId بالمهمة $taskId بنجاح');
      } else {
        debugPrint('❌ فشل في ربط المرفق: ${response.statusCode} - ${response.body}');
      }
      
      return success;
    } catch (e) {
      debugPrint('❌ خطأ في ربط المرفق بالمهمة: $e');
      return false;
    }
  }

  /// ضغط مرفقات متعددة
  Future<String?> compressAttachments(List<int> attachmentIds, String archiveName) async {
    try {
      final response = await _apiService.post(
        '/api/Attachments/compress',
        {
          'attachmentIds': attachmentIds,
          'archiveName': archiveName,
        },
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في ضغط المرفقات: $e');
      return null;
    }
  }
}
