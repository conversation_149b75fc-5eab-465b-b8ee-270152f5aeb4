/// نموذج الإجراء - متوافق 100% مع الباك اند
class ActionEntity {
  final int id;
  final String name;
  final String? code;
  final String? description;
  final bool isActive;
  final int createdAt;

  const ActionEntity({
    required this.id,
    required this.name,
    this.code,
    this.description,
    this.isActive = true,
    required this.createdAt,
  });

  factory ActionEntity.fromJson(Map<String, dynamic> json) {
    return ActionEntity(
      id: json['id'] as int,
      name: json['name'] as String,
      code: json['code'] as String?,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'code': code,
    'description': description,
    'isActive': isActive,
    'createdAt': createdAt,
  };
}