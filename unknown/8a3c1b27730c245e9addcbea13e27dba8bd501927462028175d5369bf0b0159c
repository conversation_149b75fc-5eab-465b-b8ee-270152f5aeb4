import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_status_enum.dart' as task_enums;

/// متحكم حالات المهام
class TaskStatusController extends GetxController {
  final RxList<task_enums.TaskStatus> _allStatuses = <task_enums.TaskStatus>[].obs;
  final RxList<task_enums.TaskStatus> _filteredStatuses = <task_enums.TaskStatus>[].obs;

  // حالة المهمة الحالية
  final Rx<task_enums.TaskStatus?> _currentStatus = Rx<task_enums.TaskStatus?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<task_enums.TaskStatus> get allStatuses => _allStatuses;
  List<task_enums.TaskStatus> get taskStatuses => _filteredStatuses;
  List<task_enums.TaskStatus> get filteredStatuses => _filteredStatuses;
  task_enums.TaskStatus? get currentStatus => _currentStatus.value;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  static final List<task_enums.TaskStatus> _enumStatuses = [
    task_enums.TaskStatus.pending,
    task_enums.TaskStatus.inProgress,
    task_enums.TaskStatus.waitingForInfo,
    task_enums.TaskStatus.completed,
    task_enums.TaskStatus.cancelled,
    task_enums.TaskStatus.news,
  ];

  @override
  void onInit() {
    super.onInit();
    _allStatuses.assignAll(_enumStatuses);
    _applyFilters();
  }

  /// جميع الدوال المتعلقة باستدعاء API تم تعطيلها

  /// تحميل جميع حالات المهام
  Future<void> loadAllStatuses() async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      _allStatuses.assignAll(_enumStatuses);
      _applyFilters();
      debugPrint('تم تحميل حالات المهام من التعدادات الثابتة');
    } catch (e) {
      _allStatuses.clear();
      _filteredStatuses.clear();
      _errorMessage.value = 'خطأ في تحميل حالات المهام: e.toString()';
      debugPrint('تفاصيل الخطأ: e.toString()');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على حالة مهمة بالمعرف
  Future<void> getStatusById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final status = _allStatuses.firstWhereOrNull((s) => s.id == id);
      _currentStatus.value = status;
      debugPrint('تم تحميل حالة المهمة: status?.name ?? "غير موجودة"');
    } catch (e) {
      _error.value = 'خطأ في تحميل حالة المهمة: e';
      debugPrint('خطأ في تحميل حالة المهمة: e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعادة ترتيب حالات المهام (محلي فقط)
  Future<bool> reorderTaskStatuses(List<int> orderedIds) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // تحديث ترتيب العرض محلياً (لا يوجد تعديل فعلي على enum)
      _applyFilters();
      debugPrint('تم إعادة ترتيب حالات المهام');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إعادة ترتيب حالات المهام: e';
      _errorMessage.value = 'خطأ في إعادة ترتيب حالات المهام: e';
      debugPrint('خطأ في إعادة ترتيب حالات المهام: e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على الحالات النشطة فقط
  List<task_enums.TaskStatus> get activeStatuses {
    return _allStatuses.toList();
  }

  /// الحصول على حالة افتراضية
  task_enums.TaskStatus? get defaultStatus {
    return _allStatuses.isNotEmpty ? _allStatuses.first : null;
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allStatuses.where((status) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!status.name.toLowerCase().contains(query)) {
          return false;
        }
      }
      // مرشح النشط فقط (جميع الحالات نشطة في enum)
      return true;
    }).toList();

    _filteredStatuses.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllStatuses();
  }
}
