import 'package:flutter/widgets.dart';
import 'package:flutter_application_2/controllers/notification_settings_controller.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/notification_controller.dart';
import '../controllers/notifications_controller.dart';
import '../controllers/task_controller.dart';
import '../controllers/user_controller.dart';
import '../controllers/department_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/theme_controller.dart';
import '../controllers/language_controller.dart';
import '../services/export_services/chart_export_service.dart';

import '../services/unified_permission_service.dart';
import '../screens/dashboard/services/dashboard_service.dart';
import '../screens/dashboard/core/dashboard_repository.dart';
import '../controllers/unified_chat_controller.dart';


/// رابط الشاشة الرئيسية
///
/// يضمن تسجيل جميع الخدمات والمتحكمات اللازمة للشاشة الرئيسية
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // التأكد من وجود المتحكمات الأساسية
    _ensureBasicControllers();
    
    // تسجيل متحكمات الشاشة الرئيسية
    _registerHomeControllers();
    
    // تسجيل الخدمات المطلوبة
    _registerServices();
  }

  /// التأكد من وجود المتحكمات الأساسية
  void _ensureBasicControllers() {
    // متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }

    // متحكم السمة
    if (!Get.isRegistered<ThemeController>()) {
      Get.put(ThemeController(), permanent: true);
    }

    // متحكم اللغة
    if (!Get.isRegistered<LanguageController>()) {
      Get.put(LanguageController(), permanent: true);
    }
  }

  /// تسجيل متحكمات الشاشة الرئيسية
  void _registerHomeControllers() {
    // متحكم الإشعارات
    if (!Get.isRegistered<NotificationController>()) {
      Get.put(NotificationController(), permanent: true);
    }

    // متحكم الإشعارات (النسخة الثانية)
    if (!Get.isRegistered<NotificationsController>()) {
      Get.put(NotificationsController(), permanent: true);
      Get.put(NotificationController(), permanent: true);
      Get.put(NotificationSettingsController(), permanent: true);

    }

    // متحكم المهام
    if (!Get.isRegistered<TaskController>()) {
      Get.put(TaskController(), permanent: true);
    }

    // متحكم المستخدمين
    if (!Get.isRegistered<UserController>()) {
      Get.put(UserController(), permanent: true);
    }

    // متحكم الأقسام
    if (!Get.isRegistered<DepartmentController>()) {
      Get.put(DepartmentController(), permanent: true);
    }

    // متحكم لوحة المعلومات
    if (!Get.isRegistered<DashboardController>()) {
      Get.put(DashboardController(), permanent: true);
    }

    // متحكم الدردشة الموحدة
    if (!Get.isRegistered<UnifiedChatController>()) {
      Get.put(UnifiedChatController(), permanent: true);
    }
  }

  /// تسجيل الخدمات المطلوبة
  void _registerServices() {
    // خدمة تصدير المخططات
    if (!Get.isRegistered<ChartExportService>()) {
      Get.put(ChartExportService(), permanent: true);
    }

    // خدمة الصلاحيات الموحدة
    if (!Get.isRegistered<UnifiedPermissionService>()) {
      Get.put(UnifiedPermissionService(), permanent: true);
    }

    // خدمة لوحة المعلومات
    if (!Get.isRegistered<DashboardService>()) {
      Get.lazyPut(() => DashboardService());
    }

    // مستودع لوحة المعلومات
    if (!Get.isRegistered<DashboardRepository>()) {
      Get.put(DashboardRepository(), permanent: true);
    }

    // مراقب المسارات
    if (!Get.isRegistered<RouteObserver>()) {
      Get.put(RouteObserver<PageRoute>(), permanent: true);
    }

    // خدمة التزامن (سيتم تهيئتها لاحقاً عند الحاجة)
    // لا نقوم بتسجيلها هنا لأنها تحتاج معرف المستخدم
  }

  /// تهيئة خدمة التزامن للمستخدم الحالي
  static void initializeSyncService(int userId) {
    try {
      // حذف خدمة التزامن الموجودة إن وجدت
      // if (Get.isRegistered<SyncService>()) {
      //   Get.delete<SyncService>();
      // }

      // إنشاء خدمة تزامن جديدة للمستخدم
      // final syncService = SyncService(userId);
      // Get.put(syncService, permanent: true);

      // تحميل البيانات الأساسية
      _loadInitialData(userId);

      debugPrint('تم تهيئة خدمة التزامن للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة التزامن: $e');
    }
  }

  /// تحميل البيانات الأساسية للمستخدم
  static void _loadInitialData(int userId) {
    try {
      // تحميل المستخدمين
      if (Get.isRegistered<UserController>()) {
        final userController = Get.find<UserController>();
        userController.loadAllUsers();
      }

      // تحميل الأقسام
      if (Get.isRegistered<DepartmentController>()) {
        final departmentController = Get.find<DepartmentController>();
        departmentController.loadAllDepartments();
      }

      // تحميل المهام حسب صلاحيات المستخدم
      if (Get.isRegistered<TaskController>()) {
        final taskController = Get.find<TaskController>();
        taskController.loadTasksByUserPermissions(userId);
      }

      debugPrint('تم تحميل البيانات الأساسية للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الأساسية: $e');
    }
  }

  /// تنظيف الموارد عند الخروج من الشاشة الرئيسية
  static void cleanup() {
    try {
      // حذف خدمة التزامن
      // if (Get.isRegistered<SyncService>()) {
      //   Get.delete<SyncService>();
      // }
      
      debugPrint('تم تنظيف موارد الشاشة الرئيسية');
    } catch (e) {
      debugPrint('خطأ في تنظيف موارد الشاشة الرئيسية: $e');
    }
  }
}
