import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
// import 'package:extended_image/extended_image.dart'; // تم تعليقه مؤقتًا بسبب مشكلة التوافق
import 'package:url_launcher/url_launcher.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/download_service.dart';
import '../../services/unified_permission_service.dart';
import '../../utils/file_processor.dart';
import 'unified_pdf_viewer.dart';

/// عارض الملفات العام
///
/// يوفر واجهة موحدة لعرض مختلف أنواع الملفات (PDF، صور، فيديو، مستندات، إلخ)
/// مع دعم التنزيل والمشاركة
class FileViewerWidget extends StatefulWidget {
  /// المرفق المراد عرضه
  final Attachment attachment;

  /// إظهار شريط الأدوات
  final bool showToolbar;

  /// دالة يتم استدعاؤها عند تنزيل الملف
  final Function(Attachment)? onDownload;

  /// دالة يتم استدعاؤها عند مشاركة الملف
  final Function(Attachment)? onShare;

  /// إنشاء عارض الملفات
  const FileViewerWidget({
    super.key,
    required this.attachment,
    this.showToolbar = true,
    this.onDownload,
    this.onShare,
  });

  @override
  State<FileViewerWidget> createState() => _FileViewerWidgetState();
}

class _FileViewerWidgetState extends State<FileViewerWidget> {
  /// خدمة التنزيل
  final DownloadService _downloadService = DownloadService();

  /// الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  /// حالة تحميل الملف
  bool _isLoading = true;

  /// رسالة الخطأ (إن وجدت)
  String? _errorMessage;

  /// حالة تنزيل الملف
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    _checkFile();
  }

  /// التحقق من وجود الملف
  Future<void> _checkFile() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // التحقق من وجود الملف
      if (!kIsWeb) {
        final file = File(widget.attachment.filePath);
        if (!await file.exists()) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'الملف غير موجود'.tr;
          });
          return;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ أثناء التحقق من الملف: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء التحقق من الملف: ${e.toString()}'.tr;
      });
    }
  }

  /// تنزيل الملف
  Future<void> _downloadFile() async {
    try {
      setState(() {
        _isDownloading = true;
      });

      // عرض مؤشر التحميل
      Get.dialog(
        AlertDialog(
          title: Text('جاري التنزيل...'.tr),
          content: const LinearProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تنزيل الملف
      final success = await _downloadService.downloadAttachment(widget.attachment);

      // إغلاق مؤشر التحميل
      Get.back();

      setState(() {
        _isDownloading = false;
      });

      // عرض رسالة نجاح أو فشل
      if (success) {
        Get.snackbar(
          'نجاح'.tr,
          'تم تنزيل الملف بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );

        // استدعاء دالة التنزيل (إن وجدت)
        if (widget.onDownload != null) {
          widget.onDownload!(widget.attachment);
        }
      } else {
        Get.snackbar(
          'خطأ'.tr,
          'فشل تنزيل الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      setState(() {
        _isDownloading = false;
      });

      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تنزيل الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تنزيل وفتح الملف
  Future<void> _downloadAndOpenFile() async {
    try {
      setState(() {
        _isDownloading = true;
      });

      // عرض مؤشر التحميل
      Get.dialog(
        AlertDialog(
          title: Text('جاري التنزيل وفتح الملف...'.tr),
          content: const LinearProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تنزيل الملف وفتحه
      final success = await _downloadService.downloadAttachment(
        widget.attachment,
        openAfterDownload: true,
      );

      // إغلاق مؤشر التحميل
      Get.back();

      setState(() {
        _isDownloading = false;
      });

      // عرض رسالة نجاح أو فشل
      if (success) {
        Get.snackbar(
          'نجاح'.tr,
          'تم تنزيل وفتح الملف بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );

        // استدعاء دالة التنزيل (إن وجدت)
        if (widget.onDownload != null) {
          widget.onDownload!(widget.attachment);
        }
      } else {
        Get.snackbar(
          'خطأ'.tr,
          'فشل تنزيل أو فتح الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      setState(() {
        _isDownloading = false;
      });

      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تنزيل أو فتح الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// مشاركة الملف
  void _shareFile() {
    // استدعاء دالة المشاركة (إن وجدت)
    if (widget.onShare != null) {
      widget.onShare!(widget.attachment);
    }
  }

  /// فتح الملف في المتصفح
  Future<void> _openInBrowser() async {
    try {
      final url = Uri.parse(widget.attachment.filePath);
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      } else {
        Get.snackbar(
          'خطأ'.tr,
          'لا يمكن فتح الملف في المتصفح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء فتح الملف في المتصفح: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// فتح الملف باستخدام التطبيق الافتراضي
  Future<void> _openWithDefaultApp() async {
    try {
      final result = await _downloadService.openFile(widget.attachment.filePath);
      if (result['success']) {
        // تم فتح الملف بنجاح
      } else {
        Get.snackbar(
          'خطأ'.tr,
          result['message'] ?? 'لا يمكن فتح الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء فتح الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.attachment.fileName),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: widget.showToolbar
            ? [
                // زر الفتح باستخدام التطبيق الافتراضي
                if (!kIsWeb)
                  IconButton(
                    icon: const Icon(Icons.open_in_new),
                    tooltip: 'فتح'.tr,
                    onPressed: _openWithDefaultApp,
                  ),
                // زر التنزيل
                if (_permissionService.canDownloadFiles())
                  IconButton(
                    icon: const Icon(Icons.download),
                    tooltip: 'تنزيل'.tr,
                    onPressed: _isDownloading ? null : _downloadFile,
                  ),
                // زر التنزيل وفتح الملف
                if (!kIsWeb && _permissionService.canDownloadFiles())
                  IconButton(
                    icon: const Icon(Icons.download_for_offline),
                    tooltip: 'تنزيل وفتح'.tr,
                    onPressed: _isDownloading ? null : () => _downloadAndOpenFile(),
                  ),
                // زر المشاركة
                if (_permissionService.canShareFiles())
                  IconButton(
                    icon: const Icon(Icons.share),
                    tooltip: 'مشاركة'.tr,
                    onPressed: _shareFile,
                  ),
                // زر فتح في المتصفح
                if (kIsWeb)
                  IconButton(
                    icon: const Icon(Icons.open_in_browser),
                    tooltip: 'فتح في المتصفح'.tr,
                    onPressed: _openInBrowser,
                  ),
              ]
            : null,
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkFile,
              child: Text('إعادة المحاولة'.tr),
            ),
          ],
        ),
      );
    }

    // تحديد نوع الملف
    final fileType = FileProcessor.getFileType(widget.attachment.filePath);

    // عرض الملف حسب نوعه
    switch (fileType) {
      case 'image':
        return _buildImageViewer();
      case 'pdf':
        return _buildPdfViewer();
      case 'video':
        return _buildVideoInfo();
      case 'audio':
        return _buildAudioInfo();
      case 'text':
      case 'word':
      case 'spreadsheet':
      case 'presentation':
      case 'code':
      default:
        return _buildGenericFileInfo();
    }
  }

  /// بناء عارض الصور المحسن مع ميزات التكبير والتصغير
  Widget _buildImageViewer() {
    return Column(
      children: [
        // شريط أدوات الصورة
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _downloadFile,
                icon: const Icon(Icons.download, color: Colors.white),
                tooltip: 'تنزيل الصورة'.tr,
              ),
              IconButton(
                onPressed: () {
                  if (widget.onShare != null) {
                    widget.onShare!(widget.attachment);
                  }
                },
                icon: const Icon(Icons.share, color: Colors.white),
                tooltip: 'مشاركة الصورة'.tr,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),

        // عارض الصورة مع إمكانية التكبير والتصغير
        Expanded(
          child: InteractiveViewer(
            minScale: 0.5,
            maxScale: 5.0,
            child: Center(
              child: kIsWeb
                  ? Image.network(
                      widget.attachment.filePath,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildImageError();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return _buildImageLoading(loadingProgress);
                      },
                    )
                  : Image.file(
                      File(widget.attachment.filePath),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildImageError();
                      },
                    ),
            ),
          ),
        ),

        // معلومات الصورة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  const Icon(Icons.image, color: Colors.blue),
                  const SizedBox(height: 4),
                  Text('صورة'.tr, style: AppStyles.bodySmall),
                ],
              ),
              Column(
                children: [
                  const Icon(Icons.storage, color: Colors.green),
                  const SizedBox(height: 4),
                  Text(FileProcessor.formatFileSize(widget.attachment.fileSize),
                    style: AppStyles.bodySmall),
                ],
              ),
              Column(
                children: [
                  const Icon(Icons.zoom_in, color: Colors.orange),
                  const SizedBox(height: 4),
                  Text('اسحب للتكبير'.tr, style: AppStyles.bodySmall),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء واجهة خطأ تحميل الصورة
  Widget _buildImageError() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.broken_image, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text('فشل في تحميل الصورة'.tr, style: AppStyles.titleMedium),
          const SizedBox(height: 8),
          Text('تحقق من اتصال الإنترنت أو جرب التنزيل'.tr,
            style: AppStyles.bodyMedium, textAlign: TextAlign.center),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: Text('تنزيل الصورة'.tr),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة تحميل الصورة
  Widget _buildImageLoading(ImageChunkEvent loadingProgress) {
    final progress = loadingProgress.expectedTotalBytes != null
        ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
        : null;

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(value: progress),
          const SizedBox(height: 16),
          Text('جاري تحميل الصورة...'.tr, style: AppStyles.titleMedium),
          if (progress != null)
            Text('${(progress * 100).toStringAsFixed(1)}%',
              style: AppStyles.bodyMedium),
        ],
      ),
    );
  }

  /// بناء عارض PDF
  Widget _buildPdfViewer() {
    return UnifiedPdfViewer(
      filePath: widget.attachment.filePath,
      fileName: widget.attachment.fileName,
      title: widget.attachment.fileName,
      showToolbar: true,
      showPrintButton: true,
      showShareButton: true,
      showSaveButton: true,
    );
  }

  /// بناء معلومات الفيديو
  Widget _buildVideoInfo() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.movie,
            color: Colors.red,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            widget.attachment.fileName,
            style: AppStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'ملف فيديو'.tr,
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            FileProcessor.formatFileSize(widget.attachment.fileSize),
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: Text('تنزيل الفيديو'.tr),
          ),
          const SizedBox(height: 16),
          Text(
            'عذراً، لا يمكن تشغيل الفيديو مباشرة في التطبيق حالياً'.tr,
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الصوت
  Widget _buildAudioInfo() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.audio_file,
            color: Colors.blue,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            widget.attachment.fileName,
            style: AppStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'ملف صوتي'.tr,
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            FileProcessor.formatFileSize(widget.attachment.fileSize),
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: Text('تنزيل الملف الصوتي'.tr),
          ),
          const SizedBox(height: 16),
          Text(
            'عذراً، لا يمكن تشغيل الملف الصوتي مباشرة في التطبيق حالياً'.tr,
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الملف العام
  Widget _buildGenericFileInfo() {
    final fileIcon = FileProcessor.getFileIcon(widget.attachment.filePath);
    final fileExtension = path.extension(widget.attachment.filePath).replaceAll('.', '').toUpperCase();

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            fileIcon,
            color: AppColors.primary,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            widget.attachment.fileName,
            style: AppStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              fileExtension,
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            FileProcessor.formatFileSize(widget.attachment.fileSize),
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: Text('تنزيل الملف'.tr),
          ),
          const SizedBox(height: 16),
          Text(
            'عذراً، لا يمكن عرض هذا النوع من الملفات مباشرة في التطبيق'.tr,
            style: AppStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
