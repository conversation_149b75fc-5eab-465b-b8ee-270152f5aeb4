import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/contribution_report_model.dart';
import '../models/time_tracking_models.dart';
import '../models/task_models.dart';
import '../services/api/contribution_reports_api_service.dart';
import '../services/api/time_tracking_api_service.dart';
import 'auth_controller.dart';

/// نموذج مقارنة المهام المشابهة
class TaskComparison {
  final Task task;
  final double similarityScore;

  TaskComparison({
    required this.task,
    required this.similarityScore,
  });
}

/// وحدة تحكم تقارير المساهمات
/// متوافقة مع ASP.NET Core API
class ContributionReportController extends GetxController {
  final ContributionReportsApiService _apiService = ContributionReportsApiService();
  final TimeTrackingApiService _timeTrackingApiService = TimeTrackingApiService();
  final AuthController _authController = Get.find<AuthController>();

  // حالة التحميل والأخطاء
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  // قوائم التقارير
  final RxList<ContributionReport> reports = <ContributionReport>[].obs;
  final RxList<ContributionReport> userReports = <ContributionReport>[].obs;
  final RxList<ContributionReport> taskReports = <ContributionReport>[].obs;
  final RxList<ContributionReport> departmentReports = <ContributionReport>[].obs;

  // التقرير الحالي
  final Rx<ContributionReport?> currentReport = Rx<ContributionReport?>(null);
  final Rx<ContributionStatistics?> currentStatistics = Rx<ContributionStatistics?>(null);
  final RxList<ContributionData> currentContributions = <ContributionData>[].obs;

  // ملخص المساهمات
  final Rx<ContributionSummary?> contributionSummary = Rx<ContributionSummary?>(null);

  // تفاصيل المساهمات
  final RxList<ContributionDetail> contributionDetails = <ContributionDetail>[].obs;

  // دعم متقدم لتتبع الوقت
  final RxList<TimeTrackingEntry> timeTrackingEntries = <TimeTrackingEntry>[].obs;
  final RxMap<String, double> userTimeTotals = <String, double>{}.obs;

  // دعم مقارنة المهام المشابهة
  final RxList<TaskComparison> similarTaskComparisons = <TaskComparison>[].obs;

  // دعم تصفية المساهمات
  final Rx<DateTime?> filterStartDate = Rx<DateTime?>(null);
  final Rx<DateTime?> filterEndDate = Rx<DateTime?>(null);
  final RxString filterUserId = ''.obs;
  final RxString filterTaskId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// تهيئة وحدة التحكم
  Future<void> _initializeController() async {
    try {
      await _apiService.initialize();
      debugPrint('تم تهيئة ContributionReportController بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة ContributionReportController: $e');
      error.value = 'خطأ في تهيئة النظام: ${e.toString()}';
    }
  }

  /// تحميل جميع التقارير
  Future<void> loadAllReports() async {
    try {
      isLoading.value = true;
      error.value = '';

      final allReports = await _apiService.getAllReports();
      reports.value = allReports;

      // فلترة التقارير حسب النوع
      _filterReports();

    } catch (e) {
      error.value = 'خطأ في تحميل التقارير: ${e.toString()}';
      debugPrint('خطأ في تحميل التقارير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل سجلات تتبع الوقت مع دعم التصفية
  Future<void> loadTimeTrackingEntries() async {
    try {
      isLoading.value = true;
      error.value = '';

      List<TimeTrackingEntry> entries;

      if (filterStartDate.value != null && filterEndDate.value != null) {
        entries = await _timeTrackingApiService.getTimeEntriesByDateRange(
          filterStartDate.value!,
          filterEndDate.value!,
        );
      } else {
        entries = await _timeTrackingApiService.getAllTimeEntries();
      }

      if (filterUserId.value.isNotEmpty) {
        entries = entries.where((e) => e.userId.toString() == filterUserId.value).toList();
      }
      if (filterTaskId.value.isNotEmpty) {
        entries = entries.where((e) => e.taskId.toString() == filterTaskId.value).toList();
      }

      timeTrackingEntries.value = entries;

      // حساب إجمالي الوقت لكل مستخدم
      userTimeTotals.clear();
      for (var entry in entries) {
        userTimeTotals[entry.userId.toString()] = (userTimeTotals[entry.userId.toString()] ?? 0) + (entry.duration ?? 0) / 3600.0;
      }

    } catch (e) {
      error.value = 'خطأ في تحميل سجلات تتبع الوقت: ${e.toString()}';
      debugPrint('خطأ في تحميل سجلات تتبع الوقت: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل مقارنة المهام المشابهة
  Future<void> loadSimilarTaskComparisons(String taskId) async {
    try {
      isLoading.value = true;
      error.value = '';

      // استدعاء API لتحميل المهام المشابهة (يجب تنفيذ API في الخادم)
      // مؤقتاً، نعيد قائمة فارغة
      final comparisons = <TaskComparison>[];

      similarTaskComparisons.value = comparisons;

    } catch (e) {
      error.value = 'خطأ في تحميل مقارنة المهام: ${e.toString()}';
      debugPrint('خطأ في تحميل مقارنة المهام: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث عوامل التصفية وإعادة تحميل البيانات
  Future<void> updateFilters({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? taskId,
  }) async {
    filterStartDate.value = startDate;
    filterEndDate.value = endDate;
    filterUserId.value = userId ?? '';
    filterTaskId.value = taskId ?? '';

    await loadTimeTrackingEntries();
  }

  /// تحميل تقارير المستخدم الحالي
  Future<void> loadReportsForCurrentUser() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return;
      }

      final userReports = await _apiService.getReportsByUserId(currentUser.id.toString());
      reports.value = userReports;

      // فلترة التقارير حسب النوع
      _filterReports();

    } catch (e) {
      error.value = 'خطأ في تحميل تقارير المستخدم: ${e.toString()}';
      debugPrint('خطأ في تحميل تقارير المستخدم: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقرير محدد
  Future<void> loadReport(String reportId) async {
    try {
      isLoading.value = true;
      error.value = '';

      final report = await _apiService.getReportById(reportId);
      currentReport.value = report;

      // تحميل بيانات التقرير
      await _loadReportData(reportId);

    } catch (e) {
      error.value = 'خطأ في تحميل التقرير: ${e.toString()}';
      debugPrint('خطأ في تحميل التقرير: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل بيانات التقرير
  Future<void> _loadReportData(String reportId) async {
    try {
      // تحميل الإحصائيات
      final statistics = await _apiService.getReportStatistics(reportId);
      currentStatistics.value = statistics;

      // تحميل المساهمات
      final contributions = await _apiService.getReportContributions(reportId);
      currentContributions.value = contributions;

      // تحميل ملخص المساهمات
      await _loadContributionSummary(reportId);

      // تحميل تفاصيل المساهمات
      await _loadContributionDetails(reportId);

    } catch (e) {
      debugPrint('خطأ في تحميل بيانات التقرير: $e');
    }
  }

  /// تحميل ملخص المساهمات
  Future<void> _loadContributionSummary(String reportId) async {
    try {
      // محاكاة تحميل ملخص المساهمات
      // في التطبيق الحقيقي، سيتم تحميل البيانات من API
      final summary = ContributionSummary(
        totalContributions: currentContributions.length,
        totalContributors: currentContributions.map((c) => c.userId).toSet().length,
        averageContributionPerUser: currentContributions.isEmpty
            ? 0.0
            : currentContributions.map((c) => c.value).reduce((a, b) => a + b) / currentContributions.map((c) => c.userId).toSet().length,
        highestContribution: currentContributions.isEmpty
            ? 0.0
            : currentContributions.map((c) => c.value).reduce((a, b) => a > b ? a : b),
        contributionsByType: _groupContributionsByType(),
        contributionsByUser: _groupContributionsByUser(),
      );

      contributionSummary.value = summary;
    } catch (e) {
      debugPrint('خطأ في تحميل ملخص المساهمات: $e');
    }
  }

  /// تحميل تفاصيل المساهمات
  Future<void> _loadContributionDetails(String reportId) async {
    try {
      // تحويل بيانات المساهمات إلى تفاصيل
      final details = currentContributions.map((contribution) {
        return ContributionDetail(
          id: contribution.id,
          userName: 'مستخدم ${contribution.userId}', // سيتم تحسينه لاحقاً
          taskTitle: 'مهمة ${contribution.taskId}', // سيتم تحسينه لاحقاً
          contributionType: contribution.type,
          contributionPercentage: contribution.value,
          contributionDate: contribution.timestamp,
          notes: contribution.metadata?['notes']?.toString(),
        );
      }).toList();

      contributionDetails.value = details;
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل المساهمات: $e');
    }
  }

  /// إنشاء تقرير جديد
  Future<ContributionReport?> createReport({
    required String title,
    String? description,
    String? taskId,
    String? userId,
    String? departmentId,
    int? periodDays,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? settings,
  }) async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return null;
      }

      final report = ContributionReport(
        id: '', // سيتم تعيينه من الخادم
        title: title,
        description: description,
        taskId: taskId,
        userId: userId,
        departmentId: departmentId,
        startDate: startDate,
        endDate: endDate,
        periodDays: periodDays,
        createdById: currentUser.id.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        settings: settings,
      );

      final createdReport = await _apiService.createReport(report);
      
      // إضافة التقرير إلى القائمة
      reports.add(createdReport);
      _filterReports();

      return createdReport;

    } catch (e) {
      error.value = 'خطأ في إنشاء التقرير: ${e.toString()}';
      debugPrint('خطأ في إنشاء التقرير: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    try {
      isLoading.value = true;
      error.value = '';

      final success = await _apiService.deleteReport(reportId);
      
      if (success) {
        // إزالة التقرير من القوائم
        reports.removeWhere((report) => report.id == reportId);
        _filterReports();
        
        // إذا كان التقرير المحذوف هو التقرير الحالي، قم بمسحه
        if (currentReport.value?.id == reportId) {
          currentReport.value = null;
          currentStatistics.value = null;
          currentContributions.clear();
        }
      }

      return success;

    } catch (e) {
      error.value = 'خطأ في حذف التقرير: ${e.toString()}';
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير التقرير بتنسيق PDF
  Future<String?> exportReportToPdf(String reportId) async {
    try {
      return await _apiService.exportReportToPdf(reportId);
    } catch (e) {
      error.value = 'خطأ في تصدير التقرير: ${e.toString()}';
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// تصدير التقرير بتنسيق Excel
  Future<String?> exportReportToExcel(String reportId) async {
    try {
      return await _apiService.exportReportToExcel(reportId);
    } catch (e) {
      error.value = 'خطأ في تصدير التقرير: ${e.toString()}';
      debugPrint('خطأ في تصدير التقرير: $e');
      return null;
    }
  }

  /// فلترة التقارير حسب النوع
  void _filterReports() {
    userReports.value = reports.where((report) => report.userId != null).toList();
    taskReports.value = reports.where((report) => report.taskId != null).toList();
    departmentReports.value = reports.where((report) => report.departmentId != null).toList();
  }

  /// تحديث التقرير
  Future<bool> updateReport(ContributionReport report) async {
    try {
      isLoading.value = true;
      error.value = '';

      final updatedReport = await _apiService.updateReport(report);
      
      // تحديث التقرير في القائمة
      final index = reports.indexWhere((r) => r.id == report.id);
      if (index != -1) {
        reports[index] = updatedReport;
        _filterReports();
      }

      // تحديث التقرير الحالي إذا كان نفس التقرير
      if (currentReport.value?.id == report.id) {
        currentReport.value = updatedReport;
      }

      return true;

    } catch (e) {
      error.value = 'خطأ في تحديث التقرير: ${e.toString()}';
      debugPrint('خطأ في تحديث التقرير: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// مسح البيانات
  void clearData() {
    reports.clear();
    userReports.clear();
    taskReports.clear();
    departmentReports.clear();
    currentReport.value = null;
    currentStatistics.value = null;
    currentContributions.clear();
    contributionSummary.value = null;
    contributionDetails.clear();
    error.value = '';
  }

  /// تجميع المساهمات حسب النوع
  Map<String, int> _groupContributionsByType() {
    final Map<String, int> grouped = {};
    for (final contribution in currentContributions) {
      grouped[contribution.type] = (grouped[contribution.type] ?? 0) + 1;
    }
    return grouped;
  }

  /// تجميع المساهمات حسب المستخدم
  Map<String, double> _groupContributionsByUser() {
    final Map<String, double> grouped = {};
    for (final contribution in currentContributions) {
      grouped[contribution.userId] = (grouped[contribution.userId] ?? 0.0) + contribution.value;
    }
    return grouped;
  }

  /// الحصول على نص نوع المساهمة
  String getContributionTypeText(String type) {
    switch (type.toLowerCase()) {
      case 'transfer':
        return 'نقل';
      case 'file':
        return 'ملف';
      case 'comment':
        return 'تعليق';
      case 'manual':
        return 'يدوي';
      case 'auto':
        return 'تلقائي';
      case 'activity':
        return 'نشاط';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم المستخدم
  Future<String> getUserName(String userId) async {
    try {
      // في التطبيق الحقيقي، سيتم تحميل اسم المستخدم من API
      // مؤقتاً، نرجع اسم افتراضي
      return 'مستخدم $userId';
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم المستخدم: $e');
      return 'مستخدم غير معروف';
    }
  }
}
