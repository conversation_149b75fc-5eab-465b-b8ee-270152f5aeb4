/// نموذج إعدادات النظام
class SystemSettings {
  final int id;
  final String key;
  final String value;
  final String? description;
  final String category;
  final String dataType;
  final bool isEditable;
  final bool isVisible;
  final int? createdAt;
  final int? updatedAt;

  const SystemSettings({
    required this.id,
    required this.key,
    required this.value,
    this.description,
    required this.category,
    required this.dataType,
    this.isEditable = true,
    this.isVisible = true,
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء من JSON
  factory SystemSettings.fromJson(Map<String, dynamic> json) {
    return SystemSettings(
      id: json['id'] ?? 0,
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      description: json['description'],
      category: json['category'] ?? 'general',
      dataType: json['dataType'] ?? 'string',
      isEditable: json['isEditable'] ?? true,
      isVisible: json['isVisible'] ?? true,
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'description': description,
      'category': category,
      'dataType': dataType,
      'isEditable': isEditable,
      'isVisible': isVisible,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// نسخ مع تعديل
  SystemSettings copyWith({
    int? id,
    String? key,
    String? value,
    String? description,
    String? category,
    String? dataType,
    bool? isEditable,
    bool? isVisible,
    int? createdAt,
    int? updatedAt,
  }) {
    return SystemSettings(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      category: category ?? this.category,
      dataType: dataType ?? this.dataType,
      isEditable: isEditable ?? this.isEditable,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على القيمة كـ bool
  bool get boolValue {
    return value.toLowerCase() == 'true' || value == '1';
  }

  /// الحصول على القيمة كـ int
  int get intValue {
    return int.tryParse(value) ?? 0;
  }

  /// الحصول على القيمة كـ double
  double get doubleValue {
    return double.tryParse(value) ?? 0.0;
  }

  @override
  String toString() {
    return 'SystemSettings(id: $id, key: $key, value: $value, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemSettings &&
        other.id == id &&
        other.key == key &&
        other.value == value;
  }

  @override
  int get hashCode {
    return id.hashCode ^ key.hashCode ^ value.hashCode;
  }
}

/// فئات الإعدادات
class SettingsCategory {
  static const String general = 'general';
  static const String security = 'security';
  static const String notifications = 'notifications';
  static const String appearance = 'appearance';
  static const String performance = 'performance';
  static const String backup = 'backup';
  static const String email = 'email';
  static const String database = 'database';
}

/// أنواع البيانات
class SettingsDataType {
  static const String string = 'string';
  static const String boolean = 'boolean';
  static const String integer = 'integer';
  static const String decimal = 'decimal';
  static const String json = 'json';
  static const String password = 'password';
}

/// إعدادات افتراضية للنظام
class DefaultSystemSettings {
  static List<SystemSettings> get defaults => [
    // إعدادات عامة
    const SystemSettings(
      id: 0,
      key: 'app_name',
      value: 'نظام إدارة المهام',
      description: 'اسم التطبيق',
      category: SettingsCategory.general,
      dataType: SettingsDataType.string,
    ),
    const SystemSettings(
      id: 0,
      key: 'app_version',
      value: '1.0.0',
      description: 'إصدار التطبيق',
      category: SettingsCategory.general,
      dataType: SettingsDataType.string,
      isEditable: false,
    ),
    const SystemSettings(
      id: 0,
      key: 'max_users',
      value: '1000',
      description: 'الحد الأقصى للمستخدمين',
      category: SettingsCategory.general,
      dataType: SettingsDataType.integer,
    ),
    
    // إعدادات الأمان
    const SystemSettings(
      id: 0,
      key: 'session_timeout',
      value: '30',
      description: 'انتهاء الجلسة (بالدقائق)',
      category: SettingsCategory.security,
      dataType: SettingsDataType.integer,
    ),
    const SystemSettings(
      id: 0,
      key: 'password_min_length',
      value: '6',
      description: 'الحد الأدنى لطول كلمة المرور',
      category: SettingsCategory.security,
      dataType: SettingsDataType.integer,
    ),
    const SystemSettings(
      id: 0,
      key: 'enable_two_factor',
      value: 'false',
      description: 'تفعيل المصادقة الثنائية',
      category: SettingsCategory.security,
      dataType: SettingsDataType.boolean,
    ),
    
    // إعدادات الإشعارات
    const SystemSettings(
      id: 0,
      key: 'enable_email_notifications',
      value: 'true',
      description: 'تفعيل إشعارات البريد الإلكتروني',
      category: SettingsCategory.notifications,
      dataType: SettingsDataType.boolean,
    ),
    const SystemSettings(
      id: 0,
      key: 'enable_push_notifications',
      value: 'true',
      description: 'تفعيل الإشعارات الفورية',
      category: SettingsCategory.notifications,
      dataType: SettingsDataType.boolean,
    ),
    
    // إعدادات النسخ الاحتياطية
    const SystemSettings(
      id: 0,
      key: 'auto_backup_enabled',
      value: 'true',
      description: 'تفعيل النسخ الاحتياطية التلقائية',
      category: SettingsCategory.backup,
      dataType: SettingsDataType.boolean,
    ),
    const SystemSettings(
      id: 0,
      key: 'backup_frequency_hours',
      value: '24',
      description: 'تكرار النسخ الاحتياطية (بالساعات)',
      category: SettingsCategory.backup,
      dataType: SettingsDataType.integer,
    ),
  ];
}
