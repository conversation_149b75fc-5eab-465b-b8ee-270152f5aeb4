import 'package:signalr_netcore/signalr_client.dart';
import 'package:get/get.dart'; // استخدام GetX Rx لإدارة الحالة
import '../models/message_models.dart'; // استيراد نموذج الرسالة
import '../utils/logger.dart'; // استيراد Logger

/// خدمة SignalR للتواصل اللحظي
class SignalRService extends GetxService {
  // تغيير إلى GetxService
  HubConnection? _hubConnection;

  // Public getter to access the hub connection
  HubConnection? get hubConnection => _hubConnection;

  // Use the correct API URL for development
  final String _hubUrl = "http://localhost:7111/taskHub"; // Updated URL - تغيير من HTTPS إلى HTTP

  // Rx متغير لبث الرسائل الجديدة
  final Rx<Message?> _newMessage =
      Rx<Message?>(null); // Rx متغير للرسائل الجديدة

  // Getter للوصول إلى بث الرسائل الجديدة
  Rx<Message?> get newMessageStream => _newMessage; // Getter للبث

  @override
  void onInit() {
    super.onInit();
    connect(); // الاتصال بـ SignalR عند تهيئة الخدمة
  }

  @override
  void onClose() {
    disconnect(); // قطع الاتصال بـ SignalR عند إغلاق الخدمة
    _newMessage.close(); // إغلاق بث الرسائل
    super.onClose();
  }

  Future<void> connect() async {
    if (_hubConnection?.state == HubConnectionState.Connected) {
      AppLogger.debug('SignalR Connection already connected.');
      return;
    }

    _hubConnection = HubConnectionBuilder().withUrl(_hubUrl).build();

    _hubConnection?.onclose((error) {
      AppLogger.error("SignalR Connection closed: $error");
      // Implement reconnection logic here
    });

    _hubConnection?.on("TaskUpdated", (arguments) {
      AppLogger.debug("Task Updated: $arguments");
      // Process the received task data and update UI
      // arguments will be a List, the first element is the task object
    });

    _hubConnection?.on("TaskStatusUpdated", (arguments) {
      if (arguments != null && arguments.length >= 2) {
        int taskId = arguments[0];
        String newStatus = arguments[1];
        AppLogger.debug("Task $taskId status updated to $newStatus");
        // Update UI for this specific task
      }
    });

    // Add handler for receiving chat messages
    _hubConnection?.on("ReceiveMessage", (arguments) {
      // Added chat message handler
      AppLogger.debug("Received Message: $arguments");
      if (arguments != null && arguments.isNotEmpty) {
        try {
          // Assuming the first argument is the message object
          var messageData = arguments[0];
          // You'll need to parse this into your Message model
          // Ensure your Message model has a fromJson constructor
          Message newMessage =
              Message.fromJson(messageData); // Assuming fromJson exists
          _newMessage.value = newMessage; // بث الرسالة الجديدة عبر Rx
        } catch (e) {
          AppLogger.error("Error processing received message: $e");
        }
      }
    });

    try {
      await _hubConnection?.start();
      AppLogger.debug("SignalR Connection started");
    } catch (e) {
      AppLogger.error("Error starting SignalR connection: $e");
    }
  }

  Future<void> disconnect() async {
    if (_hubConnection?.state == HubConnectionState.Connected) {
      await _hubConnection?.stop();
      AppLogger.debug("SignalR Connection stopped");
    }
  }

  // Method to send messages to the hub (if needed) - Note: Sending might be handled by API
  Future<void> sendMessageToHub(String groupId, Message message) async {
    // Added method to send via hub
    if (_hubConnection?.state == HubConnectionState.Connected) {
      // The backend Hub method expects groupId (string) and message object
      await _hubConnection?.invoke("SendMessageToGroup",
          args: [groupId, message.toJson()]); // Assuming toJson exists
      AppLogger.debug('Sent message to group $groupId via SignalR hub');
    } else {
      AppLogger.warning(
          "SignalR connection not connected. Cannot send message via hub.");
    }
  }

  // Method to join a chat group via the hub
  Future<void> joinChatGroup(String groupId) async {
    // Added method to join group via hub
    if (_hubConnection?.state == HubConnectionState.Connected) {
      await _hubConnection?.invoke("JoinGroup", args: [groupId]);
      AppLogger.debug("Joined SignalR group: $groupId");
    } else {
      AppLogger.warning("SignalR connection not connected. Cannot join group.");
    }
  }

  // Method to leave a chat group via the hub
  Future<void> leaveChatGroup(String groupId) async {
    // Added method to leave group via hub
    if (_hubConnection?.state == HubConnectionState.Connected) {
      await _hubConnection?.invoke("LeaveGroup", args: [groupId]);
      AppLogger.debug("Left SignalR group: $groupId");
    } else {
      AppLogger.warning(
          "SignalR connection not connected. Cannot leave group.");
    }
  }
}
