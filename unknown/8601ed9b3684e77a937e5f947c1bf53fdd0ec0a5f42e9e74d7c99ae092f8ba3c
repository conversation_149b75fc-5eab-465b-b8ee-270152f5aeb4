using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة سجلات الأنشطة
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ActivityLogsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ActivityLogsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع سجلات الأنشطة
        /// </summary>
        /// <returns>قائمة بجميع سجلات الأنشطة</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetActivityLogs()
        {
            try
            {
                var activityLogs = await _context.ActivityLogs
                    .Include(al => al.Task)
                    .Include(al => al.User)
                    .Include(al => al.ChangedByNavigation)
                    .OrderByDescending(al => al.Timestamp)
                    .ToListAsync();

                return Ok(activityLogs);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحميل سجلات الأنشطة", error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على سجل نشاط محدد
        /// </summary>
        /// <param name="id">معرف سجل النشاط</param>
        /// <returns>سجل النشاط المطلوب</returns>
        /// <response code="200">إرجاع سجل النشاط</response>
        /// <response code="404">سجل النشاط غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ActivityLog>> GetActivityLog(int id)
        {
            var activityLog = await _context.ActivityLogs
                .Include(al => al.Task)
                .Include(al => al.User)
                .Include(al => al.ChangedByNavigation)
                .FirstOrDefaultAsync(al => al.Id == id);

            if (activityLog == null)
            {
                return NotFound();
            }

            return activityLog;
        }

        /// <summary>
        /// الحصول على سجلات أنشطة مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>قائمة سجلات أنشطة المهمة</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetTaskActivityLogs(int taskId)
        {
            return await _context.ActivityLogs
                .Include(al => al.User)
                .Include(al => al.ChangedByNavigation)
                .Where(al => al.TaskId == taskId)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجلات أنشطة مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة سجلات أنشطة المستخدم</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetUserActivityLogs(int userId)
        {
            return await _context.ActivityLogs
                .Include(al => al.Task)
                .Include(al => al.ChangedByNavigation)
                .Where(al => al.UserId == userId)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجلات الأنشطة حسب نوع التغيير
        /// </summary>
        /// <param name="changeType">نوع التغيير</param>
        /// <returns>قائمة سجلات الأنشطة من النوع المحدد</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet("change-type/{changeType}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetActivityLogsByChangeType(string changeType)
        {
            return await _context.ActivityLogs
                .Include(al => al.Task)
                .Include(al => al.User)
                .Include(al => al.ChangedByNavigation)
                .Where(al => al.ChangeType == changeType)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجلات الأنشطة حسب الإجراء
        /// </summary>
        /// <param name="action">الإجراء</param>
        /// <returns>قائمة سجلات الأنشطة للإجراء المحدد</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet("action/{action}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetActivityLogsByAction(string action)
        {
            return await _context.ActivityLogs
                .Include(al => al.Task)
                .Include(al => al.User)
                .Include(al => al.ChangedByNavigation)
                .Where(al => al.Action == action)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجلات الأنشطة في فترة زمنية محددة
        /// </summary>
        /// <param name="startDate">تاريخ البداية (Unix timestamp)</param>
        /// <param name="endDate">تاريخ النهاية (Unix timestamp)</param>
        /// <returns>قائمة سجلات الأنشطة في الفترة المحددة</returns>
        /// <response code="200">إرجاع قائمة سجلات الأنشطة</response>
        [HttpGet("date-range")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ActivityLog>>> GetActivityLogsByDateRange(
            [FromQuery] long startDate, 
            [FromQuery] long endDate)
        {
            return await _context.ActivityLogs
                .Include(al => al.Task)
                .Include(al => al.User)
                .Include(al => al.ChangedByNavigation)
                .Where(al => al.Timestamp >= startDate && al.Timestamp <= endDate)
                .OrderByDescending(al => al.Timestamp)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على أنواع التغييرات المتاحة
        /// </summary>
        /// <returns>قائمة أنواع التغييرات</returns>
        /// <response code="200">إرجاع قائمة أنواع التغييرات</response>
        [HttpGet("change-types")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetChangeTypes()
        {
            var changeTypes = await _context.ActivityLogs
                .Where(al => !string.IsNullOrEmpty(al.ChangeType))
                .Select(al => al.ChangeType!)
                .Distinct()
                .OrderBy(ct => ct)
                .ToListAsync();

            return changeTypes;
        }

        /// <summary>
        /// الحصول على الإجراءات المتاحة
        /// </summary>
        /// <returns>قائمة الإجراءات</returns>
        /// <response code="200">إرجاع قائمة الإجراءات</response>
        [HttpGet("actions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetActions()
        {
            var actions = await _context.ActivityLogs
                .Where(al => !string.IsNullOrEmpty(al.Action))
                .Select(al => al.Action!)
                .Distinct()
                .OrderBy(a => a)
                .ToListAsync();

            return actions;
        }

        /// <summary>
        /// إنشاء سجل نشاط جديد
        /// </summary>
        /// <param name="activityLog">بيانات سجل النشاط</param>
        /// <returns>سجل النشاط المُنشأ</returns>
        /// <response code="201">تم إنشاء سجل النشاط بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ActivityLog>> PostActivityLog(ActivityLog activityLog)
        {
            // التحقق من وجود المستخدم
            var userExists = await _context.Users.AnyAsync(u => u.Id == activityLog.UserId && !u.IsDeleted);
            if (!userExists)
            {
                return BadRequest("المستخدم غير موجود");
            }

            // التحقق من وجود المهمة إذا كانت محددة
            if (activityLog.TaskId > 0)
            {
                var taskExists = await _context.Tasks.AnyAsync(t => t.Id == activityLog.TaskId && !t.IsDeleted);
                if (!taskExists)
                {
                    return BadRequest("المهمة غير موجودة");
                }
            }

            // تعيين الطابع الزمني الحالي إذا لم يكن محدداً
            if (activityLog.Timestamp == 0)
            {
                activityLog.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            }

            if (activityLog.ChangedAt == null || activityLog.ChangedAt == 0)
            {
                activityLog.ChangedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            }

            _context.ActivityLogs.Add(activityLog);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetActivityLog", new { id = activityLog.Id }, activityLog);
        }

        /// <summary>
        /// تسجيل نشاط بسيط
        /// </summary>
        /// <param name="action">الإجراء</param>
        /// <param name="entityType">نوع الكيان (task, user)</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <param name="description">الوصف</param>
        /// <param name="userId">معرف المستخدم (اختياري)</param>
        /// <returns>سجل النشاط المُنشأ</returns>
        /// <response code="201">تم تسجيل النشاط بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost("log")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ActivityLog>> LogActivity(
            [FromQuery] string action,
            [FromQuery] string entityType,
            [FromQuery] int entityId,
            [FromQuery] string? description = null,
            [FromQuery] int? userId = null)
        {
            // تحديد userId إذا لم يكن محدداً (يمكن الحصول عليه من JWT token)
            if (userId == null)
            {
                // هنا يمكن الحصول على userId من JWT token
                userId = 1; // قيمة افتراضية للاختبار
            }

            var activityLog = new ActivityLog
            {
                Action = action,
                TaskId = entityType.ToLower() == "task" ? entityId : 0,
                UserId = entityType.ToLower() == "user" ? entityId : userId.Value,
                Details = description,
                ChangeDescription = description,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                ChangedBy = userId,
                ChangedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                ChangeType = $"{entityType}_activity"
            };

            return await PostActivityLog(activityLog);
        }


    }
}
