import 'package:flutter/material.dart';

/// نتيجة التقرير
class ReportResult {
  final bool isSuccess;
  final DateTime generatedAt;
  final Map<String, dynamic>? summary;
  final List<Map<String, dynamic>>? data;
  final Map<String, List<Map<String, dynamic>>>? visualizationData;
  final List<String>? errorMessages;
  final String? filePath;
  final int? totalRecords;
  final Map<String, dynamic>? metadata;

  const ReportResult({
    required this.isSuccess,
    required this.generatedAt,
    this.summary,
    this.data,
    this.visualizationData,
    this.errorMessages,
    this.filePath,
    this.totalRecords,
    this.metadata,
  });

  factory ReportResult.fromJson(Map<String, dynamic> json) {
    return ReportResult(
      isSuccess: json['isSuccess'] as bool? ?? false,
      generatedAt: json['generatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['generatedAt'] as int)
          : DateTime.now(),
      summary: json['summary'] as Map<String, dynamic>?,
      data: json['data'] != null 
          ? List<Map<String, dynamic>>.from(json['data'] as List)
          : null,
      visualizationData: json['visualizationData'] != null
          ? Map<String, List<Map<String, dynamic>>>.from(
              (json['visualizationData'] as Map).map(
                (key, value) => MapEntry(
                  key.toString(),
                  List<Map<String, dynamic>>.from(value as List),
                ),
              ),
            )
          : null,
      errorMessages: json['errorMessages'] != null 
          ? List<String>.from(json['errorMessages'] as List)
          : null,
      filePath: json['filePath'] as String?,
      totalRecords: json['totalRecords'] as int?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'generatedAt': generatedAt.millisecondsSinceEpoch,
      'summary': summary,
      'data': data,
      'visualizationData': visualizationData,
      'errorMessages': errorMessages,
      'filePath': filePath,
      'totalRecords': totalRecords,
      'metadata': metadata,
    };
  }

  /// إنشاء نتيجة نجح
  factory ReportResult.success({
    required DateTime generatedAt,
    Map<String, dynamic>? summary,
    List<Map<String, dynamic>>? data,
    Map<String, List<Map<String, dynamic>>>? visualizationData,
    String? filePath,
    int? totalRecords,
    Map<String, dynamic>? metadata,
  }) {
    return ReportResult(
      isSuccess: true,
      generatedAt: generatedAt,
      summary: summary,
      data: data,
      visualizationData: visualizationData,
      filePath: filePath,
      totalRecords: totalRecords,
      metadata: metadata,
    );
  }

  /// إنشاء نتيجة فشل
  factory ReportResult.failure({
    required DateTime generatedAt,
    required List<String> errorMessages,
    Map<String, dynamic>? metadata,
  }) {
    return ReportResult(
      isSuccess: false,
      generatedAt: generatedAt,
      errorMessages: errorMessages,
      metadata: metadata,
    );
  }
}

/// فترات التقرير
enum ReportPeriod {
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  thisQuarter,
  lastQuarter,
  thisYear,
  lastYear,
  custom,
}

/// عوامل التصفية
enum FilterOperator {
  equals,
  notEquals,
  contains,
  notContains,
  startsWith,
  endsWith,
  greaterThan,
  lessThan,
  greaterOrEqual,
  lessOrEqual,
  between,
  isIn,
  notIn,
  isNull,
  isNotNull,
  before,
  after,
  onOrBefore,
  onOrAfter,
  inRange,
}

/// فلتر التقرير
class ReportFilter {
  final String field;
  final String label;
  final FilterOperator operator;
  final dynamic value;
  final dynamic value2;
  final List<dynamic>? values;
  final bool isActive;

  const ReportFilter({
    required this.field,
    required this.label,
    required this.operator,
    this.value,
    this.value2,
    this.values,
    this.isActive = true,
  });

  factory ReportFilter.fromJson(Map<String, dynamic> json) {
    return ReportFilter(
      field: json['field'] as String,
      label: json['label'] as String,
      operator: FilterOperator.values.firstWhere(
        (e) => e.name == json['operator'],
        orElse: () => FilterOperator.equals,
      ),
      value: json['value'],
      value2: json['value2'],
      values: json['values'] != null ? List.from(json['values'] as List) : null,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'label': label,
      'operator': operator.name,
      'value': value,
      'value2': value2,
      'values': values,
      'isActive': isActive,
    };
  }

  ReportFilter copyWith({
    String? field,
    String? label,
    FilterOperator? operator,
    dynamic value,
    dynamic value2,
    List<dynamic>? values,
    bool? isActive,
  }) {
    return ReportFilter(
      field: field ?? this.field,
      label: label ?? this.label,
      operator: operator ?? this.operator,
      value: value ?? this.value,
      value2: value2 ?? this.value2,
      values: values ?? this.values,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// أنواع التصور المرئي
enum VisualizationType {
  barChart,
  lineChart,
  pieChart,
  areaChart,
  table,
  kpiCard,
  gaugeChart,
  heatMap,
  radarChart,
  bubbleChart,
  ganttChart,
  treeMap,
  summary,
  custom,
}

/// اتجاه المخطط
enum ChartOrientation {
  vertical,
  horizontal,
}

/// موضع المفتاح
enum LegendPosition {
  top,
  bottom,
  left,
  right,
}

/// تصور مرئي للتقرير
class ReportVisualization {
  final String id;
  final String title;
  final String? description;
  final VisualizationType type;
  final String? xAxisField;
  final String? xAxisLabel;
  final String? yAxisField;
  final String? yAxisLabel;
  final List<String> dataFields;
  final List<String>? dataLabels;
  final ChartOrientation orientation;
  final bool showValues;
  final bool showLabels;
  final bool showGrid;
  final bool showLegend;
  final LegendPosition? legendPosition;
  final List<Color>? seriesColors;
  final double? width;
  final double? height;
  final Map<String, dynamic>? settings;

  const ReportVisualization({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.xAxisField,
    this.xAxisLabel,
    this.yAxisField,
    this.yAxisLabel,
    this.dataFields = const [],
    this.dataLabels,
    this.orientation = ChartOrientation.vertical,
    this.showValues = true,
    this.showLabels = true,
    this.showGrid = true,
    this.showLegend = true,
    this.legendPosition,
    this.seriesColors,
    this.width,
    this.height,
    this.settings,
  });

  factory ReportVisualization.fromJson(Map<String, dynamic> json) {
    return ReportVisualization(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      type: VisualizationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => VisualizationType.table,
      ),
      xAxisField: json['xAxisField'] as String?,
      xAxisLabel: json['xAxisLabel'] as String?,
      yAxisField: json['yAxisField'] as String?,
      yAxisLabel: json['yAxisLabel'] as String?,
      dataFields: json['dataFields'] != null
          ? List<String>.from(json['dataFields'] as List)
          : [],
      dataLabels: json['dataLabels'] != null
          ? List<String>.from(json['dataLabels'] as List)
          : null,
      orientation: json['orientation'] == 'horizontal'
          ? ChartOrientation.horizontal
          : ChartOrientation.vertical,
      showValues: json['showValues'] as bool? ?? true,
      showLabels: json['showLabels'] as bool? ?? true,
      showGrid: json['showGrid'] as bool? ?? true,
      showLegend: json['showLegend'] as bool? ?? true,
      legendPosition: json['legendPosition'] != null
          ? LegendPosition.values.firstWhere(
              (e) => e.name == json['legendPosition'],
              orElse: () => LegendPosition.bottom,
            )
          : null,
      width: json['width'] as double?,
      height: json['height'] as double?,
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'xAxisField': xAxisField,
      'xAxisLabel': xAxisLabel,
      'yAxisField': yAxisField,
      'yAxisLabel': yAxisLabel,
      'dataFields': dataFields,
      'dataLabels': dataLabels,
      'orientation': orientation.name,
      'showValues': showValues,
      'showLabels': showLabels,
      'showGrid': showGrid,
      'showLegend': showLegend,
      'legendPosition': legendPosition?.name,
      'width': width,
      'height': height,
      'settings': settings,
    };
  }
}
