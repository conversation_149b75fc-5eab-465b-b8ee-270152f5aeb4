import 'package:flutter/foundation.dart';
import '../../models/task_history_models.dart';
import 'api_service.dart';

/// خدمة API لتاريخ المهام - متطابقة مع ASP.NET Core API
class TaskHistoryApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع سجلات تاريخ المهام
  Future<List<TaskHistory>> getAllHistory() async {
    try {
      final response = await _apiService.get('/api/TaskHistory');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ المهام: $e');
      rethrow;
    }
  }

  /// الحصول على سجل تاريخ مهمة بواسطة المعرف
  Future<TaskHistory?> getHistoryById(int id) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/$id');
      return _apiService.handleResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل تاريخ المهمة: $e');
      return null;
    }
  }

  /// إنشاء سجل تاريخ مهمة جديد
  Future<TaskHistory> createHistory(TaskHistory history) async {
    try {
      final response = await _apiService.post(
        '/api/TaskHistory',
        history.toJson(),
      );
      return _apiService.handleResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل تاريخ المهمة: $e');
      rethrow;
    }
  }

  /// تحديث سجل تاريخ مهمة
  Future<TaskHistory> updateHistory(TaskHistory history) async {
    try {
      final response = await _apiService.put(
        '/api/TaskHistory/${history.id}',
        history.toJson(),
      );
      return _apiService.handleResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث سجل تاريخ المهمة: $e');
      rethrow;
    }
  }

  /// حذف سجل تاريخ مهمة
  Future<bool> deleteHistory(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskHistory/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف سجل تاريخ المهمة: $e');
      return false;
    }
  }

  /// الحصول على تاريخ مهمة محددة
  Future<List<TaskHistory>> getHistoryByTask(int taskId) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/task/$taskId');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ المهمة: $e');
      rethrow;
    }
  }

  /// الحصول على تاريخ مستخدم محدد
  Future<List<TaskHistory>> getHistoryByUser(int userId) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/user/$userId');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ المستخدم: $e');
      rethrow;
    }
  }

  /// الحصول على التاريخ حسب الإجراء
  Future<List<TaskHistory>> getHistoryByAction(String action) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/action/$action');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ الإجراء: $e');
      rethrow;
    }
  }

  /// الحصول على التاريخ حسب الفترة الزمنية
  Future<List<TaskHistory>> getHistoryByDateRange(DateTime from, DateTime to) async {
    try {
      final fromTimestamp = from.millisecondsSinceEpoch ~/ 1000;
      final toTimestamp = to.millisecondsSinceEpoch ~/ 1000;

      final response = await _apiService.get(
        '/api/TaskHistory/date-range?startDate=$fromTimestamp&endDate=$toTimestamp'
      );
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ الفترة الزمنية: $e');
      rethrow;
    }
  }

  /// تسجيل إجراء جديد على المهمة
  Future<TaskHistory> logTaskAction(
    int taskId,
    String action,
    String description,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
  ) async {
    try {
      final requestBody = {
        'taskId': taskId,
        'action': action,
        'description': description,
        'oldValues': oldValues,
        'newValues': newValues,
        'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      };

      final response = await _apiService.post('/api/TaskHistory/log-action', requestBody);
      return _apiService.handleResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل إجراء المهمة: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات التاريخ
  Future<Map<String, dynamic>> getHistoryStatistics() async {
    try {
      final response = await _apiService.get('/api/TaskHistory/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات التاريخ: $e');
      rethrow;
    }
  }

  /// تصدير تاريخ المهام
  Future<String> exportHistory(String format, DateTime? from, DateTime? to) async {
    try {
      var url = '/api/TaskHistory/export?format=$format';
      if (from != null && to != null) {
        final fromTimestamp = from.millisecondsSinceEpoch ~/ 1000;
        final toTimestamp = to.millisecondsSinceEpoch ~/ 1000;
        url += '&startDate=$fromTimestamp&endDate=$toTimestamp';
      }
      
      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body;
      }
      throw Exception('فشل في تصدير التاريخ');
    } catch (e) {
      debugPrint('خطأ في تصدير تاريخ المهام: $e');
      rethrow;
    }
  }

  /// مسح التاريخ القديم
  Future<int> clearOldHistory(int daysOld) async {
    try {
      final response = await _apiService.delete('/api/TaskHistory/clear-old?daysOld=$daysOld');
      final result = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return result['deletedCount'] as int? ?? 0;
    } catch (e) {
      debugPrint('خطأ في مسح التاريخ القديم: $e');
      rethrow;
    }
  }

  /// البحث في تاريخ المهام
  Future<List<TaskHistory>> searchHistory(String query) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/search?query=${Uri.encodeComponent(query)}');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في تاريخ المهام: $e');
      rethrow;
    }
  }

  /// الحصول على أحدث التغييرات
  Future<List<TaskHistory>> getRecentChanges({int limit = 50}) async {
    try {
      final response = await _apiService.get('/api/TaskHistory/recent?limit=$limit');
      return _apiService.handleListResponse<TaskHistory>(
        response,
        (json) => TaskHistory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحدث التغييرات: $e');
      rethrow;
    }
  }
}
