import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms & Conditions'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms and Conditions',
              style: AppStyles.headingMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              title: '1. Introduction',
              content: 'Welcome to Task Management System. These Terms and Conditions govern your use of our application and services. By using our application, you accept these terms and conditions in full. If you disagree with these terms and conditions or any part of them, you must not use our application.',
            ),
            
            _buildSection(
              title: '2. License to use application',
              content: 'Unless otherwise stated, we or our licensors own the intellectual property rights in the application and material on the application. Subject to the license below, all these intellectual property rights are reserved.\n\nYou may view, download for caching purposes only, and print pages from the application for your own personal use, subject to the restrictions set out below and elsewhere in these terms and conditions.',
            ),
            
            _buildSection(
              title: '3. Acceptable use',
              content: 'You must not use our application in any way that causes, or may cause, damage to the application or impairment of the availability or accessibility of the application; or in any way which is unlawful, illegal, fraudulent or harmful, or in connection with any unlawful, illegal, fraudulent or harmful purpose or activity.',
            ),
            
            _buildSection(
              title: '4. User content',
              content: 'In these terms and conditions, "your user content" means material (including without limitation text, images, audio material, video material and audio-visual material) that you submit to our application, for whatever purpose.\n\nYou grant to us a worldwide, irrevocable, non-exclusive, royalty-free license to use, reproduce, adapt, publish, translate and distribute your user content in any existing or future media. You also grant to us the right to sub-license these rights, and the right to bring an action for infringement of these rights.',
            ),
            
            _buildSection(
              title: '5. Limitation of liability',
              content: 'Nothing in these terms and conditions will: (a) limit or exclude our or your liability for death or personal injury resulting from negligence; (b) limit or exclude our or your liability for fraud or fraudulent misrepresentation; (c) limit any of our or your liabilities in any way that is not permitted under applicable law; or (d) exclude any of our or your liabilities that may not be excluded under applicable law.',
            ),
            
            _buildSection(
              title: '6. Indemnity',
              content: 'You hereby indemnify us and undertake to keep us indemnified against any losses, damages, costs, liabilities and expenses (including without limitation legal expenses and any amounts paid by us to a third party in settlement of a claim or dispute on the advice of our legal advisers) incurred or suffered by us arising out of any breach by you of any provision of these terms and conditions, or arising out of any claim that you have breached any provision of these terms and conditions.',
            ),
            
            _buildSection(
              title: '7. Breaches of these terms and conditions',
              content: 'Without prejudice to our other rights under these terms and conditions, if you breach these terms and conditions in any way, we may take such action as we deem appropriate to deal with the breach, including suspending your access to the application, prohibiting you from accessing the application, blocking computers using your IP address from accessing the application, contacting your internet service provider to request that they block your access to the application and/or bringing court proceedings against you.',
            ),
            
            _buildSection(
              title: '8. Variation',
              content: 'We may revise these terms and conditions from time-to-time. Revised terms and conditions will apply to the use of our application from the date of the publication of the revised terms and conditions on our application. Please check this page regularly to ensure you are familiar with the current version.',
            ),
            
            _buildSection(
              title: '9. Assignment',
              content: 'We may transfer, sub-contract or otherwise deal with our rights and/or obligations under these terms and conditions without notifying you or obtaining your consent.\n\nYou may not transfer, sub-contract or otherwise deal with your rights and/or obligations under these terms and conditions.',
            ),
            
            _buildSection(
              title: '10. Severability',
              content: 'If a provision of these terms and conditions is determined by any court or other competent authority to be unlawful and/or unenforceable, the other provisions will continue in effect. If any unlawful and/or unenforceable provision would be lawful or enforceable if part of it were deleted, that part will be deemed to be deleted, and the rest of the provision will continue in effect.',
            ),
            
            _buildSection(
              title: '11. Entire agreement',
              content: 'These terms and conditions constitute the entire agreement between you and us in relation to your use of our application, and supersede all previous agreements in respect of your use of this application.',
            ),
            
            _buildSection(
              title: '12. Law and jurisdiction',
              content: 'These terms and conditions will be governed by and construed in accordance with the laws of [JURISDICTION], and any disputes relating to these terms and conditions will be subject to the exclusive jurisdiction of the courts of [JURISDICTION].',
            ),
            
            const SizedBox(height: 32),
            
            // Contact information
            Text(
              'Contact Information',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            const Text(
              'If you have any questions about these Terms and Conditions, please contact us:',
            ),
            const SizedBox(height: 8),
            const Text('Email: <EMAIL>'),
            const Text('Phone: +****************'),
            const Text('Address: 123 Legal Street, City, Country'),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: AppStyles.bodyMedium,
          ),
        ],
      ),
    );
  }
}
