import 'package:flutter/material.dart';
import '../../../models/archive_models.dart';

/// أداة اختيار الوسوم
class TagSelector extends StatefulWidget {
  /// قائمة الوسوم المتاحة
  final List<ArchiveTag> tags;

  /// قائمة الوسوم المحددة
  final List<ArchiveTag> selectedTags;

  /// دالة يتم استدعاؤها عند تغيير الوسوم المحددة
  final Function(List<ArchiveTag>) onTagsChanged;

  const TagSelector({
    super.key,
    required this.tags,
    required this.selectedTags,
    required this.onTagsChanged,
  });

  @override
  State<TagSelector> createState() => _TagSelectorState();
}

class _TagSelectorState extends State<TagSelector> {
  // متحكم البحث
  final TextEditingController _searchController = TextEditingController();

  // قائمة الوسوم المصفاة
  List<ArchiveTag> _filteredTags = [];

  @override
  void initState() {
    super.initState();
    _filteredTags = List.from(widget.tags);

    // الاستماع لتغييرات البحث
    _searchController.addListener(_filterTags);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(TagSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    // تحديث عند تغيير قائمة الوسوم
    _filterTags();
  }

  /// تصفية الوسوم حسب نص البحث
  void _filterTags() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredTags = List.from(widget.tags);
      } else {
        _filteredTags = widget.tags
            .where((tag) => tag.name.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  /// إضافة وسم إلى القائمة المحددة
  void _addTag(ArchiveTag tag) {
    if (!widget.selectedTags.contains(tag)) {
      final updatedTags = List<ArchiveTag>.from(widget.selectedTags)..add(tag);
      widget.onTagsChanged(updatedTags);
    }
  }

  /// إزالة وسم من القائمة المحددة
  void _removeTag(ArchiveTag tag) {
    final updatedTags = List<ArchiveTag>.from(widget.selectedTags)..remove(tag);
    widget.onTagsChanged(updatedTags);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان
        const Text(
          'الوسوم',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        // الوسوم المحددة
        if (widget.selectedTags.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.selectedTags.map((tag) => _buildTagChip(tag, true)).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // حقل البحث
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'بحث عن وسوم',
            hintText: 'اكتب للبحث عن وسوم',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // قائمة الوسوم المتاحة
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          height: 150,
          child: _filteredTags.isEmpty
              ? const Center(
                  child: Text('لا توجد وسوم متاحة'),
                )
              : ListView.builder(
                  itemCount: _filteredTags.length,
                  itemBuilder: (context, index) {
                    final tag = _filteredTags[index];
                    final isSelected = widget.selectedTags.contains(tag);
                    return ListTile(
                      title: Text(tag.name),
                      leading: Icon(
                        Icons.local_offer,
                        color: _getTagColor(tag.color),
                      ),
                      trailing: isSelected
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.add_circle_outline),
                      onTap: () {
                        if (isSelected) {
                          _removeTag(tag);
                        } else {
                          _addTag(tag);
                        }
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }

  /// بناء رقاقة الوسم
  Widget _buildTagChip(ArchiveTag tag, bool isDeletable) {
    return Chip(
      label: Text(
        tag.name,
        style: TextStyle(
          color: _getTextColor(_getTagColor(tag.color)),
        ),
      ),
      backgroundColor: _getTagColor(tag.color).withAlpha(51),
      deleteIcon: isDeletable ? const Icon(Icons.close, size: 16) : null,
      onDeleted: isDeletable ? () => _removeTag(tag) : null,
      deleteIconColor: _getTagColor(tag.color),
    );
  }

  /// الحصول على لون الوسم من سلسلة نصية
  Color _getTagColor(String? colorString) {
    try {
      if (colorString == null || colorString.isEmpty) {
        return Colors.blue;
      }
      return Color(int.parse(colorString.replaceAll('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  /// الحصول على لون النص المناسب (أبيض أو أسود) بناءً على لون الخلفية
  Color _getTextColor(Color backgroundColor) {
    // حساب درجة سطوع اللون
    final brightness = backgroundColor.computeLuminance();

    // إذا كان اللون فاتحًا، استخدم النص الأسود، وإلا استخدم النص الأبيض
    return brightness > 0.5 ? Colors.black : Colors.white;
  }
}
