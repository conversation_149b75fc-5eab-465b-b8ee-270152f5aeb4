import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/screens/widgets/documents/quill_error_handler.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'quill_table_manager.dart';
import 'quill_pdf_exporter.dart';
import 'quill_search_manager.dart';
import '../../../services/unified_permission_service.dart';

/// مدير شريط الأدوات الموحد لمحرر Quill
/// يجمع جميع الأدوات في شريط واحد مع تمرير أفقي
class QuillToolbarManager extends StatefulWidget {
  final QuillController controller;
  final bool showToolbar;
  final String documentTitle;
  final String? documentDescription;
  final Function(String)? onContentChanged;
  final dynamic editorWidget; // مرجع للمحرر لتعطيل الحفظ التلقائي

  const QuillToolbarManager({
    Key? key,
    required this.controller,
    this.showToolbar = true,
    required this.documentTitle,
    this.documentDescription,
    this.onContentChanged,
    this.editorWidget,
  }) : super(key: key);

  @override
  State<QuillToolbarManager> createState() => _QuillToolbarManagerState();
}

class _QuillToolbarManagerState extends State<QuillToolbarManager> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  QuillTableManager? _tableManager;
  QuillPdfExporter? _pdfExporter;
  QuillSearchManager? _searchManager;

  @override
  void initState() {
    super.initState();
    // سيتم تهيئة المكونات في didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeManagers();
  }

  void _initializeManagers() {
    _tableManager ??= QuillTableManager(
      controller: widget.controller,
      context: context,
    );
    _pdfExporter ??= QuillPdfExporter(
      controller: widget.controller,
      context: context,
      documentTitle: widget.documentTitle,
      documentDescription: widget.documentDescription,
    );
    _searchManager ??= QuillSearchManager(
      controller: widget.controller,
      context: context,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showToolbar) return const SizedBox.shrink();

    try {
      // التأكد من تهيئة المكونات
      _initializeManagers();

      return Container(
        height: 120, // ارتفاع ثابت مناسب
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            // شريط الأدوات الأساسي
            Expanded(
              flex: 2,
              child: _buildBasicToolbar(),
            ),
            
            // شريط الأدوات المتقدم
            Expanded(
              flex: 1,
              child: _buildAdvancedToolbar(),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('خطأ في بناء QuillToolbarManager: $e');
      return Container(
        height: 50,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        ),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.format_bold),
              onPressed: () {
                try {
                  widget.controller.formatSelection(Attribute.bold);
                } catch (e) {
                  debugPrint('خطأ في التنسيق: $e');
                }
              },
              tooltip: 'عريض',
            ),
            IconButton(
              icon: const Icon(Icons.format_italic),
              onPressed: () {
                try {
                  widget.controller.formatSelection(Attribute.italic);
                } catch (e) {
                  debugPrint('خطأ في التنسيق: $e');
                }
              },
              tooltip: 'مائل',
            ),
            const Spacer(),
            const Text(
              'شريط أدوات احتياطي',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      );
    }
  }

  /// بناء شريط الأدوات الأساسي مع QuillSimpleToolbar
  Widget _buildBasicToolbar() {
    try {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: QuillSimpleToolbar(
          controller: widget.controller,
          configurations: QuillSimpleToolbarConfigurations(
            multiRowsDisplay: false, // صف واحد مع تمرير أفقي
            
            // جميع أدوات التنسيق الأساسية
            showBoldButton: true,
            showItalicButton: true,
            showUnderLineButton: true,
            showStrikeThrough: true,
            showColorButton: true,
            showBackgroundColorButton: true,
            showAlignmentButtons: true,
            showDirection: true,
            showListNumbers: true,
            showListBullets: true,
            showHeaderStyle: true,
            showFontFamily: true,
            showFontSize: true,
            showClearFormat: true,
            showUndo: true,
            showRedo: true,
            showSearchButton: true,
            showDividers: true,
            // showListCheck: true,
            showCodeBlock: true,
            showLink: true,
            showQuote: true,
            
            
            // الخطوط العربية
            fontFamilyValues: const {
              'Arial': 'Arial',
              'Cairo': 'Cairo',
              'Amiri': 'Amiri',
              'Noto Sans Arabic': 'Noto Sans Arabic',
              'Times New Roman': 'Times New Roman',
              'Tahoma': 'Tahoma',
            },
          ),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في بناء شريط الأدوات الأساسي: $e');
      return Container(
        height: 40,
        child: const Center(
          child: Text('خطأ في تحميل شريط الأدوات الأساسي'),
        ),
      );
    }
  }

  /// بناء شريط الأدوات المتقدم مع جميع الأدوات الإضافية
  Widget _buildAdvancedToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border(top: BorderSide(color: Colors.blue.shade200)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // مجموعة الملفات
            _buildToolGroup('الملفات', [
              _buildToolButton(Icons.picture_as_pdf, 'حفظ PDF', Colors.red, () => _pdfExporter?.exportToPdf()),
              _buildToolButton(Icons.save, 'حفظ', Colors.green, _saveDocument),
            ]),
            
            const SizedBox(width: 16),
            
            // مجموعة الإدراج
            _buildToolGroup('إدراج', [
              if (_permissionService.canEditDocuments()) _buildToolButton(Icons.table_chart, 'جدول جديد', Colors.orange, () => _tableManager?.insertTable()),
              if (_permissionService.canEditDocuments()) _buildToolButton(Icons.edit_note, 'تحرير جدول', Colors.deepOrange, () => _tableManager?.editExistingTable()),
              if (_permissionService.canUploadFiles()) _buildToolButton(Icons.image, 'صورة', Colors.purple, _insertImage),
              if (_permissionService.canEditDocuments()) _buildToolButton(Icons.link, 'رابط', Colors.indigo, _insertLink),
              if (_permissionService.canEditDocuments()) _buildToolButton(Icons.emoji_emotions, 'رموز', Colors.amber, _insertEmoji),
            ]),
            
            const SizedBox(width: 16),
            
            // مجموعة البحث
            _buildToolGroup('بحث', [
              _buildToolButton(Icons.search, 'بحث', Colors.teal, () => _searchManager?.showSearchDialog()),
            ]),
          ],
        ),
      ),
    );
  }

  /// بناء مجموعة أدوات
  Widget _buildToolGroup(String title, List<Widget> tools) {
    return Row(
      children: [
        Text(
          '$title: ',
          style: TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
        ...tools,
      ],
    );
  }

  /// بناء زر أداة
  Widget _buildToolButton(IconData icon, String tooltip, Color color, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: IconButton(
        icon: Icon(icon, color: color, size: 18),
        tooltip: tooltip,
        onPressed: onPressed,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        padding: const EdgeInsets.all(4),
      ),
    );
  }

  
  /// حفظ المستند
  void _saveDocument() {
    try {
      if (widget.onContentChanged != null) {
        final content = widget.controller.document.toDelta().toJson();
        widget.onContentChanged!(jsonEncode(content));
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ المستند بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المستند: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إدراج صورة محسن
  Future<void> _insertImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null && file.bytes != null) {
          // محاولة إدراج الصورة الفعلية
          try {
            final selection = widget.controller.selection;

            // إدراج placeholder للصورة مع معلومات مفيدة
            final imageInfo = '''
📷 صورة مدرجة: ${file.name}
   الحجم: ${(file.size / 1024).toStringAsFixed(1)} KB
   النوع: ${file.extension?.toUpperCase() ?? 'غير معروف'}
   المسار: ${file.path}
''';

            widget.controller.document.insert(selection.baseOffset, imageInfo);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إدراج معلومات الصورة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            // في حالة فشل الإدراج المتقدم، استخدم الطريقة البسيطة
            final selection = widget.controller.selection;
            widget.controller.document.insert(selection.baseOffset, '[صورة: ${file.name}]');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إدراج مرجع الصورة'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        QuillErrorHandler.handleImageError(context, e);
      }
    }
  }

  /// إدراج رابط محسن
  Future<void> _insertLink() async {
    try {
      String linkUrl = '';
      String linkText = '';

      // عرض حوار لإدخال الرابط
      final result = await showDialog<Map<String, String>>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إدراج رابط'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'عنوان الرابط',
                  hintText: 'مثال: موقع جوجل',
                ),
                onChanged: (value) => linkText = value,
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'الرابط',
                  hintText: 'https://example.com',
                ),
                onChanged: (value) => linkUrl = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop({
                'text': linkText.isEmpty ? linkUrl : linkText,
                'url': linkUrl,
              }),
              child: const Text('إدراج'),
            ),
          ],
        ),
      );

      if (result != null && result['url']!.isNotEmpty) {
        final selection = widget.controller.selection;
        final linkDisplay = result['text']!.isNotEmpty
            ? '${result['text']} (${result['url']})'
            : result['url']!;

        widget.controller.document.insert(selection.baseOffset, linkDisplay);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إدراج الرابط بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        QuillErrorHandler.handleError(context, 'إدراج رابط', e);
      }
    }
  }

  /// إدراج رموز تعبيرية محسن
  Future<void> _insertEmoji() async {
    try {
      final emojis = [
        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
        '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
        '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
        '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
        '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
        '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      ];

      // عرض حوار اختيار الرمز التعبيري
      final selectedEmoji = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('اختر رمز تعبيري'),
          content: SizedBox(
            width: 300,
            height: 400,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 6,
                childAspectRatio: 1,
              ),
              itemCount: emojis.length,
              itemBuilder: (context, index) => InkWell(
                onTap: () => Navigator.of(context).pop(emojis[index]),
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      emojis[index],
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (selectedEmoji != null) {
        final selection = widget.controller.selection;
        widget.controller.document.insert(selection.baseOffset, selectedEmoji);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إدراج الرمز التعبيري'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        QuillErrorHandler.handleError(context, 'إدراج رمز تعبيري', e);
      }
    }
  }
}
