import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_history_models.dart';
import '../../utils/date_formatter.dart';
import '../../services/unified_permission_service.dart';

/// تبويب سجل النشاط للمهمة
class TaskActivityLogTab extends StatefulWidget {
  final String taskId;

  const TaskActivityLogTab({
    super.key,
    required this.taskId,
  });

  @override
  State<TaskActivityLogTab> createState() => _TaskActivityLogTabState();
}

class _TaskActivityLogTabState extends State<TaskActivityLogTab> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadActivityLog();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadActivityLog() async {
    final taskController = Get.find<TaskController>();
    final taskIdInt = int.tryParse(widget.taskId);
    if (taskIdInt != null) {
      await taskController.loadTaskHistory(taskIdInt);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilterAndSearchBar(),
        Expanded(
          child: GetBuilder<TaskController>(
            id: 'task_history',
            builder: (controller) {
              final filteredHistory = _getFilteredHistory(controller.taskHistory);
              
              if (controller.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (filteredHistory.isEmpty) {
                return _buildEmptyState();
              }

              return RefreshIndicator(
                onRefresh: _loadActivityLog,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredHistory.length,
                  itemBuilder: (context, index) {
                    final historyItem = filteredHistory[index];
                    return _buildActivityLogItem(historyItem, index);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFilterAndSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في سجل النشاط...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          // مرشحات النشاط
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('all', 'الكل'),
                const SizedBox(width: 8),
                _buildFilterChip('created', 'إنشاء'),
                const SizedBox(width: 8),
                _buildFilterChip('updated', 'تحديث'),
                const SizedBox(width: 8),
                _buildFilterChip('status_changed', 'تغيير الحالة'),
                const SizedBox(width: 8),
                _buildFilterChip('assigned', 'تعيين'),
                const SizedBox(width: 8),
                _buildFilterChip('transferred', 'تحويل'),
                const SizedBox(width: 8),
                _buildFilterChip('completed', 'إكمال'),
                const SizedBox(width: 8),
                _buildFilterChip('comment_added', 'تعليق'),
                const SizedBox(width: 8),
                _buildFilterChip('attachment_added', 'مرفق'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return _permissionService.canFilterTasks()
        ? FilterChip(
            label: Text(label),
            selected: isSelected,
            onSelected: (selected) {
              setState(() {
                _selectedFilter = selected ? value : 'all';
              });
            },
            selectedColor: AppColors.primary.withAlpha(50),
            checkmarkColor: AppColors.primary,
          )
        : Chip(
            label: Text(label),
            backgroundColor: isSelected ? AppColors.primary.withAlpha(50) : null,
          );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد سجل نشاط',
            style: AppStyles.headingMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم تسجيل أي نشاط لهذه المهمة بعد',
            style: AppStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadActivityLog,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityLogItem(TaskHistory historyItem, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildActionIcon(historyItem.action),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildActionHeader(historyItem),
                        const SizedBox(height: 4),
                        _buildActionDescription(historyItem),
                        if (historyItem.details != null && historyItem.details!.isNotEmpty)
                          _buildActionDetails(historyItem.details!),
                        if (_hasValueChanges(historyItem))
                          _buildValueChanges(historyItem),
                        const SizedBox(height: 8),
                        _buildActionFooter(historyItem),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionIcon(String action) {
    IconData iconData;
    Color iconColor;

    switch (action.toLowerCase()) {
      case 'created':
        iconData = Icons.add_circle;
        iconColor = Colors.green;
        break;
      case 'updated':
        iconData = Icons.edit;
        iconColor = Colors.blue;
        break;
      case 'assigned':
        iconData = Icons.person_add;
        iconColor = Colors.orange;
        break;
      case 'transferred':
        iconData = Icons.swap_horiz;
        iconColor = Colors.blue;
        break;
      case 'completed':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'status_changed':
        iconData = Icons.swap_horiz;
        iconColor = Colors.purple;
        break;
      case 'priority_changed':
        iconData = Icons.priority_high;
        iconColor = Colors.red;
        break;
      case 'due_date_changed':
        iconData = Icons.schedule;
        iconColor = Colors.amber;
        break;
      case 'comment_added':
        iconData = Icons.comment;
        iconColor = Colors.teal;
        break;
      case 'attachment_added':
        iconData = Icons.attach_file;
        iconColor = Colors.indigo;
        break;
      case 'deleted':
        iconData = Icons.delete;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.info;
        iconColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  Widget _buildActionHeader(TaskHistory historyItem) {
    return Row(
      children: [
        Expanded(
          child: Text(
            historyItem.actionDescription,
            style: AppStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (historyItem.changeType != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              historyItem.changeType!,
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionDescription(TaskHistory historyItem) {
    if (historyItem.changeDescription != null && historyItem.changeDescription!.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          historyItem.changeDescription!,
          style: AppStyles.bodyMedium.copyWith(
            color: Colors.grey[600],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildActionDetails(String details) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: _buildParsedDetails(details),
    );
  }

  /// تحليل وعرض تفاصيل JSON بشكل مفهوم
  Widget _buildParsedDetails(String details) {
    try {
      // محاولة تحليل JSON
      if (details.startsWith('{')) {
        final detailsMap = json.decode(details) as Map<String, dynamic>;
        return _buildJsonDetails(detailsMap);
      }
    } catch (e) {
      // إذا فشل التحليل، عرض النص كما هو
      debugPrint('خطأ في تحليل details JSON: $e');
    }

    // عرض النص العادي
    return Text(
      details,
      style: AppStyles.bodySmall.copyWith(
        color: Colors.grey[700],
      ),
    );
  }

  /// بناء عرض تفاصيل JSON
  Widget _buildJsonDetails(Map<String, dynamic> detailsMap) {
    List<Widget> children = [];

    // عرض معلومات التحويل
    if (detailsMap.containsKey('newAssigneeId') || detailsMap.containsKey('previousAssigneeId')) {
      children.add(_buildTransferDetails(detailsMap));
    }

    // عرض الملاحظات
    if (detailsMap.containsKey('note') && detailsMap['note'] != null && detailsMap['note'].toString().isNotEmpty) {
      children.add(_buildNoteDetail(detailsMap['note'].toString()));
    }

    // عرض المرفقات
    if (detailsMap.containsKey('attachments') && detailsMap['attachments'] != null && detailsMap['attachments'].toString().isNotEmpty) {
      children.add(_buildAttachmentsDetail(detailsMap['attachments'].toString()));
    }

    // عرض معلومات المساهمة
    if (detailsMap.containsKey('contributionRecorded') && detailsMap['contributionRecorded'] == 'true') {
      children.add(_buildContributionDetail(detailsMap));
    }

    // إذا لم توجد تفاصيل معروفة، عرض JSON كما هو
    if (children.isEmpty) {
      return Text(
        detailsMap.toString(),
        style: AppStyles.bodySmall.copyWith(
          color: Colors.grey[700],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  /// بناء تفاصيل التحويل
  Widget _buildTransferDetails(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.swap_horiz, size: 16, color: Colors.blue),
              const SizedBox(width: 4),
              Text(
                'تفاصيل التحويل:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          if (detailsMap.containsKey('previousAssigneeId'))
            FutureBuilder<String>(
              future: _getUserNameById(detailsMap['previousAssigneeId'].toString()),
              builder: (context, snapshot) {
                return Text(
                  'من: ${snapshot.data ?? 'مستخدم غير معروف'}',
                  style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
                );
              },
            ),
          if (detailsMap.containsKey('newAssigneeId'))
            FutureBuilder<String>(
              future: _getUserNameById(detailsMap['newAssigneeId'].toString()),
              builder: (context, snapshot) {
                return Text(
                  'إلى: ${snapshot.data ?? 'مستخدم غير معروف'}',
                  style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
                );
              },
            ),
        ],
      ),
    );
  }

  /// بناء تفاصيل الملاحظة
  Widget _buildNoteDetail(String note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.note, size: 16, color: Colors.orange),
              const SizedBox(width: 4),
              Text(
                'ملاحظة:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            note,
            style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
          ),
        ],
      ),
    );
  }

  /// بناء تفاصيل المرفقات
  Widget _buildAttachmentsDetail(String attachments) {
    final attachmentList = attachments.split(',').where((a) => a.trim().isNotEmpty).toList();

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.attach_file, size: 16, color: Colors.green),
              const SizedBox(width: 4),
              Text(
                'مرفقات (${attachmentList.length}):',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...attachmentList.map((attachment) => Padding(
            padding: const EdgeInsets.only(left: 20, bottom: 2),
            child: Text(
              '• ${attachment.trim()}',
              style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
            ),
          )),
        ],
      ),
    );
  }

  /// بناء تفاصيل المساهمة
  Widget _buildContributionDetail(Map<String, dynamic> detailsMap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.trending_up, size: 16, color: Colors.purple),
              const SizedBox(width: 4),
              Text(
                'مساهمة مسجلة:',
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          if (detailsMap.containsKey('contributionPercentage'))
            Text(
              'نسبة المساهمة: ${detailsMap['contributionPercentage']}%',
              style: AppStyles.bodySmall.copyWith(color: Colors.grey[700]),
            ),
        ],
      ),
    );
  }

  bool _hasValueChanges(TaskHistory historyItem) {
    return (historyItem.oldValue != null && historyItem.oldValue!.isNotEmpty) ||
           (historyItem.newValue != null && historyItem.newValue!.isNotEmpty);
  }

  Widget _buildValueChanges(TaskHistory historyItem) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التغييرات:',
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 4),
          if (historyItem.oldValue != null && historyItem.oldValue!.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'من: ',
                  style: AppStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.red[700],
                  ),
                ),
                Expanded(
                  child: _buildValueDisplay(historyItem.oldValue!, isOldValue: true),
                ),
              ],
            ),
          if (historyItem.newValue != null && historyItem.newValue!.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إلى: ',
                  style: AppStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.green[700],
                  ),
                ),
                Expanded(
                  child: _buildValueDisplay(historyItem.newValue!, isOldValue: false),
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// بناء عرض القيمة مع تحويل معرفات المستخدمين إلى أسماء
  Widget _buildValueDisplay(String value, {required bool isOldValue}) {
    // التحقق من كون القيمة معرف مستخدم (رقم)
    final userId = int.tryParse(value);
    if (userId != null && userId > 0) {
      return FutureBuilder<String>(
        future: _getUserNameById(value),
        builder: (context, snapshot) {
          final displayValue = snapshot.data ?? value;
          return Text(
            displayValue,
            style: AppStyles.bodySmall.copyWith(
              color: isOldValue ? Colors.red[600] : Colors.green[600],
              decoration: isOldValue ? TextDecoration.lineThrough : null,
              fontWeight: isOldValue ? null : FontWeight.w500,
            ),
          );
        },
      );
    }

    // عرض القيمة العادية
    return Text(
      value,
      style: AppStyles.bodySmall.copyWith(
        color: isOldValue ? Colors.red[600] : Colors.green[600],
        decoration: isOldValue ? TextDecoration.lineThrough : null,
        fontWeight: isOldValue ? null : FontWeight.w500,
      ),
    );
  }

  /// الحصول على اسم المستخدم بواسطة المعرف
  Future<String> _getUserNameById(String userId) async {
    try {
      final userController = Get.find<UserController>();
      return await userController.getUserNameById(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم المستخدم: $e');
      return 'مستخدم غير معروف';
    }
  }

  Widget _buildActionFooter(TaskHistory historyItem) {
    return Row(
      children: [
        Icon(
          Icons.person,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          _getUserName(historyItem),
          style: AppStyles.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const Spacer(),
        Icon(
          Icons.access_time,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          _formatTimestamp(historyItem.timestampDateTime),
          style: AppStyles.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _getUserName(TaskHistory historyItem) {
    if (historyItem.changedByNavigation != null) {
      return historyItem.changedByNavigation!.name;
    } else if (historyItem.user != null) {
      return historyItem.user!.name;
    }
    return 'النظام';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormatter.formatDate(timestamp);
    }
  }

  List<TaskHistory> _getFilteredHistory(RxList<TaskHistory> history) {
    var filtered = history.where((item) {
      // تطبيق مرشح النوع
      if (_selectedFilter != 'all' && item.action.toLowerCase() != _selectedFilter) {
        return false;
      }

      // تطبيق البحث النصي
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return item.actionDescription.toLowerCase().contains(query) ||
               (item.details?.toLowerCase().contains(query) ?? false) ||
               (item.changeDescription?.toLowerCase().contains(query) ?? false) ||
               (item.oldValue?.toLowerCase().contains(query) ?? false) ||
               (item.newValue?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.timestampDateTime.compareTo(a.timestampDateTime));

    return filtered;
  }
}