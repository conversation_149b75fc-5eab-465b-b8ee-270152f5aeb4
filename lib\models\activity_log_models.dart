import 'user_model.dart';
import 'task_models.dart';

/// نموذج سجل النشاط - متطابق مع TaskHistory في ASP.NET Core API
class ActivityLog {
  final int id;
  final int taskId;
  final int userId;
  final String action;
  final String? details;
  final int timestamp;
  final String? changeType;
  final String? changeDescription;
  final String? oldValue;
  final String? newValue;
  final int? changedBy;
  final int? changedAt;

  // Navigation properties
  final User? changedByNavigation;
  final Task? task;
  final User? user;

  const ActivityLog({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.action,
    this.details,
    required this.timestamp,
    this.changeType,
    this.changeDescription,
    this.oldValue,
    this.newValue,
    this.changedBy,
    this.changedAt,
    this.changedByNavigation,
    this.task,
    this.user,
  });

  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      action: json['action'] as String,
      details: json['details'] as String?,
      timestamp: json['timestamp'] as int,
      changeType: json['changeType'] as String?,
      changeDescription: json['changeDescription'] as String?,
      oldValue: json['oldValue'] as String?,
      newValue: json['newValue'] as String?,
      changedBy: json['changedBy'] as int?,
      changedAt: json['changedAt'] as int?,
      changedByNavigation: json['changedByNavigation'] != null
          ? User.fromJson(json['changedByNavigation'] as Map<String, dynamic>)
          : null,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'action': action,
      'details': details,
      'timestamp': timestamp,
      'changeType': changeType,
      'changeDescription': changeDescription,
      'oldValue': oldValue,
      'newValue': newValue,
      'changedBy': changedBy,
      'changedAt': changedAt,
    };
  }

  ActivityLog copyWith({
    int? id,
    int? taskId,
    int? userId,
    String? action,
    String? details,
    int? timestamp,
    String? changeType,
    String? changeDescription,
    String? oldValue,
    String? newValue,
    int? changedBy,
    int? changedAt,
    User? changedByNavigation,
    Task? task,
    User? user,
  }) {
    return ActivityLog(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      action: action ?? this.action,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      changeType: changeType ?? this.changeType,
      changeDescription: changeDescription ?? this.changeDescription,
      oldValue: oldValue ?? this.oldValue,
      newValue: newValue ?? this.newValue,
      changedBy: changedBy ?? this.changedBy,
      changedAt: changedAt ?? this.changedAt,
      changedByNavigation: changedByNavigation ?? this.changedByNavigation,
      task: task ?? this.task,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الطابع الزمني كـ DateTime
  DateTime get timestampDateTime => 
      DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

  /// الحصول على تاريخ التغيير كـ DateTime
  DateTime? get changedAtDateTime => changedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(changedAt! * 1000)
      : null;

  /// الحصول على وصف الإجراء باللغة العربية
  String get actionDescription {
    switch (action.toLowerCase()) {
      case 'created': return 'تم إنشاء المهمة';
      case 'updated': return 'تم تحديث المهمة';
      case 'assigned': return 'تم تعيين المهمة';
      case 'completed': return 'تم إكمال المهمة';
      case 'status_changed': return 'تم تغيير حالة المهمة';
      case 'priority_changed': return 'تم تغيير أولوية المهمة';
      case 'due_date_changed': return 'تم تغيير تاريخ الاستحقاق';
      case 'comment_added': return 'تم إضافة تعليق';
      case 'attachment_added': return 'تم إضافة مرفق';
      case 'deleted': return 'تم حذف المهمة';
      case 'login': return 'تسجيل دخول';
      case 'logout': return 'تسجيل خروج';
      default: return action;
    }
  }

  /// الحصول على نوع الكيان (Entity Type) للنشاط
  String get entityType {
    if (taskId > 0) return 'task';
    return 'user';
  }

  /// الحصول على معرف الكيان
  int get entityId => taskId > 0 ? taskId : userId;

  @override
  String toString() {
    return 'ActivityLog(id: $id, action: $action, timestamp: $timestampDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityLog && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
