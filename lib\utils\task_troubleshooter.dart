import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/task_controller.dart';
import '../controllers/user_controller.dart';
import '../controllers/department_controller.dart';
import 'api_diagnostics.dart';

/// أداة استكشاف وإصلاح مشاكل المهام
class TaskTroubleshooter {
  
  /// تشخيص شامل لمشاكل المهام
  static Future<Map<String, dynamic>> diagnoseTasksIssues() async {
    debugPrint('🔍 بدء التشخيص الشامل لمشاكل المهام...');
    
    final results = <String, dynamic>{};
    
    try {
      // 1. فحص الاتصال بالخادم
      results['connectivity'] = await _checkServerConnectivity();
      
      // 2. فحص حالة المتحكمات
      results['controllers'] = await _checkControllersStatus();
      
      // 3. فحص API للمهام
      results['tasksApi'] = await _checkTasksApi();
      
      // 4. فحص التخزين المؤقت
      results['cache'] = await _checkCacheStatus();
      
      // 5. فحص البيانات المرتبطة
      results['relatedData'] = await _checkRelatedData();
      
      // 6. تحليل الأخطاء
      results['analysis'] = _analyzeResults(results);
      
      debugPrint('✅ انتهى التشخيص الشامل');
      return results;
      
    } catch (e) {
      debugPrint('❌ خطأ في التشخيص: $e');
      results['error'] = e.toString();
      return results;
    }
  }

  /// فحص الاتصال بالخادم
  static Future<Map<String, dynamic>> _checkServerConnectivity() async {
    try {
      debugPrint('🌐 فحص الاتصال بالخادم...');
      
      final diagnostics = await ApiDiagnostics.quickTasksTest();
      
      return {
        'status': 'checked',
        'results': diagnostics,
        'hasIssues': diagnostics.values.any((result) =>
          result is Map<String, dynamic> && (result['success'] as bool? ?? false) != true),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasIssues': true,
      };
    }
  }

  /// فحص حالة المتحكمات
  static Future<Map<String, dynamic>> _checkControllersStatus() async {
    try {
      debugPrint('🎮 فحص حالة المتحكمات...');
      
      final issues = <String>[];
      final status = <String, dynamic>{};
      
      // فحص TaskController
      if (Get.isRegistered<TaskController>()) {
        final taskController = Get.find<TaskController>();
        status['taskController'] = {
          'registered': true,
          'isLoading': taskController.isLoading,
          'hasError': taskController.error.isNotEmpty,
          'tasksCount': taskController.allTasks.length,
          'error': taskController.error,
        };
        
        if (taskController.error.isNotEmpty) {
          issues.add('TaskController has error: ${taskController.error}');
        }
      } else {
        status['taskController'] = {'registered': false};
        issues.add('TaskController is not registered');
      }
      
      // فحص UserController
      if (Get.isRegistered<UserController>()) {
        final userController = Get.find<UserController>();
        status['userController'] = {
          'registered': true,
          'isLoading': userController.isLoading,
          'hasError': userController.error.isNotEmpty,
          'usersCount': userController.allUsers.length,
        };
      } else {
        status['userController'] = {'registered': false};
        issues.add('UserController is not registered');
      }
      
      // فحص DepartmentController
      if (Get.isRegistered<DepartmentController>()) {
        final deptController = Get.find<DepartmentController>();
        status['departmentController'] = {
          'registered': true,
          'isLoading': deptController.isLoading,
          'hasError': deptController.error.isNotEmpty,
          'departmentsCount': deptController.allDepartments.length,
        };
      } else {
        status['departmentController'] = {'registered': false};
        issues.add('DepartmentController is not registered');
      }
      
      return {
        'status': 'checked',
        'controllers': status,
        'issues': issues,
        'hasIssues': issues.isNotEmpty,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasIssues': true,
      };
    }
  }

  /// فحص API للمهام
  static Future<Map<String, dynamic>> _checkTasksApi() async {
    try {
      debugPrint('📡 فحص API للمهام...');
      
      final results = await ApiDiagnostics.quickTasksTest();
      final issues = <String>[];
      
      for (final entry in results.entries) {
        final testName = entry.key;
        final result = entry.value as Map<String, dynamic>;
        
        if (!(result['success'] as bool? ?? false)) {
          issues.add('$testName failed: ${result['error'] ?? 'Unknown error'}');
        }
      }
      
      return {
        'status': 'checked',
        'results': results,
        'issues': issues,
        'hasIssues': issues.isNotEmpty,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasIssues': true,
      };
    }
  }

  /// فحص التخزين المؤقت
  static Future<Map<String, dynamic>> _checkCacheStatus() async {
    try {
      debugPrint('💾 فحص التخزين المؤقت...');
      
      final issues = <String>[];
      final cacheInfo = <String, dynamic>{};
      
      if (Get.isRegistered<TaskController>()) {
        final taskController = Get.find<TaskController>();
        // يمكن إضافة فحوصات للتخزين المؤقت هنا
        cacheInfo['tasksCached'] = taskController.allTasks.isNotEmpty;
      }
      
      return {
        'status': 'checked',
        'cacheInfo': cacheInfo,
        'issues': issues,
        'hasIssues': issues.isNotEmpty,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasIssues': true,
      };
    }
  }

  /// فحص البيانات المرتبطة
  static Future<Map<String, dynamic>> _checkRelatedData() async {
    try {
      debugPrint('🔗 فحص البيانات المرتبطة...');
      
      final issues = <String>[];
      final dataStatus = <String, dynamic>{};
      
      // فحص المستخدمين
      if (Get.isRegistered<UserController>()) {
        final userController = Get.find<UserController>();
        dataStatus['users'] = {
          'loaded': userController.allUsers.isNotEmpty,
          'count': userController.allUsers.length,
          'hasError': userController.error.isNotEmpty,
        };
        
        if (userController.allUsers.isEmpty) {
          issues.add('No users loaded');
        }
      }
      
      // فحص الأقسام
      if (Get.isRegistered<DepartmentController>()) {
        final deptController = Get.find<DepartmentController>();
        dataStatus['departments'] = {
          'loaded': deptController.allDepartments.isNotEmpty,
          'count': deptController.allDepartments.length,
          'hasError': deptController.error.isNotEmpty,
        };
        
        if (deptController.allDepartments.isEmpty) {
          issues.add('No departments loaded');
        }
      }
      
      return {
        'status': 'checked',
        'dataStatus': dataStatus,
        'issues': issues,
        'hasIssues': issues.isNotEmpty,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'hasIssues': true,
      };
    }
  }

  /// تحليل النتائج وتقديم التوصيات
  static Map<String, dynamic> _analyzeResults(Map<String, dynamic> results) {
    final recommendations = <String>[];
    final severity = <String, int>{}; // low: 1, medium: 2, high: 3
    
    // تحليل الاتصال
    final connectivity = results['connectivity'] as Map<String, dynamic>?;
    if (connectivity?['hasIssues'] == true) {
      recommendations.add('تحقق من الاتصال بالخادم والشبكة');
      severity['connectivity'] = 3;
    }
    
    // تحليل المتحكمات
    final controllers = results['controllers'] as Map<String, dynamic>?;
    if (controllers?['hasIssues'] == true) {
      recommendations.add('أعد تهيئة المتحكمات المفقودة أو المعطلة');
      severity['controllers'] = 2;
    }
    
    // تحليل API
    final tasksApi = results['tasksApi'] as Map<String, dynamic>?;
    if (tasksApi?['hasIssues'] == true) {
      recommendations.add('تحقق من إعدادات API وقاعدة البيانات');
      severity['api'] = 3;
    }
    
    // تحليل البيانات المرتبطة
    final relatedData = results['relatedData'] as Map<String, dynamic>?;
    if (relatedData?['hasIssues'] == true) {
      recommendations.add('تحميل البيانات الأساسية (المستخدمين والأقسام)');
      severity['data'] = 2;
    }
    
    // تحديد الخطورة الإجمالية
    final maxSeverity = severity.values.isNotEmpty 
        ? severity.values.reduce((a, b) => a > b ? a : b)
        : 0;
    
    String severityText;
    switch (maxSeverity) {
      case 3:
        severityText = 'عالية';
        break;
      case 2:
        severityText = 'متوسطة';
        break;
      case 1:
        severityText = 'منخفضة';
        break;
      default:
        severityText = 'لا توجد مشاكل';
        break;
    }
    
    return {
      'recommendations': recommendations,
      'severity': severityText,
      'severityLevel': maxSeverity,
      'hasIssues': recommendations.isNotEmpty,
    };
  }

  /// محاولة إصلاح المشاكل تلقائياً
  static Future<Map<String, dynamic>> attemptAutoFix() async {
    debugPrint('🔧 محاولة الإصلاح التلقائي...');
    
    final fixResults = <String, dynamic>{};
    final fixedIssues = <String>[];
    final failedFixes = <String>[];
    
    try {
      // 1. إعادة تهيئة المتحكمات
      try {
        if (Get.isRegistered<TaskController>()) {
          final taskController = Get.find<TaskController>();
          await taskController.attemptTasksFix();
          fixedIssues.add('تم إعادة تهيئة TaskController');
        }
      } catch (e) {
        failedFixes.add('فشل في إعادة تهيئة TaskController: $e');
      }
      
      // 2. تحميل البيانات الأساسية
      try {
        if (Get.isRegistered<UserController>()) {
          final userController = Get.find<UserController>();
          if (userController.allUsers.isEmpty) {
            await userController.loadAllUsers();
            fixedIssues.add('تم تحميل بيانات المستخدمين');
          }
        }
      } catch (e) {
        failedFixes.add('فشل في تحميل المستخدمين: $e');
      }
      
      // 3. تحميل الأقسام
      try {
        if (Get.isRegistered<DepartmentController>()) {
          final deptController = Get.find<DepartmentController>();
          if (deptController.allDepartments.isEmpty) {
            await deptController.loadAllDepartments();
            fixedIssues.add('تم تحميل بيانات الأقسام');
          }
        }
      } catch (e) {
        failedFixes.add('فشل في تحميل الأقسام: $e');
      }
      
      fixResults['success'] = failedFixes.isEmpty;
      fixResults['fixedIssues'] = fixedIssues;
      fixResults['failedFixes'] = failedFixes;
      fixResults['timestamp'] = DateTime.now().toIso8601String();
      
      debugPrint('✅ انتهى الإصلاح التلقائي');
      return fixResults;
      
    } catch (e) {
      debugPrint('❌ خطأ في الإصلاح التلقائي: $e');
      return {
        'success': false,
        'error': e.toString(),
        'fixedIssues': fixedIssues,
        'failedFixes': failedFixes,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// طباعة تقرير التشخيص
  static void printDiagnosisReport(Map<String, dynamic> results) {
    debugPrint('\n${'=' * 60}');
    debugPrint('🔍 تقرير تشخيص مشاكل المهام');
    debugPrint('=' * 60);
    
    final analysis = results['analysis'] as Map<String, dynamic>?;
    if (analysis != null) {
      debugPrint('\n📊 التحليل العام:');
      debugPrint('   الخطورة: ${analysis['severity']}');
      debugPrint('   يوجد مشاكل: ${(analysis['hasIssues'] as bool? ?? false) ? 'نعم' : 'لا'}');
      
      final recommendations = analysis['recommendations'] as List<dynamic>?;
      if (recommendations != null && recommendations.isNotEmpty) {
        debugPrint('\n💡 التوصيات:');
        for (int i = 0; i < recommendations.length; i++) {
          debugPrint('   ${i + 1}. ${recommendations[i]}');
        }
      }
    }
    
    debugPrint('\n${'=' * 60}');
    debugPrint('⏰ وقت التقرير: ${DateTime.now().toIso8601String()}');
    debugPrint('${'=' * 60}\n');
  }
}
