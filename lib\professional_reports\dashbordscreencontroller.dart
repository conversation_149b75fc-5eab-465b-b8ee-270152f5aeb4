// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart' as pw;
// import 'package:flutter_application_2/professional_reports/screens/pdf_preview_screen_fixed.dart';
// import 'package:flutter_application_2/services/api/user_api_service.dart';
// import 'package:flutter_quill/flutter_quill.dart' as pw;
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:get/get_state_manager/src/simple/get_controllers.dart';
// import 'package:path/path.dart' as pw;
// import 'package:pdf/pdf.dart';

// /// تحكم في لوحة التقارير
// class ReportsDashboardController extends GetxController {
//   /// خدمة API للمستخدمين
//   final UserApiService _userApiService = UserApiService();
  
//   /// حالة التحميل
//   final isLoading = false.obs;
  
//   /// قائمة المستخدمين
//   final users = <User>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     _loadUsers();
//   }

//   /// تحميل قائمة المستخدمين
//   Future<void> _loadUsers() async {
//     try {
//       isLoading.value = true;
      
//       // جلب المستخدمين من API
//       final usersList = await _userApiService.getAllUsers();
//       users.assignAll(usersList);
      
//     } catch (e) {
//       Get.snackbar(
//         'خطأ',
//         'فشل في تحميل المستخدمين: $e',
//         backgroundColor: Colors.red,
//         colorText: Colors.white,
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   /// عرض تقرير المستخدمين بصيغة PDF مع معاينة
//   Future<void> showUsersReport() async {
//     try {
//       // عرض مؤشر التحميل
//       Get.dialog(
//         const Center(
//           child: Card(
//             child: Padding(
//               padding: EdgeInsets.all(20),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   CircularProgressIndicator(),
//                   SizedBox(height: 16),
//                   Text(
//                     'جاري إنشاء التقرير...',
//                     style: TextStyle(fontSize: 16),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         barrierDismissible: false,
//       );
      
//       // إنشاء مستند PDF
//       final pdf = await _generateUsersPdfReport();
//       final pdfData = await pdf.save();
      
//       // إغلاق مؤشر التحميل
//       Get.back();
      
//       // اسم الملف مع الطابع الزمني
//       final fileName = 'تقرير_المستخدمين_${DateTime.now().millisecondsSinceEpoch}.pdf';
      
//       // فتح شاشة معاينة PDF
//       Get.to(
//         () => PdfPreviewScreen(
//           pdfData: pdfData,
//           fileName: fileName,
//         ),
//         transition: Transition.rightToLeft,
//         duration: const Duration(milliseconds: 300),
//       );
      
//     } catch (e) {
//       // إغلاق مؤشر التحميل في حالة الخطأ
//       if (Get.isDialogOpen ?? false) {
//         Get.back();
//       }
      
//       Get.snackbar(
//         'خطأ',
//         'فشل في إنشاء التقرير: $e',
//         backgroundColor: Colors.red,
//         colorText: Colors.white,
//         icon: const Icon(Icons.error, color: Colors.white),
//       );
//     }
//   }

//   /// دالة عامة لعرض أي تقرير PDF بنفس أسلوب عرض تقرير المستخدمين
//   Future<void> showPdfReport({
//     required Future<pw.Document> Function() generatePdf,
//     String? fileNamePrefix,
//   }) async {
//     try {
//       Get.dialog(
//         const Center(
//           child: Card(
//             child: Padding(
//               padding: EdgeInsets.all(20),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   CircularProgressIndicator(),
//                   SizedBox(height: 16),
//                   Text(
//                     'جاري إنشاء التقرير...',
//                     style: TextStyle(fontSize: 16),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         barrierDismissible: false,
//       );

//       final pdf = await generatePdf();
//       // final pdfData = await pdf.close();

//       Get.back();

//       final fileName = '${fileNamePrefix ?? "تقرير"}_${DateTime.now().millisecondsSinceEpoch}.pdf';

//       Get.to(
//         () => PdfPreviewScreen(
//           pdfData: pdfData,
//           fileName: fileName,
//         ),
//         transition: Transition.rightToLeft,
//         duration: const Duration(milliseconds: 300),
//       );
//     } catch (e) {
//       if (Get.isDialogOpen ?? false) {
//         Get.back();
//       }
//       Get.snackbar(
//         'خطأ',
//         'فشل في إنشاء التقرير: $e',
//         backgroundColor: Colors.red,
//         colorText: Colors.white,
//         icon: const Icon(Icons.error, color: Colors.white),
//       );
//     }
//   }

//   /// تحديث البيانات
//   Future<void> refreshData() async {
//     await _loadUsers();
//   }

//   /// تحويل Unix timestamp إلى تاريخ قابل للقراءة
//   String _formatDate(int timestamp) {
//     final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
//     return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
//   }

//   /// إنشاء تقرير PDF للمستخدمين المحسّن مع تصميم احترافي
//   Future<pw.Document> _generateUsersPdfReport() async {
//     final pdf = pw.Document();
    
//     // تحميل الخطوط المطلوبة للـ PDF
//     // استخدام خط Noto Sans الذي يدعم العربية والإنجليزية معاً
//     final regularFont = await PdfGoogleFonts.notoSansRegular();
//     final boldFont = await PdfGoogleFonts.notoSansBold();
    
//     // إحصائيات المستخدمين
//     final totalUsers = users.length;
//     final activeUsers = users.where((user) => user.isActive).length;
//     final inactiveUsers = totalUsers - activeUsers;
    
//     // تعريف لوحة الألوان الاحترافية
//     const PdfColor primaryColor = PdfColors.blueGrey800;
//     const PdfColor secondaryColor = PdfColors.blueGrey50;
//     const PdfColor accentColor = PdfColors.teal400;
    
//     pdf.addPage(
//       pw.MultiPage(
//         pageFormat: PdfPageFormat.a4,
//         textDirection: pw.TextDirection.rtl,
//         theme: pw.ThemeData.withFont(
//           base: regularFont,
//           bold: boldFont,
//         ),
//         header: (pw.Context context) => _buildPdfHeader(context, 'تقرير المستخدمين', primaryColor, boldFont),
//         footer: (pw.Context context) => _buildPdfFooter(context, regularFont),
//         build: (pw.Context context) {
//           return [
//             // القسم العلوي: معلومات التقرير والمخطط البياني
//             _buildInfoAndChartSection(
//               totalUsers,
//               activeUsers,
//               inactiveUsers,
//               primaryColor,
//               accentColor,
//               regularFont,
//               boldFont,
//             ),
            
//             pw.SizedBox(height: 30),

//             // عنوان جدول المستخدمين
//             _buildSectionTitle('قائمة المستخدمين التفصيلية', primaryColor, boldFont),
            
//             pw.SizedBox(height: 15),
            
//             // جدول بيانات المستخدمين المحسّن
//             _buildEnhancedUsersTable(users, primaryColor, secondaryColor, regularFont, boldFont),
//           ];
//         },
//       ),
//     );
    
//     return pdf;
//   }



//   /// بناء رأس الصفحة البسيط والأنيق
//   pw.Widget _buildPdfHeader(pw.Context context, String title, PdfColor primaryColor, pw.Font boldFont) {
//     return pw.Container(
//       padding: const pw.EdgeInsets.symmetric(vertical: 15, horizontal: 20),
//       decoration: pw.BoxDecoration(
//         color: primaryColor.shade(0.1),
//         border: pw.Border.all(color: primaryColor.shade(0.3), width: 1),
//         borderRadius: pw.BorderRadius.circular(8),
//       ),
//       child: pw.Row(
//         mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//         children: [
//           // عنوان التقرير
//           pw.Text(
//             title,
//             style: pw.TextStyle(font: boldFont, fontSize: 20, color: primaryColor),
//             textDirection: pw.TextDirection.rtl,
//           ),
//           // تاريخ اليوم
//           pw.Text(
//             _formatDateArabic(DateTime.now()),
//             style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey600),
//             textDirection: pw.TextDirection.rtl,
//           ),
//         ],
//      ),
//       );
//   }

//   /// بناء تذييل الصفحة
//   pw.Widget _buildPdfFooter(pw.Context context, pw.Font regularFont) {
//     return pw.Container(
//       alignment: pw.Alignment.centerRight,
//       margin: const pw.EdgeInsets.only(top: 1.0 * PdfPageFormat.cm),
//       child: pw.Text(
//         'صفحة ${context.pageNumber} من ${context.pagesCount}',
//         style: pw.TextStyle(font: regularFont, fontSize: 10),
//         textDirection: pw.TextDirection.rtl,
//       ),
//     );
//   }

//   /// بناء قسم المعلومات والإحصائيات البسيط
//   pw.Widget _buildInfoAndChartSection(
//     int totalUsers,
//     int activeUsers,
//     int inactiveUsers,
//     PdfColor primaryColor,
//     PdfColor accentColor,
//     pw.Font regularFont,
//     pw.Font boldFont,
//   ) {
//     return pw.Column(
//       crossAxisAlignment: pw.CrossAxisAlignment.start,
//       children: [
//         // معلومات التقرير الأساسية
//         pw.Container(
//           padding: const pw.EdgeInsets.all(15),
//           decoration: pw.BoxDecoration(
//             color: primaryColor.shade(0.05),
//             border: pw.Border.all(color: primaryColor.shade(0.2), width: 1),
//             borderRadius: pw.BorderRadius.circular(8),
//           ),
//           child: pw.Column(
//             crossAxisAlignment: pw.CrossAxisAlignment.start,
//             children: [
//               _buildSectionTitle('ملخص التقرير', primaryColor, boldFont),
//               pw.SizedBox(height: 10),
//               pw.Row(
//                 mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//                 children: [
//                   pw.Text('تاريخ الإنشاء:', style: pw.TextStyle(font: regularFont, fontSize: 11), textDirection: pw.TextDirection.rtl),
//                   pw.Text(_formatDateArabic(DateTime.now()), style: pw.TextStyle(font: boldFont, fontSize: 11), textDirection: pw.TextDirection.rtl),
//                 ],
//               ),
//               pw.SizedBox(height: 5),
//               pw.Row(
//                 mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//                 children: [
//                   pw.Text('إجمالي المستخدمين:', style: pw.TextStyle(font: regularFont, fontSize: 11), textDirection: pw.TextDirection.rtl),
//                   pw.Text(totalUsers.toString(), style: pw.TextStyle(font: boldFont, fontSize: 11, color: primaryColor), textDirection: pw.TextDirection.rtl),
//                 ],
//               ),
//             ],
//           ),
//         ),
        
//         pw.SizedBox(height: 20),
        
//         // الإحصائيات السريعة في صف واحد
//         _buildSectionTitle('الإحصائيات السريعة', primaryColor, boldFont),
//         pw.SizedBox(height: 15),
        
//         pw.Row(
//           mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
//           children: [
//             pw.Expanded(child: _buildSimpleStatCard('إجمالي المستخدمين', totalUsers.toString(), PdfColors.blue600, regularFont, boldFont)),
//             pw.SizedBox(width: 10),
//             pw.Expanded(child: _buildSimpleStatCard('المستخدمون النشطون', activeUsers.toString(), PdfColors.green600, regularFont, boldFont)),
//             pw.SizedBox(width: 10),
//             pw.Expanded(child: _buildSimpleStatCard('غير النشطين', inactiveUsers.toString(), PdfColors.red600, regularFont, boldFont)),
//           ],
//         ),
//       ],
//     );
//   }

//   /// بناء عنوان القسم
//   pw.Widget _buildSectionTitle(String title, PdfColor color, pw.Font boldFont) {
//     return pw.Text(
//       title,
//       style: pw.TextStyle(
//         font: boldFont,
//         fontSize: 14,
//         color: color,
//       ),
//       textDirection: pw.TextDirection.rtl,
//     );
//   }



//   /// بناء بطاقة إحصائية بسيطة ومحسّنة
//   pw.Widget _buildSimpleStatCard(String title, String value, PdfColor color, pw.Font regularFont, pw.Font boldFont) {
//     return pw.Container(
//       padding: const pw.EdgeInsets.all(12),
//       decoration: pw.BoxDecoration(
//         color: color.shade(0.1), // لون خلفية خفيف
//         border: pw.Border.all(color: color.shade(0.3), width: 1),
//         borderRadius: pw.BorderRadius.circular(8),
//       ),
//       child: pw.Column(
//         mainAxisAlignment: pw.MainAxisAlignment.center,
//         children: [
//           // القيمة الرقمية
//           pw.Text(
//             value,
//             style: pw.TextStyle(
//               font: boldFont, 
//               fontSize: 24, 
//               color: color,
//             ),
//             textDirection: pw.TextDirection.rtl,
//           ),
//           pw.SizedBox(height: 5),
//           // العنوان
//           pw.Text(
//             title,
//             style: pw.TextStyle(
//               font: regularFont, 
//               fontSize: 10, 
//               color: PdfColors.grey700,
//             ),
//             textAlign: pw.TextAlign.center,
//             textDirection: pw.TextDirection.rtl,
//           ),
//         ],
//        ),
//           );
//   }

//   /// بناء جدول المستخدمين البسيط والمحسّن
//   pw.Widget _buildEnhancedUsersTable(List<User> users, PdfColor primaryColor, PdfColor secondaryColor, pw.Font regularFont, pw.Font boldFont) {
//     final headers = ['#', 'الاسم', 'البريد الإلكتروني', 'تاريخ الإنشاء', 'الدور', 'الحالة'];

//     final data = users.asMap().entries.map((entry) {
//       final index = entry.key + 1;
//       final user = entry.value;
//       return [
//         index.toString(),
//         user.name,
//         user.email,
//         _formatDate(user.createdAt),
//         user.role.displayName,
//         user.isActive ? 'نشط ✓' : 'غير نشط ✗', // إضافة رموز بصرية بسيطة
//       ];
//     }).toList();

//     return pw.Container(
//       decoration: pw.BoxDecoration(
//         borderRadius: pw.BorderRadius.circular(8),
//         border: pw.Border.all(color: PdfColors.grey300, width: 1),
//       ),
//       child: pw.TableHelper.fromTextArray(
//         headers: headers,
//         data: data,
//         border: null, // إزالة الحدود الداخلية للحصول على مظهر أنظف
//         headerStyle: pw.TextStyle(
//           font: boldFont,
//           fontSize: 11,
//           color: PdfColors.white,
//         ),
//         headerDecoration: pw.BoxDecoration(
//           color: primaryColor,
//           borderRadius: const pw.BorderRadius.only(
//             topLeft: pw.Radius.circular(7),
//             topRight: pw.Radius.circular(7),
//           ),
//         ),
//         cellStyle: pw.TextStyle(
//           font: regularFont,
//           fontSize: 10,
//           color: PdfColors.grey800,
//         ),
//         cellPadding: const pw.EdgeInsets.all(8), // مساحة أكبر داخل الخلايا
//         cellAlignments: {
//           0: pw.Alignment.center,
//           1: pw.Alignment.centerRight,
//           2: pw.Alignment.centerRight,
//           3: pw.Alignment.center,
//           4: pw.Alignment.center,
//           5: pw.Alignment.center,
//         },
//         // ألوان متناوبة للصفوف
//         rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
//         oddRowDecoration: pw.BoxDecoration(color: secondaryColor),
//     )  );
//   }

//   /// تنسيق التاريخ بالعربية
//   String _formatDateArabic(DateTime date) {
//     return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
//   }

//   /// توليد تقرير PDF للمهام (حقيقي)
//   Future<pw.Document> generateTasksReportPdf() async {
//     final pdf = pw.Document();
//     final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
//     final arabicFont = pw.Font.ttf(fontData);
//     final boldFont = pw.Font.ttf(fontData);

//     // جلب المهام الحقيقية من API
//     final taskApi = TaskApiService();
//     List<Task> tasks = [];
//     try {
//       tasks = await taskApi.getAllTasks();
//       debugPrint('عدد المهام المسترجعة: \\${tasks.length}');
//       if (tasks.isNotEmpty) {
//         debugPrint('أول مهمة: العنوان=\\${tasks.first.title}, الحالة=\\${tasks.first.status}');
//       }
//     } catch (e) {
//       // في حال فشل الجلب، أضف رسالة خطأ في التقرير
//       pdf.addPage(
//         pw.Page(
//           pageFormat: PdfPageFormat.a4,
//           textDirection: pw.TextDirection.rtl,
//           build: (pw.Context context) {
//             return pw.Center(
//               child: pw.Text('فشل في جلب بيانات المهام: $e', style: pw.TextStyle(font: arabicFont, fontSize: 18, color: PdfColors.red)),
//             );
//           },
//         ),
//       );
//       return pdf;
//     }

//     // بناء بيانات الجدول
//     if (tasks.isEmpty) {
//       pdf.addPage(
//         pw.Page(
//           pageFormat: PdfPageFormat.a4,
//           textDirection: pw.TextDirection.rtl,
//           build: (pw.Context context) {
//             return pw.Center(
//               child: pw.Text('لا توجد مهام متاحة لعرضها في التقرير.', style: pw.TextStyle(font: arabicFont, fontSize: 18, color: PdfColors.grey800)),
//             );
//           },
//         ),
//       );
//       return pdf;
//     }

//     final headers = [
//       '#',
//       'العنوان',
//       'الحالة',
//       'الأولوية',
//       'النسبة %',
//       'تاريخ الإنشاء',
//       'المكلف',
//       'القسم',
//     ];
//     final data = tasks.asMap().entries.map((entry) {
//       final i = entry.key + 1;
//       final t = entry.value;
//       return [
//         i.toString(),
//         t.title,
//         t.status,
//         t.priority,
//         t.completionPercentage.toString(),
//         _formatDate(t.createdAt),
//         t.assignee?.name ?? '-',
//         t.department?.name ?? '-',
//       ];
//     }).toList();

//     pdf.addPage(
//       pw.Page(
//         pageFormat: PdfPageFormat.a4,
//         textDirection: pw.TextDirection.rtl,
//         build: (pw.Context context) {
//           return pw.Column(
//             crossAxisAlignment: pw.CrossAxisAlignment.start,
//             children: [
//               pw.Text('بسم الله الرحمن الرحيم', style: pw.TextStyle(font: arabicFont, fontSize: 18, color: PdfColors.teal800)),
//               pw.SizedBox(height: 10),
//               pw.Text('تقرير المهام', style: pw.TextStyle(font: arabicFont, fontSize: 22)),
//               pw.SizedBox(height: 20),
//               pw.Table.fromTextArray(
//                 headers: headers,
//                 data: data,
//                 cellStyle: pw.TextStyle(font: arabicFont, fontSize: 12),
//                 headerStyle: pw.TextStyle(font: boldFont, fontSize: 14, fontWeight: pw.FontWeight.bold),
//                 cellAlignment: pw.Alignment.centerRight,
//                 headerDecoration: const pw.BoxDecoration(color: PdfColors.green100),
//                 rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
//                 oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey100),
//                 cellAlignments: {
//                   0: pw.Alignment.center,
//                   1: pw.Alignment.centerRight,
//                   2: pw.Alignment.center,
//                   3: pw.Alignment.center,
//                   4: pw.Alignment.center,
//                   5: pw.Alignment.center,
//                   6: pw.Alignment.centerRight,
//                   7: pw.Alignment.centerRight,
//                 },
//               ),
//             ],
//           );
//         },
//       ),
//     );
//     return pdf;
//   }
// }
