using System.ComponentModel.DataAnnotations;

namespace webApi.Models.Auth;

/// <summary>
/// نموذج طلب تحديث الرمز
/// </summary>
public class RefreshTokenRequest
{
    /// <summary>
    /// رمز التحديث
    /// </summary>
    [Required(ErrorMessage = "رمز التحديث مطلوب")]
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// نموذج طلب تغيير كلمة المرور
/// </summary>
public class ChangePasswordRequest
{
    /// <summary>
    /// كلمة المرور الحالية
    /// </summary>
    [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// كلمة المرور الجديدة
    /// </summary>
    [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
    [MinLength(6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// تأكيد كلمة المرور الجديدة
    /// </summary>
    [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
    [Compare("NewPassword", ErrorMessage = "كلمة المرور الجديدة وتأكيدها غير متطابقتين")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// نموذج طلب إعادة تعيين كلمة المرور
/// </summary>
public class ForgotPasswordRequest
{
    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    public string Email { get; set; } = string.Empty;
}
