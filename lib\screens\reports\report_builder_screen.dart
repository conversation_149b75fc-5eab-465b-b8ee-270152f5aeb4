import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/report_controller.dart';
import 'package:flutter_application_2/models/report_models.dart';
import 'package:flutter_application_2/models/reporting/report_result_model.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../models/user_model.dart';
import '../../models/enhanced_report_model.dart';
import '../widgets/reporting/report_filter_panel.dart';
import 'report_viewer_screen.dart';

/// شاشة إنشاء وتعديل التقارير
///
/// تتيح إنشاء تقارير جديدة أو تعديل تقارير موجودة
class ReportBuilderScreen extends StatefulWidget {
  /// التقرير المراد تعديله (اختياري)
  final EnhancedReport? report;

  const ReportBuilderScreen({
    super.key,
    this.report,
  });

  @override
  State<ReportBuilderScreen> createState() => _ReportBuilderScreenState();
}

class _ReportBuilderScreenState extends State<ReportBuilderScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  final AuthController _authController = Get.find<AuthController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  ReportType _selectedType = ReportType.taskStatus;
  ReportPeriod _selectedPeriod = ReportPeriod.thisMonth;
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  List<ReportFilter> _filters = [];
  List<ReportVisualization> _visualizations = [];
  bool _isShared = false;
  List<int> _sharedWithUserIds = [];
  bool _isFavorite = false;
  Color? _reportColor;
  IconData? _reportIcon;

  bool _isLoading = false;
  String _errorMessage = '';

  // final Uuid _uuid = const Uuid();

  @override
  void initState() {
    super.initState();

    // إذا كان هناك تقرير للتعديل، قم بتعبئة الحقول
    if (widget.report != null) {
      _titleController.text = widget.report!.title;
      _descriptionController.text = widget.report!.description ?? '';
      _selectedType = widget.report!.type;
      _selectedPeriod = widget.report!.period;
      _customStartDate = widget.report!.customStartDate;
      _customEndDate = widget.report!.customEndDate;
      _filters = List.from(widget.report!.filters);
      _visualizations = List.from(widget.report!.visualizations);
      _isShared = widget.report!.isShared;
      _sharedWithUserIds = widget.report!.sharedWithUserIds?.map((id) => id).toList() ?? [];
      _isFavorite = widget.report!.isFavorite;
      _reportColor = widget.report!.color;
      _reportIcon = widget.report!.icon;
    } else {
      // إنشاء تقرير جديد، إضافة تصور مرئي افتراضي
      _addDefaultVisualization();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// إضافة تصور مرئي افتراضي
  void _addDefaultVisualization() {
    switch (_selectedType) {
      case ReportType.taskStatus:
        _visualizations.add(
          _reportController.createVisualization(
            title: 'توزيع المهام حسب الحالة',
            type: VisualizationType.pieChart,
            dataFields: ['status', 'count'],
          ),
        );
        break;
      case ReportType.userPerformance:
        _visualizations.add(
          _reportController.createVisualization(
            title: 'أداء المستخدمين',
            type: VisualizationType.barChart,
            dataFields: ['userName', 'completionRate'],
            xAxisField: 'userName',
            yAxisField: 'completionRate',
          ),
        );
        break;
      default:
        _visualizations.add(
          _reportController.createVisualization(
            title: 'تصور مرئي',
            type: VisualizationType.table,
            dataFields: ['*'],
          ),
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(widget.report != null ? 'تعديل التقرير' : 'إنشاء تقرير جديد'),
        actions: [
          // زر الحفظ
          TextButton.icon(
            onPressed: _saveReport,
            icon: const Icon(Icons.save),
            label: const Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رسالة الخطأ
                    if (_errorMessage.isNotEmpty)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red),
                        ),
                        child: Text(
                          _errorMessage,
                          style: AppStyles.bodyMedium
                              .copyWith(color: Colors.red[900]),
                        ),
                      ),

                    // معلومات التقرير الأساسية
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات التقرير',
                              style: AppStyles.titleMedium,
                            ),
                            const SizedBox(height: 16),

                            // عنوان التقرير
                            TextFormField(
                              controller: _titleController,
                              decoration: const InputDecoration(
                                labelText: 'عنوان التقرير *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال عنوان التقرير';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // وصف التقرير
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'وصف التقرير',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),

                            // نوع التقرير
                            DropdownButtonFormField<ReportType>(
                              value: _selectedType,
                              decoration: const InputDecoration(
                                labelText: 'نوع التقرير *',
                                border: OutlineInputBorder(),
                              ),
                              items: ReportType.values.map((type) {
                                return DropdownMenuItem<ReportType>(
                                  value: type,
                                  child: Text(_getReportTypeName(type)),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedType = value;

                                    // إذا كان التقرير جديدًا، قم بإضافة تصور مرئي افتراضي
                                    if (widget.report == null &&
                                        _visualizations.isEmpty) {
                                      _addDefaultVisualization();
                                    }
                                  });
                                }
                              },
                            ),
                            const SizedBox(height: 16),

                            // خيارات إضافية
                            Row(
                              children: [
                                // المفضلة
                                Expanded(
                                  child: CheckboxListTile(
                                    title: const Text('إضافة للمفضلة'),
                                    value: _isFavorite,
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _isFavorite = value;
                                        });
                                      }
                                    },
                                    controlAffinity:
                                        ListTileControlAffinity.leading,
                                  ),
                                ),
                                // المشاركة
                                Expanded(
                                  child: CheckboxListTile(
                                    title: const Text('مشاركة التقرير'),
                                    value: _isShared,
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _isShared = value;
                                          if (value) {
                                            // عرض مربع حوار لاختيار المستخدمين
                                            _showShareDialog();
                                          } else {
                                            _sharedWithUserIds = [];
                                          }
                                        });
                                      }
                                    },
                                    controlAffinity:
                                        ListTileControlAffinity.leading,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // لوحة الفلاتر
                    ReportFilterPanel(
                      filters: _filters,
                      period: _selectedPeriod,
                      customStartDate: _customStartDate,
                      customEndDate: _customEndDate,
                      onFiltersChanged: (filters) {
                        setState(() {
                          _filters = filters;
                        });
                      },
                      onPeriodChanged: (period, startDate, endDate) {
                        setState(() {
                          _selectedPeriod = period;
                          _customStartDate = startDate;
                          _customEndDate = endDate;
                        });
                      },
                    ),
                    const SizedBox(height: 24),

                    // التصورات المرئية
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'التصورات المرئية',
                                  style: AppStyles.titleMedium,
                                ),
                                const Spacer(),
                                // زر إضافة تصور مرئي
                                TextButton.icon(
                                  icon: const Icon(Icons.add),
                                  label: const Text('إضافة'),
                                  onPressed: _showAddVisualizationDialog,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // قائمة التصورات المرئية
                            if (_visualizations.isEmpty)
                              const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: Text(
                                    'لا توجد تصورات مرئية',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                ),
                              )
                            else
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _visualizations.length,
                                itemBuilder: (context, index) {
                                  final visualization = _visualizations[index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    child: ListTile(
                                      title: Text(visualization.title),
                                      subtitle: Text(_getVisualizationTypeName(
                                          visualization.type)),
                                      leading: Icon(_getVisualizationTypeIcon(
                                          visualization.type)),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          // زر التعديل
                                          IconButton(
                                            icon: const Icon(Icons.edit,
                                                size: 20),
                                            onPressed: () =>
                                                _editVisualization(index),
                                            tooltip: 'تعديل',
                                          ),
                                          // زر الحذف
                                          IconButton(
                                            icon: const Icon(Icons.delete,
                                                size: 20),
                                            onPressed: () {
                                              setState(() {
                                                _visualizations.removeAt(index);
                                              });
                                            },
                                            tooltip: 'حذف',
                                            color: Colors.red,
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// حفظ التقرير
  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      if (_visualizations.isEmpty) {
        throw Exception('يجب إضافة تصور مرئي واحد على الأقل');
      }

      if (_selectedPeriod == ReportPeriod.custom &&
          (_customStartDate == null || _customEndDate == null)) {
        throw Exception('يجب تحديد تاريخ البداية والنهاية للفترة المخصصة');
      }

      EnhancedReport? report;

      if (widget.report != null) {
        // تحديث تقرير موجود
        report = await _reportController.updateReport(
          widget.report!.copyWith(
            title: _titleController.text,
            description: _descriptionController.text.isEmpty
                ? null
                : _descriptionController.text,
            type: _selectedType,
            updatedAt: DateTime.now(),
            filters: _filters,
            visualizations: _visualizations,
            period: _selectedPeriod,
            customStartDate: _customStartDate,
            customEndDate: _customEndDate,
            isShared: _isShared,
            sharedWithUserIds: _isShared ? _sharedWithUserIds : null,
            isFavorite: _isFavorite,
            color: _reportColor,
            icon: _reportIcon,
          ),
        );
      } else {
        // إنشاء تقرير جديد
        final newReport = EnhancedReport(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: _titleController.text,
          description: _descriptionController.text.isEmpty
              ? null
              : _descriptionController.text,
          type: _selectedType,
          createdBy: userId,
          createdAt: DateTime.now(),
          filters: _filters,
          visualizations: _visualizations,
          period: _selectedPeriod,
          customStartDate: _customStartDate,
          customEndDate: _customEndDate,
          isShared: _isShared,
          sharedWithUserIds: _isShared ? _sharedWithUserIds : null,
          isFavorite: _isFavorite,
          color: _reportColor,
          icon: _reportIcon,
        );

        report = await _reportController.createReport(newReport);
      }

      if (report != null) {
        Get.back();
        Get.to(() => ReportViewerScreen(reportId: report!.id));
      } else {
        throw Exception('فشل في حفظ التقرير');
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض مربع حوار المشاركة
  void _showShareDialog() {
    // الحصول على قائمة المستخدمين
    final authController = Get.find<AuthController>();
    final currentUserId = authController.currentUser.value?.id;

    if (currentUserId == null) {
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول لمشاركة التقرير',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // قائمة المستخدمين المشارك معهم
    final selectedUserIds = List<int>.from(_sharedWithUserIds);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Text(
                'مشاركة التقرير',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'اختر المستخدمين الذين تريد مشاركة التقرير معهم',
                style: AppStyles.bodyMedium,
              ),
              const SizedBox(height: 24),

              // خيار المشاركة العامة
              SwitchListTile(
                title: const Text('مشاركة عامة'),
                subtitle: const Text('مشاركة التقرير مع جميع المستخدمين'),
                value: _isShared,
                onChanged: (value) {
                  setState(() {
                    _isShared = value;
                  });
                  Get.back();
                },
              ),

              const Divider(),
              const SizedBox(height: 16),

              // قائمة المستخدمين
              Flexible(
                child: FutureBuilder<List<User>>(
                  future: Future.value([]), // authController.getAllUsers(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'حدث خطأ أثناء تحميل المستخدمين: ${snapshot.error}',
                          style:
                              AppStyles.bodyMedium.copyWith(color: Colors.red),
                        ),
                      );
                    }

                    final users = snapshot.data ?? [];

                    // استبعاد المستخدم الحالي
                    final otherUsers = users
                        .where((user) => user.id != currentUserId)
                        .toList();

                    if (otherUsers.isEmpty) {
                      return const Center(
                        child: Text('لا يوجد مستخدمين آخرين'),
                      );
                    }

                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: otherUsers.length,
                      itemBuilder: (context, index) {
                        final user = otherUsers[index];
                        final isSelected = selectedUserIds.contains(user.id);

                        return CheckboxListTile(
                          title: Text(user.name),
                          subtitle: Text(user.email.toString()),
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              selectedUserIds.add(user.id);
                            } else {
                              selectedUserIds.remove(user.id);
                            }
                            setState(() {});
                          },
                        );
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _sharedWithUserIds = selectedUserIds;
                      });
                      Get.back();
                    },
                    child: const Text('مشاركة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار إضافة تصور مرئي
  void _showAddVisualizationDialog() {
    // متغيرات الحوار
    String title = '';
    String description = '';
    VisualizationType selectedType = VisualizationType.barChart;
    String xAxisField = '';
    String yAxisField = '';
    List<String> dataFields = [];

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Text(
                'إضافة تصور مرئي جديد',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 24),

              // نموذج إضافة التصور المرئي
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان التصور المرئي
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'عنوان التصور المرئي *',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          title = value;
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال عنوان التصور المرئي';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // وصف التصور المرئي
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'وصف التصور المرئي',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                        onChanged: (value) {
                          description = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // نوع التصور المرئي
                      DropdownButtonFormField<VisualizationType>(
                        value: selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع التصور المرئي *',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          VisualizationType.barChart,
                          VisualizationType.pieChart,
                          VisualizationType.lineChart,
                          VisualizationType.table,
                          VisualizationType.kpiCard,
                        ].map((type) {
                          return DropdownMenuItem<VisualizationType>(
                            value: type,
                            child: Text(_getVisualizationTypeName(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            selectedType = value;
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقول البيانات
                      Text(
                        'حقول البيانات',
                        style: AppStyles.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      // حقل المحور السيني (X)
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'حقل المحور السيني (X) *',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: status, name, date',
                        ),
                        onChanged: (value) {
                          xAxisField = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقل المحور الصادي (Y)
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'حقل المحور الصادي (Y) *',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: count, value, amount',
                        ),
                        onChanged: (value) {
                          yAxisField = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقول البيانات الإضافية
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'حقول البيانات الإضافية',
                          border: OutlineInputBorder(),
                          hintText: 'أدخل الحقول مفصولة بفواصل',
                        ),
                        onChanged: (value) {
                          dataFields = value
                              .split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList();
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      // التحقق من صحة البيانات
                      if (title.isEmpty ||
                          xAxisField.isEmpty ||
                          yAxisField.isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال جميع الحقول المطلوبة',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                        );
                        return;
                      }

                      // إنشاء قائمة حقول البيانات
                      final allDataFields = [xAxisField, yAxisField];
                      allDataFields.addAll(dataFields);

                      // إنشاء التصور المرئي
                      final visualization =
                          _reportController.createVisualization(
                        title: title,
                        description: description.isEmpty ? null : description,
                        type: selectedType,
                        xAxisField: xAxisField,
                        yAxisField: yAxisField,
                        dataFields:
                            allDataFields.toSet().toList(), // إزالة التكرارات
                      );

                      // إضافة التصور المرئي إلى القائمة
                      setState(() {
                        _visualizations.add(visualization);
                      });

                      Get.back();
                    },
                    child: const Text('إضافة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تعديل تصور مرئي
  void _editVisualization(int index) {
    final visualization = _visualizations[index];

    // متغيرات الحوار
    String title = visualization.title;
    String description = visualization.description ?? '';
    VisualizationType selectedType = visualization.type;
    String xAxisField =
        visualization.xAxisField ?? visualization.dataFields.first;
    String yAxisField = visualization.yAxisField ??
        (visualization.dataFields.length > 1
            ? visualization.dataFields[1]
            : '');
    List<String> dataFields = List.from(visualization.dataFields);

    // إزالة حقول المحاور من قائمة حقول البيانات الإضافية
    dataFields.remove(xAxisField);
    dataFields.remove(yAxisField);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Text(
                'تعديل التصور المرئي',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 24),

              // نموذج تعديل التصور المرئي
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان التصور المرئي
                      TextFormField(
                        initialValue: title,
                        decoration: const InputDecoration(
                          labelText: 'عنوان التصور المرئي *',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          title = value;
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال عنوان التصور المرئي';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // وصف التصور المرئي
                      TextFormField(
                        initialValue: description,
                        decoration: const InputDecoration(
                          labelText: 'وصف التصور المرئي',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                        onChanged: (value) {
                          description = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // نوع التصور المرئي
                      DropdownButtonFormField<VisualizationType>(
                        value: selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع التصور المرئي *',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          VisualizationType.barChart,
                          VisualizationType.pieChart,
                          VisualizationType.lineChart,
                          VisualizationType.table,
                          VisualizationType.kpiCard,
                        ].map((type) {
                          return DropdownMenuItem<VisualizationType>(
                            value: type,
                            child: Text(_getVisualizationTypeName(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            selectedType = value;
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقول البيانات
                      Text(
                        'حقول البيانات',
                        style: AppStyles.titleSmall,
                      ),
                      const SizedBox(height: 8),

                      // حقل المحور السيني (X)
                      TextFormField(
                        initialValue: xAxisField,
                        decoration: const InputDecoration(
                          labelText: 'حقل المحور السيني (X) *',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: status, name, date',
                        ),
                        onChanged: (value) {
                          xAxisField = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقل المحور الصادي (Y)
                      TextFormField(
                        initialValue: yAxisField,
                        decoration: const InputDecoration(
                          labelText: 'حقل المحور الصادي (Y) *',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: count, value, amount',
                        ),
                        onChanged: (value) {
                          yAxisField = value;
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقول البيانات الإضافية
                      TextFormField(
                        initialValue: dataFields.join(', '),
                        decoration: const InputDecoration(
                          labelText: 'حقول البيانات الإضافية',
                          border: OutlineInputBorder(),
                          hintText: 'أدخل الحقول مفصولة بفواصل',
                        ),
                        onChanged: (value) {
                          dataFields = value
                              .split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList();
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      // التحقق من صحة البيانات
                      if (title.isEmpty ||
                          xAxisField.isEmpty ||
                          yAxisField.isEmpty) {
                        Get.snackbar(
                          'خطأ',
                          'يرجى إدخال جميع الحقول المطلوبة',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                        );
                        return;
                      }

                      // إنشاء قائمة حقول البيانات
                      final allDataFields = [xAxisField, yAxisField];
                      allDataFields.addAll(dataFields);

                      // تحديث التصور المرئي
                      setState(() {
                        _visualizations[index] = ReportVisualization(
                          id: visualization.id,
                          title: title,
                          description: description.isEmpty ? null : description,
                          type: selectedType,
                          xAxisField: xAxisField,
                          yAxisField: yAxisField,
                          dataFields: allDataFields.toSet().toList(), // إزالة التكرارات
                          orientation: visualization.orientation,
                          showValues: visualization.showValues,
                          showLabels: visualization.showLabels,
                          showGrid: visualization.showGrid,
                          showLegend: visualization.showLegend,
                          legendPosition: visualization.legendPosition,
                          seriesColors: visualization.seriesColors,
                          width: visualization.width,
                          height: visualization.height,
                          settings: visualization.settings,
                        );
                      });

                      Get.back();
                    },
                    child: const Text('حفظ'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'أداء القسم';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.taskCompletion:
        return 'إكمال المهام';
      case ReportType.userActivity:
        return 'نشاط المستخدم';
      case ReportType.departmentWorkload:
        return 'عبء العمل للقسم';
      case ReportType.projectStatus:
        return 'حالة المشروع';
      case ReportType.custom:
        return 'مخصص';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم نوع التصور المرئي
  String _getVisualizationTypeName(VisualizationType type) {
    switch (type) {
      case VisualizationType.table:
        return 'جدول';
      case VisualizationType.barChart:
        return 'مخطط شريطي';
      case VisualizationType.lineChart:
        return 'مخطط خطي';
      case VisualizationType.pieChart:
        return 'مخطط دائري';
      case VisualizationType.areaChart:
        return 'مخطط مساحي';
      case VisualizationType.bubbleChart:
        return 'مخطط فقاعي';
      case VisualizationType.radarChart:
        return 'مخطط راداري';
      case VisualizationType.gaugeChart:
        return 'مخطط مقياس';
      case VisualizationType.kpiCard:
        return 'بطاقة مؤشر أداء';
      case VisualizationType.heatMap:
        return 'خريطة حرارية';
      case VisualizationType.ganttChart:
        return 'مخطط جانت';
      case VisualizationType.treeMap:
        return 'خريطة شجرية';
      case VisualizationType.summary:
        return 'ملخص';
      case VisualizationType.custom:
        return 'مخصص';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على أيقونة نوع التصور المرئي
  IconData _getVisualizationTypeIcon(VisualizationType type) {
    switch (type) {
      case VisualizationType.table:
        return Icons.table_chart;
      case VisualizationType.barChart:
        return Icons.bar_chart;
      case VisualizationType.lineChart:
        return Icons.show_chart;
      case VisualizationType.pieChart:
        return Icons.pie_chart;
      case VisualizationType.areaChart:
        return Icons.area_chart;
      case VisualizationType.bubbleChart:
        return Icons.bubble_chart;
      case VisualizationType.radarChart:
        return Icons.radar;
      case VisualizationType.gaugeChart:
        return Icons.speed;
      case VisualizationType.kpiCard:
        return Icons.dashboard;
      case VisualizationType.heatMap:
        return Icons.grid_on;
      case VisualizationType.ganttChart:
        return Icons.stacked_bar_chart;
      case VisualizationType.treeMap:
        return Icons.account_tree;
      case VisualizationType.summary:
        return Icons.summarize;
      case VisualizationType.custom:
        return Icons.build;
      default:
        return Icons.insert_chart;
    }
  }
}
