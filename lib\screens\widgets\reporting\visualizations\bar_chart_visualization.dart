import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../constants/app_styles.dart';

/// تعداد اتجاه المخطط
enum ChartOrientation {
  vertical,
  horizontal,
}

/// تعداد موضع المفتاح
enum ChartLegendPosition {
  top,
  bottom,
  left,
  right,
}

/// مكون المخطط الشريطي باستخدام Syncfusion Charts
class BarChartVisualization extends StatelessWidget {
  /// عنوان المخطط
  final String title;

  /// وصف المخطط
  final String? description;

  /// بيانات المخطط
  final List<Map<String, dynamic>> data;

  /// حقل المحور السيني (X)
  final String xAxisField;

  /// عنوان المحور السيني
  final String? xAxisLabel;

  /// حقل المحور الصادي (Y)
  final String yAxisField;

  /// عنوان المحور الصادي
  final String? yAxisLabel;

  /// اتجاه المخطط
  final ChartOrientation orientation;

  /// إظهار القيم
  final bool showValues;

  /// إظهار التسميات
  final bool showLabels;

  /// إظهار الشبكة
  final bool showGrid;

  /// إظهار المفتاح
  final bool showLegend;

  /// موضع المفتاح
  final ChartLegendPosition? legendPosition;

  /// ألوان السلاسل
  final List<Color>? seriesColors;

  /// العرض
  final double? width;

  /// الارتفاع
  final double? height;

  const BarChartVisualization({
    super.key,
    required this.title,
    this.description,
    required this.data,
    required this.xAxisField,
    this.xAxisLabel,
    required this.yAxisField,
    this.yAxisLabel,
    this.orientation = ChartOrientation.vertical,
    this.showValues = true,
    this.showLabels = true,
    this.showGrid = true,
    this.showLegend = true,
    this.legendPosition,
    this.seriesColors,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    // تحديد الارتفاع حسب عدد البيانات
    final calculatedHeight = height ?? (data.length * 40.0 + 100).clamp(200.0, 500.0);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المخطط
              Text(
                title,
                style: AppStyles.titleMedium,
              ),
              if (description != null && description!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  description!,
                  style: AppStyles.bodySmall,
                ),
              ],
              const SizedBox(height: 16),
              // المخطط
              SizedBox(
                height: calculatedHeight,
                child: _buildChart(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المخطط باستخدام Syncfusion
  Widget _buildChart() {
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: xAxisLabel ?? ''),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: yAxisLabel ?? ''),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      legend: Legend(
        isVisible: showLegend,
        position: _getSyncfusionLegendPosition(),
      ),
      series: <CartesianSeries>[
        ColumnSeries<Map<String, dynamic>, String>(
          dataSource: data,
          xValueMapper: (Map<String, dynamic> item, _) => item[xAxisField].toString(),
          yValueMapper: (Map<String, dynamic> item, _) => _getYValueFromData(item),
          pointColorMapper: (Map<String, dynamic> item, int index) => _getBarColor(index),
          dataLabelSettings: DataLabelSettings(isVisible: showValues),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
      ],
    );
  }

  /// تحويل موضع المفتاح إلى Syncfusion
  LegendPosition _getSyncfusionLegendPosition() {
    switch (legendPosition) {
      case ChartLegendPosition.top:
        return LegendPosition.top;
      case ChartLegendPosition.left:
        return LegendPosition.left;
      case ChartLegendPosition.right:
        return LegendPosition.right;
      case ChartLegendPosition.bottom:
      default:
        return LegendPosition.bottom;
    }
  }

  /// الحصول على قيمة Y من البيانات
  double _getYValueFromData(Map<String, dynamic> item) {
    final value = item[yAxisField];
    if (value is int) {
      return value.toDouble();
    } else if (value is double) {
      return value;
    } else if (value is String) {
      return double.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// الحصول على لون الشريط
  Color _getBarColor(int index) {
    if (seriesColors != null && index < seriesColors!.length) {
      return seriesColors![index];
    }

    // ألوان افتراضية
    final defaultColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
    ];

    return defaultColors[index % defaultColors.length];
  }
}