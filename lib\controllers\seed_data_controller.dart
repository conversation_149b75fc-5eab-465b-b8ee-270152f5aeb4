import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/api/seed_data_api_service.dart';

/// متحكم بيانات البذور (البيانات التجريبية)
class SeedDataController extends GetxController {
  final SeedDataApiService _apiService = SeedDataApiService();

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // حالة العمليات
  final RxBool _isSeedingUsers = false.obs;
  final RxBool _isSeedingDepartments = false.obs;
  final RxBool _isSeedingTasks = false.obs;
  final RxBool _isSeedingTaskStatuses = false.obs;
  final RxBool _isSeedingTaskPriorities = false.obs;
  final RxBool _isSeedingPermissions = false.obs;
  final RxBool _isClearingData = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  bool get isSeedingUsers => _isSeedingUsers.value;
  bool get isSeedingDepartments => _isSeedingDepartments.value;
  bool get isSeedingTasks => _isSeedingTasks.value;
  bool get isSeedingTaskStatuses => _isSeedingTaskStatuses.value;
  bool get isSeedingTaskPriorities => _isSeedingTaskPriorities.value;
  bool get isSeedingPermissions => _isSeedingPermissions.value;
  bool get isClearingData => _isClearingData.value;

  /// إضافة جميع البيانات التجريبية
  Future<bool> seedAllData() async {
    _isLoading.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedAllData();
      _successMessage.value = result;
      debugPrint('تم إضافة جميع البيانات التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة البيانات التجريبية: $e';
      debugPrint('خطأ في إضافة البيانات التجريبية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة بيانات المستخدمين التجريبية
  Future<bool> seedUsers() async {
    _isSeedingUsers.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedUsers();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات المستخدمين التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات المستخدمين التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات المستخدمين التجريبية: $e');
      return false;
    } finally {
      _isSeedingUsers.value = false;
    }
  }

  /// إضافة بيانات الأقسام التجريبية
  Future<bool> seedDepartments() async {
    _isSeedingDepartments.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedDepartments();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات الأقسام التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات الأقسام التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات الأقسام التجريبية: $e');
      return false;
    } finally {
      _isSeedingDepartments.value = false;
    }
  }

  /// إضافة بيانات المهام التجريبية
  Future<bool> seedTasks() async {
    _isSeedingTasks.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedTasks();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات المهام التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات المهام التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات المهام التجريبية: $e');
      return false;
    } finally {
      _isSeedingTasks.value = false;
    }
  }

  /// إضافة بيانات حالات المهام التجريبية
  Future<bool> seedTaskStatuses() async {
    _isSeedingTaskStatuses.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedTaskStatuses();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات حالات المهام التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات حالات المهام التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات حالات المهام التجريبية: $e');
      return false;
    } finally {
      _isSeedingTaskStatuses.value = false;
    }
  }

  /// إضافة بيانات أولويات المهام التجريبية
  Future<bool> seedTaskPriorities() async {
    _isSeedingTaskPriorities.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedTaskPriorities();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات أولويات المهام التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات أولويات المهام التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات أولويات المهام التجريبية: $e');
      return false;
    } finally {
      _isSeedingTaskPriorities.value = false;
    }
  }

  /// إضافة بيانات الصلاحيات التجريبية
  Future<bool> seedPermissions() async {
    _isSeedingPermissions.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.seedPermissions();
      _successMessage.value = result;
      debugPrint('تم إضافة بيانات الصلاحيات التجريبية بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة بيانات الصلاحيات التجريبية: $e';
      debugPrint('خطأ في إضافة بيانات الصلاحيات التجريبية: $e');
      return false;
    } finally {
      _isSeedingPermissions.value = false;
    }
  }

  /// مسح جميع البيانات
  Future<bool> clearAllData() async {
    _isClearingData.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final result = await _apiService.clearAllData();
      _successMessage.value = result;
      debugPrint('تم مسح جميع البيانات بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مسح البيانات: $e';
      debugPrint('خطأ في مسح البيانات: $e');
      return false;
    } finally {
      _isClearingData.value = false;
    }
  }

  /// إضافة البيانات التجريبية خطوة بخطوة
  Future<bool> seedDataStepByStep() async {
    _isLoading.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      // إضافة المستخدمين
      await seedUsers();
      if (_error.value.isNotEmpty) return false;

      // إضافة الأقسام
      await seedDepartments();
      if (_error.value.isNotEmpty) return false;

      // إضافة حالات المهام
      await seedTaskStatuses();
      if (_error.value.isNotEmpty) return false;

      // إضافة أولويات المهام
      await seedTaskPriorities();
      if (_error.value.isNotEmpty) return false;

      // إضافة الصلاحيات
      await seedPermissions();
      if (_error.value.isNotEmpty) return false;

      // إضافة المهام
      await seedTasks();
      if (_error.value.isNotEmpty) return false;

      _successMessage.value = 'تم إضافة جميع البيانات التجريبية بنجاح خطوة بخطوة!';
      debugPrint('تم إضافة جميع البيانات التجريبية بنجاح خطوة بخطوة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة البيانات التجريبية خطوة بخطوة: $e';
      debugPrint('خطأ في إضافة البيانات التجريبية خطوة بخطوة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من وجود البيانات
  Future<Map<String, bool>> checkDataExists() async {
    try {
      final result = await _apiService.checkDataStatus();
      // تحويل Map<String, int> إلى Map<String, bool>
      final boolResult = <String, bool>{};
      result.forEach((key, value) {
        boolResult[key] = value > 0;
      });
      debugPrint('تم التحقق من وجود البيانات');
      return boolResult;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود البيانات: $e');
      return {};
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// مسح رسالة النجاح
  void clearSuccessMessage() {
    _successMessage.value = '';
  }

  /// مسح جميع الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }
}
