import 'package:flutter_application_2/models/role_default_permission_model.dart';
import 'package:flutter_application_2/models/user_model.dart';

/// نموذج الدور (Role) - متوافق 100% مع الباك اند
class Role {
  final int id;
  final String name;
  final String displayName;
  final String? description;
  final int level;
  final bool isSystemRole;
  final bool isActive;
  final int createdAt;
  final int? updatedAt;
  final int? createdBy;
  final int? updatedBy;

  // العلاقات البرمجية
  final List<RoleDefaultPermission>? defaultPermissions;
  final List<User>? users;

  const Role({
    required this.id,
    required this.name,
    required this.displayName,
    this.description,
    required this.level,
    this.isSystemRole = false,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.defaultPermissions,
    this.users,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    try {
      return Role(
        id: json['id'] != null ? (json['id'] as num).toInt() : 0,
        name: json['name']?.toString() ?? 'دور غير محدد',
        displayName: json['displayName']?.toString() ?? json['name']?.toString() ?? 'دور غير محدد',
        description: json['description']?.toString(),
        level: json['level'] != null ? (json['level'] as num).toInt() : 0,
        isSystemRole: json['isSystemRole'] as bool? ?? false,
        isActive: json['isActive'] as bool? ?? true,
        createdAt: json['createdAt'] != null ? (json['createdAt'] as num).toInt() : DateTime.now().millisecondsSinceEpoch ~/ 1000,
        updatedAt: json['updatedAt'] != null ? (json['updatedAt'] as num).toInt() : null,
        createdBy: json['createdBy'] != null ? (json['createdBy'] as num).toInt() : null,
        updatedBy: json['updatedBy'] != null ? (json['updatedBy'] as num).toInt() : null,
        defaultPermissions: json['defaultPermissions'] != null
            ? (json['defaultPermissions'] as List)
                .map((p) => RoleDefaultPermission.fromJson(p as Map<String, dynamic>))
                .toList()
            : null,
        users: json['users'] != null
            ? (json['users'] as List)
                .map((u) => User.fromJson(u as Map<String, dynamic>))
                .toList()
            : null,
      );
    } catch (e) {
      throw Exception('خطأ في تحليل Role: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'description': description,
      'level': level,
      'isSystemRole': isSystemRole,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'defaultPermissions': defaultPermissions?.map((p) => p.toJson()).toList(),
      'users': users?.map((u) => u.toJson()).toList(),
    };
  }

  /// التحقق من أن الدور هو مدير أو أعلى
  bool get isManagerOrAbove {
    final roleName = name.toLowerCase();
    return roleName.contains('مدير') || 
           roleName.contains('admin') || 
           roleName.contains('manager') || 
           roleName.contains('supervisor') || 
           roleName.contains('مشرف');
  }
}
