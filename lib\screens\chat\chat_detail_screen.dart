import 'package:flutter/material.dart';
import '../../models/chat_group_models.dart';

/// @deprecated استخدم UnifiedChatDetailScreen بدلاً من ذلك
/// تم استبداله بشاشة المحادثة الموحدة
/// انظر: lib/screens/chat/unified_chat_detail_screen.dart
@Deprecated('Use UnifiedChatDetailScreen instead')
class ChatDetailScreen extends StatelessWidget {
  final ChatGroup chatGroup;

  const ChatDetailScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Deprecated Screen'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'This screen is deprecated',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Please use UnifiedChatDetailScreen instead',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
