using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace webApi.Middleware
{
    /// <summary>
    /// Middleware للأمان المتقدم
    /// يوفر حماية من الهجمات الشائعة ومراقبة الأنشطة المشبوهة
    /// </summary>
    public class SecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SecurityMiddleware> _logger;
        private readonly SecurityOptions _options;

        // قوائم الحماية
        private static readonly HashSet<string> _blockedIPs = new();
        private static readonly Dictionary<string, int> _requestCounts = new();
        private static readonly Dictionary<string, DateTime> _lastRequestTimes = new();

        // أنماط الهجمات الشائعة
        private static readonly List<Regex> _maliciousPatterns = new()
        {
            new Regex(@"('|(\\')|(;|\\;)|(\\|)|(\\*)|(\*|\\*))", RegexOptions.IgnoreCase),
            new Regex(@"(union|select|insert|delete|update|drop|create|alter)", RegexOptions.IgnoreCase),
            new Regex(@"(script|javascript|vbscript|onload|onerror)", RegexOptions.IgnoreCase),
            new Regex(@"(<|&lt;)(\s*)script", RegexOptions.IgnoreCase),
            new Regex(@"(exec|execute|sp_|xp_)", RegexOptions.IgnoreCase),
            new Regex(@"(\.\.\/|\.\.\\)", RegexOptions.IgnoreCase),
        };

        public SecurityMiddleware(RequestDelegate next, ILogger<SecurityMiddleware> logger, SecurityOptions options = null)
        {
            _next = next;
            _logger = logger;
            _options = options ?? new SecurityOptions();
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var clientIP = GetClientIP(context);
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            var requestPath = context.Request.Path.Value;

            try
            {
                // 1. فحص IP المحظور
                if (IsIPBlocked(clientIP))
                {
                    _logger.LogWarning("🚫 طلب من IP محظور: {IP}", clientIP);
                    await RespondWithError(context, HttpStatusCode.Forbidden, "IP محظور");
                    return;
                }

                // 2. فحص معدل الطلبات (Rate Limiting)
                if (!CheckRateLimit(clientIP))
                {
                    _logger.LogWarning("⚠️ تجاوز معدل الطلبات المسموح: {IP}", clientIP);
                    await RespondWithError(context, HttpStatusCode.TooManyRequests, "تجاوز معدل الطلبات المسموح");
                    return;
                }

                // 3. فحص User Agent المشبوه
                if (IsSuspiciousUserAgent(userAgent))
                {
                    _logger.LogWarning("🔍 User Agent مشبوه: {UserAgent} من {IP}", userAgent, clientIP);
                    LogSecurityEvent("SuspiciousUserAgent", clientIP, userAgent);
                }

                // 4. فحص المسار للهجمات الشائعة
                if (IsMaliciousPath(requestPath))
                {
                    _logger.LogWarning("🚨 مسار مشبوه: {Path} من {IP}", requestPath, clientIP);
                    LogSecurityEvent("MaliciousPath", clientIP, requestPath);
                    await RespondWithError(context, HttpStatusCode.BadRequest, "طلب غير صالح");
                    return;
                }

                // 5. فحص محتوى الطلب
                if (context.Request.Method == "POST" || context.Request.Method == "PUT")
                {
                    var body = await ReadRequestBody(context);
                    if (!string.IsNullOrEmpty(body) && ContainsMaliciousContent(body))
                    {
                        _logger.LogWarning("🚨 محتوى مشبوه في الطلب من {IP}", clientIP);
                        LogSecurityEvent("MaliciousContent", clientIP, body.Substring(0, Math.Min(body.Length, 200)));
                        await RespondWithError(context, HttpStatusCode.BadRequest, "محتوى غير صالح");
                        return;
                    }
                }

                // 6. إضافة headers الأمان
                AddSecurityHeaders(context);

                // 7. تسجيل الطلب الآمن
                LogSecureRequest(clientIP, requestPath, userAgent);

                // متابعة معالجة الطلب
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في Security Middleware");
                await RespondWithError(context, HttpStatusCode.InternalServerError, "خطأ داخلي");
            }
        }

        /// <summary>
        /// الحصول على IP العميل الحقيقي
        /// </summary>
        private string GetClientIP(HttpContext context)
        {
            // فحص headers المختلفة للحصول على IP الحقيقي
            var headers = new[]
            {
                "CF-Connecting-IP", // Cloudflare
                "X-Forwarded-For",
                "X-Real-IP",
                "X-Client-IP",
                "X-Forwarded",
                "X-Cluster-Client-IP",
                "Forwarded-For",
                "Forwarded"
            };

            foreach (var header in headers)
            {
                var value = context.Request.Headers[header].FirstOrDefault();
                if (!string.IsNullOrEmpty(value))
                {
                    // أخذ أول IP في حالة وجود عدة IPs
                    var ip = value.Split(',')[0].Trim();
                    if (IPAddress.TryParse(ip, out _))
                    {
                        return ip;
                    }
                }
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        /// <summary>
        /// فحص إذا كان IP محظور
        /// </summary>
        private bool IsIPBlocked(string ip)
        {
            return _blockedIPs.Contains(ip);
        }

        /// <summary>
        /// فحص معدل الطلبات
        /// </summary>
        private bool CheckRateLimit(string ip)
        {
            var now = DateTime.UtcNow;
            var key = $"{ip}:{now:yyyy-MM-dd-HH-mm}"; // نافذة دقيقة واحدة

            if (!_requestCounts.ContainsKey(key))
            {
                _requestCounts[key] = 0;
            }

            _requestCounts[key]++;

            // تنظيف البيانات القديمة
            CleanupOldRateLimitData();

            return _requestCounts[key] <= _options.MaxRequestsPerMinute;
        }

        /// <summary>
        /// فحص User Agent المشبوه
        /// </summary>
        private bool IsSuspiciousUserAgent(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent))
                return true;

            var suspiciousPatterns = new[]
            {
                "bot", "crawler", "spider", "scraper",
                "curl", "wget", "python", "java",
                "scanner", "exploit", "hack"
            };

            return suspiciousPatterns.Any(pattern => 
                userAgent.Contains(pattern, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// فحص المسار للهجمات
        /// </summary>
        private bool IsMaliciousPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            return _maliciousPatterns.Any(pattern => pattern.IsMatch(path));
        }

        /// <summary>
        /// فحص المحتوى للهجمات
        /// </summary>
        private bool ContainsMaliciousContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            return _maliciousPatterns.Any(pattern => pattern.IsMatch(content));
        }

        /// <summary>
        /// قراءة محتوى الطلب
        /// </summary>
        private async Task<string> ReadRequestBody(HttpContext context)
        {
            try
            {
                context.Request.EnableBuffering();
                var body = context.Request.Body;
                body.Position = 0;

                using var reader = new StreamReader(body, Encoding.UTF8, leaveOpen: true);
                var content = await reader.ReadToEndAsync();
                body.Position = 0;

                return content;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// إضافة headers الأمان
        /// </summary>
        private void AddSecurityHeaders(HttpContext context)
        {
            var response = context.Response;

            // منع تضمين الصفحة في iframe
            response.Headers.Add("X-Frame-Options", "DENY");

            // منع تخمين نوع المحتوى
            response.Headers.Add("X-Content-Type-Options", "nosniff");

            // تفعيل حماية XSS
            response.Headers.Add("X-XSS-Protection", "1; mode=block");

            // إجبار HTTPS
            response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

            // سياسة المحتوى الآمن
            response.Headers.Add("Content-Security-Policy", 
                "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");

            // إخفاء معلومات الخادم
            response.Headers.Remove("Server");
            response.Headers.Add("Server", "SecureServer");
        }

        /// <summary>
        /// الرد بخطأ
        /// </summary>
        private async Task RespondWithError(HttpContext context, HttpStatusCode statusCode, string message)
        {
            context.Response.StatusCode = (int)statusCode;
            context.Response.ContentType = "application/json";

            var response = new
            {
                error = message,
                timestamp = DateTime.UtcNow,
                path = context.Request.Path.Value
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }

        /// <summary>
        /// تسجيل حدث أمان
        /// </summary>
        private void LogSecurityEvent(string eventType, string ip, string details)
        {
            _logger.LogWarning("🔒 حدث أمان: {EventType} من {IP} - {Details}", 
                eventType, ip, details);
        }

        /// <summary>
        /// تسجيل طلب آمن
        /// </summary>
        private void LogSecureRequest(string ip, string path, string userAgent)
        {
            _logger.LogInformation("✅ طلب آمن: {IP} -> {Path}", ip, path);
        }

        /// <summary>
        /// تنظيف بيانات معدل الطلبات القديمة
        /// </summary>
        private void CleanupOldRateLimitData()
        {
            var cutoff = DateTime.UtcNow.AddMinutes(-5);
            var keysToRemove = _requestCounts.Keys
                .Where(key => DateTime.TryParseExact(key.Split(':')[1], "yyyy-MM-dd-HH-mm", 
                    null, System.Globalization.DateTimeStyles.None, out var time) && time < cutoff)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _requestCounts.Remove(key);
            }
        }

        /// <summary>
        /// حظر IP
        /// </summary>
        public static void BlockIP(string ip)
        {
            _blockedIPs.Add(ip);
        }

        /// <summary>
        /// إلغاء حظر IP
        /// </summary>
        public static void UnblockIP(string ip)
        {
            _blockedIPs.Remove(ip);
        }
    }

    /// <summary>
    /// خيارات الأمان
    /// </summary>
    public class SecurityOptions
    {
        public int MaxRequestsPerMinute { get; set; } = 100;
        public bool EnableRateLimit { get; set; } = true;
        public bool EnableMaliciousContentDetection { get; set; } = true;
        public bool EnableSuspiciousUserAgentDetection { get; set; } = true;
    }
}
