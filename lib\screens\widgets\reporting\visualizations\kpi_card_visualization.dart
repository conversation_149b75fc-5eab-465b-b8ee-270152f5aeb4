import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';

import '../../../../constants/app_styles.dart';

/// مكون بطاقة مؤشر الأداء الرئيسي
///
/// يعرض مؤشر أداء رئيسي في شكل بطاقة
class KpiCardVisualization extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// وصف البطاقة
  final String? description;

  /// بيانات البطاقة
  final Map<String, dynamic> data;

  /// حقل القيمة
  final String valueField;

  /// حقل القيمة المستهدفة (اختياري)
  final String? targetField;

  /// حقل القيمة السابقة (اختياري)
  final String? previousField;

  /// وحدة القياس (اختياري)
  final String? unit;

  /// تنسيق القيمة (اختياري)
  final String? format;

  /// لون البطاقة
  final Color? color;

  /// أيقونة البطاقة
  final IconData? icon;

  /// العرض
  final double? width;

  /// الارتفاع
  final double? height;

  const KpiCardVisualization({
    super.key,
    required this.title,
    this.description,
    required this.data,
    required this.valueField,
    this.targetField,
    this.previousField,
    this.unit,
    this.format,
    this.color,
    this.icon,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    // الحصول على القيم
    final value = _getValue(valueField);
    final target = targetField != null ? _getValue(targetField!) : null;
    final previous = previousField != null ? _getValue(previousField!) : null;

    // حساب النسبة المئوية
    final percentage = target != null && target > 0 ? (value / target).clamp(0.0, 1.0) : null;

    // حساب نسبة التغيير
    final changePercentage = previous != null && previous != 0
        ? ((value - previous) / previous * 100)
        : null;

    // تحديد لون البطاقة
    final cardColor = color ?? Colors.blue;

    // تحديد لون التغيير
    Color changeColor = Colors.grey;
    if (changePercentage != null) {
      changeColor = changePercentage > 0 ? Colors.green : (changePercentage < 0 ? Colors.red : Colors.grey);
    }

    return SingleChildScrollView(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          width: width,
          height: height,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: cardColor.withValues(alpha: 77)), // 0.3 * 255 = 77
            gradient: LinearGradient(
              colors: [
                cardColor.withValues(alpha: 26), // 0.1 * 255 = 26
                Colors.white,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // رأس البطاقة
            Row(
              children: [
                // أيقونة البطاقة
                if (icon != null)
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: cardColor.withValues(alpha: 51), // 0.2 * 255 = 51
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: cardColor,
                      size: 24,
                    ),
                  ),
                if (icon != null) const SizedBox(width: 12),
                // عنوان البطاقة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppStyles.titleSmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (description != null && description!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          description!,
                          style: AppStyles.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const Spacer(),
            // القيمة
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  _formatValue(value),
                  style: AppStyles.headingLarge.copyWith(
                    color: cardColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (unit != null) ...[
                  const SizedBox(width: 4),
                  Text(
                    unit!,
                    style: AppStyles.bodyMedium.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
                const Spacer(),
                // نسبة التغيير
                if (changePercentage != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: changeColor.withValues(alpha: 26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          changePercentage > 0
                              ? Icons.arrow_upward
                              : (changePercentage < 0 ? Icons.arrow_downward : Icons.remove),
                          color: changeColor,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${changePercentage.abs().toStringAsFixed(1)}%',
                          style: AppStyles.bodySmall.copyWith(
                            color: changeColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            // شريط التقدم
            if (percentage != null) ...[
              const SizedBox(height: 8),
              LinearPercentIndicator(
                percent: percentage,
                lineHeight: 8,
                animation: true,
                animationDuration: 1000,
                backgroundColor: Colors.grey.shade200,
                progressColor: cardColor,
                barRadius: const Radius.circular(4),
                padding: EdgeInsets.zero,
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${(percentage * 100).toStringAsFixed(0)}%',
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (target != null)
                    Text(
                      'الهدف: ${_formatValue(target)}',
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    ),
  );
  }

  /// الحصول على قيمة من البيانات
  double _getValue(String field) {
    final value = data[field];
    if (value is int) {
      return value.toDouble();
    } else if (value is double) {
      return value;
    } else if (value is String) {
      return double.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// تنسيق القيمة
  String _formatValue(double value) {
    if (format == null) {
      // تنسيق افتراضي
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      } else if (value % 1 == 0) {
        return value.toInt().toString();
      } else {
        return value.toStringAsFixed(1);
      }
    } else {
      // تنسيق مخصص
      switch (format) {
        case 'integer':
          return value.toInt().toString();
        case 'decimal1':
          return value.toStringAsFixed(1);
        case 'decimal2':
          return value.toStringAsFixed(2);
        case 'percent':
          return '${(value * 100).toStringAsFixed(1)}%';
        case 'currency':
          return '\$${value.toStringAsFixed(2)}';
        default:
          return value.toString();
      }
    }
  }
}
