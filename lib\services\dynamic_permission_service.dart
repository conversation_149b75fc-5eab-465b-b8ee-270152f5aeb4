// import 'dart:convert';
// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import '../controllers/auth_controller.dart';
// import '../services/api/permissions_api_service.dart';
// import '../models/permission_models.dart';
// import '../models/user_model.dart';

// /// خدمة الصلاحيات الديناميكية المحسنة
// /// تتحقق من الصلاحيات بشكل ديناميكي من قاعدة البيانات
// /// وتوفر تخزين مؤقت ذكي لتحسين الأداء
// class DynamicPermissionService extends GetxService {
//   static DynamicPermissionService get instance => Get.find<DynamicPermissionService>();
  
//   final PermissionsApiService _apiService = PermissionsApiService();
//   final AuthController _authController = Get.find<AuthController>();
  
//   // تخزين مؤقت للصلاحيات
//   final RxMap<String, bool> _permissionCache = <String, bool>{}.obs;
//   final RxMap<String, DateTime> _cacheTimestamps = <String, DateTime>{}.obs;
  
//   // إعدادات التخزين المؤقت
//   static const Duration _cacheExpiry = Duration(minutes: 15);
//   static const int _maxCacheSize = 1000;
  
//   // حالة التحميل
//   final RxBool _isLoading = false.obs;
//   final RxString _error = ''.obs;
  
//   // Getters
//   bool get isLoading => _isLoading.value;
//   String get error => _error.value;
//   Map<String, bool> get cachedPermissions => Map.from(_permissionCache);
  
//   @override
//   void onInit() {
//     super.onInit();
//     debugPrint('🔐 تهيئة خدمة الصلاحيات الديناميكية');
//     _initializeService();
//   }
  
//   /// تهيئة الخدمة
//   Future<void> _initializeService() async {
//     try {
//       // تنظيف التخزين المؤقت المنتهي الصلاحية
//       _cleanExpiredCache();
      
//       // تحميل الصلاحيات الأساسية للمستخدم الحالي
//       await _loadUserBasicPermissions();
      
//       debugPrint('✅ تم تهيئة خدمة الصلاحيات الديناميكية');
//     } catch (e) {
//       debugPrint('❌ خطأ في تهيئة خدمة الصلاحيات: $e');
//       _error.value = 'فشل في تهيئة خدمة الصلاحيات: $e';
//     }
//   }
  
//   /// تحميل الصلاحيات الأساسية للمستخدم الحالي
//   Future<void> _loadUserBasicPermissions() async {
//     final currentUser = _authController.currentUser.value;
//     if (currentUser == null) return;
    
//     try {
//       // تحميل الصلاحيات الأساسية المطلوبة للواجهة الرئيسية
//       final basicPermissions = [
//         'view_dashboard',
//         'view_tasks',
//         'view_users',
//         'view_reports',
//         'view_admin_panel',
//         'manage_users',
//         'manage_roles',
//         'manage_permissions',
//         'system_settings',
//       ];
      
//       await checkMultiplePermissions(basicPermissions);
//     } catch (e) {
//       debugPrint('⚠️ فشل في تحميل الصلاحيات الأساسية: $e');
//     }
//   }
  
//   /// التحقق من صلاحية واحدة
//   Future<bool> hasPermission(String permissionName) async {
//     if (permissionName.isEmpty) return false;
    
//     try {
//       // التحقق من التخزين المؤقت أولاً
//       final cachedResult = _getCachedPermission(permissionName);
//       if (cachedResult != null) {
//         debugPrint('🔍 استخدام التخزين المؤقت للصلاحية: $permissionName = $cachedResult');
//         return cachedResult;
//       }
      
//       // التحقق من قاعدة البيانات
//       final hasPermission = await _checkPermissionFromDatabase(permissionName);
      
//       // حفظ في التخزين المؤقت
//       _cachePermission(permissionName, hasPermission);
      
//       debugPrint('🔐 نتيجة التحقق من الصلاحية: $permissionName = $hasPermission');
//       return hasPermission;
//     } catch (e) {
//       debugPrint('❌ خطأ في التحقق من الصلاحية $permissionName: $e');
//       _error.value = 'فشل في التحقق من الصلاحية: $e';
//       return false;
//     }
//   }
  
//   /// التحقق من صلاحيات متعددة
//   Future<Map<String, bool>> checkMultiplePermissions(List<String> permissionNames) async {
//     if (permissionNames.isEmpty) return {};
    
//     try {
//       _isLoading.value = true;
//       _error.value = '';
      
//       final results = <String, bool>{};
//       final uncachedPermissions = <String>[];
      
//       // فصل الصلاحيات المخزنة مؤقتاً عن غير المخزنة
//       for (final permission in permissionNames) {
//         final cachedResult = _getCachedPermission(permission);
//         if (cachedResult != null) {
//           results[permission] = cachedResult;
//         } else {
//           uncachedPermissions.add(permission);
//         }
//       }
      
//       // التحقق من الصلاحيات غير المخزنة مؤقتاً
//       if (uncachedPermissions.isNotEmpty) {
//         final currentUser = _authController.currentUser.value;
//         if (currentUser != null) {
//           final apiResults = await _apiService.checkMultiplePermissions(
//             currentUser.id,
//             uncachedPermissions,
//           );
          
//           // دمج النتائج وحفظها في التخزين المؤقت
//           for (final entry in apiResults.entries) {
//             results[entry.key] = entry.value;
//             _cachePermission(entry.key, entry.value);
//           }
//         }
//       }
      
//       debugPrint('🔐 نتائج التحقق من ${permissionNames.length} صلاحية: ${results.length} نتيجة');
//       return results;
//     } catch (e) {
//       debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
//       _error.value = 'فشل في التحقق من الصلاحيات: $e';
//       return Map.fromIterable(permissionNames, value: (_) => false);
//     } finally {
//       _isLoading.value = false;
//     }
//   }
  
//   /// التحقق من صلاحية من قاعدة البيانات
//   Future<bool> _checkPermissionFromDatabase(String permissionName) async {
//     final currentUser = _authController.currentUser.value;
//     if (currentUser == null) {
//       debugPrint('❌ لا يوجد مستخدم مسجل دخول');
//       return false;
//     }
    
//     try {
//       return await _apiService.checkUserPermission(currentUser.id, permissionName);
//     } catch (e) {
//       debugPrint('❌ خطأ في التحقق من الصلاحية من قاعدة البيانات: $e');
//       return false;
//     }
//   }
  
//   /// الحصول على صلاحية من التخزين المؤقت
//   bool? _getCachedPermission(String permissionName) {
//     final timestamp = _cacheTimestamps[permissionName];
//     if (timestamp == null) return null;
    
//     // التحقق من انتهاء صلاحية التخزين المؤقت
//     if (DateTime.now().difference(timestamp) > _cacheExpiry) {
//       _permissionCache.remove(permissionName);
//       _cacheTimestamps.remove(permissionName);
//       return null;
//     }
    
//     return _permissionCache[permissionName];
//   }
  
//   /// حفظ صلاحية في التخزين المؤقت
//   void _cachePermission(String permissionName, bool hasPermission) {
//     // تنظيف التخزين المؤقت إذا وصل للحد الأقصى
//     if (_permissionCache.length >= _maxCacheSize) {
//       _cleanOldestCacheEntries();
//     }
    
//     _permissionCache[permissionName] = hasPermission;
//     _cacheTimestamps[permissionName] = DateTime.now();
//   }
  
//   /// تنظيف التخزين المؤقت المنتهي الصلاحية
//   void _cleanExpiredCache() {
//     final now = DateTime.now();
//     final expiredKeys = <String>[];
    
//     for (final entry in _cacheTimestamps.entries) {
//       if (now.difference(entry.value) > _cacheExpiry) {
//         expiredKeys.add(entry.key);
//       }
//     }
    
//     for (final key in expiredKeys) {
//       _permissionCache.remove(key);
//       _cacheTimestamps.remove(key);
//     }
    
//     if (expiredKeys.isNotEmpty) {
//       debugPrint('🧹 تم تنظيف ${expiredKeys.length} صلاحية منتهية الصلاحية من التخزين المؤقت');
//     }
//   }
  
//   /// تنظيف أقدم عناصر التخزين المؤقت
//   void _cleanOldestCacheEntries() {
//     final entries = _cacheTimestamps.entries.toList();
//     entries.sort((a, b) => a.value.compareTo(b.value));
    
//     final toRemove = entries.take(_maxCacheSize ~/ 4).map((e) => e.key).toList();
//     for (final key in toRemove) {
//       _permissionCache.remove(key);
//       _cacheTimestamps.remove(key);
//     }
    
//     debugPrint('🧹 تم تنظيف ${toRemove.length} عنصر قديم من التخزين المؤقت');
//   }
  
//   /// مسح جميع التخزين المؤقت
//   void clearCache() {
//     _permissionCache.clear();
//     _cacheTimestamps.clear();
//     debugPrint('🧹 تم مسح جميع التخزين المؤقت للصلاحيات');
//   }
  
//   /// إعادة تحميل صلاحيات المستخدم
//   Future<void> refreshUserPermissions() async {
//     clearCache();
//     await _loadUserBasicPermissions();
//     debugPrint('🔄 تم إعادة تحميل صلاحيات المستخدم');
//   }
  
//   /// التحقق من صلاحية الوصول لشاشة معينة
//   Future<bool> canAccessScreen(String screenName) async {
//     final permissionName = 'access_$screenName';
//     return await hasPermission(permissionName);
//   }
  
//   /// التحقق من صلاحية تنفيذ إجراء معين
//   Future<bool> canPerformAction(String actionName) async {
//     final permissionName = 'action_$actionName';
//     return await hasPermission(permissionName);
//   }
  
//   /// التحقق من صلاحيات إدارية
//   Future<bool> hasAdminPermissions() async {
//     return await hasPermission('admin_access');
//   }
  
//   /// التحقق من صلاحيات المدير العام
//   Future<bool> hasSuperAdminPermissions() async {
//     return await hasPermission('super_admin_access');
//   }
  
//   @override
//   void onClose() {
//     clearCache();
//     super.onClose();
//   }
// }
