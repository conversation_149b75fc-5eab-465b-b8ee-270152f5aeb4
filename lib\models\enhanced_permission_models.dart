
/*
/// تعداد أنواع الصلاحيات المفصلة
enum PermissionType {
  view('عرض', 'view'),
  create('إضافة', 'create'),
  edit('تعديل', 'edit'),
  delete('حذف', 'delete'),
  approve('موافقة', 'approve'),
  export('تصدير', 'export'),
  import('استيراد', 'import'),
  admin('إدارة', 'admin'),
  assign('إسناد', 'assign'),
  transfer('نقل', 'transfer'),
  comment('تعليق', 'comment'),
  attach('إرفاق', 'attach'),
  viewAll('عرض الكل', 'viewAll'),
  viewOwn('عرض الخاص', 'viewOwn'),
  interfaceAccess('الوصول للواجهة', 'interfaceAccess'),
  download('تحميل', 'download'),
  upload('رفع', 'upload'),
  share('مشاركة', 'share'),
  print('طباعة', 'print'),
  ganttChart('مخطط جانت', 'ganttChart'),
  powerBI('تقارير BI', 'powerBI'),
  calendar('التقويم', 'calendar');

  const PermissionType(this.displayName, this.value);
  final String displayName;
  final String value;

  static PermissionType fromValue(String value) {
    return PermissionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PermissionType.view,
    );
  }
}
*/
/// تعداد مجالات الصلاحيات الشامل - يغطي جميع الواجهات والتبويبات في التطبيق
enum PermissionScope {
  // الواجهات الرئيسية
  dashboard('لوحة التحكم', 'dashboard'),
  tasks('المهام', 'tasks'),
  departments('الأقسام', 'departments'),
  messages('الرسائل والمحادثات', 'messages'),
  reports('التقارير', 'reports'),
  notifications('الإشعارات', 'notifications'),
  profile('الملف الشخصي', 'profile'),
  calendar('التقويم', 'calendar'),
  powerBI('تقارير Power BI', 'powerBI'),

  // الإدارة والمستخدمين
  admin('لوحة التحكم الإدارية', 'admin'),
  users('إدارة المستخدمين', 'users'),
  roles('إدارة الأدوار', 'roles'),
  permissions('إدارة الصلاحيات', 'permissions'),
  userDashboard('لوحة تحكم المستخدم', 'userDashboard'),

  // إدارة النظام
  settings('الإعدادات', 'settings'),
  systemSettings('إعدادات النظام', 'systemSettings'),
  syncSettings('إعدادات التزامن', 'syncSettings'),
  database('إدارة قاعدة البيانات', 'database'),
  backups('النسخ الاحتياطية', 'backups'),
  analytics('التحليلات', 'analytics'),
  logs('السجلات', 'logs'),

  // نظام الأرشفة الإلكترونية
  archive('الأرشيف الإلكتروني', 'archive'),
  documentBrowser('متصفح الوثائق', 'documentBrowser'),
  documentUpload('رفع الوثائق', 'documentUpload'),
  categoryManagement('إدارة التصنيفات', 'categoryManagement'),
  tagManagement('إدارة العلامات', 'tagManagement'),
  documentVersionHistory('تاريخ إصدارات الوثائق', 'documentVersionHistory'),
  editDocument('تحرير الوثائق', 'editDocument'),

  // المهام المتقدمة
  taskDetail('تفاصيل المهام', 'taskDetail'),
  createTask('إنشاء المهام', 'createTask'),
  taskBoard('لوحة المهام', 'taskBoard'),
  ganttChart('مخطط جانت', 'ganttChart'),
  taskStats('إحصائيات المهام', 'taskStats'),
  subtasks('المهام الفرعية', 'subtasks'),
  taskAttachments('مرفقات المهام', 'taskAttachments'),
  taskDocuments('مستندات المهام', 'taskDocuments'),
  taskProgress('تقدم المهام', 'taskProgress'),
  taskTransferHistory('تاريخ نقل المهام', 'taskTransferHistory'),
  contributorContributions('مساهمات المساهمين', 'contributorContributions'),

  // إدارة المهام
  taskPriorityManagement('إدارة أولويات المهام', 'taskPriorityManagement'),
  taskStatusManagement('إدارة حالات المهام', 'taskStatusManagement'),
  taskTypeManagement('إدارة أنواع المهام', 'taskTypeManagement'),

  // المحادثات والتواصل
  chatList('قائمة المحادثات', 'chatList'),
  chatDetail('تفاصيل المحادثة', 'chatDetail'),
  createGroupChat('إنشاء محادثة جماعية', 'createGroupChat'),
  addMembers('إضافة أعضاء', 'addMembers'),
  searchConversation('البحث في المحادثات', 'searchConversation'),
  muteNotifications('كتم الإشعارات', 'muteNotifications'),
  chatInfo('معلومات المحادثة', 'chatInfo'),
  groups('المجموعات', 'groups'),

  // التقارير المتقدمة
  reportingDashboard('لوحة التقارير', 'reportingDashboard'),
  reportBuilder('منشئ التقارير', 'reportBuilder'),
  reportViewer('عارض التقارير', 'reportViewer'),
  exportedReports('التقارير المصدرة', 'exportedReports'),
  mondayStyleReports('تقارير بنمط Monday', 'mondayStyleReports'),
  contributionReports('تقارير المساهمات', 'contributionReports'),
  customReportBuilder('منشئ التقارير المخصص', 'customReportBuilder'),
  enhancedCharts('الرسوم البيانية المحسنة', 'enhancedCharts'),
  staticReports('التقارير الثابتة', 'staticReports'),
  reportScheduler('جدولة التقارير', 'reportScheduler'),
  jsonExportSettings('إعدادات تصدير JSON', 'jsonExportSettings'),

  // لوحات المعلومات
  customizableDashboard('لوحة المعلومات القابلة للتخصيص', 'customizableDashboard'),
  newDashboard('لوحة المعلومات الجديدة', 'newDashboard'),
  mondayDashboard('لوحة معلومات Monday', 'mondayDashboard'),
  fullscreenChartView('عرض الرسم البياني بملء الشاشة', 'fullscreenChartView'),

  // Power BI
  createPowerBIReport('إنشاء تقرير Power BI', 'createPowerBIReport'),
  dynamicPowerBIReport('تقرير Power BI الديناميكي', 'dynamicPowerBIReport'),
  powerBIReportDetails('تفاصيل تقرير Power BI', 'powerBIReportDetails'),
  powerBIIntegration('تكامل Power BI', 'powerBIIntegration'),

  // التقويم
  calendarEventDetails('تفاصيل أحداث التقويم', 'calendarEventDetails'),
  calendarEventForm('نموذج أحداث التقويم', 'calendarEventForm'),

  // البحث
  unifiedSearch('البحث الموحد', 'unifiedSearch'),

  // المستندات النصية
  textDocumentsList('قائمة المستندات النصية', 'textDocumentsList'),
  textDocumentEditor('محرر المستندات النصية', 'textDocumentEditor'),
  advancedTextDocumentEditor('محرر المستندات النصية المتقدم', 'advancedTextDocumentEditor'),

  // الملفات
  fileViewer('عارض الملفات', 'fileViewer'),
  enhancedFileViewer('عارض الملفات المحسن', 'enhancedFileViewer'),

  // الإعدادات المتقدمة
  changePassword('تغيير كلمة المرور', 'changePassword'),
  editProfile('تحرير الملف الشخصي', 'editProfile'),
  languageSettings('إعدادات اللغة', 'languageSettings'),
  themeSettings('إعدادات المظهر', 'themeSettings'),
  helpSupport('المساعدة والدعم', 'helpSupport'),
  privacyPolicy('سياسة الخصوصية', 'privacyPolicy'),
  termsConditions('الشروط والأحكام', 'termsConditions'),

  // إصلاح النظام
  databaseRepair('إصلاح قاعدة البيانات', 'databaseRepair'),
  archiveSystemRepair('إصلاح نظام الأرشيف', 'archiveSystemRepair'),
  diagnostics('التشخيصات', 'diagnostics'),

  // الاختبار والتطوير
  permissionTest('اختبار الصلاحيات', 'permissionTest'),
  testUsers('اختبار المستخدمين', 'testUsers'),
  encodingTest('اختبار التشفير', 'encodingTest'),

  // عام
  interfaces('الواجهات', 'interfaces'),
  attachments('المرفقات', 'attachments'),
  files('الملفات', 'files');

  const PermissionScope(this.displayName, this.value);
  final String displayName;
  final String value;

  static PermissionScope fromValue(String value) {
    return PermissionScope.values.firstWhere(
      (scope) => scope.value == value,
      orElse: () => PermissionScope.tasks,
    );
  }
}

/*
/// نموذج الدور المخصص - يستخدم جدول permissions الموجود
class CustomRole extends Permission {
  /// إنشاء دور مخصص جديد
  factory CustomRole.create({
    required String name,
    String? description,
    String? category,
    String? color,
    String? icon,
    int level = 1,
  }) {
    return CustomRole._(
      id: DateTime.now().millisecondsSinceEpoch,
      name: name,
      description: description,
      permissionGroup: 'CustomRoles', // مجموعة خاصة للأدوار المخصصة
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      category: category,
      level: level,
      icon: icon,
      color: color,
      isDefault: false,
    );
  }

  const CustomRole._({
    required super.id,
    required super.name,
    super.description,
    required super.permissionGroup,
    required super.createdAt,
    super.updatedAt,
    super.category,
    required super.level,
    super.icon,
    super.color,
    super.isDefault,
    super.users,
  });

  /// إنشاء CustomRole من Permission
  factory CustomRole.fromPermission(Permission permission) {
    return CustomRole._(
      id: permission.id,
      name: permission.name,
      description: permission.description,
      permissionGroup: permission.permissionGroup,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
      category: permission.category,
      level: permission.level,
      icon: permission.icon,
      color: permission.color,
      isDefault: permission.isDefault,
      users: permission.users,
    );
  }

  /// التحقق من كون هذا دور مخصص
  bool get isCustomRole => permissionGroup == 'CustomRoles';
}

/// نموذج صلاحية الواجهة
class InterfacePermission {
  final String id;
  final String interfaceName;
  final String interfaceRoute;
  final String? description;
  final String? icon;
  final String? category;
  final bool isActive;
  final int createdAt;

  const InterfacePermission({
    required this.id,
    required this.interfaceName,
    required this.interfaceRoute,
    this.description,
    this.icon,
    this.category,
    this.isActive = true,
    required this.createdAt,
  });

  /// إنشاء صلاحية واجهة جديدة
  factory InterfacePermission.create({
    required String interfaceName,
    required String interfaceRoute,
    String? description,
    String? icon,
    String? category,
  }) {
    return InterfacePermission(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      interfaceName: interfaceName,
      interfaceRoute: interfaceRoute,
      description: description,
      icon: icon,
      category: category,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );
  }

  /// إنشاء InterfacePermission من JSON (من API)
  factory InterfacePermission.fromJson(Map<String, dynamic> json) {
    return InterfacePermission(
      id: json['id']?.toString() ?? '',
      interfaceName: json['interfaceName'] as String,
      interfaceRoute: json['interfaceRoute'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      category: json['category'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
    );
  }

  /// تحويل إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'interfaceName': interfaceName,
      'interfaceRoute': interfaceRoute,
      'description': description,
      'icon': icon,
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InterfacePermission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'InterfacePermission(id: $id, name: $interfaceName, route: $interfaceRoute)';
  }
}
*/
/// نموذج طلب إنشاء دور جديد
class CreateRoleRequest {
  final String name;
  final String? description;
  final String? category;
  final String? color;
  final String? icon;
  final int level;

  const CreateRoleRequest({
    required this.name,
    this.description,
    this.category,
    this.color,
    this.icon,
    this.level = 1,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'permissionGroup': 'CustomRoles',
      'category': category,
      'color': color,
      'icon': icon,
      'level': level,
      'isDefault': false,
    };
  }
}

/// نموذج طلب تحديث دور
class UpdateRoleRequest {
  final int id;
  final String name;
  final String? description;
  final String? category;
  final String? color;
  final String? icon;
  final int level;

  const UpdateRoleRequest({
    required this.id,
    required this.name,
    this.description,
    this.category,
    this.color,
    this.icon,
    this.level = 1,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissionGroup': 'CustomRoles',
      'category': category,
      'color': color,
      'icon': icon,
      'level': level,
    };
  }
}

/// نموذج طلب تحديث الصلاحيات
class UpdatePermissionsRequest {
  final int roleId;
  final List<int> permissionIds;

  const UpdatePermissionsRequest({
    required this.roleId,
    required this.permissionIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'roleId': roleId,
      'permissionIds': permissionIds,
    };
  }
}
