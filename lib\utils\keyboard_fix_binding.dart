import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// معالج مخصص لأحداث لوحة المفاتيح لإصلاح مشكلة تكرار أحداث المفاتيح
/// يستخدم هذا الملف لتجاوز السلوك الافتراضي لمعالجة أحداث لوحة المفاتيح في Flutter
class KeyboardFixBinding extends WidgetsFlutterBinding {
  /// مثيل واحد من الفئة (نمط Singleton)
  static KeyboardFixBinding? _instance;

  /// الحصول على المثيل الوحيد أو إنشاؤه إذا لم يكن موجودًا
  static KeyboardFixBinding get instance {
    _instance ??= KeyboardFixBinding();
    return _instance!;
  }

  /// تهيئة المثيل وتنظيف حالة لوحة المفاتيح
  @override
  void initInstances() {
    super.initInstances();

    // تنظيف حالة لوحة المفاتيح عند بدء التطبيق
    _clearKeyboardState();

    // تسجيل معالج مخصص لأحداث لوحة المفاتيح
    HardwareKeyboard.instance.addHandler(_handleKeyEvent);

    // إضافة معالج لتنظيف حالة لوحة المفاتيح بشكل دوري
    // هذا يساعد في تجنب مشاكل المفاتيح العالقة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupPeriodicCleanup();
    });
  }

  /// إعداد تنظيف دوري لحالة لوحة المفاتيح
  void _setupPeriodicCleanup() {
    // تنظيف حالة لوحة المفاتيح كل 2 ثوانٍ
    Future.delayed(const Duration(seconds: 2), () {
      // تنظيف حالة لوحة المفاتيح بصمت
      _forceResetKeyboardState();

      // جدولة التنظيف التالي
      _setupPeriodicCleanup();
    });
  }

  /// إعادة تعيين حالة لوحة المفاتيح بالقوة
  void _forceResetKeyboardState() {
    // استخدام طريقة بديلة لإعادة تعيين حالة لوحة المفاتيح
    // إلغاء تسجيل المعالج مؤقتًا
    HardwareKeyboard.instance.removeHandler(_handleKeyEvent);

    // إعادة تسجيل المعالج بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 100), () {
      HardwareKeyboard.instance.addHandler(_handleKeyEvent);
    });
  }

  /// تسجيل معالج مخصص لأحداث لوحة المفاتيح
  static void ensureInitialized() {
    KeyboardFixBinding.instance;
  }

  /// معالج مخصص لأحداث لوحة المفاتيح
  bool _handleKeyEvent(KeyEvent event) {
    // السماح بمعالجة جميع أحداث لوحة المفاتيح
    // لا نقوم بأي فحص أو تنظيف هنا لتجنب التداخل مع معالجة الأحداث الأساسية
    return false;
  }

  /// تنظيف حالة لوحة المفاتيح
  void _clearKeyboardState() {
    try {
      // طريقة بديلة لتنظيف حالة لوحة المفاتيح
      // إعادة تعيين معالج أحداث المفاتيح
      HardwareKeyboard.instance.removeHandler(_handleKeyEvent);

      // إعادة تسجيل المعالج بعد فترة قصيرة
      Future.delayed(const Duration(milliseconds: 50), () {
        HardwareKeyboard.instance.addHandler(_handleKeyEvent);
      });
    } catch (e) {
      // تجاهل الأخطاء
    }
  }
}
