import 'package:flutter/material.dart';

import '../../constants/app_styles.dart';

/// شريط أدوات التقرير
///
/// يحتوي على أزرار الإجراءات المختلفة للتقرير
class ReportToolbar extends StatelessWidget {
  /// حدث تصدير PDF
  final VoidCallback? onExportPdf;

  /// حدث تصدير Excel
  final VoidCallback? onExportExcel;

  /// حدث تصدير CSV
  final VoidCallback? onExportCsv;

  /// حدث تصدير JSON
  final VoidCallback? onExportJson;

  /// حدث المشاركة
  final VoidCallback? onShare;

  /// حدث الطباعة
  final VoidCallback? onPrint;

  /// حدث التحديث
  final VoidCallback? onRefresh;

  /// حدث الإعدادات
  final VoidCallback? onSettings;

  /// حدث الجدولة
  final VoidCallback? onSchedule;

  /// حدث إضافة إلى المفضلة
  final VoidCallback? onAddToFavorites;

  /// هل التقرير في المفضلة
  final bool isFavorite;

  /// هل يتم التحميل
  final bool isLoading;

  const ReportToolbar({
    super.key,
    this.onExportPdf,
    this.onExportExcel,
    this.onExportCsv,
    this.onExportJson,
    this.onShare,
    this.onPrint,
    this.onRefresh,
    this.onSettings,
    this.onSchedule,
    this.onAddToFavorites,
    this.isFavorite = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          // مجموعة التصدير
          _buildToolbarGroup([
            _buildToolbarButton(
              icon: Icons.picture_as_pdf,
              label: 'PDF',
              onPressed: onExportPdf,
              color: Colors.red,
            ),
            _buildToolbarButton(
              icon: Icons.table_chart,
              label: 'Excel',
              onPressed: onExportExcel,
              color: Colors.green,
            ),
            _buildToolbarButton(
              icon: Icons.format_list_bulleted,
              label: 'CSV',
              onPressed: onExportCsv,
              color: Colors.blue,
            ),
            _buildToolbarButton(
              icon: Icons.code,
              label: 'JSON',
              onPressed: onExportJson,
              color: Colors.purple,
            ),
          ]),

          const SizedBox(width: 16),

          // مجموعة الإجراءات
          _buildToolbarGroup([
            _buildToolbarButton(
              icon: Icons.share,
              label: 'مشاركة',
              onPressed: onShare,
              color: Colors.orange,
            ),
            _buildToolbarButton(
              icon: Icons.print,
              label: 'طباعة',
              onPressed: onPrint,
              color: Colors.indigo,
            ),
            _buildToolbarButton(
              icon: isLoading ? Icons.hourglass_empty : Icons.refresh,
              label: 'تحديث',
              onPressed: isLoading ? null : onRefresh,
              color: Colors.teal,
            ),
          ]),

          const Spacer(),

          // مجموعة الإعدادات
          _buildToolbarGroup([
            _buildToolbarButton(
              icon: isFavorite ? Icons.star : Icons.star_border,
              label: 'مفضلة',
              onPressed: onAddToFavorites,
              color: isFavorite ? Colors.amber : Colors.grey,
            ),
            _buildToolbarButton(
              icon: Icons.schedule,
              label: 'جدولة',
              onPressed: onSchedule,
              color: Colors.deepPurple,
            ),
            _buildToolbarButton(
              icon: Icons.settings,
              label: 'إعدادات',
              onPressed: onSettings,
              color: Colors.grey[700]!,
            ),
          ]),
        ],
      ),
    );
  }

  /// بناء مجموعة أزرار
  Widget _buildToolbarGroup(List<Widget> buttons) {
    return Row(
      children: buttons
          .expand((button) => [button, const SizedBox(width: 8)])
          .take(buttons.length * 2 - 1)
          .toList(),
    );
  }

  /// بناء زر شريط الأدوات
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: onPressed != null ? color.withValues(alpha: 0.1) : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: onPressed != null ? color.withValues(alpha: 0.3) : Colors.grey[400]!,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: onPressed != null ? color : Colors.grey[500],
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppStyles.bodySmall.copyWith(
                  color: onPressed != null ? color : Colors.grey[500],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// شريط أدوات مضغوط للتقرير
class CompactReportToolbar extends StatelessWidget {
  /// حدث التصدير
  final VoidCallback? onExport;

  /// حدث المشاركة
  final VoidCallback? onShare;

  /// حدث التحديث
  final VoidCallback? onRefresh;

  /// حدث المزيد من الخيارات
  final VoidCallback? onMoreOptions;

  /// هل يتم التحميل
  final bool isLoading;

  const CompactReportToolbar({
    super.key,
    this.onExport,
    this.onShare,
    this.onRefresh,
    this.onMoreOptions,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          // زر التصدير
          _buildCompactButton(
            icon: Icons.download,
            label: 'تصدير',
            onPressed: onExport,
          ),
          const SizedBox(width: 8),
          // زر المشاركة
          _buildCompactButton(
            icon: Icons.share,
            label: 'مشاركة',
            onPressed: onShare,
          ),
          const SizedBox(width: 8),
          // زر التحديث
          _buildCompactButton(
            icon: isLoading ? Icons.hourglass_empty : Icons.refresh,
            label: 'تحديث',
            onPressed: isLoading ? null : onRefresh,
          ),
          const Spacer(),
          // زر المزيد
          _buildCompactButton(
            icon: Icons.more_vert,
            label: 'المزيد',
            onPressed: onMoreOptions,
          ),
        ],
      ),
    );
  }

  /// بناء زر مضغوط
  Widget _buildCompactButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return Tooltip(
      message: label,
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        color: onPressed != null ? Colors.grey[700] : Colors.grey[400],
      ),
    );
  }
}
