import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../services/unified_permission_service.dart';
import '../controllers/auth_controller.dart';

/// مساعد تشخيص الصلاحيات
class PermissionDebugHelper {
  static final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  static final AuthController _authController = Get.find<AuthController>();

  /// طباعة جميع صلاحيات المستخدم الحالي
  static void printAllUserPermissions() {
    debugPrint('🔍 === تشخيص صلاحيات المستخدم ===');
    
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      debugPrint('❌ لا يوجد مستخدم مسجل دخول');
      return;
    }

    debugPrint('👤 المستخدم: ${currentUser.name} (ID: ${currentUser.id})');
    debugPrint('🎭 الدور: ${currentUser.role}');
    
    final userPermissions = _permissionService.userPermissions;
    debugPrint('📊 إجمالي الصلاحيات المحملة: ${userPermissions.length}');
    
    if (userPermissions.isEmpty) {
      debugPrint('⚠️ لا توجد صلاحيات محملة للمستخدم!');
      return;
    }

    debugPrint('📋 قائمة الصلاحيات:');
    userPermissions.forEach((permission, isActive) {
      final status = isActive ? '✅' : '❌';
      debugPrint('  $status $permission');
    });
  }

  /// فحص صلاحيات المهام تحديداً
  static void checkTaskPermissions() {
    debugPrint('🔍 === فحص صلاحيات المهام ===');

    final taskPermissions = [
      'tasks.view',
      'tasks.view_all',
      'tasks.create',
      'tasks.edit',
      'tasks.delete',
      'tasks.assign',
      'tasks.update_own',
      'tasks.transfer',
      'tasks.duplicate',
      'tasks.archive',
      'tasks.restore',
      'tasks.export',
      'tasks.import',
      'tasks.bulk_edit',
      'tasks.bulk_delete',
      'tasks.gantt_view',
      'tasks.board_view',
      'tasks.timeline_view',
      'tasks.view_details',
      'tasks.update_progress',
      'tasks.filter',
      'tasks.sort',
      'tasks.manage_board',
      'tasks.change_status',
      'tasks.change_priority',
      'tasks.refresh',
      'tasks.view_workload_report',
    ];

    for (final permission in taskPermissions) {
      final hasPermission = _permissionService.hasPermission(permission);
      final status = hasPermission ? '✅' : '❌';
      debugPrint('  $status $permission');
    }

    debugPrint('🎯 === نتائج الفحص الوظيفي ===');
    debugPrint('  canAccessTasks(): ${_permissionService.canAccessTasks()}');
    debugPrint('  canViewAllTasks(): ${_permissionService.canViewAllTasks()}');
    debugPrint('  canCreateTask(): ${_permissionService.canCreateTask()}');
    debugPrint('  canEditTask(): ${_permissionService.canEditTask()}');
    debugPrint('  canDeleteTask(): ${_permissionService.canDeleteTask()}');
  }

  /// فحص تفصيلي لصلاحية tasks.view_all
  static void deepCheckViewAllTasks() {
    debugPrint('🔍 === فحص تفصيلي لصلاحية tasks.view_all ===');

    final userPermissions = _permissionService.userPermissions;

    // فحص وجود الصلاحية في القاموس
    final hasInMap = userPermissions.containsKey('tasks.view_all');
    final valueInMap = userPermissions['tasks.view_all'];

    debugPrint('📋 تفاصيل الصلاحية:');
    debugPrint('  موجودة في القاموس: $hasInMap');
    debugPrint('  القيمة في القاموس: $valueInMap');
    debugPrint('  نوع القيمة: ${valueInMap.runtimeType}');

    // فحص الدالة المباشرة
    final directCheck = _permissionService.hasPermission('tasks.view_all');
    debugPrint('  hasPermission("tasks.view_all"): $directCheck');

    // فحص الدالة المركبة
    final functionCheck = _permissionService.canViewAllTasks();
    debugPrint('  canViewAllTasks(): $functionCheck');

    // البحث عن صلاحيات مشابهة
    debugPrint('📋 صلاحيات مشابهة:');
    userPermissions.forEach((key, value) {
      if (key.contains('view_all') || key.contains('tasks')) {
        debugPrint('  $key: $value');
      }
    });

    // فحص حساسية الأحرف
    final variations = [
      'tasks.view_all',
      'Tasks.view_all',
      'TASKS.VIEW_ALL',
      'tasks.VIEW_ALL',
      'tasks.view_ALL',
    ];

    debugPrint('📋 فحص حساسية الأحرف:');
    for (final variation in variations) {
      final exists = userPermissions.containsKey(variation);
      debugPrint('  "$variation": $exists');
    }
  }

  /// فحص حالة تحميل الصلاحيات
  static void checkPermissionLoadingStatus() {
    debugPrint('🔍 === فحص حالة تحميل الصلاحيات ===');
    
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      debugPrint('❌ لا يوجد مستخدم مسجل دخول');
      return;
    }

    final userPermissions = _permissionService.userPermissions;
    final allPermissions = _permissionService.permissions;
    
    debugPrint('👤 المستخدم: ${currentUser.name}');
    debugPrint('📊 الصلاحيات المحملة للمستخدم: ${userPermissions.length}');
    debugPrint('📊 إجمالي الصلاحيات في النظام: ${allPermissions.length}');
    
    if (userPermissions.isEmpty) {
      debugPrint('⚠️ المشكلة: لا توجد صلاحيات محملة للمستخدم!');
      debugPrint('💡 الحلول المقترحة:');
      debugPrint('   1. تحقق من تحميل صلاحيات المستخدم في AuthController');
      debugPrint('   2. تحقق من صلاحيات الدور في قاعدة البيانات');
      debugPrint('   3. تحقق من اتصال API');
    }
  }

  /// إعادة تحميل صلاحيات المستخدم
  static Future<void> reloadUserPermissions() async {
    debugPrint('🔄 إعادة تحميل صلاحيات المستخدم...');
    
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      debugPrint('❌ لا يوجد مستخدم مسجل دخول');
      return;
    }

    try {
      await _permissionService.loadUserPermissions(currentUser.id);
      debugPrint('✅ تم إعادة تحميل الصلاحيات بنجاح');
      
      // طباعة النتائج الجديدة
      checkTaskPermissions();
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل الصلاحيات: $e');
    }
  }

  /// فحص شامل للمشكلة
  static Future<void> fullDiagnostic() async {
    debugPrint('🚀 === بدء التشخيص الشامل للصلاحيات ===');

    checkPermissionLoadingStatus();
    printAllUserPermissions();
    checkTaskPermissions();

    debugPrint('🔄 إعادة تحميل الصلاحيات...');
    await reloadUserPermissions();

    debugPrint('✅ === انتهاء التشخيص الشامل ===');
  }

  /// إصلاح صلاحيات المهام المفقودة
  static Future<void> fixMissingTaskPermissions() async {
    debugPrint('🔧 === إصلاح صلاحيات المهام المفقودة ===');

    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      debugPrint('❌ لا يوجد مستخدم مسجل دخول');
      return;
    }

    try {
      // قائمة الصلاحيات المفقودة المهمة
      final missingPermissions = <String>[];

      // فحص الصلاحيات المهمة
      if (!_permissionService.hasPermission('tasks.view_all')) {
        missingPermissions.add('tasks.view_all');
      }

      if (missingPermissions.isNotEmpty) {
        debugPrint('🔍 تم العثور على ${missingPermissions.length} صلاحية مفقودة:');
        for (final permission in missingPermissions) {
          debugPrint('  ❌ $permission');
        }

        debugPrint('💡 لإصلاح هذه المشكلة:');
        debugPrint('   1. شغّل ملف SQL: fix_tasks_view_all_permission.sql');
        debugPrint('   2. أو اطلب من المدير إضافة هذه الصلاحيات');
        debugPrint('   3. ثم أعد تحميل الصلاحيات من القائمة الجانبية');
      } else {
        debugPrint('✅ جميع صلاحيات المهام المهمة موجودة');
      }

    } catch (e) {
      debugPrint('❌ خطأ في فحص الصلاحيات المفقودة: $e');
    }
  }

  /// تشخيص مشكلة القائمة الجانبية
  static void diagnoseDrawerIssue() {
    debugPrint('🔍 === تشخيص مشكلة القائمة الجانبية ===');

    final canAccess = _permissionService.canAccessTasks();
    final canViewAll = _permissionService.canViewAllTasks();

    debugPrint('📋 نتائج التشخيص:');
    debugPrint('  canAccessTasks(): $canAccess');
    debugPrint('  canViewAllTasks(): $canViewAll');

    if (canAccess && !canViewAll) {
      debugPrint('🎯 المشكلة: لديك صلاحية الوصول للمهام ولكن ليس لديك صلاحية عرض جميع المهام');
      debugPrint('💡 الحل: تم تحديث القائمة الجانبية لتظهر المهام حتى بدون صلاحية view_all');
      debugPrint('🔧 لإضافة صلاحية view_all: شغّل ملف fix_tasks_view_all_permission.sql');
    } else if (!canAccess) {
      debugPrint('❌ المشكلة: ليس لديك صلاحية الوصول للمهام أساساً');
      debugPrint('💡 الحل: اطلب من المدير إضافة صلاحية tasks.view');
    } else {
      debugPrint('✅ لا توجد مشكلة - يجب أن تظهر المهام في القائمة الجانبية');
    }
  }

  /// اختبار الإصلاح الجديد
  static void testPermissionFix() {
    debugPrint('🧪 === اختبار إصلاح الصلاحيات ===');

    final canAccess = _permissionService.canAccessTasks();
    final canViewAll = _permissionService.canViewAllTasks();

    debugPrint('📋 نتائج الاختبار بعد الإصلاح:');
    debugPrint('  canAccessTasks(): $canAccess');
    debugPrint('  canViewAllTasks(): $canViewAll');

    // اختبار مباشر للصلاحيات
    final directViewAll = _permissionService.hasPermission('tasks.view_all');
    final directViewAllWithSpace = _permissionService.hasPermission('tasks.view_all ');

    debugPrint('📋 اختبار مباشر:');
    debugPrint('  hasPermission("tasks.view_all"): $directViewAll');
    debugPrint('  hasPermission("tasks.view_all "): $directViewAllWithSpace');

    if (canAccess && canViewAll) {
      debugPrint('🎉 تم إصلاح المشكلة! المهام يجب أن تظهر الآن في القائمة الجانبية');
    } else {
      debugPrint('⚠️ المشكلة لا تزال موجودة. قد تحتاج لإعادة تحميل الصلاحيات');
    }
  }

  /// فحص شامل لجميع الصلاحيات للبحث عن مشاكل مماثلة
  static void scanAllPermissionsForIssues() {
    debugPrint('🔍 === فحص شامل لجميع الصلاحيات ===');

    final userPermissions = _permissionService.userPermissions;
    final issues = <String, List<String>>{};

    debugPrint('📊 إجمالي الصلاحيات: ${userPermissions.length}');

    // فحص المشاكل المختلفة
    final permissionsWithSpaces = <String>[];
    final permissionsWithSpecialChars = <String>[];
    final permissionsWithCaseIssues = <String>[];
    final inactivePermissions = <String>[];

    for (final entry in userPermissions.entries) {
      final permissionName = entry.key;
      final isActive = entry.value;

      // فحص المسافات الإضافية
      if (permissionName != permissionName.trim()) {
        permissionsWithSpaces.add(permissionName);
      }

      // فحص الأحرف الخاصة غير المتوقعة
      if (permissionName.contains(RegExp(r'[^\w\.\-_]'))) {
        permissionsWithSpecialChars.add(permissionName);
      }

      // فحص الصلاحيات غير المفعلة
      if (!isActive) {
        inactivePermissions.add(permissionName);
      }

      // فحص مشاكل الأحرف الكبيرة/الصغيرة
      if (permissionName != permissionName.toLowerCase()) {
        permissionsWithCaseIssues.add(permissionName);
      }
    }

    // طباعة النتائج
    debugPrint('🔍 === نتائج الفحص ===');

    if (permissionsWithSpaces.isNotEmpty) {
      debugPrint('⚠️ صلاحيات تحتوي على مسافات (${permissionsWithSpaces.length}):');
      for (final permission in permissionsWithSpaces) {
        debugPrint('  "$permission" (الطول: ${permission.length})');
      }
      issues['مسافات إضافية'] = permissionsWithSpaces;
    }

    if (permissionsWithSpecialChars.isNotEmpty) {
      debugPrint('⚠️ صلاحيات تحتوي على أحرف خاصة (${permissionsWithSpecialChars.length}):');
      for (final permission in permissionsWithSpecialChars) {
        debugPrint('  "$permission"');
      }
      issues['أحرف خاصة'] = permissionsWithSpecialChars;
    }

    if (permissionsWithCaseIssues.isNotEmpty) {
      debugPrint('⚠️ صلاحيات تحتوي على أحرف كبيرة (${permissionsWithCaseIssues.length}):');
      for (final permission in permissionsWithCaseIssues) {
        debugPrint('  "$permission"');
      }
      issues['أحرف كبيرة'] = permissionsWithCaseIssues;
    }

    if (inactivePermissions.isNotEmpty) {
      debugPrint('⚠️ صلاحيات غير مفعلة (${inactivePermissions.length}):');
      for (final permission in inactivePermissions) {
        debugPrint('  "$permission"');
      }
      issues['غير مفعلة'] = inactivePermissions;
    }

    if (issues.isEmpty) {
      debugPrint('✅ لم يتم العثور على مشاكل في الصلاحيات');
    } else {
      debugPrint('📋 ملخص المشاكل المكتشفة:');
      issues.forEach((type, permissions) {
        debugPrint('  $type: ${permissions.length} صلاحية');
      });

      debugPrint('💡 توصيات الإصلاح:');
      if (issues.containsKey('مسافات إضافية')) {
        debugPrint('  - شغّل ملف fix_permission_names_spaces.sql لإزالة المسافات');
      }
      if (issues.containsKey('أحرف خاصة')) {
        debugPrint('  - راجع أسماء الصلاحيات في قاعدة البيانات');
      }
      if (issues.containsKey('أحرف كبيرة')) {
        debugPrint('  - وحّد استخدام الأحرف الصغيرة في أسماء الصلاحيات');
      }
      if (issues.containsKey('غير مفعلة')) {
        debugPrint('  - فعّل الصلاحيات المطلوبة أو احذف غير المستخدمة');
      }
    }
  }

  /// فحص الصلاحيات المتوقعة مقابل الموجودة
  static void checkExpectedPermissions() {
    debugPrint('🔍 === فحص الصلاحيات المتوقعة ===');

    // قائمة الصلاحيات المتوقعة للمهام
    final expectedTaskPermissions = [
      'tasks.view',
      'tasks.view_all',
      'tasks.create',
      'tasks.edit',
      'tasks.delete',
      'tasks.assign',
      'tasks.update_own',
      'tasks.transfer',
      'tasks.duplicate',
      'tasks.archive',
      'tasks.restore',
      'tasks.export',
      'tasks.import',
      'tasks.bulk_edit',
      'tasks.bulk_delete',
      'tasks.gantt_view',
      'tasks.board_view',
      'tasks.timeline_view',
      'tasks.view_details',
      'tasks.update_progress',
      'tasks.filter',
      'tasks.sort',
      'tasks.manage_board',
      'tasks.change_status',
      'tasks.change_priority',
      'tasks.refresh',
      'tasks.view_workload_report',
    ];

    final userPermissions = _permissionService.userPermissions;
    final missingPermissions = <String>[];
    final extraPermissions = <String>[];

    // فحص الصلاحيات المفقودة
    for (final expected in expectedTaskPermissions) {
      if (!userPermissions.containsKey(expected)) {
        // فحص إذا كانت موجودة مع مسافة
        final withSpace = userPermissions.containsKey('$expected ');
        if (!withSpace) {
          missingPermissions.add(expected);
        }
      }
    }

    // فحص الصلاحيات الإضافية للمهام
    for (final permission in userPermissions.keys) {
      if (permission.startsWith('tasks.') &&
          !expectedTaskPermissions.contains(permission.trim())) {
        extraPermissions.add(permission);
      }
    }

    debugPrint('📋 نتائج فحص صلاحيات المهام:');
    debugPrint('  المتوقعة: ${expectedTaskPermissions.length}');
    debugPrint('  الموجودة: ${userPermissions.keys.where((k) => k.startsWith('tasks.')).length}');

    if (missingPermissions.isNotEmpty) {
      debugPrint('❌ صلاحيات مفقودة (${missingPermissions.length}):');
      for (final permission in missingPermissions) {
        debugPrint('  - $permission');
      }
    }

    if (extraPermissions.isNotEmpty) {
      debugPrint('➕ صلاحيات إضافية (${extraPermissions.length}):');
      for (final permission in extraPermissions) {
        debugPrint('  + $permission');
      }
    }

    if (missingPermissions.isEmpty && extraPermissions.isEmpty) {
      debugPrint('✅ جميع صلاحيات المهام متطابقة مع المتوقع');
    }
  }

  /// فحص شامل لجميع المشاكل
  static Future<void> comprehensivePermissionScan() async {
    debugPrint('🚀 === بدء الفحص الشامل للصلاحيات ===');

    // فحص حالة التحميل
    checkPermissionLoadingStatus();

    // فحص المشاكل العامة
    scanAllPermissionsForIssues();

    // فحص الصلاحيات المتوقعة
    checkExpectedPermissions();

    // فحص صلاحيات المهام
    checkTaskPermissions();

    // فحص مشكلة القائمة الجانبية
    diagnoseDrawerIssue();

    debugPrint('✅ === انتهاء الفحص الشامل ===');
  }
}
