import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../../constants/app_colors.dart';
import '../../services/unified_permission_service.dart';

/// عارض PDF موحد ومحسن
/// يجمع جميع الميزات المطلوبة في مكون واحد
class UnifiedPdfViewer extends StatefulWidget {
  /// مسار ملف PDF (للملفات المحلية)
  final String? filePath;
  
  /// بيانات PDF (للملفات في الذاكرة)
  final Uint8List? pdfData;
  
  /// اسم الملف
  final String fileName;
  
  /// عنوان الملف (اختياري)
  final String? title;
  
  /// إظهار شريط الأدوات
  final bool showToolbar;
  
  /// إظهار زر الطباعة
  final bool showPrintButton;
  
  /// إظهار زر المشاركة
  final bool showShareButton;
  
  /// إظهار زر الحفظ
  final bool showSaveButton;
  
  /// دالة مخصصة عند الطباعة
  final VoidCallback? onPrint;

  const UnifiedPdfViewer({
    super.key,
    this.filePath,
    this.pdfData,
    required this.fileName,
    this.title,
    this.showToolbar = true,
    this.showPrintButton = true,
    this.showShareButton = true,
    this.showSaveButton = true,
    this.onPrint,
  }) : assert(filePath != null || pdfData != null, 'يجب توفير إما filePath أو pdfData');

  @override
  State<UnifiedPdfViewer> createState() => _UnifiedPdfViewerState();
}

class _UnifiedPdfViewerState extends State<UnifiedPdfViewer> {
  late PdfViewerController _pdfViewerController;
  final TextEditingController _searchController = TextEditingController();
  
  bool _isLoading = true;
  bool _isFullScreen = false;
  bool _isSearching = false;
  bool _showThumbnails = false;
  String? _errorMessage;
  
  int _currentPageNumber = 1;
  int _totalPages = 0;
  double _currentZoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
    _initializeViewer();
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تهيئة العارض
  Future<void> _initializeViewer() async {
    try {
      debugPrint('🔄 بدء تهيئة العارض الموحد...');

      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // التحقق من وجود الملف إذا كان مسار ملف
      if (widget.filePath != null) {
        debugPrint('📁 فحص وجود الملف: ${widget.filePath}');
        final file = File(widget.filePath!);
        if (!await file.exists()) {
          debugPrint('❌ الملف غير موجود: ${widget.filePath}');
          setState(() {
            _isLoading = false;
            _errorMessage = 'الملف غير موجود: ${widget.filePath}';
          });
          return;
        }
        debugPrint('✅ الملف موجود');
      } else if (widget.pdfData != null) {
        debugPrint('💾 استخدام بيانات PDF من الذاكرة - الحجم: ${widget.pdfData!.length} بايت');
      }

      setState(() {
        _isLoading = false;
      });
      debugPrint('✅ تم تهيئة العارض بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة العارض: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تهيئة العارض: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.showToolbar ? _buildAppBar() : null,
      body: _buildBody(),
      bottomNavigationBar: widget.showToolbar ? _buildBottomBar() : null,
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.title ?? widget.fileName),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      actions: [
        if (_isSearching) ...[
          // شريط البحث
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'البحث في PDF...',
                  hintStyle: TextStyle(color: Colors.white70),
                  border: InputBorder.none,
                ),
                onChanged: _searchPDF,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.navigate_before),
            onPressed: _findPreviousOccurrence,
            tooltip: 'السابق',
          ),
          IconButton(
            icon: const Icon(Icons.navigate_next),
            onPressed: _findNextOccurrence,
            tooltip: 'التالي',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _closeSearch,
            tooltip: 'إغلاق البحث',
          ),
        ] else ...[
          // أزرار الأدوات العادية
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => setState(() => _isSearching = true),
            tooltip: 'بحث',
          ),
          if (widget.showShareButton && Get.find<UnifiedPermissionService>().canShareFiles())
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareFile,
              tooltip: 'مشاركة',
            ),
          if (widget.showPrintButton && Get.find<UnifiedPermissionService>().canPrintDocuments())
            IconButton(
              icon: const Icon(Icons.print),
              onPressed: widget.onPrint ?? _printFile,
              tooltip: 'طباعة',
            ),
          if (widget.showSaveButton && Get.find<UnifiedPermissionService>().canDownloadFiles())
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: _saveFile,
              tooltip: 'حفظ',
            ),
          IconButton(
            icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
            onPressed: _toggleFullScreen,
            tooltip: _isFullScreen ? 'إنهاء ملء الشاشة' : 'ملء الشاشة',
          ),
        ],
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل PDF...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeViewer,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return _buildPdfViewer();
  }

  /// بناء عارض PDF
  Widget _buildPdfViewer() {
    Widget pdfViewer;

    if (widget.pdfData != null) {
      // عرض من البيانات في الذاكرة
      pdfViewer = SfPdfViewer.memory(
        widget.pdfData!,
        controller: _pdfViewerController,
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: false,
        canShowScrollStatus: false,
        canShowPaginationDialog: true,
        onDocumentLoaded: _onDocumentLoaded,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      // عرض من ملف محلي
      pdfViewer = SfPdfViewer.file(
        File(widget.filePath!),
        controller: _pdfViewerController,
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: false,
        canShowScrollStatus: false,
        canShowPaginationDialog: true,
        onDocumentLoaded: _onDocumentLoaded,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
        onDocumentLoadFailed: (details) {
          setState(() {
            _errorMessage = 'فشل تحميل ملف PDF: ${details.error}';
          });
        },
      );
    }

    return Container(
      color: Colors.grey.shade200,
      child: pdfViewer,
    );
  }

  /// بناء الشريط السفلي
  Widget? _buildBottomBar() {
    if (_isLoading || _errorMessage != null) return null;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // معلومات الصفحة
          Text(
            'صفحة $_currentPageNumber من $_totalPages',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          
          // أزرار التنقل
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.first_page, color: Colors.white),
                onPressed: _currentPageNumber > 1 ? () => _pdfViewerController.firstPage() : null,
                tooltip: 'الصفحة الأولى',
              ),
              IconButton(
                icon: const Icon(Icons.navigate_before, color: Colors.white),
                onPressed: _currentPageNumber > 1 ? () => _pdfViewerController.previousPage() : null,
                tooltip: 'الصفحة السابقة',
              ),
              IconButton(
                icon: const Icon(Icons.navigate_next, color: Colors.white),
                onPressed: _currentPageNumber < _totalPages ? () => _pdfViewerController.nextPage() : null,
                tooltip: 'الصفحة التالية',
              ),
              IconButton(
                icon: const Icon(Icons.last_page, color: Colors.white),
                onPressed: _currentPageNumber < _totalPages ? () => _pdfViewerController.lastPage() : null,
                tooltip: 'الصفحة الأخيرة',
              ),
            ],
          ),
          
          // معلومات التكبير
          Text(
            '${(_currentZoomLevel * 100).toInt()}%',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  // ==================== دوال الأحداث ====================

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
    });
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPageNumber = details.newPageNumber;
    });
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _currentZoomLevel = details.newZoomLevel;
    });
  }

  // ==================== دوال البحث ====================

  void _searchPDF(String text) {
    if (text.isEmpty) {
      setState(() => _isSearching = false);
      return;
    }
    _pdfViewerController.searchText(text);
  }

  void _findNextOccurrence() {
    _pdfViewerController.searchText(_searchController.text).nextInstance();
  }

  void _findPreviousOccurrence() {
    _pdfViewerController.searchText(_searchController.text).previousInstance();
  }

  void _closeSearch() {
    _searchController.clear();
    _pdfViewerController.clearSelection();
    setState(() => _isSearching = false);
  }

  // ==================== دوال الأدوات ====================

  void _toggleFullScreen() {
    setState(() => _isFullScreen = !_isFullScreen);
    // يمكن إضافة منطق ملء الشاشة هنا
  }

  /// طباعة الملف
  Future<void> _printFile() async {
    try {
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحضير الطباعة...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      Uint8List? data;
      if (widget.pdfData != null) {
        data = widget.pdfData!;
      } else {
        data = await File(widget.filePath!).readAsBytes();
      }

      await Printing.layoutPdf(
        onLayout: (format) async => data!,
        name: widget.fileName,
      );

      if (Get.isDialogOpen ?? false) Get.back();

      Get.snackbar(
        'نجح',
        'تم إرسال الملف للطباعة',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check, color: Colors.white),
      );
    } catch (e) {
      if (Get.isDialogOpen ?? false) Get.back();
      
      Get.snackbar(
        'خطأ',
        'فشل في طباعة الملف: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// مشاركة الملف
  Future<void> _shareFile() async {
    try {
      if (widget.filePath != null) {
        await Share.shareXFiles([XFile(widget.filePath!)], text: widget.fileName);
      } else if (widget.pdfData != null) {
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/${widget.fileName}');
        await tempFile.writeAsBytes(widget.pdfData!);
        await Share.shareXFiles([XFile(tempFile.path)], text: widget.fileName);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الملف: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// حفظ الملف
  Future<void> _saveFile() async {
    try {
      final downloadsDir = await getDownloadsDirectory();
      if (downloadsDir == null) {
        throw 'لا يمكن الوصول إلى مجلد التحميلات';
      }

      final fileName = widget.fileName.endsWith('.pdf') ? widget.fileName : '${widget.fileName}.pdf';
      final savePath = '${downloadsDir.path}/$fileName';

      if (widget.pdfData != null) {
        await File(savePath).writeAsBytes(widget.pdfData!);
      } else {
        await File(widget.filePath!).copy(savePath);
      }

      Get.snackbar(
        'نجح',
        'تم حفظ الملف في: $savePath',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check, color: Colors.white),
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حفظ الملف: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
