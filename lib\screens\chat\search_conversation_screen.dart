import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chat_group_models.dart';
import 'package:flutter_application_2/models/message_models.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/messages_controller.dart';
import '../../utils/date_formatter.dart';

class SearchConversationScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const SearchConversationScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<SearchConversationScreen> createState() => _SearchConversationScreenState();
}

class _SearchConversationScreenState extends State<SearchConversationScreen> {
  final _messagesController = Get.find<MessagesController>();
  final _authController = Get.find<AuthController>();

  final _searchController = TextEditingController();
  final _searchResults = <Message>[].obs;
  final _isSearching = false.obs;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.isEmpty) {
      _searchResults.clear();
      return;
    }

    _isSearching.value = true;
    try {
      final results = _messagesController.allMessages
          .where((message) =>
              message.content.toLowerCase().contains(query.toLowerCase()) &&
              !message.isDeleted)
          .toList();

      _searchResults.value = results;
    } finally {
      _isSearching.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Search in conversation...',
            border: InputBorder.none,
            hintStyle: TextStyle(color: Colors.white70),
          ),
          style: const TextStyle(color: Colors.white),
          autofocus: true,
          onChanged: _performSearch,
        ),
        actions: [
          Obx(() => _isSearching.value
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _searchResults.clear();
                  },
                )),
        ],
      ),
      body: Obx(() {
        if (_searchResults.isEmpty && _searchController.text.isNotEmpty) {
          return const Center(
            child: Text('No results found'),
          );
        }

        return ListView.builder(
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            final message = _searchResults[index];
            final isCurrentUser = message.senderId == _authController.currentUser.value?.id;

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: isCurrentUser ? AppColors.primary : AppColors.accent,
                child: Text(
                  'U',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(
                'User ${message.senderId}',
                style: AppStyles.titleSmall,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(message.createdAt * 1000)),
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              onTap: () {
                // Close the search screen and return to the chat with the message ID
                Get.back(result: message.id);
              },
            );
          },
        );
      }),
    );
  }
}
