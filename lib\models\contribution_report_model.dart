import 'package:json_annotation/json_annotation.dart';

part 'contribution_report_model.g.dart';

/// نموذج تقرير المساهمات
/// متوافق مع ASP.NET Core API
@JsonSerializable()
class ContributionReport {
  /// معرف التقرير
  final String id;
  
  /// عنوان التقرير
  final String title;
  
  /// وصف التقرير
  final String? description;
  
  /// معرف المهمة (اختياري)
  final String? taskId;
  
  /// معرف المستخدم (اختياري)
  final String? userId;
  
  /// معرف القسم (اختياري)
  final String? departmentId;
  
  /// تاريخ البداية
  final DateTime? startDate;
  
  /// تاريخ النهاية
  final DateTime? endDate;
  
  /// عدد الأيام للفترة
  final int? periodDays;
  
  /// معرف منشئ التقرير
  final String createdById;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// حالة الحذف
  final bool isDeleted;
  
  /// بيانات التقرير (JSON)
  final Map<String, dynamic>? reportData;
  
  /// إعدادات التقرير
  final Map<String, dynamic>? settings;

  const ContributionReport({
    required this.id,
    required this.title,
    this.description,
    this.taskId,
    this.userId,
    this.departmentId,
    this.startDate,
    this.endDate,
    this.periodDays,
    required this.createdById,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
    this.reportData,
    this.settings,
  });

  /// إنشاء من JSON
  factory ContributionReport.fromJson(Map<String, dynamic> json) =>
      _$ContributionReportFromJson(json);

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => _$ContributionReportToJson(this);

  /// إنشاء نسخة معدلة
  ContributionReport copyWith({
    String? id,
    String? title,
    String? description,
    String? taskId,
    String? userId,
    String? departmentId,
    DateTime? startDate,
    DateTime? endDate,
    int? periodDays,
    String? createdById,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
    Map<String, dynamic>? reportData,
    Map<String, dynamic>? settings,
  }) {
    return ContributionReport(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      departmentId: departmentId ?? this.departmentId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      periodDays: periodDays ?? this.periodDays,
      createdById: createdById ?? this.createdById,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      reportData: reportData ?? this.reportData,
      settings: settings ?? this.settings,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContributionReport && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ContributionReport(id: $id, title: $title, createdAt: $createdAt)';
  }
}

/// نموذج بيانات المساهمة
@JsonSerializable()
class ContributionData {
  /// معرف المساهمة
  final String id;
  
  /// نوع المساهمة
  final String type;
  
  /// وصف المساهمة
  final String description;
  
  /// معرف المستخدم
  final String userId;
  
  /// معرف المهمة
  final String taskId;
  
  /// تاريخ المساهمة
  final DateTime timestamp;
  
  /// قيمة المساهمة
  final double value;
  
  /// وحدة القياس
  final String unit;
  
  /// بيانات إضافية
  final Map<String, dynamic>? metadata;

  const ContributionData({
    required this.id,
    required this.type,
    required this.description,
    required this.userId,
    required this.taskId,
    required this.timestamp,
    required this.value,
    required this.unit,
    this.metadata,
  });

  /// إنشاء من JSON
  factory ContributionData.fromJson(Map<String, dynamic> json) =>
      _$ContributionDataFromJson(json);

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => _$ContributionDataToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContributionData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ContributionData(id: $id, type: $type, value: $value)';
  }
}

/// نموذج إحصائيات المساهمات
@JsonSerializable()
class ContributionStatistics {
  /// إجمالي المساهمات
  final int totalContributions;
  
  /// إجمالي القيمة
  final double totalValue;
  
  /// متوسط القيمة
  final double averageValue;
  
  /// أعلى قيمة
  final double maxValue;
  
  /// أقل قيمة
  final double minValue;
  
  /// عدد المساهمين
  final int contributorsCount;
  
  /// المساهمات حسب النوع
  final Map<String, int> contributionsByType;
  
  /// المساهمات حسب التاريخ
  final Map<String, double> contributionsByDate;

  const ContributionStatistics({
    required this.totalContributions,
    required this.totalValue,
    required this.averageValue,
    required this.maxValue,
    required this.minValue,
    required this.contributorsCount,
    required this.contributionsByType,
    required this.contributionsByDate,
  });

  /// إنشاء من JSON
  factory ContributionStatistics.fromJson(Map<String, dynamic> json) =>
      _$ContributionStatisticsFromJson(json);

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => _$ContributionStatisticsToJson(this);

  @override
  String toString() {
    return 'ContributionStatistics(total: $totalContributions, value: $totalValue)';
  }
}

/// نموذج ملخص المساهمات
class ContributionSummary {
  /// إجمالي المساهمات
  final int totalContributions;

  /// إجمالي المساهمين
  final int totalContributors;

  /// متوسط المساهمة لكل مستخدم
  final double averageContributionPerUser;

  /// أعلى مساهمة
  final double highestContribution;

  /// المساهمات حسب النوع
  final Map<String, int> contributionsByType;

  /// المساهمات حسب المستخدم
  final Map<String, double> contributionsByUser;

  const ContributionSummary({
    required this.totalContributions,
    required this.totalContributors,
    required this.averageContributionPerUser,
    required this.highestContribution,
    required this.contributionsByType,
    required this.contributionsByUser,
  });

  /// إنشاء من JSON
  factory ContributionSummary.fromJson(Map<String, dynamic> json) {
    return ContributionSummary(
      totalContributions: json['totalContributions'] as int? ?? 0,
      totalContributors: json['totalContributors'] as int? ?? 0,
      averageContributionPerUser: (json['averageContributionPerUser'] as num?)?.toDouble() ?? 0.0,
      highestContribution: (json['highestContribution'] as num?)?.toDouble() ?? 0.0,
      contributionsByType: Map<String, int>.from(json['contributionsByType'] as Map? ?? {}),
      contributionsByUser: Map<String, double>.from(
        (json['contributionsByUser'] as Map? ?? {}).map(
          (key, value) => MapEntry(key.toString(), (value as num?)?.toDouble() ?? 0.0),
        ),
      ),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'totalContributions': totalContributions,
      'totalContributors': totalContributors,
      'averageContributionPerUser': averageContributionPerUser,
      'highestContribution': highestContribution,
      'contributionsByType': contributionsByType,
      'contributionsByUser': contributionsByUser,
    };
  }

  @override
  String toString() {
    return 'ContributionSummary(total: $totalContributions, contributors: $totalContributors)';
  }
}

/// نموذج تفاصيل المساهمة
class ContributionDetail {
  /// معرف المساهمة
  final String id;

  /// اسم المستخدم
  final String userName;

  /// عنوان المهمة
  final String taskTitle;

  /// نوع المساهمة
  final String contributionType;

  /// نسبة المساهمة
  final double contributionPercentage;

  /// تاريخ المساهمة
  final DateTime contributionDate;

  /// ملاحظات
  final String? notes;

  const ContributionDetail({
    required this.id,
    required this.userName,
    required this.taskTitle,
    required this.contributionType,
    required this.contributionPercentage,
    required this.contributionDate,
    this.notes,
  });

  /// إنشاء من JSON
  factory ContributionDetail.fromJson(Map<String, dynamic> json) {
    return ContributionDetail(
      id: json['id'] as String? ?? '',
      userName: json['userName'] as String? ?? '',
      taskTitle: json['taskTitle'] as String? ?? '',
      contributionType: json['contributionType'] as String? ?? '',
      contributionPercentage: (json['contributionPercentage'] as num?)?.toDouble() ?? 0.0,
      contributionDate: json['contributionDate'] != null
          ? DateTime.parse(json['contributionDate'] as String)
          : DateTime.now(),
      notes: json['notes'] as String?,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'taskTitle': taskTitle,
      'contributionType': contributionType,
      'contributionPercentage': contributionPercentage,
      'contributionDate': contributionDate.toIso8601String(),
      'notes': notes,
    };
  }

  @override
  String toString() {
    return 'ContributionDetail(id: $id, user: $userName, percentage: $contributionPercentage)';
  }
}
