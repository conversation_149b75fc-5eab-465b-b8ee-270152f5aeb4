import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/message_reaction_models.dart';
import '../../../models/user_model.dart';
import '../../../constants/app_colors.dart';
import '../../../controllers/message_reactions_controller.dart';

/// ويدجت لعرض تفاعلات الرسائل
class MessageReactionsWidget extends StatefulWidget {
  /// معرف الرسالة
  final String messageId;

  /// معرف المستخدم الحالي
  final String currentUserId;

  /// حجم أيقونة التفاعل
  final double iconSize;

  /// حجم الخط
  final double fontSize;

  /// دالة يتم استدعاؤها عند إضافة تفاعل
  final Function(ReactionType)? onReactionAdded;

  /// دالة يتم استدعاؤها عند إزالة تفاعل
  final Function(ReactionType)? onReactionRemoved;

  const MessageReactionsWidget({
    super.key,
    required this.messageId,
    required this.currentUserId,
    this.iconSize = 18.0,
    this.fontSize = 12.0,
    this.onReactionAdded,
    this.onReactionRemoved,
  });

  @override
  State<MessageReactionsWidget> createState() => _MessageReactionsWidgetState();
}

class _MessageReactionsWidgetState extends State<MessageReactionsWidget> {
  final MessageReactionsController _reactionController = Get.find<MessageReactionsController>();

  Map<ReactionType, int> _reactionCounts = {};
  ReactionType? _userReaction;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReactions();
  }

  @override
  void didUpdateWidget(MessageReactionsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.messageId != widget.messageId) {
      _loadReactions();
    }
  }

  /// تحميل التفاعلات
  Future<void> _loadReactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل التفاعلات من الكونترولر
      await _reactionController.getReactionsByMessage(int.parse(widget.messageId));

      // الحصول على عدد التفاعلات لكل نوع
      final counts = <ReactionType, int>{};
      for (final reaction in _reactionController.messageReactions) {
        if (reaction.messageId.toString() == widget.messageId) {
          final reactionType = ReactionType.fromEmoji(reaction.reaction);
          counts[reactionType] = (counts[reactionType] ?? 0) + 1;
        }
      }

      // الحصول على تفاعل المستخدم الحالي
      final userReaction = _reactionController.messageReactions.firstWhereOrNull(
        (r) => r.messageId.toString() == widget.messageId &&
               r.userId.toString() == widget.currentUserId
      );

      if (mounted) {
        setState(() {
          _reactionCounts = counts;
          _userReaction = userReaction != null ? ReactionType.fromEmoji(userReaction.reaction) : null;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إضافة تفاعل
  Future<void> _addReaction(ReactionType type) async {
    try {
      await _reactionController.addReaction(
        int.parse(widget.messageId),
        int.parse(widget.currentUserId),
        type.emoji,
      );

      if (widget.onReactionAdded != null) {
        widget.onReactionAdded!(type);
      }

      _loadReactions();
    } catch (e) {
      // إذا كان الخطأ هو "تم إزالة التفاعل"، فهذا يعني أن المستخدم قام بإزالة تفاعله
      if (e.toString().contains('تم إزالة التفاعل')) {
        if (widget.onReactionRemoved != null && _userReaction != null) {
          widget.onReactionRemoved!(_userReaction!);
        }
      }

      _loadReactions();
    }
  }

  /// عرض قائمة التفاعلات المتاحة
  void _showReactionsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر تفاعلاً',
              style: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: ReactionType.values.map((type) {
                return InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    _addReaction(type);
                  },
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8.0),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(24.0),
                        ),
                        child: Text(
                          type.emoji,
                          style: TextStyle(fontSize: 24.0),
                        ),
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        type.name,
                        style: TextStyle(fontSize: 12.0),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض المستخدمين الذين تفاعلوا
  Future<void> _showReactingUsers(ReactionType type) async {
    try {
      // الحصول على المستخدمين الذين تفاعلوا بهذا النوع
      final users = _reactionController.messageReactions
          .where((r) => r.messageId.toString() == widget.messageId &&
                       r.reaction == type.emoji)
          .map((r) => r.user)
          .where((user) => user != null)
          .cast<User>()
          .toList();

      if (!mounted) return;

      showModalBottomSheet(
        context: context,
        builder: (context) => Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${type.emoji} ${type.displayName}',
                style: TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16.0),
              Expanded(
                child: ListView.builder(
                  itemCount: users.length,
                  itemBuilder: (context, index) {
                    final user = users[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundImage: user.profileImage != null
                            ? NetworkImage(user.profileImage!)
                            : null,
                        child: user.profileImage == null
                            ? Text(user.name[0])
                            : null,
                      ),
                      title: Text(user.name),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      // التعامل مع الخطأ
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox.shrink();
    }

    // إذا لم تكن هناك تفاعلات، لا تعرض شيئًا
    if (_reactionCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4.0),
      child: Wrap(
        spacing: 4.0,
        children: _reactionCounts.entries
            .where((entry) => entry.value > 0)
            .map((entry) {
          final type = entry.key;
          final count = entry.value;
          final isUserReaction = _userReaction == type;

          return InkWell(
            onTap: () => _addReaction(type),
            onLongPress: () => _showReactingUsers(type),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 4.0,
              ),
              decoration: BoxDecoration(
                color: isUserReaction
                    ? AppColors.primary.withValues(alpha: 51) // 0.2 * 255 = 51
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: isUserReaction ? AppColors.primary : Colors.grey[300]!,
                  width: 1.0,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    type.emoji,
                    style: TextStyle(fontSize: widget.iconSize),
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    count.toString(),
                    style: TextStyle(
                      fontSize: widget.fontSize,
                      fontWeight:
                          isUserReaction ? FontWeight.bold : FontWeight.normal,
                      color:
                          isUserReaction ? AppColors.primary : Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList()
          ..add(
            InkWell(
              onTap: _showReactionsMenu,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8.0,
                  vertical: 4.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Icon(
                  Icons.add_reaction_outlined,
                  size: widget.iconSize,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
      ),
    );
  }
}
