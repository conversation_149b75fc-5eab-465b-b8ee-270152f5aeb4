import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';


/// مخطط دائري بسيط باستخدام BaseChartWidget
class PieChartWidget extends BaseChartWidget {
  const PieChartWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    return Column(
      children: [
        // المخطط الدائري
        Expanded(
          flex: 3,
          child: SfCircularChart(
            title: ChartTitle(text: item.title),
            legend: Legend(isVisible: true, position: LegendPosition.bottom),
            series: <PieSeries<ChartData, String>>[
              PieSeries<ChartData, String>(
                dataSource: data,
                xValueMapper: (ChartData data, _) => data.label,
                yValueMapper: (ChartData data, _) => data.value,
                dataLabelSettings: const DataLabelSettings(isVisible: true),
                enableTooltip: true,
              )
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // مفتاح المخطط
        Expanded(
          flex: 1,
          child: ChartLegend(
            data: data,
            showValues: true,
          ),
        ),
      ],
    );
  }

}
