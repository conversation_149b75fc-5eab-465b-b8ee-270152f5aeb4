import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/task_document_model.dart';
import '../../models/task_models.dart';
import '../../controllers/task_documents_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../utils/date_formatter.dart';
import '../documents/task_document_editor_screen.dart';

/// تبويب المستندات المحسن للمهام - يستخدم نظام الأرشفة المتكامل
class EnhancedTaskDocumentsTab extends StatefulWidget {
  final Task task;

  const EnhancedTaskDocumentsTab({
    Key? key,
    required this.task,
  }) : super(key: key);

  @override
  State<EnhancedTaskDocumentsTab> createState() => _EnhancedTaskDocumentsTabState();
}

class _EnhancedTaskDocumentsTabState extends State<EnhancedTaskDocumentsTab>
    with SingleTickerProviderStateMixin {
  
  late TaskDocumentsController _documentsController;
  late AuthController _authController;
  late TabController _tabController;

  // متحكمات النماذج
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _contentController = TextEditingController();

  // حالة التطبيق
  TaskDocumentType _selectedType = TaskDocumentType.report;
  TaskDocument? _selectedDocument;

  @override
  void initState() {
    super.initState();
    _documentsController = Get.put(TaskDocumentsController());
    _authController = Get.find<AuthController>();
    _tabController = TabController(length: 4, vsync: this);
    
    // تحميل مستندات المهمة
    _loadTaskDocuments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _loadTaskDocuments() async {
    await _documentsController.loadTaskDocuments(widget.task.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(),
          
          // شريط التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(text: 'جميع المستندات', icon: Icon(Icons.description)),
              Tab(text: 'التقارير', icon: Icon(Icons.assessment)),
              Tab(text: 'التحليلات', icon: Icon(Icons.analytics)),
              Tab(text: 'المشتركة', icon: Icon(Icons.share)),
            ],
          ),
          
          // محتوى التبويب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllDocumentsTab(),
                _buildFilteredDocumentsTab(TaskDocumentType.report),
                _buildFilteredDocumentsTab(TaskDocumentType.analysis),
                _buildSharedDocumentsTab(),
              ],
            ),
          ),
        ],
      ),
      
      // زر الإضافة العائم
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateDocumentDialog,
        icon: const Icon(Icons.add),
        label: const Text('إضافة مستند'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  /// بناء شريط الإحصائيات
  Widget _buildStatsBar() {
    return Obx(() {
      final stats = _documentsController.stats;
      if (stats == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('المجموع', stats.totalDocuments, Icons.description),
            _buildStatItem('التقارير', stats.reportCount, Icons.assessment),
            _buildStatItem('التحليلات', stats.analysisCount, Icons.analytics),
            _buildStatItem('المشتركة', stats.sharedDocuments, Icons.share),
          ],
        ),
      );
    });
  }

  Widget _buildStatItem(String label, int count, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.primary, size: 20),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        Text(
          label,
          style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// بناء تبويب جميع المستندات
  Widget _buildAllDocumentsTab() {
    return Column(
      children: [
        // شريط البحث والمرشحات
        _buildSearchAndFilters(),
        
        // قائمة المستندات
        Expanded(
          child: Obx(() {
            if (_documentsController.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_documentsController.error.isNotEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red.shade300),
                    const SizedBox(height: 16),
                    Text(
                      'خطأ في تحميل المستندات',
                      style: AppStyles.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _documentsController.error,
                      style: AppStyles.bodyMedium.copyWith(color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadTaskDocuments,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            final documents = _documentsController.filteredDocuments;
            if (documents.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.description, size: 64, color: Colors.grey.shade300),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد مستندات',
                      style: AppStyles.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'ابدأ بإضافة مستند جديد للمهمة',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: documents.length,
              itemBuilder: (context, index) {
                final document = documents[index];
                return _buildDocumentCard(document);
              },
            );
          }),
        ),
      ],
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث في المستندات...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              _documentsController.searchDocuments(value);
            },
          ),
          
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<TaskDocumentType?>(
                  value: _documentsController.selectedTypeFilter,
                  decoration: const InputDecoration(
                    labelText: 'نوع المستند',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<TaskDocumentType?>(
                      value: null,
                      child: Text('جميع الأنواع'),
                    ),
                    ...TaskDocumentType.allTypes.map((type) =>
                      DropdownMenuItem<TaskDocumentType?>(
                        value: type,
                        child: Text(type.displayName),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    _documentsController.setTypeFilter(value);
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              FilterChip(
                label: const Text('المشتركة فقط'),
                selected: _documentsController.showSharedOnly,
                onSelected: (selected) {
                  _documentsController.setSharedFilter(selected);
                },
              ),
              
              const SizedBox(width: 8),
              
              IconButton(
                icon: const Icon(Icons.clear),
                tooltip: 'مسح المرشحات',
                onPressed: () {
                  _documentsController.clearFilters();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء تبويب المستندات المفلترة
  Widget _buildFilteredDocumentsTab(TaskDocumentType type) {
    return Obx(() {
      final documents = _documentsController.filteredDocuments
          .where((doc) => doc.type == type)
          .toList();

      if (documents.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                type == TaskDocumentType.report ? Icons.assessment : Icons.analytics,
                size: 64,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد ${type.displayName}',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'ابدأ بإضافة ${type.displayName} جديد',
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: documents.length,
        itemBuilder: (context, index) {
          final document = documents[index];
          return _buildDocumentCard(document);
        },
      );
    });
  }

  /// بناء تبويب المستندات المشتركة
  Widget _buildSharedDocumentsTab() {
    return Obx(() {
      final sharedDocuments = _documentsController.filteredDocuments
          .where((doc) => doc.isShared)
          .toList();

      if (sharedDocuments.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.share, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد مستندات مشتركة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'يمكنك مشاركة المستندات مع المساهمين في المهمة',
                style: TextStyle(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: sharedDocuments.length,
        itemBuilder: (context, index) {
          final document = sharedDocuments[index];
          return _buildDocumentCard(document, showSharedBadge: true);
        },
      );
    });
  }

  /// بناء بطاقة المستند
  Widget _buildDocumentCard(TaskDocument document, {bool showSharedBadge = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _viewDocument(document),
        // onTap: () => Get.to(ContainerDocEditor()),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والنوع
              Row(
                children: [
                  Expanded(
                    child: Text(
                      document.archiveDocument?.title ?? 'مستند بدون عنوان',
                      style: AppStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTypeColor(document.type).withAlpha(51),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      document.type.displayName,
                      style: AppStyles.bodySmall.copyWith(
                        color: _getTypeColor(document.type),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (showSharedBadge) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(51),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.share,
                        size: 12,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ],
              ),
              
              // الوصف
              if (document.description != null && document.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  document.description!,
                  style: AppStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey.shade500),
                  const SizedBox(width: 4),
                  Text(
                    DateFormatter.formatDate(
                      DateTime.fromMillisecondsSinceEpoch(document.createdAt),
                    ),
                    style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  
                  // أزرار الإجراءات
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _editDocument(document),
                    tooltip: 'تحرير',
                  ),
                  IconButton(
                    icon: Icon(
                      document.isShared ? Icons.share : Icons.share_outlined,
                      size: 20,
                    ),
                    onPressed: () => _toggleDocumentSharing(document),
                    tooltip: document.isShared ? 'إلغاء المشاركة' : 'مشاركة',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                    onPressed: () => _deleteDocument(document),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(TaskDocumentType type) {
    switch (type) {
      case TaskDocumentType.report:
        return Colors.blue;
      case TaskDocumentType.analysis:
        return Colors.green;
      case TaskDocumentType.plan:
        return Colors.orange;
      case TaskDocumentType.attachment:
        return Colors.purple;
      case TaskDocumentType.note:
        return Colors.teal;
      case TaskDocumentType.specification:
        return Colors.indigo;
      case TaskDocumentType.documentation:
        return Colors.brown;
    }
  }

  /// عرض حوار إنشاء مستند جديد
  void _showCreateDocumentDialog() {
    _titleController.clear();
    _descriptionController.clear();
    _contentController.clear();
    _selectedType = TaskDocumentType.report;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مستند جديد'),
        content: SizedBox(
          width: 500,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المستند *',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<TaskDocumentType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع المستند',
                  border: OutlineInputBorder(),
                ),
                items: TaskDocumentType.allTypes.map((type) =>
                  DropdownMenuItem<TaskDocumentType>(
                    value: type,
                    child: Text(type.displayName),
                  ),
                ).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _createDocument,
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  /// إنشاء مستند جديد
  Future<void> _createDocument() async {
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان المستند',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    Navigator.of(context).pop();

    final document = await _documentsController.createTaskDocument(
      taskId: widget.task.id,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      type: _selectedType,
      content: '',
      createdBy: _authController.currentUser.value!.id,
    );

    if (document != null) {
      Get.snackbar(
        'تم بنجاح',
        'تم إنشاء المستند بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
      
      // فتح المحرر للمستند الجديد
      Get.to(() => TaskDocumentEditorScreen(
        taskId: widget.task.id,
        initialType: _selectedType,
      ));
    }
  }

  /// عرض المستند
  void _viewDocument(TaskDocument document) {
    Get.to(() => TaskDocumentEditorScreen(
      document: document,
      taskId: widget.task.id,
    ));
  }

  /// تحرير المستند
  void _editDocument(TaskDocument document) {
    Get.to(() => TaskDocumentEditorScreen(
      document: document,
      taskId: widget.task.id,
    ));
  }

  /// تبديل مشاركة المستند
  Future<void> _toggleDocumentSharing(TaskDocument document) async {
    try {
      final success = await _documentsController.shareDocumentWithContributors(
        document,
        [], // قائمة فارغة للمساهمين - يمكن تحسينها لاحقاً
      );

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          document.isShared ? 'تم إلغاء مشاركة المستند' : 'تم مشاركة المستند',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تغيير حالة مشاركة المستند',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// حذف المستند
  Future<void> _deleteDocument(TaskDocument document) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستند "${document.archiveDocument?.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await _documentsController.deleteTaskDocument(document);

        if (success) {
          Get.snackbar(
            'تم بنجاح',
            'تم حذف المستند بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );
        } else {
          Get.snackbar(
            'خطأ',
            'فشل في حذف المستند',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف المستند: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }
}
