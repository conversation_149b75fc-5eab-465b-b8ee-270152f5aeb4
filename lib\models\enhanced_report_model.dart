import 'package:flutter/material.dart';
import 'report_models.dart';
import 'reporting/report_result_model.dart';

/// تقرير محسن مع ميزات إضافية
class EnhancedReport {
  final String id;
  final String title;
  final String? description;
  final ReportType type;
  final String? query;
  final List<ReportFilter> filters;
  final List<ReportVisualization> visualizations;
  final ReportPeriod period;
  final DateTime? customStartDate;
  final DateTime? customEndDate;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int createdBy;
  final bool isShared;
  final List<int>? sharedWithUserIds;
  final bool isFavorite;
  final bool isPublic;
  final Color? color;
  final IconData? icon;
  final Map<String, dynamic>? settings;

  const EnhancedReport({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.query,
    this.filters = const [],
    this.visualizations = const [],
    this.period = ReportPeriod.thisMonth,
    this.customStartDate,
    this.customEndDate,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
    this.isShared = false,
    this.sharedWithUserIds,
    this.isFavorite = false,
    this.isPublic = false,
    this.color,
    this.icon,
    this.settings,
  });

  factory EnhancedReport.fromJson(Map<String, dynamic> json) {
    return EnhancedReport(
      id: json['id'].toString(),
      title: json['title'] as String,
      description: json['description'] as String?,
      type: ReportType.values.firstWhere(
        (e) => e.value == json['reportType'] || e.name == json['type'],
        orElse: () => ReportType.custom,
      ),
      query: json['query'] as String?,
      filters: json['filters'] != null
          ? (json['filters'] as List)
              .map((f) => ReportFilter.fromJson(f as Map<String, dynamic>))
              .toList()
          : [],
      visualizations: json['visualizations'] != null
          ? (json['visualizations'] as List)
              .map((v) => ReportVisualization.fromJson(v as Map<String, dynamic>))
              .toList()
          : [],
      period: json['period'] != null
          ? ReportPeriod.values.firstWhere(
              (e) => e.name == json['period'],
              orElse: () => ReportPeriod.thisMonth,
            )
          : ReportPeriod.thisMonth,
      customStartDate: json['customStartDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['customStartDate'] as int)
          : null,
      customEndDate: json['customEndDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['customEndDate'] as int)
          : null,
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'] is int 
              ? json['createdAt'] as int
              : (json['createdAt'] as int) * 1000)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'] is int 
              ? json['updatedAt'] as int
              : (json['updatedAt'] as int) * 1000)
          : null,
      createdBy: json['createdBy'] as int? ?? 0,
      isShared: json['isShared'] as bool? ?? false,
      sharedWithUserIds: json['sharedWithUserIds'] != null
          ? List<int>.from(json['sharedWithUserIds'] as List)
          : null,
      isFavorite: json['isFavorite'] as bool? ?? false,
      isPublic: json['isPublic'] as bool? ?? false,
      color: json['color'] != null
          ? Color(json['color'] as int)
          : null,
      icon: json['icon'] != null
          ? IconData(json['icon'] as int, fontFamily: 'MaterialIcons')
          : null,
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'reportType': type.value,
      'type': type.name,
      'query': query,
      'filters': filters.map((f) => f.toJson()).toList(),
      'visualizations': visualizations.map((v) => v.toJson()).toList(),
      'period': period.name,
      'customStartDate': customStartDate?.millisecondsSinceEpoch,
      'customEndDate': customEndDate?.millisecondsSinceEpoch,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'createdBy': createdBy,
      'isShared': isShared,
      'sharedWithUserIds': sharedWithUserIds,
      'isFavorite': isFavorite,
      'isPublic': isPublic,
      'color': color?.toARGB32().toRadixString(16),
      'icon': icon?.codePoint,
      'settings': settings,
    };
  }

  EnhancedReport copyWith({
    String? id,
    String? title,
    String? description,
    ReportType? type,
    String? query,
    List<ReportFilter>? filters,
    List<ReportVisualization>? visualizations,
    ReportPeriod? period,
    DateTime? customStartDate,
    DateTime? customEndDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? createdBy,
    bool? isShared,
    List<int>? sharedWithUserIds,
    bool? isFavorite,
    bool? isPublic,
    Color? color,
    IconData? icon,
    Map<String, dynamic>? settings,
  }) {
    return EnhancedReport(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      query: query ?? this.query,
      filters: filters ?? this.filters,
      visualizations: visualizations ?? this.visualizations,
      period: period ?? this.period,
      customStartDate: customStartDate ?? this.customStartDate,
      customEndDate: customEndDate ?? this.customEndDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      isShared: isShared ?? this.isShared,
      sharedWithUserIds: sharedWithUserIds ?? this.sharedWithUserIds,
      isFavorite: isFavorite ?? this.isFavorite,
      isPublic: isPublic ?? this.isPublic,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      settings: settings ?? this.settings,
    );
  }

  /// تحويل من Report عادي إلى EnhancedReport
  factory EnhancedReport.fromReport(Report report) {
    return EnhancedReport(
      id: report.id.toString(),
      title: report.title,
      description: report.description,
      type: report.reportType,
      query: report.query,
      createdAt: report.createdAtDateTime,
      updatedAt: report.updatedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(report.updatedAt! * 1000)
          : null,
      createdBy: report.createdBy,
      isPublic: report.isPublic,
    );
  }

  /// تحويل إلى Report عادي
  Report toReport() {
    return Report(
      id: int.tryParse(id) ?? 0,
      title: title,
      description: description,
      reportType: type,
      query: query ?? '',
      createdBy: createdBy,
      createdAt: createdAt.millisecondsSinceEpoch ~/ 1000,
      updatedAt: updatedAt!.millisecondsSinceEpoch ~/ 1000,
      isPublic: isPublic,
      isDeleted: false,
    );
  }
}

/// تنسيقات التصدير
enum ReportFormat {
  pdf,
  excel,
  csv,
  json,
  xml,
  html,
}

/// حالة التقرير
enum ReportStatus {
  draft,
  published,
  archived,
  scheduled,
  running,
  completed,
  failed,
}

/// أولوية التقرير
enum ReportPriority {
  low,
  normal,
  high,
  urgent,
}

/// نوع الوصول للتقرير
enum ReportAccessType {
  private,
  shared,
  public,
  departmental,
}

/// إعدادات التقرير المتقدمة
class ReportSettings {
  final bool autoRefresh;
  final int? refreshInterval;
  final bool enableNotifications;
  final bool enableScheduling;
  final ReportFormat defaultExportFormat;
  final Map<String, dynamic>? customSettings;

  const ReportSettings({
    this.autoRefresh = false,
    this.refreshInterval,
    this.enableNotifications = true,
    this.enableScheduling = true,
    this.defaultExportFormat = ReportFormat.pdf,
    this.customSettings,
  });

  factory ReportSettings.fromJson(Map<String, dynamic> json) {
    return ReportSettings(
      autoRefresh: json['autoRefresh'] as bool? ?? false,
      refreshInterval: json['refreshInterval'] as int?,
      enableNotifications: json['enableNotifications'] as bool? ?? true,
      enableScheduling: json['enableScheduling'] as bool? ?? true,
      defaultExportFormat: json['defaultExportFormat'] != null
          ? ReportFormat.values.firstWhere(
              (e) => e.name == json['defaultExportFormat'],
              orElse: () => ReportFormat.pdf,
            )
          : ReportFormat.pdf,
      customSettings: json['customSettings'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoRefresh': autoRefresh,
      'refreshInterval': refreshInterval,
      'enableNotifications': enableNotifications,
      'enableScheduling': enableScheduling,
      'defaultExportFormat': defaultExportFormat.name,
      'customSettings': customSettings,
    };
  }
}
