import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../controllers/contribution_report_controller.dart';
import '../../models/contribution_report_model.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/common/loading_indicator.dart';

/// فئة بيانات للرسم البياني الدائري
class PieData {
  final String category;
  final double value;
  final Color color;

  PieData(this.category, this.value, this.color);
}

/// شاشة تفاصيل تقرير المساهمات
/// تعرض تفاصيل تقرير المساهمات المحدد
class ContributionReportDetailsScreen extends StatefulWidget {
  final String reportId;

  const ContributionReportDetailsScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ContributionReportDetailsScreen> createState() => _ContributionReportDetailsScreenState();
}

class _ContributionReportDetailsScreenState extends State<ContributionReportDetailsScreen> with SingleTickerProviderStateMixin {
  final ContributionReportController _reportController = Get.find<ContributionReportController>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // استخدام Future.microtask لتأخير تحميل التقرير حتى اكتمال بناء الواجهة
    Future.microtask(() => _loadReport());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل تقرير المساهمات
  Future<void> _loadReport() async {
    try {
      await _reportController.loadReport(widget.reportId);
    } catch (e) {
      debugPrint('خطأ في تحميل التقرير: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'تفاصيل تقرير المساهمات',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReport,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () => _exportReportToPdf(widget.reportId),
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.table_chart),
            onPressed: () => _exportReportToExcel(widget.reportId),
            tooltip: 'تصدير Excel',
          ),
        ],
      ),
      body: Obx(() {
        if (_reportController.isLoading.value) {
          return const Center(child: LoadingIndicator());
        }

        if (_reportController.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ',
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  _reportController.error.value,
                  style: AppStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _loadReport,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        if (_reportController.currentReport.value == null) {
          return const Center(
            child: Text('التقرير غير موجود'),
          );
        }

        return Column(
          children: [
            // معلومات التقرير
            _buildReportHeader(),

            // شريط التبويب
            TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
              tabs: const [
                Tab(text: 'الملخص'),
                Tab(text: 'الرسوم البيانية'),
                Tab(text: 'التفاصيل'),
              ],
            ),

            // محتوى التبويب
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildSummaryTab(),
                  _buildChartsTab(),
                  _buildDetailsTab(),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء رأس التقرير
  Widget _buildReportHeader() {
    final report = _reportController.currentReport.value!;

    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.primary.withValues(alpha: 26), // 0.1 * 255 = 26
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            report.title,
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.primary,
            ),
          ),
          if (report.description != null && report.description!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              report.description!,
              style: AppStyles.bodySmall,
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.date_range, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                'الفترة: ${_getReportPeriodText(report)}',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء تبويب الملخص
  Widget _buildSummaryTab() {
    final summary = _reportController.contributionSummary.value;

    if (summary == null) {
      return const Center(
        child: Text('لا توجد بيانات ملخص متاحة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الإحصائيات الرئيسية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الإحصائيات الرئيسية',
                    style: AppStyles.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow(
                    'إجمالي المساهمات',
                    '${summary.totalContributions}',
                    Icons.analytics,
                  ),
                  const Divider(),
                  _buildStatRow(
                    'إجمالي المساهمين',
                    '${summary.totalContributors}',
                    Icons.people,
                  ),
                  const Divider(),
                  _buildStatRow(
                    'متوسط المساهمة لكل مستخدم',
                    '${summary.averageContributionPerUser.toStringAsFixed(1)}%',
                    Icons.person,
                  ),
                  const Divider(),
                  _buildStatRow(
                    'أعلى مساهمة',
                    '${summary.highestContribution.toStringAsFixed(1)}%',
                    Icons.emoji_events,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // بطاقة توزيع المساهمات حسب النوع
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع المساهمات حسب النوع',
                    style: AppStyles.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  ...summary.contributionsByType.entries.map((entry) {
                    return Column(
                      children: [
                        _buildStatRow(
                          _reportController.getContributionTypeText(entry.key),
                          '${entry.value}',
                          _getContributionTypeIcon(entry.key),
                        ),
                        if (entry.key != summary.contributionsByType.keys.last)
                          const Divider(),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // بطاقة توزيع المساهمات حسب المستخدم
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع المساهمات حسب المستخدم',
                    style: AppStyles.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  ...summary.contributionsByUser.entries.map((entry) {
                    return FutureBuilder<String>(
                      future: _reportController.getUserName(entry.key),
                      builder: (context, snapshot) {
                        final userName = snapshot.data ?? 'مستخدم غير معروف';
                        return Column(
                          children: [
                            _buildUserContributionRow(
                              userName,
                              entry.value,
                            ),
                            if (entry.key != summary.contributionsByUser.keys.last)
                              const Divider(),
                          ],
                        );
                      },
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب الرسوم البيانية
  Widget _buildChartsTab() {
    final summary = _reportController.contributionSummary.value;

    if (summary == null) {
      return const Center(
        child: Text('لا توجد بيانات رسوم بيانية متاحة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسم بياني دائري لتوزيع المساهمات حسب النوع
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع المساهمات حسب النوع',
                    style: AppStyles.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 300,
                    child: _buildContributionTypesPieChart(summary.contributionsByType),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // رسم بياني دائري لتوزيع المساهمات حسب المستخدم
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع المساهمات حسب المستخدم',
                    style: AppStyles.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 300,
                    child: _buildUserContributionsPieChart(summary.contributionsByUser),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب التفاصيل
  Widget _buildDetailsTab() {
    final details = _reportController.contributionDetails;

    if (details.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات تفاصيل متاحة'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(detail.userName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('المهمة: ${detail.taskTitle}'),
                Text('النوع: ${_reportController.getContributionTypeText(detail.contributionType)}'),
                Text('المساهمة: ${detail.contributionPercentage.toStringAsFixed(1)}%'),
                Text('التاريخ: ${DateFormat('yyyy/MM/dd HH:mm').format(detail.contributionDate)}'),
                if (detail.notes != null && detail.notes!.isNotEmpty)
                  Text('ملاحظات: ${detail.notes}'),
              ],
            ),
            leading: CircleAvatar(
              backgroundColor: AppColors.primary,
              child: Icon(
                _getContributionTypeIcon(detail.contributionType),
                color: Colors.white,
                size: 20,
              ),
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value, IconData icon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(label, style: AppStyles.bodyMedium),
          ],
        ),
        Text(
          value,
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء صف مساهمة المستخدم
  Widget _buildUserContributionRow(String userName, double contribution) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(userName, style: AppStyles.bodyMedium),
            Text(
              '${contribution.toStringAsFixed(1)}%',
              style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: contribution / 100,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ],
    );
  }

  /// بناء رسم بياني دائري لتوزيع المساهمات حسب النوع
  Widget _buildContributionTypesPieChart(Map<String, int> contributionsByType) {
    if (contributionsByType.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات متاحة'),
      );
    }

    // تحديد الألوان لكل نوع مساهمة
    final Map<String, Color> typeColors = {
      'transfer': Colors.orange,
      'file': Colors.green,
      'comment': Colors.blue,
      'manual': Colors.purple,
      'auto': Colors.teal,
      'activity': Colors.indigo,
      'unknown': Colors.grey,
    };

    // إنشاء قطاعات الرسم البياني
    final List<PieData> pieData = [];
    final total = contributionsByType.values.reduce((a, b) => a + b);

    contributionsByType.forEach((type, count) {
      pieData.add(
        PieData(
          type,
          count.toDouble(),
          typeColors[type] ?? Colors.grey,
        ),
      );
    });

    return Row(
      children: [
        // الرسم البياني
        Expanded(
          flex: 2,
          child: SfCircularChart(
            series: <CircularSeries>[
              PieSeries<PieData, String>(
                dataSource: pieData,
                xValueMapper: (PieData data, _) => data.category,
                yValueMapper: (PieData data, _) => data.value,
                pointColorMapper: (PieData data, _) => data.color,
                dataLabelSettings: const DataLabelSettings(
                  isVisible: true,
                  labelPosition: ChartDataLabelPosition.outside,
                ),
                radius: '80%',
              ),
            ],
            tooltipBehavior: TooltipBehavior(enable: true),
          ),
        ),
        // مفتاح الرسم البياني
        Expanded(
          flex: 1,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: contributionsByType.entries.map((entry) {
              final type = entry.key;
              final count = entry.value;
              final percentage = (count / total) * 100;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: typeColors[type] ?? Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${_reportController.getContributionTypeText(type)} (${percentage.toStringAsFixed(1)}%)',
                        style: AppStyles.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء رسم بياني دائري لتوزيع المساهمات حسب المستخدم
  Widget _buildUserContributionsPieChart(Map<String, double> contributionsByUser) {
    if (contributionsByUser.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات متاحة'),
      );
    }

    // إنشاء قطاعات الرسم البياني
    final List<PieData> pieData = [];
    final total = contributionsByUser.values.reduce((a, b) => a + b);

    // تحديد الألوان لكل مستخدم
    final List<Color> userColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
      Colors.cyan,
      Colors.deepOrange,
    ];

    int colorIndex = 0;
    final Map<String, Color> colorMap = {};

    contributionsByUser.forEach((userId, contribution) {
      colorMap[userId] = userColors[colorIndex % userColors.length];
      colorIndex++;

      pieData.add(
        PieData(
          userId,
          contribution.toDouble(),
          colorMap[userId]!,
        ),
      );
    });

    return FutureBuilder<List<String>>(
      future: Future.wait(
        contributionsByUser.keys.map((userId) => _reportController.getUserName(userId)),
      ),
      builder: (context, snapshot) {
        final userNames = snapshot.data ?? List.filled(contributionsByUser.length, 'مستخدم غير معروف');

        return Row(
          children: [
            // الرسم البياني
            Expanded(
              flex: 2,
              child: SfCircularChart(
                series: <CircularSeries>[
                  PieSeries<PieData, String>(
                    dataSource: pieData,
                    xValueMapper: (PieData data, _) => data.category,
                    yValueMapper: (PieData data, _) => data.value,
                    pointColorMapper: (PieData data, _) => data.color,
                    dataLabelSettings: const DataLabelSettings(
                      isVisible: true,
                      labelPosition: ChartDataLabelPosition.outside,
                    ),
                    radius: '80%',
                  ),
                ],
                tooltipBehavior: TooltipBehavior(enable: true),
              ),
            ),
            // مفتاح الرسم البياني
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(contributionsByUser.length, (index) {
                  final userId = contributionsByUser.keys.elementAt(index);
                  final contribution = contributionsByUser[userId]!;
                  final percentage = (contribution / total) * 100;
                  final userName = userNames[index];

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: colorMap[userId],
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '$userName (${percentage.toStringAsFixed(1)}%)',
                            style: AppStyles.bodySmall,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على نص الفترة الزمنية للتقرير
  String _getReportPeriodText(ContributionReport report) {
    if (report.startDate != null && report.endDate != null) {
      return '${DateFormat('yyyy/MM/dd').format(report.startDate!)} - ${DateFormat('yyyy/MM/dd').format(report.endDate!)}';
    } else if (report.periodDays != null) {
      return 'آخر ${report.periodDays} يوم';
    } else {
      return 'الفترة الكاملة';
    }
  }

  /// الحصول على أيقونة نوع المساهمة
  IconData _getContributionTypeIcon(String type) {
    switch (type) {
      case 'transfer':
        return Icons.swap_horiz;
      case 'file':
        return Icons.attach_file;
      case 'comment':
        return Icons.comment;
      case 'manual':
        return Icons.edit;
      case 'auto':
        return Icons.auto_awesome;
      case 'activity':
        return Icons.diversity_3;
      default:
        return Icons.update;
    }
  }

  /// تصدير التقرير بتنسيق PDF
  Future<void> _exportReportToPdf(String reportId) async {
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      final filePath = await _reportController.exportReportToPdf(reportId);
      Get.back();

      if (filePath != null) {
        Get.snackbar(
          'نجاح',
          'تم تصدير التقرير بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تصدير التقرير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصدير التقرير بتنسيق Excel
  Future<void> _exportReportToExcel(String reportId) async {
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      final filePath = await _reportController.exportReportToExcel(reportId);
      Get.back();

      if (filePath != null) {
        Get.snackbar(
          'نجاح',
          'تم تصدير التقرير بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تصدير التقرير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
}
