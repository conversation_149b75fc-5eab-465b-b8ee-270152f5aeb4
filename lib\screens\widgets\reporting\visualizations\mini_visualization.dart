import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../../constants/app_styles.dart';

/// تعداد أنواع التصور المرئي
enum VisualizationType {
  bar<PERSON><PERSON>,
  line<PERSON>hart,
  pie<PERSON>hart,
  area<PERSON><PERSON>,
  scatter<PERSON><PERSON>,
  bubble<PERSON>hart,
  radarChart,
  gaugeChart,
  kpiCard,
  heatMap,
  ganttChart,
  treeMap,
  table,
  summary,
  custom,
}

/// معاينة مصغرة للتصور المرئي
///
/// تعرض معاينة مصغرة للتصور المرئي في بطاقة التقرير
class MiniVisualization extends StatelessWidget {
  /// نوع التصور المرئي
  final VisualizationType type;

  /// عنوان التصور المرئي
  final String title;

  /// بيانات التصور المرئي (اختياري)
  final Map<String, dynamic>? data;

  const MiniVisualization({
    super.key,
    required this.type,
    required this.title,
    this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التصور المرئي
          Text(
            title,
            style: AppStyles.captionMedium,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // معاينة التصور المرئي
          Expanded(
            child: _buildVisualizationPreview(),
          ),
        ],
      ),
    );
  }

  /// بناء معاينة التصور المرئي
  Widget _buildVisualizationPreview() {
    switch (type) {
      case VisualizationType.barChart:
        return _buildMiniBarChart();
      case VisualizationType.lineChart:
        return _buildMiniLineChart();
      case VisualizationType.pieChart:
        return _buildMiniPieChart();
      case VisualizationType.areaChart:
        return _buildMiniAreaChart();
      case VisualizationType.table:
        return _buildMiniTable();
      case VisualizationType.kpiCard:
        return _buildMiniKpiCard();
      case VisualizationType.gaugeChart:
        return _buildMiniGaugeChart();
      default:
        return _buildGenericPreview();
    }
  }

  /// بناء معاينة مخطط شريطي مصغر
  Widget _buildMiniBarChart() {
    // إنشاء بيانات عشوائية للعرض
    final random = math.Random();
    final barCount = 5;
    final barValues = List.generate(barCount, (_) => random.nextDouble() * 100);

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;
        final barWidth = width / barCount;

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: List.generate(barCount, (index) {
            final barHeight = (barValues[index] / 100) * height;

            return Container(
              width: barWidth * 0.7,
              height: barHeight,
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }),
        );
      },
    );
  }

  /// بناء معاينة مخطط خطي مصغر
  Widget _buildMiniLineChart() {
    return CustomPaint(
      painter: MiniLineChartPainter(),
      size: Size.infinite,
    );
  }

  /// بناء معاينة مخطط دائري مصغر
  Widget _buildMiniPieChart() {
    return CustomPaint(
      painter: MiniPieChartPainter(),
      size: Size.infinite,
    );
  }

  /// بناء معاينة مخطط مساحي مصغر
  Widget _buildMiniAreaChart() {
    return CustomPaint(
      painter: MiniAreaChartPainter(),
      size: Size.infinite,
    );
  }

  /// بناء معاينة جدول مصغر
  Widget _buildMiniTable() {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.5,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: 9,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            color: index == 0
                ? Colors.grey.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(2),
          ),
        );
      },
    );
  }

  /// بناء معاينة بطاقة KPI مصغرة
  Widget _buildMiniKpiCard() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '85%',
            style: AppStyles.titleLarge.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'نسبة الإنجاز',
            style: AppStyles.captionSmall,
          ),
        ],
      ),
    );
  }

  /// بناء معاينة مخطط مقياس مصغر
  Widget _buildMiniGaugeChart() {
    return CustomPaint(
      painter: MiniGaugeChartPainter(),
      size: Size.infinite,
    );
  }

  /// بناء معاينة عامة
  Widget _buildGenericPreview() {
    return Center(
      child: Icon(
        _getVisualizationIcon(),
        size: 32,
        color: Colors.grey,
      ),
    );
  }

  /// الحصول على أيقونة التصور المرئي
  IconData _getVisualizationIcon() {
    switch (type) {
      case VisualizationType.barChart:
        return Icons.bar_chart;
      case VisualizationType.lineChart:
        return Icons.show_chart;
      case VisualizationType.pieChart:
        return Icons.pie_chart;
      case VisualizationType.areaChart:
        return Icons.area_chart;
      case VisualizationType.scatterChart:
        return Icons.scatter_plot;
      case VisualizationType.bubbleChart:
        return Icons.bubble_chart;
      case VisualizationType.radarChart:
        return Icons.radar;
      case VisualizationType.gaugeChart:
        return Icons.speed;
      case VisualizationType.kpiCard:
        return Icons.dashboard;
      case VisualizationType.heatMap:
        return Icons.grid_on;
      case VisualizationType.ganttChart:
        return Icons.view_timeline;
      case VisualizationType.treeMap:
        return Icons.account_tree;
      case VisualizationType.table:
        return Icons.table_chart;
      case VisualizationType.summary:
        return Icons.summarize;
      case VisualizationType.custom:
        return Icons.dashboard_customize;
      default:
        return Icons.bar_chart;
    }
  }
}

/// رسام مخطط خطي مصغر
class MiniLineChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final random = math.Random();
    final path = Path();

    final pointCount = 7;
    final pointWidth = size.width / (pointCount - 1);

    // نقطة البداية
    path.moveTo(0, size.height * 0.7);

    // النقاط الوسطى
    for (int i = 1; i < pointCount - 1; i++) {
      final x = pointWidth * i;
      final y = size.height * (0.2 + random.nextDouble() * 0.6);
      path.lineTo(x, y);
    }

    // نقطة النهاية
    path.lineTo(size.width, size.height * 0.3);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مخطط دائري مصغر
class MiniPieChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم القطاعات
    final segments = [
      {'color': Colors.blue, 'percent': 0.4},
      {'color': Colors.green, 'percent': 0.3},
      {'color': Colors.orange, 'percent': 0.2},
      {'color': Colors.purple, 'percent': 0.1},
    ];

    var startAngle = 0.0;

    for (final segment in segments) {
      final sweepAngle = (segment['percent'] as double) * 2 * math.pi;
      final paint = Paint()
        ..color = segment['color'] as Color
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مخطط مساحي مصغر
class MiniAreaChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    final linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final random = math.Random();
    final path = Path();

    final pointCount = 7;
    final pointWidth = size.width / (pointCount - 1);

    // نقطة البداية
    path.moveTo(0, size.height * 0.7);

    // النقاط الوسطى
    for (int i = 1; i < pointCount - 1; i++) {
      final x = pointWidth * i;
      final y = size.height * (0.2 + random.nextDouble() * 0.6);
      path.lineTo(x, y);
    }

    // نقطة النهاية
    path.lineTo(size.width, size.height * 0.3);

    // إغلاق المسار لإنشاء منطقة
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);

    // رسم الخط فوق المنطقة
    final linePath = Path();
    linePath.moveTo(0, size.height * 0.7);

    for (int i = 1; i < pointCount - 1; i++) {
      final x = pointWidth * i;
      final y = size.height * (0.2 + random.nextDouble() * 0.6);
      linePath.lineTo(x, y);
    }

    linePath.lineTo(size.width, size.height * 0.3);

    canvas.drawPath(linePath, linePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام مخطط مقياس مصغر
class MiniGaugeChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم القوس الخارجي
    final outerPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..strokeWidth = radius * 0.2
      ..style = PaintingStyle.stroke;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      math.pi * 0.8,
      math.pi * 1.4,
      false,
      outerPaint,
    );

    // رسم القوس الداخلي (القيمة)
    final innerPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = radius * 0.2
      ..style = PaintingStyle.stroke;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      math.pi * 0.8,
      math.pi * 0.8, // 60% من القوس الكامل
      false,
      innerPaint,
    );

    // رسم الدائرة المركزية
    final centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.1, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
