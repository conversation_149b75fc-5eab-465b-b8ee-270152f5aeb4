import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

/// مؤشر التحميل المخصص
/// يعرض رسالة تحميل بسيطة بدلاً من المؤشر الدائري
class LoadingIndicator extends StatelessWidget {
  /// الرسالة المعروضة
  final String? message;

  /// حجم الأيقونة (اختياري)
  final double size;

  /// لون النص والأيقونة
  final Color? color;

  /// إنشاء مؤشر التحميل
  const LoadingIndicator({
    super.key,
    this.message,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final displayColor = color ?? AppColors.primary;
    final displayMessage = message ?? 'جاري التحميل...';

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.hourglass_empty,
          size: size,
          color: displayColor,
        ),
        const SizedBox(height: 12),
        Text(
          displayMessage,
          style: AppStyles.bodyMedium.copyWith(
            color: displayColor,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
