import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../services/api/dashboards_api_service.dart';

/// متحكم لوحات المعلومات
class DashboardsController extends GetxController {
  final DashboardsApiService _apiService = DashboardsApiService();

  // قوائم لوحات المعلومات
  final RxList<Dashboard> _allDashboards = <Dashboard>[].obs;
  final RxList<Dashboard> _filteredDashboards = <Dashboard>[].obs;
  final RxList<Dashboard> _myDashboards = <Dashboard>[].obs;

  // لوحة المعلومات الحالية
  final Rx<Dashboard?> _currentDashboard = Rx<Dashboard?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;
  final RxBool _showMyDashboardsOnly = false.obs;

  // Getters
  List<Dashboard> get allDashboards => _allDashboards;
  List<Dashboard> get filteredDashboards => _filteredDashboards;
  List<Dashboard> get myDashboards => _myDashboards;
  Dashboard? get currentDashboard => _currentDashboard.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;
  bool get showMyDashboardsOnly => _showMyDashboardsOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllDashboards();
    loadMyDashboards();
  }

  /// تحميل جميع لوحات المعلومات
  Future<void> loadAllDashboards() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final dashboards = await _apiService.getAllDashboards();
      _allDashboards.assignAll(dashboards);
      _applyFilters();
      debugPrint('تم تحميل ${dashboards.length} لوحة معلومات');
    } catch (e) {
      _error.value = 'خطأ في تحميل لوحات المعلومات: $e';
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل لوحات معلوماتي
  Future<void> loadMyDashboards() async {
    try {
      final dashboards = await _apiService.getMyDashboards();
      _myDashboards.assignAll(dashboards);
      debugPrint('تم تحميل ${dashboards.length} من لوحات معلوماتي');
    } catch (e) {
      debugPrint('خطأ في تحميل لوحات معلوماتي: $e');
    }
  }

  /// الحصول على لوحة معلومات بالمعرف
  Future<void> getDashboardById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final dashboard = await _apiService.getDashboardById(id);
      _currentDashboard.value = dashboard;
      debugPrint('تم تحميل لوحة المعلومات: ${dashboard.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل لوحة المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<bool> createDashboard(Dashboard dashboard) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newDashboard = await _apiService.createDashboard(dashboard);
      _allDashboards.add(newDashboard);
      _applyFilters();
      await loadMyDashboards();
      debugPrint('تم إنشاء لوحة معلومات جديدة: ${newDashboard.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء لوحة المعلومات: $e';
      debugPrint('خطأ في إنشاء لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث لوحة معلومات
  Future<bool> updateDashboard(int id, Dashboard dashboard) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedDashboard = await _apiService.updateDashboard(dashboard);
      final index = _allDashboards.indexWhere((d) => d.id == id);
      if (index != -1) {
        _allDashboards[index] = updatedDashboard;
        _applyFilters();
      }
      await loadMyDashboards();
      debugPrint('تم تحديث لوحة المعلومات: ${dashboard.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث لوحة المعلومات: $e';
      debugPrint('خطأ في تحديث لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف لوحة معلومات
  Future<bool> deleteDashboard(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteDashboard(id);
      _allDashboards.removeWhere((d) => d.id == id);
      _applyFilters();
      await loadMyDashboards();
      debugPrint('تم حذف لوحة المعلومات');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف لوحة المعلومات: $e';
      debugPrint('خطأ في حذف لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// مشاركة لوحة معلومات
  Future<bool> shareDashboard(int dashboardId, int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.shareDashboard(dashboardId, [userId]);
      debugPrint('تم مشاركة لوحة المعلومات');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مشاركة لوحة المعلومات: $e';
      debugPrint('خطأ في مشاركة لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء مشاركة لوحة معلومات
  Future<bool> unshareDashboard(int dashboardId, int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.unshareDashboard(dashboardId, userId);
      debugPrint('تم إلغاء مشاركة لوحة المعلومات');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إلغاء مشاركة لوحة المعلومات: $e';
      debugPrint('خطأ في إلغاء مشاركة لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allDashboards.where((dashboard) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!dashboard.name.toLowerCase().contains(query) &&
            !(dashboard.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !dashboard.isActive) {
        return false;
      }

      // مرشح لوحات معلوماتي فقط
      if (_showMyDashboardsOnly.value) {
        if (!_myDashboards.any((myDashboard) => myDashboard.id == dashboard.id)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredDashboards.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// تعيين مرشح لوحات معلوماتي فقط
  void setMyDashboardsFilter(bool showMyDashboardsOnly) {
    _showMyDashboardsOnly.value = showMyDashboardsOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _showMyDashboardsOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllDashboards(),
      loadMyDashboards(),
    ]);
  }
}
