import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../config/api_config.dart';
import '../storage_service.dart';

/// خدمة API لرفع الصور إلى الخادم
class ImageUploadApiService {
  final StorageService _storageService = StorageService.instance;

  /// رفع صورة الملف الشخصي
  Future<String?> uploadProfileImage(File imageFile, int userId) async {
    try {
      debugPrint('بدء رفع صورة الملف الشخصي للمستخدم: $userId');

      // الحصول على رمز المصادقة
      final token = await _storageService.getToken();
      if (token == null) {
        throw Exception('لم يتم العثور على رمز المصادقة');
      }

      // إنشاء طلب multipart
      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Users/<USER>/upload-avatar');
      final request = http.MultipartRequest('POST', uri);

      // إضافة رؤوس HTTP
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
      });

      // إضافة الملف
      final multipartFile = await http.MultipartFile.fromPath(
        'avatar',
        imageFile.path,
        filename: 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      request.files.add(multipartFile);

      // إرسال الطلب
      debugPrint('إرسال طلب رفع الصورة إلى: $uri');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      debugPrint('استجابة رفع الصورة - Status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        final imageUrl = responseData['imageUrl'] as String?;
        
        if (imageUrl != null) {
          debugPrint('تم رفع الصورة بنجاح: $imageUrl');
          return imageUrl;
        } else {
          throw Exception('لم يتم إرجاع رابط الصورة من الخادم');
        }
      } else {
        final errorMessage = response.body.isNotEmpty 
            ? response.body 
            : 'خطأ في رفع الصورة (${response.statusCode})';
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('خطأ في رفع صورة الملف الشخصي: $e');
      rethrow;
    }
  }

  /// رفع صورة عامة
  Future<String?> uploadImage(File imageFile, {String? folder}) async {
    try {
      debugPrint('بدء رفع صورة إلى المجلد: ${folder ?? 'default'}');

      // الحصول على رمز المصادقة
      final token = await _storageService.getToken();
      if (token == null) {
        throw Exception('لم يتم العثور على رمز المصادقة');
      }

      // إنشاء طلب multipart
      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Upload/image');
      final request = http.MultipartRequest('POST', uri);

      // إضافة رؤوس HTTP
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
      });

      // إضافة الملف
      final multipartFile = await http.MultipartFile.fromPath(
        'image',
        imageFile.path,
        filename: 'image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      request.files.add(multipartFile);

      // إضافة المجلد إذا تم تحديده
      if (folder != null) {
        request.fields['folder'] = folder;
      }

      // إرسال الطلب
      debugPrint('إرسال طلب رفع الصورة إلى: $uri');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      debugPrint('استجابة رفع الصورة - Status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        final imageUrl = responseData['imageUrl'] as String?;
        
        if (imageUrl != null) {
          debugPrint('تم رفع الصورة بنجاح: $imageUrl');
          return imageUrl;
        } else {
          throw Exception('لم يتم إرجاع رابط الصورة من الخادم');
        }
      } else {
        final errorMessage = response.body.isNotEmpty 
            ? response.body 
            : 'خطأ في رفع الصورة (${response.statusCode})';
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('خطأ في رفع الصورة: $e');
      rethrow;
    }
  }

  /// حذف صورة من الخادم
  Future<bool> deleteImage(String imageUrl) async {
    try {
      debugPrint('بدء حذف الصورة: $imageUrl');

      // الحصول على رمز المصادقة
      final token = await _storageService.getToken();
      if (token == null) {
        throw Exception('لم يتم العثور على رمز المصادقة');
      }

      // إنشاء الطلب
      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Upload/delete');
      final response = await http.delete(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'imageUrl': imageUrl}),
      );

      debugPrint('استجابة حذف الصورة - Status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        debugPrint('تم حذف الصورة بنجاح');
        return true;
      } else {
        debugPrint('فشل في حذف الصورة: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في حذف الصورة: $e');
      return false;
    }
  }
}
