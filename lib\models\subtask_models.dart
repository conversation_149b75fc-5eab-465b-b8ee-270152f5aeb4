import 'package:flutter/foundation.dart';
import 'task_models.dart';

/// نموذج المهمة الفرعية - متطابق مع ASP.NET Core API
class Subtask {
  final int id;
  final int taskId;
  final String title;
  final bool isCompleted;
  final int createdAt;
  final int? completedAt;

  // Navigation properties
  final Task? task;

  const Subtask({
    required this.id,
    required this.taskId,
    required this.title,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
    this.task,
  });

  factory Subtask.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔍 تحليل Subtask من JSON: ${json.keys}');

      // معالجة آمنة للحقول المطلوبة
      final id = _parseIntField(json, ['id', 'Id']);
      final taskId = _parseIntField(json, ['taskId', 'TaskId']);
      final title = _parseStringField(json, ['title', 'Title']);
      final isCompleted = _parseBoolField(json, ['isCompleted', 'IsCompleted']);
      final createdAt = _parseIntField(json, ['createdAt', 'CreatedAt']);
      final completedAt = _parseOptionalIntField(json, ['completedAt', 'CompletedAt']);

      if (id == null || taskId == null || title == null || createdAt == null) {
        throw FormatException('حقول مطلوبة مفقودة: id=$id, taskId=$taskId, title=$title, createdAt=$createdAt');
      }

      return Subtask(
        id: id,
        taskId: taskId,
        title: title,
        isCompleted: isCompleted,
        createdAt: createdAt,
        completedAt: completedAt,
        task: _parseTask(json),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحليل Subtask من JSON: $e');
      debugPrint('📄 البيانات المستلمة: $json');
      rethrow;
    }
  }

  /// تحليل آمن للحقول الرقمية
  static int? _parseIntField(Map<String, dynamic> json, List<String> keys) {
    for (final key in keys) {
      final value = json[key];
      if (value != null) {
        if (value is int) return value;
        if (value is num) return value.toInt();
        if (value is String) return int.tryParse(value);
      }
    }
    return null;
  }

  /// تحليل آمن للحقول الرقمية الاختيارية
  static int? _parseOptionalIntField(Map<String, dynamic> json, List<String> keys) {
    for (final key in keys) {
      final value = json[key];
      if (value != null) {
        if (value is int) return value;
        if (value is num) return value.toInt();
        if (value is String) return int.tryParse(value);
      }
    }
    return null;
  }

  /// تحليل آمن للحقول النصية
  static String? _parseStringField(Map<String, dynamic> json, List<String> keys) {
    for (final key in keys) {
      final value = json[key];
      if (value != null) {
        return value.toString();
      }
    }
    return null;
  }

  /// تحليل آمن للحقول المنطقية
  static bool _parseBoolField(Map<String, dynamic> json, List<String> keys) {
    for (final key in keys) {
      final value = json[key];
      if (value != null) {
        if (value is bool) return value;
        if (value is String) return value.toLowerCase() == 'true';
        if (value is num) return value != 0;
      }
    }
    return false;
  }

  /// تحليل آمن لبيانات المهمة
  static Task? _parseTask(Map<String, dynamic> json) {
    try {
      final taskData = json['task'] ?? json['Task'];
      if (taskData == null) {
        debugPrint('🔍 لا توجد بيانات مهمة في الاستجابة');
        return null;
      }

      if (taskData is Map<String, dynamic>) {
        // تجنب circular dependency عن طريق عدم تحليل المهام الفرعية داخل المهمة
        debugPrint('🔍 تحليل بيانات المهمة (بدون المهام الفرعية لتجنب التكرار)');
        final taskDataCopy = Map<String, dynamic>.from(taskData);
        taskDataCopy.remove('subtasks'); // إزالة المهام الفرعية لتجنب circular dependency
        return Task.fromJson(taskDataCopy);
      } else {
        debugPrint('⚠️ بيانات المهمة ليست من نوع Map: ${taskData.runtimeType}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحليل بيانات المهمة: $e');
      // في حالة فشل تحليل المهمة، نعيد null بدلاً من إيقاف العملية
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'title': title,
      'isCompleted': isCompleted,
      'createdAt': createdAt,
      'completedAt': completedAt,
    };
  }

  Subtask copyWith({
    int? id,
    int? taskId,
    String? title,
    bool? isCompleted,
    int? createdAt,
    int? completedAt,
    Task? task,
  }) {
    return Subtask(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      task: task ?? this.task,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الإكمال كـ DateTime
  DateTime? get completedAtDateTime => completedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(completedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Subtask(id: $id, title: $title, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subtask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مهمة فرعية
class CreateSubtaskRequest {
  final int taskId;
  final String title;

  const CreateSubtaskRequest({
    required this.taskId,
    required this.title,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'title': title,
    };
  }
}

/// نموذج طلب تحديث مهمة فرعية
class UpdateSubtaskRequest {
  final String? title;
  final bool? isCompleted;

  const UpdateSubtaskRequest({
    this.title,
    this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (isCompleted != null) json['isCompleted'] = isCompleted;
    return json;
  }
}

/// نموذج طلب تبديل حالة إكمال المهمة الفرعية
class ToggleSubtaskCompletionRequest {
  final bool isCompleted;

  const ToggleSubtaskCompletionRequest({
    required this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    return {
      'isCompleted': isCompleted,
    };
  }
}

/// نموذج استجابة قائمة المهام الفرعية
class SubtasksResponse {
  final List<Subtask> subtasks;
  final int totalCount;
  final int completedCount;
  final int pendingCount;

  const SubtasksResponse({
    required this.subtasks,
    required this.totalCount,
    required this.completedCount,
    required this.pendingCount,
  });

  factory SubtasksResponse.fromJson(Map<String, dynamic> json) {
    return SubtasksResponse(
      subtasks: (json['subtasks'] as List)
          .map((s) => Subtask.fromJson(s as Map<String, dynamic>))
          .toList(),
      totalCount: json['totalCount'] as int,
      completedCount: json['completedCount'] as int,
      pendingCount: json['pendingCount'] as int,
    );
  }

  /// نسبة الإكمال
  double get completionPercentage => 
      totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;

  /// التحقق من اكتمال جميع المهام الفرعية
  bool get allCompleted => totalCount > 0 && completedCount == totalCount;

  /// التحقق من عدم وجود مهام فرعية مكتملة
  bool get noneCompleted => completedCount == 0;
}

/// نموذج إحصائيات المهام الفرعية
class SubtaskStats {
  final int taskId;
  final int totalSubtasks;
  final int completedSubtasks;
  final int pendingSubtasks;
  final double completionPercentage;
  final DateTime? lastUpdated;

  const SubtaskStats({
    required this.taskId,
    required this.totalSubtasks,
    required this.completedSubtasks,
    required this.pendingSubtasks,
    required this.completionPercentage,
    this.lastUpdated,
  });

  factory SubtaskStats.fromJson(Map<String, dynamic> json) {
    return SubtaskStats(
      taskId: json['taskId'] as int,
      totalSubtasks: json['totalSubtasks'] as int,
      completedSubtasks: json['completedSubtasks'] as int,
      pendingSubtasks: json['pendingSubtasks'] as int,
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastUpdated'] as int) * 1000)
          : null,
    );
  }

  /// التحقق من اكتمال جميع المهام الفرعية
  bool get isFullyCompleted => totalSubtasks > 0 && completedSubtasks == totalSubtasks;

  /// التحقق من عدم البدء في أي مهمة فرعية
  bool get isNotStarted => completedSubtasks == 0;

  /// التحقق من وجود تقدم جزئي
  bool get isInProgress => completedSubtasks > 0 && completedSubtasks < totalSubtasks;
}
