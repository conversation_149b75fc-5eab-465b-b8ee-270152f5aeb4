import 'package:flutter/material.dart';

import '../performance_reports/workload_distribution_report.dart';
import '../../services/api/task_api_service.dart';
import '../../services/api/user_api_service.dart';
import '../../services/api/departments_api_service.dart';

class DebugWorkloadReportScreen extends StatefulWidget {
  const DebugWorkloadReportScreen({super.key});

  @override
  State<DebugWorkloadReportScreen> createState() => _DebugWorkloadReportScreenState();
}

class _DebugWorkloadReportScreenState extends State<DebugWorkloadReportScreen> {
  final TaskApiService _taskApiService = TaskApiService();
  final UserApiService _userApiService = UserApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();

  String _debugOutput = 'اضغط على "فحص البيانات" لبدء التحليل...';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فحص بيانات التقرير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: _debugData,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.search),
                    label: const Text('فحص البيانات'),
                    onPressed: _isLoading ? null : _debugData,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح النتائج'),
                    onPressed: () {
                      setState(() {
                        _debugOutput = 'تم مسح النتائج...';
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const LinearProgressIndicator(),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _debugOutput,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _debugData() async {
    setState(() {
      _isLoading = true;
      _debugOutput = 'جاري فحص البيانات...\n\n';
    });

    try {
      final buffer = StringBuffer();
      buffer.writeln('=== فحص شامل لبيانات التقرير ===\n');

      // 1. فحص المهام
      buffer.writeln('1️⃣ فحص المهام:');
      final tasks = await _taskApiService.getAllTasks();
      buffer.writeln('   📋 إجمالي المهام: ${tasks.length}');
      
      if (tasks.isNotEmpty) {
        final firstTask = tasks.first;
        buffer.writeln('   📝 مثال على مهمة:');
        buffer.writeln('      - ID: ${firstTask.id}');
        buffer.writeln('      - العنوان: ${firstTask.title}');
        buffer.writeln('      - الحالة: ${firstTask.status}');
        buffer.writeln('      - المنشئ ID: ${firstTask.creatorId}');
        buffer.writeln('      - المسند إليه ID: ${firstTask.assigneeId}');
        buffer.writeln('      - تاريخ الإنشاء: ${firstTask.createdAtDateTime}');
        buffer.writeln('      - محذوف: ${firstTask.isDeleted}');
        
        // إحصائيات الحالات
        final statusCounts = <String, int>{};
        for (final task in tasks) {
          statusCounts[task.status] = (statusCounts[task.status] ?? 0) + 1;
        }
        buffer.writeln('   📊 توزيع الحالات:');
        statusCounts.forEach((status, count) {
          buffer.writeln('      - $status: $count');
        });
        
        // المهام المحذوفة
        final deletedCount = tasks.where((t) => t.isDeleted).length;
        buffer.writeln('   🗑️ المهام المحذوفة: $deletedCount');
        
        // المهام بدون تخصيص
        final unassignedCount = tasks.where((t) => t.assigneeId == null).length;
        buffer.writeln('   ❓ المهام غير المخصصة: $unassignedCount');
        
        // المهام في الشهر الحالي (معلومة إضافية)
        final currentMonthTasks = tasks.where((task) {
          try {
            final now = DateTime.now();
            final taskDate = task.createdAtDateTime;
            return taskDate.year == now.year && taskDate.month == now.month;
          } catch (e) {
            return false;
          }
        }).length;
        buffer.writeln('   � المهام في الشهر الحالي: $currentMonthTasks');
      }

      buffer.writeln('\n');

      // 2. فحص المستخدمين
      buffer.writeln('2️⃣ فحص المستخدمين:');
      final users = await _userApiService.getAllUsers();
      buffer.writeln('   👥 إجمالي المستخدمين: ${users.length}');
      
      if (users.isNotEmpty) {
        final firstUser = users.first;
        buffer.writeln('   👤 مثال على مستخدم:');
        buffer.writeln('      - ID: ${firstUser.id}');
        buffer.writeln('      - الاسم: ${firstUser.name}');
        buffer.writeln('      - القسم ID: ${firstUser.departmentId}');
        
        // المستخدمين المرتبطين بالمهام
        final userIds = users.map((u) => u.id).toSet();
        final taskUserIds = <int>{};
        for (final task in tasks) {
          // إضافة معرف المنشئ (دائماً موجود)
          taskUserIds.add(task.creatorId);
          // إضافة معرف المسند إليه (اختياري)
          final assigneeId = task.assigneeId;
          if (assigneeId != null) taskUserIds.add(assigneeId);
        }
        final connectedUsers = userIds.intersection(taskUserIds);
        buffer.writeln('   🔗 المستخدمين المرتبطين بمهام: ${connectedUsers.length}');
        
        // المستخدمين بدون مهام
        final usersWithoutTasks = userIds.difference(taskUserIds);
        buffer.writeln('   ❌ المستخدمين بدون مهام: ${usersWithoutTasks.length}');
      }

      buffer.writeln('\n');

      // 3. فحص الأقسام
      buffer.writeln('3️⃣ فحص الأقسام:');
      final departments = await _departmentsApiService.getAllDepartments();
      buffer.writeln('   🏢 إجمالي الأقسام: ${departments.length}');
      
      if (departments.isNotEmpty) {
        final firstDept = departments.first;
        buffer.writeln('   🏛️ مثال على قسم:');
        buffer.writeln('      - ID: ${firstDept.id}');
        buffer.writeln('      - الاسم: ${firstDept.name}');
      }

      buffer.writeln('\n');

      // 4. تشغيل التقرير واختباره
      buffer.writeln('4️⃣ اختبار إنشاء التقرير:');
      // فحص سلامة البيانات قبل إنشاء التقرير
      if (tasks.isEmpty && users.isEmpty && departments.isEmpty) {
        buffer.writeln('   ⚠️ جميع البيانات فارغة - لن يكون التقرير مفيداً');
      }
      
      late WorkloadDistributionReport report;
      try {
        report = WorkloadDistributionReport.fromTasks(
          tasks,
          users,
          departments,
        );
      } catch (reportError) {
        buffer.writeln('   ❌ فشل في إنشاء التقرير: $reportError');
        rethrow; // إعادة رمي الخطأ للمعالج الخارجي
      }
      
      buffer.writeln('   ✅ تم إنشاء التقرير بنجاح!');
      buffer.writeln('   📊 نتائج التقرير:');
      buffer.writeln('      - إجمالي المهام: ${report.totalTasks}');
      buffer.writeln('      - المستخدمين النشطين: ${report.totalActiveUsers}');
      buffer.writeln('      - متوسط المهام/مستخدم: ${report.averageWorkloadPerUser.toStringAsFixed(2)}');
      buffer.writeln('      - معدل الاستغلال العام: ${report.systemUtilization.toStringAsFixed(2)}%');
      
      buffer.writeln('\n   👥 تفاصيل المستخدمين:');
      for (final userWorkload in report.userWorkloads.take(5)) {
        buffer.writeln('      - ${userWorkload.user.name}: ${userWorkload.totalTasks} مهمة');
        buffer.writeln('        (نشطة: ${userWorkload.activeTasks}, مكتملة: ${userWorkload.completedTasks})');
      }
      
      if (report.userWorkloads.length > 5) {
        buffer.writeln('      ... و ${report.userWorkloads.length - 5} مستخدم آخر');
      }
      
      buffer.writeln('\n   🏢 تفاصيل الأقسام:');
      for (final deptWorkload in report.departmentWorkloads.take(3)) {
        buffer.writeln('      - ${deptWorkload.department.name}: ${deptWorkload.totalTasks} مهمة');
        buffer.writeln('        (متوسط: ${deptWorkload.averageTasksPerUser.toStringAsFixed(2)} مهمة/مستخدم)');
      }

      buffer.writeln('\n');

      // 5. التحقق من وجود مشاكل
      buffer.writeln('5️⃣ فحص المشاكل المحتملة:');
      
      final issues = <String>[];
      
      if (tasks.isEmpty) {
        issues.add('❌ لا توجد مهام في النظام');
      }
      
      if (users.isEmpty) {
        issues.add('❌ لا توجد مستخدمين في النظام');
      }
      
      if (report.totalTasks == 0) {
        issues.add('❌ التقرير يُظهر 0 مهام');
      }
      
      if (report.totalActiveUsers == 0) {
        issues.add('❌ لا يوجد مستخدمين نشطين');
      }
      
      // فحص المهام في الشهر الحالي
      final tasksInCurrentMonth = tasks.where((task) {
        try {
          final now = DateTime.now();
          final taskDate = task.createdAtDateTime;
          return taskDate.year == now.year && taskDate.month == now.month;
        } catch (e) {
          // في حالة وجود خطأ في تاريخ المهمة، نتجاهلها
          return false;
        }
      }).length;
      
      if (tasksInCurrentMonth == 0) {
        issues.add('⚠️ لا توجد مهام في الشهر الحالي (التقرير يُصفي حسب الشهر الحالي افتراضياً)');
      }
      
      if (issues.isEmpty) {
        buffer.writeln('   ✅ لم يتم العثور على مشاكل واضحة');
      } else {
        buffer.writeln('   🔍 المشاكل المكتشفة:');
        for (final issue in issues) {
          buffer.writeln('      $issue');
        }
      }
      
      buffer.writeln('\n=== انتهاء الفحص ===');

      setState(() {
        _debugOutput = buffer.toString();
        _isLoading = false;
      });

    } catch (e, stackTrace) {
      setState(() {
        _debugOutput = 'خطأ في فحص البيانات:\n\n$e\n\nStack Trace:\n$stackTrace';
        _isLoading = false;
      });
    }
  }
}