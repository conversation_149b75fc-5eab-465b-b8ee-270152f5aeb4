using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;
using System.Threading.Tasks; // إضافة هذا السطر لحل الغموض

namespace webApi.Hubs
{
    /// <summary>
    /// Hub لتعليقات المهام الفورية مع معالجة أخطاء محسنة
    /// </summary>
    public class TaskCommentsHub : Hub
    {
        private readonly ILogger<TaskCommentsHub> _logger;
        private readonly TasksDbContext _context;
        private readonly INotificationService _notificationService;

        public TaskCommentsHub(
            ILogger<TaskCommentsHub> logger,
            TasksDbContext context,
            INotificationService notificationService)
        {
            _logger = logger;
            _context = context;
            _notificationService = notificationService;
        }

        /// <summary>
        /// عند اتصال عميل جديد
        /// </summary>
        public override System.Threading.Tasks.Task OnConnectedAsync()
        {
            _logger.LogInformation("عميل جديد متصل بـ TaskCommentsHub: {ConnectionId}", Context.ConnectionId);
            return base.OnConnectedAsync();
        }

        /// <summary>
        /// عند قطع اتصال عميل
        /// </summary>
        public override System.Threading.Tasks.Task OnDisconnectedAsync(Exception? exception)
        {
            _logger.LogInformation("عميل منقطع من TaskCommentsHub: {ConnectionId}, السبب: {Exception}",
                Context.ConnectionId, exception?.Message);
            return base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// الانضمام لمجموعة تعليقات مهمة مع معالجة أخطاء
        /// </summary>
        public async System.Threading.Tasks.Task<object> JoinTaskCommentsGroup(string taskId)
        {
            try
            {
                // التحقق من صحة معرف المهمة
                if (string.IsNullOrWhiteSpace(taskId))
                {
                    _logger.LogWarning("محاولة انضمام بمعرف مهمة فارغ من {ConnectionId}", Context.ConnectionId);
                    await Clients.Caller.SendAsync("Error", new { Message = "معرف المهمة مطلوب" });
                    return new { Success = false, Message = "معرف المهمة مطلوب" };
                }

                var groupName = $"TaskComments_{taskId}";
                await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

                _logger.LogInformation("المستخدم {ConnectionId} انضم لمجموعة تعليقات المهمة {TaskId}",
                    Context.ConnectionId, taskId);

                return new { Success = true, Message = "تم الانضمام بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في انضمام المستخدم لمجموعة تعليقات المهمة {TaskId}", taskId);
                await Clients.Caller.SendAsync("Error", new { Message = "خطأ في الانضمام للمجموعة" });
                return new { Success = false, Message = "خطأ في الانضمام للمجموعة" };
            }
        }

        /// <summary>
        /// مغادرة مجموعة تعليقات مهمة مع معالجة أخطاء
        /// </summary>
        public async System.Threading.Tasks.Task<object> LeaveTaskCommentsGroup(string taskId)
        {
            try
            {
                var groupName = $"TaskComments_{taskId}";
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

                _logger.LogInformation("المستخدم {ConnectionId} غادر مجموعة تعليقات المهمة {TaskId}",
                    Context.ConnectionId, taskId);

                return new { Success = true, Message = "تم المغادرة بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مغادرة المستخدم لمجموعة تعليقات المهمة {TaskId}", taskId);
                return new { Success = false, Message = "خطأ في المغادرة" };
            }
        }

        /// <summary>
        /// بث تعليق جديد مع معالجة أخطاء
        /// </summary>
        public async System.Threading.Tasks.Task<object> SendTaskComment(string taskId, TaskComment comment)
        {
            try
            {
                // التحقق من صحة المعاملات
                if (string.IsNullOrWhiteSpace(taskId) || comment == null)
                {
                    _logger.LogWarning("محاولة إرسال تعليق بمعاملات غير صالحة من {ConnectionId}", Context.ConnectionId);
                    await Clients.Caller.SendAsync("Error", new { Message = "بيانات التعليق غير صالحة" });
                    return new { Success = false, Message = "بيانات التعليق غير صالحة" };
                }

                var groupName = $"TaskComments_{taskId}";
                // استخدام نفس اسم الحدث الذي ينتظره الفرونت إند
                await Clients.Group(groupName).SendAsync("ReceiveTaskComment", comment);

                // إنشاء إشعارات للمستخدمين المعنيين بالمهمة
                await CreateCommentNotifications(taskId, comment);

                _logger.LogInformation("تم إرسال تعليق جديد للمهمة {TaskId} من {ConnectionId}", taskId, Context.ConnectionId);
                return new { Success = true, Message = "تم إرسال التعليق بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال تعليق للمهمة {TaskId}", taskId);
                await Clients.Caller.SendAsync("Error", new { Message = "خطأ في إرسال التعليق" });
                return new { Success = false, Message = "خطأ في إرسال التعليق" };
            }
        }

        /// <summary>
        /// بث تحديث تعليق مع معالجة أخطاء
        /// </summary>
        public async System.Threading.Tasks.Task<object> UpdateTaskComment(string taskId, TaskComment comment)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(taskId) || comment == null)
                {
                    _logger.LogWarning("محاولة تحديث تعليق بمعاملات غير صالحة من {ConnectionId}", Context.ConnectionId);
                    return new { Success = false, Message = "بيانات التعليق غير صالحة" };
                }

                var groupName = $"TaskComments_{taskId}";
                // استخدام اسم حدث متطابق مع الفرونت إند
                await Clients.Group(groupName).SendAsync("TaskCommentUpdated", comment);

                _logger.LogInformation("تم تحديث تعليق للمهمة {TaskId} من {ConnectionId}", taskId, Context.ConnectionId);
                return new { Success = true, Message = "تم تحديث التعليق بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث تعليق للمهمة {TaskId}", taskId);
                return new { Success = false, Message = "خطأ في تحديث التعليق" };
            }
        }

        /// <summary>
        /// بث حذف تعليق مع معالجة أخطاء
        /// </summary>
        public async System.Threading.Tasks.Task<object> DeleteTaskComment(string taskId, int commentId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(taskId) || commentId <= 0)
                {
                    _logger.LogWarning("محاولة حذف تعليق بمعاملات غير صالحة من {ConnectionId}", Context.ConnectionId);
                    return new { Success = false, Message = "معاملات غير صالحة" };
                }

                var groupName = $"TaskComments_{taskId}";
                // استخدام اسم حدث متطابق مع الفرونت إند
                await Clients.Group(groupName).SendAsync("TaskCommentDeleted", new { CommentId = commentId });

                _logger.LogInformation("تم حذف تعليق {CommentId} للمهمة {TaskId} من {ConnectionId}",
                    commentId, taskId, Context.ConnectionId);

                return new { Success = true, Message = "تم حذف التعليق بنجاح" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف تعليق {CommentId} للمهمة {TaskId}", commentId, taskId);
                return new { Success = false, Message = "خطأ في حذف التعليق" };
            }
        }

        /// <summary>
        /// فحص حالة الاتصال
        /// </summary>
        public async System.Threading.Tasks.Task<object> Ping()
        {
            try
            {
                await Clients.Caller.SendAsync("Pong", new {
                    ConnectionId = Context.ConnectionId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                });
                _logger.LogDebug("تم الرد على ping من {ConnectionId}", Context.ConnectionId);

                return new {
                    Success = true,
                    ConnectionId = Context.ConnectionId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الرد على ping من {ConnectionId}", Context.ConnectionId);
                return new { Success = false, Message = "خطأ في ping" };
            }
        }

        /// <summary>
        /// إنشاء إشعارات للمستخدمين المعنيين بتعليق جديد
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="comment">التعليق الجديد</param>
        private async System.Threading.Tasks.Task CreateCommentNotifications(string taskId, TaskComment comment)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(taskId) || comment == null || !int.TryParse(taskId, out int taskIdInt))
                {
                    _logger.LogWarning("معاملات غير صالحة لإنشاء إشعارات التعليق");
                    return;
                }

                // الحصول على المهمة
                var task = await _context.Tasks
                    .Include(t => t.Creator)
                    .Include(t => t.Assignee)
                    .FirstOrDefaultAsync(t => t.Id == taskIdInt);

                if (task == null)
                {
                    _logger.LogWarning("المهمة غير موجودة: {TaskId}", taskId);
                    return;
                }

                // قائمة المستخدمين الذين سيتلقون إشعارات
                var userIds = new List<int>();

                // إضافة منشئ المهمة إذا كان مختلفاً عن كاتب التعليق
                if (task.CreatorId != comment.UserId)
                {
                    userIds.Add(task.CreatorId);
                }

                // إضافة المسند له إذا كان موجوداً ومختلفاً عن كاتب التعليق
                if (task.AssigneeId.HasValue && task.AssigneeId.Value != comment.UserId)
                {
                    userIds.Add(task.AssigneeId.Value);
                }

                // الحصول على المستخدمين الذين لهم وصول للمهمة
                var accessUsers = await _context.TaskAccessUsers
                    .Where(au => au.TaskId == taskIdInt && au.UserId != comment.UserId)
                    .Select(au => au.UserId)
                    .ToListAsync();

                // إضافة المستخدمين الذين لهم وصول للمهمة
                foreach (var userId in accessUsers)
                {
                    if (!userIds.Contains(userId))
                    {
                        userIds.Add(userId);
                    }
                }

                // إنشاء إشعارات للمستخدمين المعنيين
                if (userIds.Any())
                {
                    var commentUser = await _context.Users.FindAsync(comment.UserId);
                    string userName = commentUser?.Name ?? "مستخدم";

                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        "تعليق جديد على مهمة",
                        $"قام {userName} بإضافة تعليق على المهمة: {task.Title}",
                        "comment_added",
                        task.Id
                    );

                    _logger.LogInformation("تم إنشاء إشعارات لـ {Count} مستخدم للتعليق {CommentId} على المهمة {TaskId}",
                        userIds.Count, comment.Id, taskId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء إشعارات للتعليق على المهمة {TaskId}", taskId);
            }
        }
    }
}
