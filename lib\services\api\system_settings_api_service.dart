import 'package:flutter/foundation.dart';
import '../../models/system_setting_models.dart';
import 'api_service.dart';

/// خدمة API لإعدادات النظام - متطابقة مع ASP.NET Core API
class SystemSettingsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع إعدادات النظام
  Future<List<SystemSetting>> getAllSettings() async {
    try {
      final response = await _apiService.get('/api/SystemSettings');
      return _apiService.handleListResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات النظام: $e');
      rethrow;
    }
  }

  /// الحصول على إعداد نظام بواسطة المعرف
  Future<SystemSetting?> getSettingById(int id) async {
    try {
      final response = await _apiService.get('/api/SystemSettings/$id');
      return _apiService.handleResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعداد النظام $id: $e');
      return null;
    }
  }

  /// الحصول على قيمة إعداد نظام بالمفتاح
  Future<String?> getSettingValue(String key) async {
    try {
      final response = await _apiService.get('/api/SystemSettings/value/$key');
      if (response.statusCode == 200) {
        return response.body;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة الإعداد $key: $e');
      return null;
    }
  }

  /// الحصول على إعداد نظام بالمفتاح
  Future<SystemSetting?> getSettingByKey(String key) async {
    try {
      final response = await _apiService.get('/api/SystemSettings/key/$key');
      return _apiService.handleResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعداد النظام بالمفتاح $key: $e');
      return null;
    }
  }

  /// الحصول على إعدادات النظام حسب المجموعة
  Future<List<SystemSetting>> getSettingsByGroup(String group) async {
    try {
      final response = await _apiService.get('/api/SystemSettings/group/$group');
      return _apiService.handleListResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات المجموعة $group: $e');
      rethrow;
    }
  }

  /// إنشاء إعداد نظام جديد
  Future<SystemSetting> createSetting(CreateSystemSettingRequest request) async {
    try {
      final response = await _apiService.post(
        '/api/SystemSettings',
        request.toJson(),
      );
      return _apiService.handleResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إعداد النظام: $e');
      rethrow;
    }
  }

  /// تحديث إعداد نظام
  Future<SystemSetting> updateSetting(int id, UpdateSystemSettingRequest request) async {
    try {
      final response = await _apiService.put(
        '/api/SystemSettings/$id',
        request.toJson(),
      );
      return _apiService.handleResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث إعداد النظام $id: $e');
      rethrow;
    }
  }

  /// تحديث قيمة إعداد نظام بالمفتاح
  Future<bool> updateSettingValue(String key, String value) async {
    try {
      final response = await _apiService.patch(
        '/api/SystemSettings/key/$key',
        {'value': value},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث قيمة الإعداد $key: $e');
      return false;
    }
  }

  /// حذف إعداد نظام
  Future<bool> deleteSetting(int id) async {
    try {
      final response = await _apiService.delete('/api/SystemSettings/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف إعداد النظام $id: $e');
      return false;
    }
  }

  /// حذف إعداد نظام بالمفتاح
  Future<bool> deleteSettingByKey(String key) async {
    try {
      final response = await _apiService.delete('/api/SystemSettings/key/$key');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف إعداد النظام بالمفتاح $key: $e');
      return false;
    }
  }

  /// البحث في إعدادات النظام
  Future<List<SystemSetting>> searchSettings(String query) async {
    try {
      final response = await _apiService.get('/api/SystemSettings/search?query=${Uri.encodeComponent(query)}');
      return _apiService.handleListResponse<SystemSetting>(
        response,
        (json) => SystemSetting.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في إعدادات النظام: $e');
      rethrow;
    }
  }

  /// الحصول على مجموعات الإعدادات المتاحة
  Future<List<String>> getAvailableGroups() async {
    try {
      final response = await _apiService.get('/api/SystemSettings/groups');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مجموعات الإعدادات: $e');
      return [];
    }
  }

  /// تصدير إعدادات النظام
  Future<Map<String, dynamic>> exportSettings({String? group}) async {
    try {
      var url = '/api/SystemSettings/export';
      if (group != null) {
        url += '?group=${Uri.encodeComponent(group)}';
      }
      
      final response = await _apiService.get(url);
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير إعدادات النظام: $e');
      rethrow;
    }
  }

  /// استيراد إعدادات النظام
  Future<bool> importSettings(Map<String, dynamic> settings, {bool overwrite = false}) async {
    try {
      final response = await _apiService.post(
        '/api/SystemSettings/import?overwrite=$overwrite',
        settings,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد إعدادات النظام: $e');
      return false;
    }
  }

  /// إعادة تعيين إعدادات النظام للقيم الافتراضية
  Future<bool> resetToDefaults({String? group}) async {
    try {
      var url = '/api/SystemSettings/reset-defaults';
      if (group != null) {
        url += '?group=${Uri.encodeComponent(group)}';
      }
      
      final response = await _apiService.post(url, {});
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين إعدادات النظام: $e');
      return false;
    }
  }

  /// التحقق من صحة إعداد النظام
  Future<Map<String, dynamic>> validateSetting(String key, String value) async {
    try {
      final response = await _apiService.post(
        '/api/SystemSettings/validate',
        {
          'key': key,
          'value': value,
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الإعداد: $e');
      return {'isValid': false, 'error': e.toString()};
    }
  }

  /// الحصول على إحصائيات إعدادات النظام
  Future<Map<String, dynamic>> getSettingsStatistics() async {
    try {
      final response = await _apiService.get('/api/SystemSettings/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الإعدادات: $e');
      return {};
    }
  }

  /// تحديث إعدادات متعددة دفعة واحدة
  Future<bool> updateMultipleSettings(Map<String, String> settings) async {
    try {
      final response = await _apiService.put(
        '/api/SystemSettings/batch-update',
        settings,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث الإعدادات المتعددة: $e');
      return false;
    }
  }

  /// الحصول على سجل تغييرات الإعدادات
  Future<List<Map<String, dynamic>>> getSettingsHistory({String? key, int? limit}) async {
    try {
      var url = '/api/SystemSettings/history';
      final params = <String>[];
      if (key != null) params.add('key=${Uri.encodeComponent(key)}');
      if (limit != null) params.add('limit=$limit');
      if (params.isNotEmpty) url += '?${params.join('&')}';
      
      final response = await _apiService.get(url);
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل تغييرات الإعدادات: $e');
      return [];
    }
  }
}
