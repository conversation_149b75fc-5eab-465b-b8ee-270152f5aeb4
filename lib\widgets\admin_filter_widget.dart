import 'package:flutter/material.dart';

/// ودجت فلتر إداري عام (بحث أو اختيار فئة)
/// يمكن استخدامه في جميع الشاشات الإدارية لتوحيد منطق الفلاتر
class AdminFilterWidget extends StatelessWidget {
  final String? searchLabel;
  final ValueChanged<String>? onSearchChanged;
  final String? searchValue;
  final String? dropdownLabel;
  final List<DropdownMenuItem<String?>>? dropdownItems;
  final String? dropdownValue;
  final ValueChanged<String?>? onDropdownChanged;

  const AdminFilterWidget({
    super.key,
    this.searchLabel,
    this.onSearchChanged,
    this.searchValue,
    this.dropdownLabel,
    this.dropdownItems,
    this.dropdownValue,
    this.onDropdownChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            if (searchLabel != null && onSearchChanged != null)
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    labelText: searchLabel,
                    prefixIcon: const Icon(Icons.search),
                  ),
                  onChanged: onSearchChanged,
                  controller: TextEditingController(text: searchValue),
                ),
              ),
            if (dropdownLabel != null && dropdownItems != null && onDropdownChanged != null) ...[
              if (searchLabel != null) const SizedBox(width: 16),
              Text(dropdownLabel!),
              const SizedBox(width: 8),
              DropdownButton<String?>(
                value: dropdownValue,
                hint: Text(dropdownLabel!),
                items: dropdownItems,
                onChanged: onDropdownChanged,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
