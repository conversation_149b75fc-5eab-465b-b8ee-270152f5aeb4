import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../routes/app_routes.dart';
import '../services/unified_permission_service.dart';

/// وسطاء الصلاحيات الموحد
/// يتحقق من صلاحيات المستخدم قبل الوصول للصفحات المحمية
class UnifiedPermissionMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    // التحقق من وجود متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      debugPrint('🔒 AuthController غير مسجل - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    final authController = Get.find<AuthController>();

    // التحقق من تسجيل الدخول
    if (!authController.isLoggedIn) {
      debugPrint('🔒 المستخدم غير مسجل الدخول - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    // التحقق من وجود المستخدم الحالي
    if (authController.currentUser.value == null) {
      debugPrint('🔒 لا يوجد مستخدم حالي - إعادة توجيه لصفحة تسجيل الدخول');
      return const RouteSettings(name: AppRoutes.login);
    }

    // التحقق من الصلاحيات حسب المسار
    if (route != null && !_hasPermissionForRoute(route, authController)) {
      debugPrint('🔒 المستخدم لا يملك صلاحية للوصول إلى: $route');
      // إعادة توجيه للصفحة الرئيسية أو صفحة عدم وجود صلاحية
      return const RouteSettings(name: AppRoutes.home);
    }

    // السماح بالوصول
    return null;
  }

  /// التحقق من صلاحية المستخدم للوصول لمسار معين
  bool _hasPermissionForRoute(String route, AuthController authController) {
    final user = authController.currentUser.value;
    if (user == null) return false;

    // الحصول على خدمة الصلاحيات
    if (!Get.isRegistered<UnifiedPermissionService>()) {
      debugPrint('🔒 خدمة الصلاحيات غير متاحة');
      return false;
    }

    final permissionService = Get.find<UnifiedPermissionService>();

    // قائمة المسارات التي تتطلب صلاحيات المهام
    final taskRoutes = [
      AppRoutes.taskDetail,
      '/task/detail',
      '/api/task/detail',
      AppRoutes.createTask,
      AppRoutes.taskReminders,
    ];

    // قائمة المسارات التي تتطلب صلاحيات إدارية
    final adminRoutes = [
      AppRoutes.admin,
      AppRoutes.enhancedAdmin,
    ];

    // التحقق من صلاحيات المهام
    if (taskRoutes.contains(route)) {
      return permissionService.canViewTaskDetails();
    }

    // التحقق من الصلاحيات الإدارية
    if (adminRoutes.contains(route)) {
      return permissionService.canAccessAdmin();
    }

    // المسارات العامة متاحة لجميع المستخدمين المسجلين
    return true;
  }

}
