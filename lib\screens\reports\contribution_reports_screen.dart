import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/contribution_report_controller.dart';
import '../../models/contribution_report_model.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/common/loading_indicator.dart';
import 'contribution_report_details_screen.dart';

/// شاشة تقارير المساهمات
/// تعرض قائمة بتقارير المساهمات وتوفر إمكانية إنشاء تقارير جديدة
class ContributionReportsScreen extends StatefulWidget {
  const ContributionReportsScreen({Key? key}) : super(key: key);

  @override
  State<ContributionReportsScreen> createState() =>
      _ContributionReportsScreenState();
}

class _ContributionReportsScreenState extends State<ContributionReportsScreen> {
  final ContributionReportController _reportController =
      Get.put(ContributionReportController());
  final UserController _userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  /// تحميل تقارير المساهمات
  Future<void> _loadReports() async {
    await _reportController.loadReportsForCurrentUser();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'تقارير المساهمات',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Obx(() {
        if (_reportController.isLoading.value) {
          return const Center(child: LoadingIndicator());
        }

        if (_reportController.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ',
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  _reportController.error.value,
                  style: AppStyles.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _loadReports,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        if (_reportController.reports.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.analytics,
                  size: 64,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد تقارير مساهمات',
                  style: AppStyles.titleMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'قم بإنشاء تقرير جديد للبدء',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _showCreateReportDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء تقرير جديد'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadReports,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // زر إنشاء تقرير جديد
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'تقارير المساهمات (${_reportController.reports.length})',
                      style: AppStyles.titleMedium,
                    ),
                    ElevatedButton.icon(
                      onPressed: _showCreateReportDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('إنشاء تقرير جديد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // قائمة التقارير
                Expanded(
                  child: _buildReportsList(),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  /// بناء قائمة التقارير
  Widget _buildReportsList() {
    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    final int crossAxisCount = ResponsiveHelper.isMobile(context)
        ? 1
        : ResponsiveHelper.isTablet(context)
            ? 2
            : 3;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: _reportController.reports.length,
      itemBuilder: (context, index) {
        final report = _reportController.reports[index];
        return _buildReportCard(report);
      },
    );
  }

  /// بناء بطاقة التقرير
  Widget _buildReportCard(ContributionReport report) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openReportDetails(report.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان التقرير
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      report.title,
                      style: AppStyles.titleSmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleReportAction(value, report),
                    itemBuilder: (context) => [
                      const PopupMenuItem<String>(
                        value: 'export_pdf',
                        child: Row(
                          children: [
                            Icon(Icons.picture_as_pdf, size: 20),
                            SizedBox(width: 8),
                            Text('تصدير PDF'),
                          ],
                        ),
                      ),
                      const PopupMenuItem<String>(
                        value: 'export_excel',
                        child: Row(
                          children: [
                            Icon(Icons.table_chart, size: 20),
                            SizedBox(width: 8),
                            Text('تصدير Excel'),
                          ],
                        ),
                      ),
                      const PopupMenuItem<String>(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share, size: 20),
                            SizedBox(width: 8),
                            Text('مشاركة'),
                          ],
                        ),
                      ),
                      const PopupMenuItem<String>(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red, size: 20),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // وصف التقرير
              if (report.description != null && report.description!.isNotEmpty)
                Text(
                  report.description!,
                  style: AppStyles.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              const SizedBox(height: 8),

              // معلومات التقرير
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // نوع التقرير
                    _buildReportInfoItem(
                      icon: Icons.category,
                      label: _getReportTypeText(report),
                    ),
                    const SizedBox(height: 4),

                    // الفترة الزمنية
                    _buildReportInfoItem(
                      icon: Icons.date_range,
                      label: _getReportPeriodText(report),
                    ),
                    const SizedBox(height: 4),

                    // منشئ التقرير
                    FutureBuilder<String>(
                      future:
                          _userController.getUserNameById(report.createdById),
                      builder: (context, snapshot) {
                        final userName = snapshot.data ?? 'مستخدم غير معروف';
                        return _buildReportInfoItem(
                          icon: Icons.person,
                          label: 'بواسطة: $userName',
                        );
                      },
                    ),
                  ],
                ),
              ),

              // تاريخ الإنشاء
              Text(
                'تم الإنشاء: ${DateFormat('yyyy/MM/dd').format(report.createdAt)}',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر معلومات التقرير
  Widget _buildReportInfoItem({required IconData icon, required String label}) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            label,
            style: AppStyles.bodySmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// الحصول على نص نوع التقرير
  String _getReportTypeText(ContributionReport report) {
    if (report.taskId != null) {
      return 'تقرير مهمة';
    } else if (report.userId != null) {
      return 'تقرير مستخدم';
    } else if (report.departmentId != null) {
      return 'تقرير قسم';
    } else {
      return 'تقرير عام';
    }
  }

  /// الحصول على نص الفترة الزمنية للتقرير
  String _getReportPeriodText(ContributionReport report) {
    if (report.startDate != null && report.endDate != null) {
      return '${DateFormat('yyyy/MM/dd').format(report.startDate!)} - ${DateFormat('yyyy/MM/dd').format(report.endDate!)}';
    } else if (report.periodDays != null) {
      return 'آخر ${report.periodDays} يوم';
    } else {
      return 'الفترة الكاملة';
    }
  }

  /// فتح تفاصيل التقرير
  void _openReportDetails(String reportId) {
    Get.to(() => ContributionReportDetailsScreen(reportId: reportId));
  }

  /// معالجة إجراءات التقرير
  Future<void> _handleReportAction(
      String action, ContributionReport report) async {
    switch (action) {
      case 'export_pdf':
        await _exportReportToPdf(report.id);
        break;
      case 'export_excel':
        await _exportReportToExcel(report.id);
        break;
      case 'share':
        await _showShareReportDialog(report);
        break;
      case 'delete':
        await _showDeleteReportDialog(report);
        break;
    }
  }

  /// تصدير التقرير بتنسيق PDF
  Future<void> _exportReportToPdf(String reportId) async {
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      final filePath = await _reportController.exportReportToPdf(reportId);
      Get.back();

      if (filePath != null) {
        Get.snackbar(
          'نجاح',
          'تم تصدير التقرير بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تصدير التقرير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصدير التقرير بتنسيق Excel
  Future<void> _exportReportToExcel(String reportId) async {
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      final filePath = await _reportController.exportReportToExcel(reportId);
      Get.back();

      if (filePath != null) {
        Get.snackbar(
          'نجاح',
          'تم تصدير التقرير بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تصدير التقرير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير التقرير: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// عرض حوار مشاركة التقرير
  Future<void> _showShareReportDialog(ContributionReport report) async {
    // سيتم تنفيذ هذا في المستقبل
    Get.snackbar(
      'قريبًا',
      'ستتوفر ميزة مشاركة التقارير قريبًا',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  /// عرض حوار حذف التقرير
  Future<void> _showDeleteReportDialog(ContributionReport report) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('حذف التقرير'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await _reportController.deleteReport(report.id);
      if (success) {
        Get.snackbar(
          'نجاح',
          'تم حذف التقرير بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل حذف التقرير: ${_reportController.error.value}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }

  /// عرض حوار إنشاء تقرير جديد
  Future<void> _showCreateReportDialog() async {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    String? selectedTaskId;
    String? selectedUserId;
    String? selectedDepartmentId;
    String reportType = 'general';
    int periodDays = 30;

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('إنشاء تقرير مساهمات جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان التقرير
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان التقرير *',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // وصف التقرير
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف التقرير',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // نوع التقرير
              const Text('نوع التقرير:'),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    children: [
                      RadioListTile<String>(
                        title: const Text('تقرير عام'),
                        value: 'general',
                        groupValue: reportType,
                        onChanged: (value) {
                          setState(() {
                            reportType = value!;
                            selectedTaskId = null;
                            selectedUserId = null;
                            selectedDepartmentId = null;
                          });
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('تقرير مهمة'),
                        value: 'task',
                        groupValue: reportType,
                        onChanged: (value) {
                          setState(() {
                            reportType = value!;
                            selectedUserId = null;
                            selectedDepartmentId = null;
                          });
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('تقرير مستخدم'),
                        value: 'user',
                        groupValue: reportType,
                        onChanged: (value) {
                          setState(() {
                            reportType = value!;
                            selectedTaskId = null;
                            selectedDepartmentId = null;
                          });
                        },
                      ),
                      RadioListTile<String>(
                        title: const Text('تقرير قسم'),
                        value: 'department',
                        groupValue: reportType,
                        onChanged: (value) {
                          setState(() {
                            reportType = value!;
                            selectedTaskId = null;
                            selectedUserId = null;
                          });
                        },
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),

              // الفترة الزمنية
              const Text('الفترة الزمنية:'),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setState) {
                  return DropdownButtonFormField<int>(
                    value: periodDays,
                    decoration: const InputDecoration(
                      labelText: 'الفترة الزمنية',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem<int>(
                        value: 7,
                        child: Text('آخر 7 أيام'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 30,
                        child: Text('آخر 30 يوم'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 90,
                        child: Text('آخر 3 أشهر'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 180,
                        child: Text('آخر 6 أشهر'),
                      ),
                      const DropdownMenuItem<int>(
                        value: 365,
                        child: Text('آخر سنة'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        periodDays = value!;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (titleController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال عنوان للتقرير',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }
              Get.back(result: true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );

    if (result == true) {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: periodDays));

      final report = await _reportController.createReport(
        title: titleController.text.trim(),
        description: descriptionController.text.trim(),
        taskId: reportType == 'task' ? selectedTaskId : null,
        userId: reportType == 'user' ? selectedUserId : null,
        departmentId: reportType == 'department' ? selectedDepartmentId : null,
        periodDays: periodDays,
        startDate: startDate,
        endDate: endDate,
      );

      if (report != null) {
        Get.snackbar(
          'نجاح',
          'تم إنشاء التقرير بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        _openReportDetails(report.id);
      } else {
        Get.snackbar(
          'خطأ',
          'فشل إنشاء التقرير: ${_reportController.error.value}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }
}
