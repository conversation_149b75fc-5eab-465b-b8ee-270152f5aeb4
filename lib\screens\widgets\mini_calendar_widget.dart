import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import '../../routes/app_routes.dart';
import '../../constants/app_colors.dart';
import '../../models/calendar_models.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;

/// ويدجت تقويم مصغر
/// يعرض تقويمًا مصغرًا مع إمكانية عرض الأحداث اليومية
class MiniCalendarWidget extends StatefulWidget {
  /// ارتفاع التقويم المصغر
  final double height;

  /// عرض التقويم المصغر
  final double? width;

  /// دالة يتم استدعاؤها عند النقر على يوم
  final Function(DateTime)? onDaySelected;

  /// إظهار الأحداث أسفل التقويم
  final bool showEvents;

  /// عدد الأحداث التي يتم عرضها
  final int maxEvents;

  const MiniCalendarWidget({
    super.key,
    this.height = 300,
    this.width,
    this.onDaySelected,
    this.showEvents = true,
    this.maxEvents = 3,
  });

  @override
  State<MiniCalendarWidget> createState() => _MiniCalendarWidgetState();
}

class _MiniCalendarWidgetState extends State<MiniCalendarWidget> {
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late CalendarFormat _calendarFormat;
  late app_calendar.CalendarController _calendarController;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _calendarFormat = CalendarFormat.month;

    // الحصول على وحدة تحكم التقويم
    try {
      _calendarController = Get.find<app_calendar.CalendarController>();
    } catch (e) {
      // إذا لم تكن وحدة التحكم موجودة، إنشاء وحدة تحكم جديدة
      _calendarController = Get.put(app_calendar.CalendarController());
    }

    // تحميل الأحداث
    _calendarController.loadEvents();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // تغيير إلى min لمنع تجاوز المساحة المتاحة
        children: [
          // رأس التقويم
          Container(
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'التقويم',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.calendar_month, color: Colors.white),
                  tooltip: 'عرض التقويم الكامل',
                  padding: EdgeInsets.zero, // تقليل المساحة الداخلية للزر
                  constraints: const BoxConstraints(), // إزالة القيود الافتراضية للزر
                  onPressed: () {
                    Get.toNamed(AppRoutes.calendar);
                  },
                ),
              ],
            ),
          ),

          // التقويم المصغر
          GetBuilder<app_calendar.CalendarController>(
            builder: (controller) {
              return TableCalendar(
                firstDay: DateTime.now().subtract(const Duration(days: 365)),
                lastDay: DateTime.now().add(const Duration(days: 365)),
                focusedDay: _focusedDay,
                calendarFormat: _calendarFormat,
                startingDayOfWeek: StartingDayOfWeek.saturday, // السبت هو أول يوم في الأسبوع
                rowHeight: 20, // تقليل ارتفاع الصفوف بشكل أكبر
                daysOfWeekHeight: 10, // تقليل ارتفاع أيام الأسبوع بشكل أكبر
                sixWeekMonthsEnforced: false, // السماح بعرض أقل من 6 أسابيع
                headerStyle: HeaderStyle(
                  formatButtonVisible: false,
                  titleCentered: true,
                  leftChevronIcon: const Icon(Icons.chevron_left, size: 14),
                  rightChevronIcon: const Icon(Icons.chevron_right, size: 14),
                  titleTextStyle: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                  headerPadding: const EdgeInsets.symmetric(vertical: 2),
                  headerMargin: EdgeInsets.zero,
                ),
                daysOfWeekStyle: const DaysOfWeekStyle(
                  weekdayStyle: TextStyle(fontSize: 11),
                  weekendStyle: TextStyle(fontSize: 11, color: Colors.red),
                  decoration: BoxDecoration(color: Colors.transparent),
                ),
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: false,
                  todayDecoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withAlpha(70),
                    shape: BoxShape.circle,
                  ),
                  selectedDecoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withAlpha(180),
                    border: Border.all(
                      color: Theme.of(context).primaryColor,
                      width: 1,
                    ),
                    shape: BoxShape.circle,
                  ),
                  markerDecoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                  markersMaxCount: 3,
                  cellMargin: const EdgeInsets.all(1),
                  cellPadding: EdgeInsets.zero,
                  defaultTextStyle: const TextStyle(fontSize: 11),
                  weekendTextStyle: TextStyle(
                    fontSize: 11,
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                  // إضافة خاصية markersAnchor لتحديد موقع المؤشرات
                  markersAnchor: 0.7,
                  // إضافة خاصية markerSize لتحديد حجم المؤشرات
                  markerSize: 5,
                  // إضافة خاصية markerMargin لتحديد المسافة بين المؤشرات
                  markerMargin: const EdgeInsets.symmetric(horizontal: 0.3),
                ),
                selectedDayPredicate: (day) {
                  return isSameDay(_selectedDay, day);
                },
                onDaySelected: (selectedDay, focusedDay) {
                  setState(() {
                    _selectedDay = selectedDay;
                    _focusedDay = focusedDay;
                  });

                  if (widget.onDaySelected != null) {
                    widget.onDaySelected!(selectedDay);
                  }
                },
                // إضافة بناة مخصصة للتقويم لإضافة التلميحات
                calendarBuilders: CalendarBuilders(
                  // بناء خلية اليوم مع تلميح للأحداث
                  markerBuilder: (context, date, events) {
                    if (events.isEmpty) return null;

                    return Positioned(
                      bottom: 1,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                          events.length > 3 ? 3 : events.length,
                          (index) {
                            final event = events[index] as CalendarEvent;
                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 0.5),
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                color: _parseColor(event.color) ?? Theme.of(context).colorScheme.secondary,
                                shape: BoxShape.circle,
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  },
                  // بناء خلية اليوم العادي مع تلميح للأحداث
                  defaultBuilder: (context, day, focusedDay) {
                    final events = controller.getEventsForDay(day);

                    // إنشاء ويدجت اليوم العادي
                    final defaultCell = Center(
                      child: Text(
                        '${day.day}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    );

                    // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                    if (events.isEmpty) {
                      return defaultCell;
                    }

                    // إنشاء ويدجت لعرض اليوم مع تلميح
                    return Tooltip(
                      message: _buildEventsTooltipText(events),
                      preferBelow: false,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      textStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 12,
                      ),
                      child: defaultCell,
                    );
                  },
                  // بناء خلية اليوم المحدد مع تلميح للأحداث
                  selectedBuilder: (context, day, focusedDay) {
                    final events = controller.getEventsForDay(day);

                    // إنشاء ويدجت اليوم المحدد
                    final selectedCell = Center(
                      child: Text(
                        '${day.day}',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    );

                    // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                    if (events.isEmpty) {
                      return selectedCell;
                    }

                    // إنشاء ويدجت لعرض اليوم مع تلميح
                    return Tooltip(
                      message: _buildEventsTooltipText(events),
                      preferBelow: false,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      textStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 12,
                      ),
                      child: selectedCell,
                    );
                  },
                  // بناء خلية اليوم الحالي مع تلميح للأحداث
                  todayBuilder: (context, day, focusedDay) {
                    final events = controller.getEventsForDay(day);

                    // إنشاء ويدجت اليوم الحالي
                    final todayCell = Center(
                      child: Text(
                        '${day.day}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    );

                    // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                    if (events.isEmpty) {
                      return todayCell;
                    }

                    // إنشاء ويدجت لعرض اليوم مع تلميح
                    return Tooltip(
                      message: _buildEventsTooltipText(events),
                      preferBelow: false,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      textStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 12,
                      ),
                      child: todayCell,
                    );
                  },
                ),
                eventLoader: (day) {
                  // استخدام وحدة تحكم التقويم للحصول على أحداث اليوم
                  // تأكد من أن اليوم يتم تمريره بشكل صحيح (بدون وقت)
                  final normalizedDay = DateTime(day.year, day.month, day.day);

                  try {
                    final events = controller.getEventsForDay(normalizedDay);

                    // التحقق من صحة الأحداث المسترجعة
                    if (events.isNotEmpty) {
                      // تحقق من أن جميع الأحداث لها تواريخ صالحة
                      events.removeWhere((event) =>
                        event.startTime > event.endTime);

                      // فقط إرجاع الأحداث إذا كانت هناك أحداث صالحة
                      if (events.isNotEmpty) {
                        return events;
                      }
                    }

                    // إرجاع قائمة فارغة إذا لم تكن هناك أحداث
                    return [];
                  } catch (e) {
                    debugPrint('خطأ في تحميل أحداث اليوم: $e');
                    return [];
                  }
                },
              );
            },
          ),

          // قائمة الأحداث
          if (widget.showEvents)
            SizedBox(
              height: 40, // تقليل ارتفاع قائمة الأحداث
              child: GetBuilder<app_calendar.CalendarController>(
                builder: (controller) {
                  final events = controller.getEventsForDay(_selectedDay);

                  if (events.isEmpty) {
                    return const Center(
                      child: Text(
                        'لا توجد أحداث لهذا اليوم',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    itemCount: events.length > widget.maxEvents ? widget.maxEvents : events.length,
                    itemBuilder: (context, index) {
                      final event = events[index];
                      return _buildEventItem(event);
                    },
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عنصر حدث
  Widget _buildEventItem(CalendarEvent event) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 1), // تقليل الهامش العمودي
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(6),
        onTap: () {
          // عرض تفاصيل الحدث
          Get.toNamed(
            AppRoutes.calendarEventDetails,
            arguments: {
              'event': event,
              'onEventUpdated': () {
                _calendarController.update();
              },
              'onEventDeleted': () {
                _calendarController.update();
              },
            },
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // تقليل المساحة العمودية
          child: Row(
            children: [
              // شريط اللون
              Container(
                width: 3,
                height: 20, // تقليل الارتفاع أكثر
                decoration: BoxDecoration(
                  color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              // معلومات الحدث
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      event.title,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${_formatTime(event.startDateTime)} - ${_formatTime(event.endDateTime)}',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              // أيقونة نوع الحدث
              Icon(
                _getEventTypeIcon(event.eventType),
                size: 14,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    // تنسيق الوقت بشكل صحيح مع تبطين الساعات والدقائق
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على لون حسب نوع الحدث
  Color _getEventTypeColor(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return Colors.orange;
      case CalendarEventType.reminder:
        return Colors.purple;
      case CalendarEventType.vacation:
        return Colors.green;
      case CalendarEventType.other:
        return Colors.grey;
      default:
        return AppColors.primary;
    }
  }

  /// الحصول على أيقونة حسب نوع الحدث
  IconData _getEventTypeIcon(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Icons.task;
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.reminder:
        return Icons.alarm;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
        return Icons.event;
      default:
        return Icons.event;
    }
  }

  /// بناء نص التلميح للأحداث
  String _buildEventsTooltipText(List<CalendarEvent> events) {
    if (events.isEmpty) return '';

    // تنظيم الأحداث حسب النوع
    final Map<CalendarEventType, List<CalendarEvent>> eventsByType = {};

    for (final event in events) {
      if (!eventsByType.containsKey(event.eventType)) {
        eventsByType[event.eventType] = [];
      }
      eventsByType[event.eventType]!.add(event);
    }

    // بناء نص التلميح
    final StringBuffer tooltipText = StringBuffer();

    // إضافة عدد الأحداث الإجمالي
    tooltipText.writeln('${events.length} أحداث:');
    tooltipText.writeln('');

    // إضافة تفاصيل الأحداث حسب النوع
    eventsByType.forEach((type, typeEvents) {
      // إضافة نوع الحدث
      switch (type) {
        case CalendarEventType.task:
          tooltipText.writeln('🔷 مهام (${typeEvents.length}):');
          break;
        case CalendarEventType.meeting:
          tooltipText.writeln('🔶 اجتماعات (${typeEvents.length}):');
          break;
        case CalendarEventType.reminder:
          tooltipText.writeln('🔔 تذكيرات (${typeEvents.length}):');
          break;
        case CalendarEventType.vacation:
          tooltipText.writeln('🏖️ إجازات (${typeEvents.length}):');
          break;
        case CalendarEventType.other:
          tooltipText.writeln('📌 أخرى (${typeEvents.length}):');
          break;
      }

      // إضافة تفاصيل الأحداث من هذا النوع (بحد أقصى 3 أحداث لكل نوع)
      for (int i = 0; i < typeEvents.length && i < 3; i++) {
        final event = typeEvents[i];
        final timeFormat = DateFormat('h:mm a');
        final startTime = timeFormat.format(event.startDateTime);
        tooltipText.writeln('- ${event.title} ($startTime)');
      }

      // إذا كان هناك المزيد من الأحداث، إضافة إشارة إلى ذلك
      if (typeEvents.length > 3) {
        tooltipText.writeln('  ... و${typeEvents.length - 3} أخرى');
      }

      tooltipText.writeln('');
    });

    return tooltipText.toString().trim();
  }

  /// تحليل اللون من النص
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      // إزالة الرمز # إذا كان موجوداً
      String cleanColor = colorString.replaceAll('#', '');

      // إضافة FF للشفافية إذا لم تكن موجودة
      if (cleanColor.length == 6) {
        cleanColor = 'FF$cleanColor';
      }

      // تحويل إلى int ثم إلى Color
      return Color(int.parse(cleanColor, radix: 16));
    } catch (e) {
      debugPrint('خطأ في تحليل اللون: $colorString');
      return null;
    }
  }
}
