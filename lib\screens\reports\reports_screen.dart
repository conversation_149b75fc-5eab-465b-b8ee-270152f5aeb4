import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';


import '../../repositories/report_repository.dart';
import '../../repositories/user_repository.dart';
import '../../services/report_export_service.dart';
import '../../services/unified_permission_service.dart';
import '../../utils/responsive_helper.dart';
import '../../routes/app_routes.dart';
import 'custom_report_builder_screen.dart';

/// شاشة التقارير الرئيسية
///
/// تعرض قائمة بالتقارير المتاحة وتوفر إمكانية إنشاء تقارير جديدة
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> with SingleTickerProviderStateMixin {
  final ReportRepository _reportRepository = ReportRepository();
  final ReportExportService _reportExportService = ReportExportService();
  final AuthController _authController = Get.find<AuthController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  late TabController _tabController;
  List<Report> _myReports = [];
  List<Report> _sharedReports = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل التقارير
  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'لم يتم تسجيل الدخول';
        });
        return;
      }

      // تحميل التقارير الخاصة بالمستخدم
      final myReports = await _reportRepository.getReportsByCreator(userId);

      // تحميل التقارير المشتركة مع المستخدم
      final sharedReports = await _reportRepository.getReportsSharedWithUser(userId);

      setState(() {
        _myReports = myReports;
        _sharedReports = sharedReports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل التقارير: $e';
      });
    }
  }

  /// عرض مربع حوار إنشاء تقرير جديد
  void _showCreateReportDialog() {
    final formKey = GlobalKey<FormState>();
    String title = '';
    String? description;
    ReportType selectedType = ReportType.taskStatus;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء تقرير جديد'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'عنوان التقرير',
                    hintText: 'أدخل عنوان التقرير',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال عنوان التقرير';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    title = value;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'وصف التقرير (اختياري)',
                    hintText: 'أدخل وصف التقرير',
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    description = value.isEmpty ? null : value;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ReportType>(
                  decoration: const InputDecoration(
                    labelText: 'نوع التقرير',
                  ),
                  value: selectedType,
                  items: ReportType.values.map((type) {
                    return DropdownMenuItem<ReportType>(
                      value: type,
                      child: Text(_getReportTypeName(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      selectedType = value;
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.of(context).pop();

                // الانتقال إلى شاشة إنشاء التقرير المخصص
                final result = await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CustomReportBuilderScreen(
                      title: title,
                      description: description,
                      reportType: selectedType.toString(),
                    ),
                  ),
                );

                if (result == true) {
                  _loadReports();
                }
              }
            },
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تصدير التقرير
  void _showExportReportDialog(Report report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: const Text('PDF'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(report, ReportFormat.pdf);
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('Excel'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(report, ReportFormat.excel);
              },
            ),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('CSV'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(report, ReportFormat.csv);
              },
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('JSON'),
              onTap: () async {
                Navigator.of(context).pop();
                _exportReport(report, ReportFormat.json);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تصدير التقرير
  Future<void> _exportReport(Report report, ReportFormat format) async {
    if (!mounted) return;

    // استخدام مرجع إلى ScaffoldMessenger
    // لتجنب استخدام context بعد عمليات غير متزامنة
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // عرض مؤشر التقدم
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تصدير التقرير...'),
            ],
          ),
        );
      },
    );

    try {
      String? filePath;

      switch (format) {
        case ReportFormat.pdf:
          filePath = await _reportExportService.exportReportToPdf(report.id);
          break;
        case ReportFormat.excel:
          filePath = await _reportExportService.exportReportToExcel(report.id);
          break;
        case ReportFormat.csv:
          filePath = await _reportExportService.exportReportToCsv(report.id);
          break;
        case ReportFormat.json:
          filePath = await _reportExportService.exportReportToJson(report.id);
          break;
        default:
          filePath = null;
      }

      if (!mounted) return;

      if (filePath != null) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
            duration: const Duration(seconds: 5),
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('فشل تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض مربع حوار حذف التقرير
  void _showDeleteReportDialog(Report report) {
    final currentContext = context;
    final scaffoldMessenger = ScaffoldMessenger.of(currentContext);

    showDialog(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('حذف التقرير'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(dialogContext).pop();
              await _reportRepository.deleteReport(report.id);
              _loadReports();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف التقرير بنجاح'),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار مشاركة التقرير
  Future<void> _showShareReportDialog(Report report) async {
    final userRepository = UserRepository();
    final reportRepository = ReportRepository();

    // الحصول على جميع المستخدمين
    final allUsers = await userRepository.getAllUsers();

    // استبعاد المستخدم الحالي ومن تمت مشاركة التقرير معهم بالفعل
    final currentUserId = _authController.currentUser.value?.id;
    final sharedUserIds = report.sharedWithUserIds ?? [];

    final availableUsers = allUsers.where((user) =>
      user.id != currentUserId && !sharedUserIds.contains(user.id)
    ).toList();

    if (!mounted) return;

    if (availableUsers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد مستخدمين متاحين للمشاركة معهم'),
        ),
      );
      return;
    }

    // قائمة المستخدمين المحددين للمشاركة
    final List<String> selectedUserIds = [];

    // تخزين مرجع ScaffoldMessenger
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // عرض مربع حوار اختيار المستخدمين
    final bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('مشاركة التقرير'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر المستخدمين لمشاركة التقرير معهم:'),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: availableUsers.length,
                    itemBuilder: (context, index) {
                      final user = availableUsers[index];
                      return CheckboxListTile(
                        title: Text(user.name),
                        subtitle: Text(user.email ?? 'غير محدد'),
                        value: selectedUserIds.contains(user.id.toString()),
                        onChanged: (selected) {
                          setState(() {
                            if (selected == true) {
                              selectedUserIds.add(user.id.toString());
                            } else {
                              selectedUserIds.remove(user.id.toString());
                            }
                          });
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: selectedUserIds.isEmpty
                  ? null
                  : () {
                      Navigator.of(context).pop(true);
                    },
              child: const Text('مشاركة'),
            ),
          ],
        ),
      ),
    );

    // إذا تم إلغاء العملية أو لم يتم اختيار أي مستخدم
    if (result != true || !mounted) return;

    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري مشاركة التقرير...'),
            ],
          ),
        );
      },
    );

    try {
      // مشاركة التقرير مع المستخدمين المحددين
      await reportRepository.shareReportWithUsers(report.id, selectedUserIds);

      // تحديث قائمة التقارير
      _loadReports();

      // عرض رسالة نجاح
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('تمت مشاركة التقرير مع ${selectedUserIds.length} مستخدم'),
        ),
      );
    } catch (e) {
      // عرض رسالة خطأ
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء مشاركة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      // إغلاق مؤشر التقدم
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'تقرير حالة المهام';
      case ReportType.userPerformance:
        return 'تقرير أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'تقرير أداء القسم';
      case ReportType.timeTracking:
        return 'تقرير تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقرير تقدم المهام';
      case ReportType.taskDetails:
        return 'تقرير تفاصيل المهمة';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'تقرير غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'تقاريري'),
            Tab(text: 'التقارير المشتركة'),
          ],
        ),
        actions: [
          // التقارير المحسنة
          if (_permissionService.canAccessAdvancedReports())
            IconButton(
              icon: const Icon(Icons.auto_graph),
              tooltip: 'التقارير المحسنة',
              onPressed: () => Get.toNamed(AppRoutes.enhancedReports),
            ),
          // التقارير الثابتة
          if (_permissionService.canAccessReports())
            IconButton(
              icon: const Icon(Icons.description),
              tooltip: 'التقارير الثابتة',
              onPressed: () => Get.toNamed(AppRoutes.staticReports),
            ),
          // تحديث
          if (_permissionService.canAccessReports())
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: 'تحديث',
              onPressed: _loadReports,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildReportsList(_myReports, true),
                    _buildReportsList(_sharedReports, false),
                  ],
                ),
      floatingActionButton: _permissionService.canCreateReport()
          ? FloatingActionButton(
              onPressed: _showCreateReportDialog,
              tooltip: 'إنشاء تقرير جديد',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildReportsList(List<Report> reports, bool isMyReports) {
    if (reports.isEmpty) {
      return Center(
        child: Text(
          isMyReports ? 'لا توجد تقارير خاصة بك' : 'لا توجد تقارير مشتركة معك',
          style: AppStyles.titleMedium,
        ),
      );
    }

    return ResponsiveHelper.isDesktop(context) || ResponsiveHelper.isTablet(context)
        ? _buildGridView(reports, isMyReports)
        : _buildListView(reports, isMyReports);
  }

  Widget _buildGridView(List<Report> reports, bool isMyReports) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: reports.length,
      itemBuilder: (context, index) {
        final report = reports[index];
        return _buildReportCard(report, isMyReports);
      },
    );
  }

  Widget _buildListView(List<Report> reports, bool isMyReports) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      itemCount: reports.length,
      itemBuilder: (context, index) {
        final report = reports[index];
        return _buildReportListItem(report, isMyReports);
      },
    );
  }

  Widget _buildReportCard(Report report, bool isMyReports) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
          if (!_permissionService.canViewReports()) {
            Get.snackbar(
              'غير مسموح',
              'ليس لديك صلاحية لعرض تفاصيل التقارير',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
              duration: const Duration(seconds: 3),
            );
            return;
          }

          // Navigator.of(context).push(
          //   MaterialPageRoute(
          //     builder: (context) => ReportDetailsScreen(reportId: report.id.toString()),
          //   ),
          // ).then((_) => _loadReports());
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      report.title,
                      style: AppStyles.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildReportTypeChip(report.type),
                ],
              ),
              const SizedBox(height: 8),
              if (report.description != null) ...[
                Text(
                  report.description!,
                  style: AppStyles.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(report.createdAt * 1000))}',
                    style: AppStyles.captionSmall,
                  ),
                  Row(
                    children: [
                      // تصدير التقرير
                      if (_permissionService.canExportReport())
                        IconButton(
                          icon: const Icon(Icons.file_download, size: 20),
                          tooltip: 'تصدير',
                          onPressed: () => _showExportReportDialog(report),
                        ),
                      if (isMyReports) ...[
                        // مشاركة التقرير
                        if (_permissionService.canShareReport())
                          IconButton(
                            icon: const Icon(Icons.share, size: 20),
                            tooltip: 'مشاركة',
                            onPressed: () => _showShareReportDialog(report),
                          ),
                        // حذف التقرير
                        if (_permissionService.canDeleteReport())
                          IconButton(
                            icon: const Icon(Icons.delete, size: 20),
                            tooltip: 'حذف',
                            onPressed: () => _showDeleteReportDialog(report),
                          ),
                      ],
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReportListItem(Report report, bool isMyReports) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(report.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (report.description != null) ...[
              Text(
                report.description!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
            ],
            Text(
              'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(report.createdAt * 1000))}',
              style: AppStyles.captionSmall,
            ),
          ],
        ),
        leading: _buildReportTypeIcon(report.type),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // تصدير التقرير
            if (_permissionService.canExportReport())
              IconButton(
                icon: const Icon(Icons.file_download),
                tooltip: 'تصدير',
                onPressed: () => _showExportReportDialog(report),
              ),
            if (isMyReports) ...[
              // مشاركة التقرير
              if (_permissionService.canShareReport())
                IconButton(
                  icon: const Icon(Icons.share),
                  tooltip: 'مشاركة',
                  onPressed: () => _showShareReportDialog(report),
                ),
              // حذف التقرير
              if (_permissionService.canDeleteReport())
                IconButton(
                  icon: const Icon(Icons.delete),
                  tooltip: 'حذف',
                  onPressed: () => _showDeleteReportDialog(report),
                ),
            ],
          ],
        ),
        onTap: () {
          // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
          if (!_permissionService.canViewReports()) {
            Get.snackbar(
              'غير مسموح',
              'ليس لديك صلاحية لعرض تفاصيل التقارير',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
              duration: const Duration(seconds: 3),
            );
            return;
          }

          // Navigator.of(context).push(
          //   MaterialPageRoute(
          //     builder: (context) => ReportDetailsScreen(reportId: report.id.toString()),
          //   ),
          // ).then((_) => _loadReports());
        },
      ),
    );
  }

  Widget _buildReportTypeChip(ReportType type) {
    Color chipColor = Colors.grey; // قيمة افتراضية

    switch (type) {
      case ReportType.taskStatus:
        chipColor = Colors.blue;
        break;
      case ReportType.userPerformance:
        chipColor = Colors.green;
        break;
      case ReportType.departmentPerformance:
        chipColor = Colors.purple;
        break;
      case ReportType.timeTracking:
        chipColor = Colors.orange;
        break;
      case ReportType.taskProgress:
        chipColor = Colors.teal;
        break;
      case ReportType.taskDetails:
        chipColor = Colors.indigo;
        break;
      case ReportType.custom:
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.grey;
        break;
    }

    return Chip(
      label: Text(
        _getReportTypeName(type),
        style: const TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
      ),
      backgroundColor: chipColor,
      padding: EdgeInsets.zero,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildReportTypeIcon(ReportType type) {
    // قيم افتراضية
    IconData iconData = Icons.help_outline;
    Color iconColor = Colors.grey;

    switch (type) {
      case ReportType.taskStatus:
        iconData = Icons.assignment;
        iconColor = Colors.blue;
        break;
      case ReportType.userPerformance:
        iconData = Icons.person;
        iconColor = Colors.green;
        break;
      case ReportType.departmentPerformance:
        iconData = Icons.business;
        iconColor = Colors.purple;
        break;
      case ReportType.timeTracking:
        iconData = Icons.timer;
        iconColor = Colors.orange;
        break;
      case ReportType.taskProgress:
        iconData = Icons.trending_up;
        iconColor = Colors.teal;
        break;
      case ReportType.taskDetails:
        iconData = Icons.assignment_ind;
        iconColor = Colors.indigo;
        break;
      case ReportType.custom:
        iconData = Icons.dashboard_customize;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withAlpha(51), // 0.2 * 255 = ~51
      child: Icon(
        iconData,
        color: iconColor,
      ),
    );
  }
}
