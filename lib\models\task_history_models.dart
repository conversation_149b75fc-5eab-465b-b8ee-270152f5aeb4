import 'task_models.dart';
import 'user_model.dart';
import 'task_action_type.dart';

/// نموذج تاريخ المهمة - متطابق مع ASP.NET Core API
class TaskHistory {
  final int id;
  final int taskId;
  final int userId;
  final String action;
  final String? details;
  final int timestamp;
  final String? changeType;
  final String? changeDescription;
  final String? oldValue;
  final String? newValue;
  final int? changedBy;
  final int? changedAt;

  // Navigation properties
  final User? changedByNavigation;
  final Task? task;
  final User? user;

  // Additional properties for UI
  TaskActionType get actionType => TaskActionType.fromValue(action);
  String? get previousValue => oldValue;

  const TaskHistory({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.action,
    this.details,
    required this.timestamp,
    this.changeType,
    this.changeDescription,
    this.oldValue,
    this.newValue,
    this.changedBy,
    this.changedAt,
    this.changedByNavigation,
    this.task,
    this.user,
  });

  factory TaskHistory.fromJson(Map<String, dynamic> json) {
    return TaskHistory(
      id: json['id'] is int ? json['id'] : (int.tryParse(json['id']?.toString() ?? '') ?? 0),
      taskId: json['taskId'] is int ? json['taskId'] : (int.tryParse(json['taskId']?.toString() ?? '') ?? 0),
      userId: json['userId'] is int ? json['userId'] : (int.tryParse(json['userId']?.toString() ?? '') ?? 0),
      action: json['action']?.toString() ?? '',
      details: json['details']?.toString(),
      timestamp: json['timestamp'] is int ? json['timestamp'] : (int.tryParse(json['timestamp']?.toString() ?? '') ?? 0),
      changeType: json['changeType']?.toString(),
      changeDescription: json['changeDescription']?.toString(),
      oldValue: json['oldValue']?.toString(),
      newValue: json['newValue']?.toString(),
      changedBy: json['changedBy'] is int ? json['changedBy'] : int.tryParse(json['changedBy']?.toString() ?? ''),
      changedAt: json['changedAt'] is int ? json['changedAt'] : int.tryParse(json['changedAt']?.toString() ?? ''),
      changedByNavigation: json['changedByNavigation'] != null && json['changedByNavigation'] is Map<String, dynamic>
          ? User.fromJson(json['changedByNavigation'])
          : null,
      task: json['task'] != null && json['task'] is Map<String, dynamic>
          ? Task.fromJson(json['task'])
          : null,
      user: json['user'] != null && json['user'] is Map<String, dynamic>
          ? User.fromJson(json['user'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'action': action,
      'details': details,
      'timestamp': timestamp,
      'changeType': changeType,
      'changeDescription': changeDescription,
      'oldValue': oldValue,
      'newValue': newValue,
      'changedBy': changedBy,
      'changedAt': changedAt,
    };
  }

  TaskHistory copyWith({
    int? id,
    int? taskId,
    int? userId,
    String? action,
    String? details,
    int? timestamp,
    String? changeType,
    String? changeDescription,
    String? oldValue,
    String? newValue,
    int? changedBy,
    int? changedAt,
    User? changedByNavigation,
    Task? task,
    User? user,
  }) {
    return TaskHistory(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      action: action ?? this.action,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      changeType: changeType ?? this.changeType,
      changeDescription: changeDescription ?? this.changeDescription,
      oldValue: oldValue ?? this.oldValue,
      newValue: newValue ?? this.newValue,
      changedBy: changedBy ?? this.changedBy,
      changedAt: changedAt ?? this.changedAt,
      changedByNavigation: changedByNavigation ?? this.changedByNavigation,
      task: task ?? this.task,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الطابع الزمني كـ DateTime
  DateTime get timestampDateTime => 
      DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

  /// الحصول على تاريخ التغيير كـ DateTime
  DateTime? get changedAtDateTime => changedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(changedAt! * 1000)
      : null;

  /// الحصول على وصف الإجراء باللغة العربية
  String get actionDescription {
    switch (action.toLowerCase()) {
      case 'created': return 'تم إنشاء المهمة';
      case 'updated': return 'تم تحديث المهمة';
      case 'assigned': return 'تم تعيين المهمة';
      case 'completed': return 'تم إكمال المهمة';
      case 'status_changed': return 'تم تغيير حالة المهمة';
      case 'priority_changed': return 'تم تغيير أولوية المهمة';
      case 'due_date_changed': return 'تم تغيير تاريخ الاستحقاق';
      case 'comment_added': return 'تم إضافة تعليق';
      case 'attachment_added': return 'تم إضافة مرفق';
      case 'deleted': return 'تم حذف المهمة';
      default: return action;
    }
  }

  @override
  String toString() {
    return 'TaskHistory(id: $id, action: $action, timestamp: $timestampDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskHistory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
