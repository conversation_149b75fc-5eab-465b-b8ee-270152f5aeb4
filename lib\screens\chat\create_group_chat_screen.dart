import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/chat_groups_controller.dart';
import '../../models/user_model.dart';
import '../../models/chat_group_models.dart';
import '../../constants/app_colors.dart';
import '../../services/api/users_api_service.dart';

/// شاشة إنشاء مجموعة محادثة جديدة
class CreateGroupChatScreen extends StatefulWidget {
  const CreateGroupChatScreen({super.key});

  @override
  State<CreateGroupChatScreen> createState() => _CreateGroupChatScreenState();
}

class _CreateGroupChatScreenState extends State<CreateGroupChatScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  final AuthController _authController = Get.find<AuthController>();
  final ChatGroupsController _chatController = Get.find<ChatGroupsController>();
  final UsersApiService _usersApiService = UsersApiService();

  final RxList<User> _selectedMembers = <User>[].obs;
  final RxList<User> _availableUsers = <User>[].obs;
  final RxBool _isPrivate = false.obs;
  final RxBool _isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadAvailableUsers();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// تحميل المستخدمين المتاحين
  Future<void> _loadAvailableUsers() async {
    try {
      _isLoading.value = true;

      // تحميل المستخدمين من API
      final users = await _usersApiService.getAllUsers();

      // إزالة المستخدم الحالي من القائمة
      final currentUserId = _authController.currentUser.value?.id;
      _availableUsers.assignAll(
        users.where((user) => user.id != currentUserId && user.isActive).toList()
      );

      debugPrint('تم تحميل ${_availableUsers.length} مستخدم متاح');
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المستخدمين: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء المجموعة
  Future<void> _createGroup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من تسجيل الدخول
    if (_authController.currentUser.value == null) {
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول أولاً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    if (_selectedMembers.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'يجب اختيار عضو واحد على الأقل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
      );
      return;
    }

    try {
      _isLoading.value = true;

      debugPrint('إنشاء مجموعة جديدة:');
      debugPrint('- الاسم: ${_nameController.text.trim()}');
      debugPrint('- الوصف: ${_descriptionController.text.trim()}');
      debugPrint('- خاصة: ${_isPrivate.value}');
      debugPrint('- عدد الأعضاء: ${_selectedMembers.length}');
      debugPrint('- المستخدم الحالي: ${_authController.currentUser.value?.id}');

      // إنشاء طلب إنشاء المجموعة
      final createRequest = CreateChatGroupRequest(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        isPrivate: _isPrivate.value,
        groupType: _isPrivate.value ? 'private' : 'general',
        maxMembers: _isPrivate.value ? 50 : 500,
        initialMemberIds: _selectedMembers.map((user) => user.id).toList(),
      );

      // إنشاء المجموعة عبر API
      final success = await _chatController.createGroup(createRequest);

      if (success) {
        // إظهار رسالة نجاح
        Get.snackbar(
          'نجح',
          'تم إنشاء المجموعة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 2),
        );
        Get.back();

        // تحديث قائمة المجموعات
        await _chatController.refresh();

        // العودة إلى قائمة المحادثات مع تأخير بسيط لإظهار الرسالة
        await Future.delayed(const Duration(milliseconds: 500));

        // العودة للصفحة السابقة (قائمة المحادثات)
        Get.back();

        // يمكن إضافة خيار للانتقال مباشرة للمجموعة الجديدة:
        // Get.offNamed('/chat/group/${newGroupId}');
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في إنشاء المجموعة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
      Get.back();
    } catch (e) {
      debugPrint('خطأ في إنشاء المجموعة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء المجموعة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء مجموعة جديدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          Obx(() => _isLoading.value
              ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                )
              : TextButton(
                  onPressed: _createGroup,
                  child: const Text(
                    'إنشاء',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // معلومات المجموعة
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات المجموعة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // اسم المجموعة
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم المجموعة *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.group),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يجب إدخال اسم المجموعة';
                      }
                      if (value.trim().length < 3) {
                        return 'يجب أن يكون اسم المجموعة 3 أحرف على الأقل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // وصف المجموعة
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'وصف المجموعة (اختياري)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // مجموعة خاصة
                  Obx(() => SwitchListTile(
                    title: const Text('مجموعة خاصة'),
                    subtitle: const Text('المجموعات الخاصة تتطلب دعوة للانضمام'),
                    value: _isPrivate.value,
                    onChanged: (value) => _isPrivate.value = value,
                    activeColor: AppColors.primary,
                  )),
                ],
              ),
            ),

            const Divider(),

            // قائمة الأعضاء
            Expanded(
              child: Column(
                children: [
                  // عنوان الأعضاء
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        const Text(
                          'الأعضاء المحددين',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Obx(() => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${_selectedMembers.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )),
                      ],
                    ),
                  ),

                  // قائمة الأعضاء المحددين
                  Obx(() => _selectedMembers.isEmpty
                      ? const Expanded(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.people_outline,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'لم يتم اختيار أعضاء بعد',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'اضغط على زر + لإضافة أعضاء',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : Expanded(
                          child: ListView.builder(
                            itemCount: _selectedMembers.length,
                            itemBuilder: (context, index) {
                              final member = _selectedMembers[index];
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundImage: member.profileImage != null
                                      ? NetworkImage(member.profileImage!)
                                      : null,
                                  child: member.profileImage == null
                                      ? Text(member.name.substring(0, 1).toUpperCase())
                                      : null,
                                ),
                                title: Text(member.name),
                                subtitle: Text(member.email??'غير محدد'),
                                trailing: IconButton(
                                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                                  onPressed: () => _selectedMembers.removeAt(index),
                                ),
                              );
                            },
                          ),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddMembersDialog,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.person_add, color: Colors.white),
      ),
    );
  }

  /// عرض حوار إضافة أعضاء
  void _showAddMembersDialog() {
    if (_availableUsers.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'لا توجد مستخدمين متاحين للإضافة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
      );
      return;
    }

    // قائمة المستخدمين المتاحين (غير المحددين مسبقاً)
    final availableToAdd = _availableUsers
        .where((user) => !_selectedMembers.any((selected) => selected.id == user.id))
        .toList();

    if (availableToAdd.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'تم إضافة جميع المستخدمين المتاحين',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
      );
      return;
    }

    Addmembertogroup(availableToAdd);
  }

  Future<dynamic> Addmembertogroup(List<User> availableToAdd) {
    return Get.dialog(
    Dialog(
      child: Container(
        width: 400,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // عنوان الحوار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'إضافة أعضاء',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // قائمة المستخدمين المتاحين
            Expanded(
              child: ListView.builder(
                itemCount: availableToAdd.length,
                itemBuilder: (context, index) {
                  final user = availableToAdd[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundImage: user.profileImage != null
                          ? NetworkImage(user.profileImage!)
                          : null,
                      child: user.profileImage == null
                          ? Text(user.name.substring(0, 1).toUpperCase())
                          : null,
                    ),
                    title: Text(user.name),
                    subtitle: Text(user.email??'غير محدد'),
                    trailing: IconButton(
                      icon: const Icon(Icons.add_circle, color: Colors.green),
                      onPressed: () {
                        _selectedMembers.add(user);
                        Get.back();
                      },
                    ),
                    onTap: () {
                      _selectedMembers.add(user);
                      Get.back();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    ),
  );
  }
}
