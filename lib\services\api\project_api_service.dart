// import 'package:flutter/foundation.dart';

// import 'api_service.dart';

// /// خدمة API للمشاريع - متطابقة مع ASP.NET Core API
// class ProjectApiService {
//   final ApiService _apiService = ApiService();

//   /// الحصول على جميع المشاريع
//   Future<List<Project>> getAllProjects() async {
//     try {
//       final response = await _apiService.get('/Projects');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشاريع: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على مشروع بواسطة المعرف
//   Future<Project?> getProjectById(int id) async {
//     try {
//       final response = await _apiService.get('/Projects/$id');
//       return _apiService.handleResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشروع: $e');
//       return null;
//     }
//   }

//   /// إنشاء مشروع جديد
//   Future<Project?> createProject(Project project) async {
//     try {
//       final response = await _apiService.post(
//         '/Projects',
//         project.toJson(),
//       );
//       return _apiService.handleResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في إنشاء المشروع: $e');
//       rethrow;
//     }
//   }

//   /// تحديث مشروع
//   Future<Project?> updateProject(Project project) async {
//     try {
//       final response = await _apiService.put(
//         '/Projects/${project.id}',
//         project.toJson(),
//       );
//       return _apiService.handleResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في تحديث المشروع: $e');
//       rethrow;
//     }
//   }

//   /// حذف مشروع
//   Future<bool> deleteProject(int id) async {
//     try {
//       final response = await _apiService.delete('/Projects/$id');
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في حذف المشروع: $e');
//       return false;
//     }
//   }

//   /// الحصول على المشاريع النشطة
//   Future<List<Project>> getActiveProjects() async {
//     try {
//       final response = await _apiService.get('/Projects/active');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشاريع النشطة: $e');
//       rethrow;
//     }
//   }

//   /// البحث في المشاريع
//   Future<List<Project>> searchProjects(String query) async {
//     try {
//       final response = await _apiService.get('/Projects/search?searchTerm=$query');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في البحث عن المشاريع: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على مشاريع مستخدم معين
//   Future<List<Project>> getProjectsByUser(int userId) async {
//     try {
//       final response = await _apiService.get('/Projects/user/$userId');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على مشاريع المستخدم: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على مشاريع قسم معين
//   Future<List<Project>> getProjectsByDepartment(int departmentId) async {
//     try {
//       final response = await _apiService.get('/Projects/department/$departmentId');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على مشاريع القسم: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على مشاريع بحالة معينة
//   Future<List<Project>> getProjectsByStatus(String status) async {
//     try {
//       final response = await _apiService.get('/Projects/status/$status');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشاريع بالحالة: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على مهام مشروع معين
//   Future<List<Map<String, dynamic>>> getProjectTasks(int projectId) async {
//     try {
//       final response = await _apiService.get('/Projects/$projectId/tasks');
//       if (response.statusCode >= 200 && response.statusCode < 300) {
//         return List<Map<String, dynamic>>.from(response.body as List);
//       }
//       return [];
//     } catch (e) {
//       debugPrint('خطأ في الحصول على مهام المشروع: $e');
//       return [];
//     }
//   }

//   /// الحصول على أعضاء فريق المشروع
//   Future<List<Map<String, dynamic>>> getProjectTeamMembers(int projectId) async {
//     try {
//       final response = await _apiService.get('/Projects/$projectId/team');
//       if (response.statusCode >= 200 && response.statusCode < 300) {
//         return List<Map<String, dynamic>>.from(response.body as List);
//       }
//       return [];
//     } catch (e) {
//       debugPrint('خطأ في الحصول على أعضاء فريق المشروع: $e');
//       return [];
//     }
//   }

//   /// إضافة عضو إلى فريق المشروع
//   Future<bool> addTeamMember(int projectId, int userId, String role) async {
//     try {
//       final response = await _apiService.post(
//         '/Projects/$projectId/team',
//         {
//           'userId': userId,
//           'role': role,
//         },
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في إضافة عضو إلى فريق المشروع: $e');
//       return false;
//     }
//   }

//   /// إزالة عضو من فريق المشروع
//   Future<bool> removeTeamMember(int projectId, int userId) async {
//     try {
//       final response = await _apiService.delete('/Projects/$projectId/team/$userId');
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في إزالة عضو من فريق المشروع: $e');
//       return false;
//     }
//   }

//   /// تحديث دور عضو في فريق المشروع
//   Future<bool> updateTeamMemberRole(int projectId, int userId, String newRole) async {
//     try {
//       final response = await _apiService.put(
//         '/Projects/$projectId/team/$userId',
//         {'role': newRole},
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في تحديث دور عضو الفريق: $e');
//       return false;
//     }
//   }

//   /// الحصول على إحصائيات المشروع
//   Future<Map<String, dynamic>?> getProjectStatistics(int projectId) async {
//     try {
//       final response = await _apiService.get('/Projects/$projectId/statistics');
//       if (response.statusCode >= 200 && response.statusCode < 300) {
//         return Map<String, dynamic>.from(response.body as Map);
//       }
//       return null;
//     } catch (e) {
//       debugPrint('خطأ في الحصول على إحصائيات المشروع: $e');
//       return null;
//     }
//   }

//   /// الحصول على تقدم المشروع
//   Future<double?> getProjectProgress(int projectId) async {
//     try {
//       final response = await _apiService.get('/Projects/$projectId/progress');
//       if (response.statusCode >= 200 && response.statusCode < 300) {
//         return (response.body as num?)?.toDouble();
//       }
//       return null;
//     } catch (e) {
//       debugPrint('خطأ في الحصول على تقدم المشروع: $e');
//       return null;
//     }
//   }

//   /// تحديث حالة المشروع
//   Future<bool> updateProjectStatus(int projectId, String status) async {
//     try {
//       final response = await _apiService.put(
//         '/Projects/$projectId/status',
//         {'status': status},
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في تحديث حالة المشروع: $e');
//       return false;
//     }
//   }

//   /// تحديث تواريخ المشروع
//   Future<bool> updateProjectDates(
//     int projectId,
//     DateTime? startDate,
//     DateTime? endDate,
//   ) async {
//     try {
//       final body = <String, dynamic>{};
//       if (startDate != null) {
//         body['startDate'] = startDate.millisecondsSinceEpoch ~/ 1000;
//       }
//       if (endDate != null) {
//         body['endDate'] = endDate.millisecondsSinceEpoch ~/ 1000;
//       }

//       final response = await _apiService.put(
//         '/Projects/$projectId/dates',
//         body,
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في تحديث تواريخ المشروع: $e');
//       return false;
//     }
//   }

//   /// الحصول على المشاريع المتأخرة
//   Future<List<Project>> getOverdueProjects() async {
//     try {
//       final response = await _apiService.get('/Projects/overdue');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشاريع المتأخرة: $e');
//       rethrow;
//     }
//   }

//   /// الحصول على المشاريع المكتملة
//   Future<List<Project>> getCompletedProjects() async {
//     try {
//       final response = await _apiService.get('/Projects/completed');
//       return _apiService.handleListResponse<Project>(
//         response,
//         (json) => Project.fromJson(json),
//       );
//     } catch (e) {
//       debugPrint('خطأ في الحصول على المشاريع المكتملة: $e');
//       rethrow;
//     }
//   }

//   /// أرشفة مشروع
//   Future<bool> archiveProject(int projectId) async {
//     try {
//       final response = await _apiService.put(
//         '/Projects/$projectId/archive',
//         {},
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في أرشفة المشروع: $e');
//       return false;
//     }
//   }

//   /// إلغاء أرشفة مشروع
//   Future<bool> unarchiveProject(int projectId) async {
//     try {
//       final response = await _apiService.put(
//         '/Projects/$projectId/unarchive',
//         {},
//       );
//       return response.statusCode >= 200 && response.statusCode < 300;
//     } catch (e) {
//       debugPrint('خطأ في إلغاء أرشفة المشروع: $e');
//       return false;
//     }
//   }
// }
