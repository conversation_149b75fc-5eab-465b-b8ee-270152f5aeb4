import 'user_model.dart';

/// أنواع سجلات النظام
enum SystemLogType {
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  error('error', 'خطأ'),
  security('security', 'أمان'),
  audit('audit', 'مراجعة');

  const SystemLogType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static SystemLogType fromValue(String value) {
    return SystemLogType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SystemLogType.info,
    );
  }
}

/// نموذج سجل النظام - متطابق مع ASP.NET Core API
class SystemLog {
  final int id;
  final String logType;
  final String logLevel;
  final String message;
  final String? details;
  final int? userId;
  final String? ipAddress;
  final int createdAt;

  // Navigation properties
  final User? user;

  const SystemLog({
    required this.id,
    required this.logType,
    required this.logLevel,
    required this.message,
    this.details,
    this.userId,
    this.ipAddress,
    required this.createdAt,
    this.user,
  });

  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as int,
      logType: json['logType'] as String,
      logLevel: json['logLevel'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
      userId: json['userId'] as int?,
      ipAddress: json['ipAddress'] as String?,
      createdAt: json['createdAt'] as int,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'logType': logType,
      'logLevel': logLevel,
      'message': message,
      'details': details,
      'userId': userId,
      'ipAddress': ipAddress,
      'createdAt': createdAt,
    };
  }

  /// الحصول على التاريخ كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);
}



/// نموذج النسخة الاحتياطية
class Backup {
  final int id;
  final String name; // اسم النسخة الاحتياطية
  final String fileName;
  final String filePath;
  final double? size; // حجم الملف بالبايت (double)
  final int fileSize;
  final int createdBy;
  final int createdAt;
  final String? description;
  final String? status; // حالة النسخة الاحتياطية
  final bool isAutoBackup;
  final bool isRestored;
  final int? restoredAt;
  final int? restoredBy;

  // Navigation properties
  final User? createdByUser;
  final User? restoredByUser;

  const Backup({
    required this.id,
    required this.name,
    required this.fileName,
    required this.filePath,
    this.size,
    required this.fileSize,
    required this.createdBy,
    required this.createdAt,
    this.description,
    this.status,
    this.isAutoBackup = false,
    this.isRestored = false,
    this.restoredAt,
    this.restoredBy,
    this.createdByUser,
    this.restoredByUser,
  });

  factory Backup.fromJson(Map<String, dynamic> json) {
    return Backup(
      id: json['id'] as int,
      name: json['name'] as String? ?? json['fileName'] as String,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      size: (json['size'] ?? json['fileSize'])?.toDouble(),
      fileSize: json['fileSize'] as int,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      description: json['description'] as String?,
      status: json['status'] as String? ?? 'completed',
      isAutoBackup: json['isAutoBackup'] as bool? ?? false,
      isRestored: json['isRestored'] as bool? ?? false,
      restoredAt: json['restoredAt'] as int?,
      restoredBy: json['restoredBy'] as int?,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      restoredByUser: json['restoredByNavigation'] != null 
          ? User.fromJson(json['restoredByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'description': description,
      'isAutoBackup': isAutoBackup,
      'isRestored': isRestored,
      'restoredAt': restoredAt,
      'restoredBy': restoredBy,
    };
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الاستعادة كـ DateTime
  DateTime? get restoredAtDateTime => restoredAt != null
      ? DateTime.fromMillisecondsSinceEpoch(restoredAt! * 1000)
      : null;

  /// الحصول على اسم النسخة الاحتياطية للعرض
  String get displayName => name.isNotEmpty ? name : fileName;

  /// الحصول على نوع النسخة الاحتياطية
  String get type => isAutoBackup ? 'automatic' : 'manual';

  /// نسخ النموذج مع تحديث بعض الخصائص
  Backup copyWith({
    int? id,
    String? name,
    String? fileName,
    String? filePath,
    double? size,
    int? fileSize,
    int? createdBy,
    int? createdAt,
    String? description,
    String? status,
    bool? isAutoBackup,
    bool? isRestored,
    int? restoredAt,
    int? restoredBy,
    User? createdByUser,
    User? restoredByUser,
  }) {
    return Backup(
      id: id ?? this.id,
      name: name ?? this.name,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      size: size ?? this.size,
      fileSize: fileSize ?? this.fileSize,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      description: description ?? this.description,
      status: status ?? this.status,
      isAutoBackup: isAutoBackup ?? this.isAutoBackup,
      isRestored: isRestored ?? this.isRestored,
      restoredAt: restoredAt ?? this.restoredAt,
      restoredBy: restoredBy ?? this.restoredBy,
      createdByUser: createdByUser ?? this.createdByUser,
      restoredByUser: restoredByUser ?? this.restoredByUser,
    );
  }

  @override
  String toString() {
    return 'Backup(id: $id, fileName: $fileName, size: $fileSizeFormatted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Backup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// تم نقل مودلات Permission و UserPermission إلى ملف منفصل

/// نموذج طلب إنشاء نسخة احتياطية
class CreateBackupRequest {
  final String? description;
  final bool includeFiles;

  const CreateBackupRequest({
    this.description,
    this.includeFiles = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'includeFiles': includeFiles,
    };
  }
}

/// نموذج طلب تحديث إعدادات النظام
class UpdateSystemSettingsRequest {
  final Map<String, String> settings;

  const UpdateSystemSettingsRequest({
    required this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'settings': settings,
    };
  }
}
