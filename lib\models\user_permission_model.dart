import 'user_model.dart';
import 'permission_models.dart';

/// نموذج صلاحيات المستخدم (UserPermission) - متوافق مع الباك اند
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final bool isActive;
  final int? expiresAt;
  final bool isDeleted;
  final int createdAt;

  // العلاقات البرمجية
  final User? user;
  final Permission? permission;
  final User? grantedByNavigation;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.isActive = true,
    this.expiresAt,
    this.isDeleted = false,
    int? createdAt,
    this.user,
    this.permission,
    this.grantedByNavigation,
  }) : createdAt = createdAt ?? grantedAt;

  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as int,
      userId: json['userId'] as int,
      permissionId: json['permissionId'] as int,
      grantedBy: json['grantedBy'] as int,
      grantedAt: json['grantedAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      expiresAt: json['expiresAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdAt: json['createdAt'] as int? ?? json['grantedAt'] as int,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      permission: json['permission'] != null ? Permission.fromJson(json['permission']) : null,
      grantedByNavigation: json['grantedByNavigation'] != null ? User.fromJson(json['grantedByNavigation']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'isActive': isActive,
      'expiresAt': expiresAt,
      'isDeleted': isDeleted,
      'createdAt': createdAt,
      // لا نرسل العلاقات البرمجية للباك اند
    };
  }

  /// تحويل UserPermission إلى JSON مع ضمان أنواع البيانات الصحيحة للباك اند
  Map<String, dynamic> toJsonForApi() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'isActive': isActive,
      'expiresAt': expiresAt,
      'isDeleted': isDeleted,
      'createdAt': createdAt,
    };
  }

  /// إنشاء نسخة جديدة من UserPermission مع تغيير بعض القيم
  UserPermission copyWith({
    int? id,
    int? userId,
    int? permissionId,
    int? grantedBy,
    int? grantedAt,
    bool? isActive,
    int? expiresAt,
    bool? isDeleted,
    int? createdAt,
    User? user,
    Permission? permission,
    User? grantedByNavigation,
  }) {
    return UserPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      permissionId: permissionId ?? this.permissionId,
      grantedBy: grantedBy ?? this.grantedBy,
      grantedAt: grantedAt ?? this.grantedAt,
      isActive: isActive ?? this.isActive,
      expiresAt: expiresAt ?? this.expiresAt,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
      permission: permission ?? this.permission,
      grantedByNavigation: grantedByNavigation ?? this.grantedByNavigation,
    );
  }
}
