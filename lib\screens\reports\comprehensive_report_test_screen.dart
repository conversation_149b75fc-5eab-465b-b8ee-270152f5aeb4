import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../professional_reports/simple_comprehensive_task_report_service.dart';

/// شاشة اختبار التقرير الشامل
/// تتيح للمستخدم اختبار إنشاء التقارير الشاملة للمهام
class ComprehensiveReportTestScreen extends StatefulWidget {
  const ComprehensiveReportTestScreen({super.key});

  @override
  State<ComprehensiveReportTestScreen> createState() => _ComprehensiveReportTestScreenState();
}

class _ComprehensiveReportTestScreenState extends State<ComprehensiveReportTestScreen> 
    with TickerProviderStateMixin {
  
  final _taskIdController = TextEditingController(text: '1');
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  
  bool _isGenerating = false;
  String _currentStep = '';
  double _progress = 0.0;
  String? _error;
  
  final SimpleComprehensiveTaskReportService _reportService = SimpleComprehensiveTaskReportService();

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut)
    );
  }

  @override
  void dispose() {
    _taskIdController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// إنشاء التقرير الشامل
  Future<void> _generateReport() async {
    final taskIdText = _taskIdController.text.trim();
    if (taskIdText.isEmpty) {
      _showError('يرجى إدخال رقم المهمة');
      return;
    }

    final taskId = int.tryParse(taskIdText);
    if (taskId == null) {
      _showError('رقم المهمة غير صحيح');
      return;
    }

    setState(() {
      _isGenerating = true;
      _error = null;
      _progress = 0.0;
      _currentStep = 'بدء إنشاء التقرير...';
    });

    try {
      setState(() {
        _currentStep = 'بدء إنشاء التقرير الشامل...';
        _progress = 0.1;
      });
      _progressController.animateTo(0.1);

      // محاكاة تقدم العملية
      setState(() {
        _currentStep = 'جمع بيانات المهمة...';
        _progress = 0.3;
      });
      _progressController.animateTo(0.3);

      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _currentStep = 'جمع بيانات المساهمين والتحويلات...';
        _progress = 0.6;
      });
      _progressController.animateTo(0.6);

      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _currentStep = 'إنشاء التقرير الشامل...';
        _progress = 0.9;
      });
      _progressController.animateTo(0.9);

      // إنشاء التقرير الشامل
      await _reportService.generateComprehensiveReport(taskId);

      setState(() {
        _currentStep = 'تم إنشاء التقرير بنجاح!';
        _progress = 1.0;
      });
      _progressController.animateTo(1.0);

    } catch (e) {
      _showError('خطأ في إنشاء التقرير: $e');
    } finally {
      setState(() {
        _isGenerating = false;
        _currentStep = '';
        _progress = 0.0;
      });
      _progressController.reset();
    }
  }

  /// عرض رسالة خطأ
  void _showError(String message) {
    setState(() {
      _error = message;
      _isGenerating = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار التقرير الشامل'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // بطاقة معلومات
                _buildInfoCard(),
                
                const SizedBox(height: 20),
                
                // بطاقة إدخال البيانات
                _buildInputCard(),
                
                const SizedBox(height: 20),
                
                // بطاقة التقدم
                if (_isGenerating) _buildProgressCard(),
                
                const SizedBox(height: 20),
                
                // زر الإنشاء
                _buildGenerateButton(),
                
                const SizedBox(height: 20),
                
                // بطاقة الميزات
                _buildFeaturesCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.white],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 8),
                Text(
                  'حول التقرير الشامل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'يتيح لك هذا النظام إنشاء تقارير شاملة ومفصلة للمهام تتضمن جميع البيانات الحقيقية من قاعدة البيانات بتصميم احترافي ودعم كامل للعربية.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإدخال
  Widget _buildInputCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.edit_outlined, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'بيانات المهمة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _taskIdController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                labelText: 'رقم المهمة',
                hintText: 'أدخل رقم المهمة المراد إنشاء تقرير لها',
                prefixIcon: const Icon(Icons.task_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'رقم المهمة مطلوب';
                }
                if (int.tryParse(value) == null) {
                  return 'رقم المهمة غير صحيح';
                }
                return null;
              },
            ),
            if (_error != null) ...[
              const SizedBox(height: 8),
              Text(
                _error!,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التقدم
  Widget _buildProgressCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'جاري الإنشاء...',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LinearProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: Colors.grey.shade300,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(_progress * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 12),
            Text(
              _currentStep,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإنشاء
  Widget _buildGenerateButton() {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: _isGenerating ? null : _generateReport,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isGenerating) ...[
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              const Text('جاري الإنشاء...', style: TextStyle(fontSize: 16)),
            ] else ...[
              const Icon(Icons.picture_as_pdf, size: 24),
              const SizedBox(width: 8),
              const Text('إنشاء التقرير الشامل', style: TextStyle(fontSize: 16)),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الميزات
  Widget _buildFeaturesCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star_outline, color: Colors.amber.shade600),
                const SizedBox(width: 8),
                const Text(
                  'ميزات التقرير الشامل',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...const [
              '📊 تحليل شامل للمساهمين والأدوار',
              '📈 رسوم بيانية متقدمة للتقدم والأداء', 
              '⏱️ تحليل مفصل للوقت والإنتاجية',
              '📝 سجل كامل للأحداث والتحويلات',
              '💬 تحليل التعليقات والتفاعل',
              '📎 إحصائيات المرفقات والوثائق',
              '🎯 إحصائيات متقدمة ومؤشرات أداء',
              '📋 توصيات مبنية على البيانات الفعلية',
            ].map((feature) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      feature,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade700,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }


}