import 'package:flutter_application_2/models/task_model.dart';
import 'package:flutter_application_2/models/user_model.dart';

class UserPerformance {
  final User user;
  final int totalTasks;
  final int completedTasks;
  final int overdueTasks;
  final int inProgressTasks;
  final int pendingTasks;
  final double completionRate;
  final double averageCompletionTime;

  UserPerformance({
    required this.user,
    required this.totalTasks,
    required this.completedTasks,
    required this.overdueTasks,
    required this.inProgressTasks,
    required this.pendingTasks,
    required this.completionRate,
    required this.averageCompletionTime,
  });
}

class UserPerformanceComparisonReport {
  final List<UserPerformance> userPerformances;

  UserPerformanceComparisonReport({
    required this.userPerformances,
  });

  factory UserPerformanceComparisonReport.fromTasks(
    List<Task> tasks,
    List<User> users, {
    Set<int>? userIds,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    // Filter users if userIds are provided
    final filteredUsers = userIds == null || userIds.isEmpty
        ? users
        : users.where((user) => userIds.contains(user.id)).toList();

    final userPerformances = filteredUsers.map((user) {
      // Filter tasks for the current user and within the date range
      var userTasks = tasks.where((task) => task.assigneeId == user.id).toList();

      if (startDate != null) {
        userTasks = userTasks.where((task) =>
            task.dueDateDateTime != null &&
            task.dueDateDateTime!.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        userTasks = userTasks.where((task) =>
            task.dueDateDateTime != null &&
            task.dueDateDateTime!.isBefore(endDate)).toList();
      }

      final totalTasks = userTasks.length;
      final completedTasks = userTasks.where((task) => task.status == 'completed').length;
      final inProgressTasks = userTasks.where((task) => task.status == 'in-progress').length;
      final pendingTasks = userTasks.where((task) => task.status == 'pending').length;
      
      final overdueTasks = userTasks.where((task) {
        return task.dueDateDateTime != null &&
               task.dueDateDateTime!.isBefore(DateTime.now()) &&
               task.status != 'completed';
      }).length;

      final completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0;

      double totalCompletionTime = 0;
      int tasksWithCompletionTime = 0;
      for (var task in userTasks) {
        if (task.status == 'completed' && task.startDate != null && task.dueDateDateTime != null) {
          final startTime = DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000);
          final endTime = task.dueDateDateTime!;
          totalCompletionTime += endTime.difference(startTime).inHours;
          tasksWithCompletionTime++;
        }
      }

      final averageCompletionTime = tasksWithCompletionTime > 0
          ? totalCompletionTime / tasksWithCompletionTime
          : 0.0;

      return UserPerformance(
        user: user,
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        overdueTasks: overdueTasks,
        inProgressTasks: inProgressTasks,
        pendingTasks: pendingTasks,
        completionRate: completionRate,
        averageCompletionTime: averageCompletionTime,
      );
    }).toList();

    // Sort by completion rate descending
    userPerformances.sort((a, b) => b.completionRate.compareTo(a.completionRate));

    return UserPerformanceComparisonReport(
      userPerformances: userPerformances,
    );
  }
}
