import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/unified_models.dart';

/// فئة مساعدة موحدة لإدارة المساهمين والتواريخ في التقارير
/// تتجنب تكرار الكود وتعتمد على جدول task_access_users كمصدر أساسي
class ReportHelpers {
  
  /// استخراج المساهمين من جدول task_access_users بشكل موحد
  /// 
  /// يعتمد على جدول task_access_users كمصدر أساسي للمساهمين
  /// ويحسب مساهمات كل مستخدم بناءً على أنشطته الفعلية في المهمة
  static List<TaskContributor> extractContributorsFromAccessUsers(
    Task task, 
    Map<String, dynamic> taskJson
  ) {
    final contributors = <TaskContributor>[];
    final contributorsMap = <int, TaskContributor>{};

    // تسجيل تشخيصي
    if (kDebugMode) {
      print('🔍 استخراج المساهمين من task_access_users للمهمة ${task.id}');
      print('   - هل يوجد accessUsers في البيانات؟ ${taskJson.containsKey('accessUsers')}');
      print('   - مفاتيح البيانات المتاحة: ${taskJson.keys.toList()}');
      if (taskJson.containsKey('accessUsers')) {
        print('   - نوع accessUsers: ${taskJson['accessUsers'].runtimeType}');
        print('   - عدد accessUsers: ${taskJson['accessUsers'] is List ? (taskJson['accessUsers'] as List).length : 'غير متاح'}');
        if (taskJson['accessUsers'] is List && (taskJson['accessUsers'] as List).isNotEmpty) {
          print('   - أول accessUser: ${(taskJson['accessUsers'] as List).first}');
        }
      }
    }

    // التحقق من وجود accessUsers في البيانات
    if (!taskJson.containsKey('accessUsers') ||
        taskJson['accessUsers'] is! List ||
        (taskJson['accessUsers'] as List).isEmpty) {

      // في حالة عدم وجود accessUsers، نعيد قائمة فارغة بدلاً من استخدام الطريقة البديلة
      if (kDebugMode) {
        print('ℹ️ لم يتم العثور على accessUsers، إرجاع قائمة فارغة');
      }
      return [];
    }

    final accessUsers = taskJson['accessUsers'] as List;
    final users = taskJson['users'] as Map<int, User>?;

    if (users == null) {
      if (kDebugMode) {
        print('⚠️ لم يتم العثور على بيانات المستخدمين');
      }
      return [];
    }

    // معالجة كل مستخدم في قائمة accessUsers
    for (var userItem in accessUsers) {
      int? userId;

      // التعامل مع أنواع البيانات المختلفة
      if (userItem is int) {
        userId = userItem;
      } else if (userItem is Map<String, dynamic>) {
        userId = userItem['userId'] ?? userItem['id'];
      } else {
        continue;
      }

      if (userId == null || !users.containsKey(userId)) {
        if (kDebugMode) {
          print('⚠️ لم يتم العثور على مستخدم بالمعرف: $userId');
        }
        continue;
      }

      final user = users[userId]!;

      // إنشاء كائن مستخدم مؤقت للمعالجة
      final userJson = {
        'userId': user.id,
        'userName': user.name,
        'userEmail': user.email ?? '',
        'role': 'مساهم', // دور افتراضي
      };

      final contributor = _createContributorFromAccessUser(userJson, task, taskJson);
      if (contributor != null) {
        contributorsMap[contributor.userId] = contributor;
      }
    }

    contributors.addAll(contributorsMap.values);
    
    // ترتيب المساهمين حسب عدد المساهمات (الأعلى أولاً)
    contributors.sort((a, b) => b.totalContributions.compareTo(a.totalContributions));
    
    // حساب النسب المئوية لكل مساهم
    _calculateContributorsPercentages(contributors);
    
    // تسجيل تشخيصي للمساهمين المستخرجين
    if (kDebugMode) {
      print('✅ تم استخراج ${contributors.length} مساهم من task_access_users');
      if (contributors.isEmpty) {
        print('⚠️ لم يتم العثور على أي مساهمين!');
        print('   - عدد accessUsers المعالجة: ${accessUsers.length}');
        print('   - عدد المساهمين في الخريطة: ${contributorsMap.length}');
      } else {
        for (var contributor in contributors) {
          print('   - ${contributor.userName}: ${contributor.totalContributions} مساهمة (${contributor.percentage.toStringAsFixed(1)}%)');
          print('     * تعليقات: ${contributor.commentsCount}, مرفقات: ${contributor.attachmentsCount}, مستندات: ${contributor.documentsCount}, أنشطة: ${contributor.activitiesCount}');
        }
      }
    }

    return contributors;
  }

  /// إنشاء مساهم من بيانات accessUser
  static TaskContributor? _createContributorFromAccessUser(
    Map<String, dynamic> userJson, 
    Task task,
    Map<String, dynamic> taskJson
  ) {
    try {
      // استخراج معرف المستخدم مع معالجة محسنة للقيم null
      int userId;
      if (userJson['id'] is int) {
        userId = userJson['id'];
      } else if (userJson['id'] != null) {
        userId = int.tryParse(userJson['id'].toString()) ?? 0;
      } else {
        userId = 0;
      }
      
      final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
      final userEmail = userJson['email']?.toString() ?? '';
      
      // حساب المساهمات الفعلية للمستخدم في هذه المهمة
      int commentsCount = 0;
      int attachmentsCount = 0;
      int activitiesCount = 0;
      int progressUpdatesCount = 0;
      int timeTrackingCount = 0;
      
      try {
        commentsCount = _countUserComments(task, userId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد تعليقات المستخدم $userName: $e');
        }
      }
      
      try {
        attachmentsCount = _countUserAttachments(task, userId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد مرفقات المستخدم $userName: $e');
        }
      }
      
      try {
        activitiesCount = _countUserActivities(task, userId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد أنشطة المستخدم $userName: $e');
        }
      }
      
      try {
        progressUpdatesCount = _countUserProgressUpdates(taskJson, userId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد تحديثات التقدم للمستخدم $userName: $e');
        }
      }
      
      try {
        timeTrackingCount = _countUserTimeTracking(taskJson, userId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد سجلات تتبع الوقت للمستخدم $userName: $e');
        }
      }

      // حساب عدد المستندات التي أنشأها المستخدم
      int documentsCount = 0;
      try {
        documentsCount = _countUserDocuments(task, userId, taskJson);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب عدد المستندات للمستخدم $userName: $e');
        }
      }

      // تحديد دور المستخدم في المهمة
      String role = userJson['role']?.toString() ?? 'مساهم';
      int roleBonus = 0;
      
      try {
        role = _determineUserRole(task, userId, progressUpdatesCount, documentsCount);
        roleBonus = _getRoleBonus(role);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في تحديد دور المستخدم $userName: $e');
        }
      }

      // حساب إجمالي المساهمات (شامل المساهمات المحسنة والمستندات)
      final totalContributions = commentsCount + attachmentsCount + activitiesCount +
                              progressUpdatesCount + timeTrackingCount + documentsCount + roleBonus;
      
      // تحديد مستوى النشاط
      final level = ContributorLevel.fromContributions(totalContributions);
      
      if (kDebugMode) {
        print('✅ تم إنشاء مساهم: $userName (المساهمات: $totalContributions)');
      }
      
      return TaskContributor(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        role: role,
        commentsCount: commentsCount,
        attachmentsCount: attachmentsCount,
        activitiesCount: activitiesCount + progressUpdatesCount + timeTrackingCount,
        documentsCount: documentsCount, // إضافة المستندات كخاصية منفصلة
        totalContributions: totalContributions,
        level: level,
      );
    } catch (e) {
      // في حالة خطأ في معالجة مستخدم معين، تسجيل الخطأ وإرجاع null
      if (kDebugMode) {
        print('❌ خطأ في إنشاء مساهم من بيانات accessUser: $e');
        print('❌ بيانات المستخدم: $userJson');
      }
      return null;
    }
  }

  /// حساب عدد تعليقات المستخدم في المهمة
  static int _countUserComments(Task task, int userId) {
    return task.comments.where((comment) => comment.user?.id == userId).length;
  }

  /// حساب عدد مرفقات المستخدم في المهمة
  static int _countUserAttachments(Task task, int userId) {
    return task.attachments.where((attachment) => attachment.uploadedByUser?.id == userId).length;
  }

  /// حساب عدد أنشطة المستخدم في المهمة (من السجل التاريخي)
  static int _countUserActivities(Task task, int userId) {
    // محاولة الحصول على taskHistories من toJson()
    final taskJson = task.toJson();
    if (taskJson.containsKey('taskHistories') && taskJson['taskHistories'] is List) {
      final histories = taskJson['taskHistories'] as List;
      return histories.where((history) {
        if (history is Map<String, dynamic>) {
          // التحقق من المستخدم الذي قام بالتغيير
          if (history['changedByNavigation'] != null) {
            final changedBy = history['changedByNavigation'] as Map<String, dynamic>;
            return changedBy['id'] == userId;
          }
          if (history['user'] != null) {
            final user = history['user'] as Map<String, dynamic>;
            return user['id'] == userId;
          }
          if (history['userId'] == userId) {
            return true;
          }
        }
        return false;
      }).length;
    }
    return 0;
  }

  /// تحديد دور المستخدم في المهمة (محسن مع دعم المستندات)
  static String _determineUserRole(Task task, int userId, int progressUpdatesCount, int documentsCount) {
    if (userId == task.creatorId) {
      return 'المنشئ';
    } else if (userId == task.assigneeId) {
      return 'المكلف';
    } else if (progressUpdatesCount > 0) {
      return 'متتبع التقدم';
    } else if (documentsCount > 0) {
      return 'منشئ مستندات';
    } else {
      // تحديد الدور بناءً على نوع المساهمة الأكثر
      final commentsCount = _countUserComments(task, userId);
      final attachmentsCount = _countUserAttachments(task, userId);

      if (commentsCount > attachmentsCount) {
        return 'معلق نشط';
      } else if (attachmentsCount > 0) {
        return 'رافع ملفات';
      } else {
        return 'مساهم';
      }
    }
  }

  /// الحصول على نقاط إضافية حسب الدور (محسن مع دعم المستندات)
  static int _getRoleBonus(String role) {
    switch (role) {
      case 'المنشئ':
        return 3;
      case 'المكلف':
        return 2;
      case 'متتبع التقدم':
        return 2;
      case 'منشئ مستندات':
        return 2; // نقاط إضافية لمنشئي المستندات
      case 'معلق نشط':
        return 1;
      case 'رافع ملفات':
        return 1;
      default:
        return 0;
    }
  }

  /// حساب النسب المئوية للمساهمين
  static void _calculateContributorsPercentages(List<TaskContributor> contributors) {
    if (contributors.isEmpty) return;
    
    final totalContributions = contributors.fold(0, (sum, contributor) => sum + contributor.totalContributions);
    
    if (totalContributions > 0) {
      for (var contributor in contributors) {
        final percentage = (contributor.totalContributions / totalContributions) * 100;
        contributor.setPercentage(percentage);
      }
    }
  }

  // تم إزالة الدالة الاحتياطية - نستخدم البيانات الحقيقية فقط

  /// حساب عدد تحديثات التقدم للمستخدم
  static int _countUserProgressUpdates(Map<String, dynamic> taskJson, int userId) {
    try {
      if (!taskJson.containsKey('progressTrackers') || taskJson['progressTrackers'] is! List) {
        return 0;
      }
      
      final progressTrackers = taskJson['progressTrackers'] as List;
      return progressTrackers.where((tracker) {
        if (tracker is Map<String, dynamic>) {
          // التحقق من المستخدم الذي قام بالتحديث
          if (tracker['user'] != null) {
            try {
              final user = tracker['user'] as Map<String, dynamic>;
              return user['id'] == userId;
            } catch (e) {
              return false;
            }
          }
          if (tracker['updatedBy'] != null) {
            // إذا كان updatedBy يحتوي على معرف المستخدم مباشرة
            return tracker['updatedBy'].toString().contains(userId.toString());
          }
          if (tracker['userId'] == userId) {
            return true;
          }
        }
        return false;
      }).length;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في حساب عدد تحديثات التقدم للمستخدم: $e');
      }
      return 0;
    }
  }

  /// حساب عدد سجلات تتبع الوقت للمستخدم
  static int _countUserTimeTracking(Map<String, dynamic> taskJson, int userId) {
    try {
      if (!taskJson.containsKey('timeTrackingEntries') || taskJson['timeTrackingEntries'] is! List) {
        return 0;
      }

      final timeEntries = taskJson['timeTrackingEntries'] as List;
      return timeEntries.where((entry) {
        if (entry is Map<String, dynamic>) {
          // التحقق من المستخدم الذي سجل الوقت
          if (entry['user'] != null) {
            try {
              final user = entry['user'] as Map<String, dynamic>;
              return user['id'] == userId;
            } catch (e) {
              return false;
            }
          }
          if (entry['userName'] != null) {
            // مقارنة بالاسم إذا لم يتوفر معرف المستخدم
            // هذا ليس مثالياً لكنه احتياطي
            return entry['userName'].toString().isNotEmpty;
          }
          if (entry['userId'] == userId) {
            return true;
          }
        }
        return false;
      }).length;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في حساب عدد سجلات تتبع الوقت للمستخدم: $e');
      }
      return 0;
    }
  }

  /// حساب عدد المستندات التي أنشأها المستخدم في المهمة
  static int _countUserDocuments(Task task, int userId, Map<String, dynamic> taskJson) {
    try {
      if (!taskJson.containsKey('taskDocuments')) {
        return 0;
      }

      final taskDocuments = taskJson['taskDocuments'] as List;
      return taskDocuments.where((document) {
        if (document is Map<String, dynamic>) {
          // التحقق من المستخدم الذي أنشأ المستند
          if (document['createdBy'] == userId) {
            return true;
          }
          // التحقق من بيانات المستخدم المنشئ
          if (document['createdByUser'] != null) {
            try {
              final user = document['createdByUser'] as Map<String, dynamic>;
              return user['id'] == userId;
            } catch (e) {
              return false;
            }
          }
        }
        return false;
      }).length;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في حساب عدد المستندات للمستخدم: $e');
      }
      return 0;
    }
  }
}

/// فئة مساعدة موحدة لتنسيق التواريخ والأوقات
class DateTimeHelpers {
  
  /// تنسيق التاريخ بصيغة موحدة (YYYY-MM-DD)
  static String formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// تنسيق التاريخ والوقت بصيغة موحدة (YYYY-MM-DD HH:MM)
  static String formatDateTime(DateTime dateTime) {
    return '${formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// تحويل timestamp (بالثواني) إلى DateTime
  static DateTime timestampToDateTime(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }

  /// تنسيق timestamp مباشرة إلى نص تاريخ
  static String formatTimestamp(int? timestamp) {
    if (timestamp == null) return 'غير محدد';
    try {
      return formatDate(timestampToDateTime(timestamp));
    } catch (e) {
      return 'تاريخ غير صالح';
    }
  }

  /// تنسيق timestamp مباشرة إلى نص تاريخ ووقت
  static String formatTimestampWithTime(int? timestamp) {
    if (timestamp == null) return 'غير محدد';
    try {
      return formatDateTime(timestampToDateTime(timestamp));
    } catch (e) {
      return 'تاريخ غير صالح';
    }
  }

  /// حساب الفرق بين تاريخين بالأيام
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// تنسيق مدة زمنية بالدقائق إلى نص مقروء
  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes دقيقة';
    } else if (minutes < 1440) { // أقل من يوم
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $remainingMinutes دقيقة';
      }
    } else { // أكثر من يوم
      final days = minutes ~/ 1440;
      final remainingHours = (minutes % 1440) ~/ 60;
      if (remainingHours == 0) {
        return '$days يوم';
      } else {
        return '$days يوم و $remainingHours ساعة';
      }
    }
  }

  /// التحقق من صحة التاريخ
  static bool isValidDate(DateTime? date) {
    return date != null && date.year > 1900 && date.year < 2100;
  }

  /// الحصول على تاريخ آمن (يرجع تاريخ افتراضي في حالة null أو تاريخ غير صحيح)
  static DateTime getSafeDate(DateTime? date, {DateTime? fallback}) {
    if (isValidDate(date)) {
      return date!;
    }
    return fallback ?? DateTime.now();
  }
}