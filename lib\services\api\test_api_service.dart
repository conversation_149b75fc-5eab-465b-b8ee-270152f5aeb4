import 'package:flutter/foundation.dart';
import '../../models/user_model.dart';
import 'api_service.dart';

/// خدمة API للاختبار - تعمل بدون مصادقة
/// تستخدم نقاط النهاية العامة في SeedDataController و DatabaseController
class TestApiService {
  final ApiService _apiService = ApiService();

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _apiService.initialize();
  }

  /// اختبار الاتصال مع API
  Future<Map<String, dynamic>?> testConnection() async {
    try {
      final response = await _apiService.get(
        '/api/Auth/test',
        requireAuth: false,
      );
      
      if (response.statusCode == 200) {
        return _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في اختبار الاتصال: $e');
      return null;
    }
  }

  /// إضافة بيانات المستخدمين التجريبية
  Future<bool> seedUsers() async {
    try {
      final response = await _apiService.post(
        '/api/SeedData/seed-users',
        null,
        requireAuth: false,
      );
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إضافة بيانات المستخدمين التجريبية: $e');
      return false;
    }
  }

  /// إضافة الأقسام التجريبية
  Future<bool> seedDepartments() async {
    try {
      final response = await _apiService.post(
        '/api/SeedData/seed-departments',
        null,
        requireAuth: false,
      );
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إضافة الأقسام التجريبية: $e');
      return false;
    }
  }

  /// إضافة جميع البيانات التجريبية
  Future<bool> seedAllData() async {
    try {
      final response = await _apiService.post(
        '/api/SeedData/seed-all',
        null,
        requireAuth: false,
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إضافة جميع البيانات التجريبية: $e');
      return false;
    }
  }

  /// إضافة البيانات التجريبية الكاملة مع المستخدم الافتراضي
  Future<bool> seedCompleteTestData() async {
    try {
      // إضافة المستخدمين أولاً
      final usersResult = await seedUsers();
      if (!usersResult) {
        debugPrint('فشل في إضافة المستخدمين');
        return false;
      }

      // إضافة الأقسام
      final departmentsResult = await seedDepartments();
      if (!departmentsResult) {
        debugPrint('فشل في إضافة الأقسام');
        return false;
      }

      // إضافة جميع البيانات الأخرى
      final allDataResult = await seedAllData();
      if (!allDataResult) {
        debugPrint('فشل في إضافة البيانات الإضافية');
        return false;
      }

      debugPrint('تم إضافة جميع البيانات التجريبية بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة البيانات التجريبية الكاملة: $e');
      return false;
    }
  }

  /// مسح جميع البيانات
  Future<bool> clearAllData() async {
    try {
      final response = await _apiService.delete('/api/SeedData/clear-all');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  /// محاولة الحصول على المستخدمين (قد تفشل بسبب المصادقة)
  /// هذه الطريقة للاختبار فقط - ستفشل لأن endpoint يتطلب مصادقة
  Future<List<User>> tryGetUsers() async {
    try {
      final response = await _apiService.get(
        '/api/Users',
        requireAuth: false, // محاولة بدون مصادقة
      );
      
      if (response.statusCode == 200) {
        return _apiService.handleListResponse<User>(
          response,
          (json) => User.fromJson(json),
        );
      }
      
      debugPrint('فشل في الحصول على المستخدمين: ${response.statusCode}');
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      return [];
    }
  }

  /// الحصول على معلومات قاعدة البيانات (إذا كانت متاحة)
  Future<Map<String, dynamic>?> getDatabaseInfo() async {
    try {
      final response = await _apiService.get(
        '/api/Database/info',
        requireAuth: false,
      );
      
      if (response.statusCode == 200) {
        return _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات قاعدة البيانات: $e');
      return null;
    }
  }

  /// إنشاء قاعدة البيانات (إذا كانت متاحة)
  Future<bool> createDatabase() async {
    try {
      final response = await _apiService.post(
        '/api/Database/create',
        null,
        requireAuth: false,
      );
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إنشاء قاعدة البيانات: $e');
      return false;
    }
  }

  /// تهيئة قاعدة البيانات (إذا كانت متاحة)
  Future<bool> initializeDatabase() async {
    try {
      final response = await _apiService.post(
        '/api/Database/initialize',
        null,
        requireAuth: false,
      );
      
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في تهيئة قاعدة البيانات: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات النظام (إذا كانت متاحة)
  Future<Map<String, dynamic>?> getSystemStats() async {
    try {
      final response = await _apiService.get(
        '/api/Database/stats',
        requireAuth: false,
      );
      
      if (response.statusCode == 200) {
        return _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات النظام: $e');
      return null;
    }
  }

  /// محاولة الحصول على مستخدم واحد بالمعرف (للاختبار)
  Future<User?> tryGetUserById(int id) async {
    try {
      final response = await _apiService.get(
        '/api/Users/<USER>',
        requireAuth: false,
      );
      
      if (response.statusCode == 200) {
        return _apiService.handleResponse<User>(
          response,
          (json) => User.fromJson(json),
        );
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم $id: $e');
      return null;
    }
  }

  /// الحصول على معلومات الخادم
  Future<Map<String, dynamic>?> getServerInfo() async {
    try {
      // محاولة الحصول على معلومات من endpoint الاختبار
      final testResult = await testConnection();
      if (testResult != null) {
        return {
          'server_status': 'online',
          'api_version': testResult['version'] ?? 'unknown',
          'timestamp': testResult['timestamp'] ?? DateTime.now().toIso8601String(),
          'message': testResult['message'] ?? 'API متصل',
        };
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الخادم: $e');
      return null;
    }
  }

  /// تشغيل اختبار شامل للنظام
  Future<Map<String, dynamic>> runSystemTest() async {
    final results = <String, dynamic>{
      'connection_test': false,
      'seed_users_test': false,
      'seed_departments_test': false,
      'get_users_test': false,
      'server_info': null,
      'errors': <String>[],
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      // اختبار الاتصال
      final connectionResult = await testConnection();
      results['connection_test'] = connectionResult != null;
      if (connectionResult != null) {
        results['server_info'] = connectionResult;
      } else {
        results['errors'].add('فشل في اختبار الاتصال');
      }

      // اختبار إضافة المستخدمين
      final seedUsersResult = await seedUsers();
      results['seed_users_test'] = seedUsersResult;
      if (!seedUsersResult) {
        results['errors'].add('فشل في إضافة المستخدمين التجريبيين');
      }

      // اختبار إضافة الأقسام
      final seedDepartmentsResult = await seedDepartments();
      results['seed_departments_test'] = seedDepartmentsResult;
      if (!seedDepartmentsResult) {
        results['errors'].add('فشل في إضافة الأقسام التجريبية');
      }

      // اختبار الحصول على المستخدمين (متوقع أن يفشل)
      final users = await tryGetUsers();
      results['get_users_test'] = users.isNotEmpty;
      results['users_count'] = users.length;
      if (users.isEmpty) {
        results['errors'].add('لا يمكن الحصول على المستخدمين (متوقع - يتطلب مصادقة)');
      }

    } catch (e) {
      results['errors'].add('خطأ عام في الاختبار: $e');
    }

    return results;
  }
}
