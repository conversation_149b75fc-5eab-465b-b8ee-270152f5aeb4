import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/permissions_controller.dart';
import '../../models/permission_models.dart';
import '../../services/unified_permission_service.dart';
import 'test_user_helper.dart';

/// شاشة اختبار الصلاحيات
class PermissionsTestScreen extends StatelessWidget {
  const PermissionsTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PermissionsController());
    final permissionService = Get.find<UnifiedPermissionService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('🔐 اختبار الصلاحيات'),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
        actions: [
          if (permissionService.canTestPermissions())
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => controller.refresh(),
              tooltip: 'إعادة تحميل',
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري تحميل الصلاحيات...'),
              ],
            ),
          );
        }

        return Column(
          children: [
            // شريط الإحصائيات
            _buildStatsBar(controller),
            
            // شريط البحث
            _buildSearchBar(controller),
            
            // قائمة الصلاحيات
            Expanded(
              child: _buildPermissionsList(controller),
            ),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showTestDialog(context, controller),
        icon: const Icon(Icons.science),
        label: const Text('اختبار صلاحية'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  /// شريط الإحصائيات
  Widget _buildStatsBar(PermissionsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.purple.shade50,
      child: Obx(() {
        final totalPermissions = controller.allPermissions.length;
        final filteredCount = controller.filteredPermissions.length;

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatCard(
              'إجمالي الصلاحيات',
              totalPermissions.toString(),
              Icons.security,
              Colors.purple,
            ),
            _buildStatCard(
              'المعروضة',
              filteredCount.toString(),
              Icons.filter_list,
              Colors.green,
            ),
            _buildStatCard(
              'الافتراضية',
              controller.allPermissions.where((p) => p.isDefault).length.toString(),
              Icons.star,
              Colors.orange,
            ),
          ],
        );
      }),
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// شريط البحث
  Widget _buildSearchBar(PermissionsController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'البحث في الصلاحيات...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => controller.setSearchQuery(value),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => controller.clearFilters(),
            icon: const Icon(Icons.clear),
            tooltip: 'مسح البحث',
          ),
        ],
      ),
    );
  }

  /// قائمة الصلاحيات
  Widget _buildPermissionsList(PermissionsController controller) {
    return Obx(() {
      if (controller.error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red.shade300),
              const SizedBox(height: 16),
              Text(
                'خطأ في تحميل الصلاحيات',
                style: TextStyle(fontSize: 18, color: Colors.red.shade700),
              ),
              const SizedBox(height: 8),
              Text(
                controller.error,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => controller.refresh(),
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      final permissions = controller.filteredPermissions;
      
      if (permissions.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_off, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد صلاحيات تطابق البحث',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: permissions.length,
        itemBuilder: (context, index) {
          final permission = permissions[index];
          return _buildPermissionCard(permission, controller);
        },
      );
    });
  }

  /// بطاقة الصلاحية
  Widget _buildPermissionCard(Permission permission, PermissionsController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: permission.isDefault ? Colors.green : Colors.purple,
          child: Text(
            permission.level.toString(),
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          permission.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (permission.description != null)
              Text(permission.description!),
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(permission.permissionGroup),
                  backgroundColor: Colors.purple.shade100,
                  labelStyle: const TextStyle(fontSize: 12),
                ),
                const SizedBox(width: 8),
                if (permission.isDefault)
                  const Chip(
                    label: Text('افتراضية'),
                    backgroundColor: Colors.green,
                    labelStyle: TextStyle(color: Colors.white, fontSize: 12),
                  ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('المعرف', permission.id.toString()),
                _buildDetailRow('المجموعة', permission.permissionGroup),
                _buildDetailRow('المستوى', permission.level.toString()),
                _buildDetailRow('تاريخ الإنشاء',
                  permission.createdAt != null
                    ? DateTime.fromMillisecondsSinceEpoch(permission.createdAt! * 1000).toString()
                    : 'غير محدد'),
                
                const SizedBox(height: 16),
                
                // زر اختبار الصلاحية
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () => _testPermission(permission, controller),
                    icon: const Icon(Icons.check_circle),
                    label: const Text('اختبار هذه الصلاحية'),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// اختبار صلاحية محددة
  void _testPermission(Permission permission, PermissionsController controller) async {
    Get.dialog(
      AlertDialog(
        title: Text('اختبار الصلاحية: ${permission.name}'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري اختبار الصلاحية...'),
          ],
        ),
      ),
    );

    try {
      // اختبار الصلاحية
      final hasPermission = await controller.checkPermission(permission.id, permission.name);
      
      Get.back(); // إغلاق dialog التحميل
      
      Get.dialog(
        AlertDialog(
          title: Text('نتيجة الاختبار: ${permission.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                hasPermission ? Icons.check_circle : Icons.cancel,
                size: 64,
                color: hasPermission ? Colors.green : Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                hasPermission ? 'لديك هذه الصلاحية ✅' : 'ليس لديك هذه الصلاحية ❌',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: hasPermission ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.back(); // إغلاق dialog التحميل
      Get.snackbar(
        'خطأ',
        'فشل في اختبار الصلاحية: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// عرض dialog اختبار الصلاحيات
  void _showTestDialog(BuildContext context, PermissionsController controller) {
    final permissionNameController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('اختبار صلاحية مخصصة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: permissionNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الصلاحية',
                hintText: 'مثال: tasks.view',
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'أدخل اسم الصلاحية التي تريد اختبارها',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final permissionName = permissionNameController.text.trim();
              if (permissionName.isEmpty) {
                Get.snackbar('خطأ', 'يرجى إدخال اسم الصلاحية');
                return;
              }

              Get.back(); // إغلاق dialog الإدخال

              // اختبار الصلاحية
              try {
                final userId = getCurrentUserIdForTest();
                final hasPermission = await controller.checkPermission(userId, permissionName);
                
                Get.dialog(
                  AlertDialog(
                    title: Text('نتيجة اختبار: $permissionName'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          hasPermission ? Icons.check_circle : Icons.cancel,
                          size: 64,
                          color: hasPermission ? Colors.green : Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          hasPermission ? 'الصلاحية متوفرة ✅' : 'الصلاحية غير متوفرة ❌',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: hasPermission ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('موافق'),
                      ),
                    ],
                  ),
                );
              } catch (e) {
                Get.snackbar(
                  'خطأ',
                  'فشل في اختبار الصلاحية: $e',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('اختبار'),
          ),
        ],
      ),
    );
  }
}