import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/role_model.dart';
import '../../../models/permission_models.dart';
import '../../../controllers/admin_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../services/api/roles_api_service.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';

/// حوار إدارة صلاحيات الدور
class RolePermissionsDialog extends StatefulWidget {
  final Role role;
  final VoidCallback? onSuccess;

  const RolePermissionsDialog({
    super.key,
    required this.role,
    this.onSuccess,
  });

  @override
  State<RolePermissionsDialog> createState() => _RolePermissionsDialogState();
}

class _RolePermissionsDialogState extends State<RolePermissionsDialog> {
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();
  final RolesApiService _rolesApiService = RolesApiService();

  // حالة الحوار
  final RxBool _isLoading = false.obs;
  final RxBool _isSaving = false.obs;
  final RxSet<int> _selectedPermissions = <int>{}.obs;
  final RxSet<int> _originalPermissions = <int>{}.obs;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    _isLoading.value = true;
    try {
      // تحميل جميع الصلاحيات
      await _adminController.loadPermissions();
      
      // تحميل صلاحيات الدور الحالية
      await _loadRolePermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل البيانات: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل صلاحيات الدور الحالية
  Future<void> _loadRolePermissions() async {
    try {
      debugPrint('🔄 بدء تحميل صلاحيات الدور ${widget.role.id}...');
      final permissions = await _rolesApiService.getRolePermissions(widget.role.id);
      debugPrint('✅ تم جلب ${permissions.length} صلاحية للدور');

      final permissionIds = permissions.map((p) => p.id).toSet();
      debugPrint('📋 معرفات الصلاحيات: $permissionIds');

      _selectedPermissions.assignAll(permissionIds);
      _originalPermissions.assignAll(permissionIds);

      debugPrint('✅ تم تحديد ${_selectedPermissions.length} صلاحية في الواجهة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات الدور: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل صلاحيات الدور: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('صلاحيات ${widget.role.displayName}'),
          Obx(() {
            final totalPermissions = _adminController.permissions.length;
            final selectedCount = _selectedPermissions.length;
            return Text(
              'الصلاحيات الممنوحة: $selectedCount من $totalPermissions',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            );
          }),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 500,
        child: Obx(() {
          if (_isLoading.value) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل صلاحيات الدور...'),
                ],
              ),
            );
          }

          return Column(
            children: [
              // شريط الأدوات
              _buildToolbar(),

              const SizedBox(height: 16),

              // قائمة الصلاحيات
              Expanded(child: _buildPermissionsList()),
            ],
          );
        }),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('إلغاء'),
        ),
        Obx(() => ElevatedButton(
          onPressed: _isSaving.value ? null : _savePermissions,
          child: _isSaving.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        )),
      ],
    );
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    final permissionService = Get.find<UnifiedPermissionService>();
    final canManagePermissions = permissionService.canManagePermissions();

    return Row(
      children: [
        Obx(() {
          final totalPermissions = _adminController.permissions.length;
          final selectedCount = _selectedPermissions.length;
          return Text(
            'محدد: $selectedCount من $totalPermissions',
            style: Theme.of(context).textTheme.bodyMedium,
          );
        }),

        const Spacer(),

        if (canManagePermissions)
          TextButton(
            onPressed: _selectAllPermissions,
            child: const Text('تحديد الكل'),
          ),

        if (canManagePermissions)
          TextButton(
            onPressed: _clearAllPermissions,
            child: const Text('إلغاء الكل'),
          ),
      ],
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    final permissions = _adminController.permissions;
    
    if (permissions.isEmpty) {
      return const Center(
        child: Text('لا توجد صلاحيات متاحة'),
      );
    }

    // تجميع الصلاحيات حسب المجموعة
    final groupedPermissions = <String, List<Permission>>{};
    for (final permission in permissions) {
      final group = permission.permissionGroup;
      groupedPermissions.putIfAbsent(group, () => []).add(permission);
    }

    return ListView(
      children: groupedPermissions.entries.map((entry) {
        return _buildPermissionGroup(entry.key, entry.value);
      }).toList(),
    );
  }

  /// بناء مجموعة صلاحيات
  Widget _buildPermissionGroup(String groupName, List<Permission> permissions) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          groupName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Obx(() {
          final selectedCount = permissions
              .where((p) => _selectedPermissions.contains(p.id))
              .length;
          return Text('$selectedCount من ${permissions.length} محدد');
        }),
        children: [
          // زر تحديد/إلغاء تحديد المجموعة
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Obx(() {
                  final groupPermissionIds = permissions.map((p) => p.id).toSet();
                  final selectedInGroup = _selectedPermissions.intersection(groupPermissionIds);
                  final isAllSelected = selectedInGroup.length == groupPermissionIds.length;
                  final isPartiallySelected = selectedInGroup.isNotEmpty && !isAllSelected;
                  
                  return Checkbox(
                    value: isAllSelected ? true : (isPartiallySelected ? null : false),
                    tristate: true,
                    onChanged: (value) {
                      if (value == true) {
                        _selectedPermissions.addAll(groupPermissionIds);
                      } else {
                        _selectedPermissions.removeAll(groupPermissionIds);
                      }
                    },
                  );
                }),
                Text(
                  'تحديد جميع صلاحيات $groupName',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // قائمة الصلاحيات في المجموعة
          ...permissions.map((permission) {
            return Obx(() => CheckboxListTile(
              title: Text(permission.name),
              subtitle: permission.description != null
                  ? Text(permission.description!)
                  : null,
              value: _selectedPermissions.contains(permission.id),
              onChanged: (value) {
                if (value == true) {
                  _selectedPermissions.add(permission.id);
                } else {
                  _selectedPermissions.remove(permission.id);
                }
              },
            ));
          }),
        ],
      ),
    );
  }

  /// تحديد جميع الصلاحيات
  void _selectAllPermissions() {
    final allPermissionIds = _adminController.permissions.map((p) => p.id).toSet();
    _selectedPermissions.assignAll(allPermissionIds);
  }

  /// إلغاء تحديد جميع الصلاحيات
  void _clearAllPermissions() {
    _selectedPermissions.clear();
  }

  /// حفظ الصلاحيات
  Future<void> _savePermissions() async {
    // التحقق من وجود تغييرات
    if (_selectedPermissions.difference(_originalPermissions).isEmpty &&
        _originalPermissions.difference(_selectedPermissions).isEmpty) {
      Get.back();
      return;
    }

    _isSaving.value = true;

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // تحديث صلاحيات الدور
      await _rolesApiService.grantMultiplePermissionsToRole(
        roleId: widget.role.id,
        permissionIds: _selectedPermissions.toList(),
        userId: currentUser.id,
        replaceExisting: true, // استبدال الصلاحيات الحالية
      );

      // إغلاق الحوار وإظهار رسالة نجاح
      Get.back();
      AdminMessageDialog.showSuccess(
        title: 'تم بنجاح',
        message: 'تم تحديث صلاحيات الدور بنجاح',
      );

      // استدعاء callback النجاح
      widget.onSuccess?.call();

    } catch (e) {
      debugPrint('خطأ في حفظ الصلاحيات: $e');
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في حفظ الصلاحيات: $e',
      );
    } finally {
      _isSaving.value = false;
    }
  }
}
