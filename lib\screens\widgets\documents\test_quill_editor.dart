import 'package:flutter/material.dart';
import 'advanced_quill_editor_widget.dart';

/// شاشة اختبار للمحرر المحسن
class TestQuillEditor extends StatefulWidget {
  const TestQuillEditor({super.key});

  @override
  State<TestQuillEditor> createState() => _TestQuillEditorState();
}

class _TestQuillEditorState extends State<TestQuillEditor> {
  String _content = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المحرر المحسن'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // معلومات الاختبار
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'اختبار المحرر المحسن',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'هذا اختبار للمحرر المحسن الجديد مع جميع الميزات المطورة.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'طول المحتوى: ${_content.length} حرف',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المحرر
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: AdvancedQuillEditorWidget(
                  initialContent: '',
                  placeholder: 'ابدأ الكتابة هنا لاختبار المحرر المحسن...',
                  showToolbar: true,
                  enableAutoSave: false, // تعطيل الحفظ التلقائي للاختبار
                  documentTitle: 'مستند اختبار',
                  documentType: 'اختبار',
                  onContentChanged: (content) {
                    setState(() {
                      _content = content;
                    });
                  },
                  onSave: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم حفظ المستند بنجاح!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // أزرار الاختبار
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _content = '';
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح المحتوى'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                ElevatedButton.icon(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('محتوى المحرر'),
                        content: SingleChildScrollView(
                          child: Text(_content.isEmpty ? 'لا يوجد محتوى' : _content),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('إغلاق'),
                          ),
                        ],
                      ),
                    );
                  },
                  icon: const Icon(Icons.visibility),
                  label: const Text('عرض المحتوى'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                const Spacer(),
                
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('العودة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
