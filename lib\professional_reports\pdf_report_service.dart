/// خدمة إنشاء تقارير PDF احترافية
/// 
/// هذه الخدمة مسؤولة عن إنشاء ملفات PDF للتقارير مع دعم كامل للغة العربية
/// وتضمين الرسوم البيانية والجداول والتصميم الاحترافي

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/professional_reports/models/report_data_models.dart';
import 'package:flutter_application_2/professional_reports/models/report_template_models.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة إنشاء تقارير PDF احترافية
class PdfReportService {
  /// الخط العربي المحمل
  pw.Font? _arabicFont;
  
  /// الخط العربي العريض
  pw.Font? _arabicBoldFont;

  /// تحميل الخطوط العربية
  /// 
  /// يجب استدعاء هذه الدالة قبل إنشاء أي تقرير PDF
  Future<void> loadArabicFonts() async {
    try {
      debugPrint('🔄 تحميل الخطوط العربية...');
      
      // محاولة تحميل خط Cairo
      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        _arabicFont = pw.Font.ttf(fontData);
        debugPrint('✅ تم تحميل خط Cairo العادي');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على خط Cairo، محاولة تحميل NotoSansArabic...');
        try {
          final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
          _arabicFont = pw.Font.ttf(fontData);
          debugPrint('✅ تم تحميل خط NotoSansArabic العادي');
        } catch (e2) {
          debugPrint('⚠️ لم يتم العثور على الخطوط العربية، استخدام الخط الافتراضي');
          _arabicFont = pw.Font.courier();
        }
      }

      // محاولة تحميل الخط العريض
      try {
        final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        _arabicBoldFont = pw.Font.ttf(boldFontData);
        debugPrint('✅ تم تحميل خط Cairo العريض');
      } catch (e) {
        try {
          final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
          _arabicBoldFont = pw.Font.ttf(boldFontData);
          debugPrint('✅ تم تحميل خط NotoSansArabic العريض');
        } catch (e2) {
          _arabicBoldFont = _arabicFont; // استخدام نفس الخط العادي
          debugPrint('⚠️ لم يتم العثور على الخط العريض، استخدام الخط العادي');
        }
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الخطوط العربية: $e');
      _arabicFont = pw.Font.courier();
      _arabicBoldFont = pw.Font.courier();
    }
  }

  /// إنشاء تقرير ملخص المهام بتنسيق PDF
  /// 
  /// [tasks] قائمة المهام
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  /// [fileName] اسم الملف (اختياري)
  Future<String?> createTaskSummaryReport({
    required List<TaskReportData> tasks,
    required ReportStatistics statistics,
    required ReportTemplate template,
    String? fileName,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء تقرير ملخص المهام...');
      
      // التأكد من تحميل الخطوط
      if (_arabicFont == null) {
        await loadArabicFonts();
      }

      // إنشاء مستند PDF
      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
      );

      // إضافة صفحة الغلاف
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildCoverPage(template, 'تقرير ملخص المهام');
          },
        ),
      );

      // إضافة صفحة الملخص التنفيذي
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildExecutiveSummaryPage(statistics, template);
          },
        ),
      );

      // إضافة صفحة الرسوم البيانية
      if (template.contentSettings.showCharts) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildChartsPage(tasks, statistics, template);
            },
          ),
        );
      }

      // إضافة صفحة الجداول
      if (template.contentSettings.showTables) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildTablesPage(tasks, template);
            },
          ),
        );
      }

      // حفظ الملف
      final filePath = await _savePdfFile(pdf, fileName ?? 'تقرير_ملخص_المهام');
      
      debugPrint('✅ تم إنشاء تقرير ملخص المهام بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء تقرير ملخص المهام: $e');
      return null;
    }
  }

  /// إنشاء تقرير تفاصيل المهام بتنسيق PDF
  /// 
  /// [tasks] قائمة المهام
  /// [template] قالب التقرير
  /// [fileName] اسم الملف (اختياري)
  Future<String?> createTaskDetailsReport({
    required List<TaskReportData> tasks,
    required ReportTemplate template,
    String? fileName,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء تقرير تفاصيل المهام...');
      
      // التأكد من تحميل الخطوط
      if (_arabicFont == null) {
        await loadArabicFonts();
      }

      // إنشاء مستند PDF
      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
      );

      // إضافة صفحة الغلاف
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildCoverPage(template, 'تقرير تفاصيل المهام');
          },
        ),
      );

      // تقسيم المهام إلى صفحات
      final itemsPerPage = template.contentSettings.itemsPerPage;
      for (int i = 0; i < tasks.length; i += itemsPerPage) {
        final pageTasks = tasks.skip(i).take(itemsPerPage).toList();
        
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildTaskDetailsPage(pageTasks, template, i ~/ itemsPerPage + 1);
            },
          ),
        );
      }

      // حفظ الملف
      final filePath = await _savePdfFile(pdf, fileName ?? 'تقرير_تفاصيل_المهام');
      
      debugPrint('✅ تم إنشاء تقرير تفاصيل المهام بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء تقرير تفاصيل المهام: $e');
      return null;
    }
  }

  /// بناء صفحة الغلاف
  /// 
  /// [template] قالب التقرير
  /// [title] عنوان التقرير
  pw.Widget _buildCoverPage(ReportTemplate template, String title) {
    return pw.Column(
      mainAxisAlignment: pw.MainAxisAlignment.center,
      children: [
        // الشعار (إذا كان متاحاً)
        if (template.designSettings.logoPath != null)
          pw.Container(
            height: 100,
            width: 100,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey),
            ),
            child: pw.Center(
              child: pw.Text(
                'الشعار',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                  color: PdfColors.grey,
                ),
              ),
            ),
          ),
        
        pw.SizedBox(height: 50),
        
        // عنوان التقرير
        pw.Text(
          title,
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: template.designSettings.titleFontSize,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
          textAlign: pw.TextAlign.center,
        ),
        
        pw.SizedBox(height: 30),
        
        // تاريخ التقرير
        pw.Text(
          'تاريخ التقرير: ${DateFormat('yyyy/MM/dd - HH:mm', 'ar').format(DateTime.now())}',
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize + 2,
            color: PdfColors.grey700,
          ),
          textAlign: pw.TextAlign.center,
        ),
        
        pw.SizedBox(height: 20),
        
        // معلومات إضافية
        pw.Container(
          padding: const pw.EdgeInsets.all(20),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(
              color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              width: 2,
            ),
            borderRadius: pw.BorderRadius.circular(10),
          ),
          child: pw.Column(
            children: [
              pw.Text(
                'نظام إدارة المهام',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 16,
                ),
                textAlign: pw.TextAlign.center,
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'تقرير شامل ومفصل عن حالة المهام والأداء',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الملخص التنفيذي
  /// 
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  pw.Widget _buildExecutiveSummaryPage(ReportStatistics statistics, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'الملخص التنفيذي',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // الإحصائيات الرئيسية
        pw.Container(
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey100,
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            children: [
              _buildStatisticRow('إجمالي المهام', statistics.totalTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام المكتملة', statistics.completedTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام قيد التنفيذ', statistics.inProgressTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام المتأخرة', statistics.overdueTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('نسبة الإنجاز', '${statistics.completionRate.toStringAsFixed(1)}%'),
            ],
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // تحليل الأداء
        pw.Text(
          'تحليل الأداء',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 16,
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        pw.Text(
          _generatePerformanceAnalysis(statistics),
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize,
            lineSpacing: 1.5,
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // التوصيات
        pw.Text(
          'التوصيات',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 16,
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        pw.Text(
          _generateRecommendations(statistics),
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize,
            lineSpacing: 1.5,
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الرسوم البيانية
  /// 
  /// [tasks] قائمة المهام
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  pw.Widget _buildChartsPage(List<TaskReportData> tasks, ReportStatistics statistics, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'الرسوم البيانية والتحليلات',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // رسم بياني للحالات (محاكاة)
        pw.Container(
          height: 200,
          width: double.infinity,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                'توزيع المهام حسب الحالة',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'مكتملة: ${statistics.completedTasks} | قيد التنفيذ: ${statistics.inProgressTasks} | متأخرة: ${statistics.overdueTasks}',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // رسم بياني للأداء (محاكاة)
        pw.Container(
          height: 200,
          width: double.infinity,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                'مؤشر الأداء العام',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'نسبة الإنجاز: ${statistics.completionRate.toStringAsFixed(1)}%',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 16,
                  color: statistics.completionRate >= 70 ? PdfColors.green : PdfColors.orange,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الجداول
  /// 
  /// [tasks] قائمة المهام
  /// [template] قالب التقرير
  pw.Widget _buildTablesPage(List<TaskReportData> tasks, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'جدول المهام التفصيلي',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // الجدول
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(1),
          },
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              ),
              children: [
                _buildTableCell('عنوان المهمة', isHeader: true),
                _buildTableCell('الحالة', isHeader: true),
                _buildTableCell('المكلف', isHeader: true),
                _buildTableCell('الإنجاز', isHeader: true),
              ],
            ),
            
            // بيانات الجدول
            ...tasks.take(20).map((task) => pw.TableRow(
              children: [
                _buildTableCell(task.title),
                _buildTableCell(task.status),
                _buildTableCell(task.assignee?.name ?? task.creator.name),
                _buildTableCell('${task.completionPercentage}%'),
              ],
            )),
          ],
        ),
        
        pw.SizedBox(height: 20),
        
        // ملاحظة
        if (tasks.length > 20)
          pw.Text(
            'ملاحظة: يعرض الجدول أول 20 مهمة فقط. للاطلاع على جميع المهام، يرجى الرجوع إلى تقرير التفاصيل الكامل.',
            style: pw.TextStyle(
              font: _arabicFont,
              fontSize: 10,
              color: PdfColors.grey600,
              fontStyle: pw.FontStyle.italic,
            ),
          ),
      ],
    );
  }

  /// بناء صفحة تفاصيل المهام
  /// 
  /// [tasks] قائمة المهام في الصفحة
  /// [template] قالب التقرير
  /// [pageNumber] رقم الصفحة
  pw.Widget _buildTaskDetailsPage(List<TaskReportData> tasks, ReportTemplate template, int pageNumber) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'تفاصيل المهام - الصفحة $pageNumber',
              style: pw.TextStyle(
                font: _arabicBoldFont,
                fontSize: 18,
                color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              ),
            ),
            pw.Text(
              DateFormat('yyyy/MM/dd', 'ar').format(DateTime.now()),
              style: pw.TextStyle(
                font: _arabicFont,
                fontSize: 12,
                color: PdfColors.grey600,
              ),
            ),
          ],
        ),
        
        pw.SizedBox(height: 20),
        
        // تفاصيل كل مهمة
        ...tasks.map((task) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 15),
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey300),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // عنوان المهمة
              pw.Text(
                task.title,
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              
              pw.SizedBox(height: 8),
              
              // معلومات المهمة
              pw.Row(
                children: [
                  pw.Expanded(
                    child: _buildDetailRow('الحالة', task.status),
                  ),
                  pw.Expanded(
                    child: _buildDetailRow('الأولوية', task.priority),
                  ),
                ],
              ),
              
              pw.SizedBox(height: 5),
              
              pw.Row(
                children: [
                  pw.Expanded(
                    child: _buildDetailRow('المكلف', task.assignee?.name ?? 'غير محدد'),
                  ),
                  pw.Expanded(
                    child: _buildDetailRow('الإنجاز', '${task.completionPercentage}%'),
                  ),
                ],
              ),
              
              if (task.description != null && task.description!.isNotEmpty) ...[
                pw.SizedBox(height: 8),
                pw.Text(
                  'الوصف: ${task.description}',
                  style: pw.TextStyle(
                    font: _arabicFont,
                    fontSize: 10,
                    color: PdfColors.grey700,
                  ),
                ),
              ],
            ],
          ),
        )),
      ],
    );
  }

  /// بناء صف إحصائية
  /// 
  /// [label] التسمية
  /// [value] القيمة
  pw.Widget _buildStatisticRow(String label, String value) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: 12,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 12,
            color: PdfColors.blue800,
          ),
        ),
      ],
    );
  }

  /// بناء خلية جدول
  /// 
  /// [text] النص
  /// [isHeader] هل هي خلية رأس
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: isHeader ? _arabicBoldFont : _arabicFont,
          fontSize: isHeader ? 12 : 10,
          color: isHeader ? PdfColors.white : PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء صف تفاصيل
  /// 
  /// [label] التسمية
  /// [value] القيمة
  pw.Widget _buildDetailRow(String label, String value) {
    return pw.RichText(
      text: pw.TextSpan(
        children: [
          pw.TextSpan(
            text: '$label: ',
            style: pw.TextStyle(
              font: _arabicBoldFont,
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.TextSpan(
            text: value,
            style: pw.TextStyle(
              font: _arabicFont,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// توليد تحليل الأداء
  /// 
  /// [statistics] إحصائيات التقرير
  String _generatePerformanceAnalysis(ReportStatistics statistics) {
    final completionRate = statistics.completionRate;
    final overdueRate = statistics.overdueRate;
    
    String analysis = '';
    
    if (completionRate >= 80) {
      analysis += 'يظهر الأداء العام مستوى ممتاز مع نسبة إنجاز عالية تبلغ ${completionRate.toStringAsFixed(1)}%. ';
    } else if (completionRate >= 60) {
      analysis += 'يظهر الأداء العام مستوى جيد مع نسبة إنجاز ${completionRate.toStringAsFixed(1)}% ولكن يحتاج إلى تحسين. ';
    } else {
      analysis += 'يظهر الأداء العام مستوى منخفض مع نسبة إنجاز ${completionRate.toStringAsFixed(1)}% ويحتاج إلى تدخل فوري. ';
    }
    
    if (overdueRate > 20) {
      analysis += 'هناك نسبة عالية من المهام المتأخرة (${overdueRate.toStringAsFixed(1)}%) مما يتطلب مراجعة عملية التخطيط والمتابعة. ';
    } else if (overdueRate > 10) {
      analysis += 'هناك نسبة متوسطة من المهام المتأخرة (${overdueRate.toStringAsFixed(1)}%) تحتاج إلى متابعة. ';
    } else {
      analysis += 'نسبة المهام المتأخرة منخفضة (${overdueRate.toStringAsFixed(1)}%) مما يدل على إدارة جيدة للوقت. ';
    }
    
    return analysis;
  }

  /// توليد التوصيات
  /// 
  /// [statistics] إحصائيات التقرير
  String _generateRecommendations(ReportStatistics statistics) {
    List<String> recommendations = [];
    
    if (statistics.completionRate < 70) {
      recommendations.add('• تحسين عملية متابعة المهام وتقديم الدعم اللازم للفرق');
      recommendations.add('• مراجعة توزيع المهام والتأكد من التوازن في العبء');
    }
    
    if (statistics.overdueRate > 15) {
      recommendations.add('• تحسين عملية التخطيط الزمني للمهام');
      recommendations.add('• إنشاء نظام تنبيهات مبكرة للمهام المعرضة للتأخير');
    }
    
    if (statistics.inProgressTasks > statistics.completedTasks) {
      recommendations.add('• التركيز على إنجاز المهام قيد التنفيذ قبل بدء مهام جديدة');
      recommendations.add('• تحديد العوائق التي تمنع إتمام المهام ومعالجتها');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('• الحفاظ على مستوى الأداء الحالي');
      recommendations.add('• البحث عن فرص التحسين المستمر');
    }
    
    return recommendations.join('\n');
  }

  /// حفظ ملف PDF
  /// 
  /// [pdf] مستند PDF
  /// [fileName] اسم الملف
  Future<String> _savePdfFile(pw.Document pdf, String fileName) async {
    try {
      // الحصول على مجلد التنزيلات
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');
      
      // إنشاء مجلد التقارير إذا لم يكن موجوداً
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }
      
      // إنشاء اسم الملف مع التاريخ
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final filePath = '${reportsDir.path}/${fileName}_$timestamp.pdf';
      
      // حفظ الملف
      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());
      
      debugPrint('✅ تم حفظ ملف PDF: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في حفظ ملف PDF: $e');
      rethrow;
    }
  }
}



  /// الخط العربي المحمل
  pw.Font? _arabicFont;
  
  /// الخط العربي العريض
  pw.Font? _arabicBoldFont;

  /// تحميل الخطوط العربية
  /// 
  /// يجب استدعاء هذه الدالة قبل إنشاء أي تقرير PDF
  Future<void> loadArabicFonts() async {
    try {
      debugPrint('🔄 تحميل الخطوط العربية...');
      
      // محاولة تحميل خط Cairo
      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        _arabicFont = pw.Font.ttf(fontData);
        debugPrint('✅ تم تحميل خط Cairo العادي');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على خط Cairo، محاولة تحميل NotoSansArabic...');
        try {
          final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
          _arabicFont = pw.Font.ttf(fontData);
          debugPrint('✅ تم تحميل خط NotoSansArabic العادي');
        } catch (e2) {
          debugPrint('⚠️ لم يتم العثور على الخطوط العربية، استخدام الخط الافتراضي');
          _arabicFont = pw.Font.courier();
        }
      }

      // محاولة تحميل الخط العريض
      try {
        final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        _arabicBoldFont = pw.Font.ttf(boldFontData);
        debugPrint('✅ تم تحميل خط Cairo العريض');
      } catch (e) {
        try {
          final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
          _arabicBoldFont = pw.Font.ttf(boldFontData);
          debugPrint('✅ تم تحميل خط NotoSansArabic العريض');
        } catch (e2) {
          _arabicBoldFont = _arabicFont; // استخدام نفس الخط العادي
          debugPrint('⚠️ لم يتم العثور على الخط العريض، استخدام الخط العادي');
        }
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الخطوط العربية: $e');
      _arabicFont = pw.Font.courier();
      _arabicBoldFont = pw.Font.courier();
    }
  }

  /// إنشاء تقرير ملخص المهام بتنسيق PDF
  /// 
  /// [tasks] قائمة المهام
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  /// [fileName] اسم الملف (اختياري)
  Future<String?> createTaskSummaryReport({
    required List<TaskReportData> tasks,
    required ReportStatistics statistics,
    required ReportTemplate template,
    String? fileName,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء تقرير ملخص المهام...');
      
      // التأكد من تحميل الخطوط
      if (_arabicFont == null) {
        await loadArabicFonts();
      }

      // إنشاء مستند PDF
      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
      );

      // إضافة صفحة الغلاف
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildCoverPage(template, 'تقرير ملخص المهام');
          },
        ),
      );

      // إضافة صفحة الملخص التنفيذي
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildExecutiveSummaryPage(statistics, template);
          },
        ),
      );

      // إضافة صفحة الرسوم البيانية
      if (template.contentSettings.showCharts) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildChartsPage(tasks, statistics, template);
            },
          ),
        );
      }

      // إضافة صفحة الجداول
      if (template.contentSettings.showTables) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildTablesPage(tasks, template);
            },
          ),
        );
      }

      // حفظ الملف
      final filePath = await _savePdfFile(pdf, fileName ?? 'تقرير_ملخص_المهام');
      
      debugPrint('✅ تم إنشاء تقرير ملخص المهام بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء تقرير ملخص المهام: $e');
      return null;
    }
  }

  /// إنشاء تقرير تفاصيل المهام بتنسيق PDF
  /// 
  /// [tasks] قائمة المهام
  /// [template] قالب التقرير
  /// [fileName] اسم الملف (اختياري)
  Future<String?> createTaskDetailsReport({
    required List<TaskReportData> tasks,
    required ReportTemplate template,
    String? fileName,
  }) async {
    try {
      debugPrint('🔄 بدء إنشاء تقرير تفاصيل المهام...');
      
      // التأكد من تحميل الخطوط
      if (_arabicFont == null) {
        await loadArabicFonts();
      }

      // إنشاء مستند PDF
      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
      );

      // إضافة صفحة الغلاف
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return _buildCoverPage(template, 'تقرير تفاصيل المهام');
          },
        ),
      );

      // تقسيم المهام إلى صفحات
      final itemsPerPage = template.contentSettings.itemsPerPage;
      for (int i = 0; i < tasks.length; i += itemsPerPage) {
        final pageTasks = tasks.skip(i).take(itemsPerPage).toList();
        
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return _buildTaskDetailsPage(pageTasks, template, i ~/ itemsPerPage + 1);
            },
          ),
        );
      }

      // حفظ الملف
      final filePath = await _savePdfFile(pdf, fileName ?? 'تقرير_تفاصيل_المهام');
      
      debugPrint('✅ تم إنشاء تقرير تفاصيل المهام بنجاح: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء تقرير تفاصيل المهام: $e');
      return null;
    }
  }

  /// بناء صفحة الغلاف
  /// 
  /// [template] قالب التقرير
  /// [title] عنوان التقرير
  pw.Widget _buildCoverPage(ReportTemplate template, String title) {
    return pw.Column(
      mainAxisAlignment: pw.MainAxisAlignment.center,
      children: [
        // الشعار (إذا كان متاحاً)
        if (template.designSettings.logoPath != null)
          pw.Container(
            height: 100,
            width: 100,
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey),
            ),
            child: pw.Center(
              child: pw.Text(
                'الشعار',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                  color: PdfColors.grey,
                ),
              ),
            ),
          ),
        
        pw.SizedBox(height: 50),
        
        // عنوان التقرير
        pw.Text(
          title,
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: template.designSettings.titleFontSize,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
          textAlign: pw.TextAlign.center,
        ),
        
        pw.SizedBox(height: 30),
        
        // تاريخ التقرير
        pw.Text(
          'تاريخ التقرير: ${DateFormat('yyyy/MM/dd - HH:mm', 'ar').format(DateTime.now())}',
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize + 2,
            color: PdfColors.grey700,
          ),
          textAlign: pw.TextAlign.center,
        ),
        
        pw.SizedBox(height: 20),
        
        // معلومات إضافية
        pw.Container(
          padding: const pw.EdgeInsets.all(20),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(
              color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              width: 2,
            ),
            borderRadius: pw.BorderRadius.circular(10),
          ),
          child: pw.Column(
            children: [
              pw.Text(
                'نظام إدارة المهام',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 16,
                ),
                textAlign: pw.TextAlign.center,
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'تقرير شامل ومفصل عن حالة المهام والأداء',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الملخص التنفيذي
  /// 
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  pw.Widget _buildExecutiveSummaryPage(ReportStatistics statistics, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'الملخص التنفيذي',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // الإحصائيات الرئيسية
        pw.Container(
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey100,
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            children: [
              _buildStatisticRow('إجمالي المهام', statistics.totalTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام المكتملة', statistics.completedTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام قيد التنفيذ', statistics.inProgressTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('المهام المتأخرة', statistics.overdueTasks.toString()),
              pw.SizedBox(height: 8),
              _buildStatisticRow('نسبة الإنجاز', '${statistics.completionRate.toStringAsFixed(1)}%'),
            ],
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // تحليل الأداء
        pw.Text(
          'تحليل الأداء',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 16,
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        pw.Text(
          _generatePerformanceAnalysis(statistics),
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize,
            lineSpacing: 1.5,
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // التوصيات
        pw.Text(
          'التوصيات',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 16,
          ),
        ),
        
        pw.SizedBox(height: 15),
        
        pw.Text(
          _generateRecommendations(statistics),
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: template.designSettings.bodyFontSize,
            lineSpacing: 1.5,
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الرسوم البيانية
  /// 
  /// [tasks] قائمة المهام
  /// [statistics] إحصائيات التقرير
  /// [template] قالب التقرير
  pw.Widget _buildChartsPage(List<TaskReportData> tasks, ReportStatistics statistics, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'الرسوم البيانية والتحليلات',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // رسم بياني للحالات (محاكاة)
        pw.Container(
          height: 200,
          width: double.infinity,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                'توزيع المهام حسب الحالة',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'مكتملة: ${statistics.completedTasks} | قيد التنفيذ: ${statistics.inProgressTasks} | متأخرة: ${statistics.overdueTasks}',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        
        pw.SizedBox(height: 30),
        
        // رسم بياني للأداء (محاكاة)
        pw.Container(
          height: 200,
          width: double.infinity,
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                'مؤشر الأداء العام',
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Text(
                'نسبة الإنجاز: ${statistics.completionRate.toStringAsFixed(1)}%',
                style: pw.TextStyle(
                  font: _arabicFont,
                  fontSize: 16,
                  color: statistics.completionRate >= 70 ? PdfColors.green : PdfColors.orange,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صفحة الجداول
  /// 
  /// [tasks] قائمة المهام
  /// [template] قالب التقرير
  pw.Widget _buildTablesPage(List<TaskReportData> tasks, ReportTemplate template) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Text(
          'جدول المهام التفصيلي',
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 20,
            color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
          ),
        ),
        
        pw.SizedBox(height: 20),
        
        // الجدول
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(1),
          },
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              ),
              children: [
                _buildTableCell('عنوان المهمة', isHeader: true),
                _buildTableCell('الحالة', isHeader: true),
                _buildTableCell('المكلف', isHeader: true),
                _buildTableCell('الإنجاز', isHeader: true),
              ],
            ),
            
            // بيانات الجدول
            ...tasks.take(20).map((task) => pw.TableRow(
              children: [
                _buildTableCell(task.title),
                _buildTableCell(task.status),
                _buildTableCell(task.assignee?.name ?? task.creator.name),
                _buildTableCell('${task.completionPercentage}%'),
              ],
            )),
          ],
        ),
        
        pw.SizedBox(height: 20),
        
        // ملاحظة
        if (tasks.length > 20)
          pw.Text(
            'ملاحظة: يعرض الجدول أول 20 مهمة فقط. للاطلاع على جميع المهام، يرجى الرجوع إلى تقرير التفاصيل الكامل.',
            style: pw.TextStyle(
              font: _arabicFont,
              fontSize: 10,
              color: PdfColors.grey600,
              fontStyle: pw.FontStyle.italic,
            ),
          ),
      ],
    );
  }

  /// بناء صفحة تفاصيل المهام
  /// 
  /// [tasks] قائمة المهام في الصفحة
  /// [template] قالب التقرير
  /// [pageNumber] رقم الصفحة
  pw.Widget _buildTaskDetailsPage(List<TaskReportData> tasks, ReportTemplate template, int pageNumber) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الصفحة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'تفاصيل المهام - الصفحة $pageNumber',
              style: pw.TextStyle(
                font: _arabicBoldFont,
                fontSize: 18,
                color: PdfColor.fromHex(template.designSettings.primaryColor.value.toRadixString(16).substring(2)),
              ),
            ),
            pw.Text(
              DateFormat('yyyy/MM/dd', 'ar').format(DateTime.now()),
              style: pw.TextStyle(
                font: _arabicFont,
                fontSize: 12,
                color: PdfColors.grey600,
              ),
            ),
          ],
        ),
        
        pw.SizedBox(height: 20),
        
        // تفاصيل كل مهمة
        ...tasks.map((task) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 15),
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey300),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // عنوان المهمة
              pw.Text(
                task.title,
                style: pw.TextStyle(
                  font: _arabicBoldFont,
                  fontSize: 14,
                ),
              ),
              
              pw.SizedBox(height: 8),
              
              // معلومات المهمة
              pw.Row(
                children: [
                  pw.Expanded(
                    child: _buildDetailRow('الحالة', task.status),
                  ),
                  pw.Expanded(
                    child: _buildDetailRow('الأولوية', task.priority),
                  ),
                ],
              ),
              
              pw.SizedBox(height: 5),
              
              pw.Row(
                children: [
                  pw.Expanded(
                    child: _buildDetailRow('المكلف', task.assignee?.name ?? 'غير محدد'),
                  ),
                  pw.Expanded(
                    child: _buildDetailRow('الإنجاز', '${task.completionPercentage}%'),
                  ),
                ],
              ),
              
              if (task.description != null && task.description!.isNotEmpty) ...[
                pw.SizedBox(height: 8),
                pw.Text(
                  'الوصف: ${task.description}',
                  style: pw.TextStyle(
                    font: _arabicFont,
                    fontSize: 10,
                    color: PdfColors.grey700,
                  ),
                ),
              ],
            ],
          ),
        )),
      ],
    );
  }

  /// بناء صف إحصائية
  /// 
  /// [label] التسمية
  /// [value] القيمة
  pw.Widget _buildStatisticRow(String label, String value) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: _arabicFont,
            fontSize: 12,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: _arabicBoldFont,
            fontSize: 12,
            color: PdfColors.blue800,
          ),
        ),
      ],
    );
  }

  /// بناء خلية جدول
  /// 
  /// [text] النص
  /// [isHeader] هل هي خلية رأس
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: isHeader ? _arabicBoldFont : _arabicFont,
          fontSize: isHeader ? 12 : 10,
          color: isHeader ? PdfColors.white : PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء صف تفاصيل
  /// 
  /// [label] التسمية
  /// [value] القيمة
  pw.Widget _buildDetailRow(String label, String value) {
    return pw.RichText(
      text: pw.TextSpan(
        children: [
          pw.TextSpan(
            text: '$label: ',
            style: pw.TextStyle(
              font: _arabicBoldFont,
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.TextSpan(
            text: value,
            style: pw.TextStyle(
              font: _arabicFont,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// توليد تحليل الأداء
  /// 
  /// [statistics] إحصائيات التقرير
  String _generatePerformanceAnalysis(ReportStatistics statistics) {
    final completionRate = statistics.completionRate;
    final overdueRate = statistics.overdueRate;
    
    String analysis = '';
    
    if (completionRate >= 80) {
      analysis += 'يظهر الأداء العام مستوى ممتاز مع نسبة إنجاز عالية تبلغ ${completionRate.toStringAsFixed(1)}%. ';
    } else if (completionRate >= 60) {
      analysis += 'يظهر الأداء العام مستوى جيد مع نسبة إنجاز ${completionRate.toStringAsFixed(1)}% ولكن يحتاج إلى تحسين. ';
    } else {
      analysis += 'يظهر الأداء العام مستوى منخفض مع نسبة إنجاز ${completionRate.toStringAsFixed(1)}% ويحتاج إلى تدخل فوري. ';
    }
    
    if (overdueRate > 20) {
      analysis += 'هناك نسبة عالية من المهام المتأخرة (${overdueRate.toStringAsFixed(1)}%) مما يتطلب مراجعة عملية التخطيط والمتابعة. ';
    } else if (overdueRate > 10) {
      analysis += 'هناك نسبة متوسطة من المهام المتأخرة (${overdueRate.toStringAsFixed(1)}%) تحتاج إلى متابعة. ';
    } else {
      analysis += 'نسبة المهام المتأخرة منخفضة (${overdueRate.toStringAsFixed(1)}%) مما يدل على إدارة جيدة للوقت. ';
    }
    
    return analysis;
  }

  /// توليد التوصيات
  /// 
  /// [statistics] إحصائيات التقرير
  String _generateRecommendations(ReportStatistics statistics) {
    List<String> recommendations = [];
    
    if (statistics.completionRate < 70) {
      recommendations.add('• تحسين عملية متابعة المهام وتقديم الدعم اللازم للفرق');
      recommendations.add('• مراجعة توزيع المهام والتأكد من التوازن في العبء');
    }
    
    if (statistics.overdueRate > 15) {
      recommendations.add('• تحسين عملية التخطيط الزمني للمهام');
      recommendations.add('• إنشاء نظام تنبيهات مبكرة للمهام المعرضة للتأخير');
    }
    
    if (statistics.inProgressTasks > statistics.completedTasks) {
      recommendations.add('• التركيز على إنجاز المهام قيد التنفيذ قبل بدء مهام جديدة');
      recommendations.add('• تحديد العوائق التي تمنع إتمام المهام ومعالجتها');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('• الحفاظ على مستوى الأداء الحالي');
      recommendations.add('• البحث عن فرص التحسين المستمر');
    }
    
    return recommendations.join('\n');
  }

  /// حفظ ملف PDF
  /// 
  /// [pdf] مستند PDF
  /// [fileName] اسم الملف
  Future<String> _savePdfFile(pw.Document pdf, String fileName) async {
    try {
      // الحصول على مجلد التنزيلات
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');
      
      // إنشاء مجلد التقارير إذا لم يكن موجوداً
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }
      
      // إنشاء اسم الملف مع التاريخ
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final filePath = '${reportsDir.path}/${fileName}_$timestamp.pdf';
      
      // حفظ الملف
      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());
      
      debugPrint('✅ تم حفظ ملف PDF: $filePath');
      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في حفظ ملف PDF: $e');
      rethrow;
    }
  }
