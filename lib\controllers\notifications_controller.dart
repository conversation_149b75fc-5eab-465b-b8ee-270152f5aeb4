import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/notification_models.dart';
import '../services/api/notifications_api_service.dart';
import '../services/notifications_service.dart';
import 'auth_controller.dart';

/// متحكم الإشعارات
class NotificationsController extends GetxController {
  final NotificationsApiService _apiService = NotificationsApiService();

  // قوائم الإشعارات
  final RxList<NotificationModel> _allNotifications = <NotificationModel>[].obs;
  final RxList<NotificationModel> _filteredNotifications = <NotificationModel>[].obs;
  final RxList<NotificationModel> _unreadNotifications = <NotificationModel>[].obs;

  // الإشعار الحالي
  final Rx<NotificationModel?> _currentNotification = Rx<NotificationModel?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _typeFilter = Rx<String?>(null);
  final RxBool _showUnreadOnly = false.obs;

  // إحصائيات
  final RxInt _unreadCount = 0.obs;

  // Getters
  List<NotificationModel> get allNotifications => _allNotifications;
  List<NotificationModel> get filteredNotifications => _filteredNotifications;
  List<NotificationModel> get unreadNotifications => _unreadNotifications;
  NotificationModel? get currentNotification => _currentNotification.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get typeFilter => _typeFilter.value;
  bool get showUnreadOnly => _showUnreadOnly.value;
  int get unreadCount => _unreadCount.value;

  @override
  void onInit() {
    super.onInit();
    // تهيئة خدمة SignalR
    _initializeSignalRService();
    // تحميل البيانات
    loadAllNotifications();
    loadUnreadNotifications();
  }
  
  /// تهيئة خدمة SignalR
  void _initializeSignalRService() {
    try {
      // الحصول على مثيل خدمة SignalR
      debugPrint('تم تهيئة خدمة SignalR في متحكم الإشعارات');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة SignalR: $e');
    }
  }

  /// تحميل جميع الإشعارات
  Future<void> loadAllNotifications() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final notifications = await _apiService.getAllNotifications();
      _allNotifications.assignAll(notifications);
      _applyFilters();
      debugPrint('تم تحميل ${notifications.length} إشعار');
    } catch (e) {
      _error.value = 'خطأ في تحميل الإشعارات: $e';
      debugPrint('خطأ في تحميل الإشعارات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإشعارات غير المقروءة
  Future<void> loadUnreadNotifications() async {
    try {
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      if (userId == null) {
        debugPrint('لا يوجد مستخدم مسجل حالياً');
        _unreadNotifications.clear();
        _unreadCount.value = 0;
        return;
      }
      final notifications = await _apiService.getUnreadNotifications(userId);
      _unreadNotifications.assignAll(notifications);
      _unreadCount.value = notifications.length;
      debugPrint('تم تحميل ${notifications.length} إشعار غير مقروء');
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات غير المقروءة: $e');
    }
  }

  /// الحصول على إشعار بالمعرف
  Future<void> getNotificationById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final notification = await _apiService.getNotificationById(id);
      _currentNotification.value = notification;
      debugPrint('تم تحميل الإشعار: ${notification?.title ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل الإشعار: $e';
      debugPrint('خطأ في تحميل الإشعار: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء إشعار جديد
  Future<bool> createNotification(NotificationModel notification) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newNotification = await _apiService.createNotification(notification);
      _allNotifications.insert(0, newNotification);
      _applyFilters();
      await loadUnreadNotifications();
      debugPrint('تم إنشاء إشعار جديد: ${newNotification.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء الإشعار: $e';
      debugPrint('خطأ في إنشاء الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إشعار
  Future<bool> updateNotification(int id, NotificationModel notification) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateNotification(id, notification);
      final index = _allNotifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        _allNotifications[index] = notification;
        _applyFilters();
      }
      debugPrint('تم تحديث الإشعار: ${notification.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الإشعار: $e';
      debugPrint('خطأ في تحديث الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteNotification(id);
      _allNotifications.removeWhere((n) => n.id == id);
      _applyFilters();
      await loadUnreadNotifications();
      debugPrint('تم حذف الإشعار');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الإشعار: $e';
      debugPrint('خطأ في حذف الإشعار: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديد الإشعار كمقروء
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
      final index = _allNotifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _allNotifications[index] = _allNotifications[index].copyWith(isRead: true);
        _applyFilters();
      }
      await loadUnreadNotifications();
      debugPrint('تم تحديد الإشعار كمقروء');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllAsRead() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      if (userId == null) {
        debugPrint('لا يوجد مستخدم مسجل حالياً');
        return false;
      }
      await _apiService.markAllAsRead(userId);
      for (int i = 0; i < _allNotifications.length; i++) {
        _allNotifications[i] = _allNotifications[i].copyWith(isRead: true);
      }
      _applyFilters();
      await loadUnreadNotifications();
      debugPrint('تم تحديد جميع الإشعارات كمقروءة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديد جميع الإشعارات كمقروءة: $e';
      debugPrint('خطأ في تحديد جميع الإشعارات كمقروءة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف جميع الإشعارات المقروءة
  Future<bool> deleteAllRead() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteAllNotifications();
      _allNotifications.removeWhere((n) => n.isRead);
      _applyFilters();
      debugPrint('تم حذف جميع الإشعارات المقروءة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الإشعارات المقروءة: $e';
      debugPrint('خطأ في حذف الإشعارات المقروءة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allNotifications.where((notification) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!notification.title.toLowerCase().contains(query) &&
            !notification.content.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النوع
      if (_typeFilter.value != null && notification.type != _typeFilter.value) {
        return false;
      }

      // مرشح غير المقروءة فقط
      if (_showUnreadOnly.value && notification.isRead) {
        return false;
      }

      return true;
    }).toList();

    _filteredNotifications.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النوع
  void setTypeFilter(String? type) {
    _typeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح غير المقروءة فقط
  void setUnreadFilter(bool showUnreadOnly) {
    _showUnreadOnly.value = showUnreadOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _typeFilter.value = null;
    _showUnreadOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllNotifications(),
      loadUnreadNotifications(),
    ]);
  }
  
  /// إضافة إشعار جديد من SignalR
  void addNewNotification(Map<String, dynamic> notificationData) {
    try {
      // تحويل البيانات إلى نموذج إشعار
      final notification = NotificationModel.fromJson(notificationData);
      // التحقق من عدم وجود الإشعار مسبقاً
      final existingIndex = _allNotifications.indexWhere((n) => n.id == notification.id);
      if (existingIndex != -1) {
        // تحديث الإشعار الموجود
        _allNotifications[existingIndex] = notification;
      } else {
        // إضافة الإشعار الجديد في بداية القائمة
        _allNotifications.insert(0, notification);
      }
      // تحديث القوائم
      _applyFilters();
      // تحديث عدد الإشعارات غير المقروءة
      if (!notification.isRead) {
        _unreadCount.value++;
        _unreadNotifications.insert(0, notification);
      }
      // إظهار إشعار نظامي للمستخدم مع تخصيص حسب النوع
      _showLocalNotification(notification);
      debugPrint('تم إضافة إشعار جديد من SignalR: ${notification.title}');
    } catch (e) {
      debugPrint('خطأ في إضافة إشعار جديد من SignalR: $e');
    }
  }
  
  /// تحديث عدد الإشعارات غير المقروءة
  void updateUnreadCount(int count) {
    _unreadCount.value = count;
    debugPrint('تم تحديث عدد الإشعارات غير المقروءة: $count');
  }

  /// عرض إشعار محلي مخصص حسب النوع
  void _showLocalNotification(NotificationModel notification) {
    String title = notification.title;
    String body = notification.content;

    // تخصيص الإشعار حسب النوع
    switch (notification.type) {
      case 'reminder_6_hours':
        title = '⏰ تذكير عاجل';
        break;
      case 'reminder_24_hours':
        title = '⏰ تذكير يومي';
        break;
      case 'reminder_48_hours':
        title = '⏰ تذكير';
        break;
      case 'task_overdue':
        title = '🚨 مهمة متأخرة';
        break;
      case 'task_access_granted':
        title = '🔓 تم منح الوصول';
        break;
      case 'task_status_changed':
        title = '🔄 تم تغيير حالة المهمة';
        break;
      case 'task_priority_changed':
        title = '⚡ تم تغيير أولوية المهمة';
        break;
      case 'comment_added':
        title = '💬 تعليق جديد';
        break;
      case 'attachment_added':
        title = '📎 مرفق جديد';
        break;
      case 'attachment_deleted':
        title = '🗑️ تم حذف مرفق';
        break;
      case 'document_created':
        title = '📄 مستند جديد';
        break;
      case 'task_message_received':
        title = '💬 رسالة جديدة';
        break;
      case 'task_transferred':
        title = '🔄 تم تحويل مهمة';
        break;
      case 'task_updated':
        title = '📝 تم تحديث مهمة';
        break;
      case 'permission_granted':
        title = '🔐 تم منح صلاحية جديدة';
        break;
      case 'permission_revoked':
        title = '🚫 تم إلغاء صلاحية';
        break;
      case 'subtask_created':
        title = '📋 مهمة فرعية جديدة';
        break;
      case 'subtask_updated':
        title = '✏️ تم تحديث مهمة فرعية';
        break;
      case 'subtask_completed':
        title = '✅ تم إكمال مهمة فرعية';
        break;
      case 'message_received':
        title = '💬 رسالة جديدة في المجموعة';
        break;
    }

    NotificationsService.showSimpleNotification(
      title: title,
      body: body,
      payload: notification.actionUrl,
    );
  }
}
