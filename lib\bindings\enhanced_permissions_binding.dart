// import 'package:get/get.dart';
// import '../controllers/enhanced_permissions_controller.dart';
// import '../services/api/enhanced_permissions_api_service.dart';

// /// ربط تبعيات نظام الصلاحيات المحسن
// class EnhancedPermissionsBinding extends Bindings {
//   @override
//   void dependencies() {
//     // تسجيل خدمة API للصلاحيات المحسنة
//     Get.lazyPut<EnhancedPermissionsApiService>(
//       () => EnhancedPermissionsApiService(),
//       fenix: true,
//     );

//     // تسجيل متحكم الصلاحيات المحسنة
//     Get.lazyPut<EnhancedPermissionsController>(
//       () => EnhancedPermissionsController(),
//       fenix: true,
//     );
//   }
// }
