using System.ComponentModel.DataAnnotations;

namespace webApi.Models.Auth;

/// <summary>
/// نموذج طلب التسجيل
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// الاسم الكامل
    /// </summary>
    [Required(ErrorMessage = "الاسم مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// الاسم الأول
    /// </summary>
    [StringLength(50, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 50 حرف")]
    public string? FirstName { get; set; }

    /// <summary>
    /// الاسم الأخير
    /// </summary>
    [StringLength(50, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 50 حرف")]
    public string? LastName { get; set; }

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
    [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
    [StringLength(255, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 255 حرف")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// اسم المستخدم
    /// </summary>
    [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
    public string? Username { get; set; }

    /// <summary>
    /// كلمة المرور
    /// </summary>
    [Required(ErrorMessage = "كلمة المرور مطلوبة")]
    [MinLength(6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
    [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون أقل من 100 حرف")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// تأكيد كلمة المرور
    /// </summary>
    [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
    [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقتين")]
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// معرف القسم
    /// </summary>
    public int? DepartmentId { get; set; }

    /// <summary>
    /// دور المستخدم (افتراضي: مستخدم عادي)
    /// </summary>
    public UserRole Role { get; set; } = UserRole.User;
}
