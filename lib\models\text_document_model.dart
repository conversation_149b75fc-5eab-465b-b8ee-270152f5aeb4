import 'dart:convert';
import 'archive_models.dart';
import 'user_model.dart';

/// أنواع المستندات النصية
enum TextDocumentType {
  note('note', 'ملاحظة'),
  report('report', 'تقرير'),
  template('template', 'قالب'),
  letter('letter', 'خطاب'),
  memo('memo', 'مذكرة'),
  contract('contract', 'عقد'),
  other('other', 'أخرى');

  const TextDocumentType(this.value, this.displayName);

  final String value;
  final String displayName;

  static TextDocumentType fromValue(String value) {
    return TextDocumentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TextDocumentType.note,
    );
  }
}

/// نموذج المستند النصي - متوافق تماماً مع ArchiveDocument في الـ API
class TextDocument {
  final int id;
  final String title;
  final String? description;
  final String fileName;
  final String filePath;
  final String fileType;
  final int fileSize;
  final int? categoryId;
  final String? metadata;
  final String? content;
  final int uploadedBy;
  final int uploadedAt;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Additional properties for text documents
  final TextDocumentType type;
  final int? taskId;
  final bool isShared;

  // Navigation properties
  final ArchiveCategory? category;
  final User? uploadedByUser;
  final List<ArchiveTag>? tags;

  const TextDocument({
    required this.id,
    required this.title,
    this.description,
    required this.fileName,
    required this.filePath,
    required this.fileType,
    required this.fileSize,
    this.categoryId,
    this.metadata,
    this.content,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    required this.type,
    this.taskId,
    this.isShared = false,
    this.category,
    this.uploadedByUser,
    this.tags,
  });

  /// إنشاء مستند من JSON - متوافق مع ArchiveDocument API
  factory TextDocument.fromJson(Map<String, dynamic> json) {
    // Extract document type from metadata or default to note
    final metadata = json['metadata'] as String?;
    final type = metadata != null
        ? TextDocumentType.fromValue(metadata)
        : TextDocumentType.note;

    return TextDocument(
      id: json['id'] as int? ?? 0,
      title: json['title'] as String? ?? '',
      description: json['description'] as String?,
      fileName: json['fileName'] as String? ?? 'document.txt',
      filePath: json['filePath'] as String? ?? '',
      fileType: json['fileType'] as String? ?? 'text/plain',
      fileSize: json['fileSize'] != null ? (json['fileSize'] as num).toInt() : 0,
      categoryId: json['categoryId'] as int?,
      metadata: metadata,
      content: json['content'] as String?,
      uploadedBy: json['uploadedBy'] as int? ?? json['createdBy'] as int? ?? 1,
      uploadedAt: json['uploadedAt'] != null
          ? (json['uploadedAt'] as num).toInt()
          : json['createdAt'] != null
              ? (json['createdAt'] as num).toInt()
              : DateTime.now().millisecondsSinceEpoch ~/ 1000,
      createdBy: json['createdBy'] as int? ?? 1,
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as num).toInt()
          : DateTime.now().millisecondsSinceEpoch ~/ 1000,
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as num).toInt()
          : null,
      isDeleted: json['isDeleted'] as bool? ?? false,
      type: type,
      taskId: json['taskId'] as int?,
      isShared: json['isShared'] as bool? ?? false,
      category: json['category'] != null
          ? ArchiveCategory.fromJson(json['category'] as Map<String, dynamic>)
          : null,
      uploadedByUser: json['uploadedByUser'] != null
          ? User.fromJson(json['uploadedByUser'] as Map<String, dynamic>)
          : null,
      tags: json['tags'] != null
          ? (json['tags'] as List).map((tag) => ArchiveTag.fromJson(tag as Map<String, dynamic>)).toList()
          : null,
    );
  }

  /// تحويل المستند إلى JSON - متوافق مع ArchiveDocument API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'fileName': fileName,
      'filePath': filePath,
      'fileType': fileType,
      'fileSize': fileSize,
      'categoryId': categoryId,
      'metadata': type.value, // Store document type in metadata
      'content': content,
      'uploadedBy': uploadedBy,
      'uploadedAt': uploadedAt,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      // Additional fields for text documents
      'taskId': taskId,
      'isShared': isShared,
    };
  }

  /// إنشاء نسخة محدثة من المستند
  TextDocument copyWith({
    int? id,
    String? title,
    String? description,
    String? fileName,
    String? filePath,
    String? fileType,
    int? fileSize,
    int? categoryId,
    String? metadata,
    String? content,
    int? uploadedBy,
    int? uploadedAt,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    TextDocumentType? type,
    int? taskId,
    bool? isShared,
  }) {
    return TextDocument(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileType: fileType ?? this.fileType,
      fileSize: fileSize ?? this.fileSize,
      categoryId: categoryId ?? this.categoryId,
      metadata: metadata ?? this.metadata,
      content: content ?? this.content,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      type: type ?? this.type,
      taskId: taskId ?? this.taskId,
      isShared: isShared ?? this.isShared,
      category: category,
      uploadedByUser: uploadedByUser,
      tags: tags,
    );
  }

  /// الحصول على النص الخام من المحتوى
  String getDocumentText() {
    try {
      final contentText = content ?? '';
      if (contentText.isEmpty) return '';

      // محاولة تحليل المحتوى كـ JSON
      final decoded = jsonDecode(contentText);
      if (decoded is Map<String, dynamic> && decoded.containsKey('text')) {
        return decoded['text'] as String;
      }
      // إذا لم يكن JSON، إرجاع المحتوى كما هو
      return contentText;
    } catch (e) {
      // إذا فشل التحليل، إرجاع المحتوى كما هو
      return content ?? '';
    }
  }

  /// تحويل النص إلى تنسيق JSON للحفظ
  static String textToJson(String text) {
    return jsonEncode({
      'text': text,
      'format': 'plain',
      'version': '1.0',
    });
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// تحويل DateTime إلى Unix timestamp
  static int dateTimeToTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch ~/ 1000;
  }

  @override
  String toString() {
    return 'TextDocument(id: $id, title: $title, type: ${type.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextDocument && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مستند جديد
class CreateTextDocumentRequest {
  final String title;
  final String content;
  final TextDocumentType type;
  final String? taskId;
  final bool isShared;

  const CreateTextDocumentRequest({
    required this.title,
    required this.content,
    required this.type,
    this.taskId,
    this.isShared = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'type': type.value,
      'taskId': taskId,
      'isShared': isShared,
    };
  }
}

/// نموذج طلب تحديث مستند
class UpdateTextDocumentRequest {
  final String? title;
  final String? content;
  final TextDocumentType? type;
  final bool? isShared;

  const UpdateTextDocumentRequest({
    this.title,
    this.content,
    this.type,
    this.isShared,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (content != null) json['content'] = content;
    if (type != null) json['type'] = type!.value;
    if (isShared != null) json['isShared'] = isShared;
    return json;
  }
}
