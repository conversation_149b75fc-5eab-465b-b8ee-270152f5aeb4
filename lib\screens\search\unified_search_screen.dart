import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/search_models.dart';
import 'package:get/get.dart';
import '../../controllers/unified_search_controller.dart';
import '../../utils/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../routes/app_routes.dart';
import '../../services/unified_permission_service.dart';
import '../widgets/search/search_bar_widget.dart';

/// شاشة البحث الموحد
class UnifiedSearchScreen extends StatefulWidget {
  const UnifiedSearchScreen({super.key});

  @override
  State<UnifiedSearchScreen> createState() => _UnifiedSearchScreenState();
}

class _UnifiedSearchScreenState extends State<UnifiedSearchScreen>
    with SingleTickerProviderStateMixin {
  // وحدة تحكم البحث
  final UnifiedSearchController _searchController =
      Get.find<UnifiedSearchController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  // وحدة تحكم التبويب
  late TabController _tabController;

  // تحكم النص
  final TextEditingController _textController = TextEditingController();

  // مؤشر التركيز
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // إنشاء وحدة تحكم التبويب
    _tabController = TabController(
      length: SearchResultType.values.length + 1, // عدد الأنواع + تبويب "الكل"
      vsync: this,
    );

    // الاستماع لتغييرات التبويب
    _tabController.addListener(() {
      // إذا تم تحديد تبويب "الكل"
      if (_tabController.index == 0) {
        _searchController.selectAllTypes();
      }
      // إذا تم تحديد تبويب نوع محدد
      else {
        final selectedType = SearchResultType.values[_tabController.index - 1];
        _searchController.clearAllTypes();
        _searchController.toggleSearchType(selectedType);
      }
    });

    // تعيين نص البحث الحالي
    _textController.text = _searchController.searchQuery.value;

    // التركيز على حقل البحث
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث الشامل'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            const Tab(text: 'الكل'),
            ...SearchResultType.values
                .map((type) => Tab(text: _getTypeLabel(type))),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SearchBarWidget(
              controller: _textController,
              hintText: 'ابحث في النظام...',
              onChanged: (value) {
                // تنفيذ البحث بعد توقف المستخدم عن الكتابة
                _searchController.search(value);
              },
              onSubmitted: (value) {
                // تنفيذ البحث عند الضغط على Enter
                _searchController.search(value);
              },
              focusNode: _focusNode,
              enableClear: true,
              height: 50,
              backgroundColor: Colors.grey.shade100,
              borderRadius: 8.0,
            ),
          ),

          // عرض سجل البحث إذا كان حقل البحث فارغًا
          Obx(() {
            if (_searchController.searchQuery.value.isEmpty &&
                _searchController.searchHistory.isNotEmpty) {
              return _buildSearchHistory();
            }
            return const SizedBox.shrink();
          }),

          // عرض نتائج البحث
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب "الكل"
                _buildAllResultsTab(),

                // تبويبات الأنواع
                ...SearchResultType.values
                    .map((type) => _buildTypeResultsTab(type)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب "الكل"
  Widget _buildAllResultsTab() {
    return Obx(() {
      if (_searchController.isSearching.value) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search, size: 48, color: AppColors.primary),
              SizedBox(height: 16),
              Text(
                'جاري البحث...',
                style: TextStyle(fontSize: 16, color: AppColors.primary),
              ),
            ],
          ),
        );
      }

      if (_searchController.searchResults.isEmpty) {
        if (_searchController.searchQuery.value.isEmpty) {
          return const Center(
            child: Text('ابدأ البحث للعثور على ما تريد'),
          );
        }

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.search_off, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                'لا توجد نتائج لـ "${_searchController.searchQuery.value}"',
                style: AppStyles.titleMedium,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: _searchController.searchResults.length,
        itemBuilder: (context, index) {
          final result = _searchController.searchResults[index];
          return _buildSearchResultItem(result);
        },
      );
    });
  }

  /// بناء تبويب نوع محدد
  Widget _buildTypeResultsTab(SearchResultType type) {
    return Obx(() {
      if (_searchController.isSearching.value) {
        return const Center(child: CircularProgressIndicator());
      }

      // تصفية النتائج حسب النوع
      final results = _searchController.searchResults
          .where((result) => result.type == type)
          .toList();

      if (results.isEmpty) {
        if (_searchController.searchQuery.value.isEmpty) {
          return const Center(
            child: Text('ابدأ البحث للعثور على ما تريد'),
          );
        }

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.search_off, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                'لا توجد نتائج من نوع ${_getTypeLabel(type)} لـ "${_searchController.searchQuery.value}"',
                style: AppStyles.titleMedium,
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: results.length,
        itemBuilder: (context, index) {
          final result = results[index];
          return _buildSearchResultItem(result);
        },
      );
    });
  }

  /// بناء عنصر نتيجة بحث
  Widget _buildSearchResultItem(SearchResult result) {
    return ListTile(
      leading: _getIconForType(result.type),
      title: result.type == SearchResultType.task && result.data != null
          ? Row(
              children: [
                // رقم المهمة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.blue.shade200, width: 1),
                  ),
                  child: Text(
                    '#${result.data.id}',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // عنوان المهمة
                Expanded(
                  child: Text(
                    result.title,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            )
          : Text(result.title),
      subtitle: Text(
        result.subtitle,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: result.date != null
          ? Text(
              _formatDate(result.date!),
              style: AppStyles.labelSmall.copyWith(color: Colors.grey),
            )
          : null,
      onTap: () => _navigateToResult(result),
    );
  }

  /// بناء سجل البحث
  Widget _buildSearchHistory() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'سجل البحث',
                style: AppStyles.titleSmall,
              ),
              if (_permissionService.canManageSearchHistory())
                TextButton(
                  onPressed: () => _searchController.clearSearchHistory(),
                  child: const Text('مسح'),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: _searchController.searchHistory.map((query) {
              return Chip(
                label: Text(query),
                onDeleted: () {
                  _searchController.searchHistory.remove(query);
                  _searchController.saveSearchHistory();
                },
                deleteIcon: const Icon(Icons.close, size: 16),
                backgroundColor: Colors.grey.shade200,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة لنوع النتيجة
  Widget _getIconForType(SearchResultType type) {
    switch (type) {
      case SearchResultType.task:
        return const CircleAvatar(
          backgroundColor: Colors.blue,
          child: Icon(Icons.task_alt, color: Colors.white),
        );
      case SearchResultType.message:
        return const CircleAvatar(
          backgroundColor: Colors.green,
          child: Icon(Icons.message, color: Colors.white),
        );
      case SearchResultType.user:
        return const CircleAvatar(
          backgroundColor: Colors.orange,
          child: Icon(Icons.person, color: Colors.white),
        );
      case SearchResultType.document:
        return const CircleAvatar(
          backgroundColor: Colors.purple,
          child: Icon(Icons.description, color: Colors.white),
        );
      case SearchResultType.event:
        return const CircleAvatar(
          backgroundColor: Colors.red,
          child: Icon(Icons.event, color: Colors.white),
        );
      case SearchResultType.report:
        return const CircleAvatar(
          backgroundColor: Colors.teal,
          child: Icon(Icons.bar_chart, color: Colors.white),
        );
      case SearchResultType.other:
        return const CircleAvatar(
          backgroundColor: Colors.grey,
          child: Icon(Icons.more_horiz, color: Colors.white),
        );
    }
  }

  /// الحصول على تسمية لنوع النتيجة
  String _getTypeLabel(SearchResultType type) {
    switch (type) {
      case SearchResultType.task:
        return 'المهام';
      case SearchResultType.message:
        return 'الرسائل';
      case SearchResultType.user:
        return 'المستخدمين';
      case SearchResultType.document:
        return 'الوثائق';
      case SearchResultType.event:
        return 'الأحداث';
      case SearchResultType.report:
        return 'التقارير';
      case SearchResultType.other:
        return 'أخرى';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.year}/${date.month}/${date.day}';
    }
  }

  /// الانتقال إلى نتيجة البحث
  void _navigateToResult(SearchResult result) {
    switch (result.type) {
      case SearchResultType.task:
        // 🔒 فحص الصلاحيات قبل التنقل - إصلاح ثغرة أمنية
       
        if (!_permissionService.canViewTaskDetails()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية لعرض تفاصيل المهام',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
            duration: const Duration(seconds: 3),
          );
          return;
        }

        // الانتقال إلى صفحة المهمة
        Get.toNamed(AppRoutes.taskDetail,
            arguments: {'taskId': result.data.id});
        break;
      case SearchResultType.message:
        // الانتقال إلى صفحة المحادثة
        Get.toNamed(AppRoutes.unifiedChatDetail, arguments: {
          'chatGroup': {'id': result.data.groupId},
          'messageId': result.id,
        });
        break;
      case SearchResultType.user:
        // الانتقال إلى صفحة المستخدم
        Get.toNamed(AppRoutes.userDashboard, arguments: result.data);
        break;
      case SearchResultType.document:
        // الانتقال إلى صفحة الوثيقة
        Get.toNamed(AppRoutes.documentBrowser);
        break;
      case SearchResultType.event:
        // الانتقال إلى صفحة الحدث
        Get.toNamed(AppRoutes.calendarEventDetails, arguments: {
          'event': result.data,
          'onEventUpdated': null,
          'onEventDeleted': null,
        });
        break;
      case SearchResultType.report:
        // الانتقال إلى صفحة التقرير
        Get.toNamed(AppRoutes.reportDetails,
            arguments: {'reportId': result.data.id});
        break;
      case SearchResultType.other:
        // لا يوجد إجراء محدد
        break;
    }
  }
}
