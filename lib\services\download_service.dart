import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;

import '../utils/app_config.dart';


/// خدمة التنزيل المحسنة
class DownloadService {
  static final DownloadService _instance = DownloadService._internal();
  factory DownloadService() => _instance;
  DownloadService._internal();

  /// تنزيل ملف من URL
  Future<bool> downloadFile({
    required String url,
    required String fileName,
    String? customPath,
    Function(int received, int total)? onProgress,
  }) async {
    try {
      // طلب الصلاحيات
      if (!await _requestPermissions()) {
        debugPrint('لم يتم منح صلاحيات التخزين');
        return false;
      }

      // تحديد مسار التنزيل
      final downloadPath = customPath ?? await _getDownloadPath();
      final fullFilePath = '$downloadPath/$fileName';

      // إنشاء المجلد إذا لم يكن موجوداً
      final directory = Directory(downloadPath);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // تنزيل الملف
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final file = File(fullFilePath);
        await file.writeAsBytes(response.bodyBytes);

        debugPrint('تم تنزيل الملف: $fullFilePath');
        return true;
      } else {
        debugPrint('فشل في تنزيل الملف: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في تنزيل الملف: $e');
      return false;
    }
  }

  /// تنزيل مرفق
  Future<bool> downloadAttachment(
    Attachment attachment, {
    Function(int received, int total)? onProgress,
    bool openAfterDownload = false,
  }) async {
    try {
      final url = '${AppConfig.apiUrl}/api/Attachments/${attachment.id}/download';
      final success = await downloadFile(
        url: url,
        fileName: attachment.fileName,
        onProgress: onProgress,
      );

      if (success && openAfterDownload) {
        // فتح الملف بعد التنزيل
        final downloadPath = await _getDownloadPath();
        final filePath = '$downloadPath/${attachment.fileName}';
        await openFile(filePath);
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في تنزيل المرفق: $e');
      return false;
    }
  }

  /// فتح ملف باستخدام التطبيق الافتراضي
  Future<Map<String, dynamic>> openFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('الملف غير موجود: $filePath');
        return {
          'success': false,
          'message': 'الملف غير موجود: $filePath'
        };
      }

      final result = await OpenFile.open(filePath);
      if (result.type == ResultType.done) {
        debugPrint('تم فتح الملف بنجاح: $filePath');
        return {
          'success': true,
          'message': 'تم فتح الملف بنجاح'
        };
      } else {
        debugPrint('فشل في فتح الملف: ${result.message}');
        return {
          'success': false,
          'message': result.message
        };
      }
    } catch (e) {
      debugPrint('خطأ في فتح الملف: $e');
      return {
        'success': false,
        'message': 'خطأ في فتح الملف: $e'
      };
    }
  }

  /// تحديد نوع العارض المناسب للملف
  String getViewerType(String filePath) {
    final String extension = path.extension(filePath).toLowerCase();

    // ملفات PDF
    if (extension == '.pdf') {
      return 'pdf';
    }

    // ملفات الصور
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico', '.heic'].contains(extension)) {
      return 'image';
    }

    // ملفات أخرى
    return 'other';
  }

  /// الحصول على مسار التنزيل
  Future<String> _getDownloadPath() async {
    try {
      if (Platform.isAndroid) {
        // للأندرويد، استخدم مجلد التنزيلات
        final directory = await getExternalStorageDirectory();
        return directory != null ? '${directory.path}/Downloads' : '/storage/emulated/0/Download';
      } else if (Platform.isIOS) {
        // لـ iOS، استخدم مجلد المستندات
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      } else {
        // للمنصات الأخرى، استخدم مجلد المستندات
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار التنزيل: $e');
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
  }

  /// طلب الصلاحيات المطلوبة
  Future<bool> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        // للأندرويد، نحتاج صلاحية التخزين
        // تم تعطيل طلب الصلاحيات مؤقتاً حتى يتم إضافة حزمة permission_handler
        // final status = await Permission.storage.request();
        // return status.isGranted;
        return true; // مؤقت
      } else {
        // للمنصات الأخرى، لا نحتاج صلاحيات خاصة
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في طلب الصلاحيات: $e');
      return false;
    }
  }

  /// حفظ البيانات كملف
  Future<bool> saveDataAsFile({
    required Uint8List data,
    required String fileName,
    String? customPath,
  }) async {
    try {
      // طلب الصلاحيات
      if (!await _requestPermissions()) {
        debugPrint('لم يتم منح صلاحيات التخزين');
        return false;
      }

      // تحديد مسار الحفظ
      final savePath = customPath ?? await _getDownloadPath();
      final filePath = '$savePath/$fileName';

      // إنشاء المجلد إذا لم يكن موجوداً
      final directory = Directory(savePath);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // حفظ الملف
      final file = File(filePath);
      await file.writeAsBytes(data);
      
      debugPrint('تم حفظ الملف: $filePath');
      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ الملف: $e');
      return false;
    }
  }

  /// التحقق من وجود ملف
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الملف: $e');
      return false;
    }
  }

  /// حذف ملف
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('تم حذف الملف: $filePath');
        return true;
      } else {
        debugPrint('الملف غير موجود: $filePath');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// الحصول على حجم الملف
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      } else {
        return 0;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على حجم الملف: $e');
      return 0;
    }
  }

  /// الحصول على قائمة الملفات في مجلد
  Future<List<String>> getFilesInDirectory(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (await directory.exists()) {
        final files = await directory.list().toList();
        return files
            .whereType<File>()
            .map((entity) => entity.path)
            .toList();
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة الملفات: $e');
      return [];
    }
  }

  /// مشاركة ملف
  Future<bool> shareFile(String filePath) async {
    try {
      // هذه الوظيفة تحتاج إلى مكتبة share_plus
      // يمكن تنفيذها لاحقاً
      debugPrint('مشاركة الملف: $filePath');
      return true;
    } catch (e) {
      debugPrint('خطأ في مشاركة الملف: $e');
      return false;
    }
  }
}
