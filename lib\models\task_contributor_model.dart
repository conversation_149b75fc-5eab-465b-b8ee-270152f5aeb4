/// نموذج المساهم في المهمة
/// يمثل مستخدم لديه صلاحية وصول للمهمة ويعتبر مساهم محتمل
class TaskContributor {
  final int userId;
  final String userName;
  final String userEmail;
  final String userRole;
  final double contributionPercentage;
  final int totalUpdates;
  final int totalComments;
  final bool isActiveContributor;

  const TaskContributor({
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.userRole,
    required this.contributionPercentage,
    required this.totalUpdates,
    required this.totalComments,
    required this.isActiveContributor,
  });

  /// إنشاء من JSON
  factory TaskContributor.fromJson(Map<String, dynamic> json) {
    return TaskContributor(
      userId: json['userId'] as int,
      userName: json['userName'] as String? ?? '',
      userEmail: json['userEmail'] as String? ?? '',
      userRole: json['userRole'] as String? ?? '',
      contributionPercentage: (json['contributionPercentage'] as num?)?.toDouble() ?? 0.0,
      totalUpdates: json['totalUpdates'] as int? ?? 0,
      totalComments: json['totalComments'] as int? ?? 0,
      isActiveContributor: json['isActiveContributor'] as bool? ?? false,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'userRole': userRole,
      'contributionPercentage': contributionPercentage,
      'totalUpdates': totalUpdates,
      'totalComments': totalComments,
      'isActiveContributor': isActiveContributor,
    };
  }

  /// نسخ مع تعديل
  TaskContributor copyWith({
    int? userId,
    String? userName,
    String? userEmail,
    String? userRole,
    double? contributionPercentage,
    int? totalUpdates,
    int? totalComments,
    bool? isActiveContributor,
  }) {
    return TaskContributor(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userRole: userRole ?? this.userRole,
      contributionPercentage: contributionPercentage ?? this.contributionPercentage,
      totalUpdates: totalUpdates ?? this.totalUpdates,
      totalComments: totalComments ?? this.totalComments,
      isActiveContributor: isActiveContributor ?? this.isActiveContributor,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskContributor && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'TaskContributor(userId: $userId, userName: $userName, contributionPercentage: $contributionPercentage, isActiveContributor: $isActiveContributor)';
  }

  /// الحصول على وصف المساهمة
  String get contributionDescription {
    if (!isActiveContributor) {
      return 'مساهم محتمل (لم يساهم بعد)';
    }
    
    if (contributionPercentage > 0) {
      return 'مساهم نشط (${contributionPercentage.toStringAsFixed(1)}%)';
    }
    
    return 'مساهم نشط';
  }

  /// الحصول على لون المساهمة
  String get contributionColor {
    if (!isActiveContributor) return '#9E9E9E'; // رمادي
    if (contributionPercentage >= 50) return '#4CAF50'; // أخضر
    if (contributionPercentage >= 25) return '#FF9800'; // برتقالي
    if (contributionPercentage > 0) return '#2196F3'; // أزرق
    return '#9E9E9E'; // رمادي
  }

  /// الحصول على أيقونة المساهمة
  String get contributionIcon {
    if (!isActiveContributor) return 'person_outline';
    if (contributionPercentage >= 50) return 'star';
    if (contributionPercentage >= 25) return 'trending_up';
    if (contributionPercentage > 0) return 'check_circle_outline';
    return 'person';
  }
}
