// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contribution_report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContributionReport _$ContributionReportFromJson(Map<String, dynamic> json) =>
    ContributionReport(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      taskId: json['taskId'] as String?,
      userId: json['userId'] as String?,
      departmentId: json['departmentId'] as String?,
      startDate: json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      periodDays: (json['periodDays'] as num?)?.toInt(),
      createdById: json['createdById'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
      reportData: json['reportData'] as Map<String, dynamic>?,
      settings: json['settings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ContributionReportToJson(ContributionReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'taskId': instance.taskId,
      'userId': instance.userId,
      'departmentId': instance.departmentId,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'periodDays': instance.periodDays,
      'createdById': instance.createdById,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
      'reportData': instance.reportData,
      'settings': instance.settings,
    };

ContributionData _$ContributionDataFromJson(Map<String, dynamic> json) =>
    ContributionData(
      id: json['id'] as String,
      type: json['type'] as String,
      description: json['description'] as String,
      userId: json['userId'] as String,
      taskId: json['taskId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      value: (json['value'] as num).toDouble(),
      unit: json['unit'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ContributionDataToJson(ContributionData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'description': instance.description,
      'userId': instance.userId,
      'taskId': instance.taskId,
      'timestamp': instance.timestamp.toIso8601String(),
      'value': instance.value,
      'unit': instance.unit,
      'metadata': instance.metadata,
    };

ContributionStatistics _$ContributionStatisticsFromJson(
        Map<String, dynamic> json) =>
    ContributionStatistics(
      totalContributions: (json['totalContributions'] as num).toInt(),
      totalValue: (json['totalValue'] as num).toDouble(),
      averageValue: (json['averageValue'] as num).toDouble(),
      maxValue: (json['maxValue'] as num).toDouble(),
      minValue: (json['minValue'] as num).toDouble(),
      contributorsCount: (json['contributorsCount'] as num).toInt(),
      contributionsByType:
          Map<String, int>.from(json['contributionsByType'] as Map),
      contributionsByDate:
          (json['contributionsByDate'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$ContributionStatisticsToJson(
        ContributionStatistics instance) =>
    <String, dynamic>{
      'totalContributions': instance.totalContributions,
      'totalValue': instance.totalValue,
      'averageValue': instance.averageValue,
      'maxValue': instance.maxValue,
      'minValue': instance.minValue,
      'contributorsCount': instance.contributorsCount,
      'contributionsByType': instance.contributionsByType,
      'contributionsByDate': instance.contributionsByDate,
    };
