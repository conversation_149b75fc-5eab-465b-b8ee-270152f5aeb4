import 'user_model.dart';

/// نموذج أولوية المهمة - متطابق مع ASP.NET Core API
class TaskPriority {
  final int id;
  final String name;
  final String? description;
  final int level;
  final String? color;
  final String? icon;
  final bool isDefault;
  final int createdAt;
  final bool isActive;
  final int? updatedAt;
  final int? createdBy;

  // Navigation properties
  final User? createdByUser;

  const TaskPriority({
    required this.id,
    required this.name,
    this.description,
    required this.level,
    this.color,
    this.icon,
    this.isDefault = false,
    required this.createdAt,
    this.isActive = true,
    this.updatedAt,
    this.createdBy,
    this.createdByUser,
  });

  /// إنشاء TaskPriority من JSON (من API)
  factory TaskPriority.fromJson(Map<String, dynamic> json) {
    return TaskPriority(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      level: json['level'] as int,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      updatedAt: json['updatedAt'] as int?,
      createdBy: json['createdBy'] as int?,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل TaskPriority إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'level': level,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
      'createdAt': createdAt,
      'isActive': isActive,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
    };
  }

  /// إنشاء نسخة معدلة من TaskPriority
  TaskPriority copyWith({
    int? id,
    String? name,
    String? description,
    int? level,
    String? color,
    String? icon,
    bool? isDefault,
    int? createdAt,
    bool? isActive,
    int? updatedAt,
    int? createdBy,
    User? createdByUser,
  }) {
    return TaskPriority(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      level: level ?? this.level,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      createdByUser: createdByUser ?? this.createdByUser,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskPriority && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskPriority(id: $id, name: $name, level: $level, isActive: $isActive)';
  }
}

/// نموذج طلب إنشاء أولوية مهمة
class CreateTaskPriorityRequest {
  final String name;
  final String? description;
  final int level;
  final String? color;
  final String? icon;
  final bool isDefault;

  const CreateTaskPriorityRequest({
    required this.name,
    this.description,
    required this.level,
    this.color,
    this.icon,
    this.isDefault = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'level': level,
      'color': color,
      'icon': icon,
      'isDefault': isDefault,
    };
  }
}

/// نموذج طلب تحديث أولوية مهمة
class UpdateTaskPriorityRequest {
  final String? name;
  final String? description;
  final int? level;
  final String? color;
  final String? icon;
  final bool? isDefault;
  final bool? isActive;

  const UpdateTaskPriorityRequest({
    this.name,
    this.description,
    this.level,
    this.color,
    this.icon,
    this.isDefault,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (level != null) data['level'] = level;
    if (color != null) data['color'] = color;
    if (icon != null) data['icon'] = icon;
    if (isDefault != null) data['isDefault'] = isDefault;
    if (isActive != null) data['isActive'] = isActive;
    return data;
  }
}
