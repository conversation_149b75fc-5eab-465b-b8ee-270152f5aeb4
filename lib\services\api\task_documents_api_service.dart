import 'package:flutter/foundation.dart';
import 'dart:io';
import 'dart:convert';
import '../../models/task_document_model.dart';
import '../../models/archive_models.dart';
import 'api_service.dart';
import 'archive_documents_api_service.dart';

/// خدمة API لإدارة المستندات المرتبطة بالمهام باستخدام نظام الأرشفة
/// توفر تكامل كامل بين نظام المهام ونظام الأرشفة الموجود
class TaskDocumentsApiService {
  final ApiService _apiService = ApiService();
  final ArchiveDocumentsApiService _archiveService = ArchiveDocumentsApiService();

  /// الحصول على جميع مستندات مهمة محددة من TaskDocuments API
  Future<List<TaskDocument>> getTaskDocuments(
    int taskId, {
    TaskDocumentFilter? filter,
  }) async {
    try {
      // استخدام TaskDocuments API الجديد
      final queryParams = <String, dynamic>{
        'taskId': taskId.toString(),
      };

      // إضافة المرشحات إذا وجدت
      if (filter != null) {
        if (filter.type != null) queryParams['type'] = filter.type!.value;
        if (filter.isShared != null) queryParams['isShared'] = filter.isShared.toString();
        if (filter.permission != null) queryParams['permission'] = filter.permission!.value;
        if (filter.searchQuery != null) queryParams['search'] = filter.searchQuery;
        if (filter.fromDate != null) queryParams['fromDate'] = filter.fromDate.toString();
        if (filter.toDate != null) queryParams['toDate'] = filter.toDate.toString();
      }

      final response = await _apiService.get(
        '/api/TaskDocuments',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => TaskDocument.fromJson(json)).toList();
      } else {
        throw Exception('فشل في تحميل مستندات المهمة: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
      // العودة للطريقة القديمة كـ fallback
      return await _getTaskDocumentsFromArchive(taskId, filter);
    }
  }

  /// طريقة احتياطية للحصول على المستندات من نظام الأرشفة مباشرة
  Future<List<TaskDocument>> _getTaskDocumentsFromArchive(
    int taskId,
    TaskDocumentFilter? filter,
  ) async {
    try {
      // البحث في مستندات الأرشيف باستخدام metadata
      final archiveDocuments = await _archiveService.getAllDocuments();

      // تصفية المستندات المرتبطة بالمهمة
      final taskDocuments = <TaskDocument>[];

      for (final archiveDoc in archiveDocuments) {
        final metadata = _parseMetadata(archiveDoc.metadata);
        if (metadata['taskId'] == taskId.toString()) {
          final taskDoc = _convertToTaskDocument(archiveDoc, taskId);
          if (taskDoc != null) {
            taskDocuments.add(taskDoc);
          }
        }
      }

      // تطبيق المرشحات إذا وجدت
      if (filter != null) {
        return _applyFilters(taskDocuments, filter);
      }

      return taskDocuments;
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة من الأرشيف $taskId: $e');
      return [];
    }
  }

  /// الحصول على مستند مرتبط بمهمة بواسطة المعرف
  Future<TaskDocument?> getTaskDocumentById(int taskId, int archiveDocumentId) async {
    try {
      // استخدام TaskDocuments API الجديد
      final response = await _apiService.get('/api/TaskDocuments/$taskId/$archiveDocumentId');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return TaskDocument.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('فشل في تحميل المستند: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل مستند المهمة $taskId-$archiveDocumentId: $e');
      // العودة للطريقة القديمة كـ fallback
      return await _getTaskDocumentByIdFromArchive(taskId, archiveDocumentId);
    }
  }

  /// طريقة احتياطية للحصول على المستند من نظام الأرشفة مباشرة
  Future<TaskDocument?> _getTaskDocumentByIdFromArchive(int taskId, int archiveDocumentId) async {
    try {
      // البحث في مستندات الأرشيف
      final archiveDoc = await _archiveService.getDocumentById(archiveDocumentId);

      if (archiveDoc != null) {
        final metadata = _parseMetadata(archiveDoc.metadata);
        if (metadata['taskId'] == taskId.toString()) {
          return _convertToTaskDocument(archiveDoc, taskId);
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل مستند المهمة من الأرشيف $taskId-$archiveDocumentId: $e');
      return null;
    }
  }

  /// إنشاء مستند جديد مرتبط بمهمة باستخدام TaskDocuments API
  Future<TaskDocument?> createTaskDocument(
    CreateTaskDocumentRequest request,
  ) async {
    try {
      debugPrint('🚀 بدء إنشاء مستند المهمة');

      // التحقق من صحة البيانات قبل الإرسال
      if (request.title.trim().isEmpty) {
        throw Exception('عنوان المستند مطلوب');
      }

      if (request.taskId <= 0) {
        throw Exception('معرف المهمة غير صالح');
      }

      if (request.createdBy <= 0) {
        throw Exception('معرف المنشئ غير صالح');
      }

      final requestData = request.toJson();
      debugPrint('📋 بيانات الطلب: ${json.encode(requestData)}');

      // استخدام TaskDocuments API الجديد
      final response = await _apiService.post(
        '/api/TaskDocuments',
        requestData,
      );

      debugPrint('📡 استجابة الخادم: Status=${response.statusCode}');
      debugPrint('📄 محتوى الاستجابة: ${response.body}');

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('✅ تم إنشاء المستند بنجاح');
        return TaskDocument.fromJson(data);
      } else if (response.statusCode == 400) {
        debugPrint('❌ خطأ في البيانات المرسلة: ${response.statusCode}');
        debugPrint('📄 تفاصيل الخطأ: ${response.body}');

        // محاولة تحليل رسالة الخطأ
        try {
          final errorData = json.decode(response.body);
          final errorMessage = errorData['message'] ?? 'خطأ في البيانات المرسلة';
          throw Exception(errorMessage);
        } catch (_) {
          throw Exception('خطأ في البيانات المرسلة: ${response.body}');
        }
      } else {
        debugPrint('❌ فشل في إنشاء المستند: ${response.statusCode}');
        debugPrint('📄 تفاصيل الخطأ: ${response.body}');
        throw Exception('فشل في إنشاء المستند: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('💥 خطأ في إنشاء مستند المهمة: $e');
      debugPrint('🔄 محاولة استخدام الطريقة الاحتياطية');
      // العودة للطريقة القديمة كـ fallback
      return await _createTaskDocumentInArchive(request);
    }
  }

  /// طريقة احتياطية لإنشاء المستند في نظام الأرشفة مباشرة
  Future<TaskDocument?> _createTaskDocumentInArchive(
    CreateTaskDocumentRequest request,
  ) async {
    try {
      debugPrint('🔄 استخدام الطريقة الاحتياطية لإنشاء المستند في الأرشيف');

      // التحقق من صحة البيانات
      if (request.title.trim().isEmpty) {
        throw Exception('عنوان المستند مطلوب');
      }

      // إنشاء metadata للربط مع المهمة
      final metadata = _createTaskMetadata(
        taskId: request.taskId,
        type: request.type,
        description: request.description,
        isShared: request.isShared,
        permission: request.permission,
      );

      // إنشاء اسم ملف أفضل
      final fileName = _generateFileName(request.title, request.type.value);
      final fileType = _getFileTypeByDocumentType(request.type.value);
      final fileSize = _calculateFileSize(request.content);

      debugPrint('📋 تفاصيل المستند: fileName=$fileName, fileType=$fileType, fileSize=$fileSize');

      // إنشاء مستند في نظام الأرشفة
      final archiveDoc = ArchiveDocument(
        id: 0, // سيتم تعيينه من الخادم
        title: request.title.trim(),
        description: request.description?.trim(),
        fileName: fileName,
        filePath: '/documents/tasks/${request.taskId}/',
        fileType: fileType,
        fileSize: fileSize,
        categoryId: 1, // استخدام فئة افتراضية موجودة
        metadata: metadata,
        content: request.content ?? '',
        uploadedBy: request.createdBy,
        uploadedAt: DateTime.now().millisecondsSinceEpoch,
        createdBy: request.createdBy,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        isDeleted: false,
      );

      debugPrint('📤 إرسال مستند الأرشيف: ${archiveDoc.title}');
      final createdDoc = await _archiveService.createDocument(archiveDoc);

      if (createdDoc != null) {
        debugPrint('✅ تم إنشاء مستند الأرشيف بنجاح');
        return _convertToTaskDocument(createdDoc, request.taskId);
      }

      debugPrint('❌ فشل في إنشاء مستند الأرشيف');
      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء مستند المهمة في الأرشيف: $e');
      rethrow;
    }
  }

  /// إنشاء اسم ملف بناءً على العنوان والنوع
  String _generateFileName(String title, String type) {
    final cleanTitle = title.trim().replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanTitle}_${type}_$timestamp.json';
  }

  /// الحصول على نوع الملف بناءً على نوع المستند
  String _getFileTypeByDocumentType(String documentType) {
    switch (documentType.toLowerCase()) {
      case 'report':
      case 'analysis':
      case 'plan':
      case 'specification':
      case 'documentation':
        return 'application/json';
      case 'note':
        return 'text/plain';
      case 'attachment':
        return 'application/octet-stream';
      default:
        return 'application/json';
    }
  }

  /// حساب حجم الملف بناءً على المحتوى
  int _calculateFileSize(String? content) {
    if (content == null || content.isEmpty) {
      return 0;
    }
    // تقدير تقريبي لحجم الملف (UTF-8 encoding)
    return content.length * 2;
  }

  /// تحديث مستند مرتبط بمهمة
  Future<TaskDocument?> updateTaskDocument(
    UpdateTaskDocumentRequest request,
  ) async {
    try {
      debugPrint('🔄 إرسال طلب تحديث المستند:');
      debugPrint('  URL: /api/TaskDocuments/${request.taskId}/${request.archiveDocumentId}');

      final requestData = request.toJson();
      debugPrint('  البيانات المرسلة: ${json.encode(requestData)}');

      // استخدام TaskDocuments API الجديد
      final response = await _apiService.put(
        '/api/TaskDocuments/${request.taskId}/${request.archiveDocumentId}',
        requestData,
      );

      debugPrint('📡 استجابة API: ${response.statusCode}');
      if (response.statusCode != 200) {
        debugPrint('❌ محتوى الاستجابة: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('✅ تم تحديث المستند بنجاح');
        return TaskDocument.fromJson(data);
      } else {
        throw Exception('فشل في تحديث المستند: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث مستند المهمة ${request.archiveDocumentId}: $e');
      // العودة للطريقة القديمة كـ fallback
      return await _updateTaskDocumentInArchive(request);
    }
  }

  /// طريقة احتياطية لتحديث المستند في نظام الأرشفة مباشرة
  Future<TaskDocument?> _updateTaskDocumentInArchive(
    UpdateTaskDocumentRequest request,
  ) async {
    try {
      // تحديث المستند في نظام الأرشفة
      final archiveDoc = await _archiveService.getDocumentById(request.archiveDocumentId);

      if (archiveDoc != null) {
        // تحديث metadata
        final metadata = _parseMetadata(archiveDoc.metadata);
        metadata['type'] = request.type?.value ?? metadata['type'];
        metadata['description'] = request.description ?? metadata['description'];
        metadata['isShared'] = request.isShared ?? metadata['isShared'];
        metadata['permission'] = request.permission?.value ?? metadata['permission'];

        // إنشاء مستند محدث جديد بدلاً من استخدام copyWith
        final updatedArchiveDoc = ArchiveDocument(
          id: archiveDoc.id,
          title: request.title ?? archiveDoc.title,
          description: request.description ?? archiveDoc.description,
          fileName: archiveDoc.fileName,
          filePath: archiveDoc.filePath,
          fileType: archiveDoc.fileType,
          fileSize: archiveDoc.fileSize,
          categoryId: archiveDoc.categoryId,
          metadata: json.encode(metadata),
          content: request.content ?? archiveDoc.content,
          uploadedBy: archiveDoc.uploadedBy,
          uploadedAt: archiveDoc.uploadedAt,
          createdBy: archiveDoc.createdBy,
          createdAt: archiveDoc.createdAt,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          isDeleted: archiveDoc.isDeleted,
          category: archiveDoc.category,
          uploadedByUser: archiveDoc.uploadedByUser,
          tags: archiveDoc.tags,
        );

        final result = await _archiveService.updateDocument(updatedArchiveDoc);

        if (result != null) {
          return _convertToTaskDocument(result, request.taskId);
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في تحديث مستند المهمة في الأرشيف ${request.archiveDocumentId}: $e');
      rethrow;
    }
  }

  /// حذف مستند مرتبط بمهمة
  Future<bool> deleteTaskDocument(int taskId, int archiveDocumentId) async {
    try {
      final response = await _apiService.delete('/api/TaskDocuments/$taskId/$archiveDocumentId');
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في حذف مستند المهمة $taskId-$archiveDocumentId: $e');
      return false;
    }
  }

  /// مشاركة مستند مع مساهمين في المهمة
  Future<bool> shareDocumentWithContributors(
    int taskDocumentId,
    List<int> contributorIds,
  ) async {
    try {
      final response = await _apiService.post(
        '/api/TaskDocuments/$taskDocumentId/share',
        {
          'contributorIds': contributorIds,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في مشاركة المستند $taskDocumentId: $e');
      return false;
    }
  }

  /// إلغاء مشاركة مستند
  Future<bool> unshareDocument(int taskDocumentId) async {
    try {
      final response = await _apiService.post(
        '/api/TaskDocuments/$taskDocumentId/unshare',
        {},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إلغاء مشاركة المستند $taskDocumentId: $e');
      return false;
    }
  }

  /// رفع مستند من ملف
  Future<TaskDocument?> uploadTaskDocument({
    required int taskId,
    required File file,
    required String title,
    required TaskDocumentType type,
    String? description,
    bool isShared = false,
    required int createdBy,
  }) async {
    try {
      // رفع الملف إلى نظام الأرشفة
      final uploadedDoc = await _archiveService.uploadDocument(
        title: title,
        categoryId: 1, // فئة افتراضية للمستندات المرتبطة بالمهام
        file: file,
        fileName: file.path.split('/').last,
        fileType: 'application/octet-stream',
        description: description,
        createdBy: createdBy,
      );

      if (uploadedDoc == null) {
        throw Exception('فشل في رفع الملف');
      }

      // إنشاء metadata للربط مع المهمة
      final metadata = _createTaskMetadata(
        taskId: taskId,
        type: type,
        description: description,
        isShared: isShared,
      );

      // تحديث المستند بـ metadata المهمة
      final updatedDoc = ArchiveDocument(
        id: uploadedDoc.id,
        title: uploadedDoc.title,
        description: uploadedDoc.description,
        fileName: uploadedDoc.fileName,
        filePath: uploadedDoc.filePath,
        fileType: uploadedDoc.fileType,
        fileSize: uploadedDoc.fileSize,
        categoryId: uploadedDoc.categoryId,
        metadata: metadata,
        content: uploadedDoc.content,
        uploadedBy: uploadedDoc.uploadedBy,
        uploadedAt: uploadedDoc.uploadedAt,
        createdBy: uploadedDoc.createdBy,
        createdAt: uploadedDoc.createdAt,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        isDeleted: uploadedDoc.isDeleted,
        category: uploadedDoc.category,
        uploadedByUser: uploadedDoc.uploadedByUser,
        tags: uploadedDoc.tags,
      );

      final finalDoc = await _archiveService.updateDocument(updatedDoc);

      if (finalDoc != null) {
        return _convertToTaskDocument(finalDoc, taskId);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في رفع مستند المهمة: $e');
      rethrow;
    }
  }

  /// البحث في مستندات المهام
  Future<List<TaskDocument>> searchTaskDocuments(
    String query, {
    TaskDocumentFilter? filter,
  }) async {
    try {
      final queryParams = <String, String>{
        'search': query,
        ...?filter?.toQueryParams(),
      };

      final response = await _apiService.get(
        '/api/TaskDocuments/search',
        queryParams: queryParams,
      );

      return _apiService.handleListResponse<TaskDocument>(
        response,
        (json) => TaskDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن مستندات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات مستندات المهام
  Future<TaskDocumentStats?> getTaskDocumentStats(int taskId) async {
    try {
      final response = await _apiService.get('/api/TaskDocuments/stats/$taskId');
      return _apiService.handleResponse<TaskDocumentStats>(
        response,
        (json) => TaskDocumentStats.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات مستندات المهمة $taskId: $e');
      return null;
    }
  }

  /// الحصول على المستندات المشتركة للمهمة
  Future<List<TaskDocument>> getSharedTaskDocuments(int taskId) async {
    try {
      final filter = TaskDocumentFilter(
        taskId: taskId,
        isShared: true,
      );

      return await getTaskDocuments(taskId, filter: filter);
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة للمهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على المستندات الحديثة للمهمة
  Future<List<TaskDocument>> getRecentTaskDocuments(
    int taskId, {
    int limit = 10,
  }) async {
    try {
      final response = await _apiService.get(
        '/api/TaskDocuments/recent/$taskId',
        queryParams: {'limit': limit.toString()},
      );

      return _apiService.handleListResponse<TaskDocument>(
        response,
        (json) => TaskDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة للمهمة $taskId: $e');
      rethrow;
    }
  }

  /// تحديث صلاحيات مستند
  Future<bool> updateDocumentPermissions(
    int taskDocumentId,
    TaskDocumentPermission permission,
  ) async {
    try {
      final response = await _apiService.put(
        '/api/TaskDocuments/$taskDocumentId/permissions',
        {'permission': permission.value},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في تحديث صلاحيات المستند $taskDocumentId: $e');
      return false;
    }
  }

  /// تصدير مستندات المهمة
  Future<String?> exportTaskDocuments(
    int taskId, {
    String format = 'pdf',
  }) async {
    try {
      final response = await _apiService.get(
        '/api/TaskDocuments/export/$taskId',
        queryParams: {'format': format},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['filePath'] as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير مستندات المهمة $taskId: $e');
      return null;
    }
  }

  /// تحليل metadata من JSON string
  Map<String, dynamic> _parseMetadata(String? metadata) {
    if (metadata == null || metadata.isEmpty) {
      return {};
    }

    try {
      return json.decode(metadata) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('خطأ في تحليل metadata: $e');
      return {};
    }
  }

  /// تحويل ArchiveDocument إلى TaskDocument
  TaskDocument? _convertToTaskDocument(ArchiveDocument archiveDoc, int taskId) {
    try {
      final metadata = _parseMetadata(archiveDoc.metadata);

      return TaskDocument(
        taskId: taskId,
        archiveDocumentId: archiveDoc.id,
        type: TaskDocumentType.fromValue(metadata['type'] as String? ?? 'attachment'),
        description: metadata['description'] as String?,
        createdBy: archiveDoc.createdBy,
        createdAt: archiveDoc.createdAt,
        updatedAt: archiveDoc.updatedAt,
        isDeleted: archiveDoc.isDeleted,
        isShared: metadata['isShared'] as bool? ?? false,
        permission: TaskDocumentPermission.fromValue(metadata['permission'] as String? ?? 'read'),
        archiveDocument: archiveDoc,
      );
    } catch (e) {
      debugPrint('خطأ في تحويل ArchiveDocument إلى TaskDocument: $e');
      return null;
    }
  }

  /// تطبيق المرشحات على قائمة المستندات
  List<TaskDocument> _applyFilters(List<TaskDocument> documents, TaskDocumentFilter filter) {
    var filtered = documents;

    if (filter.type != null) {
      filtered = filtered.where((doc) => doc.type == filter.type).toList();
    }

    if (filter.isShared != null) {
      filtered = filtered.where((doc) => doc.isShared == filter.isShared).toList();
    }

    if (filter.permission != null) {
      filtered = filtered.where((doc) => doc.permission == filter.permission).toList();
    }

    if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
      final query = filter.searchQuery!.toLowerCase();
      filtered = filtered.where((doc) =>
          doc.archiveDocument?.title.toLowerCase().contains(query) == true ||
          doc.description?.toLowerCase().contains(query) == true
      ).toList();
    }

    return filtered;
  }

  /// إنشاء metadata للمستند المرتبط بالمهمة
  String _createTaskMetadata({
    required int taskId,
    required TaskDocumentType type,
    String? description,
    bool isShared = false,
    TaskDocumentPermission permission = TaskDocumentPermission.read,
  }) {
    final metadata = {
      'taskId': taskId.toString(),
      'type': type.value,
      'description': description,
      'isShared': isShared,
      'permission': permission.value,
      'createdForTask': true,
    };

    return json.encode(metadata);
  }
}
