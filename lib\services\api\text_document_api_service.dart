import 'package:flutter/foundation.dart';
import '../../models/text_document_model.dart';
import 'unified_document_api_service.dart';

/// خدمة API للمستندات النصية - تستخدم الخدمة الموحدة
class TextDocumentApiService {
  final UnifiedDocumentApiService _unifiedService = UnifiedDocumentApiService();

  /// الحصول على جميع المستندات
  Future<List<TextDocument>> getAllDocuments() async {
    try {
      return await _unifiedService.getAllTextDocuments();
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات: $e');
      rethrow;
    }
  }

  /// الحصول على مستند بواسطة المعرف
  Future<TextDocument?> getDocumentById(int id) async {
    try {
      return await _unifiedService.getTextDocumentById(id);
    } catch (e) {
      debugPrint('خطأ في تحميل المستند $id: $e');
      return null;
    }
  }

  /// الحصول على المستندات المرتبطة بمهمة
  Future<List<TextDocument>> getDocumentsByTaskId(int taskId) async {
    try {
      return await _unifiedService.getTextDocumentsByTaskId(taskId);
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
      return [];
    }
  }

  /// البحث في المستندات
  Future<List<TextDocument>> searchDocuments(String query) async {
    try {
      return await _unifiedService.searchTextDocuments(query);
    } catch (e) {
      debugPrint('خطأ في البحث عن المستندات: $e');
      return [];
    }
  }

  /// إنشاء مستند جديد
  Future<TextDocument?> createDocument(TextDocument document) async {
    try {
      return await _unifiedService.createTextDocument(document);
    } catch (e) {
      debugPrint('خطأ في إنشاء المستند: $e');
      rethrow;
    }
  }

  /// تحديث مستند
  Future<TextDocument?> updateDocument(TextDocument document) async {
    try {
      return await _unifiedService.updateTextDocument(document);
    } catch (e) {
      debugPrint('خطأ في تحديث المستند ${document.id}: $e');
      rethrow;
    }
  }

  /// حذف مستند
  Future<bool> deleteDocument(int id) async {
    try {
      return await _unifiedService.deleteTextDocument(id);
    } catch (e) {
      debugPrint('خطأ في حذف المستند $id: $e');
      return false;
    }
  }

  /// مشاركة مستند
  /// Note: ArchiveDocuments API doesn't have share functionality, so we'll update the document
  Future<bool> shareDocument(int id, bool isShared) async {
    try {
      // Get the document first
      final document = await getDocumentById(id);
      if (document == null) return false;

      // Update with new sharing status
      final updatedDocument = document.copyWith(isShared: isShared);
      final result = await updateDocument(updatedDocument);
      return result != null;
    } catch (e) {
      debugPrint('خطأ في مشاركة المستند $id: $e');
      return false;
    }
  }

  /// الحصول على المستندات المشتركة
  Future<List<TextDocument>> getSharedDocuments() async {
    try {
      return await _unifiedService.getSharedTextDocuments();
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة: $e');
      return [];
    }
  }

  /// الحصول على المستندات حسب النوع
  Future<List<TextDocument>> getDocumentsByType(String type) async {
    try {
      final documentType = TextDocumentType.fromValue(type);
      return await _unifiedService.getTextDocumentsByType(documentType);
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات النوع $type: $e');
      return [];
    }
  }

  /// الحصول على المستندات الحديثة
  Future<List<TextDocument>> getRecentDocuments({int limit = 10}) async {
    try {
      return await _unifiedService.getRecentTextDocuments(limit: limit);
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة: $e');
      return [];
    }
  }

  /// تصدير مستند
  Future<String?> exportDocument(int id, String format) async {
    try {
      // Get document content for export
      final document = await getDocumentById(id);
      if (document == null) return null;

      // Return document content (basic export)
      return document.getDocumentText();
    } catch (e) {
      debugPrint('خطأ في تصدير المستند $id: $e');
      return null;
    }
  }
}
