import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:flutter/services.dart' show rootBundle;

class ArabicTextTestReportScreen extends StatelessWidget {
  const ArabicTextTestReportScreen({super.key});

  Future<void> _generateAndPreviewPdf(BuildContext context) async {
    final pdf = pw.Document();
    // استخدم خط NotoSansArabic من ملف محلي أو خط افتراضي
    final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final arabicFont = pw.Font.ttf(fontData);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Text(
              'تجربة عرض النص العربي في تقرير PDF\nمرحبا بكم في نظام التقارير الاحترافية',
              style: pw.TextStyle(font: arabicFont, fontSize: 24),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          );
        },
      ),
    );

    final pdfData = await pdf.save();
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: 'تجربة_النص_العربي.pdf',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تجربة اللغة العربية في PDF'),
        backgroundColor: Colors.blue,
      ),
      body: Center(
        child: ElevatedButton.icon(
          icon: const Icon(Icons.picture_as_pdf),
          label: const Text('عرض تقرير تجربة اللغة العربية'),
          onPressed: () => _generateAndPreviewPdf(context),
        ),
      ),
    );
  }
}
