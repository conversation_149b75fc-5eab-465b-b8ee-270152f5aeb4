import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/department_model.dart';
import 'package:intl/intl.dart';
// استورد دالة التقرير والـ models حسب مشروعك
// import 'package:flutter_application_2/professional_reports/report_departments.dart';
// import 'package:flutter_application_2/models/department_model.dart';

class DepartmentsReportFilterWidget extends StatefulWidget {
  final List<Department> allDepartments;
  // أنواع المهام وحالاتها مخفية مؤقتًا
  // final List<String> allTaskTypes;
  // final List<String> allTaskStatuses;
  final Function({
    List<int>? departmentIds,
    DateTime? fromDate,
    DateTime? toDate,
    List<String>? taskTypes,
    List<String>? taskStatuses,
  }) onGenerateReport;

  const DepartmentsReportFilterWidget({
    super.key,
    required this.allDepartments,
    // required this.allTaskTypes,
    // required this.allTaskStatuses,
    required this.onGenerateReport,
  });

  @override
  State<DepartmentsReportFilterWidget> createState() => _DepartmentsReportFilterWidgetState();
}

class _DepartmentsReportFilterWidgetState extends State<DepartmentsReportFilterWidget> {
  List<int> selectedDepartmentIds = [];
  List<String> selectedTaskTypes = [];
  List<String> selectedTaskStatuses = [];
  // أمثلة لأنواع المهام وحالاتها (يمكنك تعديلها حسب مشروعك)
  final List<String> allTaskTypes = ['عادية', 'طارئة', 'مراجعة', 'اجتماع'];
  final List<String> allTaskStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'waiting_for_info'];
  DateTime? fromDate;
  DateTime? toDate;

  Future<void> pickDate(BuildContext context, bool isFrom) async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: isFrom ? (fromDate ?? now) : (toDate ?? now),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        if (isFrom) {
          fromDate = picked;
        } else {
          toDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
          Text('اختر الأقسام:', style: TextStyle(fontWeight: FontWeight.bold)),
          Wrap(
            children: widget.allDepartments.map((dept) => FilterChip(
              label: Text(dept.name),
              selected: selectedDepartmentIds.contains(dept.id),
              onSelected: (val) {
                setState(() {
                  if (val) {
                    selectedDepartmentIds.add(dept.id);
                  } else {
                    selectedDepartmentIds.remove(dept.id);
                  }
                });
              },
            )).toList(),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('من تاريخ الإنشاء:'),
                    TextButton(
                      onPressed: () => pickDate(context, true),
                      child: Text(fromDate != null ? DateFormat('yyyy/MM/dd').format(fromDate!) : 'اختر التاريخ'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('إلى تاريخ الإنشاء:'),
                    TextButton(
                      onPressed: () => pickDate(context, false),
                      child: Text(toDate != null ? DateFormat('yyyy/MM/dd').format(toDate!) : 'اختر التاريخ'),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text('نوع المهمة:', style: TextStyle(fontWeight: FontWeight.bold)),
          Wrap(
            children: allTaskTypes.map((type) => FilterChip(
              label: Text(type),
              selected: selectedTaskTypes.contains(type),
              onSelected: (val) {
                setState(() {
                  if (val) {
                    selectedTaskTypes.add(type);
                  } else {
                    selectedTaskTypes.remove(type);
                  }
                });
              },
            )).toList(),
          ),
          const SizedBox(height: 12),
          Text('حالة المهمة:', style: TextStyle(fontWeight: FontWeight.bold)),
          Wrap(
            children: allTaskStatuses.map((status) => FilterChip(
              label: Text(_translateStatus(status)),
              selected: selectedTaskStatuses.contains(status),
              onSelected: (val) {
                setState(() {
                  if (val) {
                    selectedTaskStatuses.add(status);
                  } else {
                    selectedTaskStatuses.remove(status);
                  }
                });
              },
            )).toList(),
          ),
          const SizedBox(height: 20),
          Center(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.picture_as_pdf,color: Colors.white),
              label: const Text('توليد التقرير'),
              onPressed: () {
                widget.onGenerateReport(
                  departmentIds: selectedDepartmentIds.isEmpty ? null : selectedDepartmentIds,
                  fromDate: fromDate,
                  toDate: toDate,
                  taskTypes: selectedTaskTypes.isEmpty ? null : selectedTaskTypes,
                  taskStatuses: selectedTaskStatuses.isEmpty ? null : selectedTaskStatuses,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // دالة ترجمة حالة المهمة للعربية
  String _translateStatus(String status) {
  
     switch (status) {
    case 'pending':
      return 'قيد الانتظار';
    case 'in_progress':
      return 'قيد التنفيذ';
    case 'completed':
      return 'منجزة';
    case 'cancelled':
      return 'ملغاة';
    case 'waitingForInfo':
      return 'في انتظار معلومات';
    case 'waiting_for_info':
     return 'في انتظار معلومات';
    case 'inProgress':
      return 'قيد التنفيذ';
    
    default:
      return status ?? '-';
    }
  }
}
