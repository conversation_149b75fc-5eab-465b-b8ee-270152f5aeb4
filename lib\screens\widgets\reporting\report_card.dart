import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/enhanced_report_model.dart';
import 'package:flutter_application_2/models/report_models.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_styles.dart';
import '../../../services/unified_permission_service.dart';

/// بطاقة تقرير
///
/// تعرض معلومات التقرير في بطاقة
class ReportCard extends StatelessWidget {
  /// التقرير
  final EnhancedReport report;

  /// حدث النقر
  final VoidCallback onTap;

  /// حدث التعديل
  final VoidCallback? onEdit;

  /// حدث الحذف
  final VoidCallback? onDelete;

  /// حدث المفضلة
  final VoidCallback? onFavorite;

  /// حدث التصدير
  final VoidCallback? onExport;

  /// حدث المشاركة
  final VoidCallback? onShare;

  const ReportCard({
    super.key,
    required this.report,
    required this.onTap,
    this.onEdit,
    this.onDelete,
    this.onFavorite,
    this.onExport,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: report.color ?? Colors.white,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة التقرير
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getReportTypeColor(report.type).withValues(alpha: 51), // 0.2 * 255 = 51
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        report.icon ?? _getReportTypeIcon(report.type),
                        color: _getReportTypeColor(report.type),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // عنوان التقرير
                  Expanded(
                    child: Text(
                      report.title,
                      style: AppStyles.titleMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // زر المفضلة
                  if (onFavorite != null)
                    IconButton(
                      icon: Icon(
                        report.isFavorite ? Icons.star : Icons.star_border,
                        color: report.isFavorite ? Colors.amber : Colors.grey,
                      ),
                      onPressed: onFavorite,
                      tooltip: report.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
                    ),
                ],
              ),
              const SizedBox(height: 12),
              // وصف التقرير
              if (report.description != null && report.description!.isNotEmpty)
                Expanded(
                  child: Text(
                    report.description!,
                    style: AppStyles.bodySmall,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              // معلومات التقرير
              const Spacer(),
              Row(
                children: [
                  // نوع التقرير
                  Chip(
                    label: Text(
                      _getReportTypeName(report.type),
                      style: AppStyles.bodySmall.copyWith(
                        color: _getReportTypeColor(report.type),
                      ),
                    ),
                    backgroundColor: _getReportTypeColor(report.type).withValues(alpha: 26), // 0.1 * 255 = 26
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  const Spacer(),
                  // تاريخ الإنشاء
                  Text(
                    DateFormat('yyyy-MM-dd').format(report.createdAt),
                    style: AppStyles.bodySmall.copyWith(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // زر التصدير
                  if (onExport != null && UnifiedPermissionService().canExportReport())
                    IconButton(
                      icon: const Icon(Icons.download, size: 20),
                      onPressed: onExport,
                      tooltip: 'تصدير',
                    ),
                  // زر المشاركة
                  if (onShare != null && UnifiedPermissionService().canShareReport())
                    IconButton(
                      icon: const Icon(Icons.share, size: 20),
                      onPressed: onShare,
                      tooltip: 'مشاركة',
                    ),
                  // زر التعديل
                  if (onEdit != null && UnifiedPermissionService().canEditReport())
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: onEdit,
                      tooltip: 'تعديل',
                    ),
                  // زر الحذف
                  if (onDelete != null && UnifiedPermissionService().canDeleteReport())
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: onDelete,
                      tooltip: 'حذف',
                      color: Colors.red,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'أداء القسم';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.taskCompletion:
        return 'إكمال المهام';
      case ReportType.userActivity:
        return 'نشاط المستخدم';
      case ReportType.departmentWorkload:
        return 'عبء العمل للقسم';
      case ReportType.projectStatus:
        return 'حالة المشروع';
      case ReportType.custom:
        return 'مخصص';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على أيقونة نوع التقرير
  IconData _getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Icons.assignment;
      case ReportType.userPerformance:
        return Icons.person;
      case ReportType.departmentPerformance:
        return Icons.business;
      case ReportType.timeTracking:
        return Icons.timer;
      case ReportType.taskProgress:
        return Icons.trending_up;
      case ReportType.taskDetails:
        return Icons.description;
      case ReportType.taskCompletion:
        return Icons.check_circle;
      case ReportType.userActivity:
        return Icons.person_search;
      case ReportType.departmentWorkload:
        return Icons.work;
      case ReportType.projectStatus:
        return Icons.dashboard;
      case ReportType.custom:
        return Icons.build;
      default:
        return Icons.report;
    }
  }

  /// الحصول على لون نوع التقرير
  Color _getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Colors.blue;
      case ReportType.userPerformance:
        return Colors.green;
      case ReportType.departmentPerformance:
        return Colors.purple;
      case ReportType.timeTracking:
        return Colors.orange;
      case ReportType.taskProgress:
        return Colors.teal;
      case ReportType.taskDetails:
        return Colors.indigo;
      case ReportType.taskCompletion:
        return Colors.green;
      case ReportType.userActivity:
        return Colors.amber;
      case ReportType.departmentWorkload:
        return Colors.deepPurple;
      case ReportType.projectStatus:
        return Colors.blue;
      case ReportType.custom:
        return Colors.grey;
      default:
        return Colors.blueGrey;
    }
  }
}
