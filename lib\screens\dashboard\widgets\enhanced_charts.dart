import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

/// مخططات محسنة بسيطة لحل مشاكل التقارير
class EnhancedPieChart extends StatelessWidget {
  final String title;
  final List<dynamic> data;

  const EnhancedPie<PERSON>hart({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return SfCircularChart(
      title: ChartTitle(text: title),
      legend: Legend(isVisible: true),
      series: <PieSeries<dynamic, String>>[
        PieSeries<dynamic, String>(
          dataSource: data,
          xValueMapper: (dynamic data, _) => data.toString(),
          yValueMapper: (dynamic data, _) => 1.0,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
        )
      ],
    );
  }
}

class EnhancedBarChart extends StatelessWidget {
  final String title;
  final List<dynamic> data;

  const EnhancedBar<PERSON>hart({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return SfCartesian<PERSON><PERSON>(
      title: ChartTitle(text: title),
      primaryXAxis: CategoryAxis(),
      primaryYAxis: NumericAxis(),
      series: <CartesianSeries<dynamic, String>>[
        ColumnSeries<dynamic, String>(
          dataSource: data,
          xValueMapper: (dynamic data, _) => data.toString(),
          yValueMapper: (dynamic data, _) => 1.0,
        )
      ],
    );
  }
}

class EnhancedLineChart extends StatelessWidget {
  final String title;
  final List<dynamic> data;

  const EnhancedLineChart({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return SfCartesianChart(
      title: ChartTitle(text: title),
      primaryXAxis: CategoryAxis(),
      primaryYAxis: NumericAxis(),
      series: <CartesianSeries<dynamic, String>>[
        LineSeries<dynamic, String>(
          dataSource: data,
          xValueMapper: (dynamic data, _) => data.toString(),
          yValueMapper: (dynamic data, _) => 1.0,
        )
      ],
    );
  }
}

class EnhancedGanttChart extends StatelessWidget {
  final String title;
  final List<dynamic> data;

  const EnhancedGanttChart({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          const Expanded(
            child: Center(
              child: Text('مخطط جانت - قيد التطوير'),
            ),
          ),
        ],
      ),
    );
  }
}

// إضافة GanttViewRange كـ enum بسيط
enum GanttViewRange {
  day,
  week,
  month,
  year,
}
