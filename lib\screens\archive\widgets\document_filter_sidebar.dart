import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/models/archive_models.dart';

/// شريط تصفية وثائق الأرشيف مبسط
class DocumentFilterSidebar extends StatefulWidget {
  /// قائمة التصنيفات المتاحة
  final List<ArchiveCategory> categories;
  
  /// قائمة الوسوم المتاحة
  final List<ArchiveTag> tags;
  
  /// دالة يتم استدعاؤها عند تطبيق التصفية
  final Function(Map<String, dynamic>) onFilterApplied;

  const DocumentFilterSidebar({
    super.key,
    required this.categories,
    required this.tags,
    required this.onFilterApplied,
  });

  @override
  State<DocumentFilterSidebar> createState() => _DocumentFilterSidebarState();
}

class _DocumentFilterSidebarState extends State<DocumentFilterSidebar> {
  ArchiveCategory? _selectedCategory;
  final List<ArchiveTag> _selectedTags = [];
  DateTime? _fromDate;
  DateTime? _toDate;
  String _sortBy = 'createdAt';
  bool _sortAscending = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عوامل التصفية',
                  style: AppStyles.subtitle1,
                ),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('إعادة تعيين'),
                ),
              ],
            ),
            const Divider(),

            // التصنيفات
            _buildSectionTitle('التصنيف'),
            _buildCategoryFilter(),
            const SizedBox(height: 16),

            // الوسوم
            _buildSectionTitle('الوسوم'),
            _buildTagsFilter(),
            const SizedBox(height: 16),

            // نطاق التاريخ
            _buildSectionTitle('نطاق التاريخ'),
            _buildDateRangeFilter(),
            const SizedBox(height: 16),

            // الترتيب
            _buildSectionTitle('ترتيب النتائج'),
            _buildSortingFilter(),
            
            const SizedBox(height: 16),
            
            // زر تطبيق التصفية
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تطبيق التصفية'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    if (widget.categories.isEmpty) {
      return const Text('لا توجد تصنيفات');
    }

    return DropdownButtonFormField<ArchiveCategory?>(
      value: _selectedCategory,
      decoration: const InputDecoration(
        hintText: 'اختر تصنيفًا',
        isDense: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(),
      ),
      items: [
        const DropdownMenuItem<ArchiveCategory?>(
          value: null,
          child: Text('جميع التصنيفات'),
        ),
        ...widget.categories.map((category) => DropdownMenuItem<ArchiveCategory?>(
          value: category,
          child: Text(category.name),
        )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedCategory = value;
        });
      },
    );
  }

  Widget _buildTagsFilter() {
    if (widget.tags.isEmpty) {
      return const Text('لا توجد وسوم');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.tags.map((tag) {
        final isSelected = _selectedTags.contains(tag);

        return FilterChip(
          label: Text(tag.name),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedTags.add(tag);
              } else {
                _selectedTags.remove(tag);
              }
            });
          },
          selectedColor: _getTagColor(tag.color).withAlpha(51),
          checkmarkColor: _getTagColor(tag.color),
        );
      }).toList(),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // من تاريخ
        InkWell(
          onTap: () => _selectDate(true),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'من تاريخ',
              isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: OutlineInputBorder(),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _fromDate != null
                      ? '${_fromDate!.year}/${_fromDate!.month}/${_fromDate!.day}'
                      : 'اختر تاريخ',
                  style: _fromDate != null
                      ? AppStyles.body2
                      : AppStyles.body2.copyWith(color: Colors.grey),
                ),
                const Icon(Icons.calendar_today, size: 16),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),

        // إلى تاريخ
        InkWell(
          onTap: () => _selectDate(false),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'إلى تاريخ',
              isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: OutlineInputBorder(),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _toDate != null
                      ? '${_toDate!.year}/${_toDate!.month}/${_toDate!.day}'
                      : 'اختر تاريخ',
                  style: _toDate != null
                      ? AppStyles.body2
                      : AppStyles.body2.copyWith(color: Colors.grey),
                ),
                const Icon(Icons.calendar_today, size: 16),
              ],
            ),
          ),
        ),

        if (_fromDate != null || _toDate != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: TextButton(
              onPressed: () {
                setState(() {
                  _fromDate = null;
                  _toDate = null;
                });
              },
              child: const Text('مسح التاريخ'),
            ),
          ),
      ],
    );
  }

  Widget _buildSortingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // حقل الترتيب
        DropdownButtonFormField<String>(
          value: _sortBy,
          decoration: const InputDecoration(
            labelText: 'ترتيب حسب',
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem<String>(
              value: 'createdAt',
              child: Text('تاريخ الإضافة'),
            ),
            DropdownMenuItem<String>(
              value: 'title',
              child: Text('العنوان'),
            ),
            DropdownMenuItem<String>(
              value: 'fileSize',
              child: Text('حجم الملف'),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _sortBy = value;
              });
            }
          },
        ),
        const SizedBox(height: 8),

        // اتجاه الترتيب
        Row(
          children: [
            Checkbox(
              value: _sortAscending,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortAscending = value;
                  });
                }
              },
            ),
            const Text('ترتيب تصاعدي'),
          ],
        ),
      ],
    );
  }

  Future<void> _selectDate(bool isFromDate) async {
    final initialDate = isFromDate
        ? _fromDate ?? DateTime.now()
        : _toDate ?? DateTime.now();

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = date;
        } else {
          _toDate = date;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedTags.clear();
      _fromDate = null;
      _toDate = null;
      _sortBy = 'createdAt';
      _sortAscending = false;
    });
  }

  void _applyFilters() {
    final filters = {
      'category': _selectedCategory,
      'tags': _selectedTags,
      'fromDate': _fromDate,
      'toDate': _toDate,
      'sortBy': _sortBy,
      'sortAscending': _sortAscending,
    };
    
    widget.onFilterApplied(filters);
  }

  Color _getTagColor(String? colorString) {
    try {
      if (colorString == null || colorString.isEmpty) {
        return Colors.blue;
      }
      return Color(int.parse(colorString.replaceAll('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }
}
