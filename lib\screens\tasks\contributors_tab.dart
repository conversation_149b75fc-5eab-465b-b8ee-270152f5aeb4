import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_contributor_model.dart';

/// تبويب المساهمين - بسيط وخالي من التعقيد
class ContributorsTab extends StatelessWidget {
  final String taskId;

  const ContributorsTab({
    super.key,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TaskController>(
      id: 'task_contributors', // إضافة معرف للتحديث المحدد
      builder: (controller) {
        final contributors = controller.taskContributors;

        if (contributors.isEmpty) {
          return _buildEmptyState();
        }

        return Column(
          children: [
            _buildHeader(contributors),
            _buildInstructions(),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: contributors.length,
                itemBuilder: (context, index) {
                  return _buildContributorCard(contributors[index]);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('لا يوجد مساهمون في هذه المهمة'),
        ],
      ),
    );
  }

  /// تعليمات الاستخدام
  Widget _buildInstructions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'انقر على أي مساهم لعرض تفاصيل مساهماته',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// رأس التبويب مع الإحصائيات
  Widget _buildHeader(List<TaskContributor> contributors) {
    final activeCount = contributors.where((c) => c.isActiveContributor).length;
    final totalContribution = contributors.fold<double>(
      0.0,
      (sum, c) => sum + c.contributionPercentage,
    );

    return GetBuilder<TaskController>(
      id: 'task_contributors',
      builder: (controller) {
        final task = controller.currentTask;
        final commentsCount = task?.comments.length ?? 0;
        final attachmentsCount = task?.attachments.length ?? 0;

        return Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Column(
            children: [
              // الصف الأول - إحصائيات المساهمين
              Row(
                children: [
                  _buildStatCard('إجمالي المساهمين', '${contributors.length}', Icons.people),
                  const SizedBox(width: 16),
                  _buildStatCard('المساهمون النشطون', '$activeCount', Icons.person_add),
                  const SizedBox(width: 16),
                  _buildStatCard('إجمالي المساهمة', '${totalContribution.toStringAsFixed(1)}%', Icons.pie_chart),
                ],
              ),
              const SizedBox(height: 16),
              // الصف الثاني - إحصائيات المهمة
              Row(
                children: [
                  _buildStatCard('التعليقات', '$commentsCount', Icons.comment),
                  const SizedBox(width: 16),
                  _buildStatCard('المرفقات', '$attachmentsCount', Icons.attach_file),
                  const SizedBox(width: 16),
                  _buildStatCard('التقدم', '${task?.completionPercentage ?? 0}%', Icons.trending_up),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Icon(icon, color: AppColors.primary, size: 24),
            const SizedBox(height: 8),
             Text(value, style: AppStyles.titleMedium),
            // Text('${contributor.totalComments}', style: AppStyles.titleMedium),
            Text(title, style: AppStyles.bodySmall, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }

  /// بطاقة المساهم
  Widget _buildContributorCard(TaskContributor contributor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _viewContributions(contributor),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getContributorColor(contributor).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // صورة المساهم
                CircleAvatar(
                  backgroundColor: _getContributorColor(contributor),
                  child: Text(
                    contributor.userName.isNotEmpty ? contributor.userName[0].toUpperCase() : '?',
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات المساهم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contributor.userName,
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        contributor.userEmail,
                        style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            contributor.isActiveContributor ? Icons.check_circle : Icons.circle_outlined,
                            size: 16,
                            color: contributor.isActiveContributor ? Colors.green : Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            contributor.isActiveContributor ? 'مساهم نشط' : 'مساهم محتمل',
                            style: TextStyle(
                              color: contributor.isActiveContributor ? Colors.green : Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // إحصائيات المساهم
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${contributor.contributionPercentage.toStringAsFixed(1)}%',
                      style: AppStyles.titleMedium.copyWith(
                        color: _getContributorColor(contributor),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${contributor.totalUpdates} تحديث',
                      style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${contributor.totalComments} تعليق',
                      style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),

                // سهم للإشارة إلى إمكانية النقر
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// لون المساهم حسب نسبة المساهمة
  Color _getContributorColor(TaskContributor contributor) {
    if (!contributor.isActiveContributor) return Colors.grey;
    if (contributor.contributionPercentage >= 50) return Colors.green;
    if (contributor.contributionPercentage >= 25) return Colors.orange;
    if (contributor.contributionPercentage > 0) return Colors.blue;
    return Colors.grey;
  }

  /// عرض مساهمات المساهم - انتقال مباشر
  void _viewContributions(TaskContributor contributor) {
    debugPrint('تم النقر على المساهم: ${contributor.userName}');

    // تحديث المساهم المحدد في الكونترولر
    final taskController = Get.find<TaskController>();
    taskController.selectedContributorId = contributor.userId.toString();
    debugPrint('تم تحديد المساهم: ${taskController.selectedContributorId}');

    // الانتقال المباشر إلى تبويب تفاصيل المساهمات
    try {
      final tabController = Get.find<TabController>(tag: 'task_detail_tabs');
      debugPrint('تم العثور على TabController، الانتقال إلى التبويب 11');
      tabController.animateTo(11); // تبويب تفاصيل المساهمات
    } catch (e) {
      debugPrint('خطأ في الانتقال إلى التبويب: $e');
      // محاولة بديلة
      Get.snackbar(
        'تم اختيار المساهم',
        'انتقل يدوياً إلى تبويب تفاصيل المساهمات لعرض مساهمات ${contributor.userName}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}