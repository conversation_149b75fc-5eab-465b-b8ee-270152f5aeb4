import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:convert';
import 'dart:async';

// استيراد المكونات الجديدة المحسنة
import 'quill_error_handler.dart';
import 'quill_toolbar_manager.dart';
import '../../../services/api/unified_document_api_service.dart';
import '../../../models/document_version_model.dart';

/// محرر Flutter Quill المتقدم المحسن - نسخة مبسطة ومحسنة
/// يوفر تحرير نصوص غني مثل Microsoft Word مع جميع الميزات المتقدمة
/// 
/// التحسينات الجديدة:
/// - تم تقسيم الكود إلى مكونات منفصلة
/// - تحسين معالجة الأخطاء
/// - تبسيط شريط الأدوات
/// - تحسين الأداء
class AdvancedQuillEditorWidget extends StatefulWidget {
  final String initialContent;
  final bool isReadOnly;
  final Function(String)? onContentChanged;
  final VoidCallback? onSave;
  final String? placeholder;
  final bool showToolbar;
  final double? minHeight;
  final double? maxHeight;

  // خصائص جديدة للتكامل مع النظام
  final int? documentId;
  final bool enableAutoSave;
  final Function(String)? onAutoSave;
  final Function(String, String)? onExport;
  final bool enableComments;
  final Function(String, int, int)? onAddComment;
  final bool enableVersionHistory;
  final Function()? onShowVersionHistory;

  // خصائص معلومات المستند
  final String? documentTitle;
  final String? documentType;
  final String? documentDescription;
  final String? authorName;

  const AdvancedQuillEditorWidget({
    super.key,
    this.initialContent = '',
    this.isReadOnly = false,
    this.onContentChanged,
    this.onSave,
    this.placeholder = 'ابدأ الكتابة هنا...',
    this.showToolbar = true,
    this.minHeight = 300,
    this.maxHeight,
    this.documentId,
    this.enableAutoSave = true,
    this.onAutoSave,
    this.onExport,
    this.enableComments = false,
    this.onAddComment,
    this.enableVersionHistory = false,
    this.onShowVersionHistory,
    this.documentTitle,
    this.documentType = 'مستند عام',
    this.documentDescription,
    this.authorName,
  });

  @override
  State<AdvancedQuillEditorWidget> createState() => _AdvancedQuillEditorWidgetState();
}

class _AdvancedQuillEditorWidgetState extends State<AdvancedQuillEditorWidget> {
  late QuillController _controller;
  late FocusNode _focusNode;
  late ScrollController _scrollController;
  bool _isInitialized = false;

  // متغيرات حالة إضافية
  final ValueNotifier<bool> _isSaving = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _hasUnsavedChanges = ValueNotifier<bool>(false);
  Timer? _autoSaveTimer;
  Timer? _backupTimer;

  // خدمة API للمستندات
  final UnifiedDocumentApiService _documentService = UnifiedDocumentApiService();

  // قائمة النسخ الاحتياطية المحلية
  final List<Map<String, dynamic>> _localBackups = [];

  // متغير لتتبع آخر محتوى تم معالجته
  String _lastProcessedContent = '';

  // متغير لتتبع التهيئة الأولى
  bool _isInitialSetup = true;

  // متغير لتعطيل الحفظ التلقائي مؤقتاً
  bool _isAutoSaveDisabled = false;

  // قائمة تاريخ الإصدارات
  final List<DocumentVersion> _versionHistory = [];

  @override
  void initState() {
    super.initState();
    _initializeEditor();
  }

  @override
  void didUpdateWidget(AdvancedQuillEditorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إعادة تهيئة المحرر فقط إذا تغير المحتوى الأولي بشكل جوهري
    if (oldWidget.initialContent != widget.initialContent && 
        !_isContentUpdateFromEditor(oldWidget.initialContent, widget.initialContent)) {
      debugPrint('🔄 إعادة تهيئة المحرر بسبب تغيير جوهري في المحتوى الأولي');
      _reinitializeEditor();
    }
    
    // إصلاح مشكلة المؤشر في التبويبات
    if (oldWidget.documentId != widget.documentId) {
      _fixTabViewFocusIssue();
    }
  }
  
  /// التحقق من أن التحديث جاء من المحرر نفسه وليس من مصدر خارجي
  bool _isContentUpdateFromEditor(String oldContent, String newContent) {
    return newContent == _lastProcessedContent;
  }

  /// إصلاح مشكلة التركيز في TabBarView
  void _fixTabViewFocusIssue() {
    if (!mounted) return;
    
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _focusNode.canRequestFocus) {
        _focusNode.unfocus();
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted) {
            _focusNode.requestFocus();
            _ensureCursorVisible();
          }
        });
      }
    });
  }

  /// إعادة تهيئة المحرر مع محتوى جديد
  void _reinitializeEditor() {
    if (!mounted) return;

    try {
      _controller.removeListener(_onDocumentChanged);

      Document document;
      
      if (widget.initialContent.isNotEmpty) {
        try {
          final jsonData = jsonDecode(widget.initialContent);
          document = Document.fromJson(jsonData);
          debugPrint('✅ إعادة تحميل المحتوى من JSON');
        } catch (e) {
          document = Document();
          document.insert(0, widget.initialContent);
          debugPrint('✅ إعادة إنشاء مستند جديد مع النص العادي');
        }
      } else {
        document = Document();
        debugPrint('✅ إعادة إنشاء مستند فارغ');
      }

      _controller.document = document;
      _controller.addListener(_onDocumentChanged);
      _lastProcessedContent = '';
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && widget.initialContent.isNotEmpty) {
          final textLength = _controller.document.length - 1;
          _controller.updateSelection(
            TextSelection.collapsed(offset: textLength > 0 ? textLength : 0),
            ChangeSource.local,
          );
          debugPrint('✅ تم تعديل موضع المؤشر بعد إعادة التهيئة');
        }
      });

    } catch (e) {
      QuillErrorHandler.handleError(context, 'إعادة تهيئة المحرر', e);
    }
  }

  void _initializeEditor() {
    try {
      Document document;

      if (widget.initialContent.isNotEmpty) {
        try {
          final jsonData = jsonDecode(widget.initialContent);
          document = Document.fromJson(jsonData);
          debugPrint('✅ تم تحميل المحتوى من JSON');
        } catch (e) {
          document = Document();
          document.insert(0, widget.initialContent);
          debugPrint('✅ تم إنشاء مستند جديد مع النص العادي');
        }
      } else {
        document = Document();
        debugPrint('✅ تم إنشاء مستند فارغ');
      }

      _controller = QuillController(
        document: document,
        selection: const TextSelection.collapsed(offset: 0),
      );

      _focusNode = FocusNode();
      _scrollController = ScrollController();

      _controller.addListener(_onDocumentChanged);
      _startAutoBackup();

      // تعيين حالة التهيئة
      _isInitialized = true;

    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المحرر: $e');
      // إنشاء محرر أساسي في حالة الفشل
      _controller = QuillController(
        document: Document(),
        selection: const TextSelection.collapsed(offset: 0),
      );
      _focusNode = FocusNode();
      _scrollController = ScrollController();
      _isInitialized = true;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isInitialSetup) {
        _isInitialSetup = false;

        if (widget.initialContent.isNotEmpty) {
          final textLength = _controller.document.length - 1;
          final cursorPosition = textLength > 0 ? textLength : 0;
          _controller.updateSelection(
            TextSelection.collapsed(offset: cursorPosition),
            ChangeSource.local,
          );
          debugPrint('✅ تم وضع المؤشر في نهاية النص الموجود');
        } else {
          _controller.updateSelection(
            const TextSelection.collapsed(offset: 0),
            ChangeSource.local,
          );
          debugPrint('✅ تم وضع المؤشر في بداية المستند الفارغ');
        }

        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _ensureCursorVisible();
          }
        });
      }
    });
  }

  /// التأكد من أن المؤشر مرئي
  void _ensureCursorVisible() {
    if (!mounted || !_focusNode.hasFocus || !_scrollController.hasClients) return;

    try {
      final selection = _controller.selection;
      if (!selection.isValid || selection.baseOffset < 0) return;

      final isInTabView = _isWidgetInTabView();
      final delay = isInTabView ? 200 : 50;

      Future.delayed(Duration(milliseconds: delay), () {
        if (!mounted || !_scrollController.hasClients) return;
        _performCursorScroll(selection);
      });
    } catch (e) {
      QuillErrorHandler.handleError(context, 'ضمان رؤية المؤشر', e, showToUser: false);
    }
  }

  /// التحقق من وجود الـ Widget داخل TabBarView
  bool _isWidgetInTabView() {
    try {
      final tabBarView = context.findAncestorWidgetOfExactType<TabBarView>();
      return tabBarView != null;
    } catch (e) {
      return false;
    }
  }

  /// تنفيذ التمرير للمؤشر
  void _performCursorScroll(TextSelection selection) {
    try {
      final text = _controller.document.toPlainText();
      final cursorPosition = selection.baseOffset.clamp(0, text.length);
      final double lineHeight = 25.2;
      int lineCount = 0;
      
      for (int i = 0; i < cursorPosition && i < text.length; i++) {
        if (text[i] == '\n') lineCount++;
      }

      final double targetScrollPosition = lineCount * lineHeight;
      final double currentScroll = _scrollController.offset;
      final double viewportHeight = _scrollController.position.viewportDimension;
      final double buffer = lineHeight * 2;

      if (targetScrollPosition < currentScroll + buffer ||
          targetScrollPosition > currentScroll + viewportHeight - buffer) {
        final double maxScroll = _scrollController.position.maxScrollExtent;
        double idealPosition = targetScrollPosition - (viewportHeight / 2);
        idealPosition = idealPosition.clamp(0.0, maxScroll);

        _scrollController.animateTo(
          idealPosition,
          duration: const Duration(milliseconds: 150),
          curve: Curves.easeOutCubic,
        );
      }
    } catch (e) {
      QuillErrorHandler.handleError(context, 'تمرير المؤشر', e, showToUser: false);
    }
  }

  /// بدء النسخ الاحتياطية التلقائية
  void _startAutoBackup() {
    _backupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _createLocalBackup();
    });
  }

  /// إنشاء نسخة احتياطية محلية
  void _createLocalBackup() {
    try {
      final content = _getDocumentContent();
      final plainText = _getPlainText();

      if (content.isNotEmpty) {
        final backup = {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'content': content,
          'plainText': plainText,
          'wordCount': _getWordCount(),
          'characterCount': _getCharacterCount(),
        };

        _localBackups.add(backup);

        if (_localBackups.length > 10) {
          _localBackups.removeAt(0);
        }

        debugPrint('تم إنشاء نسخة احتياطية محلية - العدد الإجمالي: ${_localBackups.length}');
      }
    } catch (e) {
      QuillErrorHandler.handleError(context, 'إنشاء النسخة الاحتياطية', e, showToUser: false);
    }
  }

  void _onDocumentChanged() {
    if (!mounted) return;

    final content = _getDocumentContent();
    if (content == _lastProcessedContent) return;

    _lastProcessedContent = content;

    if (widget.onContentChanged != null) {
      widget.onContentChanged!(content);
    }

    if (mounted) {
      _hasUnsavedChanges.value = true;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _focusNode.hasFocus) {
        if (_isWidgetInTabView()) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _focusNode.hasFocus) {
              _ensureCursorVisible();
            }
          });
        } else {
          _ensureCursorVisible();
        }
      }
    });

    _startAutoSave();
  }

  /// بدء الحفظ التلقائي
  void _startAutoSave() {
    if (!mounted || _isAutoSaveDisabled) return;

    _autoSaveTimer?.cancel();

    _autoSaveTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _hasUnsavedChanges.value && !_isSaving.value && !_isAutoSaveDisabled) {
        _autoSaveDocument();
      }
    });
  }

  /// الحفظ التلقائي للمستند
  Future<void> _autoSaveDocument() async {
    if (!mounted || _isSaving.value || !widget.enableAutoSave || _isAutoSaveDisabled) return;

    try {
      if (mounted) {
        _isSaving.value = true;
      }

      final content = _getDocumentContent();

      if (widget.documentId != null) {
        final success = await _documentService.autoSaveDocument(
          documentId: widget.documentId!,
          deltaJson: content,
          plainText: _getPlainText(),
        );

        if (mounted) {
          if (success) {
            _hasUnsavedChanges.value = false;
            debugPrint('تم الحفظ التلقائي للمستند ${widget.documentId}');
          } else {
            debugPrint('فشل في الحفظ التلقائي للمستند ${widget.documentId}');
          }
        }
      } else {
        if (widget.onAutoSave != null) {
          widget.onAutoSave!(content);
          if (mounted) {
            _hasUnsavedChanges.value = false;
          }
          debugPrint('تم الحفظ التلقائي باستخدام callback');
        } else {
          await Future.delayed(const Duration(milliseconds: 500));
          if (mounted) {
            _hasUnsavedChanges.value = false;
          }
          debugPrint('تم الحفظ التلقائي (محاكاة)');
        }
      }

    } catch (e) {
      if (mounted) {
        QuillErrorHandler.handleAutoSaveError(context, e);
      }
    } finally {
      if (mounted) {
        _isSaving.value = false;
      }
    }
  }

  String _getDocumentContent() {
    return jsonEncode(_controller.document.toDelta().toJson());
  }

  String _getPlainText() {
    return _controller.document.toPlainText();
  }

  @override
  Widget build(BuildContext context) {
    if (!mounted || !_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox(
          height: constraints.maxHeight,
          child: Column(
            children: [
              // شريط الأدوات الكامل مع جميع الميزات المتقدمة
              if (widget.showToolbar && !widget.isReadOnly)
                Container(
                  constraints: const BoxConstraints(maxHeight: 250), // ارتفاع أكبر لضمان ظهور جميع الأدوات
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: QuillToolbarManager(
                    controller: _controller,
                    showToolbar: widget.showToolbar,
                    documentTitle: widget.documentTitle ?? 'مستند جديد',
                    documentDescription: widget.documentDescription,
                    onContentChanged: widget.onContentChanged,
                    editorWidget: this, // تمرير مرجع المحرر
                  ),
                ),

              // المحرر
              Expanded(
                child: Container(
                  constraints: BoxConstraints(
                    minHeight: widget.minHeight ?? 300,
                    maxHeight: widget.maxHeight ?? double.infinity,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: QuillEditor.basic(
                    controller: _controller,
                    focusNode: _focusNode,
                    scrollController: _scrollController,
                    configurations: QuillEditorConfigurations(
                      scrollable: true,
                      autoFocus: false,
                      expands: true,
                      padding: const EdgeInsets.all(16),
                      showCursor: true,
                      enableInteractiveSelection: true,
                      placeholder: widget.placeholder,
                      textCapitalization: TextCapitalization.none,
                    ),
                  ),
                ),
              ),

              // شريط الحالة
              Container(
                constraints: const BoxConstraints(maxHeight: 50),
                child: _buildStatusBar(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// شريط الحالة
  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Text(
            'الكلمات: ${_getWordCount()}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'الأحرف: ${_getCharacterCount()}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const Spacer(),

          // أزرار الميزات الإضافية
          if (widget.enableComments) ...[
            IconButton(
              icon: const Icon(Icons.comment, size: 16),
              tooltip: 'إضافة تعليق',
              onPressed: _addComment,
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),
            const SizedBox(width: 8),
          ],

          if (widget.enableVersionHistory) ...[
            IconButton(
              icon: const Icon(Icons.history, size: 16),
              tooltip: 'تاريخ الإصدارات',
              onPressed: widget.onShowVersionHistory,
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),
            const SizedBox(width: 8),
          ],

          // مؤشر الحفظ التلقائي
          ValueListenableBuilder<bool>(
            valueListenable: _isSaving,
            builder: (context, isSaving, child) {
              if (isSaving) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'جاري الحفظ...',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                );
              }

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 12,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'محفوظ',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.green,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // ==================== دوال المساعدة ====================

  /// تعطيل الحفظ التلقائي مؤقتاً
  void disableAutoSave() {
    _isAutoSaveDisabled = true;
    _autoSaveTimer?.cancel();
  }

  /// تفعيل الحفظ التلقائي
  void enableAutoSave() {
    _isAutoSaveDisabled = false;
  }

  /// إضافة تعليق
  void _addComment() {
    try {
      final selection = _controller.selection;
      if (selection.isCollapsed) {
        // إذا لم يكن هناك نص محدد، اطلب من المستخدم تحديد نص
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى تحديد النص الذي تريد التعليق عليه'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // الحصول على النص المحدد
      final selectedText = _controller.document.toPlainText()
          .substring(selection.start, selection.end);

      // عرض حوار إدخال التعليق
      showDialog(
        context: context,
        builder: (context) {
          String comment = '';
          return AlertDialog(
            title: const Text('إضافة تعليق'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('النص المحدد: "$selectedText"'),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'التعليق',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onChanged: (value) => comment = value,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (comment.isNotEmpty) {
                    widget.onAddComment?.call(comment, selection.start, selection.end);
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة التعليق'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                },
                child: const Text('إضافة'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      QuillErrorHandler.handleError(context, 'إضافة تعليق', e);
    }
  }

  /// حفظ إصدار جديد في تاريخ الإصدارات
  void _saveVersion({String? changeDescription}) {
    try {
      final currentContent = jsonEncode(_controller.document.toDelta().toJson());

      // التحقق من وجود تغييرات فعلية
      if (_versionHistory.isNotEmpty) {
        final lastVersion = _versionHistory.last;
        if (lastVersion.content == currentContent) {
          // لا توجد تغييرات، لا حاجة لحفظ إصدار جديد
          return;
        }
      }

      final newVersion = DocumentVersion.fromContent(
        content: currentContent,
        authorName: widget.authorName ?? 'مستخدم غير معروف',
        changeDescription: changeDescription,
        metadata: {
          'documentId': widget.documentId?.toString(),
          'documentTitle': widget.documentTitle,
          'documentType': widget.documentType,
        },
      );

      _versionHistory.add(newVersion);

      // الاحتفاظ بآخر 50 إصدار فقط لتوفير الذاكرة
      if (_versionHistory.length > 50) {
        _versionHistory.removeAt(0);
      }

      debugPrint('تم حفظ إصدار جديد: ${newVersion.id}');
    } catch (e) {
      QuillErrorHandler.handleError(context, 'حفظ إصدار', e);
    }
  }

  /// عرض تاريخ الإصدارات
  void _showVersionHistory() {
    if (_versionHistory.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد تاريخ إصدارات متاح'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // showDialog(
    //   context: context,
    //   builder: (context) => _VersionHistoryDialog(
    //     versions: _versionHistory,
    //     onRestoreVersion: _restoreVersion,
    //     onCompareVersions: _compareVersions,
    //   ),
    // );
  }

  /// استعادة إصدار سابق
  void _restoreVersion(DocumentVersion version) {
    try {
      // حفظ الإصدار الحالي قبل الاستعادة
      _saveVersion(changeDescription: 'نقطة استعادة قبل العودة للإصدار ${version.id}');

      // استعادة المحتوى
      final deltaJson = jsonDecode(version.content);
      final document = Document.fromJson(deltaJson);

      _controller.document = document;

      // حفظ إصدار الاستعادة
      _saveVersion(changeDescription: 'تم استعادة الإصدار من ${version.formattedDate}');

      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم استعادة الإصدار من ${version.formattedDate}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      QuillErrorHandler.handleError(context, 'استعادة إصدار', e);
    }
  }

  /// مقارنة إصدارين
  // void _compareVersions(DocumentVersion version1, DocumentVersion version2) {
  //   showDialog(
  //     context: context,
  //     builder: (context) => _VersionComparisonDialog(
  //       version1: version1,
  //       version2: version2,
  //     ),
  //   );
  // }

  /// حساب عدد الكلمات
  int _getWordCount() {
    final text = _getPlainText();
    if (text.isEmpty) return 0;
    return text.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }

  /// حساب عدد الأحرف
  int _getCharacterCount() {
    return _getPlainText().length;
  }

  @override
  void dispose() {
    try {
      _autoSaveTimer?.cancel();
      _backupTimer?.cancel();

      try {
        _controller.removeListener(_onDocumentChanged);
      } catch (e) {
        debugPrint('خطأ في إزالة مستمع المتحكم: $e');
      }

      try {
        _controller.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من المتحكم: $e');
      }

      try {
        _focusNode.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من FocusNode: $e');
      }

      try {
        _scrollController.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من ScrollController: $e');
      }

      try {
        _isSaving.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من _isSaving: $e');
      }

      try {
        _hasUnsavedChanges.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من _hasUnsavedChanges: $e');
      }

    } catch (e) {
      debugPrint('خطأ عام في dispose: $e');
    } finally {
      super.dispose();
    }
  }
}
