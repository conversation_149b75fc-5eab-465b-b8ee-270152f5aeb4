import 'user_model.dart';

/// أنواع السجلات
enum LogType {
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  error('error', 'خطأ'),
  debug('debug', 'تصحيح'),
  security('security', 'أمان'),
  audit('audit', 'مراجعة'),
  performance('performance', 'أداء'),
  system('system', 'نظام');

  const LogType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static LogType fromValue(String value) {
    return LogType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => LogType.info,
    );
  }
}

/// مستويات السجلات
enum LogLevel {
  trace('trace', 'تتبع'),
  debug('debug', 'تصحيح'),
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  error('error', 'خطأ'),
  critical('critical', 'حرج');

  const LogLevel(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static LogLevel fromValue(String value) {
    return LogLevel.values.firstWhere(
      (level) => level.value == value,
      orElse: () => LogLevel.info,
    );
  }
}

/// نموذج سجل النظام - متطابق مع ASP.NET Core API
class SystemLog {
  final int id;
  final String logType;
  final String logLevel;
  final String message;
  final String? details;
  final int? userId;
  final String? ipAddress;
  final int createdAt;

  // Navigation properties
  final User? user;

  const SystemLog({
    required this.id,
    required this.logType,
    required this.logLevel,
    required this.message,
    this.details,
    this.userId,
    this.ipAddress,
    required this.createdAt,
    this.user,
  });

  /// إنشاء SystemLog من JSON (من API)
  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as int,
      logType: json['logType'] as String,
      logLevel: json['logLevel'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
      userId: json['userId'] as int?,
      ipAddress: json['ipAddress'] as String?,
      createdAt: json['createdAt'] as int,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل SystemLog إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'logType': logType,
      'logLevel': logLevel,
      'message': message,
      'details': details,
      'userId': userId,
      'ipAddress': ipAddress,
      'createdAt': createdAt,
    };
  }

  /// إنشاء نسخة معدلة من SystemLog
  SystemLog copyWith({
    int? id,
    String? logType,
    String? logLevel,
    String? message,
    String? details,
    int? userId,
    String? ipAddress,
    int? createdAt,
    User? user,
  }) {
    return SystemLog(
      id: id ?? this.id,
      logType: logType ?? this.logType,
      logLevel: logLevel ?? this.logLevel,
      message: message ?? this.message,
      details: details ?? this.details,
      userId: userId ?? this.userId,
      ipAddress: ipAddress ?? this.ipAddress,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
    );
  }

  /// الحصول على نوع السجل كـ enum
  LogType get logTypeEnum => LogType.fromValue(logType);

  /// الحصول على مستوى السجل كـ enum
  LogLevel get logLevelEnum => LogLevel.fromValue(logLevel);

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// التحقق من كون السجل خطأ
  bool get isError => logLevelEnum == LogLevel.error || logLevelEnum == LogLevel.critical;

  /// التحقق من كون السجل تحذير
  bool get isWarning => logLevelEnum == LogLevel.warning;

  /// التحقق من كون السجل أمني
  bool get isSecurity => logTypeEnum == LogType.security;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemLog && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SystemLog(id: $id, type: $logType, level: $logLevel, message: $message)';
  }
}

/// نموذج طلب إنشاء سجل نظام
class CreateSystemLogRequest {
  final String logType;
  final String logLevel;
  final String message;
  final String? details;
  final int? userId;
  final String? ipAddress;

  const CreateSystemLogRequest({
    required this.logType,
    required this.logLevel,
    required this.message,
    this.details,
    this.userId,
    this.ipAddress,
  });

  Map<String, dynamic> toJson() {
    return {
      'logType': logType,
      'logLevel': logLevel,
      'message': message,
      'details': details,
      'userId': userId,
      'ipAddress': ipAddress,
    };
  }
}

/// نموذج مرشح السجلات
class SystemLogFilter {
  final String? logType;
  final String? logLevel;
  final int? userId;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const SystemLogFilter({
    this.logType,
    this.logLevel,
    this.userId,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  Map<String, dynamic> toJson() {
    return {
      'logType': logType,
      'logLevel': logLevel,
      'userId': userId,
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'searchQuery': searchQuery,
    };
  }
}

/// إحصائيات السجلات
class SystemLogStats {
  final int totalLogs;
  final int errorLogs;
  final int warningLogs;
  final int infoLogs;
  final int securityLogs;
  final Map<String, int> logsByType;
  final Map<String, int> logsByLevel;

  const SystemLogStats({
    required this.totalLogs,
    required this.errorLogs,
    required this.warningLogs,
    required this.infoLogs,
    required this.securityLogs,
    required this.logsByType,
    required this.logsByLevel,
  });

  factory SystemLogStats.fromJson(Map<String, dynamic> json) {
    return SystemLogStats(
      totalLogs: json['totalLogs'] as int,
      errorLogs: json['errorLogs'] as int,
      warningLogs: json['warningLogs'] as int,
      infoLogs: json['infoLogs'] as int,
      securityLogs: json['securityLogs'] as int,
      logsByType: Map<String, int>.from(json['logsByType'] as Map),
      logsByLevel: Map<String, int>.from(json['logsByLevel'] as Map),
    );
  }
}
