/// تعدادات أنواع إجراءات المهام
/// متطابقة مع ASP.NET Core API
enum TaskActionType {
  /// إنشاء المهمة
  created('created', 'تم إنشاء المهمة'),
  
  /// تعيين المهمة
  assigned('assigned', 'تم تعيين المهمة'),
  
  /// تغيير الحالة
  statusChanged('status_changed', 'تم تغيير الحالة'),
  
  /// تغيير الأولوية
  priorityChanged('priority_changed', 'تم تغيير الأولوية'),
  
  /// إضافة تعليق
  commented('commented', 'تم إضافة تعليق'),
  
  /// إرفاق ملف
  fileAttached('file_attached', 'تم إرفاق ملف'),
  
  /// حذف ملف
  fileRemoved('file_removed', 'تم حذف ملف'),
  
  /// تحويل المهمة
  transferred('transferred', 'تم تحويل المهمة'),
  
  /// طلب معلومات
  infoRequested('info_requested', 'تم طلب معلومات'),
  
  /// تقديم معلومات
  infoProvided('info_provided', 'تم تقديم معلومات'),
  
  /// إكمال المهمة
  completed('completed', 'تم إكمال المهمة'),
  
  /// إلغاء المهمة
  cancelled('cancelled', 'تم إلغاء المهمة'),
  
  /// تحديث التقدم
  progressUpdated('progress_updated', 'تم تحديث التقدم'),
  
  /// حذف المهمة
  deleted('deleted', 'تم حذف المهمة'),
  
  /// استعادة المهمة
  restored('restored', 'تم استعادة المهمة'),
  
  /// تحديث المهمة
  updated('updated', 'تم تحديث المهمة'),
  
  /// إضافة مهمة فرعية
  subtaskAdded('subtask_added', 'تم إضافة مهمة فرعية'),
  
  /// حذف مهمة فرعية
  subtaskRemoved('subtask_removed', 'تم حذف مهمة فرعية'),
  
  /// إكمال مهمة فرعية
  subtaskCompleted('subtask_completed', 'تم إكمال مهمة فرعية'),
  
  /// تحديث تاريخ الاستحقاق
  dueDateChanged('due_date_changed', 'تم تغيير تاريخ الاستحقاق'),
  
  /// تحديث تاريخ البداية
  startDateChanged('start_date_changed', 'تم تغيير تاريخ البداية'),
  
  /// تحديث الوصف
  descriptionChanged('description_changed', 'تم تغيير الوصف'),
  
  /// تحديث العنوان
  titleChanged('title_changed', 'تم تغيير العنوان'),
  
  /// تحديث نوع المهمة
  typeChanged('type_changed', 'تم تغيير نوع المهمة'),
  
  /// تحديث القسم
  departmentChanged('department_changed', 'تم تغيير القسم'),
  
  /// تحديث الوقت المقدر
  estimatedTimeChanged('estimated_time_changed', 'تم تغيير الوقت المقدر'),
  
  /// تحديث الوقت الفعلي
  actualTimeChanged('actual_time_changed', 'تم تغيير الوقت الفعلي'),
  
  /// إضافة مساهم
  contributorAdded('contributor_added', 'تم إضافة مساهم'),
  
  /// حذف مساهم
  contributorRemoved('contributor_removed', 'تم حذف مساهم'),
  
  /// تسجيل مساهمة
  contributionRecorded('contribution_recorded', 'تم تسجيل مساهمة'),
  
  /// إرسال رسالة
  messageSent('message_sent', 'تم إرسال رسالة'),
  
  /// تحديث إعدادات الوصول
  accessUpdated('access_updated', 'تم تحديث إعدادات الوصول'),
  
  /// أخرى
  other('other', 'إجراء آخر');

  const TaskActionType(this.value, this.displayName);

  /// القيمة المخزنة في قاعدة البيانات
  final String value;
  
  /// الاسم المعروض
  final String displayName;

  /// الحصول على نوع الإجراء من القيمة
  static TaskActionType fromValue(String value) {
    return TaskActionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaskActionType.other,
    );
  }

  /// الحصول على نوع الإجراء من الاسم
  static TaskActionType fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskActionType.values.firstWhere(
      (type) => 
        type.name.toLowerCase() == lowerName ||
        type.value.toLowerCase() == lowerName ||
        type.displayName.toLowerCase() == lowerName,
      orElse: () => TaskActionType.other,
    );
  }

  /// تحويل إلى JSON
  String toJson() => value;

  /// إنشاء من JSON
  static TaskActionType fromJson(String json) => fromValue(json);

  @override
  String toString() => displayName;
}

/// امتدادات لـ TaskActionType
extension TaskActionTypeExtensions on TaskActionType {
  /// هل هذا الإجراء يتطلب إشعار؟
  bool get requiresNotification {
    switch (this) {
      case TaskActionType.created:
      case TaskActionType.assigned:
      case TaskActionType.transferred:
      case TaskActionType.completed:
      case TaskActionType.cancelled:
      case TaskActionType.infoRequested:
      case TaskActionType.dueDateChanged:
        return true;
      default:
        return false;
    }
  }

  /// هل هذا الإجراء يؤثر على حالة المهمة؟
  bool get affectsTaskStatus {
    switch (this) {
      case TaskActionType.statusChanged:
      case TaskActionType.completed:
      case TaskActionType.cancelled:
      case TaskActionType.restored:
        return true;
      default:
        return false;
    }
  }

  /// هل هذا الإجراء يؤثر على التقدم؟
  bool get affectsProgress {
    switch (this) {
      case TaskActionType.progressUpdated:
      case TaskActionType.subtaskCompleted:
      case TaskActionType.completed:
        return true;
      default:
        return false;
    }
  }

  /// هل هذا الإجراء يتطلب تسجيل تفاصيل إضافية؟
  bool get requiresDetails {
    switch (this) {
      case TaskActionType.statusChanged:
      case TaskActionType.priorityChanged:
      case TaskActionType.progressUpdated:
      case TaskActionType.transferred:
      case TaskActionType.commented:
      case TaskActionType.fileAttached:
      case TaskActionType.fileRemoved:
        return true;
      default:
        return false;
    }
  }

  /// الحصول على أيقونة الإجراء
  String get icon {
    switch (this) {
      case TaskActionType.created:
        return '➕';
      case TaskActionType.assigned:
        return '👤';
      case TaskActionType.statusChanged:
        return '🔄';
      case TaskActionType.priorityChanged:
        return '⚡';
      case TaskActionType.commented:
        return '💬';
      case TaskActionType.fileAttached:
        return '📎';
      case TaskActionType.fileRemoved:
        return '🗑️';
      case TaskActionType.transferred:
        return '↗️';
      case TaskActionType.completed:
        return '✅';
      case TaskActionType.cancelled:
        return '❌';
      case TaskActionType.progressUpdated:
        return '📊';
      case TaskActionType.deleted:
        return '🗑️';
      case TaskActionType.restored:
        return '♻️';
      default:
        return '📝';
    }
  }
}
