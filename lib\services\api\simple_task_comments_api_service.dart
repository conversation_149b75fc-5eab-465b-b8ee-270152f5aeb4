import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../models/task_comment_models.dart';
import 'api_service.dart';

/// خدمة API مبسطة لتعليقات المهام
class SimpleTaskCommentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على تعليقات مهمة محددة
  Future<List<TaskComment>> getCommentsByTask(int taskId) async {
    try {
      debugPrint('🔍 جلب تعليقات المهمة: $taskId');
      
      final response = await _apiService.get('/api/TaskComments/task/$taskId');
      
      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(utf8.decode(response.bodyBytes));
        final comments = jsonList.map((json) => TaskComment.fromJson(json)).toList();
        
        debugPrint('✅ تم جلب ${comments.length} تعليق للمهمة $taskId');
        return comments;
      } else {
        debugPrint('❌ خطأ في جلب التعليقات: ${response.statusCode}');
        throw Exception('فشل في جلب التعليقات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في getCommentsByTask: $e');
      rethrow;
    }
  }

  /// إنشاء تعليق جديد
  Future<TaskComment> createComment(TaskComment comment) async {
    try {
      debugPrint('📝 إنشاء تعليق جديد للمهمة: ${comment.taskId}');
      
      // استخدام endpoint مبسط
      final requestData = {
        'taskId': comment.taskId,
        'userId': comment.userId,
        'content': comment.content,
      };
      
      debugPrint('📤 إرسال البيانات: $requestData');
      
      final response = await _apiService.post('/api/TaskComments/simple', requestData);
      
      debugPrint('📥 استجابة الخادم: ${response.statusCode}');
      debugPrint('📄 محتوى الاستجابة: ${response.body}');
      
      if (response.statusCode == 201 || response.statusCode == 200) {
        final json = jsonDecode(utf8.decode(response.bodyBytes));
        final newComment = TaskComment.fromJson(json);
        
        debugPrint('✅ تم إنشاء التعليق بنجاح: ${newComment.id}');
        return newComment;
      } else {
        debugPrint('❌ خطأ في إنشاء التعليق: ${response.statusCode}');
        debugPrint('📄 محتوى الاستجابة: ${response.body}');
        throw Exception('فشل في إنشاء التعليق: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في createComment: $e');
      rethrow;
    }
  }

  /// تحديث تعليق
  Future<TaskComment> updateComment(int id, TaskComment comment) async {
    try {
      debugPrint('✏️ تحديث التعليق: $id');
      
      final response = await _apiService.put('/api/TaskComments/$id', comment.toJson());
      
      if (response.statusCode == 200 || response.statusCode == 204) {
        // إذا كانت الاستجابة 204 (No Content)، نرجع التعليق المحدث
        if (response.statusCode == 204) {
          return comment.copyWith(id: id);
        }
        
        final json = jsonDecode(utf8.decode(response.bodyBytes));
        final updatedComment = TaskComment.fromJson(json);
        
        debugPrint('✅ تم تحديث التعليق بنجاح: $id');
        return updatedComment;
      } else {
        debugPrint('❌ خطأ في تحديث التعليق: ${response.statusCode}');
        throw Exception('فشل في تحديث التعليق: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في updateComment: $e');
      rethrow;
    }
  }

  /// حذف تعليق
  Future<bool> deleteComment(int id) async {
    try {
      debugPrint('🗑️ حذف التعليق: $id');
      
      final response = await _apiService.delete('/api/TaskComments/$id');
      
      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('✅ تم حذف التعليق بنجاح: $id');
        return true;
      } else {
        debugPrint('❌ خطأ في حذف التعليق: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في deleteComment: $e');
      return false;
    }
  }

  /// الحصول على تعليق بالمعرف
  Future<TaskComment?> getCommentById(int id) async {
    try {
      debugPrint('🔍 جلب التعليق: $id');
      
      final response = await _apiService.get('/api/TaskComments/$id');
      
      if (response.statusCode == 200) {
        final json = jsonDecode(utf8.decode(response.bodyBytes));
        final comment = TaskComment.fromJson(json);
        
        debugPrint('✅ تم جلب التعليق بنجاح: $id');
        return comment;
      } else if (response.statusCode == 404) {
        debugPrint('⚠️ التعليق غير موجود: $id');
        return null;
      } else {
        debugPrint('❌ خطأ في جلب التعليق: ${response.statusCode}');
        throw Exception('فشل في جلب التعليق: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في getCommentById: $e');
      return null;
    }
  }
}