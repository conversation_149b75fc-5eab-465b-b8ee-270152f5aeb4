import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../constants/app_styles.dart';

/// تعداد موضع المفتاح
enum ChartLegendPosition {
  top,
  bottom,
  left,
  right,
}

/// مكون المخطط الخطي باستخدام Syncfusion Charts
class LineChartVisualization extends StatelessWidget {
  /// عنوان المخطط
  final String title;

  /// وصف المخطط
  final String? description;

  /// بيانات المخطط
  final List<Map<String, dynamic>> data;

  /// حقل المحور السيني (X)
  final String xAxisField;

  /// عنوان المحور السيني
  final String? xAxisLabel;

  /// حقول المحور الصادي (Y)
  final List<String> yAxisFields;

  /// تسميات حقول المحور الصادي
  final List<String>? yAxisLabels;

  /// عنوان المحور الصادي
  final String? yAxisLabel;

  /// إظهار النقاط
  final bool showDots;

  /// إظهار المنطقة تحت الخط
  final bool showArea;

  /// إظهار التسميات
  final bool showLabels;

  /// إظهار الشبكة
  final bool showGrid;

  /// إظهار المفتاح
  final bool showLegend;

  /// موضع المفتاح
  final ChartLegendPosition? legendPosition;

  /// ألوان السلاسل
  final List<Color>? seriesColors;

  /// العرض
  final double? width;

  /// الارتفاع
  final double? height;

  const LineChartVisualization({
    super.key,
    required this.title,
    this.description,
    required this.data,
    required this.xAxisField,
    this.xAxisLabel,
    required this.yAxisFields,
    this.yAxisLabels,
    this.yAxisLabel,
    this.showDots = true,
    this.showArea = false,
    this.showLabels = true,
    this.showGrid = true,
    this.showLegend = true,
    this.legendPosition,
    this.seriesColors,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty || yAxisFields.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات'),
      );
    }

    // تحديد الارتفاع
    final calculatedHeight = height ?? 300.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المخطط
              Text(
                title,
                style: AppStyles.titleMedium,
              ),
              if (description != null && description!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  description!,
                  style: AppStyles.bodySmall,
                ),
              ],
              const SizedBox(height: 16),
              // المخطط
              SizedBox(
                height: calculatedHeight,
                child: _buildChart(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المخطط باستخدام Syncfusion
  Widget _buildChart() {
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        title: AxisTitle(text: xAxisLabel ?? ''),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      primaryYAxis: NumericAxis(
        title: AxisTitle(text: yAxisLabel ?? ''),
        majorGridLines: MajorGridLines(width: showGrid ? 1 : 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      legend: Legend(
        isVisible: showLegend,
        position: _getSyncfusionLegendPosition(),
      ),
      series: _createLineSeries(),
    );
  }

  /// إنشاء سلاسل الخطوط
  List<CartesianSeries> _createLineSeries() {
    final List<CartesianSeries> series = [];

    for (int i = 0; i < yAxisFields.length; i++) {
      final field = yAxisFields[i];
      final fieldName = yAxisLabels != null && i < yAxisLabels!.length
          ? yAxisLabels![i]
          : field;

      series.add(
        LineSeries<Map<String, dynamic>, String>(
          dataSource: data,
          xValueMapper: (Map<String, dynamic> item, _) => item[xAxisField].toString(),
          yValueMapper: (Map<String, dynamic> item, _) => _getYValueFromData(item, field),
          name: fieldName,
          color: _getSeriesColor(i),
          width: 3,
          markerSettings: MarkerSettings(
            isVisible: showDots,
            shape: DataMarkerType.circle,
            width: 8,
            height: 8,
            borderColor: Colors.white,
            borderWidth: 2,
          ),
          dataLabelSettings: DataLabelSettings(isVisible: showLabels),
        ),
      );
    }

    return series;
  }

  /// تحويل موضع المفتاح إلى Syncfusion
  LegendPosition _getSyncfusionLegendPosition() {
    switch (legendPosition) {
      case ChartLegendPosition.top:
        return LegendPosition.top;
      case ChartLegendPosition.left:
        return LegendPosition.left;
      case ChartLegendPosition.right:
        return LegendPosition.right;
      case ChartLegendPosition.bottom:
      default:
        return LegendPosition.bottom;
    }
  }

  /// الحصول على قيمة Y من البيانات
  double _getYValueFromData(Map<String, dynamic> item, String field) {
    final value = item[field];
    if (value is int) {
      return value.toDouble();
    } else if (value is double) {
      return value;
    } else if (value is String) {
      return double.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// الحصول على لون السلسلة
  Color _getSeriesColor(int index) {
    if (seriesColors != null && index < seriesColors!.length) {
      return seriesColors![index];
    }

    // ألوان افتراضية
    final defaultColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.amber,
      Colors.indigo,
      Colors.pink,
      Colors.cyan,
    ];

    return defaultColors[index % defaultColors.length];
  }
}