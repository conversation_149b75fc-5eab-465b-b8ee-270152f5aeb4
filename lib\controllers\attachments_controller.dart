import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';

import '../services/api/attachments_api_service.dart';

/// متحكم المرفقات
class AttachmentsController extends GetxController {
  final AttachmentsApiService _apiService = AttachmentsApiService();

  // قوائم المرفقات
  final RxList<Attachment> _allAttachments = <Attachment>[].obs;
  final RxList<Attachment> _filteredAttachments = <Attachment>[].obs;

  // المرفق الحالي
  final Rx<Attachment?> _currentAttachment = Rx<Attachment?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _fileTypeFilter = Rx<String?>(null);
  final Rx<int?> _taskIdFilter = Rx<int?>(null);

  // Getters
  RxList<Attachment> get attachments => _allAttachments;

  List<Attachment> get allAttachments => _allAttachments;
  List<Attachment> get filteredAttachments => _filteredAttachments;
  Attachment? get currentAttachment => _currentAttachment.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get fileTypeFilter => _fileTypeFilter.value;
  int? get taskIdFilter => _taskIdFilter.value;

  @override
  void onInit() {
    super.onInit();
    loadAllAttachments();
  }

  /// تحميل جميع المرفقات
  Future<void> loadAllAttachments() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachments = await _apiService.getAllAttachments();
      _allAttachments.assignAll(attachments);
      _applyFilters();
      debugPrint('تم تحميل ${attachments.length} مرفق');
    } catch (e) {
      _error.value = 'خطأ في تحميل المرفقات: $e';
      debugPrint('خطأ في تحميل المرفقات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مرفق بالمعرف
  Future<void> getAttachmentById(int id, {bool forceRefresh = false}) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachment = await _apiService.getAttachmentById(id, forceRefresh: forceRefresh);
      _currentAttachment.value = attachment;
      debugPrint('تم تحميل المرفق: [32m${attachment?.fileName ?? 'غير محدد'}[0m');
    } catch (e) {
      _error.value = 'خطأ في تحميل المرفق: $e';
      debugPrint('خطأ في تحميل المرفق: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مرفق جديد
  Future<bool> createAttachment(Attachment attachment) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // Assuming attachment.taskId should not be null when calling this method.
      // If attachment.taskId can be null and the API expects a non-null int,
      // this will cause a runtime error or the API call will fail.
      // The AttachmentsApiService.createAttachment should ideally accept int? for taskId
      final newAttachment = await _apiService.createAttachmentMetadata(attachment);
      if (newAttachment != null) {
        _allAttachments.add(newAttachment);
        _applyFilters();
        debugPrint('تم إنشاء مرفق جديد: ${newAttachment.fileName}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المرفق: $e';
      debugPrint('خطأ في إنشاء المرفق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مرفق
  Future<bool> updateAttachment(int id, Attachment attachment) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedAttachment = await _apiService.updateAttachment(attachment);
      if (updatedAttachment != null) {
        final index = _allAttachments.indexWhere((a) => a.id == id);
        if (index != -1) {
          _allAttachments[index] = updatedAttachment;
          _applyFilters();
        }
        debugPrint('تم تحديث المرفق: ${updatedAttachment.fileName}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المرفق: $e';
      debugPrint('خطأ في تحديث المرفق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مرفق
  Future<bool> deleteAttachment(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteAttachment(id);
      _allAttachments.removeWhere((a) => a.id == id);
      _applyFilters();
      debugPrint('تم حذف المرفق');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف المرفق: $e';
      debugPrint('خطأ في حذف المرفق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على المرفقات حسب المهمة
  Future<void> getAttachmentsByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachments = await _apiService.getAttachmentsByTask(taskId);
      _allAttachments.assignAll(attachments);
      _applyFilters();
      debugPrint('تم تحميل ${attachments.length} مرفق للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مرفقات المهمة: $e';
      debugPrint('خطأ في تحميل مرفقات المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allAttachments.where((attachment) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!attachment.fileName.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح نوع الملف
      if (_fileTypeFilter.value != null && attachment.fileType != _fileTypeFilter.value) {
        return false;
      }

      // مرشح المهمة
      if (_taskIdFilter.value != null && attachment.taskId != _taskIdFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredAttachments.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح نوع الملف
  void setFileTypeFilter(String? fileType) {
    _fileTypeFilter.value = fileType;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskIdFilter.value = taskId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _fileTypeFilter.value = null;
    _taskIdFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllAttachments();
  }
}
