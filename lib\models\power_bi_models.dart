

/// أنواع الرسوم البيانية في Power BI
enum PowerBIChartType {
  bar('bar', 'مخطط شريطي'),
  line('line', 'مخطط خطي'),
  pie('pie', 'مخطط دائري'),
  scatter('scatter', 'مخطط انتشاري'),
  bubble('bubble', 'مخطط فقاعي'),
  radar('radar', 'مخطط راداري'),
  table('table', 'جدول'),
  heatmap('heatmap', 'خريطة حرارية'),
  treemap('treemap', 'خريطة شجرية'),
  gauge('gauge', 'مخطط مقياس');

  const PowerBIChartType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static PowerBIChartType fromString(String value) {
    return PowerBIChartType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PowerBIChartType.bar,
    );
  }
}

/// دوال التجميع المتاحة
enum AggregateFunction {
  none('none', 'بدون تجميع'),
  sum('sum', 'مجموع'),
  count('count', 'عدد'),
  avg('avg', 'متوسط'),
  min('min', 'أصغر قيمة'),
  max('max', 'أكبر قيمة'),
  distinct('distinct', 'قيم مميزة');

  const AggregateFunction(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static AggregateFunction fromString(String value) {
    return AggregateFunction.values.firstWhere(
      (func) => func.value == value,
      orElse: () => AggregateFunction.none,
    );
  }
}

/// نموذج تقرير Power BI
class PowerBIReport {
  final String id;
  final String title;
  final String? description;
  final PowerBIChartType chartType;
  final String createdById;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String tableName;
  final List<String> columnNames;
  final String xAxisColumn;
  final String yAxisColumn;
  final AggregateFunction? yAxisAggregateFunction;
  final String? sizeColumn;
  final String? colorColumn;
  final String? filterCriteria;
  final List<String>? relatedTables;
  final List<String>? joinConditions;
  final List<String>? joinTypes;
  final bool isPublic;
  final bool isShared;
  final List<String>? sharedWithUserIds;

  const PowerBIReport({
    required this.id,
    required this.title,
    this.description,
    required this.chartType,
    required this.createdById,
    required this.createdAt,
    this.updatedAt,
    required this.tableName,
    required this.columnNames,
    required this.xAxisColumn,
    required this.yAxisColumn,
    this.yAxisAggregateFunction,
    this.sizeColumn,
    this.colorColumn,
    this.filterCriteria,
    this.relatedTables,
    this.joinConditions,
    this.joinTypes,
    this.isPublic = false,
    this.isShared = false,
    this.sharedWithUserIds,
  });

  /// إنشاء نسخة من التقرير مع تعديل بعض الخصائص
  PowerBIReport copyWith({
    String? id,
    String? title,
    String? description,
    PowerBIChartType? chartType,
    String? createdById,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? tableName,
    List<String>? columnNames,
    String? xAxisColumn,
    String? yAxisColumn,
    AggregateFunction? yAxisAggregateFunction,
    String? sizeColumn,
    String? colorColumn,
    String? filterCriteria,
    List<String>? relatedTables,
    List<String>? joinConditions,
    List<String>? joinTypes,
    bool? isPublic,
    bool? isShared,
    List<String>? sharedWithUserIds,
  }) {
    return PowerBIReport(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      chartType: chartType ?? this.chartType,
      createdById: createdById ?? this.createdById,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tableName: tableName ?? this.tableName,
      columnNames: columnNames ?? this.columnNames,
      xAxisColumn: xAxisColumn ?? this.xAxisColumn,
      yAxisColumn: yAxisColumn ?? this.yAxisColumn,
      yAxisAggregateFunction: yAxisAggregateFunction ?? this.yAxisAggregateFunction,
      sizeColumn: sizeColumn ?? this.sizeColumn,
      colorColumn: colorColumn ?? this.colorColumn,
      filterCriteria: filterCriteria ?? this.filterCriteria,
      relatedTables: relatedTables ?? this.relatedTables,
      joinConditions: joinConditions ?? this.joinConditions,
      joinTypes: joinTypes ?? this.joinTypes,
      isPublic: isPublic ?? this.isPublic,
      isShared: isShared ?? this.isShared,
      sharedWithUserIds: sharedWithUserIds ?? this.sharedWithUserIds,
    );
  }

  /// تحويل إلى Map للإرسال إلى API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'chartType': chartType.value,
      'createdById': createdById,
      'createdAt': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updatedAt': updatedAt != null ? updatedAt!.millisecondsSinceEpoch ~/ 1000 : null,
      'tableName': tableName,
      'columnNames': columnNames,
      'xAxisColumn': xAxisColumn,
      'yAxisColumn': yAxisColumn,
      'yAxisAggregateFunction': yAxisAggregateFunction?.value,
      'sizeColumn': sizeColumn,
      'colorColumn': colorColumn,
      'filterCriteria': filterCriteria,
      'relatedTables': relatedTables,
      'joinConditions': joinConditions,
      'joinTypes': joinTypes,
      'isPublic': isPublic,
      'isShared': isShared,
      'sharedWithUserIds': sharedWithUserIds,
    };
  }

  /// إنشاء من Map (من API)
  factory PowerBIReport.fromJson(Map<String, dynamic> json) {
    return PowerBIReport(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      chartType: PowerBIChartType.fromString(json['chartType'] ?? 'bar'),
      createdById: json['createdById']?.toString() ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['createdAt'] as int) * 1000)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] as int) * 1000)
          : null,
      tableName: json['tableName'] ?? '',
      columnNames: List<String>.from(json['columnNames'] ?? []),
      xAxisColumn: json['xAxisColumn'] ?? '',
      yAxisColumn: json['yAxisColumn'] ?? '',
      yAxisAggregateFunction: json['yAxisAggregateFunction'] != null
          ? AggregateFunction.fromString(json['yAxisAggregateFunction'])
          : null,
      sizeColumn: json['sizeColumn'],
      colorColumn: json['colorColumn'],
      filterCriteria: json['filterCriteria'],
      relatedTables: json['relatedTables'] != null
          ? List<String>.from(json['relatedTables'])
          : null,
      joinConditions: json['joinConditions'] != null
          ? List<String>.from(json['joinConditions'])
          : null,
      joinTypes: json['joinTypes'] != null
          ? List<String>.from(json['joinTypes'])
          : null,
      isPublic: json['isPublic'] ?? false,
      isShared: json['isShared'] ?? false,
      sharedWithUserIds: json['sharedWithUserIds'] != null
          ? List<String>.from(json['sharedWithUserIds'])
          : null,
    );
  }
}
