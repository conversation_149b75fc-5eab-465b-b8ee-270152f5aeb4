import 'dart:convert';
import 'package:http/http.dart' as http;

class RolePermissionApiService {
  final String baseUrl;
  final Map<String, String> headers;

  RolePermissionApiService({required this.baseUrl, required this.headers});

  // جلب جميع الأدوار
  Future<List<dynamic>> getAllRoles() async {
    final response = await http.get(Uri.parse('$baseUrl/CustomRoles'), headers: headers);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب الأدوار');
    }
  }

  // إنشاء دور جديد
  Future<dynamic> createRole(Map<String, dynamic> roleData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/CustomRoles'),
      headers: headers,
      body: jsonEncode(roleData),
    );
    if (response.statusCode == 201) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في إنشاء الدور');
    }
  }

  // تعديل دور
  Future<dynamic> updateRole(int roleId, Map<String, dynamic> roleData) async {
    final response = await http.put(
      Uri.parse('$baseUrl/CustomRoles/$roleId'),
      headers: headers,
      body: jsonEncode(roleData),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في تعديل الدور');
    }
  }

  // حذف دور
  Future<void> deleteRole(int roleId) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/CustomRoles/$roleId'),
      headers: headers,
    );
    if (response.statusCode != 204) {
      throw Exception('فشل في حذف الدور');
    }
  }

  // جلب صلاحيات دور
  Future<List<dynamic>> getRolePermissions(int roleId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/CustomRoles/$roleId/permissions'),
      headers: headers,
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب صلاحيات الدور');
    }
  }

  // منح صلاحية لدور
  Future<void> grantRolePermission(int roleId, int permissionId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/CustomRoles/$roleId/permissions'),
      headers: headers,
      body: jsonEncode({'permissionId': permissionId}),
    );
    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('فشل في منح الصلاحية');
    }
  }

  // إلغاء صلاحية من دور
  Future<void> revokeRolePermission(int roleId, int permissionId) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/CustomRoles/$roleId/permissions/$permissionId'),
      headers: headers,
    );
    if (response.statusCode != 204) {
      throw Exception('فشل في إلغاء الصلاحية');
    }
  }

  // جلب جميع الصلاحيات
  Future<List<dynamic>> getAllPermissions() async {
    final response = await http.get(
      Uri.parse('$baseUrl/Permissions'),
      headers: headers,
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب جميع الصلاحيات');
    }
  }
}
