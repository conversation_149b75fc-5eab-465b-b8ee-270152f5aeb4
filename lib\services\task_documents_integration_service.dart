import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../controllers/task_documents_controller.dart';
import '../models/task_document_model.dart';
import '../models/task_models.dart';

/// خدمة تكامل نظام المستندات مع المهام
/// توفر واجهة موحدة للتعامل مع المستندات المرتبطة بالمهام
class TaskDocumentsIntegrationService extends GetxService {
  final TaskDocumentsController _documentsController = Get.find<TaskDocumentsController>();

  /// تهيئة نظام المستندات للمهمة
  Future<void> initializeTaskDocuments(Task task) async {
    try {
      debugPrint('تهيئة نظام المستندات للمهمة: ${task.id}');
      
      // تحميل مستندات المهمة
      await _documentsController.loadTaskDocuments(task.id);
      
      debugPrint('تم تحميل ${_documentsController.taskDocuments.length} مستند للمهمة ${task.id}');
    } catch (e) {
      debugPrint('خطأ في تهيئة نظام المستندات للمهمة ${task.id}: $e');
    }
  }

  /// إنشاء مستند تقرير افتراضي للمهمة
  Future<TaskDocument?> createDefaultReport(Task task, int createdBy) async {
    try {
      final defaultContent = _generateDefaultReportContent(task);
      
      return await _documentsController.createTaskDocument(
        taskId: task.id,
        title: 'تقرير المهمة: ${task.title}',
        description: 'تقرير تلقائي تم إنشاؤه للمهمة',
        type: TaskDocumentType.report,
        content: defaultContent,
        isShared: false,
        createdBy: createdBy,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير الافتراضي للمهمة ${task.id}: $e');
      return null;
    }
  }

  /// إنشاء مستند خطة افتراضي للمهمة
  Future<TaskDocument?> createDefaultPlan(Task task, int createdBy) async {
    try {
      final defaultContent = _generateDefaultPlanContent(task);
      
      return await _documentsController.createTaskDocument(
        taskId: task.id,
        title: 'خطة تنفيذ المهمة: ${task.title}',
        description: 'خطة تنفيذ تلقائية تم إنشاؤها للمهمة',
        type: TaskDocumentType.plan,
        content: defaultContent,
        isShared: true,
        createdBy: createdBy,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الخطة الافتراضية للمهمة ${task.id}: $e');
      return null;
    }
  }

  /// الحصول على إحصائيات المستندات للمهمة
  Future<TaskDocumentStats?> getTaskDocumentStatistics(int taskId) async {
    try {
      await _documentsController.loadTaskDocuments(taskId);
      final documents = _documentsController.taskDocuments;

      return TaskDocumentStats(
        totalDocuments: documents.length,
        reportCount: documents.where((d) => d.type == TaskDocumentType.report).length,
        analysisCount: documents.where((d) => d.type == TaskDocumentType.analysis).length,
        planCount: documents.where((d) => d.type == TaskDocumentType.plan).length,
        attachmentCount: documents.where((d) => d.type == TaskDocumentType.attachment).length,
        sharedDocuments: documents.where((d) => d.isShared).length,
        recentDocuments: documents.where((d) =>
          DateTime.fromMillisecondsSinceEpoch(d.createdAt * 1000)
            .isAfter(DateTime.now().subtract(const Duration(days: 7)))
        ).length,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المستندات للمهمة $taskId: $e');
      return null;
    }
  }

  /// البحث في مستندات المهمة
  Future<List<TaskDocument>> searchInTaskDocuments(
    int taskId,
    String query, {
    TaskDocumentType? type,
    bool? isShared,
  }) async {
    try {
      await _documentsController.loadTaskDocuments(taskId);
      final documents = _documentsController.taskDocuments;

      return documents.where((doc) {
        final matchesQuery = doc.archiveDocument?.title.toLowerCase().contains(query.toLowerCase()) == true ||
                           doc.description?.toLowerCase().contains(query.toLowerCase()) == true;
        final matchesType = type == null || doc.type == type;
        final matchesShared = isShared == null || doc.isShared == isShared;

        return matchesQuery && matchesType && matchesShared;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في مستندات المهمة $taskId: $e');
      return [];
    }
  }

  /// مشاركة جميع مستندات المهمة مع المساهمين
  Future<bool> shareAllDocumentsWithContributors(
    int taskId,
    List<int> contributorIds,
  ) async {
    try {
      final documents = _documentsController.taskDocuments
          .where((doc) => doc.taskId == taskId)
          .toList();

      bool allSuccess = true;
      for (final document in documents) {
        final success = await _documentsController.shareDocumentWithContributors(
          document,
          contributorIds,
        );
        if (!success) allSuccess = false;
      }

      return allSuccess;
    } catch (e) {
      debugPrint('خطأ في مشاركة مستندات المهمة $taskId: $e');
      return false;
    }
  }

  /// تصدير جميع مستندات المهمة
  Future<String?> exportAllTaskDocuments(
    int taskId, {
    String format = 'pdf',
  }) async {
    try {
      // TODO: تنفيذ تصدير المستندات
      debugPrint('تصدير مستندات المهمة $taskId بتنسيق $format');
      return 'exported_documents_$taskId.$format';
    } catch (e) {
      debugPrint('خطأ في تصدير مستندات المهمة $taskId: $e');
      return null;
    }
  }

  /// إنشاء محتوى تقرير افتراضي
  String _generateDefaultReportContent(Task task) {
    final now = DateTime.now();
    final content = {
      "ops": [
        {"insert": "تقرير المهمة: ${task.title}\n", "attributes": {"header": 1}},
        {"insert": "\n"},
        {"insert": "تاريخ الإنشاء: ${now.day}/${now.month}/${now.year}\n", "attributes": {"bold": true}},
        {"insert": "الحالة: ${task.status}\n", "attributes": {"bold": true}},
        {"insert": "الأولوية: ${task.priority}\n", "attributes": {"bold": true}},
        {"insert": "\n"},
        {"insert": "وصف المهمة:\n", "attributes": {"header": 2}},
        {"insert": "${task.description ?? 'لا يوجد وصف'}\n"},
        {"insert": "\n"},
        {"insert": "التقدم المحرز:\n", "attributes": {"header": 2}},
        {"insert": "• لم يتم البدء بعد\n"},
        {"insert": "• في انتظار التحديث\n"},
        {"insert": "\n"},
        {"insert": "الملاحظات:\n", "attributes": {"header": 2}},
        {"insert": "يرجى تحديث هذا التقرير بانتظام لتتبع تقدم المهمة.\n"},
      ]
    };
    
    return jsonEncode(content);
  }

  /// إنشاء محتوى خطة افتراضي
  String _generateDefaultPlanContent(Task task) {
    final content = {
      "ops": [
        {"insert": "خطة تنفيذ المهمة: ${task.title}\n", "attributes": {"header": 1}},
        {"insert": "\n"},
        {"insert": "الهدف:\n", "attributes": {"header": 2}},
        {"insert": "${task.description ?? 'تحديد الهدف من المهمة'}\n"},
        {"insert": "\n"},
        {"insert": "الخطوات المطلوبة:\n", "attributes": {"header": 2}},
        {"insert": "1. تحليل المتطلبات\n"},
        {"insert": "2. وضع الخطة التفصيلية\n"},
        {"insert": "3. تنفيذ المهمة\n"},
        {"insert": "4. المراجعة والتقييم\n"},
        {"insert": "\n"},
        {"insert": "الموارد المطلوبة:\n", "attributes": {"header": 2}},
        {"insert": "• الموارد البشرية\n"},
        {"insert": "• الموارد المالية\n"},
        {"insert": "• الموارد التقنية\n"},
        {"insert": "\n"},
        {"insert": "الجدول الزمني:\n", "attributes": {"header": 2}},
        {"insert": "تاريخ البداية: ${task.startDate != null ? DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000).toString().split(' ')[0] : 'غير محدد'}\n"},
        {"insert": "تاريخ الانتهاء: ${task.dueDate != null ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).toString().split(' ')[0] : 'غير محدد'}\n"},
      ]
    };
    
    return jsonEncode(content);
  }

  /// تنظيف الموارد
  @override
  void onClose() {
    debugPrint('تنظيف خدمة تكامل المستندات');
    super.onClose();
  }
}
