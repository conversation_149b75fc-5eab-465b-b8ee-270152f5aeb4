import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/api_config.dart';

/// أداة تشخيص API للمساعدة في حل مشاكل الاتصال
class ApiDiagnostics {
  static const String _baseUrl = ApiConfig.baseUrl;

  /// اختبار شامل لجميع نقاط النهاية
  static Future<Map<String, dynamic>> runFullDiagnostics() async {
    final results = <String, dynamic>{};
    
    debugPrint('🔍 بدء التشخيص الشامل للـ API...');
    
    // اختبار الاتصال الأساسي
    results['connectivity'] = await _testConnectivity();
    
    // اختبار نقاط النهاية المختلفة
    results['endpoints'] = await _testEndpoints();
    
    // اختبار قاعدة البيانات
    results['database'] = await _testDatabase();
    
    debugPrint('✅ انتهى التشخيص الشامل');
    return results;
  }

  /// اختبار الاتصال الأساسي
  static Future<Map<String, dynamic>> _testConnectivity() async {
    try {
      debugPrint('🌐 اختبار الاتصال الأساسي...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/api/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      return {
        'success': true,
        'statusCode': response.statusCode,
        'responseTime': DateTime.now().millisecondsSinceEpoch,
        'headers': response.headers,
      };
    } catch (e) {
      debugPrint('❌ فشل اختبار الاتصال: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// اختبار نقاط النهاية المختلفة
  static Future<Map<String, List<Map<String, dynamic>>>> _testEndpoints() async {
    final endpoints = {
      'users': '/api/Users',
      'tasks': '/api/Tasks',
      'departments': '/api/Departments',
      'taskStatuses': '/api/TaskStatuses',
      'taskPriorities': '/api/TaskPriorities',
      'taskTypes': '/api/TaskTypes',
    };

    final results = <String, List<Map<String, dynamic>>>{};

    for (final entry in endpoints.entries) {
      final name = entry.key;
      final endpoint = entry.value;
      
      debugPrint('🔍 اختبار نقطة النهاية: $name ($endpoint)');
      
      final testResults = <Map<String, dynamic>>[];
      
      // اختبار GET بدون معاملات
      testResults.add(await _testSingleEndpoint(endpoint, 'GET'));
      
      // اختبار GET مع معاملات pagination
      if (name == 'tasks' || name == 'users') {
        testResults.add(await _testSingleEndpoint('$endpoint?page=1&pageSize=5', 'GET'));
      }
      
      results[name] = testResults;
    }

    return results;
  }

  /// اختبار نقطة نهاية واحدة
  static Future<Map<String, dynamic>> _testSingleEndpoint(String endpoint, String method) async {
    final startTime = DateTime.now();
    
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      late http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(
            uri,
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(seconds: 15));
          break;
        default:
          throw UnsupportedError('Method $method not supported');
      }
      
      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;
      
      final result = {
        'endpoint': endpoint,
        'method': method,
        'success': response.statusCode >= 200 && response.statusCode < 300,
        'statusCode': response.statusCode,
        'responseTime': responseTime,
        'contentLength': response.body.length,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تحليل محتوى الاستجابة
      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          final data = jsonDecode(response.body);
          if (data is Map<String, dynamic>) {
            result['responseType'] = 'object';
            result['hasData'] = data.containsKey('data');
            if (data.containsKey('data') && data['data'] is List) {
              result['dataCount'] = (data['data'] as List).length;
            }
          } else if (data is List) {
            result['responseType'] = 'array';
            result['dataCount'] = data.length;
          }
        } catch (e) {
          result['parseError'] = e.toString();
        }
      } else {
        result['errorBody'] = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
      }

      debugPrint('${(result['success'] as bool? ?? false) ? '✅' : '❌'} $endpoint - ${result['statusCode']} (${responseTime}ms)');
      return result;
      
    } catch (e) {
      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;
      
      debugPrint('❌ $endpoint - خطأ: $e');
      return {
        'endpoint': endpoint,
        'method': method,
        'success': false,
        'error': e.toString(),
        'responseTime': responseTime,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// اختبار قاعدة البيانات
  static Future<Map<String, dynamic>> _testDatabase() async {
    try {
      debugPrint('🗄️ اختبار قاعدة البيانات...');
      
      // اختبار الاتصال بقاعدة البيانات من خلال endpoint خاص
      final response = await http.get(
        Uri.parse('$_baseUrl/api/database/test'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'connectionStatus': data['connectionStatus'] ?? 'unknown',
          'tablesCount': data['tablesCount'] ?? 0,
          'lastCheck': DateTime.now().toIso8601String(),
        };
      } else {
        return {
          'success': false,
          'statusCode': response.statusCode,
          'error': response.body,
        };
      }
    } catch (e) {
      debugPrint('❌ فشل اختبار قاعدة البيانات: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// طباعة تقرير التشخيص
  static void printDiagnosticsReport(Map<String, dynamic> results) {
    debugPrint('\n${'=' * 50}');
    debugPrint('📊 تقرير تشخيص API');
    debugPrint('=' * 50);
    
    // تقرير الاتصال
    final connectivity = results['connectivity'] as Map<String, dynamic>;
    debugPrint('\n🌐 الاتصال الأساسي:');
    debugPrint('   الحالة: ${(connectivity['success'] as bool? ?? false) ? '✅ متصل' : '❌ غير متصل'}');
    if ((connectivity['success'] as bool? ?? false)) {
      debugPrint('   رمز الحالة: ${connectivity['statusCode']}');
    } else {
      debugPrint('   الخطأ: ${connectivity['error']}');
    }
    
    // تقرير نقاط النهاية
    final endpoints = results['endpoints'] as Map<String, List<Map<String, dynamic>>>;
    debugPrint('\n🔗 نقاط النهاية:');
    
    for (final entry in endpoints.entries) {
      final name = entry.key;
      final tests = entry.value;
      
      debugPrint('\n   📍 $name:');
      for (final test in tests) {
        final status = (test['success'] as bool? ?? false) ? '✅' : '❌';
        final endpoint = test['endpoint'];
        final statusCode = test['statusCode'] ?? 'N/A';
        final responseTime = test['responseTime'] ?? 0;
        
        debugPrint('     $status $endpoint - $statusCode (${responseTime}ms)');
        
        if (test.containsKey('dataCount')) {
          debugPrint('       📊 عدد العناصر: ${test['dataCount']}');
        }
        
        if (!(test['success'] as bool? ?? false) && test.containsKey('error')) {
          debugPrint('       ❌ الخطأ: ${test['error']}');
        }
      }
    }
    
    // تقرير قاعدة البيانات
    final database = results['database'] as Map<String, dynamic>;
    debugPrint('\n🗄️ قاعدة البيانات:');
    debugPrint('   الحالة: ${(database['success'] as bool? ?? false) ? '✅ متصلة' : '❌ غير متصلة'}');
    if ((database['success'] as bool? ?? false)) {
      debugPrint('   حالة الاتصال: ${database['connectionStatus']}');
      debugPrint('   عدد الجداول: ${database['tablesCount']}');
    } else {
      debugPrint('   الخطأ: ${database['error']}');
    }
    
    debugPrint('\n${'=' * 50}');
    debugPrint('⏰ وقت التقرير: ${DateTime.now().toIso8601String()}');
    debugPrint('${'=' * 50}\n');
  }

  /// اختبار سريع للمهام فقط
  static Future<Map<String, dynamic>> quickTasksTest() async {
    debugPrint('⚡ اختبار سريع للمهام...');
    
    final results = <String, dynamic>{};
    
    // اختبار أساسي
    results['basic'] = await _testSingleEndpoint('/api/Tasks', 'GET');
    
    // اختبار مع pagination
    results['paginated'] = await _testSingleEndpoint('/api/Tasks?page=1&pageSize=5', 'GET');
    
    // اختبار مع فلاتر
    results['filtered'] = await _testSingleEndpoint('/api/Tasks?status=1', 'GET');
    
    return results;
  }
}
