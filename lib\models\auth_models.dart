import 'user_model.dart';

/// نموذج طلب تسجيل الدخول
class LoginRequest {
  final String usernameOrEmail;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.usernameOrEmail,
    required this.password,
    this.rememberMe = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'usernameOrEmail': usernameOrEmail,
      'password': password,
      'rememberMe': rememberMe,
    };
  }
}

/// نموذج طلب التسجيل
class RegisterRequest {
  final String name;
  final String email;
  final String? username;
  final String password;
  final String confirmPassword;
  final String? firstName;
  final String? lastName;
  final int? departmentId;
  final UserRole role;

  const RegisterRequest({
    required this.name,
    required this.email,
    this.username,
    required this.password,
    required this.confirmPassword,
    this.firstName,
    this.lastName,
    this.departmentId,
    this.role = UserRole.user,
  });

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'name': name,
      'email': email,
      'username': username,
      'password': password,
      'confirmPassword': confirmPassword,
      'firstName': firstName,
      'lastName': lastName,
      'departmentId': departmentId,
      'role': role.value,
    };
  }
}

/// نموذج طلب تحديث الرمز
class RefreshTokenRequest {
  final String refreshToken;

  const RefreshTokenRequest({
    required this.refreshToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'refreshToken': refreshToken,
    };
  }
}

/// نموذج طلب تغيير كلمة المرور
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }
}

/// نموذج طلب نسيان كلمة المرور
class ForgotPasswordRequest {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

/// نموذج طلب إعادة تعيين كلمة المرور
class ResetPasswordRequest {
  final String token;
  final String email;
  final String newPassword;
  final String confirmPassword;

  const ResetPasswordRequest({
    required this.token,
    required this.email,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'email': email,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }
}

/// نموذج استجابة المصادقة
class AuthResponse {
  final bool success;
  final String message;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final String tokenType;
  final UserInfo? user;

  const AuthResponse({
    required this.success,
    required this.message,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.tokenType = 'Bearer',
    this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    try {
      // Initial values from JSON
      bool serverSuccess = json['success'] as bool? ?? false;
      String serverMessage = json['message'] as String? ?? '';
      final accessToken = json['accessToken'] as String?;
      final refreshToken = json['refreshToken'] as String?;
      final expiresAt = json['expiresAt'] != null
          ? DateTime.tryParse(json['expiresAt'] as String)
          : null;
      final tokenType = json['tokenType'] as String? ?? 'Bearer';
      
      UserInfo? parsedUser;
      if (json['user'] != null && json['user'] is Map<String, dynamic>) {
        try {
          parsedUser = UserInfo.fromJson(json['user'] as Map<String, dynamic>);
        } catch (e) {
          print('خطأ في تحليل UserInfo داخل AuthResponse: $e. JSON user: ${json['user']}');
          // parsedUser remains null, error is logged
        }
      }

      // Determine final success state and message
      bool finalSuccess = serverSuccess;
      String finalMessage = serverMessage;

      if (serverSuccess && accessToken != null && parsedUser == null) {
        // Server indicated success and provided a token, but user data is missing or couldn't be parsed.
        // This is treated as a failure from the client's perspective for a login operation.
        print('AuthResponse: Server success true, token present, but UserInfo is null. Overriding to failure.');
        finalSuccess = false;
        // Provide a more specific error message if the original server message was generic or also indicated success.
        if (serverMessage.isEmpty || serverMessage.toLowerCase().contains('success') || serverMessage.toLowerCase().contains('successful')) {
          finalMessage = 'فشل في معالجة بيانات المستخدم المستلمة من الخادم.';
        } else {
          // Keep server's error message if it's already an error message
          finalMessage = serverMessage; 
        }
      }

      return AuthResponse(
        success: finalSuccess,
        message: finalMessage,
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresAt: expiresAt,
        tokenType: tokenType,
        user: parsedUser, // This will be null if parsing failed or json['user'] was null
      );
    } catch (e) {
      // This catch block is for errors in parsing AuthResponse itself, not UserInfo specifically (that's handled above)
      // or for other unexpected errors within this factory.
      print('خطأ فادح في تحليل AuthResponse: $e. JSON المستلم: $json');
      // Return a generic failure response to prevent app crash and provide some info
      return AuthResponse(
        success: false,
        message: 'خطأ جسيم في تحليل استجابة المصادقة: ${e.toString()}',
        user: null, // Ensure user is null on critical parsing failure
        accessToken: null, // Ensure token is null as well
        refreshToken: null,
        expiresAt: null
      );
      // Or rethrow if preferred: throw Exception('خطأ في تحليل AuthResponse: $e');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt?.toIso8601String(),
      'tokenType': tokenType,
      'user': user?.toJson(),
    };
  }

  /// التحقق من صحة الاستجابة ووجود الرموز
  bool get isValid => success && accessToken != null && user != null;

  /// التحقق من انتهاء صلاحية الرمز
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// الحصول على الرمز المميز (alias لـ accessToken)
  String get token => accessToken ?? '';
}

/// نموذج استجابة تسجيل الدخول
typedef LoginResponse = AuthResponse;

/// نموذج استجابة التسجيل
typedef RegisterResponse = AuthResponse;

/// نموذج استجابة تحديث الرمز المميز
class RefreshTokenResponse {
  final bool success;
  final String message;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final String tokenType;

  const RefreshTokenResponse({
    required this.success,
    required this.message,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.tokenType = 'Bearer',
  });

  factory RefreshTokenResponse.fromJson(Map<String, dynamic> json) {
    return RefreshTokenResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      tokenType: json['tokenType'] as String? ?? 'Bearer',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt?.toIso8601String(),
      'tokenType': tokenType,
    };
  }
}

/// نموذج بيانات الجلسة المحفوظة محلياً
class SessionData {
  final String accessToken;
  final String? refreshToken;
  final DateTime expiresAt;
  final UserInfo user;
  final DateTime savedAt;

  const SessionData({
    required this.accessToken,
    this.refreshToken,
    required this.expiresAt,
    required this.user,
    required this.savedAt,
  });

  factory SessionData.fromJson(Map<String, dynamic> json) {
    return SessionData(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      user: UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      savedAt: DateTime.parse(json['savedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
      'user': user.toJson(),
      'savedAt': savedAt.toIso8601String(),
    };
  }

  /// التحقق من انتهاء صلاحية الجلسة
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// التحقق من الحاجة لتحديث الرمز (قبل انتهاء الصلاحية بـ 5 دقائق)
  bool get needsRefresh {
    final refreshTime = expiresAt.subtract(const Duration(minutes: 5));
    return DateTime.now().isAfter(refreshTime);
  }
}
