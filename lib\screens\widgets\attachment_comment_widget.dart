import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/user_model.dart';

/// نموذج تعليق على مرفق
class AttachmentComment {
  final String id;
  final String attachmentId;
  final String userId;
  final String comment;
  final DateTime createdAt;

  AttachmentComment({
    required this.id,
    required this.attachmentId,
    required this.userId,
    required this.comment,
    required this.createdAt,
  });
}

/// مكون تعليقات المرفقات
/// 
/// TODO: تنفيذ مكون تعليقات المرفقات
/// - إضافة دعم إضافة تعليقات على المرفقات
/// - إضافة دعم عرض تعليقات المرفقات
/// - إضافة دعم حذف التعليقات
/// - إضافة دعم الإشارة إلى المستخدمين في التعليقات
class AttachmentCommentWidget extends StatefulWidget {
  /// معرف المرفق
  final String attachmentId;
  
  /// المستخدم الحالي
  final User currentUser;
  
  /// إنشاء مكون تعليقات المرفقات
  const AttachmentCommentWidget({
    super.key,
    required this.attachmentId,
    required this.currentUser,
  });

  @override
  State<AttachmentCommentWidget> createState() => _AttachmentCommentWidgetState();
}

class _AttachmentCommentWidgetState extends State<AttachmentCommentWidget> {
  final TextEditingController _commentController = TextEditingController();
  final List<AttachmentComment> _comments = [];
  final Map<String, User> _users = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  /// تحميل التعليقات
  Future<void> _loadComments() async {
    setState(() {
      _isLoading = true;
    });

    // TODO: تنفيذ تحميل التعليقات من قاعدة البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _isLoading = false;
    });
  }

  /// إضافة تعليق
  Future<void> _addComment() async {
    final comment = _commentController.text.trim();
    if (comment.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    // TODO: تنفيذ إضافة التعليق إلى قاعدة البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    _commentController.clear();
    setState(() {
      _isLoading = false;
    });

    // إعادة تحميل التعليقات
    await _loadComments();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Text(
          'التعليقات'.tr,
          style: AppStyles.titleMedium,
        ),
        const SizedBox(height: 8),

        // قائمة التعليقات
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(),
          )
        else if (_comments.isEmpty)
          Center(
            child: Text(
              'لا توجد تعليقات'.tr,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _comments.length,
            itemBuilder: (context, index) {
              final comment = _comments[index];
              final user = _users[comment.userId];
              return _buildCommentItem(comment, user);
            },
          ),

        const SizedBox(height: 16),

        // حقل إضافة تعليق
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _commentController,
                decoration: InputDecoration(
                  hintText: 'أضف تعليقاً...'.tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                maxLines: 3,
                minLines: 1,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addComment,
              icon: const Icon(Icons.send),
              color: AppColors.primary,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء عنصر تعليق
  Widget _buildCommentItem(AttachmentComment comment, User? user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم والتاريخ
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  user?.name ?? 'مستخدم غير معروف'.tr,
                  style: AppStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _formatDateTime(comment.createdAt),
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // نص التعليق
            Text(
              comment.comment,
              style: AppStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم'.tr;
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة'.tr;
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة'.tr;
    } else {
      return 'الآن'.tr;
    }
  }
}
