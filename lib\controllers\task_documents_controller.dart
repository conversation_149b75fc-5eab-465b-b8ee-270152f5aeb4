import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/controllers/task_controller.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../models/task_document_model.dart';
import '../services/api/task_documents_api_service.dart';

/// متحكم إدارة المستندات المرتبطة بالمهام
/// يوفر إدارة شاملة للمستندات مع التكامل الكامل مع نظام المهام
class TaskDocumentsController extends GetxController {
  final TaskDocumentsApiService _apiService = TaskDocumentsApiService();

  // البيانات الأساسية
  final RxList<TaskDocument> _taskDocuments = <TaskDocument>[].obs;
  final RxList<TaskDocument> _filteredDocuments = <TaskDocument>[].obs;
  final Rx<TaskDocument?> _currentDocument = Rx<TaskDocument?>(null);
  final Rx<TaskDocumentStats?> _stats = Rx<TaskDocumentStats?>(null);

  // المرشحات والبحث
  final RxString _searchQuery = ''.obs;
  final Rx<TaskDocumentType?> _selectedTypeFilter = Rx<TaskDocumentType?>(null);
  final RxBool _showSharedOnly = false.obs;
  final Rx<TaskDocumentPermission?> _selectedPermissionFilter = Rx<TaskDocumentPermission?>(null);

  // حالة التطبيق
  final RxInt _currentTaskId = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isCreating = false.obs;
  final RxBool _isUpdating = false.obs;
  final RxString _error = ''.obs;

  // Getters
  List<TaskDocument> get taskDocuments => _taskDocuments;
  List<TaskDocument> get filteredDocuments => _filteredDocuments;
  TaskDocument? get currentDocument => _currentDocument.value;
  TaskDocumentStats? get stats => _stats.value;
  String get searchQuery => _searchQuery.value;
  TaskDocumentType? get selectedTypeFilter => _selectedTypeFilter.value;
  bool get showSharedOnly => _showSharedOnly.value;
  TaskDocumentPermission? get selectedPermissionFilter => _selectedPermissionFilter.value;
  int get currentTaskId => _currentTaskId.value;
  bool get isLoading => _isLoading.value;
  bool get isCreating => _isCreating.value;
  bool get isUpdating => _isUpdating.value;
  String get error => _error.value;

  // دوال مساعدة لإدارة الحالة
  void setLoading(bool value) => _isLoading.value = value;
  void setError(String value) => _error.value = value;

  /// تحميل مستندات مهمة محددة
  Future<void> loadTaskDocuments(int taskId) async {
    try {
      setLoading(true);
      _currentTaskId.value = taskId;

      final documents = await _apiService.getTaskDocuments(taskId);
      _taskDocuments.assignAll(documents);
      _applyFilters();

      // تحميل الإحصائيات
      await _loadStats(taskId);

      debugPrint('تم تحميل ${documents.length} مستند للمهمة $taskId');
    } catch (e) {
      setError('خطأ في تحميل مستندات المهمة: $e');
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
    } finally {
      setLoading(false);
    }
  }

  /// تحميل إحصائيات المستندات
  Future<void> _loadStats(int taskId) async {
    try {
      final stats = await _apiService.getTaskDocumentStats(taskId);
      _stats.value = stats;
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المستندات: $e');
    }
  }

  /// إنشاء مستند جديد مرتبط بمهمة
  Future<TaskDocument?> createTaskDocument({
    required int taskId,
    required String title,
    String? description,
    required TaskDocumentType type,
    String? content,
    bool isShared = false,
    required int createdBy,
  }) async {
    try {
      debugPrint('🚀 بدء إنشاء مستند المهمة في Controller');
      debugPrint('📋 المعاملات: TaskId=$taskId, Title=$title, Type=${type.value}, CreatedBy=$createdBy');

      _isCreating.value = true;
      setError('');

      final request = CreateTaskDocumentRequest(
        taskId: taskId,
        title: title,
        description: description,
        type: type,
        content: content,
        isShared: isShared,
        createdBy: createdBy,
      );

      debugPrint('📤 إرسال طلب إنشاء المستند إلى API Service');
      final document = await _apiService.createTaskDocument(request);

      if (document != null) {
        debugPrint('✅ تم إنشاء المستند بنجاح في API Service');
        debugPrint('📄 تفاصيل المستند: TaskId=${document.taskId}, ArchiveDocumentId=${document.archiveDocumentId}');

        _taskDocuments.add(document);
        _applyFilters();
        await _loadStats(taskId);

        // تحديث تلقائي للتقدم عند إنشاء مستند
        try {
          if (Get.isRegistered<TaskController>()) {
            final taskController = Get.find<TaskController>();

            debugPrint('🔄 بدء التحديث التلقائي للتقدم عند إنشاء المستند...');
            final progressUpdated = await taskController.autoUpdateTaskProgress(
              taskId,
              createdBy,
              'document',
              actionDescription: 'تم إنشاء مستند: $title (${type.displayName})',
            );

            if (progressUpdated) {
              debugPrint('✅ تم تحديث التقدم بنجاح عند إنشاء المستند');

              // تحديث إضافي للواجهة لضمان ظهور التغييرات
              taskController.update(['task_details', 'task_overview', 'task_progress']);
              taskController.refresh();
            } else {
              debugPrint('⚠️ لم يتم تحديث التقدم عند إنشاء المستند');
            }
          }
        } catch (e) {
          debugPrint('⚠️ خطأ في التحديث التلقائي للتقدم عند إنشاء المستند: $e');
        }

        debugPrint('✅ تم إضافة المستند إلى القائمة المحلية وتحديث الإحصائيات');
        return document;
      } else {
        debugPrint('❌ API Service أرجع null');
        setError('فشل في إنشاء المستند - لم يتم إرجاع بيانات');
      }

      return null;
    } catch (e) {
      debugPrint('💥 خطأ في إنشاء مستند المهمة في Controller: $e');
      setError('خطأ في إنشاء المستند: $e');
      return null;
    } finally {
      _isCreating.value = false;
      debugPrint('🏁 انتهاء عملية إنشاء المستند في Controller');
    }
  }

  /// تحديث مستند مرتبط بمهمة
  Future<bool> updateTaskDocument({
    required int taskId,
    required int archiveDocumentId,
    String? title,
    String? description,
    String? content,
    TaskDocumentType? type,
    bool? isShared,
    TaskDocumentPermission? permission,
    required int updatedBy,
  }) async {
    try {
      _isUpdating.value = true;
      setError('');

      final request = UpdateTaskDocumentRequest(
        taskId: taskId,
        archiveDocumentId: archiveDocumentId,
        title: title,
        description: description,
        content: content,
        type: type,
        isShared: isShared,
        permission: permission,
        updatedBy: updatedBy,
      );

      final updatedDocument = await _apiService.updateTaskDocument(request);
      
      if (updatedDocument != null) {
        final index = _taskDocuments.indexWhere((doc) =>
            doc.taskId == taskId && doc.archiveDocumentId == archiveDocumentId);
        if (index != -1) {
          _taskDocuments[index] = updatedDocument;
          _applyFilters();
        }

        if (_currentDocument.value?.taskId == taskId &&
            _currentDocument.value?.archiveDocumentId == archiveDocumentId) {
          _currentDocument.value = updatedDocument;
        }

        debugPrint('تم تحديث مستند المهمة بنجاح: $taskId-$archiveDocumentId');
        return true;
      }
      
      return false;
    } catch (e) {
      setError('خطأ في تحديث المستند: $e');
      debugPrint('خطأ في تحديث مستند المهمة $taskId-$archiveDocumentId: $e');
      return false;
    } finally {
      _isUpdating.value = false;
    }
  }

  /// حذف مستند مرتبط بمهمة
  Future<bool> deleteTaskDocument(TaskDocument document) async {
    try {
      setLoading(true);
      setError('');

      final success = await _apiService.deleteTaskDocument(document.taskId, document.archiveDocumentId);

      if (success) {
        _taskDocuments.removeWhere((doc) => doc.id == document.id);
        _applyFilters();

        if (_currentDocument.value?.id == document.id) {
          _currentDocument.value = null;
        }

        await _loadStats(_currentTaskId.value);
        debugPrint('تم حذف مستند المهمة بنجاح: ${document.id}');
        return true;
      }

      return false;
    } catch (e) {
      setError('خطأ في حذف المستند: $e');
      debugPrint('خطأ في حذف مستند المهمة ${document.id}: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// رفع مستند من ملف
  Future<TaskDocument?> uploadTaskDocument({
    required int taskId,
    required File file,
    required String title,
    required TaskDocumentType type,
    String? description,
    bool isShared = false,
    required int createdBy,
  }) async {
    try {
      setLoading(true);
      setError('');

      final document = await _apiService.uploadTaskDocument(
        taskId: taskId,
        file: file,
        title: title,
        type: type,
        description: description,
        isShared: isShared,
        createdBy: createdBy,
      );
      
      if (document != null) {
        _taskDocuments.add(document);
        _applyFilters();
        await _loadStats(taskId);

        // تحديث تلقائي للتقدم عند رفع مستند
        try {
          if (Get.isRegistered<TaskController>()) {
            final taskController = Get.find<TaskController>();
            await taskController.autoUpdateTaskProgress(
              taskId,
              createdBy,
              'document',
              actionDescription: 'تم رفع مستند: $title (${type.displayName})',
            );
          }
        } catch (e) {
          debugPrint('⚠️ خطأ في التحديث التلقائي للتقدم عند رفع المستند: $e');
        }

        debugPrint('تم رفع مستند المهمة بنجاح: ${document.id}');
        return document;
      }
      
      return null;
    } catch (e) {
      setError('خطأ في رفع المستند: $e');
      debugPrint('خطأ في رفع مستند المهمة: $e');
      return null;
    } finally {
      setLoading(false);
    }
  }

  /// مشاركة مستند مع مساهمين
  Future<bool> shareDocumentWithContributors(
    TaskDocument document,
    List<int> contributorIds,
  ) async {
    try {
      setLoading(true);
      setError('');

      final success = await _apiService.shareDocumentWithContributors(
        document.archiveDocumentId,
        contributorIds,
      );

      if (success) {
        // تحديث حالة المستند محلياً
        final index = _taskDocuments.indexWhere((doc) => doc.id == document.id);
        if (index != -1) {
          _taskDocuments[index] = _taskDocuments[index].copyWith(isShared: true);
          _applyFilters();
        }

        debugPrint('تم مشاركة المستند بنجاح: ${document.id}');
        return true;
      }

      return false;
    } catch (e) {
      setError('خطأ في مشاركة المستند: $e');
      debugPrint('خطأ في مشاركة المستند ${document.id}: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// البحث في المستندات
  Future<void> searchDocuments(String query) async {
    try {
      _searchQuery.value = query;
      
      if (query.isEmpty) {
        _applyFilters();
        return;
      }

      setLoading(true);
      
      final filter = TaskDocumentFilter(
        taskId: _currentTaskId.value,
        type: _selectedTypeFilter.value,
        isShared: _showSharedOnly.value ? true : null,
        permission: _selectedPermissionFilter.value,
        searchQuery: query,
      );

      final results = await _apiService.searchTaskDocuments(query, filter: filter);
      _filteredDocuments.assignAll(results);
      
      debugPrint('تم العثور على ${results.length} نتيجة للبحث: $query');
    } catch (e) {
      setError('خطأ في البحث: $e');
      debugPrint('خطأ في البحث عن المستندات: $e');
    } finally {
      setLoading(false);
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = List<TaskDocument>.from(_taskDocuments);

    // تطبيق مرشح النوع
    if (_selectedTypeFilter.value != null) {
      filtered = filtered.where((doc) => doc.type == _selectedTypeFilter.value).toList();
    }

    // تطبيق مرشح المشاركة
    if (_showSharedOnly.value) {
      filtered = filtered.where((doc) => doc.isShared).toList();
    }

    // تطبيق مرشح الصلاحيات
    if (_selectedPermissionFilter.value != null) {
      filtered = filtered.where((doc) => doc.permission == _selectedPermissionFilter.value).toList();
    }

    // تطبيق مرشح البحث المحلي
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((doc) =>
          doc.archiveDocument?.title.toLowerCase().contains(_searchQuery.value.toLowerCase()) == true ||
          doc.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) == true
      ).toList();
    }

    _filteredDocuments.assignAll(filtered);
  }

  /// تعيين مرشح النوع
  void setTypeFilter(TaskDocumentType? type) {
    _selectedTypeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح المشاركة
  void setSharedFilter(bool showSharedOnly) {
    _showSharedOnly.value = showSharedOnly;
    _applyFilters();
  }

  /// تعيين مرشح الصلاحيات
  void setPermissionFilter(TaskDocumentPermission? permission) {
    _selectedPermissionFilter.value = permission;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _selectedTypeFilter.value = null;
    _showSharedOnly.value = false;
    _selectedPermissionFilter.value = null;
    _applyFilters();
  }

  /// تحديد المستند الحالي
  void setCurrentDocument(TaskDocument? document) {
    _currentDocument.value = document;
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    if (_currentTaskId.value > 0) {
      await loadTaskDocuments(_currentTaskId.value);
    }
  }
}
