import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/chat_group_models.dart';
import '../../utils/date_formatter.dart';
import '../../constants/app_colors.dart';
import '../../controllers/chat_groups_controller.dart';
import '../../routes/app_routes.dart';
import '../../services/unified_permission_service.dart';

class GroupsScreen extends StatefulWidget {
  const GroupsScreen({super.key});

  @override
  State<GroupsScreen> createState() => _GroupsScreenState();
}

class _GroupsScreenState extends State<GroupsScreen> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final ChatGroupsController _chatGroupsController = Get.find<ChatGroupsController>();

  @override
  void initState() {
    super.initState();
    // تحميل المجموعات
    _chatGroupsController.loadAllGroups();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات'),
        actions: [
          Obx(() => _chatGroupsController.isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.wifi, color: Colors.green)),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _chatGroupsController.loadAllGroups();
            },
          ),
        ],
      ),
      body: Obx(() {
        final groups = _chatGroupsController.allGroups;

        if (_chatGroupsController.isLoading && groups.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (groups.isEmpty) {
          return const Center(
            child: Text('لا توجد محادثات بعد'),
          );
        }

        return ListView.builder(
          itemCount: groups.length,
          itemBuilder: (context, index) {
            return _buildGroupItem(groups[index]);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _showNewChatDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildGroupItem(ChatGroup group) {
    // الحصول على عدد الرسائل غير المقروءة
    final unreadCount = 0; // يمكن تنفيذ هذا لاحقًا

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary,
        child: Text(
          group.name.isNotEmpty ? group.name[0] : '?',
          style: const TextStyle(color: Colors.white),
        ),
      ),
      title: Text(
        group.name,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Text(
        group.description ?? 'لا توجد رسائل بعد',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            group.updatedAt != null
                ? DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(group.updatedAt! * 1000))
                : '',
            style: const TextStyle(fontSize: 12),
          ),
          if (unreadCount > 0)
            Container(
              padding: const EdgeInsets.all(6),
              decoration: const BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: Text(
                unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                ),
              ),
            ),
        ],
      ),
      onTap: () {
        // فتح شاشة الدردشة الموحدة
        Get.toNamed(
          AppRoutes.unifiedChatDetail,
          arguments: {'chatGroup': group},
        );
      },
    );
  }

  void _showNewChatDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('محادثة جديدة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_permissionService.canAccessChat())
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // الانتقال إلى شاشة إنشاء محادثة مباشرة
                    Get.toNamed(AppRoutes.unifiedChatList);
                  },
                  child: const Text('محادثة مباشرة'),
                ),
              const SizedBox(height: 8),
              if (_permissionService.canAccessChat())
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // الانتقال إلى شاشة إنشاء مجموعة
                    Get.toNamed(AppRoutes.createGroupChat);
                  },
                  child: const Text('مجموعة جديدة'),
                ),
            ],
          ),
        );
      },
    );
  }
}
