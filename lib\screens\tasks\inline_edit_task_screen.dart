import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_status_enum.dart' as task_status_enum;
import 'package:flutter_application_2/models/task_type_models.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/task_type_controller.dart';
import '../../controllers/user_controller.dart';
import '../../services/unified_permission_service.dart';
import '../widgets/inline_edit_field.dart';

import '../../utils/date_formatter.dart';

class InlineEditTaskScreen extends StatefulWidget {
  final String taskId;

  const InlineEditTaskScreen({
    super.key,
    required this.taskId,
  });

  @override
  State<InlineEditTaskScreen> createState() => _InlineEditTaskScreenState();
}

class _InlineEditTaskScreenState extends State<InlineEditTaskScreen> {
  bool _isLoading = true;
  Task? _task;
  String? _errorMessage;
  List<TaskType> _taskTypes = [];
  List<Map<String, dynamic>> _taskUsers = [];
  Map<int, int> _userContributions = {};

  // خدمة الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();

    // التحقق من وجود متحكم أنواع المهام أو إنشاؤه
    if (!Get.isRegistered<TaskTypeController>()) {
      Get.put(TaskTypeController());
    }

    // التحقق من وجود متحكم المستخدمين أو إنشاؤه
    if (!Get.isRegistered<UserController>()) {
      Get.put(UserController());
    }

    _loadTask();
    _loadTaskTypes();
  }

  /// تحميل أنواع المهام
  Future<void> _loadTaskTypes() async {
    try {
      final taskTypeController = Get.find<TaskTypeController>();
      await taskTypeController.loadTaskTypes();

      if (mounted) {
        setState(() {
          _taskTypes = taskTypeController.taskTypes;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع المهام: $e');
      // استمر حتى لو فشل تحميل أنواع المهام
    }
  }

  Future<void> _loadTask() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final taskController = Get.find<TaskController>();
      await taskController.loadTaskDetails(widget.taskId);

      // تحميل المهمة
      setState(() {
        _task = taskController.currentTask;
      });

      // تحميل المستخدمين المعنيين والمكلفين
      await _loadTaskUsers();

      // تحميل مساهمات المستخدمين
      await _loadUserContributions();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'فشل تحميل المهمة: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  /// تحميل المستخدمين المعنيين والمكلفين
  Future<void> _loadTaskUsers() async {
    if (_task == null) return;

    try {
      final userController = Get.find<UserController>();
      final List<Map<String, dynamic>> users = [];

      // إضافة منشئ المهمة
      final creator = userController.getUserById(_task!.creatorId);
      if (creator != null) {
        users.add({
          'user': creator,
          'role': 'منشئ المهمة',
          'icon': Icons.person_add,
        });
      }

      // إضافة المسند إليه المهمة (إن وجد)
      if (_task!.assigneeId != null) {
        final assignee = userController.getUserById(_task!.assigneeId!);
        if (assignee != null) {
          users.add({
            'user': assignee,
            'role': 'مكلف بالمهمة',
            'icon': Icons.assignment_ind,
          });
        }
      }

      // إضافة المستخدمين الذين يمكنهم الوصول للمهمة
      if (_task!.accessUserIds != null) {
        for (final userId in _task!.accessUserIds!) {
          // تجاهل المستخدمين الذين تمت إضافتهم بالفعل
          if (userId == _task!.creatorId.toString() || userId == _task!.assigneeId?.toString()) continue;

          final user = userController.getUserById(int.parse(userId));
          if (user != null) {
            users.add({
              'user': user,
              'role': 'يمكنه الوصول',
              'icon': Icons.visibility,
            });
          }
        }
      }

      setState(() {
        _taskUsers = users;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين المعنيين: $e');
    }
  }

  /// تحميل مساهمات المستخدمين
  Future<void> _loadUserContributions() async {
    if (_task == null) return;

    try {
      // تحميل مساهمات المستخدمين - استخدام بيانات وهمية في الوقت الحالي
      // TODO: تنفيذ تحميل مساهمات المستخدمين الفعلية عندما تصبح متاحة
      final Map<int, int> contributions = {};

      // إضافة بيانات وهمية للمنشئ والمسند إليه
      contributions[_task!.creatorId] = 1;

      if (_task!.assigneeId != null) {
        contributions[_task!.assigneeId!] = (contributions[_task!.assigneeId!] ?? 0) + 1;
      }

      setState(() {
        _userContributions = contributions;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل مساهمات المستخدمين: $e');
    }
  }

  Future<void> _updateTaskField(String field, dynamic value) async {
    if (_task == null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final taskController = Get.find<TaskController>();
      final authController = Get.find<AuthController>();

      // Create updated task based on the field
      Task updatedTask;

      switch (field) {
        case 'title':
          updatedTask = _task!.copyWith(title: value);
          break;
        case 'description':
          updatedTask = _task!.copyWith(description: value);
          break;
        case 'priority':
          updatedTask = _task!.copyWith(priority: value as String);
          break;
        case 'status':
          updatedTask = _task!.copyWith(status: value as String);
          break;
        case 'dueDate':
          final dueDateTimestamp = value is DateTime ? value.millisecondsSinceEpoch ~/ 1000 : value;
          updatedTask = _task!.copyWith(dueDate: dueDateTimestamp);
          break;
        case 'completionPercentage':
          updatedTask = _task!.copyWith(completionPercentage: value.toInt());
          break;
        case 'taskTypeId':
          updatedTask = _task!.copyWith(taskTypeId: value);
          break;
        // الحقول الجديدة - New fields
        case 'incoming':
          updatedTask = _task!.copyWith(incoming: value as String?);
          break;
        case 'note':
          updatedTask = _task!.copyWith(note: value as String?);
          break;
        default:
          setState(() {
            _isLoading = false;
            _errorMessage = 'حقل غير معروف: $field';
          });
          return;
      }

      // Update the task
      final success = await _updateTask(updatedTask);

      if (success) {
        // If status was changed, also record the status change
        if (field == 'status') {
          await taskController.updateTaskStatus(
            _task!.id,
            authController.currentUser.value!.id,
            value as String,
          );
        }

        // If progress was changed, also record the progress change
        if (field == 'completionPercentage') {
          // استخدام updateTaskProgress بدلاً من updateTaskStatus لتحديث نسبة الإكمال
          await taskController.updateTaskProgress(
            _task!.id,
            authController.currentUser.value!.id,
            value,
            notes: 'تم تحديث نسبة الإكمال إلى ${value.toInt()}% عبر المحرر المباشر',
          );
        }

        setState(() {
          _task = taskController.currentTask;
          _isLoading = false;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث المهمة بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = taskController.error.isEmpty ? 'فشل تحديث المهمة' : taskController.error;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'خطأ في تحديث المهمة: ${e.toString()}';
      });
    }
  }

  Future<void> _selectDueDate() async {
    if (_task == null) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _task!.dueDate != null
          ? DateTime.fromMillisecondsSinceEpoch(_task!.dueDate! * 1000)
          : DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (picked != null) {
      _updateTaskField('dueDate', picked);
    }
  }

  // Helper method to update a task using the TaskController
  Future<bool> _updateTask(Task task) async {
    try {
      final taskController = Get.find<TaskController>();

      // استخدام updateTask من TaskController مباشرة
      final success = await taskController.updateTask(
        id: task.id,
        title: task.title,
        description: task.description,
        taskTypeId: task.taskTypeId,
        assigneeId: task.assigneeId,
        departmentId: task.departmentId,
        startDate: task.startDate != null ? DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000) : null,
        dueDate: task.dueDate != null ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000) : null,
        completedAt: task.completedAt != null ? DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000) : null,
        status: task.status,
        priority: task.priority,
        completionPercentage: task.completionPercentage,
        estimatedTime: task.estimatedTime,
        actualTime: task.actualTime,
      );

      if (success) {
        // تحديث المهمة الحالية في وحدة التحكم
        await taskController.loadTaskDetails(task.id.toString());
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating task: $e');
      return false;
    }
  }

  /// بناء حقل اختيار نوع المهمة
  Widget _buildTaskTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.category,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              'نوع المهمة',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int?>(
              isExpanded: true,
              value: _task!.taskTypeId,
              hint: const Text('اختر نوع المهمة (اختياري)'),
              items: [
                const DropdownMenuItem<int?>(
                  value: null,
                  child: Text('بدون نوع'),
                ),
                ..._taskTypes.map((taskType) {
                  return DropdownMenuItem<int?>(
                    value: taskType.id,
                    child: Row(
                      children: [
                        if (taskType.color != null)
                          Container(
                            width: 16,
                            height: 16,
                            margin: const EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              color: _parseColor(taskType.color!),
                              shape: BoxShape.circle,
                            ),
                          ),
                        Text(taskType.name),
                      ],
                    ),
                  );
                }),
              ],
              onChanged: (value) {
                _updateTaskField('taskTypeId', value);
              },
            ),
          ),
        ),
      ],
    );
  }

  /// تحويل قيمة اللون النصية إلى لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return AppColors.primary;
    } catch (e) {
      return AppColors.statusPending;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل المهمة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadTask,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadTask,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : _task == null
                  ? const Center(child: Text('المهمة غير موجودة'))
                  : _buildTaskEditForm(),
    );
  }

  Widget _buildTaskEditForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          InlineEditField(
            initialValue: _task!.title,
            label: 'العنوان',
            icon: Icons.title,
            onSaved: (value) => _updateTaskField('title', value),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'لا يمكن أن يكون العنوان فارغاً';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // الوصف
          InlineEditField(
            initialValue: _task!.description ?? '',
            label: 'الوصف',
            icon: Icons.description,
            onSaved: (value) => _updateTaskField('description', value),
            multiline: true,
          ),
          const SizedBox(height: 16),

          // الحقول الجديدة - New fields
          // الوارد - Incoming
          InlineEditField(
            initialValue: _task!.incoming ?? '',
            label: 'الوارد',
            icon: Icons.input,
            onSaved: (value) => _updateTaskField('incoming', value.isEmpty ? null : value),
            multiline: true,
            maxLines: 2,
            hintText: 'معلومات إضافية حول مصدر المهمة أو الطلب الوارد',
          ),
          const SizedBox(height: 16),

          // الملاحظات - Note
          InlineEditField(
            initialValue: _task!.note ?? '',
            label: 'الملاحظات',
            icon: Icons.note,
            onSaved: (value) => _updateTaskField('note', value.isEmpty ? null : value),
            multiline: true,
            maxLines: 3,
            hintText: 'ملاحظات إضافية حول المهمة',
          ),
          const SizedBox(height: 16),

          // Priority
          _buildPrioritySelector(),
          const SizedBox(height: 16),

          // Status
          _buildStatusSelector(),
          const SizedBox(height: 16),

          // Due Date
          _buildDueDateSelector(),
          const SizedBox(height: 16),

          // Completion Percentage
          _buildCompletionPercentageSlider(),
          const SizedBox(height: 16),

          // Task Type
          _buildTaskTypeSelector(),
          const SizedBox(height: 24),



          // المستخدمين المعنيين والمكلفين
          _buildTaskUsersSection(),
          const SizedBox(height: 24),

          // Task Info
          _buildTaskInfo(),
        ],
      ),
    );
  }

  Widget _buildPrioritySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.priority_high,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              'الأولوية',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: task_status_enum.TaskPriority.values.map((priority) {
            final isSelected = _task!.priority == priority.name;
            final color = AppColors.getTaskPriorityColor(priority.name);

            return Expanded(
              child: GestureDetector(
                onTap: () => _updateTaskField('priority', priority.name),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? color : _getPriorityBackgroundColor(priority.level, Get.isDarkMode),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isSelected ? color : Colors.transparent,
                      width: 2,
                    ),
                  ),
                  child: Text(
                    _getPriorityText(priority.level),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSelected ? Colors.white : color,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              'الحالة',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: task_status_enum.TaskStatus.values.map((status) {
            final isSelected = _task!.status == status.name;
            final color = AppColors.getTaskStatusColor(status.name);

            return GestureDetector(
              onTap: () => _updateTaskField('status', status.name),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? color : _getStatusBackgroundColor(status.name, Get.isDarkMode),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected ? color : Colors.transparent,
                    width: 2,
                  ),
                ),
                child: Text(
                  status.displayNameAr,
                  style: TextStyle(
                    color: isSelected ? Colors.white : color,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                )  ),
              );
            }).toList(),
        ),
      ],
    );
  }

  Widget _buildDueDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              'تاريخ الاستحقاق',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDueDate,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColors.getBorderColor()),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _task!.dueDate != null
                        ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(_task!.dueDate! * 1000))
                        : 'لم يتم تحديد تاريخ الاستحقاق',
                    style: _task!.dueDate == null
                        ? AppStyles.bodyMedium.copyWith(
                            color: AppColors.textHint,
                            fontStyle: FontStyle.italic,
                          )
                        : AppStyles.bodyMedium,
                  ),
                ),
                Icon(
                  _task!.dueDate != null ? Icons.edit : Icons.add,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletionPercentageSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان ونسبة الإكمال
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.percent,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'نسبة الإكمال',
                  style: AppStyles.labelMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                // زر تقليل النسبة
                GestureDetector(
                  onTap: () {
                    double newValue = _task!.completionPercentage.toDouble() - 5;
                    if (newValue < 0) newValue = 0;

                    setState(() {
                      _task = _task!.copyWith(completionPercentage: newValue.toInt());
                    });

                    _updateTaskField('completionPercentage', newValue);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.remove,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),

                // عرض النسبة الحالية
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    '${_task!.completionPercentage.toInt()}%',
                    style: AppStyles.titleSmall.copyWith(
                      color: AppColors.getTaskStatusColor(_task!.status),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // زر زيادة النسبة
                GestureDetector(
                  onTap: () {
                    double newValue = _task!.completionPercentage.toDouble() + 5;
                    if (newValue > 100) newValue = 100;

                    setState(() {
                      _task = _task!.copyWith(completionPercentage: newValue.toInt());
                    });

                    _updateTaskField('completionPercentage', newValue);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 16),

        // شريط التمرير
        SliderTheme(
          data: SliderThemeData(
            activeTrackColor: AppColors.getTaskStatusColor(_task!.status),
            inactiveTrackColor: _getStatusBackgroundColor(_task!.status, Get.isDarkMode),
            thumbColor: AppColors.getTaskStatusColor(_task!.status),
            overlayColor: Get.isDarkMode
                ? _getOverlayColor(_task!.status, 0.2)
                : _getOverlayColor(_task!.status, 0.1),
            trackHeight: 8,
          ),
          child: Slider(
            value: _task!.completionPercentage.toDouble(),
            min: 0,
            max: 100,
            divisions: 20,
            onChanged: (value) {
              setState(() {
                // Update locally for immediate feedback
                _task = _task!.copyWith(completionPercentage: value.toInt());
              });
            },
            onChangeEnd: (value) {
              // Update on server when user stops dragging
              _updateTaskField('completionPercentage', value);
            },
          ),
        ),

        // أزرار قيم سريعة
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildQuickValueButton('0%', 0),
            _buildQuickValueButton('25%', 25),
            _buildQuickValueButton('50%', 50),
            _buildQuickValueButton('75%', 75),
            _buildQuickValueButton('100%', 100),
          ],
        ),
      ],
    );
  }

  // زر قيمة سريعة
  Widget _buildQuickValueButton(String label, double value) {
    final isSelected = (_task!.completionPercentage.round() == value.round());

    return GestureDetector(
      onTap: () {
        setState(() {
          _task = _task!.copyWith(completionPercentage: value.toInt());
        });
        _updateTaskField('completionPercentage', value);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade400,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildTaskInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المهمة',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 8),
            FutureBuilder<String>(
              future: _getUserName(_task!.creatorId.toString()),
              builder: (context, snapshot) {
                return _buildInfoRow(
                  'المنشئ',
                  snapshot.connectionState == ConnectionState.waiting
                      ? 'جاري التحميل...'
                      : snapshot.hasData
                          ? snapshot.data!
                          : 'غير معروف',
                  Icons.person_outline,
                );
              },
            ),
            const SizedBox(height: 8),
            FutureBuilder<String>(
              future: _task!.assigneeId != null ? _getUserName(_task!.assigneeId!.toString()) : Future.value('غير مسند'),
              builder: (context, snapshot) {
                return _buildInfoRow(
                  'المسند إليه',
                  snapshot.connectionState == ConnectionState.waiting
                      ? 'جاري التحميل...'
                      : snapshot.hasData
                          ? snapshot.data!
                          : 'غير مسند',
                  Icons.assignment_ind_outlined,
                );
              },
            ),
            const SizedBox(height: 8),
            FutureBuilder<String>(
              future: _getDepartmentName(_task!.departmentId?.toString() ?? ''),
              builder: (context, snapshot) {
                return _buildInfoRow(
                  'الإدارة',
                  snapshot.connectionState == ConnectionState.waiting
                      ? 'جاري التحميل...'
                      : snapshot.hasData
                          ? snapshot.data!
                          : 'غير معروف',
                  Icons.business_outlined,
                );
              },
            ),
            const SizedBox(height: 8),
            _buildInfoRow('تاريخ الإنشاء', DateFormatter.formatDateTime(DateTime.fromMillisecondsSinceEpoch(_task!.createdAt * 1000)), Icons.calendar_today),
            if (_task!.startDate != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow('تاريخ البدء', DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(_task!.startDate! * 1000)), Icons.play_circle_outline),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, [IconData? icon]) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 4),
              ],
              Expanded(
                child: Text(
                  label,
                  style: AppStyles.labelMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppStyles.bodyMedium,
          ),
        ),
      ],
    );
  }

  /// الحصول على اسم المستخدم من معرفه
  Future<String> _getUserName(String userId) async {
    try {
      final userController = Get.find<UserController>();
      final userName = await userController.getUserNameById(userId);
      return userName.isNotEmpty ? userName : userId;
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم المستخدم: $e');
      return userId;
    }
  }

  /// بناء قسم المستخدمين المعنيين والمكلفين
  Widget _buildTaskUsersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المستخدمين المعنيين والمكلفين',
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 8),

            // عرض المستخدمين المعنيين والمكلفين
            if (_taskUsers.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'لا يوجد مستخدمين معنيين بالمهمة',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              Column(
                children: _taskUsers.map((userInfo) {
                  final user = userInfo['user'] as User;
                  final role = userInfo['role'] as String;
                  final icon = userInfo['icon'] as IconData;
                  final contributionCount = _userContributions[user.id] ?? 0;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: Colors.grey.shade100,
                          child: Icon(
                            icon,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.name,
                                style: AppStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                role,
                                style: AppStyles.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (contributionCount > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$contributionCount مساهمة',
                              style: AppStyles.bodySmall.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  /// الحصول على اسم الإدارة من معرفها
  Future<String> _getDepartmentName(String departmentId) async {
    try {
      if (departmentId.isEmpty) return 'غير محدد';

      // TODO: استخدام DepartmentController للحصول على اسم الإدارة
      // في الوقت الحالي، سنعيد قيمة افتراضية
      return 'إدارة $departmentId';
    } catch (e) {
      debugPrint('خطأ في الحصول على اسم الإدارة: $e');
      return 'إدارة غير معروفة';
    }
  }



  String _getPriorityText(int priorityLevel) {
    switch (priorityLevel) {
      case 1:
        return 'منخفضة';
      case 2:
        return 'متوسطة';
      case 3:
        return 'عالية';
      case 4:
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }

  /// الحصول على لون خلفية الأولوية مع دعم الوضع الداكن
  Color _getPriorityBackgroundColor(int priorityLevel, bool isDark) {
    switch (priorityLevel) {
      case 1: // low
        return isDark ? const Color(0xFF1B5E20) : const Color(0xFFE8F5E9);
      case 2: // medium
        return isDark ? const Color(0xFF795548) : const Color(0xFFFFECB3);
      case 3: // high
        return isDark ? const Color(0xFF993D00) : const Color(0xFFFFE0B2);
      case 4: // urgent
        return isDark ? const Color(0xFF8B0000) : const Color(0xFFFFCDD2);
      default:
        return isDark ? const Color(0xFF795548) : const Color(0xFFFFECB3);
    }
  }



  /// الحصول على لون خلفية الحالة
  Color _getStatusBackgroundColor(String status, bool isDark) {
    return AppColors.getTaskStatusColor(status).withValues(alpha: isDark ? 0.3 : 0.1);
  }

  /// الحصول على لون التراكب
  Color _getOverlayColor(String status, double opacity) {
    return AppColors.getTaskStatusColor(status).withValues(alpha: opacity);
  }




}
