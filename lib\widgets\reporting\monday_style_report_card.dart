import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/report_models.dart';
import 'package:flutter_application_2/models/reporting/report_result_model.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../models/enhanced_report_model.dart';

/// بطاقة التقرير بتصميم Monday.com
///
/// تعرض معلومات التقرير بتصميم مشابه لـ Monday.com
class MondayStyleReportCard extends StatelessWidget {
  /// التقرير
  final EnhancedReport report;

  /// حدث النقر على البطاقة
  final VoidCallback? onTap;

  /// حدث تعديل التقرير
  final VoidCallback? onEdit;

  /// حدث حذف التقرير
  final VoidCallback? onDelete;

  /// حدث إضافة/إزالة من المفضلة
  final VoidCallback? onToggleFavorite;

  /// حدث تصدير التقرير
  final VoidCallback? onExport;

  /// حدث مشاركة التقرير
  final VoidCallback? onShare;

  const MondayStyleReportCard({
    super.key,
    required this.report,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onToggleFavorite,
    this.onExport,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: العنوان والحالة
              Row(
                children: [
                  // شريط اللون الجانبي
                  Container(
                    width: 4,
                    height: 40,
                    decoration: BoxDecoration(
                      color: report.color ?? _getTypeColor(report.type),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // أيقونة التقرير
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: (report.color ?? _getTypeColor(report.type)).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      report.icon ?? _getTypeIcon(report.type),
                      color: report.color ?? _getTypeColor(report.type),
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // عنوان التقرير
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          report.title,
                          style: AppStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (report.description != null && report.description!.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            report.description!,
                            style: AppStyles.bodySmall.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  // حالة المفضلة
                  IconButton(
                    icon: Icon(
                      report.isFavorite ? Icons.star : Icons.star_border,
                      color: report.isFavorite ? Colors.amber : Colors.grey[400],
                      size: 20,
                    ),
                    onPressed: onToggleFavorite,
                    tooltip: report.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثاني: المعلومات الإضافية
              Row(
                children: [
                  const SizedBox(width: 16), // محاذاة مع المحتوى أعلاه
                  // نوع التقرير
                  _buildStatusChip(
                    _getReportTypeName(report.type),
                    _getTypeColor(report.type),
                  ),
                  const SizedBox(width: 8),
                  // الفترة
                  _buildStatusChip(
                    _getPeriodName(report.period),
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  // حالة المشاركة
                  if (report.isShared)
                    _buildStatusChip(
                      'مشترك',
                      Colors.orange,
                    ),
                  if (report.isPublic) ...[
                    const SizedBox(width: 8),
                    _buildStatusChip(
                      'عام',
                      Colors.purple,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثالث: التواريخ والإجراءات
              Row(
                children: [
                  const SizedBox(width: 16), // محاذاة مع المحتوى أعلاه
                  // تاريخ الإنشاء
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('yyyy/MM/dd').format(report.createdAt),
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  if (report.updatedAt != null) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.update,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      DateFormat('yyyy/MM/dd').format(report.updatedAt!),
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  const Spacer(),
                  // أزرار الإجراءات
                  _buildActionButton(
                    Icons.play_arrow,
                    'تشغيل',
                    onTap,
                    Colors.green,
                  ),
                  const SizedBox(width: 4),
                  _buildActionButton(
                    Icons.download,
                    'تصدير',
                    onExport,
                    Colors.blue,
                  ),
                  if (onEdit != null) ...[
                    const SizedBox(width: 4),
                    _buildActionButton(
                      Icons.edit,
                      'تعديل',
                      onEdit,
                      Colors.orange,
                    ),
                  ],
                  if (onShare != null) ...[
                    const SizedBox(width: 4),
                    _buildActionButton(
                      Icons.share,
                      'مشاركة',
                      onShare,
                      Colors.purple,
                    ),
                  ],
                  if (onDelete != null) ...[
                    const SizedBox(width: 4),
                    _buildActionButton(
                      Icons.delete,
                      'حذف',
                      onDelete,
                      Colors.red,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريحة الحالة
  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: AppStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: 11,
        ),
      ),
    );
  }

  /// بناء زر الإجراء
  Widget _buildActionButton(
    IconData icon,
    String tooltip,
    VoidCallback? onPressed,
    Color color,
  ) {
    return IconButton(
      icon: Icon(icon, size: 18),
      onPressed: onPressed,
      tooltip: tooltip,
      color: color,
      padding: const EdgeInsets.all(4),
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
    );
  }

  /// الحصول على لون نوع التقرير
  Color _getTypeColor(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Colors.blue;
      case ReportType.userPerformance:
        return Colors.green;
      case ReportType.departmentPerformance:
        return Colors.orange;
      case ReportType.timeTracking:
        return Colors.purple;
      case ReportType.taskProgress:
        return Colors.teal;
      case ReportType.custom:
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع التقرير
  IconData _getTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return Icons.task_alt;
      case ReportType.userPerformance:
        return Icons.person;
      case ReportType.departmentPerformance:
        return Icons.business;
      case ReportType.timeTracking:
        return Icons.access_time;
      case ReportType.taskProgress:
        return Icons.trending_up;
      case ReportType.custom:
        return Icons.settings;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    return type.displayName;
  }

  /// الحصول على اسم الفترة
  String _getPeriodName(ReportPeriod period) {
    switch (period) {
      case ReportPeriod.today:
        return 'اليوم';
      case ReportPeriod.yesterday:
        return 'أمس';
      case ReportPeriod.thisWeek:
        return 'هذا الأسبوع';
      case ReportPeriod.lastWeek:
        return 'الأسبوع الماضي';
      case ReportPeriod.thisMonth:
        return 'هذا الشهر';
      case ReportPeriod.lastMonth:
        return 'الشهر الماضي';
      case ReportPeriod.thisQuarter:
        return 'هذا الربع';
      case ReportPeriod.lastQuarter:
        return 'الربع الماضي';
      case ReportPeriod.thisYear:
        return 'هذه السنة';
      case ReportPeriod.lastYear:
        return 'السنة الماضية';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
      default:
        return 'غير معروف';
    }
  }
}
