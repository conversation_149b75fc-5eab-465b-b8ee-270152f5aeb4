import 'custom_role_model.dart';
import 'permission_models.dart';

/// نموذج ربط الدور المخصص بالصلاحية
class CustomRolePermission {
  final int id;
  final int customRoleId;
  final int permissionId;
  final int createdAt;
  final int? createdBy;
  final bool isDeleted;

  // العلاقات البرمجية
  final Permission? permission;
  final CustomRole? customRole;

  const CustomRolePermission({
    required this.id,
    required this.customRoleId,
    required this.permissionId,
    required this.createdAt,
    this.createdBy,
    this.isDeleted = false,
    this.permission,
    this.customRole,
  });

  factory CustomRolePermission.fromJson(Map<String, dynamic> json) {
    return CustomRolePermission(
      id: json['id'] as int,
      customRoleId: json['customRoleId'] as int,
      permissionId: json['permissionId'] as int,
      createdAt: json['createdAt'] as int,
      createdBy: json['createdBy'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      permission: json['permission'] != null ? Permission.fromJson(json['permission']) : null,
      customRole: json['customRole'] != null ? CustomRole.fromJson(json['customRole']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customRoleId': customRoleId,
      'permissionId': permissionId,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'isDeleted': isDeleted,
      'permission': permission?.toJson(),
      'customRole': customRole?.toJson(),
    };
  }
}
