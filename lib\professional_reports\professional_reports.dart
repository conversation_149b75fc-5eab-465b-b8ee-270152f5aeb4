/// نظام التقارير الاحترافية
/// 
/// هذا الملف هو نقطة الدخول الرئيسية لنظام التقارير الاحترافية
/// يحتوي على جميع الصادرات والتهيئة المطلوبة

library professional_reports;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
// import 'package:flutter_application_2/models/report_models.dart'; // تم إزالته لتجنب التضارب
import 'package:flutter_application_2/professional_reports/models/report_data_models.dart';
import 'package:flutter_application_2/professional_reports/models/report_template_models.dart';
import 'package:flutter_application_2/professional_reports/pdf_report_service.dart';
import 'package:flutter_application_2/professional_reports/task_report_service.dart';

import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

// النماذج
export 'models/report_data_models.dart';
export 'models/report_template_models.dart';

// الخدمات



// الشاشات
export 'screens/reports_dashboard_screen.dart';

/// فئة تهيئة نظام التقارير الاحترافية
class ProfessionalReports {
  /// إصدار النظام
  static const String version = '1.0.0';
  
  /// اسم النظام
  static const String name = 'نظام التقارير الاحترافية';
  
  /// وصف النظام
  static const String description = 'نظام شامل لإنشاء وإدارة التقارير الاحترافية مع دعم كامل للغة العربية';

  /// تهيئة النظام
  /// 
  /// يجب استدعاء هذه الدالة قبل استخدام أي من مكونات النظام
  static Future<void> initialize() async {
    try {
      // تهيئة خدمة تقارير المهام
      final taskReportService = TaskReportService();
      Get.put(taskReportService);
      
      // تهيئة خدمة PDF
      final pdfService = PdfReportService();
      await pdfService.loadArabicFonts();
      
      // تهيئة خدمة الرسوم البيانية (للاستخدام المستقبلي)
      // final chartService = ChartService();
      
      debugPrint('✅ تم تهيئة نظام التقارير الاحترافية بنجاح');
      debugPrint('📊 الإصدار: $version');
      debugPrint('🔧 الخدمات المتاحة: تقارير المهام، PDF، الرسوم البيانية');
      
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة نظام التقارير الاحترافية: $e');
      rethrow;
    }
  }

  /// الحصول على معلومات النظام
  static Map<String, dynamic> getSystemInfo() {
    return {
      'name': name,
      'version': version,
      'description': description,
      'features': [
        'إنشاء تقارير PDF احترافية',
        'دعم كامل للغة العربية',
        'رسوم بيانية تفاعلية',
        'قوالب قابلة للتخصيص',
        'تصدير بصيغ متعددة',
        'فلترة وتجميع متقدمة',
        'واجهات حديثة وسهلة الاستخدام',
      ],
      'supportedFormats': [
        'PDF',
        'Excel',
        'CSV',
        'JSON',
      ],
      'chartTypes': [
        'دائري',
        'عمودي',
        'خطي',
        'مساحي',
        'شعاعي',
      ],
      'reportTypes': [
        'ملخص المهام',
        'تفاصيل المهام',
        'تقدم المهام',
        'أداء المستخدمين',
        'أداء الأقسام',
        'تتبع الوقت',
        'تقارير مخصصة',
      ],
    };
  }

  /// التحقق من توفر المتطلبات
  static Future<bool> checkRequirements() async {
    try {
      // التحقق من توفر الخطوط العربية
      bool arabicFontsAvailable = false;
      try {
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
        arabicFontsAvailable = true;
      } catch (e) {
        try {
          await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
          arabicFontsAvailable = true;
        } catch (e2) {
          debugPrint('⚠️ لم يتم العثور على الخطوط العربية');
        }
      }

      // التحقق من توفر مجلد الأصول
      bool assetsAvailable = false;
      try {
        await rootBundle.load('assets/images/logo.png');
        assetsAvailable = true;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على مجلد الأصول');
      }

      // التحقق من صلاحيات الكتابة
      bool writePermissions = false;
      try {
        final directory = await getApplicationDocumentsDirectory();
        writePermissions = await directory.exists();
      } catch (e) {
        debugPrint('⚠️ لا توجد صلاحيات كتابة');
      }

      debugPrint('📋 نتائج فحص المتطلبات:');
      debugPrint('   الخطوط العربية: ${arabicFontsAvailable ? '✅' : '❌'}');
      debugPrint('   مجلد الأصول: ${assetsAvailable ? '✅' : '❌'}');
      debugPrint('   صلاحيات الكتابة: ${writePermissions ? '✅' : '❌'}');

      return writePermissions; // الحد الأدنى المطلوب
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص المتطلبات: $e');
      return false;
    }
  }

  /// إنشاء تقرير سريع
  /// 
  /// دالة مساعدة لإنشاء تقرير سريع بالإعدادات الافتراضية
  static Future<String?> createQuickReport({
    required List<TaskReportData> tasks,
    ReportType reportType = ReportType.taskSummary,
    String? fileName,
  }) async {
    try {
      // إنشاء قالب افتراضي
      final template = ReportTemplate(
        id: 'quick_report',
        name: 'تقرير سريع',
        description: 'قالب افتراضي للتقارير السريعة',
        reportType: reportType,
        designSettings: const ReportDesignSettings(),
        contentSettings: const ReportContentSettings(),
        exportSettings: const ReportExportSettings(),
        createdAt: DateTime.now(),
      );

      // إنشاء إحصائيات بسيطة للتقرير
      final statistics = ReportStatistics(
        totalTasks: tasks.length,
        completedTasks: tasks.where((t) => t.status == 'مكتملة').length,
        inProgressTasks: tasks.where((t) => t.status == 'قيد التنفيذ').length,
        overdueTasks: tasks.where((t) => t.isOverdue).length,
        cancelledTasks: tasks.where((t) => t.status == 'ملغاة').length,
        averageCompletion: tasks.isNotEmpty 
            ? tasks.map((t) => t.completionPercentage).reduce((a, b) => a + b) / tasks.length
            : 0.0,
        totalEstimatedTime: 0,
        totalActualTime: 0,
        activeUsers: tasks.map((t) => t.creator.id).toSet().length,
        activeDepartments: 0,
      );

      // إنشاء التقرير
      final pdfService = PdfReportService();
      await pdfService.loadArabicFonts();

      final filePath = await pdfService.createTaskSummaryReport(
        tasks: tasks,
        statistics: statistics,
        template: template,
        fileName: fileName,
      );

      return filePath;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء التقرير السريع: $e');
      return null;
    }
  }

  /// تنظيف الملفات المؤقتة
  static Future<void> cleanup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/reports');
      
      if (await reportsDir.exists()) {
        final files = await reportsDir.list().toList();
        final now = DateTime.now();
        
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified).inDays;
            
            // حذف الملفات الأقدم من 30 يوم
            if (age > 30) {
              await file.delete();
              debugPrint('🗑️ تم حذف الملف القديم: ${file.path}');
            }
          }
        }
      }
      
      debugPrint('✅ تم تنظيف الملفات المؤقتة');
      
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الملفات المؤقتة: $e');
    }
  }
}

/// الثوابت العامة للنظام
class ReportConstants {
  /// الحد الأقصى لعدد المهام في التقرير
  static const int maxTasksPerReport = 10000;
  
  /// الحد الأقصى لحجم ملف PDF (بالميجابايت)
  static const int maxPdfSizeMB = 50;
  
  /// المهلة الزمنية لإنشاء التقرير (بالثواني)
  static const int reportTimeoutSeconds = 300;
  
  /// أنواع الملفات المدعومة للتصدير
  static const List<String> supportedExportFormats = ['pdf', 'excel', 'csv', 'json'];
  
  /// الألوان الافتراضية للرسوم البيانية
  static const List<Color> defaultChartColors = [
    Color(0xFF2196F3), // أزرق
    Color(0xFF4CAF50), // أخضر
    Color(0xFFFF9800), // برتقالي
    Color(0xFFF44336), // أحمر
    Color(0xFF9C27B0), // بنفسجي
    Color(0xFF00BCD4), // سماوي
    Color(0xFFFFEB3B), // أصفر
    Color(0xFF795548), // بني
  ];
  
  /// أحجام الخطوط الافتراضية
  static const Map<String, double> defaultFontSizes = {
    'title': 24.0,
    'subtitle': 18.0,
    'body': 12.0,
    'caption': 10.0,
  };
  
  /// الهوامش الافتراضية للصفحات
  static const EdgeInsets defaultPageMargins = EdgeInsets.all(40.0);
}

/// استثناءات مخصصة للنظام
class ReportException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const ReportException(this.message, {this.code, this.originalError});

  @override
  String toString() {
    return 'ReportException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// أنواع الأخطاء المختلفة
class ReportErrorCodes {
  static const String dataNotFound = 'DATA_NOT_FOUND';
  static const String templateNotFound = 'TEMPLATE_NOT_FOUND';
  static const String exportFailed = 'EXPORT_FAILED';
  static const String fontLoadFailed = 'FONT_LOAD_FAILED';
  static const String permissionDenied = 'PERMISSION_DENIED';
  static const String fileSizeExceeded = 'FILE_SIZE_EXCEEDED';
  static const String timeoutExceeded = 'TIMEOUT_EXCEEDED';
}