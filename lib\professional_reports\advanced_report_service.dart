import 'package:flutter/services.dart';
import 'package:flutter_application_2/professional_reports/models/report_data_models.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/professional_reports/tasks_report_service.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة التقارير المتقدمة
/// 
/// توفر إمكانيات متقدمة لإنشاء تقارير مخصصة ومفصلة
/// مع دعم الفلاتر والتجميع والتحليل الإحصائي
class AdvancedReportService {
  static final TaskApiServiceReports _taskApi = TaskApiServiceReports();
  
  /// إنشاء تقرير مقارن بين فترتين زمنيتين
  static Future<pw.Document> generateComparisonReport({
    required DateTime startDate1,
    required DateTime endDate1,
    required DateTime startDate2,
    required DateTime endDate2,
    Function(String)? onProgress,
  }) async {
    final pdf = pw.Document();
    
    onProgress?.call('تحميل الخطوط...');
    final fonts = await _loadFonts();
    
    onProgress?.call('جلب بيانات الفترة الأولى...');
    final tasks1 = await _getTasksByDateRange(startDate1, endDate1);
    
    onProgress?.call('جلب بيانات الفترة الثانية...');
    final tasks2 = await _getTasksByDateRange(startDate2, endDate2);
    
    final stats1 = _calculateStatistics(tasks1);
    final stats2 = _calculateStatistics(tasks2);
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'تقرير مقارن بين فترتين زمنيتين',
            style: pw.TextStyle(font: fonts['bold'], fontSize: 20, color: PdfColors.teal800),
          );
        },
        footer: (pw.Context context) {
          return pw.Text(
            'صفحة ${context.pageNumber} - ${_formatDateTime(DateTime.now())}',
            style: pw.TextStyle(font: fonts['arabic'], fontSize: 10),
          );
        },
        build: (pw.Context context) {
          return [
            // معلومات الفترات
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                border: pw.Border.all(color: PdfColors.blue200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'الفترات المقارنة:',
                    style: pw.TextStyle(font: fonts['bold'], fontSize: 14),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    'الفترة الأولى: ${_formatDate(startDate1)} إلى ${_formatDate(endDate1)}',
                    style: pw.TextStyle(font: fonts['arabic'], fontSize: 12),
                  ),
                  pw.Text(
                    'الفترة الثانية: ${_formatDate(startDate2)} إلى ${_formatDate(endDate2)}',
                    style: pw.TextStyle(font: fonts['arabic'], fontSize: 12),
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // جدول المقارنة
            pw.Text(
              'مقارنة الإحصائيات',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 10),
            
            pw.Table.fromTextArray(
              headers: ['المؤشر', 'الفترة الأولى', 'الفترة الثانية', 'الفرق', 'نسبة التغيير'],
              data: [
                [
                  'إجمالي المهام',
                  '${stats1.totalTasks}',
                  '${stats2.totalTasks}',
                  '${stats2.totalTasks - stats1.totalTasks}',
                  '${_calculatePercentageChange(stats1.totalTasks, stats2.totalTasks)}%'
                ],
                [
                  'المهام المكتملة',
                  '${stats1.completedTasks}',
                  '${stats2.completedTasks}',
                  '${stats2.completedTasks - stats1.completedTasks}',
                  '${_calculatePercentageChange(stats1.completedTasks, stats2.completedTasks)}%'
                ],
                [
                  'معدل الإنجاز',
                  '${stats1.completionRate.toStringAsFixed(1)}%',
                  '${stats2.completionRate.toStringAsFixed(1)}%',
                  '${(stats2.completionRate - stats1.completionRate).toStringAsFixed(1)}%',
                  '${_calculatePercentageChange(stats1.completionRate, stats2.completionRate)}%'
                ],
                [
                  'المهام المتأخرة',
                  '${stats1.overdueTasks}',
                  '${stats2.overdueTasks}',
                  '${stats2.overdueTasks - stats1.overdueTasks}',
                  '${_calculatePercentageChange(stats1.overdueTasks, stats2.overdueTasks)}%'
                ],
                [
                  'متوسط نسبة الإنجاز',
                  '${stats1.averageCompletion.toStringAsFixed(1)}%',
                  '${stats2.averageCompletion.toStringAsFixed(1)}%',
                  '${(stats2.averageCompletion - stats1.averageCompletion).toStringAsFixed(1)}%',
                  '${_calculatePercentageChange(stats1.averageCompletion, stats2.averageCompletion)}%'
                ],
              ],
              cellStyle: pw.TextStyle(font: fonts['arabic'], fontSize: 11),
              headerStyle: pw.TextStyle(font: fonts['bold'], fontSize: 12, fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.center,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.green100),
              oddRowDecoration: const pw.BoxDecoration(color: PdfColors.grey100),
            ),
            
            pw.SizedBox(height: 20),
            
            // تحليل الاتجاهات
            pw.Text(
              'تحليل الاتجاهات',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 10),
            
            _buildTrendAnalysis(stats1, stats2, fonts),
          ];
        },
      ),
    );
    
    return pdf;
  }
  
  /// إنشاء تقرير الأداء حسب القسم
  static Future<pw.Document> generateDepartmentPerformanceReport({
    Function(String)? onProgress,
  }) async {
    final pdf = pw.Document();
    
    onProgress?.call('تحميل الخطوط...');
    final fonts = await _loadFonts();
    
    onProgress?.call('جلب بيانات المهام...');
    final allTasks = await _taskApi.getAllTasks();
    
    // تجميع المهام حسب القسم
    final Map<String, List<Task>> tasksByDepartment = {};
    for (final task in allTasks) {
      final deptName = task.department?.name ?? 'غير محدد';
      tasksByDepartment.putIfAbsent(deptName, () => []).add(task);
    }
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'تقرير أداء الأقسام',
            style: pw.TextStyle(font: fonts['bold'], fontSize: 20, color: PdfColors.teal800),
          );
        },
        footer: (pw.Context context) {
          return pw.Text(
            'صفحة ${context.pageNumber} - ${_formatDateTime(DateTime.now())}',
            style: pw.TextStyle(font: fonts['arabic'], fontSize: 10),
          );
        },
        build: (pw.Context context) {
          return [
            pw.Text(
              'ملخص أداء الأقسام',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 15),
            
            // جدول أداء الأقسام
            pw.Table.fromTextArray(
              headers: [
                'القسم',
                'إجمالي المهام',
                'المكتملة',
                'قيد التنفيذ',
                'المتأخرة',
                'معدل الإنجاز',
                'متوسط نسبة الإنجاز'
              ],
              data: tasksByDepartment.entries.map((entry) {
                final deptName = entry.key;
                final tasks = entry.value;
                final stats = _calculateStatistics(tasks);
                
                return [
                  deptName,
                  '${stats.totalTasks}',
                  '${stats.completedTasks}',
                  '${stats.inProgressTasks}',
                  '${stats.overdueTasks}',
                  '${stats.completionRate.toStringAsFixed(1)}%',
                  '${stats.averageCompletion.toStringAsFixed(1)}%',
                ];
              }).toList(),
              cellStyle: pw.TextStyle(font: fonts['arabic'], fontSize: 10),
              headerStyle: pw.TextStyle(font: fonts['bold'], fontSize: 11, fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.center,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.blue100),
              oddRowDecoration: const pw.BoxDecoration(color: PdfColors.grey100),
              columnWidths: {
                0: const pw.FlexColumnWidth(2), // القسم
                1: const pw.FlexColumnWidth(1), // إجمالي
                2: const pw.FlexColumnWidth(1), // مكتملة
                3: const pw.FlexColumnWidth(1), // قيد التنفيذ
                4: const pw.FlexColumnWidth(1), // متأخرة
                5: const pw.FlexColumnWidth(1.2), // معدل الإنجاز
                6: const pw.FlexColumnWidth(1.5), // متوسط نسبة الإنجاز
              },
            ),
            
            pw.SizedBox(height: 20),
            
            // تفاصيل كل قسم
            ...tasksByDepartment.entries.map((entry) {
              final deptName = entry.key;
              final tasks = entry.value;
              final stats = _calculateStatistics(tasks);
              
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Container(
                    width: double.infinity,
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.teal50,
                      border: pw.Border.all(color: PdfColors.teal200),
                    ),
                    child: pw.Text(
                      'تفاصيل قسم: $deptName',
                      style: pw.TextStyle(font: fonts['bold'], fontSize: 14, color: PdfColors.teal800),
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('إجمالي المهام: ${stats.totalTasks}', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                            pw.Text('المهام المكتملة: ${stats.completedTasks}', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                            pw.Text('المهام قيد التنفيذ: ${stats.inProgressTasks}', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                          ],
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text('المهام المتأخرة: ${stats.overdueTasks}', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                            pw.Text('معدل الإنجاز: ${stats.completionRate.toStringAsFixed(1)}%', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                            pw.Text('متوسط نسبة الإنجاز: ${stats.averageCompletion.toStringAsFixed(1)}%', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  pw.SizedBox(height: 15),
                ],
              );
            }).toList(),
          ];
        },
      ),
    );
    
    return pdf;
  }
  
  /// إنشاء تقرير الأداء الشخصي للمستخدم
  static Future<pw.Document> generateUserPerformanceReport({
    required int userId,
    Function(String)? onProgress,
  }) async {
    final pdf = pw.Document();
    
    onProgress?.call('تحميل الخطوط...');
    final fonts = await _loadFonts();
    
    onProgress?.call('جلب مهام المستخدم...');
    final assignedTasks = await _taskApi.getTasksByAssignee(userId);
    final createdTasks = await _taskApi.getTasksByCreator(userId);
    
    final assignedStats = _calculateStatistics(assignedTasks);
    final createdStats = _calculateStatistics(createdTasks);
    
    // حساب إحصائيات إضافية
    final completedOnTime = assignedTasks.where((task) {
      if (task.completedAt == null || task.dueDate == null) return false;
      final completedDate = DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000);
      final dueDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
      return completedDate.isBefore(dueDate) || completedDate.isAtSameMomentAs(dueDate);
    }).length;
    
    final onTimeRate = assignedStats.completedTasks > 0 
        ? (completedOnTime / assignedStats.completedTasks) * 100 
        : 0.0;
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'تقرير الأداء الشخصي',
            style: pw.TextStyle(font: fonts['bold'], fontSize: 20, color: PdfColors.teal800),
          );
        },
        footer: (pw.Context context) {
          return pw.Text(
            'صفحة ${context.pageNumber} - ${_formatDateTime(DateTime.now())}',
            style: pw.TextStyle(font: fonts['arabic'], fontSize: 10),
          );
        },
        build: (pw.Context context) {
          return [
            // معلومات المستخدم
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                border: pw.Border.all(color: PdfColors.blue200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'معرف المستخدم: $userId',
                    style: pw.TextStyle(font: fonts['bold'], fontSize: 14),
                  ),
                  pw.Text(
                    'تاريخ التقرير: ${_formatDateTime(DateTime.now())}',
                    style: pw.TextStyle(font: fonts['arabic'], fontSize: 12),
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // إحصائيات المهام المكلف بها
            pw.Text(
              'المهام المكلف بها',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 10),
            
            pw.Table.fromTextArray(
              headers: ['المؤشر', 'القيمة', 'النسبة المئوية'],
              data: [
                ['إجمالي المهام المكلف بها', '${assignedStats.totalTasks}', '100%'],
                ['المهام المكتملة', '${assignedStats.completedTasks}', '${assignedStats.completionRate.toStringAsFixed(1)}%'],
                ['المهام قيد التنفيذ', '${assignedStats.inProgressTasks}', '${((assignedStats.inProgressTasks / (assignedStats.totalTasks > 0 ? assignedStats.totalTasks : 1)) * 100).toStringAsFixed(1)}%'],
                ['المهام المتأخرة', '${assignedStats.overdueTasks}', '${assignedStats.overdueRate.toStringAsFixed(1)}%'],
                ['المهام المكتملة في الوقت المحدد', '$completedOnTime', '${onTimeRate.toStringAsFixed(1)}%'],
                ['متوسط نسبة الإنجاز', '${assignedStats.averageCompletion.toStringAsFixed(1)}%', '-'],
              ],
              cellStyle: pw.TextStyle(font: fonts['arabic'], fontSize: 11),
              headerStyle: pw.TextStyle(font: fonts['bold'], fontSize: 12, fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.center,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.green100),
              oddRowDecoration: const pw.BoxDecoration(color: PdfColors.grey100),
            ),
            
            pw.SizedBox(height: 20),
            
            // إحصائيات المهام المنشأة
            pw.Text(
              'المهام المنشأة',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 10),
            
            pw.Table.fromTextArray(
              headers: ['المؤشر', 'القيمة', 'النسبة المئوية'],
              data: [
                ['إجمالي المهام المنشأة', '${createdStats.totalTasks}', '100%'],
                ['المهام المكتملة', '${createdStats.completedTasks}', '${createdStats.completionRate.toStringAsFixed(1)}%'],
                ['المهام قيد التنفيذ', '${createdStats.inProgressTasks}', '${((createdStats.inProgressTasks / (createdStats.totalTasks > 0 ? createdStats.totalTasks : 1)) * 100).toStringAsFixed(1)}%'],
                ['المهام المتأخرة', '${createdStats.overdueTasks}', '${createdStats.overdueRate.toStringAsFixed(1)}%'],
                ['متوسط نسبة الإنجاز', '${createdStats.averageCompletion.toStringAsFixed(1)}%', '-'],
              ],
              cellStyle: pw.TextStyle(font: fonts['arabic'], fontSize: 11),
              headerStyle: pw.TextStyle(font: fonts['bold'], fontSize: 12, fontWeight: pw.FontWeight.bold),
              cellAlignment: pw.Alignment.center,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.orange100),
              oddRowDecoration: const pw.BoxDecoration(color: PdfColors.grey100),
            ),
            
            pw.SizedBox(height: 20),
            
            // تقييم الأداء
            pw.Text(
              'تقييم الأداء',
              style: pw.TextStyle(font: fonts['bold'], fontSize: 16, color: PdfColors.teal800),
            ),
            pw.SizedBox(height: 10),
            
            _buildPerformanceEvaluation(assignedStats, onTimeRate, fonts),
          ];
        },
      ),
    );
    
    return pdf;
  }
  
  /// تحميل الخطوط
  static Future<Map<String, pw.Font>> _loadFonts() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
      
      return {
        'arabic': pw.Font.ttf(fontData),
        'bold': pw.Font.ttf(boldFontData),
      };
    } catch (e) {
      final defaultFont = pw.ThemeData().defaultTextStyle.font!;
      return {
        'arabic': defaultFont,
        'bold': defaultFont,
      };
    }
  }
  
  /// جلب المهام حسب نطاق زمني
  static Future<List<Task>> _getTasksByDateRange(DateTime startDate, DateTime endDate) async {
    final allTasks = await _taskApi.getAllTasks();
    return allTasks.where((task) {
      final taskDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
      return taskDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             taskDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }
  
  /// حساب الإحصائيات
  static ReportStatistics _calculateStatistics(List<Task> tasks) {
    if (tasks.isEmpty) {
      return const ReportStatistics(
        totalTasks: 0,
        completedTasks: 0,
        inProgressTasks: 0,
        overdueTasks: 0,
        cancelledTasks: 0,
        averageCompletion: 0.0,
        totalEstimatedTime: 0,
        totalActualTime: 0,
        activeUsers: 0,
        activeDepartments: 0,
      );
    }
    
    final now = DateTime.now();
    int completedTasks = 0;
    int inProgressTasks = 0;
    int overdueTasks = 0;
    int cancelledTasks = 0;
    int totalCompletion = 0;
    int totalEstimatedTime = 0;
    int totalActualTime = 0;
    
    final Set<int> uniqueUsers = {};
    final Set<int> uniqueDepartments = {};
    
    for (final task in tasks) {
      switch (task.status.toLowerCase()) {
        case 'مكتملة':
        case 'completed':
          completedTasks++;
          break;
        case 'قيد التنفيذ':
        case 'in_progress':
          inProgressTasks++;
          break;
        case 'ملغاة':
        case 'cancelled':
          cancelledTasks++;
          break;
      }
      
      if (task.dueDate != null && task.status != 'مكتملة') {
        final dueDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
        if (now.isAfter(dueDate)) {
          overdueTasks++;
        }
      }
      
      totalCompletion += task.completionPercentage;
      totalEstimatedTime += task.estimatedTime ?? 0;
      totalActualTime += task.actualTime ?? 0;
      
      if (task.assigneeId != null) uniqueUsers.add(task.assigneeId!);
      uniqueUsers.add(task.creatorId);
      if (task.departmentId != null) uniqueDepartments.add(task.departmentId!);
    }
    
    return ReportStatistics(
      totalTasks: tasks.length,
      completedTasks: completedTasks,
      inProgressTasks: inProgressTasks,
      overdueTasks: overdueTasks,
      cancelledTasks: cancelledTasks,
      averageCompletion: totalCompletion / tasks.length,
      totalEstimatedTime: totalEstimatedTime,
      totalActualTime: totalActualTime,
      activeUsers: uniqueUsers.length,
      activeDepartments: uniqueDepartments.length,
    );
  }
  
  /// حساب نسبة التغيير
  static double _calculatePercentageChange(num oldValue, num newValue) {
    if (oldValue == 0) return newValue == 0 ? 0 : 100;
    return ((newValue - oldValue) / oldValue) * 100;
  }
  
  /// بناء تحليل الاتجاهات
  static pw.Widget _buildTrendAnalysis(ReportStatistics stats1, ReportStatistics stats2, Map<String, pw.Font> fonts) {
    final improvements = <String>[];
    final concerns = <String>[];
    
    // تحليل التحسينات
    if (stats2.completionRate > stats1.completionRate) {
      improvements.add('تحسن في معدل إنجاز المهام بنسبة ${(stats2.completionRate - stats1.completionRate).toStringAsFixed(1)}%');
    }
    
    if (stats2.averageCompletion > stats1.averageCompletion) {
      improvements.add('تحسن في متوسط نسبة الإنجاز بنسبة ${(stats2.averageCompletion - stats1.averageCompletion).toStringAsFixed(1)}%');
    }
    
    if (stats2.overdueTasks < stats1.overdueTasks) {
      improvements.add('انخفاض في عدد المهام المتأخرة من ${stats1.overdueTasks} إلى ${stats2.overdueTasks}');
    }
    
    // تحليل المخاوف
    if (stats2.completionRate < stats1.completionRate) {
      concerns.add('انخفاض في معدل إنجاز المهام بنسبة ${(stats1.completionRate - stats2.completionRate).toStringAsFixed(1)}%');
    }
    
    if (stats2.overdueTasks > stats1.overdueTasks) {
      concerns.add('زيادة في عدد المهام المتأخرة من ${stats1.overdueTasks} إلى ${stats2.overdueTasks}');
    }
    
    if (stats2.averageCompletion < stats1.averageCompletion) {
      concerns.add('انخفاض في متوسط نسبة الإنجاز بنسبة ${(stats1.averageCompletion - stats2.averageCompletion).toStringAsFixed(1)}%');
    }
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // التحسينات
        if (improvements.isNotEmpty) ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.green50,
              border: pw.Border.all(color: PdfColors.green200),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  '✓ التحسينات المحققة:',
                  style: pw.TextStyle(font: fonts['bold'], fontSize: 12, color: PdfColors.green800),
                ),
                pw.SizedBox(height: 5),
                ...improvements.map((improvement) => 
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 3),
                    child: pw.Text('• $improvement', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                  ),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 10),
        ],
        
        // المخاوف
        if (concerns.isNotEmpty) ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.red50,
              border: pw.Border.all(color: PdfColors.red200),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  '⚠ نقاط تحتاج إلى انتباه:',
                  style: pw.TextStyle(font: fonts['bold'], fontSize: 12, color: PdfColors.red800),
                ),
                pw.SizedBox(height: 5),
                ...concerns.map((concern) => 
                  pw.Padding(
                    padding: const pw.EdgeInsets.only(bottom: 3),
                    child: pw.Text('• $concern', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
                  ),
                ),
              ],
            ),
          ),
        ],
        
        // إذا لم تكن هناك تغييرات كبيرة
        if (improvements.isEmpty && concerns.isEmpty) ...[
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              border: pw.Border.all(color: PdfColors.blue200),
            ),
            child: pw.Text(
              'الأداء مستقر بين الفترتين مع عدم وجود تغييرات كبيرة',
              style: pw.TextStyle(font: fonts['arabic'], fontSize: 11, color: PdfColors.blue800),
            ),
          ),
        ],
      ],
    );
  }
  
  /// بناء تقييم الأداء
  static pw.Widget _buildPerformanceEvaluation(ReportStatistics stats, double onTimeRate, Map<String, pw.Font> fonts) {
    String overallRating = 'متوسط';
    PdfColor ratingColor = PdfColors.orange;
    
    // حساب التقييم الإجمالي
    final completionScore = stats.completionRate;
    final onTimeScore = onTimeRate;
    final averageScore = stats.averageCompletion;
    
    final overallScore = (completionScore + onTimeScore + averageScore) / 3;
    
    if (overallScore >= 80) {
      overallRating = 'ممتاز';
      ratingColor = PdfColors.green;
    } else if (overallScore >= 60) {
      overallRating = 'جيد';
      ratingColor = PdfColors.blue;
    } else if (overallScore >= 40) {
      overallRating = 'متوسط';
      ratingColor = PdfColors.orange;
    } else {
      overallRating = 'يحتاج تحسين';
      ratingColor = PdfColors.red;
    }
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'التقييم الإجمالي:',
                style: pw.TextStyle(font: fonts['bold'], fontSize: 14),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: ratingColor,
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: pw.Text(
                  overallRating,
                  style: pw.TextStyle(font: fonts['bold'], fontSize: 12, color: PdfColors.white),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          
          pw.Text(
            'النقاط الإيجابية:',
            style: pw.TextStyle(font: fonts['bold'], fontSize: 12, color: PdfColors.green800),
          ),
          pw.SizedBox(height: 5),
          
          if (stats.completionRate >= 70)
            pw.Text('• معدل إنجاز جيد للمهام', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
          if (onTimeRate >= 70)
            pw.Text('• التزام جيد بالمواعيد المحددة', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
          if (stats.averageCompletion >= 70)
            pw.Text('• متوسط نسبة إنجاز مرتفع', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
          
          pw.SizedBox(height: 10),
          
          pw.Text(
            'نقاط التحسين:',
            style: pw.TextStyle(font: fonts['bold'], fontSize: 12, color: PdfColors.red800),
          ),
          pw.SizedBox(height: 5),
          
          if (stats.completionRate < 70)
            pw.Text('• تحسين معدل إنجاز المهام', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
          if (onTimeRate < 70)
            pw.Text('• تحسين الالتزام بالمواعيد المحددة', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
          if (stats.overdueRate > 20)
            pw.Text('• تقليل عدد المهام المتأخرة', style: pw.TextStyle(font: fonts['arabic'], fontSize: 11)),
        ],
      ),
    );
  }
  
  /// تنسيق التاريخ
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
  
  /// تنسيق التاريخ والوقت
  static String _formatDateTime(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}