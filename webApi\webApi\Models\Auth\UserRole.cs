namespace webApi.Models.Auth;

/// <summary>
/// تعداد أدوار المستخدمين في النظام
/// </summary>
public enum UserRole
{
    /// <summary>
    /// مستخدم عادي - صلاحيات محدودة
    /// </summary>
    User = 1,

    /// <summary>
    /// مشرف - صلاحيات متوسطة
    /// </summary>
    Supervisor = 2,

    /// <summary>
    /// مدير - صلاحيات عالية
    /// </summary>
    Manager = 3,

    /// <summary>
    /// مدير عام - صلاحيات كاملة
    /// </summary>
    Admin = 4,

    /// <summary>
    /// مدير النظام - صلاحيات كاملة على النظام
    /// </summary>
    SuperAdmin = 5
}
