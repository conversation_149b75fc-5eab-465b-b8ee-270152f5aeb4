import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/report_models.dart';
import 'package:flutter_application_2/models/reporting/report_result_model.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../models/enhanced_report_model.dart';

/// بطاقة التقرير
///
/// تعرض معلومات التقرير في شكل بطاقة
class ReportCard extends StatelessWidget {
  /// التقرير
  final EnhancedReport report;

  /// حدث النقر على البطاقة
  final VoidCallback? onTap;

  /// حدث تعديل التقرير
  final VoidCallback? onEdit;

  /// حدث حذف التقرير
  final VoidCallback? onDelete;

  /// حدث إضافة/إزالة من المفضلة
  final VoidCallback? onFavorite;

  /// حدث تصدير التقرير
  final VoidCallback? onExport;

  /// حدث مشاركة التقرير
  final VoidCallback? onShare;

  /// هل البطاقة مضغوطة
  final bool isCompact;

  const ReportCard({
    super.key,
    required this.report,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onFavorite,
    this.onExport,
    this.onShare,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة التقرير
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: report.color?.withValues(alpha: 0.1) ?? Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      report.icon ?? Icons.bar_chart,
                      color: report.color ?? Colors.blue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // عنوان التقرير
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          report.title,
                          style: AppStyles.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (!isCompact && report.description != null && report.description!.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            report.description!,
                            style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  // زر المفضلة
                  IconButton(
                    icon: Icon(
                      report.isFavorite ? Icons.star : Icons.star_border,
                      color: report.isFavorite ? Colors.amber : Colors.grey,
                      size: 20,
                    ),
                    onPressed: onFavorite,
                    tooltip: report.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة',
                  ),
                ],
              ),

              if (!isCompact) ...[
                const SizedBox(height: 16),
                // معلومات التقرير
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: [
                    // نوع التقرير
                    _buildInfoChip(
                      Icons.category,
                      _getReportTypeName(report.type),
                      Colors.blue,
                    ),
                    // الفترة
                    _buildInfoChip(
                      Icons.date_range,
                      _getPeriodName(report.period),
                      Colors.green,
                    ),
                    // حالة المشاركة
                    if (report.isShared)
                      _buildInfoChip(
                        Icons.share,
                        'مشترك',
                        Colors.orange,
                      ),
                    // حالة العمومية
                    if (report.isPublic)
                      _buildInfoChip(
                        Icons.public,
                        'عام',
                        Colors.purple,
                      ),
                  ],
                ),

                const SizedBox(height: 12),
                // تاريخ الإنشاء والتحديث
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'تم الإنشاء: ${DateFormat('yyyy/MM/dd').format(report.createdAt)}',
                      style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                    ),
                    if (report.updatedAt != null) ...[
                      const SizedBox(width: 16),
                      Icon(
                        Icons.update,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تم التحديث: ${DateFormat('yyyy/MM/dd').format(report.updatedAt!)}',
                        style: AppStyles.bodySmall.copyWith(color: Colors.grey[600]),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),
                // أزرار الإجراءات
                Row(
                  children: [
                    // زر التشغيل
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.play_arrow, size: 16),
                        label: const Text('تشغيل'),
                        onPressed: onTap,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // زر التصدير
                    IconButton(
                      icon: const Icon(Icons.download, size: 20),
                      onPressed: onExport,
                      tooltip: 'تصدير',
                    ),
                    // زر التعديل
                    if (onEdit != null)
                      IconButton(
                        icon: const Icon(Icons.edit, size: 20),
                        onPressed: onEdit,
                        tooltip: 'تعديل',
                      ),
                    // زر المشاركة
                    if (onShare != null)
                      IconButton(
                        icon: const Icon(Icons.share, size: 20),
                        onPressed: onShare,
                        tooltip: 'مشاركة',
                      ),
                    // زر الحذف
                    if (onDelete != null)
                      IconButton(
                        icon: const Icon(Icons.delete, size: 20),
                        onPressed: onDelete,
                        tooltip: 'حذف',
                        color: Colors.red,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شريحة معلومات
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    return type.displayName;
  }

  /// الحصول على اسم الفترة
  String _getPeriodName(ReportPeriod period) {
    switch (period) {
      case ReportPeriod.today:
        return 'اليوم';
      case ReportPeriod.yesterday:
        return 'أمس';
      case ReportPeriod.thisWeek:
        return 'هذا الأسبوع';
      case ReportPeriod.lastWeek:
        return 'الأسبوع الماضي';
      case ReportPeriod.thisMonth:
        return 'هذا الشهر';
      case ReportPeriod.lastMonth:
        return 'الشهر الماضي';
      case ReportPeriod.thisQuarter:
        return 'هذا الربع';
      case ReportPeriod.lastQuarter:
        return 'الربع الماضي';
      case ReportPeriod.thisYear:
        return 'هذه السنة';
      case ReportPeriod.lastYear:
        return 'السنة الماضية';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
      default:
        return 'غير معروف';
    }
  }
}
