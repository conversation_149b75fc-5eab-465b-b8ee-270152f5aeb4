import 'package:flutter/foundation.dart';
import '../../models/system_models.dart';
import 'api_service.dart';

/// خدمة API لسجلات النظام - متطابقة مع ASP.NET Core API
class SystemLogsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع سجلات النظام
  Future<List<SystemLog>> getAllLogs() async {
    try {
      final response = await _apiService.get('/SystemLogs');
      return _apiService.handleListResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات النظام: $e');
      rethrow;
    }
  }

  /// الحصول على سجل نظام بواسطة المعرف
  Future<SystemLog?> getLogById(int id) async {
    try {
      final response = await _apiService.get('/SystemLogs/$id');
      return _apiService.handleResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل النظام $id: $e');
      return null;
    }
  }

  /// الحصول على السجلات بحسب المستوى
  Future<List<SystemLog>> getLogsByLevel(String level) async {
    try {
      final response = await _apiService.get('/SystemLogs/level/$level');
      return _apiService.handleListResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المستوى $level: $e');
      return [];
    }
  }

  /// الحصول على السجلات بحسب الفئة
  Future<List<SystemLog>> getLogsByCategory(String category) async {
    try {
      final response = await _apiService.get('/SystemLogs/category/$category');
      return _apiService.handleListResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات الفئة $category: $e');
      return [];
    }
  }

  /// الحصول على السجلات في فترة زمنية محددة
  Future<List<SystemLog>> getLogsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      final response = await _apiService.get('/SystemLogs/date-range?start=$startTimestamp&end=$endTimestamp');
      return _apiService.handleListResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على السجلات في الفترة المحددة: $e');
      return [];
    }
  }

  /// إنشاء سجل نظام جديد
  Future<SystemLog> createLog(SystemLog log) async {
    try {
      final response = await _apiService.post(
        '/SystemLogs',
        log.toJson(),
      );
      return _apiService.handleResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل النظام: $e');
      rethrow;
    }
  }

  /// حذف سجل نظام
  Future<bool> deleteLog(int id) async {
    try {
      final response = await _apiService.delete('/SystemLogs/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف سجل النظام $id: $e');
      return false;
    }
  }

  /// حذف السجلات القديمة
  Future<bool> deleteOldLogs(int daysOld) async {
    try {
      final response = await _apiService.delete('/SystemLogs/old?days=$daysOld');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف السجلات القديمة: $e');
      return false;
    }
  }

  /// البحث في السجلات
  Future<List<SystemLog>> searchLogs(String query) async {
    try {
      final response = await _apiService.get('/SystemLogs/search?q=$query');
      return _apiService.handleListResponse<SystemLog>(
        response,
        (json) => SystemLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في السجلات: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات السجلات
  Future<Map<String, dynamic>> getLogsStatistics() async {
    try {
      final response = await _apiService.get('/SystemLogs/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات السجلات: $e');
      return {};
    }
  }

  /// تصدير السجلات
  Future<Map<String, dynamic>> exportLogs(String format, Map<String, dynamic>? filters) async {
    try {
      final response = await _apiService.post(
        '/SystemLogs/export',
        {
          'format': format,
          'filters': filters ?? {},
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير السجلات: $e');
      return {};
    }
  }

  /// مسح جميع السجلات
  Future<bool> clearAllLogs() async {
    try {
      final response = await _apiService.delete('/SystemLogs/all');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مسح جميع السجلات: $e');
      return false;
    }
  }

  /// الحصول على مستويات السجلات المتاحة
  Future<List<String>> getAvailableLevels() async {
    try {
      final response = await _apiService.get('/SystemLogs/levels');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستويات السجلات: $e');
      return ['info', 'warning', 'error', 'debug'];
    }
  }

  /// الحصول على فئات السجلات المتاحة
  Future<List<String>> getAvailableCategories() async {
    try {
      final response = await _apiService.get('/SystemLogs/categories');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على فئات السجلات: $e');
      return ['system', 'auth', 'api', 'database'];
    }
  }
}
