// import 'package:flutter/material.dart';
// import 'package:flutter_application_2/models/task_models.dart';
// import 'package:flutter_application_2/models/user_model.dart';
// import 'package:flutter_application_2/models/department_model.dart';
// import 'reporttask.dart';

// /// مثال على كيفية استخدام الملف المحسن لإنشاء تقارير PDF
// class ReportUsageExample {
  
//   /// مثال بسيط لإنشاء تقرير PDF
//   static Future<void> generateSimpleReport() async {
//     // إنشاء مهمة تجريبية
//     final task = _createSampleTask();
    
//     try {
//       // إنشاء التقرير مع تتبع التقدم
//       final pdf = await generateSingleTaskReportPdf(
//         task,
//         onProgress: (message) {
//           print('📊 تقدم التقرير: $message');
//         },
//         username: 'أحمد محمد',
//         includeFullHistory: true,
//       );
      
//       print('✅ تم إنشاء التقرير بنجاح!');
//       // يمكن حفظ أو عرض التقرير هنا
//       // await _savePdfToFile(pdf, 'simple_task_report.pdf');
//       print('📄 تم إنشاء التقرير بنجاح - ${pdf.toString().length} بايت');
      
//     } catch (e) {
//       print('❌ خطأ في إنشاء التقرير: $e');
//     }
//   }
  
//   /// مثال متقدم مع معالجة الأخطاء
//   static Future<void> generateAdvancedReport() async {
//     final task = _createComplexSampleTask();
    
//     try {
//       print('🚀 بدء إنشاء التقرير المتقدم...');
      
//       final pdf = await generateSingleTaskReportPdf(
//         task,
//         onProgress: (message) {
//           // يمكن ربط هذا بـ progress indicator في الواجهة
//           print('📈 $message');
//         },
//         username: 'مدير المشروع',
//         includeFullHistory: true,
//       );
      
//       // حفظ التقرير (مثال)
//       // await _savePdfToFile(pdf, 'task_${task.id}_report.pdf');
      
//       print('🎉 تم إنشاء التقرير المتقدم بنجاح!');
//       print('📊 حجم التقرير: ${pdf.toString().length} بايت');
      
//     } catch (e) {
//       print('💥 فشل في إنشاء التقرير: $e');
//       // يمكن إضافة معالجة خطأ مخصصة هنا
//     }
//   }
  
//   /// مثال على الاستخدام في Widget
//   static Widget buildReportButton(Task task) {
//     return ElevatedButton.icon(
//       onPressed: () async {
//         // عرض مؤشر التحميل
//         // showDialog(context: context, builder: (_) => LoadingDialog());
        
//         try {
//           final pdf = await generateSingleTaskReportPdf(
//             task,
//             onProgress: (message) {
//               // تحديث مؤشر التقدم
//               print(message);
//             },
//             username: 'المستخدم الحالي',
//           );
          
//           // إخفاء مؤشر التحميل
//           // Navigator.pop(context);
          
//           // عرض أو حفظ التقرير
//           // await _showOrSavePdf(pdf);
//           print('📄 تم إنشاء التقرير - ${pdf.toString().length} بايت');
          
//         } catch (e) {
//           // Navigator.pop(context);
//           // _showErrorDialog(context, e.toString());
//         }
//       },
//       icon: const Icon(Icons.picture_as_pdf),
//       label: const Text('إنشاء تقرير PDF'),
//       style: ElevatedButton.styleFrom(
//         backgroundColor: Colors.teal,
//         foregroundColor: Colors.white,
//       ),
//     );
//   }
  
//   /// إنشاء مهمة تجريبية بسيطة
//   static Task _createSampleTask() {
//     return Task(
//       id: 1001,
//       title: 'تطوير تطبيق إدارة المهام',
//       description: 'تطوير تطبيق شامل لإدارة المهام والمشاريع باستخدام Flutter',
//       creatorId: 1,
//       assigneeId: 2,
//       departmentId: 1,
//       createdAt: DateTime.now().subtract(const Duration(days: 10)).millisecondsSinceEpoch ~/ 1000,
//       startDate: DateTime.now().subtract(const Duration(days: 8)).millisecondsSinceEpoch ~/ 1000,
//       dueDate: DateTime.now().add(const Duration(days: 5)).millisecondsSinceEpoch ~/ 1000,
//       status: 'في التقدم',
//       priority: 'عالية',
//       completionPercentage: 75,
//       note: 'المهمة تسير وفق الخطة المحددة',
//       creator: User(
//         id: 1,
//         name: 'أحمد محمد',
//         email: '<EMAIL>',
//         role: UserRole.manager,
//         createdAt: DateTime.now().subtract(const Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
//       ),
//       assignee: User(
//         id: 2,
//         name: 'فاطمة علي',
//         email: '<EMAIL>',
//         role: UserRole.user,
//         createdAt: DateTime.now().subtract(const Duration(days: 20)).millisecondsSinceEpoch ~/ 1000,
//       ),
//       department: Department(
//         id: 1,
//         name: 'قسم تطوير البرمجيات',
//         description: 'قسم مختص بتطوير التطبيقات والأنظمة',
//         createdAt: DateTime.now().subtract(const Duration(days: 90)).millisecondsSinceEpoch ~/ 1000,
//       ),
//     );
//   }
  
//   /// إنشاء مهمة تجريبية معقدة
//   static Task _createComplexSampleTask() {
//     return Task(
//       id: 2001,
//       title: 'تطوير نظام إدارة المستشفى الشامل',
//       description: 'تطوير نظام متكامل لإدارة جميع عمليات المستشفى من المرضى والأطباء والمواعيد والفواتير',
//       creatorId: 1,
//       assigneeId: 3,
//       departmentId: 2,
//       createdAt: DateTime.now().subtract(const Duration(days: 30)).millisecondsSinceEpoch ~/ 1000,
//       startDate: DateTime.now().subtract(const Duration(days: 25)).millisecondsSinceEpoch ~/ 1000,
//       dueDate: DateTime.now().add(const Duration(days: 15)).millisecondsSinceEpoch ~/ 1000,
//       completedAt: null,
//       status: 'في التقدم',
//       priority: 'حرجة',
//       completionPercentage: 60,
//       estimatedTime: 2400, // 40 ساعة
//       actualTime: 1800, // 30 ساعة حتى الآن
//       note: 'مشروع استراتيجي يتطلب دقة عالية في التنفيذ',
//       incoming: 'طلب من إدارة المستشفى',
//       creator: User(
//         id: 1,
//         name: 'د. محمد الأحمد',
//         email: '<EMAIL>',
//         role: UserRole.admin,
//         createdAt: DateTime.now().subtract(const Duration(days: 60)).millisecondsSinceEpoch ~/ 1000,
//       ),
//       assignee: User(
//         id: 3,
//         name: 'سارة محمود',
//         email: '<EMAIL>',
//         role: UserRole.supervisor,
//         createdAt: DateTime.now().subtract(const Duration(days: 40)).millisecondsSinceEpoch ~/ 1000,
//       ),
//       department: Department(
//         id: 2,
//         name: 'قسم تقنية المعلومات الطبية',
//         description: 'قسم مختص بتطوير الأنظمة الطبية والصحية',
//         createdAt: DateTime.now().subtract(const Duration(days: 120)).millisecondsSinceEpoch ~/ 1000,
//       ),
//       // يمكن إضافة تعليقات ومرفقات ومهام فرعية هنا
//     );
//   }
// }

// /// مثال على كيفية دمج التقرير في صفحة Flutter
// class TaskReportPage extends StatefulWidget {
//   final Task task;
  
//   const TaskReportPage({super.key, required this.task});
  
//   @override
//   State<TaskReportPage> createState() => _TaskReportPageState();
// }

// class _TaskReportPageState extends State<TaskReportPage> {
//   bool _isGenerating = false;
//   String _progressMessage = '';
  
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('تقرير المهمة'),
//         backgroundColor: Colors.teal,
//         foregroundColor: Colors.white,
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             // معلومات المهمة
//             Card(
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       widget.task.title,
//                       style: Theme.of(context).textTheme.headlineSmall?.copyWith(
//                         fontWeight: FontWeight.bold,
//                         color: Colors.teal,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Text('رقم المهمة: ${widget.task.id}'),
//                     Text('الحالة: ${widget.task.status}'),
//                     Text('الأولوية: ${widget.task.priority}'),
//                     Text('نسبة الإنجاز: ${widget.task.completionPercentage}%'),
//                   ],
//                 ),
//               ),
//             ),
            
//             const SizedBox(height: 20),
            
//             // مؤشر التقدم
//             if (_isGenerating) ...[
//               const LinearProgressIndicator(
//                 backgroundColor: Colors.grey,
//                 valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
//               ),
//               const SizedBox(height: 10),
//               Text(
//                 _progressMessage,
//                 textAlign: TextAlign.center,
//                 style: const TextStyle(color: Colors.teal),
//               ),
//               const SizedBox(height: 20),
//             ],
            
//             // أزرار الإجراءات
//             ElevatedButton.icon(
//               onPressed: _isGenerating ? null : _generateReport,
//               icon: const Icon(Icons.picture_as_pdf),
//               label: Text(_isGenerating ? 'جاري إنشاء التقرير...' : 'إنشاء تقرير PDF'),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.teal,
//                 foregroundColor: Colors.white,
//                 padding: const EdgeInsets.symmetric(vertical: 12),
//               ),
//             ),
            
//             const SizedBox(height: 10),
            
//             OutlinedButton.icon(
//               onPressed: _isGenerating ? null : _generateSimpleReport,
//               icon: const Icon(Icons.description),
//               label: const Text('إنشاء تقرير مبسط'),
//               style: OutlinedButton.styleFrom(
//                 foregroundColor: Colors.teal,
//                 padding: const EdgeInsets.symmetric(vertical: 12),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
  
//   /// إنشاء التقرير الكامل
//   Future<void> _generateReport() async {
//     setState(() {
//       _isGenerating = true;
//       _progressMessage = 'بدء إنشاء التقرير...';
//     });
    
//     try {
//       final pdf = await generateSingleTaskReportPdf(
//         widget.task,
//         onProgress: (message) {
//           setState(() {
//             _progressMessage = message;
//           });
//         },
//         username: 'المستخدم الحالي', // يمكن الحصول عليه من الجلسة
//         includeFullHistory: true,
//       );
      
//       setState(() {
//         _progressMessage = 'تم إنشاء التقرير بنجاح!';
//       });
      
//       // هنا يمكن حفظ أو عرض التقرير
//       // await _savePdf(pdf);
//       print('📄 حجم التقرير الكامل: ${pdf.toString().length} بايت');
      
//       // إظهار رسالة نجاح
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(
//             content: Text('تم إنشاء التقرير بنجاح!'),
//             backgroundColor: Colors.green,
//           ),
//         );
//       }
      
//     } catch (e) {
//       setState(() {
//         _progressMessage = 'فشل في إنشاء التقرير: $e';
//       });
      
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('خطأ: $e'),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } finally {
//       setState(() {
//         _isGenerating = false;
//       });
//     }
//   }
  
//   /// إنشاء التقرير المبسط
//   Future<void> _generateSimpleReport() async {
//     setState(() {
//       _isGenerating = true;
//       _progressMessage = 'إنشاء تقرير مبسط...';
//     });
    
//     try {
//       final pdf = await generateSingleTaskReportPdf(
//         widget.task,
//         onProgress: (message) {
//           setState(() {
//             _progressMessage = message;
//           });
//         },
//         username: 'المستخدم الحالي',
//         includeFullHistory: false, // تقرير مبسط
//       );
      
//       setState(() {
//         _progressMessage = 'تم إنشاء التقرير المبسط!';
//       });
      
//       print('📄 حجم التقرير المبسط: ${pdf.toString().length} بايت');
      
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(
//             content: Text('تم إنشاء التقرير المبسط بنجاح!'),
//             backgroundColor: Colors.green,
//           ),
//         );
//       }
      
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('خطأ: $e'),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } finally {
//       setState(() {
//         _isGenerating = false;
//       });
//     }
//   }
// }

// /// مثال على كيفية الاستخدام في التطبيق الرئيسي
// void main() {
//   // مثال على الاستخدام البسيط
//   ReportUsageExample.generateSimpleReport();
  
//   // مثال على الاستخدام المتقدم
//   ReportUsageExample.generateAdvancedReport();
// }