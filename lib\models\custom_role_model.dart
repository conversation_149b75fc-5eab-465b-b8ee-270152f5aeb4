import 'package:flutter_application_2/models/custom_role_permission_model.dart';
import 'package:flutter_application_2/models/user_custom_role_model.dart';



/// نموذج الدور المخصص
class CustomRole {
  final int id;
  final String name;
  final String? description;
  final int? parentRoleId;
  final int? createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // العلاقات البرمجية
  final List<CustomRolePermission>? customRolePermissions;
  final List<UserCustomRole>? userCustomRoles;
  final CustomRole? parentRole;

  const CustomRole({
    required this.id,
    required this.name,
    this.description,
    this.parentRoleId,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.customRolePermissions,
    this.userCustomRoles,
    this.parentRole,
  });

  factory CustomRole.fromJson(Map<String, dynamic> json) {
    return CustomRole(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      parentRoleId: json['parentRoleId'] as int?,
      createdBy: json['createdBy'] as int?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      customRolePermissions: json['customRolePermissions'] != null
          ? (json['customRolePermissions'] as List)
              .map((p) => CustomRolePermission.fromJson(p as Map<String, dynamic>))
              .toList()
          : null,
      userCustomRoles: json['userCustomRoles'] != null
          ? (json['userCustomRoles'] as List)
              .map((u) => UserCustomRole.fromJson(u as Map<String, dynamic>))
              .toList()
          : null,
      parentRole: json['parentRole'] != null ? CustomRole.fromJson(json['parentRole']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'parentRoleId': parentRoleId,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'customRolePermissions': customRolePermissions?.map((p) => p.toJson()).toList(),
      'userCustomRoles': userCustomRoles?.map((u) => u.toJson()).toList(),
      'parentRole': parentRole?.toJson(),
    };
  }

  // إضافة دالة copyWith
  CustomRole copyWith({
    int? id,
    String? name,
    String? description,
    int? parentRoleId,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    List<CustomRolePermission>? customRolePermissions,
    List<UserCustomRole>? userCustomRoles,
    CustomRole? parentRole,
  }) {
    return CustomRole(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      parentRoleId: parentRoleId ?? this.parentRoleId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      customRolePermissions: customRolePermissions ?? this.customRolePermissions,
      userCustomRoles: userCustomRoles ?? this.userCustomRoles,
      parentRole: parentRole ?? this.parentRole,
    );
  }

  // إضافة factory لإنشاء دور جديد
  factory CustomRole.create({
    required String name,
    String? description,
    int? parentRoleId,
    int? createdBy,
  }) {
    return CustomRole(
      id: DateTime.now().millisecondsSinceEpoch, // مؤقت: id تلقائي
      name: name,
      description: description,
      parentRoleId: parentRoleId,
      createdBy: createdBy,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      updatedAt: null,
      isDeleted: false,
      customRolePermissions: const [],
      userCustomRoles: const [],
      parentRole: null,
    );
  }

  // إضافة isSystem (يمكنك تخصيص المنطق حسب الحاجة)
  bool get isSystem => id == 1; // مثال: الدور ذو id == 1 هو نظامي
}

// مستودع الأدوار المخصصة
class CustomRoleRepository {
  // قائمة أدوار وهمية (محاكاة قاعدة بيانات)
  static final List<CustomRole> _roles = [
    CustomRole(
      id: 1,
      name: 'مدير النظام',
      description: 'صلاحيات كاملة للنظام',
      createdAt: DateTime.now().millisecondsSinceEpoch,
      isDeleted: false,
    ),
    CustomRole(
      id: 2,
      name: 'مدير المشروع',
      description: 'إدارة المشاريع والمهام',
      createdAt: DateTime.now().millisecondsSinceEpoch,
      isDeleted: false,
    ),
    CustomRole(
      id: 3,
      name: 'موظف',
      description: 'صلاحيات أساسية للموظفين',
      createdAt: DateTime.now().millisecondsSinceEpoch,
      isDeleted: false,
    ),
  ];

  Future<List<CustomRole>> getAllRoles() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _roles.where((r) => !r.isDeleted).toList();
  }

  Future<void> createRole(CustomRole role) async {
    await Future.delayed(const Duration(milliseconds: 200));
    _roles.add(role);
  }

  Future<void> updateRole(CustomRole updatedRole) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final index = _roles.indexWhere((r) => r.id == updatedRole.id);
    if (index != -1) {
      _roles[index] = updatedRole;
    }
  }

  Future<void> deleteRole(int id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final index = _roles.indexWhere((r) => r.id == id);
    if (index != -1) {
      _roles[index] = _roles[index].copyWith(isDeleted: true);
    }
  }
}
