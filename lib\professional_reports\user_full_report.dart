import 'package:flutter/services.dart';
import 'package:flutter_application_2/services/api/task_api_service.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter_application_2/services/api/user_api_service.dart';
// تقرير المستخدمين الشامل
// هذا التقرير يعرض ملخص شامل لأداء المستخدمين في النظام، بما في ذلك المهام الموكلة إليهم وحالتها.
// دوال مساعدة محلية (بديلة إذا لم تتوفر من تقرير الأقسام)
PdfColor getStatusColor(String? status) {
  switch (status) {
    case 'completed':
    case 'approved':
      return PdfColors.green;
    case 'in_progress':
    case 'inProgress':
      return PdfColors.orange;
    case 'delayed':
      return PdfColors.red;
    case 'pending':
      return PdfColors.blueGrey;
    case 'waitingForInfo':
    case 'waiting_for_info':
      return PdfColors.green400;
    default:
      return PdfColors.grey;
  }
}

String translateStatus(String? status) {
  switch (status) {
    case 'pending':
      return 'قيد الانتظار';
    case 'in_progress':
      return 'قيد التنفيذ';
    case 'completed':
      return 'منجزة';
    case 'cancelled':
      return 'ملغاة';
    case 'waitingForInfo':
    case 'waiting_for_info':
      return 'في انتظار معلومات';
    case 'inProgress':
      return 'قيد التنفيذ';
    case 'delayed':
      return 'متأخرة';
    default:
      return status ?? '-';
  }
}

String formatDateField(dynamic value) {
  if (value == null) return '-';
  if (value is int) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(value * 1000);
      return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
    } catch (_) {
      return value.toString();
    }
  }
  if (value is String && value.length > 6 && int.tryParse(value) != null) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(int.parse(value) * 1000);
      return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
    } catch (_) {
      return value;
    }
  }
  return value.toString();
}

// ملخص ملون وجذاب للأداء
pw.Widget buildColoredSummaryBox({
  required String title,
  required String icon,
  required PdfColor color,
  required String note,
  required int total,
  required int completed,
  required int delayed,
  required int inProgress,
  required int inwait,
  required int pending,
  required int percent,
  required String performanceNote,
  required final font,
  required String username,
}) {
  return pw.Container(
    margin: const pw.EdgeInsets.only(top: 8, bottom: 8),
    padding: const pw.EdgeInsets.all(10),
    decoration: pw.BoxDecoration(
      color: color,
      borderRadius: pw.BorderRadius.circular(8),
      border: pw.Border.all(color: PdfColors.blueGrey, width: 0.7),
    ),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          children: [
            pw.Text(icon,
                style: pw.TextStyle(
                    font: font, fontSize: 18, color: PdfColors.blueGrey)),
            pw.Text('$title :- ',
                style: pw.TextStyle(
                    font: font, fontSize: 15, color: PdfColors.blue900)),
            pw.Text(username,
                style: pw.TextStyle(
                    font: font, fontSize: 15, color: PdfColors.black)),
          ],
        ),
        // pw.SizedBox(width: 5),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // pw.SizedBox(height: 2),
            pw.Row(children: [
               pw.SizedBox(height: 12),
            pw.Text('ملخص الأداء:',
                style: pw.TextStyle(
                    font:font, fontWeight: pw.FontWeight.bold,fontSize: 15,color: PdfColors.blue900)),
              pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blueAccent,
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('اجمالي عدد المهام  : $total',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
              pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: getStatusColor('completed'),
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('منجزة: $completed',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
              pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: getStatusColor('waiting_for_info'),
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('في انتظار معلومات: $inwait',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
              pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: getStatusColor('pending'),
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('قيد الانتظار: $pending',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
              pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: getStatusColor('in_progress'),
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('قيد التنفيذ: $inProgress',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
            ]),
            // pw.SizedBox(
            //   height: 3,
            // ),
            pw.Align(
              alignment: pw.Alignment.center,
              child: pw.Container(
                margin: const pw.EdgeInsets.only(right: 8),
                padding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: pw.BoxDecoration(
                  color: percent < 50 ? PdfColors.red : PdfColors.green, // تعديل اللون حسب النسبة
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text('نسبة الإنجاز: $percent %',
                    style: pw.TextStyle(font: font, color: PdfColors.white)),
              ),
            ),
           
            pw.Text('- $performanceNote',
                style: pw.TextStyle(
                    font: font, fontSize: 11, color: PdfColors.indigo)),
            pw.Text(note, style: pw.TextStyle(font: font, fontSize: 10)),
          ],
        ),
      ],
    ),
  );
}

/// دالة توليد تقرير المستخدمين الشامل PDF
Future<pw.Document> generateUsersFullPdfReport({List<String>? userIds}) async {
  final pdf = pw.Document();
  // تحميل الخطوط العربية
  final fontData =
      await rootBundle.load('assets/fonts/NotoNaskhArabic-Regular.ttf');
  final boldFontData =
      await rootBundle.load('assets/fonts/NotoNaskhArabic-Bold.ttf');
  final arabicFont = pw.Font.ttf(fontData);
  final arabicFontBold = pw.Font.ttf(boldFontData);

  // تحميل خط لاتيني يدعم الرموز الشجرية
  final latinFontData =
      await rootBundle.load('assets/fonts/LiberationSans-Regular.ttf');
  final latinFont = pw.Font.ttf(latinFontData);
  // جلب جميع المستخدمين
  final allUsers = await UserApiService().getAllUsers();
  // إذا تم تمرير userIds، نفلتر المستخدمين بناءً عليها
  final users = userIds == null || userIds.isEmpty
      ? allUsers
      : allUsers.where((u) => userIds.contains(u.id.toString())).toList();

  final List<pw.Widget> userPages = [];

  for (final user in users) {
    // جلب المهام الحقيقية للمستخدم من API
    final userTasks = await TaskApiService().getTasksByAssignee(user.id);
    // حساب ملخص الأداء
    final total = userTasks.length;
    final completed = userTasks
        .where((t) => t.status == 'completed' || t.status == 'approved')
        .length;
    final delayed = userTasks.where((t) => t.status == 'delayed').length;
    final pending = userTasks.where((t) => t.status == 'pending').length;
    final inwait = userTasks
        .where((t) =>
            t.status == 'waiting_for_info' || t.status == 'waitingForInfo')
        .length;
    final inProgress = userTasks
        .where((t) => t.status == 'in_progress' || t.status == 'inProgress')
        .length;
    final percent = total > 0 ? ((completed / total) * 100).round() : 0;
    String performanceNote = '';
    String symbol = '';
    PdfColor bgColor = PdfColors.white;
    if (percent >= 80 && delayed == 0) {
      performanceNote = 'الأداء ممتاز، لا توجد مهام متأخرة.';
      symbol = '★';
      bgColor = PdfColors.lightGreen100;
    } else if (percent >= 60) {
      performanceNote = 'الأداء جيد، يفضل معالجة المهام المتأخرة.';
      symbol = '✓';
      bgColor = PdfColors.yellow100;
    } else {
      performanceNote = 'هناك حاجة لتحسين الأداء وتسريع إنجاز المهام.';
      symbol = '!';
      bgColor = PdfColors.red100;
    }

    final summaryBox = buildColoredSummaryBox(
      title: 'بيانات المستخدم',
      icon: symbol,
      color: bgColor,
      note: '',
      total: total,
      completed: completed,
      delayed: delayed,
      inProgress: inProgress,
      percent: percent,
      performanceNote: performanceNote,
      font: arabicFontBold,
      inwait: inwait,
      pending: pending,
      username: user.name.toString(),
    );

    userPages.add(
      pw.Container(
        margin: const pw.EdgeInsets.only(bottom: 24),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
           
            pw.SizedBox(height: 8),
            summaryBox,
           
            pw.Row(
              children: [],
            ),
            pw.SizedBox(height: 12),
            pw.Text('المهام:',
                style: pw.TextStyle(
                    font: arabicFontBold, fontWeight: pw.FontWeight.bold)),
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.blueGrey, width: 0.7),
              columnWidths: {
                0: const pw.FlexColumnWidth(3), // العنوان
                1: const pw.FlexColumnWidth(2), // الحالة
                2: const pw.FlexColumnWidth(2), // تاريخ البداية
                3: const pw.FlexColumnWidth(2), // تاريخ النهاية
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('العنوان',
                          style: pw.TextStyle(
                              font: arabicFontBold,
                              fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('الحالة',
                          style: pw.TextStyle(
                              font: arabicFontBold,
                              fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('تاريخ البداية',
                          style: pw.TextStyle(
                              font: arabicFontBold,
                              fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('تاريخ النهاية',
                          style: pw.TextStyle(
                              font: arabicFontBold,
                              fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('رقم المهمة',
                          style: pw.TextStyle(
                              font: arabicFontBold,
                              fontWeight: pw.FontWeight.bold)),
                    ),
                  ],
                ),
                ...userTasks.asMap().entries.map((entry) {
                  final task = entry.value;
                  return pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(task.title ?? '-',
                            style: pw.TextStyle(font: arabicFont)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(translateStatus(task.status),
                            style: pw.TextStyle(font: arabicFont)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(formatDateField(task.startDate),
                            style: pw.TextStyle(font: arabicFont)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(formatDateField(task.dueDateDateTime),
                            style: pw.TextStyle(font: arabicFont)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text('${task.id}',
                            style: pw.TextStyle(font: arabicFont)),
                      ),
                    ],
                  );
                }),
              ],
            ),
            // يمكن إضافة ملخص المشاركات أو بيانات إضافية هنا
            pw.SizedBox(height: 18),
            pw.Divider(
              color: PdfColors.black,
              height: 2,
            )
          ],
        ),
      ),
    );
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      build: (context) => [
        pw.Center(
          child: pw.Text(
            'تقرير المستخدمين الشامل',
            style: pw.TextStyle(
                font: arabicFontBold,
                fontSize: 20,
                color: PdfColors.blue900,
                fontWeight: pw.FontWeight.bold),
          ),
        ),
        pw.SizedBox(height: 16),
        ...userPages,
      ],
      footer: (context) => pw.Container(
        alignment: pw.Alignment.center,
        margin: const pw.EdgeInsets.only(top: 8),
        child: pw.Text(
          'صفحة ${context.pageNumber} من ${context.pagesCount}',
          style: pw.TextStyle(font: arabicFontBold, fontSize: 12, color: PdfColors.grey800),
        ),
      ),
    ),
  );

  return pdf;
}

// إضافة دالة جلب مهام المستخدم في UserApiService
// extension UserApiServiceTasks on UserApiService {

//   static Future<List<dynamic>> getUserTasks(int userId) async {
//     final api = TaskApiService();
//     final response = await api.getTasksByAssignee(userId);
//     // عدّل حسب هيكلية البيانات الفعلية للمهام
//     return response.data is List ? response.data : [];
//   }
// }
