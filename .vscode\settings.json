{"java.debug.settings.onBuildFailureProceed": true, "gradle.javaDebug.cleanOutput": true, "java.configuration.updateBuildConfiguration": "interactive", "hdinsightSpark.NET.JAVA_HOME": "C:\\Program Files\\Java\\jdk-23", "java.configuration.runtimes": [{"name": "JavaSE-23", "path": "C:\\Program Files\\Java\\jdk-23", "default": true}], "dart.flutterSdkPath": "C:\\flutter", "dart.sdkPath": "C:\\flutter\\bin\\cache\\dart-sdk", "dart.flutterSdkPaths": ["C:\\flutter"], "flutterTools.displayGetxContextMenu": true, "flutterTools.displayMobxContextMenu": false, "flutterTools.displayRiverpodContextMenu": false, "flutterTools.displayModularContextMenu": false, "zencoder.enableRepoIndexing": true, "geminicodeassist.updateChannel": "Insiders", "zencoder.repoInfo.lastCheckTime": 1751670983206, "zencoder.repoInfo.currentWarningState": false}