import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/admin_controller.dart';
import '../../services/unified_permission_service.dart';
import '../../widgets/animations/enhanced_animations.dart';
import '../../utils/admin_debug_helper.dart';
import 'shared/admin_card_widget.dart';
import 'users/user_management_screen.dart';
import 'roles/role_management_screen.dart';
import 'permissions/permission_management_screen.dart';
import 'system/system_settings_screen.dart';
import 'system/backup_management_screen.dart';
import 'reports/reports_management_screen.dart';
import 'export_import_screen.dart';

/// لوحة التحكم الإدارية المحسنة
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final AuthController _authController = Get.find<AuthController>();
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _debugPermissions();
  }

  /// تشخيص الصلاحيات للمساعدة في حل المشاكل
  void _debugPermissions() {
    debugPrint('🔍 تشخيص صلاحيات لوحة التحكم الإدارية:');
    debugPrint('- canAccessAdmin: ${_permissionService.canAccessAdmin()}');
    debugPrint('- canViewAllUsers: ${_permissionService.canViewAllUsers()}');
    debugPrint('- canManagePermissions: ${_permissionService.canManagePermissions()}');
    debugPrint('- canBackupSystem: ${_permissionService.canBackupSystem()}');
    debugPrint('- إجمالي صلاحيات المستخدم: ${_permissionService.userPermissions.length}');

    // طباعة أول 10 صلاحيات للمراجعة
    final permissions = _permissionService.userPermissions.entries.take(10);
    for (final permission in permissions) {
      debugPrint('  - ${permission.key}: ${permission.value}');
    }

    // تشخيص شامل باستخدام أداة التشخيص
    AdminDebugHelper.diagnoseAdminIssues();
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    try {
      // تحميل صلاحيات المستخدم الحالي أولاً
      await _permissionService.refreshCurrentUserPermissions();

      // ثم تحميل باقي البيانات
      await _adminController.loadUsers();
      await _adminController.loadRoles();
      await _adminController.loadPermissions();

      debugPrint('✅ تم تحميل جميع بيانات لوحة التحكم بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات لوحة التحكم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔄 بناء AdminDashboardScreen...');
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم الإدارية'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // زر تشخيص المشاكل (محمي بصلاحية الاختبار)
          if (_permissionService.canAccessTesting())
            IconButton(
              icon: const Icon(Icons.bug_report, color: Colors.white),
              tooltip: 'تشخيص مشاكل الصلاحيات',
              onPressed: () {
                AdminDebugHelper.diagnoseAdminIssues();
                AdminDebugHelper.printAllPermissions();
              },
            ),

          // معلومات المستخدم الحالي
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Obx(() {
              final user = _authController.currentUser.value;
              return Row(
                children: [
                  Text(
                    user?.name ?? 'مستخدم',
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(width: 8),
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.white,
                    child: Text(
                      user?.name != null && user!.name.isNotEmpty ? user.name.substring(0, 1) : 'م',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: FadeInAnimation(
            duration: const Duration(milliseconds: 800),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // إحصائيات سريعة
                SlideUpAnimation(
                  delay: const Duration(milliseconds: 200),
                  child: _buildStatsSection(),
                ),

                const SizedBox(height: 24),

                // قسم إدارة المستخدمين
                SlideUpAnimation(
                  delay: const Duration(milliseconds: 400),
                  child: _buildUsersSection(),
                ),

                const SizedBox(height: 16),

                // قسم إدارة الأدوار والصلاحيات
                SlideUpAnimation(
                  delay: const Duration(milliseconds: 600),
                  child: _buildRolesPermissionsSection(),
                ),

                const SizedBox(height: 16),

                // قسم إدارة النظام
                SlideUpAnimation(
                  delay: const Duration(milliseconds: 800),
                  child: _buildSystemSection(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          return Row(
            children: [
              Expanded(
                child: AdminStatsCard(
                  title: 'المستخدمين',
                  value: '${_adminController.users.length}',
                  icon: Icons.people,
                  color: Colors.blue,
                  subtitle: 'إجمالي المستخدمين',
                  onTap: _permissionService.canViewAllUsers() 
                    ? () => Get.to(() => const UserManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الأدوار',
                  value: '${_adminController.roles.length}',
                  icon: Icons.security,
                  color: Colors.green,
                  subtitle: 'الأدوار النشطة',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const RoleManagementScreen())
                    : null,
                ),
              ),
              Expanded(
                child: AdminStatsCard(
                  title: 'الصلاحيات',
                  value: '${_adminController.permissions.length}',
                  icon: Icons.vpn_key,
                  color: Colors.orange,
                  subtitle: 'إجمالي الصلاحيات',
                  onTap: _permissionService.canManagePermissions()
                    ? () => Get.to(() => const PermissionManagementScreen())
                    : null,
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء قسم إدارة المستخدمين
  Widget _buildUsersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة المستخدمين',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة المستخدمين',
          subtitle: 'إضافة وتعديل وحذف المستخدمين',
          icon: Icons.people_outline,
          isEnabled: _permissionService.canAccessAdmin(),
          onTap: _permissionService.canAccessAdmin() ? () {
            debugPrint('🔄 محاولة الانتقال إلى إدارة المستخدمين...');
            Get.to(() => const UserManagementScreen());
          } : null,
        ),
      ],
    );
  }

  /// بناء قسم إدارة الأدوار والصلاحيات
  Widget _buildRolesPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأدوار والصلاحيات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إدارة الأدوار',
          subtitle: 'إنشاء وتعديل الأدوار الافتراضية والمخصصة',
          icon: Icons.admin_panel_settings,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: _permissionService.canManagePermissions() ? () {
            debugPrint('🔄 محاولة الانتقال إلى إدارة الأدوار...');
            Get.to(() => const RoleManagementScreen());
          } : null,
        ),
        AdminCardWidget(
          title: 'إدارة الصلاحيات',
          subtitle: 'منح وسحب الصلاحيات للمستخدمين والأدوار',
          icon: Icons.security,
          isEnabled: _permissionService.canManagePermissions(),
          onTap: _permissionService.canManagePermissions() ? () {
            debugPrint('🔄 محاولة الانتقال إلى إدارة الصلاحيات...');
            Get.to(() => const PermissionManagementScreen());
          } : null,
        ),
      ],
    );
  }

  /// بناء قسم إدارة النظام
  Widget _buildSystemSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة النظام',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        AdminCardWidget(
          title: 'إعدادات النظام',
          subtitle: 'إعدادات عامة وأمان النظام',
          icon: Icons.settings,
          isEnabled: _permissionService.canManageSettings(),
          onTap: _permissionService.canManageSettings() ? () {
            debugPrint('🔄 محاولة الانتقال إلى إعدادات النظام...');
            Get.to(() => const SystemSettingsScreen());
          } : null,
        ),
        AdminCardWidget(
          title: 'النسخ الاحتياطية',
          subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
          icon: Icons.backup,
          isEnabled: _permissionService.canBackupSystem(),
          onTap: _permissionService.canBackupSystem() ? () {
            debugPrint('🔄 محاولة الانتقال إلى النسخ الاحتياطية...');
            _showBackupManagement();
          } : null,
        ),
        AdminCardWidget(
          title: 'التقارير والإحصائيات',
          subtitle: 'تقارير النشاط والاستخدام',
          icon: Icons.analytics,
          isEnabled: _permissionService.canViewReports(), // 🔒 إصلاح ثغرة أمنية
          onTap: _permissionService.canViewReports() ? () {
            debugPrint('🔄 محاولة الانتقال إلى التقارير والإحصائيات...');
            _showReportsManagement();
          } : null,
        ),
        AdminCardWidget(
          title: 'التصدير والاستيراد',
          subtitle: 'تصدير واستيراد البيانات بصيغ مختلفة',
          icon: Icons.import_export,
          isEnabled: _permissionService.canManageSystem(), // 🔒 إصلاح ثغرة أمنية
          onTap: _permissionService.canManageSystem() ? () {
            debugPrint('🔄 محاولة الانتقال إلى التصدير والاستيراد...');
            Get.to(() => const ExportImportScreen());
          } : null,
        ),
      ],
    );
  }

  /// عرض إدارة النسخ الاحتياطية
  void _showBackupManagement() {
    Get.to(() => const BackupManagementScreen());
  }

  /// عرض إدارة التقارير
  void _showReportsManagement() {
    Get.to(() => const ReportsManagementScreen());
  }
}
