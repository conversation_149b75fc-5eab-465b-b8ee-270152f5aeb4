import 'package:flutter/material.dart';
import 'table_export_manager.dart';

/// جدول قابل للتحرير للاستخدام في محررات المستندات
class EditableTableWidget extends StatefulWidget {
  final int initialRows;
  final int initialColumns;
  final bool hasHeader;
  final List<List<String>>? initialData;
  final Function(List<List<String>>)? onDataChanged;
  final bool isReadOnly;

  const EditableTableWidget({
    super.key,
    required this.initialRows,
    required this.initialColumns,
    this.hasHeader = true,
    this.initialData,
    this.onDataChanged,
    this.isReadOnly = false,
  });

  @override
  State<EditableTableWidget> createState() => _EditableTableWidgetState();
}

class _EditableTableWidgetState extends State<EditableTableWidget> {
  late List<List<TextEditingController>> _controllers;
  late List<List<String>> _data;
  int _rows = 0;
  int _columns = 0;
  bool _hasHeader = true;

  @override
  void initState() {
    super.initState();
    _rows = widget.initialRows;
    _columns = widget.initialColumns;
    _hasHeader = widget.hasHeader;
    _initializeTable();
  }

  void _initializeTable() {
    _controllers = [];
    _data = [];

    for (int row = 0; row < _rows; row++) {
      final rowControllers = <TextEditingController>[];
      final rowData = <String>[];

      for (int col = 0; col < _columns; col++) {
        String initialValue = '';
        
        // استخدام البيانات الأولية إذا كانت متوفرة
        if (widget.initialData != null && 
            row < widget.initialData!.length && 
            col < widget.initialData![row].length) {
          initialValue = widget.initialData![row][col];
        } else {
          // إنشاء محتوى افتراضي
          if (_hasHeader && row == 0) {
            initialValue = 'عنوان ${col + 1}';
          } else {
            initialValue = 'خلية ${row + 1}-${col + 1}';
          }
        }

        final controller = TextEditingController(text: initialValue);
        controller.addListener(() => _onCellChanged(row, col, controller.text));
        
        rowControllers.add(controller);
        rowData.add(initialValue);
      }

      _controllers.add(rowControllers);
      _data.add(rowData);
    }
  }

  void _onCellChanged(int row, int col, String value) {
    if (row < _data.length && col < _data[row].length) {
      _data[row][col] = value;
      widget.onDataChanged?.call(_data);
    }
  }

  void _addRow() {
    setState(() {
      _rows++;
      final newRowControllers = <TextEditingController>[];
      final newRowData = <String>[];

      for (int col = 0; col < _columns; col++) {
        final initialValue = 'خلية $_rows-${col + 1}';
        final controller = TextEditingController(text: initialValue);
        controller.addListener(() => _onCellChanged(_rows - 1, col, controller.text));
        
        newRowControllers.add(controller);
        newRowData.add(initialValue);
      }

      _controllers.add(newRowControllers);
      _data.add(newRowData);
    });
  }

  void _addColumn() {
    setState(() {
      _columns++;
      
      for (int row = 0; row < _rows; row++) {
        String initialValue;
        if (_hasHeader && row == 0) {
          initialValue = 'عنوان $_columns';
        } else {
          initialValue = 'خلية ${row + 1}-$_columns';
        }
        
        final controller = TextEditingController(text: initialValue);
        controller.addListener(() => _onCellChanged(row, _columns - 1, controller.text));
        
        _controllers[row].add(controller);
        _data[row].add(initialValue);
      }
    });
  }

  void _removeRow() {
    if (_rows > 1) {
      setState(() {
        // تحرير controllers للصف الأخير
        for (final controller in _controllers.last) {
          controller.dispose();
        }
        
        _controllers.removeLast();
        _data.removeLast();
        _rows--;
      });
    }
  }

  void _removeColumn() {
    if (_columns > 1) {
      setState(() {
        for (int row = 0; row < _rows; row++) {
          _controllers[row].last.dispose();
          _controllers[row].removeLast();
          _data[row].removeLast();
        }
        _columns--;
      });
    }
  }

  @override
  void dispose() {
    // تحرير جميع controllers
    for (final rowControllers in _controllers) {
      for (final controller in rowControllers) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // شريط أدوات الجدول
        if (!widget.isReadOnly) _buildTableToolbar(),
        
        const SizedBox(height: 8),
        
        // الجدول
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8),
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              border: TableBorder.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
              headingRowColor: _hasHeader
                  ? WidgetStateProperty.all(Colors.blue.shade50)
                  : null,
              columns: List.generate(_columns, (index) => DataColumn(
                label: Container(
                  width: 120,
                  child: _hasHeader 
                      ? _buildCell(0, index, isHeader: true)
                      : Text('عمود ${index + 1}'),
                ),
              )),
              rows: List.generate(
                _hasHeader ? _rows - 1 : _rows, 
                (rowIndex) {
                  final actualRowIndex = _hasHeader ? rowIndex + 1 : rowIndex;
                  return DataRow(
                    cells: List.generate(_columns, (colIndex) => DataCell(
                      Container(
                        width: 120,
                        child: _buildCell(actualRowIndex, colIndex),
                      ),
                    )),
                  );
                },
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // معلومات الجدول
        Text(
          'الجدول: $_rows صف × $_columns عمود',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildTableToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Wrap(
        spacing: 8,
        children: [
          _buildToolbarButton(
            icon: Icons.add,
            label: 'إضافة صف',
            onPressed: _addRow,
          ),
          _buildToolbarButton(
            icon: Icons.remove,
            label: 'حذف صف',
            onPressed: _rows > 1 ? _removeRow : null,
          ),
          _buildToolbarButton(
            icon: Icons.view_column,
            label: 'إضافة عمود',
            onPressed: _addColumn,
          ),
          _buildToolbarButton(
            icon: Icons.view_column_outlined,
            label: 'حذف عمود',
            onPressed: _columns > 1 ? _removeColumn : null,
          ),
          _buildToolbarButton(
            icon: Icons.download,
            label: 'تصدير',
            onPressed: _showExportDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label, style: const TextStyle(fontSize: 12)),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        minimumSize: const Size(0, 32),
      ),
    );
  }

  Widget _buildCell(int row, int col, {bool isHeader = false}) {
    if (row >= _controllers.length || col >= _controllers[row].length) {
      return const Text('خطأ');
    }

    return TextField(
      controller: _controllers[row][col],
      enabled: !widget.isReadOnly,
      style: TextStyle(
        fontSize: 14,
        fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
      ),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(8),
        isDense: true,
      ),
      textAlign: TextAlign.center,
      onSubmitted: (value) {
        // الانتقال للخلية التالية عند الضغط على Enter
        _focusNextCell(row, col);
      },
    );
  }

  void _focusNextCell(int currentRow, int currentCol) {
    int nextRow = currentRow;
    int nextCol = currentCol + 1;

    if (nextCol >= _columns) {
      nextCol = 0;
      nextRow++;
    }

    if (nextRow < _rows && nextCol < _columns) {
      FocusScope.of(context).requestFocus(
        FocusNode()..requestFocus(),
      );
    }
  }

  void _showExportDialog() {
    TableExportManager.showExportDialog(
      context,
      _data,
      hasHeader: _hasHeader,
    );
  }

  /// الحصول على بيانات الجدول
  List<List<String>> get tableData => _data;

  /// تحديث بيانات الجدول
  void updateTableData(List<List<String>> newData) {
    setState(() {
      // تحرير controllers القديمة
      for (final rowControllers in _controllers) {
        for (final controller in rowControllers) {
          controller.dispose();
        }
      }

      _data = newData;
      _rows = newData.length;
      _columns = newData.isNotEmpty ? newData[0].length : 0;
      _initializeTable();
    });
  }
}
