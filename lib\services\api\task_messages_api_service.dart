import 'package:flutter/foundation.dart';
import '../../models/task_message_models.dart';
import 'api_service.dart';

/// خدمة API لرسائل المهام - للمحادثات الفورية المرتبطة بالمهام
class TaskMessagesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على رسائل مهمة محددة
  Future<List<TaskMessageResponse>> getTaskMessages(
    int taskId, {
    int page = 1,
    int pageSize = 50,
  }) async {
    try {
      final response = await _apiService.get(
        '/api/TaskMessages/task/$taskId?page=$page&pageSize=$pageSize',
      );
      return _apiService.handleListResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على رسائل المهمة $taskId: $e');
      rethrow;
    }
  }

  /// إرسال رسالة جديدة للمهمة
  Future<TaskMessageResponse> sendMessage(SendTaskMessageRequest request) async {
    try {
      final response = await _apiService.post(
        '/api/TaskMessages',
        request.toJson(),
      );
      return _apiService.handleResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إرسال رسالة للمهمة ${request.taskId}: $e');
      rethrow;
    }
  }

  /// الحصول على رسالة محددة
  Future<TaskMessageResponse> getMessage(int messageId) async {
    try {
      final response = await _apiService.get('/api/TaskMessages/$messageId');
      return _apiService.handleResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسالة $messageId: $e');
      rethrow;
    }
  }

  /// تحديث رسالة موجودة
  Future<TaskMessageResponse> updateMessage(
    int messageId,
    UpdateTaskMessageRequest request,
  ) async {
    try {
      final response = await _apiService.put(
        '/api/TaskMessages/$messageId',
        request.toJson(),
      );
      return _apiService.handleResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الرسالة $messageId: $e');
      rethrow;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int messageId) async {
    try {
      final response = await _apiService.delete('/api/TaskMessages/$messageId');
      return response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في حذف الرسالة $messageId: $e');
      return false;
    }
  }

  /// تثبيت أو إلغاء تثبيت رسالة
  Future<TaskMessageResponse> pinMessage(
    int messageId,
    bool isPinned,
  ) async {
    try {
      final response = await _apiService.patch(
        '/api/TaskMessages/$messageId/pin',
        {'isPinned': isPinned},
      );
      return _apiService.handleResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تثبيت الرسالة $messageId: $e');
      rethrow;
    }
  }

  /// تحديد رسالة للمتابعة
  Future<TaskMessageResponse> markForFollowUp(
    int messageId,
    bool isMarkedForFollowUp, {
    int? followUpAt,
  }) async {
    try {
      final response = await _apiService.patch(
        '/api/TaskMessages/$messageId/follow-up',
        {
          'isMarkedForFollowUp': isMarkedForFollowUp,
          if (followUpAt != null) 'followUpAt': followUpAt,
        },
      );
      return _apiService.handleResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة للمتابعة $messageId: $e');
      rethrow;
    }
  }

  /// تحديد رسالة كمقروءة
  Future<bool> markAsRead(int messageId) async {
    try {
      final response = await _apiService.post('/api/TaskMessages/$messageId/read', {});
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة كمقروءة $messageId: $e');
      return false;
    }
  }

  /// الحصول على الرسائل المثبتة لمهمة
  Future<List<TaskMessageResponse>> getPinnedMessages(int taskId) async {
    try {
      final response = await _apiService.get('/api/TaskMessages/task/$taskId/pinned');
      return _apiService.handleListResponse<TaskMessageResponse>(
        response,
        (json) => TaskMessageResponse.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل المثبتة للمهمة $taskId: $e');
      return [];
    }
  }
}

/// نموذج استجابة رسالة المهمة مع تفاصيل إضافية
class TaskMessageResponse {
  final int id;
  final int taskId;
  final int senderId;
  final String senderName;
  final String content;
  final int contentType;
  final int? replyToMessageId;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isPinned;
  final int? pinnedAt;
  final String? pinnedByName;
  final int priority;
  final bool isMarkedForFollowUp;
  final int? followUpAt;
  final String? markedForFollowUpByName;
  final bool isEdited;
  final List<int>? mentionedUserIds;
  final List<int>? attachmentIds;
  final int readByCount;
  final int totalRecipients;
  final bool isReadByCurrentUser;
  final TaskMessageResponse? replyToMessage;

  const TaskMessageResponse({
    required this.id,
    required this.taskId,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.contentType,
    this.replyToMessageId,
    required this.createdAt,
    this.updatedAt,
    required this.isDeleted,
    required this.isPinned,
    this.pinnedAt,
    this.pinnedByName,
    required this.priority,
    required this.isMarkedForFollowUp,
    this.followUpAt,
    this.markedForFollowUpByName,
    required this.isEdited,
    this.mentionedUserIds,
    this.attachmentIds,
    required this.readByCount,
    required this.totalRecipients,
    required this.isReadByCurrentUser,
    this.replyToMessage,
  });

  factory TaskMessageResponse.fromJson(Map<String, dynamic> json) {
    return TaskMessageResponse(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      senderId: json['senderId'] as int,
      senderName: json['senderName'] as String,
      content: json['content'] as String,
      contentType: json['contentType'] as int,
      replyToMessageId: json['replyToMessageId'] as int?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool,
      isPinned: json['isPinned'] as bool,
      pinnedAt: json['pinnedAt'] as int?,
      pinnedByName: json['pinnedByName'] as String?,
      priority: json['priority'] as int,
      isMarkedForFollowUp: json['isMarkedForFollowUp'] as bool,
      followUpAt: json['followUpAt'] as int?,
      markedForFollowUpByName: json['markedForFollowUpByName'] as String?,
      isEdited: json['isEdited'] as bool,
      mentionedUserIds: json['mentionedUserIds'] != null
          ? List<int>.from(json['mentionedUserIds'] as List)
          : null,
      attachmentIds: json['attachmentIds'] != null
          ? List<int>.from(json['attachmentIds'] as List)
          : null,
      readByCount: json['readByCount'] as int,
      totalRecipients: json['totalRecipients'] as int,
      isReadByCurrentUser: json['isReadByCurrentUser'] as bool,
      replyToMessage: json['replyToMessage'] != null
          ? TaskMessageResponse.fromJson(json['replyToMessage'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'senderId': senderId,
      'senderName': senderName,
      'content': content,
      'contentType': contentType,
      'replyToMessageId': replyToMessageId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isPinned': isPinned,
      'pinnedAt': pinnedAt,
      'pinnedByName': pinnedByName,
      'priority': priority,
      'isMarkedForFollowUp': isMarkedForFollowUp,
      'followUpAt': followUpAt,
      'markedForFollowUpByName': markedForFollowUpByName,
      'isEdited': isEdited,
      'mentionedUserIds': mentionedUserIds,
      'attachmentIds': attachmentIds,
      'readByCount': readByCount,
      'totalRecipients': totalRecipients,
      'isReadByCurrentUser': isReadByCurrentUser,
      'replyToMessage': replyToMessage?.toJson(),
    };
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على نسبة القراءة
  double get readPercentage => totalRecipients > 0 
      ? (readByCount / totalRecipients) * 100 
      : 0.0;

  @override
  String toString() {
    return 'TaskMessageResponse(id: $id, taskId: $taskId, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskMessageResponse && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
