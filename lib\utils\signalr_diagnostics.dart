import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../services/unified_signalr_service.dart';

/// أداة تشخيص SignalR
class SignalRDiagnostics {
  static const String _baseUrl = 'http://localhost:7111';

  /// فحص شامل لحالة SignalR
  static Future<Map<String, dynamic>> runFullDiagnostics() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🔍 بدء تشخيص SignalR الشامل...');
      
      // 1. فحص حالة الخادم
      results['server'] = await _checkServerHealth();
      
      // 2. فحص حالة SignalR Hubs
      results['signalr_service'] = await _checkSignalRService();
      
      // 3. فحص الاتصال بكل Hub
      results['hubs'] = await _checkIndividualHubs();
      
      // 4. فحص CORS
      results['cors'] = await _checkCORS();
      
      // 5. تحليل النتائج
      results['analysis'] = _analyzeResults(results);
      
      debugPrint('✅ انتهى تشخيص SignalR');
      return results;
      
    } catch (e) {
      debugPrint('❌ خطأ في تشخيص SignalR: $e');
      results['error'] = e.toString();
      return results;
    }
  }

  /// فحص حالة الخادم
  static Future<Map<String, dynamic>> _checkServerHealth() async {
    try {
      debugPrint('🌐 فحص حالة الخادم...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/api/Diagnostics/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'status': 'healthy',
          'data': data,
          'response_time': response.headers['x-response-time'] ?? 'unknown',
        };
      } else {
        return {
          'status': 'unhealthy',
          'status_code': response.statusCode,
          'body': response.body,
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }

  /// فحص حالة خدمة SignalR
  static Future<Map<String, dynamic>> _checkSignalRService() async {
    try {
      final signalRService = Get.find<UnifiedSignalRService>();
      
      return {
        'service_found': true,
        'chat_hub_connected': signalRService.isChatHubConnected,
        'task_hub_connected': signalRService.isTaskHubConnected,
        'comments_hub_connected': signalRService.isTaskCommentsHubConnected,
        'notification_hub_connected': signalRService.isNotificationHubConnected,
        'fully_connected': signalRService.isFullyConnected,
      };
    } catch (e) {
      return {
        'service_found': false,
        'error': e.toString(),
      };
    }
  }

  /// فحص كل Hub بشكل منفصل
  static Future<Map<String, dynamic>> _checkIndividualHubs() async {
    final hubs = {
      'chatHub': '$_baseUrl/chatHub',
      'taskHub': '$_baseUrl/taskHub',
      'taskCommentsHub': '$_baseUrl/taskCommentsHub',
      'notificationHub': '$_baseUrl/notificationHub',
    };
    
    final results = <String, dynamic>{};
    
    for (final entry in hubs.entries) {
      try {
        // محاولة الوصول لـ Hub endpoint
        final response = await http.get(
          Uri.parse(entry.value),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 5));
        
        results[entry.key] = {
          'accessible': true,
          'status_code': response.statusCode,
          'headers': response.headers,
        };
      } catch (e) {
        results[entry.key] = {
          'accessible': false,
          'error': e.toString(),
        };
      }
    }
    
    return results;
  }

  /// فحص CORS
  static Future<Map<String, dynamic>> _checkCORS() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/health'),
        headers: {
          'Origin': 'http://localhost:8080',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type',
        },
      ).timeout(const Duration(seconds: 5));
      
      return {
        'cors_enabled': response.headers.containsKey('access-control-allow-origin'),
        'allowed_origins': response.headers['access-control-allow-origin'],
        'allowed_methods': response.headers['access-control-allow-methods'],
        'allowed_headers': response.headers['access-control-allow-headers'],
        'status_code': response.statusCode,
      };
    } catch (e) {
      return {
        'cors_enabled': false,
        'error': e.toString(),
      };
    }
  }

  /// تحليل النتائج وتقديم التوصيات
  static Map<String, dynamic> _analyzeResults(Map<String, dynamic> results) {
    final issues = <String>[];
    final recommendations = <String>[];
    
    // تحليل حالة الخادم
    final server = results['server'] as Map<String, dynamic>?;
    if (server?['status'] != 'healthy') {
      issues.add('الخادم غير متاح أو لا يعمل بشكل صحيح');
      recommendations.add('تأكد من تشغيل الخادم على المنفذ 7111');
    }
    
    // تحليل خدمة SignalR
    final signalrService = results['signalr_service'] as Map<String, dynamic>?;
    if (signalrService?['service_found'] != true) {
      issues.add('خدمة SignalR غير مسجلة في GetX');
      recommendations.add('تأكد من تسجيل UnifiedSignalRService في main.dart');
    } else if (signalrService?['fully_connected'] != true) {
      issues.add('بعض أو جميع SignalR Hubs غير متصلة');
      recommendations.add('تحقق من حالة الشبكة وإعدادات CORS');
    }
    
    // تحليل CORS
    final cors = results['cors'] as Map<String, dynamic>?;
    if (cors?['cors_enabled'] != true) {
      issues.add('CORS غير مفعل أو لا يعمل بشكل صحيح');
      recommendations.add('تحقق من إعدادات CORS في Program.cs');
    }
    
    return {
      'issues_count': issues.length,
      'issues': issues,
      'recommendations': recommendations,
      'overall_status': issues.isEmpty ? 'healthy' : 'needs_attention',
    };
  }

  /// عرض نتائج التشخيص في dialog
  static void showDiagnosticsDialog() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const AlertDialog(
        title: Text('جاري فحص SignalR...'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('يرجى الانتظار...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );
    
    // تشغيل التشخيص
    final results = await runFullDiagnostics();
    
    // إغلاق مؤشر التحميل
    Get.back();
    
    // عرض النتائج
    _showResultsDialog(results);
  }

  /// عرض نتائج التشخيص
  static void _showResultsDialog(Map<String, dynamic> results) {
    final analysis = results['analysis'] as Map<String, dynamic>? ?? {};
    final issues = analysis['issues'] as List<dynamic>? ?? [];
    final recommendations = analysis['recommendations'] as List<dynamic>? ?? [];
    final overallStatus = analysis['overall_status'] as String? ?? 'unknown';
    
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(
              overallStatus == 'healthy' ? Icons.check_circle : Icons.warning,
              color: overallStatus == 'healthy' ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 8),
            const Text('نتائج تشخيص SignalR'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (issues.isNotEmpty) ...[
                const Text('المشاكل المكتشفة:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
                ...issues.map((issue) => Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $issue', style: const TextStyle(fontSize: 12)),
                )),
                const SizedBox(height: 16),
              ],
              if (recommendations.isNotEmpty) ...[
                const Text('التوصيات:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                ...recommendations.map((rec) => Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $rec', style: const TextStyle(fontSize: 12)),
                )),
              ],
              if (issues.isEmpty) ...[
                const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green),
                    SizedBox(width: 8),
                    Text('جميع الاتصالات تعمل بشكل صحيح!'),
                  ],
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              _showDetailedResults(results);
            },
            child: const Text('عرض التفاصيل'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض النتائج المفصلة
  static void _showDetailedResults(Map<String, dynamic> results) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل التشخيص'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              const JsonEncoder.withIndent('  ').convert(results),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
