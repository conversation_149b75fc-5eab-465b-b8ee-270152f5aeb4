import 'package:flutter/foundation.dart';
import '../../models/archive_models.dart';
import 'api_service.dart';

/// خدمة API لعلامات الأرشيف - متطابقة مع ASP.NET Core API
class ArchiveTagsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع علامات الأرشيف
  Future<List<ArchiveTag>> getAllTags() async {
    try {
      final response = await _apiService.get('/api/ArchiveTags');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على علامات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على علامة أرشيف بواسطة المعرف
  Future<ArchiveTag?> getTagById(int id) async {
    try {
      final response = await _apiService.get('/ArchiveTags/$id');
      return _apiService.handleResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على علامة الأرشيف: $e');
      return null;
    }
  }

  /// إنشاء علامة أرشيف جديدة
  Future<ArchiveTag?> createTag(ArchiveTag tag) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags',
        tag.toJson(),
      );
      return _apiService.handleResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء علامة الأرشيف: $e');
      rethrow;
    }
  }

  /// تحديث علامة أرشيف
  Future<ArchiveTag?> updateTag(ArchiveTag tag) async {
    try {
      final response = await _apiService.put(
        '/ArchiveTags/${tag.id}',
        tag.toJson(),
      );
      return _apiService.handleResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث علامة الأرشيف: $e');
      rethrow;
    }
  }

  /// حذف علامة أرشيف
  Future<bool> deleteTag(int id) async {
    try {
      final response = await _apiService.delete('/ArchiveTags/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف علامة الأرشيف: $e');
      return false;
    }
  }

  /// البحث في علامات الأرشيف
  Future<List<ArchiveTag>> searchTags(String query) async {
    try {
      final response = await _apiService.get('/ArchiveTags/search?searchTerm=$query');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن علامات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على العلامات الأكثر استخداماً
  Future<List<ArchiveTag>> getPopularTags({int limit = 10}) async {
    try {
      final response = await _apiService.get('/ArchiveTags/popular?limit=$limit');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على العلامات الشائعة: $e');
      rethrow;
    }
  }

  /// الحصول على العلامات حسب اللون
  Future<List<ArchiveTag>> getTagsByColor(String color) async {
    try {
      final response = await _apiService.get('/ArchiveTags/color/$color');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على علامات اللون: $e');
      rethrow;
    }
  }

  /// الحصول على المستندات المرتبطة بعلامة
  Future<List<Map<String, dynamic>>> getTagDocuments(int tagId) async {
    try {
      final response = await _apiService.get('/ArchiveTags/$tagId/documents');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات العلامة: $e');
      return [];
    }
  }

  /// دمج علامتين
  Future<bool> mergeTags(int sourceTagId, int targetTagId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags/merge',
        {
          'sourceTagId': sourceTagId,
          'targetTagId': targetTagId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في دمج العلامات: $e');
      return false;
    }
  }

  /// تحديث لون العلامة
  Future<bool> updateTagColor(int tagId, String color) async {
    try {
      final response = await _apiService.put(
        '/ArchiveTags/$tagId/color',
        {'color': color},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث لون العلامة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات العلامات
  Future<Map<String, dynamic>?> getTagStatistics() async {
    try {
      final response = await _apiService.get('/api/ArchiveTags/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات العلامات: $e');
      return null;
    }
  }

  /// الحصول على العلامات غير المستخدمة
  Future<List<ArchiveTag>> getUnusedTags() async {
    try {
      final response = await _apiService.get('/ArchiveTags/unused');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على العلامات غير المستخدمة: $e');
      rethrow;
    }
  }

  /// حذف العلامات غير المستخدمة
  Future<int> deleteUnusedTags() async {
    try {
      final response = await _apiService.delete('/ArchiveTags/unused');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return int.tryParse(response.body.toString()) ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في حذف العلامات غير المستخدمة: $e');
      return 0;
    }
  }

  /// إنشاء علامات متعددة
  Future<List<ArchiveTag>> createMultipleTags(List<String> tagNames) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags/bulk-create',
        {'tagNames': tagNames},
      );
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء العلامات المتعددة: $e');
      rethrow;
    }
  }

  /// حذف علامات متعددة
  Future<int> deleteMultipleTags(List<int> tagIds) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags/bulk-delete',
        {'tagIds': tagIds},
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return int.tryParse(response.body.toString()) ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في حذف العلامات المتعددة: $e');
      return 0;
    }
  }

  /// تصدير العلامات
  Future<String?> exportTags(String format) async {
    try {
      final response = await _apiService.get('/ArchiveTags/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير العلامات: $e');
      return null;
    }
  }

  /// استيراد العلامات
  Future<bool> importTags(String data, String format) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags/import',
        {
          'data': data,
          'format': format,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد العلامات: $e');
      return false;
    }
  }

  /// اقتراح علامات للمستند
  Future<List<ArchiveTag>> suggestTagsForDocument(String documentContent) async {
    try {
      final response = await _apiService.post(
        '/ArchiveTags/suggest',
        {'content': documentContent},
      );
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في اقتراح العلامات: $e');
      rethrow;
    }
  }

  /// الحصول على العلامات المرتبطة
  Future<List<ArchiveTag>> getRelatedTags(int tagId) async {
    try {
      final response = await _apiService.get('/ArchiveTags/$tagId/related');
      return _apiService.handleListResponse<ArchiveTag>(
        response,
        (json) => ArchiveTag.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على العلامات المرتبطة: $e');
      rethrow;
    }
  }
}
