import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/notifications_controller.dart';
import '../../models/notification_models.dart';
import '../../services/unified_permission_service.dart';
import '../../utils/date_formatter.dart';

class NotificationsTab extends StatefulWidget {
  const NotificationsTab({super.key});

  @override
  State<NotificationsTab> createState() => _NotificationsTabState();
}

class _NotificationsTabState extends State<NotificationsTab> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  @override
  void initState() {
    super.initState();
    // Load notifications when tab is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNotifications();
    });
  }

  /// Load notifications for the current user
  Future<void> _loadNotifications() async {
    final notificationController = Get.find<NotificationsController>();
    await notificationController.loadAllNotifications();
  }

  /// Mark all notifications as read for the current user
  Future<void> _markAllAsRead() async {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationsController>();

    if (authController.currentUser.value != null) {
      await notificationController.markAllAsRead();
    }
  }

  /// Delete all notifications for the current user
  Future<void> _deleteAllNotifications() async {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationsController>();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Notifications'),
        content: const Text('Are you sure you want to delete all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          if (_permissionService.canDeleteNotifications())
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete'),
            ),
        ],
      ),
    );

    if (confirmed == true && authController.currentUser.value != null) {
      await notificationController.deleteAllRead();
    }
  }

  /// Builds the notifications tab UI
  @override
  Widget build(BuildContext context) {
    // Get controllers
    final notificationController = Get.find<NotificationsController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('الاشعارات'),
      ),
      body: Column(
        children: [
          // Actions bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    'Notifications',
                    style: AppStyles.titleLarge,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                LayoutBuilder(
                  builder: (context, constraints) {
                    // تكيف مع الشاشات الصغيرة
                    // Adapt to small screens
                    return Obx(() {
                      if (constraints.maxWidth < 300) {
                        return Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: notificationController.allNotifications.isEmpty
                                  ? null
                                  : _markAllAsRead,
                              icon: const Icon(Icons.check_circle_outline),
                              tooltip: 'Mark All Read',
                            ),
                            IconButton(
                              onPressed: notificationController.allNotifications.isEmpty
                                  ? null
                                  : _deleteAllNotifications,
                              icon: const Icon(Icons.delete_outline),
                              tooltip: 'Delete All',
                            ),
                          ],
                        );
                      }

                      // للشاشات العادية والكبيرة
                      // For normal and large screens
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_permissionService.canManageNotifications())
                            TextButton.icon(
                              onPressed: notificationController.allNotifications.isEmpty
                                  ? null
                                  : _markAllAsRead,
                              icon: const Icon(Icons.check_circle_outline),
                              label: const Text('Mark All Read'),
                            ),
                          if (_permissionService.canDeleteNotifications())
                            TextButton.icon(
                              onPressed: notificationController.allNotifications.isEmpty
                                  ? null
                                  : _deleteAllNotifications,
                              icon: const Icon(Icons.delete_outline),
                              label: const Text('Delete All'),
                            ),
                        ],
                      );
                    });
                  },
                ),
              ],
            ),
          ),

          // Notifications list
          Expanded(
            child: Obx(() => notificationController.isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildNotificationsList(notificationController)),
          ),
        ],
      ),
    );
  }

  /// Builds the list of notifications
  Widget _buildNotificationsList(NotificationsController notificationController) {
    if (notificationController.allNotifications.isEmpty) {
      return const Center(
        child: Text('No notifications'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12), // تقليل الهوامش
      itemCount: notificationController.allNotifications.length,
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      itemBuilder: (context, index) {
        final notification = notificationController.allNotifications[index];
        return _buildNotificationItem(context, notification);
      },
    );
  }

  /// Builds a notification item
  Widget _buildNotificationItem(BuildContext context, NotificationModel notification) {
    // Get controller
    final notificationController = Get.find<NotificationsController>();

    return Dismissible(
      key: Key(notification.id.toString()),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        notificationController.deleteNotification(notification.id);
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        color: notification.isRead ? Colors.white : Colors.blue.shade50,
        child: ListTile(
          title: Text(
            notification.title,
            style: AppStyles.titleMedium.copyWith(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis, // منع تجاوز النص للحدود
            maxLines: 1, // تحديد عدد الأسطر
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة المطلوبة
            children: [
              const SizedBox(height: 4),
              Text(
                notification.content,
                style: AppStyles.bodySmall,
                overflow: TextOverflow.ellipsis, // منع تجاوز النص للحدود
                maxLines: 2, // تحديد عدد الأسطر
              ),
              const SizedBox(height: 4),
              Text(
                DateFormatter.getRelativeTime(notification.createdAt),
                style: AppStyles.labelSmall,
              ),
            ],
          ),
          leading: CircleAvatar(
            backgroundColor: _getNotificationColor(notification.type),
            child: Icon(
              _getNotificationIcon(notification.type),
              color: Colors.white,
              size: 20,
            ),
          ),
          trailing: notification.isRead
              ? null
              : IconButton(
                  icon: const Icon(Icons.check_circle_outline),
                  onPressed: () {
                    notificationController.markAsRead(notification.id);
                  },
                  padding: EdgeInsets.zero, // تقليل المساحة المستخدمة
                  constraints: const BoxConstraints(), // تقليل القيود
                ),
          onTap: () {
            // Mark as read when tapped
            if (!notification.isRead) {
              notificationController.markAsRead(notification.id);
            }

            // Navigate to related content if applicable
            if (notification.referenceId != null) {
              // Navigate to task details using GetX
              // Get.to(() => TaskDetailScreen(taskId: notification.referenceId!));
            }
          },
          dense: true, // جعل ListTile أكثر كثافة لتوفير المساحة
          visualDensity: VisualDensity.compact, // تقليل المساحة بين العناصر
        ),
      ),
    );
  }

  Color _getNotificationColor(String type) {
    switch (type.toLowerCase()) {
      // إشعارات المهام
      case 'task_assigned':
      case 'task_transferred':
        return AppColors.primary;
      case 'task_completed':
        return AppColors.success;
      case 'task_updated':
        return AppColors.info;
      case 'task_overdue':
        return AppColors.error;
      case 'task_access_granted':
        return Colors.cyan;
      case 'task_status_changed':
        return Colors.amber;
      case 'task_priority_changed':
        return Colors.deepOrange;
      case 'comment_added':
        return AppColors.accent;
      case 'attachment_added':
      case 'file_attached':
        return Colors.purple;
      case 'attachment_deleted':
        return Colors.grey;
      case 'document_created':
        return Colors.deepPurple;
      case 'task_message_received':
        return Colors.teal;
      case 'permission_granted':
        return Colors.green;
      case 'reminder_48_hours':
      case 'reminder_24_hours':
      case 'reminder_6_hours':
      case 'reminder_due':
        return AppColors.error;
      case 'info_requested':
      case 'info_provided':
        return AppColors.warning;

      // إشعارات النظام
      case 'system_notification':
      case 'system_update':
        return Colors.grey;

      // إشعارات المحادثات
      case 'message_received':
      case 'mention_in_message':
        return Colors.teal;
      case 'new_task_message':
        return Colors.indigo;
      case 'new_direct_message':
        return Colors.blue;
      case 'mentioned_in_message':
        return Colors.deepPurple;
      case 'new_group_message':
        return Colors.amber.shade800;
      case 'added_to_group':
      case 'user_joined':
        return Colors.green;
      case 'department_update':
        return Colors.orange;
      case 'report_generated':
        return Colors.cyan;
      case 'backup_completed':
        return Colors.brown;
      case 'general':
      default:
        // قيمة افتراضية
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type.toLowerCase()) {
      // إشعارات المهام
      case 'task_assigned':
        return Icons.assignment_ind;
      case 'task_transferred':
        return Icons.swap_horiz;
      case 'task_completed':
        return Icons.task_alt;
      case 'task_updated':
        return Icons.update;
      case 'task_overdue':
        return Icons.schedule;
      case 'task_access_granted':
        return Icons.lock_open;
      case 'task_status_changed':
        return Icons.change_circle;
      case 'task_priority_changed':
        return Icons.priority_high;
      case 'comment_added':
        return Icons.comment;
      case 'attachment_added':
      case 'file_attached':
        return Icons.attach_file;
      case 'attachment_deleted':
        return Icons.delete_outline;
      case 'document_created':
        return Icons.description;
      case 'task_message_received':
        return Icons.message;
      case 'permission_granted':
        return Icons.security;
      case 'reminder_48_hours':
      case 'reminder_24_hours':
      case 'reminder_6_hours':
      case 'reminder_due':
        return Icons.alarm;
      case 'info_requested':
        return Icons.help_outline;
      case 'info_provided':
        return Icons.info_outline;

      // إشعارات النظام
      case 'system_notification':
      case 'system_update':
        return Icons.notifications;

      // إشعارات المحادثات
      case 'message_received':
      case 'mention_in_message':
      case 'mentioned_in_message':
        return Icons.alternate_email;
      case 'new_task_message':
        return Icons.message;
      case 'new_direct_message':
        return Icons.chat;
      case 'new_group_message':
        return Icons.group;
      case 'added_to_group':
      case 'user_joined':
        return Icons.group_add;
      case 'department_update':
        return Icons.business;
      case 'report_generated':
        return Icons.assessment;
      case 'backup_completed':
        return Icons.backup;
      case 'general':
      default:
        // قيمة افتراضية
        return Icons.notifications;
    }
  }
}
