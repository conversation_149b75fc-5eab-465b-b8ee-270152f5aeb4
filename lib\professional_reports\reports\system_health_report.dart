class SystemHealthReport {
  final int activeUsers;
  final int databaseSize;
  final Map<String, int> taskCountsByStatus;
  final double averageApiResponseTime;

  SystemHealthReport({
    required this.activeUsers,
    required this.databaseSize,
    required this.taskCountsByStatus,
    required this.averageApiResponseTime,
  });

  // يمكن إنشاء هذا التقرير من خلال استعلامات مباشرة على قاعدة البيانات
  // ومراقبة أداء الـ API.  هذا مثال افتراضي ويجب تعديله ليناسب نظامك.
  // factory SystemHealthReport.fromData() {
  //   return SystemHealthReport(
  //     activeUsers: 0,                // قيمة افتراضية
  //     databaseSize: 0,               // قيمة افتراضية
  //     taskCountsByStatus: {},         // قيمة افتراضية
  //     averageApiResponseTime: 0.0,   // قيمة افتراضية
  //   );
  // }
}