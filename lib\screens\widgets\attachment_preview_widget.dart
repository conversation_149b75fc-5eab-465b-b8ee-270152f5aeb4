import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';

import '../../utils/file_processor.dart';
import '../../utils/file_existence_helper.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/unified_permission_service.dart';

/// مكون معاينة المرفق
/// يعرض معاينة للمرفق مع إمكانية العرض والتنزيل والحذف
class AttachmentPreview extends StatefulWidget {
  /// المرفق المراد عرضه
  final Attachment attachment;

  /// دالة يتم استدعاؤها عند عرض المرفق
  final Function(Attachment)? onView;

  /// دالة يتم استدعاؤها عند فتح المرفق
  final Function(Attachment)? onOpen;

  /// دالة يتم استدعاؤها عند تنزيل المرفق
  final Function(Attachment)? onDownload;

  /// دالة يتم استدعاؤها عند حذف المرفق
  final Function(Attachment)? onDelete;

  /// هل يمكن حذف المرفق
  final bool canDelete;

  /// إنشاء مكون معاينة المرفق
  const AttachmentPreview({
    super.key,
    required this.attachment,
    this.onView,
    this.onOpen,
    this.onDownload,
    this.onDelete,
    this.canDelete = false,
  });

  @override
  State<AttachmentPreview> createState() => _AttachmentPreviewState();
}

class _AttachmentPreviewState extends State<AttachmentPreview> {
  bool _fileExists = true;
  bool _checked = false;
  String? _localFilePath; // المسار المحلي الفعلي بعد التحقق أو التنزيل
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _checkFile();
  }

  Future<void> _checkFile() async {
    final exists = await FileExistenceHelper.fileExists(widget.attachment);
    String? localPath;
    if (exists) {
      localPath = await FileExistenceHelper.getLocalFilePath(widget.attachment);
    }
    if (mounted) {
      setState(() {
        _fileExists = exists;
        _checked = true;
        _localFilePath = localPath;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileType = FileProcessor.getFileType(_localFilePath ?? widget.attachment.filePath);
    final fileIcon = FileProcessor.getFileIcon(_localFilePath ?? widget.attachment.filePath);
    final fileSize = FileProcessor.formatFileSize(widget.attachment.fileSize);

    // دالة التفاف لدالة التنزيل: بعد النجاح أعد التحقق من وجود الملف
    Future<void> _handleDownload() async {
      if (widget.onDownload != null) {
        await widget.onDownload!(widget.attachment);
        await _checkFile(); // إعادة التحقق بعد التنزيل
        // تحديث المسار المحلي بعد التنزيل
        if (_fileExists) {
          // يمكن فتح الملف تلقائيًا بعد التنزيل إذا رغبت:
          // if (widget.onView != null) _handleView();
        }
      }
    }

    // تمرير نسخة Attachment محدثة بالمسار المحلي الصحيح
    void _handleView() {
      if (widget.onView != null) {
        final updated = _localFilePath != null
            ? widget.attachment.copyWith(filePath: _localFilePath)
            : widget.attachment;
        widget.onView!(updated);
      }
    }
    void _handleOpen() {
      if (widget.onOpen != null) {
        final updated = _localFilePath != null
            ? widget.attachment.copyWith(filePath: _localFilePath)
            : widget.attachment;
        widget.onOpen!(updated);
      }
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        height: 170, // ارتفاع ثابت للكرت بالكامل
        child: InkWell(
          onTap: _fileExists && widget.onView != null && _permissionService.canViewAttachments() ? _handleView : null,
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // معاينة الملف (بدون Expanded)
              Container(
                height: 80, // ارتفاع ثابت للمعاينة
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: _buildFilePreview(fileType, fileIcon),
              ),
              // معلومات الملف مع تمرير داخلي فقط عند الحاجة
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الملف
                        Text(
                          widget.attachment.fileName,
                          style: AppStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // حجم الملف
                        Text(
                          fileSize,
                          style: AppStyles.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // أزرار الإجراءات - محمية بالصلاحيات
                        if (_checked)
                          Wrap(
                            spacing: 8,
                            runSpacing: 0,
                            alignment: WrapAlignment.center,
                            children: [
                              // زر العرض - محمي بصلاحية عرض المرفقات
                              ...(_fileExists && widget.onView != null && _permissionService.canViewAttachments()
                                  ? [
                                      _buildActionButton(
                                        icon: Icons.visibility,
                                        tooltip: 'عرض'.tr,
                                        onPressed: _handleView,
                                      ),
                                    ]
                                  : []),
                              // زر الفتح - محمي بصلاحية عرض المرفقات
                              ...(_fileExists && widget.onOpen != null && _permissionService.canViewAttachments()
                                  ? [
                                      _buildActionButton(
                                        icon: Icons.open_in_new,
                                        tooltip: 'فتح'.tr,
                                        onPressed: _handleOpen,
                                      ),
                                    ]
                                  : []),
                              // زر التنزيل - محمي بصلاحية تنزيل المرفقات
                              ...(!_fileExists && widget.onDownload != null && _permissionService.canDownloadAttachments()
                                  ? [
                                      _buildActionButton(
                                        icon: Icons.download,
                                        tooltip: 'تنزيل'.tr,
                                        onPressed: _handleDownload,
                                      ),
                                    ]
                                  : []),
                              // زر الحذف - محمي بصلاحية حذف المرفقات
                              ...(widget.canDelete && widget.onDelete != null && _permissionService.canDeleteAttachments()
                                  ? [
                                      _buildActionButton(
                                        icon: Icons.delete,
                                        tooltip: 'حذف'.tr,
                                        color: Colors.red,
                                        onPressed: () => widget.onDelete?.call(widget.attachment),
                                      ),
                                    ]
                                  : []),
                            ],
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء معاينة الملف
  Widget _buildFilePreview(String fileType, IconData fileIcon) {
    switch (fileType) {
      case 'image':
        return _buildImagePreview();
      default:
        return _buildGenericFilePreview(fileIcon, fileType);
    }
  }

  /// بناء معاينة الصورة
  Widget _buildImagePreview() {
    final path = _localFilePath ?? widget.attachment.filePath;
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
      ),
      child: kIsWeb
          ? Image.network(
              path,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildGenericFilePreview(Icons.image, 'image');
              },
            )
          : Image.file(
              File(path),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildGenericFilePreview(Icons.image, 'image');
              },
            ),
    );
  }

  /// بناء معاينة الملف العام
  Widget _buildGenericFilePreview(IconData fileIcon, String fileType) {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              fileIcon,
              size: 48,
              color: _getFileTypeColor(fileType),
            ),
            const SizedBox(height: 8),
            Text(
              _getFileTypeDisplay(fileType),
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return IconButton(
      icon: Icon(icon, size: 20),
      tooltip: tooltip,
      onPressed: onPressed,
      color: color ?? AppColors.primary,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      padding: EdgeInsets.zero,
    );
  }

  /// الحصول على لون نوع الملف
  Color _getFileTypeColor(String fileType) {
    switch (fileType) {
      case 'image':
        return Colors.green;
      case 'pdf':
        return Colors.red;
      case 'word':
        return Colors.blue;
      case 'spreadsheet':
        return Colors.green.shade700;
      case 'presentation':
        return Colors.orange;
      case 'video':
        return Colors.red.shade700;
      case 'audio':
        return Colors.purple;
      case 'archive':
        return Colors.brown;
      case 'code':
        return Colors.indigo;
      default:
        return Colors.grey.shade700;
    }
  }

  /// الحصول على اسم نوع الملف للعرض
  String _getFileTypeDisplay(String fileType) {
    switch (fileType) {
      case 'image':
        return 'صورة';
      case 'document':
        return 'مستند';
      case 'video':
        return 'فيديو';
      case 'audio':
        return 'ملف صوتي';
      case 'archive':
        return 'ملف مضغوط';
      default:
        return 'ملف';
    }
  }
}
