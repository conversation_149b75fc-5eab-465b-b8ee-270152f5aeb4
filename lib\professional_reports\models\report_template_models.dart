/// نماذج قوالب التقارير الاحترافية
/// 
/// هذا الملف يحتوي على نماذج القوالب المختلفة للتقارير
/// مع إعدادات التخصيص والتصميم

import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// أنواع التقارير المدعومة
enum ReportType {
  /// تقرير ملخص المهام
  taskSummary('task_summary', 'ملخص المهام', Icons.task_alt),
  
  /// تقرير تفاصيل المهام
  taskDetails('task_details', 'تفاصيل المهام', Icons.list_alt),
  
  /// تقرير تقدم المهام
  taskProgress('task_progress', 'تقدم المهام', Icons.trending_up),
  
  /// تقرير أداء المستخدمين
  userPerformance('user_performance', 'أداء المستخدمين', Icons.person_outline),
  
  /// تقرير أداء الأقسام
  departmentPerformance('department_performance', 'أداء الأقسام', Icons.business),
  
  /// تقرير تتبع الوقت
  timeTracking('time_tracking', 'تتبع الوقت', Icons.access_time),
  
  /// تقرير مخصص
  custom('custom', 'تقرير مخصص', Icons.build);

  const ReportType(this.value, this.displayName, this.icon);

  /// القيمة المخزنة في قاعدة البيانات
  final String value;
  
  /// الاسم المعروض للمستخدم
  final String displayName;
  
  /// أيقونة التقرير
  final IconData icon;

  /// البحث عن نوع التقرير بالقيمة
  static ReportType fromValue(String value) {
    return ReportType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ReportType.custom,
    );
  }
}

/// تنسيقات التصدير المدعومة
enum ExportFormat {
  /// تصدير PDF
  pdf('pdf', 'PDF', Icons.picture_as_pdf, Colors.red),
  
  /// تصدير Excel
  excel('excel', 'Excel', Icons.table_chart, Colors.green),
  
  /// تصدير CSV
  csv('csv', 'CSV', Icons.description, Colors.blue),
  
  /// تصدير JSON
  json('json', 'JSON', Icons.code, Colors.orange);

  const ExportFormat(this.value, this.displayName, this.icon, this.color);

  /// القيمة المخزنة
  final String value;
  
  /// الاسم المعروض
  final String displayName;
  
  /// أيقونة التنسيق
  final IconData icon;
  
  /// لون التنسيق
  final Color color;
}

/// أنواع الرسوم البيانية المدعومة
enum ChartType {
  /// رسم بياني دائري
  pie('pie', 'دائري', Icons.pie_chart),
  
  /// رسم بياني عمودي
  column('column', 'عمودي', Icons.bar_chart),
  
  /// رسم بياني خطي
  line('line', 'خطي', Icons.show_chart),
  
  /// رسم بياني مساحي
  area('area', 'مساحي', Icons.area_chart),
  
  /// رسم بياني مبعثر
  scatter('scatter', 'مبعثر', Icons.scatter_plot),
  
  /// رسم بياني شعاعي
  radar('radar', 'شعاعي', Icons.radar);

  const ChartType(this.value, this.displayName, this.icon);

  /// القيمة المخزنة
  final String value;
  
  /// الاسم المعروض
  final String displayName;
  
  /// أيقونة نوع الرسم
  final IconData icon;
}

/// نموذج قالب التقرير
class ReportTemplate extends Equatable {
  /// معرف القالب
  final String id;
  
  /// اسم القالب
  final String name;
  
  /// وصف القالب
  final String description;
  
  /// نوع التقرير
  final ReportType reportType;
  
  /// إعدادات التصميم
  final ReportDesignSettings designSettings;
  
  /// إعدادات المحتوى
  final ReportContentSettings contentSettings;
  
  /// إعدادات التصدير
  final ReportExportSettings exportSettings;
  
  /// هل القالب افتراضي
  final bool isDefault;
  
  /// هل القالب مخصص
  final bool isCustom;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تعديل
  final DateTime? updatedAt;

  const ReportTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.reportType,
    required this.designSettings,
    required this.contentSettings,
    required this.exportSettings,
    this.isDefault = false,
    this.isCustom = false,
    required this.createdAt,
    this.updatedAt,
  });

  /// تحويل من JSON
  factory ReportTemplate.fromJson(Map<String, dynamic> json) {
    return ReportTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      reportType: ReportType.fromValue(json['reportType'] as String),
      designSettings: ReportDesignSettings.fromJson(json['designSettings'] as Map<String, dynamic>),
      contentSettings: ReportContentSettings.fromJson(json['contentSettings'] as Map<String, dynamic>),
      exportSettings: ReportExportSettings.fromJson(json['exportSettings'] as Map<String, dynamic>),
      isDefault: json['isDefault'] as bool? ?? false,
      isCustom: json['isCustom'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'reportType': reportType.value,
      'designSettings': designSettings.toJson(),
      'contentSettings': contentSettings.toJson(),
      'exportSettings': exportSettings.toJson(),
      'isDefault': isDefault,
      'isCustom': isCustom,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة
  ReportTemplate copyWith({
    String? id,
    String? name,
    String? description,
    ReportType? reportType,
    ReportDesignSettings? designSettings,
    ReportContentSettings? contentSettings,
    ReportExportSettings? exportSettings,
    bool? isDefault,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReportTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      reportType: reportType ?? this.reportType,
      designSettings: designSettings ?? this.designSettings,
      contentSettings: contentSettings ?? this.contentSettings,
      exportSettings: exportSettings ?? this.exportSettings,
      isDefault: isDefault ?? this.isDefault,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id, name, description, reportType, designSettings, contentSettings,
    exportSettings, isDefault, isCustom, createdAt, updatedAt
  ];
}

/// إعدادات تصميم التقرير
class ReportDesignSettings extends Equatable {
  /// اللون الأساسي
  final Color primaryColor;
  
  /// اللون الثانوي
  final Color secondaryColor;
  
  /// لون الخلفية
  final Color backgroundColor;
  
  /// لون النص
  final Color textColor;
  
  /// خط العنوان الرئيسي
  final String titleFont;
  
  /// خط النص العادي
  final String bodyFont;
  
  /// حجم خط العنوان
  final double titleFontSize;
  
  /// حجم خط النص
  final double bodyFontSize;
  
  /// شعار الشركة
  final String? logoPath;
  
  /// عرض الصفحة
  final double pageWidth;
  
  /// ارتفاع الصفحة
  final double pageHeight;
  
  /// الهوامش
  final EdgeInsets margins;
  
  /// المسافة بين العناصر
  final double spacing;

  const ReportDesignSettings({
    this.primaryColor = const Color(0xFF2196F3),
    this.secondaryColor = const Color(0xFF03DAC6),
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black87,
    this.titleFont = 'NotoSansArabic',
    this.bodyFont = 'NotoSansArabic',
    this.titleFontSize = 24.0,
    this.bodyFontSize = 12.0,
    this.logoPath,
    this.pageWidth = 595.0, // A4 width
    this.pageHeight = 842.0, // A4 height
    this.margins = const EdgeInsets.all(40.0),
    this.spacing = 16.0,
  });

  /// تحويل من JSON
  factory ReportDesignSettings.fromJson(Map<String, dynamic> json) {
    return ReportDesignSettings(
      primaryColor: Color(json['primaryColor'] as int? ?? 0xFF2196F3),
      secondaryColor: Color(json['secondaryColor'] as int? ?? 0xFF03DAC6),
      backgroundColor: Color(json['backgroundColor'] as int? ?? 0xFFFFFFFF),
      textColor: Color(json['textColor'] as int? ?? 0xFF000000),
      titleFont: json['titleFont'] as String? ?? 'NotoSansArabic',
      bodyFont: json['bodyFont'] as String? ?? 'NotoSansArabic',
      titleFontSize: (json['titleFontSize'] as num?)?.toDouble() ?? 24.0,
      bodyFontSize: (json['bodyFontSize'] as num?)?.toDouble() ?? 12.0,
      logoPath: json['logoPath'] as String?,
      pageWidth: (json['pageWidth'] as num?)?.toDouble() ?? 595.0,
      pageHeight: (json['pageHeight'] as num?)?.toDouble() ?? 842.0,
      margins: EdgeInsets.all((json['margins'] as num?)?.toDouble() ?? 40.0),
      spacing: (json['spacing'] as num?)?.toDouble() ?? 16.0,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'primaryColor': primaryColor.value,
      'secondaryColor': secondaryColor.value,
      'backgroundColor': backgroundColor.value,
      'textColor': textColor.value,
      'titleFont': titleFont,
      'bodyFont': bodyFont,
      'titleFontSize': titleFontSize,
      'bodyFontSize': bodyFontSize,
      'logoPath': logoPath,
      'pageWidth': pageWidth,
      'pageHeight': pageHeight,
      'margins': margins.top, // تبسيط للحفظ
      'spacing': spacing,
    };
  }

  @override
  List<Object?> get props => [
    primaryColor, secondaryColor, backgroundColor, textColor,
    titleFont, bodyFont, titleFontSize, bodyFontSize, logoPath,
    pageWidth, pageHeight, margins, spacing
  ];
}

/// إعدادات محتوى التقرير
class ReportContentSettings extends Equatable {
  /// عرض العنوان الرئيسي
  final bool showTitle;
  
  /// عرض التاريخ
  final bool showDate;
  
  /// عرض الملخص التنفيذي
  final bool showSummary;
  
  /// عرض الرسوم البيانية
  final bool showCharts;
  
  /// عرض الجداول
  final bool showTables;
  
  /// عرض التفاصيل
  final bool showDetails;
  
  /// عرض التوقيع
  final bool showSignature;
  
  /// أنواع الرسوم البيانية المطلوبة
  final List<ChartType> chartTypes;
  
  /// الأعمدة المطلوبة في الجداول
  final List<String> tableColumns;
  
  /// عدد العناصر في الصفحة الواحدة
  final int itemsPerPage;
  
  /// ترتيب البيانات
  final String sortBy;
  
  /// اتجاه الترتيب
  final bool sortAscending;

  const ReportContentSettings({
    this.showTitle = true,
    this.showDate = true,
    this.showSummary = true,
    this.showCharts = true,
    this.showTables = true,
    this.showDetails = false,
    this.showSignature = false,
    this.chartTypes = const [ChartType.pie, ChartType.column],
    this.tableColumns = const [],
    this.itemsPerPage = 50,
    this.sortBy = 'createdAt',
    this.sortAscending = false,
  });

  /// تحويل من JSON
  factory ReportContentSettings.fromJson(Map<String, dynamic> json) {
    return ReportContentSettings(
      showTitle: json['showTitle'] as bool? ?? true,
      showDate: json['showDate'] as bool? ?? true,
      showSummary: json['showSummary'] as bool? ?? true,
      showCharts: json['showCharts'] as bool? ?? true,
      showTables: json['showTables'] as bool? ?? true,
      showDetails: json['showDetails'] as bool? ?? false,
      showSignature: json['showSignature'] as bool? ?? false,
      chartTypes: (json['chartTypes'] as List<dynamic>?)
          ?.map((e) => ChartType.values.firstWhere((type) => type.value == e))
          .toList() ?? [ChartType.pie, ChartType.column],
      tableColumns: (json['tableColumns'] as List<dynamic>?)?.cast<String>() ?? [],
      itemsPerPage: json['itemsPerPage'] as int? ?? 50,
      sortBy: json['sortBy'] as String? ?? 'createdAt',
      sortAscending: json['sortAscending'] as bool? ?? false,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'showTitle': showTitle,
      'showDate': showDate,
      'showSummary': showSummary,
      'showCharts': showCharts,
      'showTables': showTables,
      'showDetails': showDetails,
      'showSignature': showSignature,
      'chartTypes': chartTypes.map((e) => e.value).toList(),
      'tableColumns': tableColumns,
      'itemsPerPage': itemsPerPage,
      'sortBy': sortBy,
      'sortAscending': sortAscending,
    };
  }

  @override
  List<Object?> get props => [
    showTitle, showDate, showSummary, showCharts, showTables, showDetails,
    showSignature, chartTypes, tableColumns, itemsPerPage, sortBy, sortAscending
  ];
}

/// إعدادات تصدير التقرير
class ReportExportSettings extends Equatable {
  /// التنسيقات المدعومة
  final List<ExportFormat> supportedFormats;
  
  /// التنسيق الافتراضي
  final ExportFormat defaultFormat;
  
  /// جودة الصور في PDF
  final double imageQuality;
  
  /// ضغط PDF
  final bool compressPdf;
  
  /// كلمة مرور PDF
  final String? pdfPassword;
  
  /// السماح بالطباعة
  final bool allowPrinting;
  
  /// السماح بالنسخ
  final bool allowCopying;
  
  /// اسم الملف الافتراضي
  final String defaultFileName;
  
  /// مجلد الحفظ
  final String? saveDirectory;

  const ReportExportSettings({
    this.supportedFormats = const [ExportFormat.pdf, ExportFormat.excel, ExportFormat.csv],
    this.defaultFormat = ExportFormat.pdf,
    this.imageQuality = 0.8,
    this.compressPdf = true,
    this.pdfPassword,
    this.allowPrinting = true,
    this.allowCopying = true,
    this.defaultFileName = 'تقرير',
    this.saveDirectory,
  });

  /// تحويل من JSON
  factory ReportExportSettings.fromJson(Map<String, dynamic> json) {
    return ReportExportSettings(
      supportedFormats: (json['supportedFormats'] as List<dynamic>?)
          ?.map((e) => ExportFormat.values.firstWhere((format) => format.value == e))
          .toList() ?? [ExportFormat.pdf, ExportFormat.excel, ExportFormat.csv],
      defaultFormat: ExportFormat.values.firstWhere(
        (format) => format.value == (json['defaultFormat'] as String? ?? 'pdf'),
        orElse: () => ExportFormat.pdf,
      ),
      imageQuality: (json['imageQuality'] as num?)?.toDouble() ?? 0.8,
      compressPdf: json['compressPdf'] as bool? ?? true,
      pdfPassword: json['pdfPassword'] as String?,
      allowPrinting: json['allowPrinting'] as bool? ?? true,
      allowCopying: json['allowCopying'] as bool? ?? true,
      defaultFileName: json['defaultFileName'] as String? ?? 'تقرير',
      saveDirectory: json['saveDirectory'] as String?,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'supportedFormats': supportedFormats.map((e) => e.value).toList(),
      'defaultFormat': defaultFormat.value,
      'imageQuality': imageQuality,
      'compressPdf': compressPdf,
      'pdfPassword': pdfPassword,
      'allowPrinting': allowPrinting,
      'allowCopying': allowCopying,
      'defaultFileName': defaultFileName,
      'saveDirectory': saveDirectory,
    };
  }

  @override
  List<Object?> get props => [
    supportedFormats, defaultFormat, imageQuality, compressPdf, pdfPassword,
    allowPrinting, allowCopying, defaultFileName, saveDirectory
  ];
}