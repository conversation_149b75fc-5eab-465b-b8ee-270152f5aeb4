import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';
import '../../models/role_model.dart';
import '../../controllers/user_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/api/task_access_api_service.dart';

/// مدير صلاحيات الوصول للمهام
/// يسمح بإدارة المستخدمين الذين يمكنهم الوصول إلى مهمة معينة
class TaskAccessManager extends StatefulWidget {
  final Task task;
  final Function(List<String>) onAccessChanged;

  const TaskAccessManager({
    super.key,
    required this.task,
    required this.onAccessChanged,
  });

  @override
  State<TaskAccessManager> createState() => _TaskAccessManagerState();
}

class _TaskAccessManagerState extends State<TaskAccessManager> {
  final UserController _userController = Get.find<UserController>();
  final TaskAccessApiService _taskAccessApiService = TaskAccessApiService();

  List<User> _allUsers = [];
  Set<String> _accessUserIds = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل جميع المستخدمين
      await _userController.loadAllUsers();
      _allUsers = _userController.users;

      // تحميل المستخدمين الذين لديهم صلاحية الوصول حالياً
      try {
        final accessUserIds = await _taskAccessApiService.getTaskAccessUsers(widget.task.id);
        _accessUserIds = accessUserIds.map((id) => id.toString()).toSet();
      } catch (e) {
        debugPrint('خطأ في تحميل صلاحيات الوصول: $e');
        // في حالة الخطأ، نبدأ بقائمة فارغة
        _accessUserIds = <String>{};
      }

      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _addUserToAccess(String userId) async {
    try {
      // إضافة المستخدم إلى صلاحيات الوصول عبر API
      await _taskAccessApiService.addUserToTaskAccess(
        widget.task.id,
        int.parse(userId)
      );

      setState(() {
        _accessUserIds.add(userId);
      });
      
      widget.onAccessChanged(_accessUserIds.toList());
      
      Get.snackbar(
        'نجح',
        'تم منح صلاحية الوصول للمستخدم',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في إضافة صلاحية الوصول: $e');
      Get.snackbar(
        'خطأ',
        'فشل في منح صلاحية الوصول',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _removeUserFromAccess(String userId) async {
    try {
      // إزالة المستخدم من صلاحيات الوصول عبر API
      await _taskAccessApiService.removeUserFromTaskAccess(
        widget.task.id,
        int.parse(userId)
      );

      setState(() {
        _accessUserIds.remove(userId);
      });
      
      widget.onAccessChanged(_accessUserIds.toList());
      
      Get.snackbar(
        'نجح',
        'تم إلغاء صلاحية الوصول للمستخدم',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في إزالة صلاحية الوصول: $e');
      Get.snackbar(
        'خطأ',
        'فشل في إلغاء صلاحية الوصول',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إدارة صلاحيات الوصول',
                  style: AppStyles.headingMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Column(
                children: [
                  // عرض المستخدمين الحاليين الذين لديهم صلاحية
                  if (_accessUserIds.isNotEmpty) ...[
                    Text(
                      'المستخدمون الذين لديهم صلاحية الوصول:',
                      style: AppStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ..._accessUserIds.map((userId) {
                      final user = _allUsers.firstWhere(
                        (u) => u.id.toString() == userId,
                        orElse: () => User(
                          id: 0,
                          name: 'مستخدم غير معروف',
                          email: '',
                          role: null,
                          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                        ),
                      );

                      return ListTile(
                        leading: CircleAvatar(
                          child: Text(user.name.isNotEmpty ? user.name[0] : '?'),
                        ),
                        title: Text(user.name),
                        subtitle: Text(_getRoleDisplayName(user.role)),
                        trailing: IconButton(
                          icon: const Icon(Icons.remove_circle, color: AppColors.error),
                          onPressed: () => _removeUserFromAccess(userId),
                        ),
                      );
                    }),
                    const Divider(),
                  ],

                  // قائمة جميع المستخدمين لإضافة صلاحيات
                  Text(
                    'إضافة صلاحية وصول لمستخدمين آخرين:',
                    style: AppStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  SizedBox(
                    height: 300,
                    child: ListView.builder(
                      itemCount: _allUsers.length,
                      itemBuilder: (context, index) {
                        final user = _allUsers[index];
                        final hasAccess = _accessUserIds.contains(user.id.toString());

                        return ListTile(
                          leading: CircleAvatar(
                            child: Text(user.name.isNotEmpty ? user.name[0] : '?'),
                          ),
                          title: Text(user.name),
                          subtitle: Text(_getRoleDisplayName(user.role)),
                          trailing: Checkbox(
                            value: hasAccess,
                            onChanged: (value) {
                              if (value == true) {
                                _addUserToAccess(user.id.toString());
                              } else {
                                _removeUserFromAccess(user.id.toString());
                              }
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  String _getRoleDisplayName(Role? role) {
    if (role == null) return 'غير معروف';
    switch (role.name) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير قسم';
      case 'supervisor':
        return 'مشرف';
      case 'user':
        return 'موظف';
      default:
        return role.name;
    }
  }
}
