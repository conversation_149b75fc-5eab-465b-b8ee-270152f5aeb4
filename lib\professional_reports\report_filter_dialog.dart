import 'package:flutter/material.dart';

/// نموذج لخيارات فلتر التقرير
class ReportFilterOptions {
  bool includeTaskDetails;
  bool includeComments;
  bool includeAttachments;
  bool includeSubtasks;
  bool includeTimeTracking;
  bool includeHistory;
  bool includeContributors;
  bool includeProgress;

  ReportFilterOptions({
    this.includeTaskDetails = true,
    this.includeComments = true,
    this.includeAttachments = true,
    this.includeSubtasks = true,
    this.includeTimeTracking = true,
    this.includeHistory = true,
    this.includeContributors = true,
    this.includeProgress = true,
  });

  /// نسخ الخيارات مع تعديل قيم محددة
  ReportFilterOptions copyWith({
    bool? includeTaskDetails,
    bool? includeComments,
    bool? includeAttachments,
    bool? includeSubtasks,
    bool? includeTimeTracking,
    bool? includeHistory,
    bool? includeContributors,
    bool? includeProgress,
  }) {
    return ReportFilterOptions(
      includeTaskDetails: includeTaskDetails ?? this.includeTaskDetails,
      includeComments: includeComments ?? this.includeComments,
      includeAttachments: includeAttachments ?? this.includeAttachments,
      includeSubtasks: includeSubtasks ?? this.includeSubtasks,
      includeTimeTracking: includeTimeTracking ?? this.includeTimeTracking,
      includeHistory: includeHistory ?? this.includeHistory,
      includeContributors: includeContributors ?? this.includeContributors,
      includeProgress: includeProgress ?? this.includeProgress,
    );
  }

  /// تحديد جميع الخيارات
  void selectAll() {
    includeTaskDetails = true;
    includeComments = true;
    includeAttachments = true;
    includeSubtasks = true;
    includeTimeTracking = true;
    includeHistory = true;
    includeContributors = true;
    includeProgress = true;
  }

  /// إلغاء تحديد جميع الخيارات
  void deselectAll() {
    includeTaskDetails = false;
    includeComments = false;
    includeAttachments = false;
    includeSubtasks = false;
    includeTimeTracking = false;
    includeHistory = false;
    includeContributors = false;
    includeProgress = false;
  }

  /// التحقق من وجود أي خيار محدد
  bool get hasAnySelection {
    return includeTaskDetails ||
        includeComments ||
        includeAttachments ||
        includeSubtasks ||
        includeTimeTracking ||
        includeHistory ||
        includeContributors ||
        includeProgress;
  }

  /// عدد الأقسام المحددة
  int get selectedCount {
    int count = 0;
    if (includeTaskDetails) count++;
    if (includeComments) count++;
    if (includeAttachments) count++;
    if (includeSubtasks) count++;
    if (includeTimeTracking) count++;
    if (includeHistory) count++;
    if (includeContributors) count++;
    if (includeProgress) count++;
    return count;
  }
}

/// حوار فلتر التقرير
class ReportFilterDialog extends StatefulWidget {
  final ReportFilterOptions initialOptions;
  final Map<String, dynamic> reportData;

  const ReportFilterDialog({
    Key? key,
    required this.initialOptions,
    required this.reportData,
  }) : super(key: key);

  @override
  State<ReportFilterDialog> createState() => _ReportFilterDialogState();
}

class _ReportFilterDialogState extends State<ReportFilterDialog> {
  late ReportFilterOptions options;

  @override
  void initState() {
    super.initState();
    options = ReportFilterOptions(
      includeTaskDetails: widget.initialOptions.includeTaskDetails,
      includeComments: widget.initialOptions.includeComments,
      includeAttachments: widget.initialOptions.includeAttachments,
      includeSubtasks: widget.initialOptions.includeSubtasks,
      includeTimeTracking: widget.initialOptions.includeTimeTracking,
      includeHistory: widget.initialOptions.includeHistory,
      includeContributors: widget.initialOptions.includeContributors,
      includeProgress: widget.initialOptions.includeProgress,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.filter_list, color: Colors.blue),
          SizedBox(width: 8),
          Text(
            'فلتر أقسام التقرير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(maxHeight: 500),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أزرار التحديد السريع
              _buildQuickSelectionButtons(),
              
              SizedBox(height: 16),
              
              // قائمة الأقسام
              _buildSectionsList(),
              
              SizedBox(height: 16),
              
              // معلومات الملخص
              _buildSummaryInfo(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: options.hasAnySelection
              ? () => Navigator.of(context).pop(options)
              : null,
          child: Text('إنشاء التقرير'),
        ),
      ],
    );
  }

  /// أزرار التحديد السريع
  Widget _buildQuickSelectionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              setState(() {
                options.selectAll();
              });
            },
            icon: Icon(Icons.select_all, size: 16),
            label: Text('تحديد الكل'),
          ),
        ),
        SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              setState(() {
                options.deselectAll();
              });
            },
            icon: Icon(Icons.deselect, size: 16),
            label: Text('إلغاء الكل'),
          ),
        ),
      ],
    );
  }

  /// قائمة أقسام التقرير
  Widget _buildSectionsList() {
    return Column(
      children: [
        _buildSectionTile(
          title: 'تفاصيل المهمة',
          subtitle: 'المعلومات الأساسية والوصف',
          icon: Icons.assignment,
          value: options.includeTaskDetails,
          onChanged: (value) {
            setState(() {
              options.includeTaskDetails = value ?? false;
            });
          },
          isRequired: true,
        ),
        
        _buildSectionTile(
          title: 'التعليقات والملاحظات',
          subtitle: _getDataCount('comments', 'تعليق'),
          icon: Icons.comment,
          value: options.includeComments,
          onChanged: (value) {
            setState(() {
              options.includeComments = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'المرفقات والوثائق',
          subtitle: _getDataCount('attachments', 'مرفق'),
          icon: Icons.attach_file,
          value: options.includeAttachments,
          onChanged: (value) {
            setState(() {
              options.includeAttachments = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'المهام الفرعية',
          subtitle: _getDataCount('subtasks', 'مهمة فرعية'),
          icon: Icons.list,
          value: options.includeSubtasks,
          onChanged: (value) {
            setState(() {
              options.includeSubtasks = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'تتبع الوقت',
          subtitle: _getDataCount('timeTracking', 'سجل وقت'),
          icon: Icons.access_time,
          value: options.includeTimeTracking,
          onChanged: (value) {
            setState(() {
              options.includeTimeTracking = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'سجل الأحداث والتحويلات',
          subtitle: _getDataCount('history', 'حدث'),
          icon: Icons.history,
          value: options.includeHistory,
          onChanged: (value) {
            setState(() {
              options.includeHistory = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'المساهمون في المهمة',
          subtitle: _getDataCount('contributors', 'مساهم'),
          icon: Icons.people,
          value: options.includeContributors,
          onChanged: (value) {
            setState(() {
              options.includeContributors = value ?? false;
            });
          },
        ),
        
        _buildSectionTile(
          title: 'تقدم المهمة والإحصائيات',
          subtitle: _getDataCount('progressTrackers', 'تحديث تقدم'),
          icon: Icons.trending_up,
          value: options.includeProgress,
          onChanged: (value) {
            setState(() {
              options.includeProgress = value ?? false;
            });
          },
        ),
      ],
    );
  }

  /// بناء عنصر قسم
  Widget _buildSectionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool?> onChanged,
    bool isRequired = false,
  }) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: CheckboxListTile(
        title: Row(
          children: [
            Icon(icon, size: 20, color: value ? Colors.blue : Colors.grey),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: value ? Colors.black : Colors.grey[600],
                ),
              ),
            ),
            if (isRequired)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  'مطلوب',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        value: value,
        onChanged: isRequired ? null : onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  /// الحصول على عدد البيانات لكل قسم
  String _getDataCount(String key, String unit) {
    final data = widget.reportData[key] as List?;
    final count = data?.length ?? 0;
    
    if (count == 0) {
      return 'لا توجد بيانات';
    } else if (count == 1) {
      return '$count $unit';
    } else {
      return '$count ${unit}';
    }
  }

  /// معلومات الملخص
  Widget _buildSummaryInfo() {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 16),
              SizedBox(width: 4),
              Text(
                'ملخص التقرير',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'الأقسام المحددة: ${options.selectedCount} من 8',
            style: TextStyle(fontSize: 12),
          ),
          if (!options.hasAnySelection)
            Padding(
              padding: EdgeInsets.only(top: 4),
              child: Text(
                'يجب تحديد قسم واحد على الأقل',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
