
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// ويدجت يحتوي على فلاتر التقارير (التاريخ، القسم، إلخ)
class ReportFilterWidget extends StatefulWidget {
  final Function(DateTime, DateTime) onDateRangeChanged;
  final Function(int?) onDepartmentChanged;
  final VoidCallback onGenerateReport;

  const ReportFilterWidget({
    super.key,
    required this.onDateRangeChanged,
    required this.onDepartmentChanged,
    required this.onGenerateReport,
  });

  @override
  State<ReportFilterWidget> createState() => _ReportFilterWidgetState();
}

class _ReportFilterWidgetState extends State<ReportFilterWidget> {
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    _startDate = DateTime.now().subtract(const Duration(days: 30));
    _endDate = DateTime.now();
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      // إضافة اللغة العربية
      locale: const Locale('ar'),
    );

    if (picked != null &&
        (picked.start != _startDate || picked.end != _endDate)) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      widget.onDateRangeChanged(_startDate, _endDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // زر اختيار التاريخ
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDateRange(context),
                    child: Column(
                      children: [
                        const Text('الفترة المحددة', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Text(
                          '${DateFormat('yyyy/MM/dd', 'ar').format(_startDate)} - ${DateFormat('yyyy/MM/dd', 'ar').format(_endDate)}',
                          style: const TextStyle(color: Colors.blue, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),

                // يمكن إضافة فلتر القسم هنا لاحقًا
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: widget.onGenerateReport,
              icon: const Icon(Icons.analytics),
              label: const Text('توليد التقرير'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
