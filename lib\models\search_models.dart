/// أنواع نتائج البحث
enum SearchResultType {
  task,
  message,
  user,
  document,
  event,
  report,
  other,
}

/// نموذج نتيجة البحث
class SearchResult {
  final String id;
  final SearchResultType type;
  final String title;
  final String subtitle;
  final DateTime? date;
  final double relevance;
  final dynamic data;

  const SearchResult({
    required this.id,
    required this.type,
    required this.title,
    required this.subtitle,
    this.date,
    required this.relevance,
    this.data,
  });

  /// تحويل من JSON
  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      id: json['id'].toString(),
      type: SearchResultType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => SearchResultType.other,
      ),
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      date: json['date'] != null ? DateTime.parse(json['date']) : null,
      relevance: (json['relevance'] ?? 0.0).toDouble(),
      data: json['data'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'subtitle': subtitle,
      'date': date?.toIso8601String(),
      'relevance': relevance,
      'data': data,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchResult &&
        other.id == id &&
        other.type == type;
  }

  @override
  int get hashCode => id.hashCode ^ type.hashCode;

  @override
  String toString() {
    return 'SearchResult(id: $id, type: $type, title: $title, relevance: $relevance)';
  }
}

/// نموذج مرشح البحث
class SearchFilter {
  final List<SearchResultType> types;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userId;
  final String? departmentId;
  final bool includeDeleted;
  final int limit;

  const SearchFilter({
    this.types = const [],
    this.startDate,
    this.endDate,
    this.userId,
    this.departmentId,
    this.includeDeleted = false,
    this.limit = 10,
  });

  /// تحويل من JSON
  factory SearchFilter.fromJson(Map<String, dynamic> json) {
    return SearchFilter(
      types: (json['types'] as List<dynamic>?)
              ?.map((type) => SearchResultType.values.firstWhere(
                    (t) => t.name == type,
                    orElse: () => SearchResultType.other,
                  ))
              .toList() ??
          [],
      startDate: json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      userId: json['userId'],
      departmentId: json['departmentId'],
      includeDeleted: json['includeDeleted'] ?? false,
      limit: json['limit'] ?? 10,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'types': types.map((type) => type.name).toList(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'userId': userId,
      'departmentId': departmentId,
      'includeDeleted': includeDeleted,
      'limit': limit,
    };
  }

  /// نسخ مع تعديل
  SearchFilter copyWith({
    List<SearchResultType>? types,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? departmentId,
    bool? includeDeleted,
    int? limit,
  }) {
    return SearchFilter(
      types: types ?? this.types,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userId: userId ?? this.userId,
      departmentId: departmentId ?? this.departmentId,
      includeDeleted: includeDeleted ?? this.includeDeleted,
      limit: limit ?? this.limit,
    );
  }
}

/// نموذج اقتراح البحث
class SearchSuggestion {
  final String text;
  final SearchResultType? type;
  final int frequency;
  final DateTime lastUsed;

  const SearchSuggestion({
    required this.text,
    this.type,
    required this.frequency,
    required this.lastUsed,
  });

  /// تحويل من JSON
  factory SearchSuggestion.fromJson(Map<String, dynamic> json) {
    return SearchSuggestion(
      text: json['text'] ?? '',
      type: json['type'] != null
          ? SearchResultType.values.firstWhere(
              (type) => type.name == json['type'],
              orElse: () => SearchResultType.other,
            )
          : null,
      frequency: json['frequency'] ?? 0,
      lastUsed: DateTime.parse(json['lastUsed']),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'type': type?.name,
      'frequency': frequency,
      'lastUsed': lastUsed.toIso8601String(),
    };
  }
}
