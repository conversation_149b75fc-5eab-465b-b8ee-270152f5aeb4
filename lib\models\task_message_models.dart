import 'user_model.dart';
import 'task_model.dart';

/// أنواع محتوى رسائل المهام
enum TaskMessageContentType {
  text(1, 'نص'),
  image(2, 'صورة'),
  file(3, 'ملف'),
  voice(4, 'صوت'),
  video(5, 'فيديو'),
  location(6, 'موقع');

  const TaskMessageContentType(this.value, this.displayName);

  final int value;
  final String displayName;

  static TaskMessageContentType fromValue(int value) {
    return TaskMessageContentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TaskMessageContentType.text,
    );
  }
}

/// نموذج رسالة المهمة - متطابق مع ASP.NET Core API
class TaskMessage {
  final String id;
  final String taskId;
  final String senderId;
  final String content;
  final TaskMessageContentType contentType;
  final String? replyToMessageId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isDeleted;
  final bool isRead;
  final bool isPinned;
  final DateTime? pinnedAt;
  final String? pinnedBy;
  final int priority;
  final bool isMarkedForFollowUp;
  final DateTime? followUpAt;
  final String? markedForFollowUpBy;
  final bool isEdited;
  final List<String>? mentionedUserIds;
  final List<String>? attachmentIds;

  // Navigation properties
  final User? sender;
  final Task? task;
  final TaskMessage? replyToMessage;

  const TaskMessage({
    required this.id,
    required this.taskId,
    required this.senderId,
    required this.content,
    this.contentType = TaskMessageContentType.text,
    this.replyToMessageId,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isRead = false,
    this.isPinned = false,
    this.pinnedAt,
    this.pinnedBy,
    this.priority = 0,
    this.isMarkedForFollowUp = false,
    this.followUpAt,
    this.markedForFollowUpBy,
    this.isEdited = false,
    this.mentionedUserIds,
    this.attachmentIds,
    this.sender,
    this.task,
    this.replyToMessage,
  });

  factory TaskMessage.fromJson(Map<String, dynamic> json) {
    return TaskMessage(
      id: json['id'].toString(),
      taskId: json['taskId'].toString(),
      senderId: json['senderId'].toString(),
      content: json['content'] as String,
      contentType: TaskMessageContentType.fromValue(json['contentType'] as int? ?? 1),
      replyToMessageId: json['replyToMessageId']?.toString(),
      createdAt: DateTime.fromMillisecondsSinceEpoch((json['createdAt'] as int) * 1000),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] as int) * 1000)
          : null,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isRead: json['isRead'] as bool? ?? false,
      isPinned: json['isPinned'] as bool? ?? false,
      pinnedAt: json['pinnedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['pinnedAt'] as int) * 1000)
          : null,
      pinnedBy: json['pinnedBy']?.toString(),
      priority: json['priority'] as int? ?? 0,
      isMarkedForFollowUp: json['isMarkedForFollowUp'] as bool? ?? false,
      followUpAt: json['followUpAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['followUpAt'] as int) * 1000)
          : null,
      markedForFollowUpBy: json['markedForFollowUpBy']?.toString(),
      isEdited: json['isEdited'] as bool? ?? false,
      mentionedUserIds: json['mentionedUserIds'] != null 
          ? List<String>.from(json['mentionedUserIds'] as List)
          : null,
      attachmentIds: json['attachmentIds'] != null 
          ? List<String>.from(json['attachmentIds'] as List)
          : null,
      sender: json['sender'] != null 
          ? User.fromJson(json['sender'] as Map<String, dynamic>)
          : null,
      task: json['task'] != null 
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      replyToMessage: json['replyToMessage'] != null 
          ? TaskMessage.fromJson(json['replyToMessage'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'senderId': senderId,
      'content': content,
      'contentType': contentType.value,
      'replyToMessageId': replyToMessageId,
      'createdAt': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updatedAt': updatedAt != null ? updatedAt!.millisecondsSinceEpoch ~/ 1000 : null,
      'isDeleted': isDeleted,
      'isRead': isRead,
      'isPinned': isPinned,
      'pinnedAt': pinnedAt != null ? pinnedAt!.millisecondsSinceEpoch ~/ 1000 : null,
      'pinnedBy': pinnedBy,
      'priority': priority,
      'isMarkedForFollowUp': isMarkedForFollowUp,
      'followUpAt': followUpAt != null ? followUpAt!.millisecondsSinceEpoch ~/ 1000 : null,
      'markedForFollowUpBy': markedForFollowUpBy,
      'isEdited': isEdited,
      'mentionedUserIds': mentionedUserIds,
      'attachmentIds': attachmentIds,
    };
  }

  TaskMessage copyWith({
    String? id,
    String? taskId,
    String? senderId,
    String? content,
    TaskMessageContentType? contentType,
    String? replyToMessageId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDeleted,
    bool? isRead,
    bool? isPinned,
    DateTime? pinnedAt,
    String? pinnedBy,
    int? priority,
    bool? isMarkedForFollowUp,
    DateTime? followUpAt,
    String? markedForFollowUpBy,
    bool? isEdited,
    List<String>? mentionedUserIds,
    List<String>? attachmentIds,
    User? sender,
    Task? task,
    TaskMessage? replyToMessage,
  }) {
    return TaskMessage(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      contentType: contentType ?? this.contentType,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isRead: isRead ?? this.isRead,
      isPinned: isPinned ?? this.isPinned,
      pinnedAt: pinnedAt ?? this.pinnedAt,
      pinnedBy: pinnedBy ?? this.pinnedBy,
      priority: priority ?? this.priority,
      isMarkedForFollowUp: isMarkedForFollowUp ?? this.isMarkedForFollowUp,
      followUpAt: followUpAt ?? this.followUpAt,
      markedForFollowUpBy: markedForFollowUpBy ?? this.markedForFollowUpBy,
      isEdited: isEdited ?? this.isEdited,
      mentionedUserIds: mentionedUserIds ?? this.mentionedUserIds,
      attachmentIds: attachmentIds ?? this.attachmentIds,
      sender: sender ?? this.sender,
      task: task ?? this.task,
      replyToMessage: replyToMessage ?? this.replyToMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskMessage(id: $id, taskId: $taskId, senderId: $senderId, content: $content)';
  }
}

/// نموذج طلب إرسال رسالة مهمة
class SendTaskMessageRequest {
  final int taskId;
  final String content;
  final int contentType;
  final int? replyToMessageId;
  final int priority;
  final List<int>? mentionedUserIds;
  final List<int>? attachmentIds;

  const SendTaskMessageRequest({
    required this.taskId,
    required this.content,
    this.contentType = 1,
    this.replyToMessageId,
    this.priority = 0,
    this.mentionedUserIds,
    this.attachmentIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'content': content,
      'contentType': contentType,
      'replyToMessageId': replyToMessageId,
      'priority': priority,
      'mentionedUserIds': mentionedUserIds,
      'attachmentIds': attachmentIds,
    };
  }
}

/// نموذج طلب تحديث رسالة مهمة
class UpdateTaskMessageRequest {
  final String content;

  const UpdateTaskMessageRequest({
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
    };
  }
}
