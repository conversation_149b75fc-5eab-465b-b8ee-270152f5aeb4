import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:desktop_drop/desktop_drop.dart';
import 'package:cross_file/cross_file.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/unified_permission_service.dart';

/// منطقة إسقاط الملفات
/// تسمح للمستخدم بسحب وإسقاط الملفات
///
/// تم تنفيذ:
/// - إضافة معاينة الملفات قبل الرفع
/// - إظهار عدد الملفات التي تم سحبها
/// - إضافة خيار لإلغاء الملفات قبل الرفع
/// - عرض حجم الملفات
/// - تحسين واجهة المستخدم
///
/// TODO: التحسينات المستقبلية:
/// - إضافة شريط تقدم للرفع مع عرض الوقت المتبقي
/// - دعم الرفع في الخلفية مع إشعارات عند الانتهاء
/// - تحسين عرض الصور والملفات المرفقة
/// - إضافة دعم معاينة ملفات PDF والمستندات
/// - إضافة إمكانية التعديل المباشر للنصوص والصور
/// - إضافة إمكانية التعليق على المرفقات
/// - دعم التوقيع الرقمي على المستندات
class DropZoneWidget extends StatefulWidget {
  /// محتوى المنطقة
  final Widget child;

  /// دالة يتم استدعاؤها عند إسقاط الملفات
  final Function(List<XFile>) onDroppedFiles;

  /// هل يتم عرض مؤشر السحب والإسقاط
  final bool showDropIndicator;

  /// إنشاء منطقة إسقاط الملفات
  const DropZoneWidget({
    super.key,
    required this.child,
    required this.onDroppedFiles,
    this.showDropIndicator = true,
  });

  @override
  State<DropZoneWidget> createState() => _DropZoneWidgetState();
}

class _DropZoneWidgetState extends State<DropZoneWidget> {
  bool _isDragging = false;
  List<XFile> _previewFiles = [];
  bool _showPreview = false;

  @override
  Widget build(BuildContext context) {
    // على منصة الويب وسطح المكتب، نستخدم DropTarget
    if (kIsWeb || !Platform.isAndroid && !Platform.isIOS) {
      return DropTarget(
        onDragDone: (detail) {
          setState(() {
            _isDragging = false;
            _previewFiles = detail.files;
            _showPreview = _previewFiles.isNotEmpty;
          });

          if (!_showPreview) {
            // إذا لم يتم تفعيل المعاينة، نرسل الملفات مباشرة
            widget.onDroppedFiles(detail.files);
          }
        },
        onDragEntered: (detail) {
          setState(() {
            _isDragging = true;
          });
        },
        onDragExited: (detail) {
          setState(() {
            _isDragging = false;
          });
        },
        child: _showPreview ? _buildPreview() : _buildContent(),
      );
    } else {
      // على الأجهزة المحمولة، نعرض المحتوى فقط
      return widget.child;
    }
  }

  /// بناء محتوى منطقة الإسقاط
  Widget _buildContent() {
    if (!_isDragging || !widget.showDropIndicator) {
      return widget.child;
    }

    // عرض مؤشر السحب والإسقاط
    return Stack(
      children: [
        widget.child,
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(40),
              border: Border.all(
                color: AppColors.primary,
                width: 3,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12),
              // إضافة تأثير وميض للحدود
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withAlpha(100),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: AppColors.primary.withAlpha(50),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // إضافة تأثير دائري حول الأيقونة
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.primary.withAlpha(100),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.file_upload,
                      size: 48,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // تحسين نص الإسقاط
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(20),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.primary.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'أفلت الملفات هنا'.tr,
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // إضافة نص توضيحي إضافي
                  Text(
                    'اسحب وأفلت الملفات هنا للرفع'.tr,
                    style: TextStyle(
                      color: AppColors.primary.withAlpha(200),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء معاينة الملفات
  Widget _buildPreview() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(76),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان المعاينة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'معاينة الملفات (${_previewFiles.length})'.tr,
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    // زر الرفع
                    ElevatedButton.icon(
                      onPressed: _uploadFiles,
                      icon: const Icon(Icons.upload),
                      label: Text('رفع'.tr),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    // زر الإلغاء
                    IconButton(
                      onPressed: _cancelPreview,
                      icon: const Icon(Icons.close),
                      tooltip: 'إلغاء'.tr,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // قائمة الملفات
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(12),
              itemCount: _previewFiles.length,
              itemBuilder: (context, index) {
                final file = _previewFiles[index];
                return _buildFilePreviewItem(file, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معاينة ملف
  Widget _buildFilePreviewItem(XFile file, int index) {
    final fileName = file.name;
    final fileExtension = path.extension(fileName).toLowerCase();

    // تحديد نوع الأيقونة بناءً على امتداد الملف
    IconData fileIcon = Icons.insert_drive_file;
    Color iconColor = Colors.blue;

    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp'].contains(fileExtension)) {
      fileIcon = Icons.image;
      iconColor = Colors.green;
    } else if (['.pdf'].contains(fileExtension)) {
      fileIcon = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['.doc', '.docx', '.txt', '.rtf'].contains(fileExtension)) {
      fileIcon = Icons.description;
      iconColor = Colors.blue;
    } else if (['.xls', '.xlsx', '.csv'].contains(fileExtension)) {
      fileIcon = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['.ppt', '.pptx'].contains(fileExtension)) {
      fileIcon = Icons.slideshow;
      iconColor = Colors.orange;
    } else if (['.zip', '.rar', '.7z'].contains(fileExtension)) {
      fileIcon = Icons.folder_zip;
      iconColor = Colors.purple;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(fileIcon, color: iconColor, size: 36),
        title: Text(fileName),
        subtitle: FutureBuilder<String>(
          future: _getFileSize(file),
          builder: (context, snapshot) {
            return Text(snapshot.data ?? 'جاري حساب الحجم...'.tr);
          },
        ),
        trailing: UnifiedPermissionService().canDeleteFiles()
            ? IconButton(
                icon: const Icon(Icons.delete_outline, color: Colors.red),
                onPressed: () => _removeFile(index),
                tooltip: 'إزالة'.tr,
              )
            : null,
      ),
    );
  }

  /// الحصول على حجم الملف
  Future<String> _getFileSize(XFile file) async {
    try {
      final bytes = await file.length();
      if (bytes < 1024) {
        return '$bytes B';
      } else if (bytes < 1024 * 1024) {
        return '${(bytes / 1024).toStringAsFixed(1)} KB';
      } else {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
    } catch (e) {
      return 'غير معروف'.tr;
    }
  }

  /// إزالة ملف من المعاينة
  void _removeFile(int index) {
    setState(() {
      _previewFiles.removeAt(index);
      if (_previewFiles.isEmpty) {
        _showPreview = false;
      }
    });
  }

  /// إلغاء المعاينة
  void _cancelPreview() {
    setState(() {
      _previewFiles.clear();
      _showPreview = false;
    });
  }

  /// رفع الملفات
  void _uploadFiles() {
    if (_previewFiles.isNotEmpty) {
      widget.onDroppedFiles(_previewFiles);
      setState(() {
        _previewFiles.clear();
        _showPreview = false;
      });
    }
  }
}
