// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import '../models/enhanced_permission_models.dart';
// import '../models/permission_models.dart';
// import '../models/user_permission_models.dart';
// import '../services/api/enhanced_permissions_api_service.dart';

// /// متحكم إدارة الأدوار والصلاحيات المحسنة
// class EnhancedPermissionsController extends GetxController {
//   late final EnhancedPermissionsApiService _apiService;

//   // البيانات التفاعلية
//   final RxList<Permission> allPermissions = <Permission>[].obs;
//   // final RxList<CustomRole> customRoles = <CustomRole>[].obs;
//   final RxList<String> permissionGroups = <String>[].obs;
//   final RxBool isLoading = false.obs;
//   final RxString error = ''.obs;

//   // الدور المحدد حالياً
//   // final Rx<CustomRole?> selectedRole = Rx<CustomRole?>(null);
//   final RxList<UserPermission> selectedRolePermissions = <UserPermission>[].obs;

//   @override
//   void onInit() {
//     super.onInit();
//     _initializeService();
//   }

//   /// تهيئة الخدمة
//   Future<void> _initializeService() async {
//     try {
//       _apiService = EnhancedPermissionsApiService();
//       await _apiService.initialize();
//       await loadInitialData();
//     } catch (e) {
//       debugPrint('خطأ في تهيئة متحكم الصلاحيات المحسنة: $e');
//       error.value = 'خطأ في تهيئة النظام: ${e.toString()}';
//     }
//   }

//   /// تحميل البيانات الأولية
//   Future<void> loadInitialData() async {
//     isLoading.value = true;
//     error.value = '';

//     try {
//       await Future.wait([
//         loadAllPermissions(),
//         loadCustomRoles(),
//         loadPermissionGroups(),
//       ]);
//     } catch (e) {
//       debugPrint('خطأ في تحميل البيانات الأولية: $e');
//       error.value = 'خطأ في تحميل البيانات: ${e.toString()}';
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   /// تحميل جميع الصلاحيات
//   Future<void> loadAllPermissions() async {
//     try {
//       final permissions = await _apiService.getAllPermissions();
//       allPermissions.assignAll(permissions);
//       debugPrint('تم تحميل ${permissions.length} صلاحية');
//     } catch (e) {
//       debugPrint('خطأ في تحميل الصلاحيات: $e');
//       throw Exception('خطأ في تحميل الصلاحيات');
//     }
//   }

//   /// تحميل الأدوار المخصصة
//   Future<void> loadCustomRoles() async {
//     try {
//       final roles = await _apiService.getCustomRoles();
//       customRoles.assignAll(roles);
//       debugPrint('تم تحميل ${roles.length} دور مخصص');
//     } catch (e) {
//       debugPrint('خطأ في تحميل الأدوار المخصصة: $e');
//       throw Exception('خطأ في تحميل الأدوار المخصصة');
//     }
//   }

//   /// تحميل مجموعات الصلاحيات
//   Future<void> loadPermissionGroups() async {
//     try {
//       final groups = await _apiService.getPermissionGroups();
//       permissionGroups.assignAll(groups);
//       debugPrint('تم تحميل ${groups.length} مجموعة صلاحيات');
//     } catch (e) {
//       debugPrint('خطأ في تحميل مجموعات الصلاحيات: $e');
//       throw Exception('خطأ في تحميل مجموعات الصلاحيات');
//     }
//   }

//   /// إنشاء دور مخصص جديد
//   Future<bool> createCustomRole({
//     required String name,
//     String? description,
//     String? category,
//     String? color,
//     String? icon,
//     int level = 1,
//   }) async {
//     isLoading.value = true;
//     error.value = '';

//     try {
//       final request = CreateRoleRequest(
//         name: name,
//         description: description,
//         category: category,
//         color: color,
//         icon: icon,
//         level: level,
//       );

//       final newRole = await _apiService.createCustomRole(request);
//       customRoles.add(newRole);

//       debugPrint('تم إنشاء الدور المخصص بنجاح: ${newRole.name}');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في إنشاء الدور المخصص: $e');
//       error.value = 'خطأ في إنشاء الدور: ${e.toString()}';
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   /// تحديث دور مخصص
//   Future<bool> updateCustomRole({
//     required int id,
//     required String name,
//     String? description,
//     String? category,
//     String? color,
//     String? icon,
//     int level = 1,
//   }) async {
//     isLoading.value = true;
//     error.value = '';

//     try {
//       final request = UpdateRoleRequest(
//         id: id,
//         name: name,
//         description: description,
//         category: category,
//         color: color,
//         icon: icon,
//         level: level,
//       );

//       final updatedRole = await _apiService.updateCustomRole(request);
      
//       // تحديث القائمة المحلية
//       final index = customRoles.indexWhere((role) => role.id == id);
//       if (index != -1) {
//         customRoles[index] = updatedRole;
//       }

//       // تحديث الدور المحدد إذا كان هو نفسه
//       if (selectedRole.value?.id == id) {
//         selectedRole.value = updatedRole;
//       }

//       debugPrint('تم تحديث الدور المخصص بنجاح: ${updatedRole.name}');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في تحديث الدور المخصص: $e');
//       error.value = 'خطأ في تحديث الدور: ${e.toString()}';
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   /// حذف دور مخصص
//   Future<bool> deleteCustomRole(int roleId) async {
//     isLoading.value = true;
//     error.value = '';

//     try {
//       await _apiService.deleteCustomRole(roleId);
      
//       // إزالة من القائمة المحلية
//       customRoles.removeWhere((role) => role.id == roleId);

//       // إلغاء التحديد إذا كان هو المحدد
//       if (selectedRole.value?.id == roleId) {
//         selectedRole.value = null;
//         selectedRolePermissions.clear();
//       }

//       debugPrint('تم حذف الدور المخصص بنجاح: $roleId');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في حذف الدور المخصص: $e');
//       error.value = 'خطأ في حذف الدور: ${e.toString()}';
//       return false;
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   /// تحديد دور للعمل عليه
//   Future<void> selectRole(CustomRole role) async {
//     selectedRole.value = role;
//     await loadRolePermissions(role.id);
//   }

//   /// تحميل صلاحيات دور معين
//   Future<void> loadRolePermissions(int roleId) async {
//     try {
//       // في هذا التنفيذ، نحتاج إلى endpoint خاص لصلاحيات الدور
//       // مؤقتاً سنستخدم قائمة فارغة
//       selectedRolePermissions.clear();
//       debugPrint('تم تحميل صلاحيات الدور: $roleId');
//     } catch (e) {
//       debugPrint('خطأ في تحميل صلاحيات الدور: $e');
//       error.value = 'خطأ في تحميل صلاحيات الدور: ${e.toString()}';
//     }
//   }

//   /// منح صلاحية للمستخدم
//   Future<bool> grantPermissionToUser({
//     required int userId,
//     required int permissionId,
//     int? expiresAt,
//   }) async {
//     try {
//       await _apiService.grantPermissionToUser(
//         userId: userId,
//         permissionId: permissionId,
//         expiresAt: expiresAt,
//       );
//       debugPrint('تم منح الصلاحية للمستخدم بنجاح');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في منح الصلاحية: $e');
//       error.value = 'خطأ في منح الصلاحية: ${e.toString()}';
//       return false;
//     }
//   }

//   /// إلغاء صلاحية من المستخدم
//   Future<bool> revokePermissionFromUser({
//     required int userId,
//     required int permissionId,
//   }) async {
//     try {
//       await _apiService.revokePermissionFromUser(
//         userId: userId,
//         permissionId: permissionId,
//       );
//       debugPrint('تم إلغاء الصلاحية من المستخدم بنجاح');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في إلغاء الصلاحية: $e');
//       error.value = 'خطأ في إلغاء الصلاحية: ${e.toString()}';
//       return false;
//     }
//   }

//   /// التحقق من صلاحية المستخدم
//   Future<bool> checkUserPermission({
//     required int userId,
//     required int permissionId,
//   }) async {
//     try {
//       return await _apiService.checkUserPermission(
//         userId: userId,
//         permissionId: permissionId,
//       );
//     } catch (e) {
//       debugPrint('خطأ في التحقق من الصلاحية: $e');
//       return false;
//     }
//   }

//   /// تحديث صلاحيات متعددة للمستخدم
//   Future<bool> updateUserPermissions({
//     required int userId,
//     required List<int> permissionIds,
//   }) async {
//     try {
//       await _apiService.updateUserPermissions(
//         userId: userId,
//         permissionIds: permissionIds,
//       );
//       debugPrint('تم تحديث صلاحيات المستخدم بنجاح');
//       return true;
//     } catch (e) {
//       debugPrint('خطأ في تحديث صلاحيات المستخدم: $e');
//       error.value = 'خطأ في تحديث صلاحيات المستخدم: ${e.toString()}';
//       return false;
//     }
//   }

//   /// مسح الأخطاء
//   void clearError() {
//     error.value = '';
//   }

//   /// الحصول على صلاحيات مستخدم معين
//   Future<List<UserPermission>> getUserPermissions(int userId) async {
//     try {
//       return await _apiService.getUserPermissions(userId);
//     } catch (e) {
//       debugPrint('خطأ في الحصول على صلاحيات المستخدم: $e');
//       error.value = 'خطأ في الحصول على صلاحيات المستخدم: ${e.toString()}';
//       return [];
//     }
//   }

//   /// الحصول على جميع الصلاحيات
//   Future<List<Permission>> getAllPermissions() async {
//     try {
//       return await _apiService.getAllPermissions();
//     } catch (e) {
//       debugPrint('خطأ في الحصول على الصلاحيات: $e');
//       error.value = 'خطأ في الحصول على الصلاحيات: ${e.toString()}';
//       return [];
//     }
//   }

//   /// إعادة تحميل البيانات
//   @override
//   Future<void> refresh() async {
//     await loadInitialData();
//   }
// }
