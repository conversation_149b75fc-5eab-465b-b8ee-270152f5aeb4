import 'package:flutter/material.dart';
import 'chart_interface.dart';

/// الفئة الأساسية للمخططات
/// 
/// توفر الوظائف المشتركة لجميع المخططات
abstract class BaseChart extends ChartInterface {
  BaseChart({
    required super.data,
    required super.settings,
    required super.theme,
  });

  @override
  Widget build() {
    return Container(
      height: getHeight(),
      width: getWidth(),
      decoration: BoxDecoration(
        color: theme.backgroundColor,
        borderRadius: BorderRadius.circular(theme.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: theme.elevation,
            offset: Offset(0, theme.elevation / 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان المخطط
          if (showTitle()) _buildTitle(),
          
          // محتوى المخطط
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: buildChart(),
            ),
          ),
          
          // المفتاح
          if (showLegend()) _buildLegend(),
        ],
      ),
    );
  }

  /// بناء محتوى المخطط (يجب تنفيذها في الفئات المشتقة)
  Widget buildChart();

  /// بناء عنوان المخطط
  Widget _buildTitle() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(theme.borderRadius),
          topRight: Radius.circular(theme.borderRadius),
        ),
      ),
      child: Text(
        getTitle(),
        style: theme.titleStyle,
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء المفتاح
  Widget _buildLegend() {
    final legendItems = getLegendItems();
    if (legendItems.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 16.0,
        runSpacing: 8.0,
        children: legendItems.map((item) => _buildLegendItem(item)).toList(),
      ),
    );
  }

  /// بناء عنصر المفتاح
  Widget _buildLegendItem(LegendItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: item.color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          item.label,
          style: theme.labelStyle,
        ),
      ],
    );
  }

  /// الحصول على عناصر المفتاح (يمكن تخصيصها في الفئات المشتقة)
  List<LegendItem> getLegendItems() {
    return [];
  }

  /// التحقق الأساسي من صحة البيانات
  @override
  bool validateData(Map<String, dynamic> data) {
    return data.isNotEmpty;
  }

  /// تنسيق الأرقام
  String formatNumber(num value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}م';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}ك';
    }
    return value.toString();
  }

  /// تنسيق النسب المئوية
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(1)}%';
  }

  /// الحصول على لون حسب الفهرس
  Color getColorByIndex(int index) {
    final colors = getColors();
    return colors[index % colors.length];
  }

  /// بناء مؤشر التحميل
  Widget buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// بناء رسالة خطأ
  Widget buildErrorMessage(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              color: Colors.red.shade600,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء رسالة البيانات الفارغة
  Widget buildEmptyMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات لعرضها',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

/// عنصر المفتاح
class LegendItem {
  final String label;
  final Color color;

  const LegendItem({
    required this.label,
    required this.color,
  });
}
