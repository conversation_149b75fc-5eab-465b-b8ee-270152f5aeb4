import 'user_model.dart';
import 'task_models.dart';

/// نموذج المرفق
class Attachment {
  final int id;
  final int taskId;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String fileType;
  final int uploadedBy;
  final int uploadedAt;
  final bool isDeleted;

  // Navigation properties
  final Task? task;
  final User? uploadedByUser;

  const Attachment({
    required this.id,
    required this.taskId,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.fileType,
    required this.uploadedBy,
    required this.uploadedAt,
    this.isDeleted = false,
    this.task,
    this.uploadedByUser,
  });

  factory Attachment.fromJson(Map<String, dynamic> json) {
    return Attachment(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: json['fileSize'] as int,
      fileType: json['fileType'] as String,
      uploadedBy: json['uploadedBy'] as int,
      uploadedAt: json['uploadedAt'] as int,
      isDeleted: json['isDeleted'] as bool? ?? false,
      task: json['task'] != null 
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      uploadedByUser: json['uploadedByNavigation'] != null 
          ? User.fromJson(json['uploadedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'fileType': fileType,
      'uploadedBy': uploadedBy,
      'uploadedAt': uploadedAt,
      'isDeleted': isDeleted,
    };
  }

  Attachment copyWith({
    int? id,
    int? taskId,
    String? fileName,
    String? filePath,
    int? fileSize,
    String? fileType,
    int? uploadedBy,
    int? uploadedAt,
    bool? isDeleted,
    Task? task,
    User? uploadedByUser,
  }) {
    return Attachment(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      fileType: fileType ?? this.fileType,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      task: task ?? this.task,
      uploadedByUser: uploadedByUser ?? this.uploadedByUser,
    );
  }

  /// الحصول على تاريخ الرفع كـ DateTime
  DateTime get uploadedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(uploadedAt * 1000);

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// التحقق من كون الملف صورة
  bool get isImage => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(fileType.toLowerCase());

  /// التحقق من كون الملف PDF
  bool get isPdf => fileType.toLowerCase() == 'pdf';

  /// التحقق من كون الملف مستند نصي
  bool get isDocument => ['doc', 'docx', 'txt', 'rtf', 'odt'].contains(fileType.toLowerCase());

  /// التحقق من كون الملف فيديو
  bool get isVideo => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].contains(fileType.toLowerCase());

  /// التحقق من كون الملف صوت
  bool get isAudio => ['mp3', 'wav', 'flac', 'aac', 'ogg'].contains(fileType.toLowerCase());

  @override
  String toString() {
    return 'Attachment(id: $id, fileName: $fileName, fileType: $fileType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attachment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب رفع مرفق
class UploadAttachmentRequest {
  final int taskId;
  final String fileName;
  final String fileType;

  const UploadAttachmentRequest({
    required this.taskId,
    required this.fileName,
    required this.fileType,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'fileName': fileName,
      'fileType': fileType,
    };
  }
}
