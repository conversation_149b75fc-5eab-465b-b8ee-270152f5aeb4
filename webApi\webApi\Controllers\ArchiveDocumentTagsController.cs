using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// متحكم ربط مستندات الأرشيف بالعلامات
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ArchiveDocumentTagsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ArchiveDocumentTagsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع روابط المستندات والعلامات
        /// </summary>
        /// <returns>قائمة بجميع روابط المستندات والعلامات</returns>
        /// <response code="200">إرجاع قائمة روابط المستندات والعلامات</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> GetArchiveDocumentTags()
        {
            var documentTags = await _context.ArchiveDocuments
                .Where(d => !d.IsDeleted)
                .SelectMany(d => d.Tags.Where(t => !t.IsDeleted), (d, t) => new
                {
                    DocumentId = d.Id,
                    TagId = t.Id,
                    Document = new
                    {
                        d.Id,
                        d.Title,
                        d.FileName,
                        d.CreatedAt
                    },
                    Tag = new
                    {
                        t.Id,
                        t.Name,
                        t.Color,
                        t.IsActive
                    }
                })
                .ToListAsync();

            return Ok(documentTags);
        }

        /// <summary>
        /// الحصول على علامات مستند محدد
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        /// <returns>قائمة علامات المستند</returns>
        /// <response code="200">إرجاع قائمة علامات المستند</response>
        /// <response code="404">المستند غير موجود</response>
        [HttpGet("document/{documentId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<object>>> GetDocumentTags(int documentId)
        {
            var document = await _context.ArchiveDocuments
                .Include(d => d.Tags)
                .FirstOrDefaultAsync(d => d.Id == documentId && !d.IsDeleted);

            if (document == null)
            {
                return NotFound();
            }

            var documentTags = document.Tags
                .Where(t => !t.IsDeleted)
                .Select(t => new
                {
                    DocumentId = documentId,
                    TagId = t.Id,
                    Tag = new
                    {
                        t.Id,
                        t.Name,
                        t.Color,
                        t.IsActive
                    }
                });

            return Ok(documentTags);
        }

        /// <summary>
        /// الحصول على مستندات علامة محددة
        /// </summary>
        /// <param name="tagId">معرف العلامة</param>
        /// <returns>قائمة مستندات العلامة</returns>
        /// <response code="200">إرجاع قائمة مستندات العلامة</response>
        /// <response code="404">العلامة غير موجودة</response>
        [HttpGet("tag/{tagId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<object>>> GetTagDocuments(int tagId)
        {
            var tag = await _context.ArchiveTags
                .Include(t => t.Documents)
                .FirstOrDefaultAsync(t => t.Id == tagId && !t.IsDeleted);

            if (tag == null)
            {
                return NotFound();
            }

            var tagDocuments = tag.Documents
                .Where(d => !d.IsDeleted)
                .Select(d => new
                {
                    DocumentId = d.Id,
                    TagId = tagId,
                    Document = new
                    {
                        d.Id,
                        d.Title,
                        d.FileName,
                        d.CreatedAt
                    }
                });

            return Ok(tagDocuments);
        }

        /// <summary>
        /// ربط مستند بعلامة
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        /// <param name="tagId">معرف العلامة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم ربط المستند بالعلامة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">المستند أو العلامة غير موجود</response>
        /// <response code="409">الربط موجود مسبقاً</response>
        [HttpPost("link")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<IActionResult> LinkDocumentTag([FromBody] LinkDocumentTagRequest request)
        {
            if (request.DocumentId <= 0 || request.TagId <= 0)
            {
                return BadRequest("معرف المستند ومعرف العلامة مطلوبان");
            }

            // التحقق من وجود المستند
            var document = await _context.ArchiveDocuments
                .Include(d => d.Tags)
                .FirstOrDefaultAsync(d => d.Id == request.DocumentId && !d.IsDeleted);

            if (document == null)
            {
                return NotFound("المستند غير موجود");
            }

            // التحقق من وجود العلامة
            var tag = await _context.ArchiveTags
                .FirstOrDefaultAsync(t => t.Id == request.TagId && !t.IsDeleted);

            if (tag == null)
            {
                return NotFound("العلامة غير موجودة");
            }

            // التحقق من عدم وجود الربط مسبقاً
            if (document.Tags.Any(t => t.Id == request.TagId))
            {
                return Conflict("الربط موجود مسبقاً");
            }

            // إضافة الربط
            document.Tags.Add(tag);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// إلغاء ربط مستند بعلامة
        /// </summary>
        /// <param name="request">طلب إلغاء الربط</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم إلغاء ربط المستند بالعلامة بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">المستند أو العلامة أو الربط غير موجود</response>
        [HttpPost("unlink")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UnlinkDocumentTag([FromBody] UnlinkDocumentTagRequest request)
        {
            if (request.DocumentId <= 0 || request.TagId <= 0)
            {
                return BadRequest("معرف المستند ومعرف العلامة مطلوبان");
            }

            // التحقق من وجود المستند
            var document = await _context.ArchiveDocuments
                .Include(d => d.Tags)
                .FirstOrDefaultAsync(d => d.Id == request.DocumentId && !d.IsDeleted);

            if (document == null)
            {
                return NotFound("المستند غير موجود");
            }

            // البحث عن العلامة في المستند
            var tag = document.Tags.FirstOrDefault(t => t.Id == request.TagId);
            if (tag == null)
            {
                return NotFound("الربط غير موجود");
            }

            // إزالة الربط
            document.Tags.Remove(tag);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// ربط مستند بعدة علامات
        /// </summary>
        /// <param name="request">طلب ربط المستند بعدة علامات</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم ربط المستند بالعلامات بنجاح</response>
        /// <response code="400">بيانات غير صحيحة</response>
        /// <response code="404">المستند غير موجود</response>
        [HttpPost("link-multiple")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> LinkDocumentMultipleTags([FromBody] LinkDocumentMultipleTagsRequest request)
        {
            if (request.DocumentId <= 0 || request.TagIds == null || !request.TagIds.Any())
            {
                return BadRequest("معرف المستند وقائمة معرفات العلامات مطلوبة");
            }

            // التحقق من وجود المستند
            var document = await _context.ArchiveDocuments
                .Include(d => d.Tags)
                .FirstOrDefaultAsync(d => d.Id == request.DocumentId && !d.IsDeleted);

            if (document == null)
            {
                return NotFound("المستند غير موجود");
            }

            // الحصول على العلامات الموجودة
            var tags = await _context.ArchiveTags
                .Where(t => request.TagIds.Contains(t.Id) && !t.IsDeleted)
                .ToListAsync();

            // إضافة العلامات الجديدة فقط
            foreach (var tag in tags)
            {
                if (!document.Tags.Any(t => t.Id == tag.Id))
                {
                    document.Tags.Add(tag);
                }
            }

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// إحصائيات روابط المستندات والعلامات
        /// </summary>
        /// <returns>إحصائيات الروابط</returns>
        /// <response code="200">إرجاع إحصائيات الروابط</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetStatistics()
        {
            var totalDocuments = await _context.ArchiveDocuments.CountAsync(d => !d.IsDeleted);
            var totalTags = await _context.ArchiveTags.CountAsync(t => !t.IsDeleted);
            var totalLinks = await _context.ArchiveDocuments
                .Where(d => !d.IsDeleted)
                .SelectMany(d => d.Tags.Where(t => !t.IsDeleted))
                .CountAsync();

            var documentsWithTags = await _context.ArchiveDocuments
                .Where(d => !d.IsDeleted && d.Tags.Any(t => !t.IsDeleted))
                .CountAsync();

            var tagsWithDocuments = await _context.ArchiveTags
                .Where(t => !t.IsDeleted && t.Documents.Any(d => !d.IsDeleted))
                .CountAsync();

            return Ok(new
            {
                TotalDocuments = totalDocuments,
                TotalTags = totalTags,
                TotalLinks = totalLinks,
                DocumentsWithTags = documentsWithTags,
                TagsWithDocuments = tagsWithDocuments,
                DocumentsWithoutTags = totalDocuments - documentsWithTags,
                TagsWithoutDocuments = totalTags - tagsWithDocuments
            });
        }
    }

    /// <summary>
    /// نموذج طلب ربط مستند بعلامة
    /// </summary>
    public class LinkDocumentTagRequest
    {
        public int DocumentId { get; set; }
        public int TagId { get; set; }
    }

    /// <summary>
    /// نموذج طلب إلغاء ربط مستند بعلامة
    /// </summary>
    public class UnlinkDocumentTagRequest
    {
        public int DocumentId { get; set; }
        public int TagId { get; set; }
    }

    /// <summary>
    /// نموذج طلب ربط مستند بعدة علامات
    /// </summary>
    public class LinkDocumentMultipleTagsRequest
    {
        public int DocumentId { get; set; }
        public List<int> TagIds { get; set; } = new List<int>();
    }
}
