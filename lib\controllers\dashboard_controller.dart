import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../services/api/dashboards_api_service.dart';
import '../services/api/system_statistics_api_service.dart';

/// متحكم لوحة المعلومات الرئيسية
class DashboardController extends GetxController {
  final DashboardsApiService _apiService = DashboardsApiService();
  final SystemStatisticsApiService _statisticsApiService = SystemStatisticsApiService();

  // المتغيرات التفاعلية
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxMap<String, dynamic> _dashboardData = <String, dynamic>{}.obs;
  final RxList<Map<String, dynamic>> _recentActivities = <Map<String, dynamic>>[].obs;
  final RxMap<String, int> _statistics = <String, int>{}.obs;
  final RxList<Dashboard> _dashboards = <Dashboard>[].obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  Map<String, dynamic> get dashboardData => _dashboardData;
  List<Map<String, dynamic>> get recentActivities => _recentActivities;
  Map<String, int> get statistics => _statistics;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  /// تحميل بيانات لوحة المعلومات
  Future<void> loadDashboardData() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // جلب البيانات الحقيقية من الـ API
      final stats = await _statisticsApiService.getDashboardStatistics();

      // تحديث بيانات لوحة المعلومات
      _dashboardData.value = {
        'totalTasks': stats['totalTasks'] ?? 0,
        'completedTasks': stats['completedTasks'] ?? 0,
        'pendingTasks': stats['pendingTasks'] ?? 0,
        'overdueTasks': stats['overdueTasks'] ?? 0,
        'totalProjects': 0, // سيتم إضافة هذا لاحقاً
        'activeProjects': 0, // سيتم إضافة هذا لاحقاً
        'totalUsers': stats['totalUsers'] ?? 0,
        'activeUsers': stats['activeUsers'] ?? 0,
      };

      _statistics.value = {
        'tasks': stats['totalTasks'] ?? 0,
        'projects': 0, // سيتم إضافة هذا لاحقاً
        'users': stats['totalUsers'] ?? 0,
        'departments': stats['totalDepartments'] ?? 0,
      };

      // تحديث الأنشطة الحديثة بناءً على البيانات الحقيقية
      _recentActivities.value = _generateRecentActivities(stats);

      debugPrint('تم تحميل بيانات لوحة المعلومات بنجاح');
    } catch (e) {
      _error.value = 'خطأ في تحميل بيانات لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل بيانات لوحة المعلومات: $e');

      // تعيين بيانات افتراضية في حالة الخطأ
      _dashboardData.value = {
        'totalTasks': 0,
        'completedTasks': 0,
        'pendingTasks': 0,
        'overdueTasks': 0,
        'totalProjects': 0,
        'activeProjects': 0,
        'totalUsers': 0,
        'activeUsers': 0,
      };

      _statistics.value = {
        'tasks': 0,
        'projects': 0,
        'users': 0,
        'departments': 0,
      };

      // إضافة نشاط خطأ
      _recentActivities.value = [{
        'id': 1,
        'type': 'error',
        'title': 'خطأ في تحميل البيانات',
        'user': 'النظام',
        'timestamp': DateTime.now(),
      }];
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث البيانات
  Future<void> refreshData() async {
    await loadDashboardData();
  }

  /// الحصول على نسبة إكمال المهام
  double get taskCompletionRate {
    final total = _dashboardData['totalTasks'] ?? 0;
    final completed = _dashboardData['completedTasks'] ?? 0;
    return total > 0 ? (completed / total) * 100 : 0.0;
  }

  /// الحصول على نسبة المشاريع النشطة
  double get activeProjectsRate {
    final total = _dashboardData['totalProjects'] ?? 0;
    final active = _dashboardData['activeProjects'] ?? 0;
    return total > 0 ? (active / total) * 100 : 0.0;
  }

  /// الحصول على نسبة المستخدمين النشطين
  double get activeUsersRate {
    final total = _dashboardData['totalUsers'] ?? 0;
    final active = _dashboardData['activeUsers'] ?? 0;
    return total > 0 ? (active / total) * 100 : 0.0;
  }

  /// إضافة نشاط جديد
  void addActivity(Map<String, dynamic> activity) {
    _recentActivities.insert(0, activity);
    // الاحتفاظ بآخر 10 أنشطة فقط
    if (_recentActivities.length > 10) {
      _recentActivities.removeRange(10, _recentActivities.length);
    }
  }

  /// تحديث إحصائية معينة
  void updateStatistic(String key, int value) {
    _statistics[key] = value;
    _dashboardData[key] = value;
  }

  /// مسح الأخطاء
  void clearError() {
    _error.value = '';
  }

  /// الحصول على لون حالة المهام
  Color getTaskStatusColor() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final total = _dashboardData['totalTasks'] ?? 0;
    
    if (total == 0) return Colors.grey;
    
    final overdueRate = (overdue / total) * 100;
    if (overdueRate > 20) return Colors.red;
    if (overdueRate > 10) return Colors.orange;
    return Colors.green;
  }

  /// الحصول على رسالة حالة المهام
  String getTaskStatusMessage() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final pending = _dashboardData['pendingTasks'] ?? 0;
    final completed = _dashboardData['completedTasks'] ?? 0;
    
    if (overdue > 0) {
      return 'لديك $overdue مهمة متأخرة تحتاج إلى اهتمام';
    } else if (pending > 0) {
      return 'لديك $pending مهمة معلقة';
    } else if (completed > 0) {
      return 'ممتاز! تم إكمال جميع المهام';
    } else {
      return 'لا توجد مهام حالياً';
    }
  }

  /// الحصول على أيقونة حالة المهام
  IconData getTaskStatusIcon() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final pending = _dashboardData['pendingTasks'] ?? 0;
    
    if (overdue > 0) {
      return Icons.warning;
    } else if (pending > 0) {
      return Icons.pending_actions;
    } else {
      return Icons.check_circle;
    }
  }

  /// تصفية الأنشطة حسب النوع
  List<Map<String, dynamic>> getActivitiesByType(String type) {
    return _recentActivities.where((activity) => activity['type'] == type).toList();
  }

  /// الحصول على أنواع الأنشطة المختلفة
  List<String> get activityTypes {
    return _recentActivities
        .map((activity) => activity['type'] as String)
        .toSet()
        .toList();
  }

  /// تحديث بيانات محددة
  void updateDashboardData(String key, dynamic value) {
    _dashboardData[key] = value;
  }

  /// إعادة تعيين البيانات
  void resetData() {
    _dashboardData.clear();
    _recentActivities.clear();
    _statistics.clear();
    _error.value = '';
  }

  /// الحصول على لوحات المعلومات للمستخدم
  Future<List<Dashboard>> getDashboardsByUserId(int userId) async {
    try {
      // استخدام getMyDashboards بدلاً من getUserDashboards
      final dashboards = await _apiService.getMyDashboards();
      _dashboards.assignAll(dashboards);
      return dashboards;
    } catch (e) {
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
      return [];
    }
  }

  /// حفظ لوحة معلومات
  Future<Dashboard> saveDashboard(Dashboard dashboard) async {
    try {
      Dashboard savedDashboard;
      if (dashboard.id == 0) {
        // إنشاء جديد - استخدام Dashboard مباشرة
        savedDashboard = await _apiService.createDashboard(dashboard);
      } else {
        // تحديث موجود
        savedDashboard = await _apiService.updateDashboard(dashboard);
      }

      // تحديث القائمة المحلية
      final index = _dashboards.indexWhere((d) => d.id == savedDashboard.id);
      if (index != -1) {
        _dashboards[index] = savedDashboard;
      } else {
        _dashboards.add(savedDashboard);
      }

      return savedDashboard;
    } catch (e) {
      debugPrint('خطأ في حفظ لوحة المعلومات: $e');
      rethrow;
    }
  }

  /// توليد الأنشطة الحديثة بناءً على الإحصائيات
  List<Map<String, dynamic>> _generateRecentActivities(Map<String, dynamic> stats) {
    final activities = <Map<String, dynamic>>[];
    final now = DateTime.now();

    // إضافة أنشطة بناءً على الإحصائيات الحقيقية
    if (stats['completedTasks'] != null && stats['completedTasks'] > 0) {
      activities.add({
        'id': 1,
        'type': 'task_completed',
        'title': 'تم إكمال ${stats['completedTasks']} مهمة',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(minutes: 30)),
      });
    }

    if (stats['totalUsers'] != null && stats['totalUsers'] > 0) {
      activities.add({
        'id': 2,
        'type': 'users_active',
        'title': '${stats['activeUsers']} مستخدم نشط من أصل ${stats['totalUsers']}',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(hours: 1)),
      });
    }

    if (stats['totalDepartments'] != null && stats['totalDepartments'] > 0) {
      activities.add({
        'id': 3,
        'type': 'departments_summary',
        'title': 'يوجد ${stats['totalDepartments']} قسم في النظام',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(hours: 2)),
      });
    }

    if (stats['pendingTasks'] != null && stats['pendingTasks'] > 0) {
      activities.add({
        'id': 4,
        'type': 'tasks_pending',
        'title': '${stats['pendingTasks']} مهمة في انتظار التنفيذ',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(hours: 3)),
      });
    }

    if (stats['overdueTasks'] != null && stats['overdueTasks'] > 0) {
      activities.add({
        'id': 5,
        'type': 'tasks_overdue',
        'title': 'تحذير: ${stats['overdueTasks']} مهمة متأخرة',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(hours: 4)),
      });
    }

    // إذا لم تكن هناك أنشطة، أضف نشاط افتراضي
    if (activities.isEmpty) {
      activities.add({
        'id': 1,
        'type': 'system_ready',
        'title': 'النظام جاهز للاستخدام',
        'user': 'النظام',
        'timestamp': now.subtract(const Duration(minutes: 5)),
      });
    }

    return activities;
  }
}
