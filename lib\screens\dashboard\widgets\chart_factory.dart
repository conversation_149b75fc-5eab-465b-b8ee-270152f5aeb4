import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';
import 'base_chart_widget.dart';
import 'charts/pie_chart_widget.dart';
import 'charts/bar_chart_widget.dart';
import 'charts/line_chart_widget.dart';
import 'charts/gauge_chart_widget.dart';
import 'charts/kpi_widget.dart';

/// مصنع المخططات - Factory Pattern لإنشاء المخططات
class ChartFactory {
  /// إنشاء مخطط بناءً على النوع
  static BaseChartWidget createChart({
    required DashboardItem item,
    FilterOptions? filters,
    bool showHeader = true,
    bool showFilters = false,
    VoidCallback? onRefresh,
  }) {
    switch (item.chartType) {
      case ChartType.pie:
        return PieChartWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
        
      case ChartType.bar:
        return BarChartWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
        
      case ChartType.line:
        return LineChartWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
        
      case ChartType.gauge:
        return GaugeChartWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
        
      case ChartType.kpi:
        return KpiWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
        
      default:
        // إرجاع مخطط شريطي كافتراضي
        return BarChartWidget(
          item: item,
          filters: filters,
          showHeader: showHeader,
          showFilters: showFilters,
          onRefresh: onRefresh,
        );
    }
  }

  /// الحصول على قائمة أنواع المخططات المتاحة
  static List<ChartType> getAvailableChartTypes() {
    return ChartType.values;
  }

  /// التحقق من دعم نوع المخطط
  static bool isChartTypeSupported(ChartType chartType) {
    return ChartType.values.contains(chartType);
  }

  /// الحصول على وصف نوع المخطط
  static String getChartTypeDescription(ChartType chartType) {
    switch (chartType) {
      case ChartType.pie:
        return 'مخطط دائري لعرض النسب والتوزيعات';
      case ChartType.bar:
        return 'مخطط شريطي لمقارنة القيم';
      case ChartType.line:
        return 'مخطط خطي لعرض الاتجاهات عبر الزمن';
      case ChartType.gauge:
        return 'مقياس لعرض النسب المئوية والمؤشرات';
      case ChartType.kpi:
        return 'مؤشرات الأداء الرئيسية';
      default:
        return 'نوع مخطط غير معروف';
    }
  }

  /// إنشاء عنصر لوحة معلومات افتراضي
  static DashboardItem createDefaultItem({
    required String id,
    required String title,
    required ChartType chartType,
    Map<String, dynamic>? config,
  }) {
    final now = DateTime.now();
    
    return DashboardItem(
      id: id,
      title: title,
      chartType: chartType,
      config: config ?? {},
      createdAt: now,
      updatedAt: now,
    );
  }

  /// إنشاء عناصر افتراضية للوحة المعلومات
  static List<DashboardItem> createDefaultDashboardItems() {
    return [
      createDefaultItem(
        id: 'task_status',
        title: 'توزيع المهام حسب الحالة',
        chartType: ChartType.pie,
      ),
      createDefaultItem(
        id: 'monthly_tasks',
        title: 'المهام الشهرية',
        chartType: ChartType.bar,
      ),
      createDefaultItem(
        id: 'task_completion',
        title: 'معدل إكمال المهام',
        chartType: ChartType.gauge,
      ),
      createDefaultItem(
        id: 'user_performance',
        title: 'أداء المستخدمين',
        chartType: ChartType.bar,
      ),
      createDefaultItem(
        id: 'kpi_overview',
        title: 'نظرة عامة على المؤشرات',
        chartType: ChartType.kpi,
      ),
    ];
  }
}
