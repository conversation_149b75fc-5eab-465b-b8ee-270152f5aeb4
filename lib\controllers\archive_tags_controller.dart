import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/archive_models.dart';
import '../services/api/archive_tags_api_service.dart';

/// متحكم علامات الأرشيف
class ArchiveTagsController extends GetxController {
  final ArchiveTagsApiService _apiService = ArchiveTagsApiService();

  // قوائم العلامات
  final RxList<ArchiveTag> _allTags = <ArchiveTag>[].obs;
  final RxList<ArchiveTag> _filteredTags = <ArchiveTag>[].obs;

  // العلامة الحالية
  final Rx<ArchiveTag?> _currentTag = Rx<ArchiveTag?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<ArchiveTag> get allTags => _allTags;
  List<ArchiveTag> get filteredTags => _filteredTags;
  ArchiveTag? get currentTag => _currentTag.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTags();
  }

  /// تحميل جميع علامات الأرشيف
  Future<void> loadAllTags() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tags = await _apiService.getAllTags();
      _allTags.assignAll(tags);
      _applyFilters();
      debugPrint('تم تحميل ${tags.length} علامة أرشيف');
    } catch (e) {
      _error.value = 'خطأ في تحميل علامات الأرشيف: $e';
      debugPrint('خطأ في تحميل علامات الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على علامة أرشيف بالمعرف
  Future<void> getTagById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tag = await _apiService.getTagById(id);
      _currentTag.value = tag;
      debugPrint('تم تحميل علامة الأرشيف: ${tag?.name ?? 'غير محدد'}');
    } catch (e) {
      _error.value = 'خطأ في تحميل علامة الأرشيف: $e';
      debugPrint('خطأ في تحميل علامة الأرشيف: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء علامة أرشيف جديدة
  Future<bool> createTag(ArchiveTag tag) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newTag = await _apiService.createTag(tag);
      if (newTag != null) {
        _allTags.add(newTag);
        _applyFilters();
        debugPrint('تم إنشاء علامة أرشيف جديدة: ${newTag.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء علامة الأرشيف: $e';
      debugPrint('خطأ في إنشاء علامة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث علامة أرشيف
  Future<bool> updateTag(int id, ArchiveTag tag) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تحديث العلامة مع التأكد من أن ID صحيح
      final updatedTag = await _apiService.updateTag(tag);
      if (updatedTag != null) {
        final index = _allTags.indexWhere((t) => t.id == id);
        if (index != -1) {
          _allTags[index] = updatedTag;
          _applyFilters();
        }
        debugPrint('تم تحديث علامة الأرشيف: ${updatedTag.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث علامة الأرشيف: $e';
      debugPrint('خطأ في تحديث علامة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف علامة أرشيف
  Future<bool> deleteTag(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteTag(id);
      _allTags.removeWhere((t) => t.id == id);
      _applyFilters();
      debugPrint('تم حذف علامة الأرشيف');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف علامة الأرشيف: $e';
      debugPrint('خطأ في حذف علامة الأرشيف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allTags.where((tag) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!tag.name.toLowerCase().contains(query) &&
            !(tag.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !tag.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredTags.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllTags();
  }
}
