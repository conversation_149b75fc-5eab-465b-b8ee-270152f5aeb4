import 'package:flutter/foundation.dart';
import '../../models/archive_models.dart';
import 'api_service.dart';

/// خدمة API لفئات الأرشيف - متطابقة مع ASP.NET Core API
class ArchiveCategoriesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع فئات الأرشيف
  Future<List<ArchiveCategory>> getAllCategories() async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على فئات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على فئة أرشيف بواسطة المعرف
  Future<ArchiveCategory?> getCategoryById(int id) async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/$id');
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على فئة الأرشيف: $e');
      return null;
    }
  }

  /// إنشاء فئة أرشيف جديدة
  Future<ArchiveCategory?> createCategory(ArchiveCategory category) async {
    try {
      final response = await _apiService.post(
        '/api/ArchiveCategories',
        category.toJson(),
      );
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء فئة الأرشيف: $e');
      rethrow;
    }
  }

  /// تحديث فئة أرشيف
  Future<bool> updateCategory(ArchiveCategory category) async {
    try {
      final response = await _apiService.put(
        '/api/ArchiveCategories/${category.id}',
        category.toJson(),
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث فئة الأرشيف: $e');
      return false;
    }
  }

  /// حذف فئة أرشيف
  Future<bool> deleteCategory(int id) async {
    try {
      final response = await _apiService.delete('/api/ArchiveCategories/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف فئة الأرشيف: $e');
      return false;
    }
  }

  /// الحصول على الفئات الجذر (بدون فئة أب)
  Future<List<ArchiveCategory>> getRootCategories() async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/root');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الفئات الجذر: $e');
      rethrow;
    }
  }

  /// الحصول على الفئات الفرعية لفئة محددة
  Future<List<ArchiveCategory>> getChildCategories(int parentId) async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/parent/$parentId/children');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الفئات الفرعية: $e');
      rethrow;
    }
  }

  /// الحصول على شجرة الفئات الهرمية
  Future<List<Map<String, dynamic>>> getCategoriesTree() async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/tree');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على شجرة الفئات: $e');
      return [];
    }
  }

  /// البحث في فئات الأرشيف
  Future<List<ArchiveCategory>> searchCategories(String searchTerm) async {
    try {
      final response = await _apiService.get(
        '/api/ArchiveCategories/search',
        queryParams: {'searchTerm': searchTerm},
      );
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن فئات الأرشيف: $e');
      rethrow;
    }
  }

  /// عدد الوثائق في فئة محددة
  Future<int> getDocumentsCount(int categoryId) async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/$categoryId/documents/count');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as int;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الوثائق: $e');
      return 0;
    }
  }

  /// تفعيل/إلغاء تفعيل فئة أرشيف
  Future<bool> toggleCategoryActive(int categoryId, bool active) async {
    try {
      final uri = '/api/ArchiveCategories/$categoryId/toggle-active?active=$active';
      final response = await _apiService.patch(uri, null);
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة نشاط الفئة: $e');
      return false;
    }
  }

  /// نقل فئة إلى فئة أب أخرى
  Future<bool> moveCategory(int categoryId, int? newParentId) async {
    try {
      final uri = '/api/ArchiveCategories/$categoryId/move?newParentId=$newParentId';
      final response = await _apiService.patch(uri, null);
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل الفئة: $e');
      return false;
    }
  }

  /// الحصول على مسار الفئة (من الجذر إلى الفئة)
  Future<List<Map<String, dynamic>>> getCategoryPath(int categoryId) async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories/$categoryId/path');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الفئة: $e');
      return [];
    }
  }

}
