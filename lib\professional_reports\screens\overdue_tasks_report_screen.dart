import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../reports/overdue_tasks_report.dart';
import '../../services/api/task_api_service.dart';

class OverdueTasksReportScreen extends StatelessWidget {
  const OverdueTasksReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المهام المتأخرة'),
        actions: [_buildRefreshButton()],
      ),
      body: FutureBuilder<OverdueTasksReport>(
        future: _getReport(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (snapshot.hasData) {
            final report = snapshot.data!;
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('عدد المهام المتأخرة: ${report.overdueTasks.length}', style: const TextStyle(fontSize: 16)),
                  Text('متوسط أيام التأخير: ${report.averageDelayDays.toStringAsFixed(2)} يوم', style: const TextStyle(fontSize: 16)),
                  const SizedBox(height: 10),
                  if (report.overdueTasks.isNotEmpty)
                    const Text('المهام المتأخرة:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                  Expanded(
                    child: ListView.builder(
                      itemCount: report.overdueTasks.length,
                      itemBuilder: (context, index) {
                        final task = report.overdueTasks[index];
                        return ListTile(
                          title: Text(task.title),
                          subtitle: Text('Due Date: ${task.dueDateDateTime}'),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          } else {
            return const Center(child: Text('No data'));
          }
        },
      ),
    );
  }

  Future<OverdueTasksReport> _getReport() async {
    final tasks = await TaskApiService().getAllTasks();
    return OverdueTasksReport.fromTasks(tasks);
  }

  Widget _buildRefreshButton() {
    return IconButton(
      icon: const Icon(Icons.refresh),
      onPressed: () {
        // يمكنك تحديث الشاشة عن طريق استدعاء نفس الشاشة مرة أخرى
        Get.offAll(() => const OverdueTasksReportScreen());
      },
    );
  }
}