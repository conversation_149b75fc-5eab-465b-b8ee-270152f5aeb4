import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../services/storage_service.dart';
import '../services/api/api_service.dart';

/// مساعد المستخدم - يوفر وظائف مساعدة للمستخدم الحالي
class UserHelper {
  static final StorageService _storageService = Get.find<StorageService>();

  /// الحصول على معرف المستخدم الحالي
  static int? getCurrentUserId() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentUser.value?.id;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المستخدم الحالي
  static dynamic getCurrentUser() {
    try {
      final authController = Get.find<AuthController>();
      return authController.currentUser.value;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على رمز المصادقة
  static String? getToken() {
    try {
      // محاولة الحصول على الرمز من ApiService
      final apiService = Get.find<ApiService>();
      return apiService.accessToken;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على رمز المصادقة بشكل غير متزامن
  static Future<String?> getTokenAsync() async {
    try {
      return await _storageService.getString('auth_token');
    } catch (e) {
      return null;
    }
  }

  /// التحقق من تسجيل الدخول
  static bool isLoggedIn() {
    try {
      final authController = Get.find<AuthController>();
      return authController.isLoggedIn;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من دور المستخدم
  static bool hasRole(String role) {
    try {
      final user = getCurrentUser();
      if (user == null) return false;
      
      return user.role?.toString().toLowerCase() == role.toLowerCase();
    } catch (e) {
      return false;
    }
  }

  /// التحقق من الصلاحيات الإدارية
  static bool isAdmin() {
    try {
      final user = getCurrentUser();
      if (user == null) return false;
      
      final role = user.role?.toString().toLowerCase();
      return role == 'admin' || role == 'superadmin' || role == 'super_admin';
    } catch (e) {
      return false;
    }
  }

  /// التحقق من صلاحيات المدير العام
  static bool isSuperAdmin() {
    try {
      final user = getCurrentUser();
      if (user == null) return false;
      
      final role = user.role?.toString().toLowerCase();
      return role == 'superadmin' || role == 'super_admin';
    } catch (e) {
      return false;
    }
  }

  /// الحصول على اسم المستخدم
  static String getUserName() {
    try {
      final user = getCurrentUser();
      return user?.name ?? 'مستخدم غير معروف';
    } catch (e) {
      return 'مستخدم غير معروف';
    }
  }

  /// الحصول على بريد المستخدم الإلكتروني
  static String getUserEmail() {
    try {
      final user = getCurrentUser();
      return user?.email ?? '';
    } catch (e) {
      return '';
    }
  }

  /// الحصول على صورة المستخدم
  static String? getUserProfileImage() {
    try {
      final user = getCurrentUser();
      return user?.profileImage;
    } catch (e) {
      return null;
    }
  }

  /// التحقق من نشاط المستخدم
  static bool isUserActive() {
    try {
      final user = getCurrentUser();
      return user?.isActive ?? false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على معرف القسم
  static int? getUserDepartmentId() {
    try {
      final user = getCurrentUser();
      return user?.departmentId;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على اسم القسم
  static String getUserDepartmentName() {
    try {
      final user = getCurrentUser();
      return user?.department?.name ?? 'غير محدد';
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// تحديث آخر نشاط للمستخدم
  static Future<void> updateLastActivity() async {
    try {
      // يمكن إضافة منطق تحديث آخر نشاط هنا
      // مثل إرسال طلب للخادم أو تحديث الوقت محلياً
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      await _storageService.setInt('last_activity', timestamp);
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  /// تسجيل الخروج
  static Future<void> logout() async {
    try {
      final authController = Get.find<AuthController>();
      await authController.logout();
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  /// التحقق من صلاحية محددة
  static bool hasPermission(String permission) {
    try {
      final authController = Get.find<AuthController>();
      // استخدام الصلاحيات المتاحة في AuthController
      switch (permission.toLowerCase()) {
        case 'admin':
        case 'system.admin':
          return authController.isAnyAdmin;
        case 'manager':
        case 'department.manager':
          return authController.hasManagerRights;
        case 'supervisor':
          return authController.isSupervisor || authController.hasManagerRights;
        case 'all_pages':
          return authController.canSeeAllPages;
        default:
          return authController.canSeeAllPages; // افتراضي للمديرين
      }
    } catch (e) {
      return false;
    }
  }

  /// التحقق من إمكانية رؤية جميع الصفحات
  static bool canSeeAllPages() {
    try {
      final authController = Get.find<AuthController>();
      return authController.canSeeAllPages;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على رؤوس HTTP للطلبات
  static Map<String, String> getAuthHeaders() {
    final token = getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }

  /// التحقق من صحة الرمز المميز
  static bool isTokenValid() {
    try {
      final authController = Get.find<AuthController>();
      return authController.isLoggedIn;
    } catch (e) {
      return false;
    }
  }

  /// تحديث الرمز المميز
  static Future<bool> refreshToken() async {
    try {
      final authController = Get.find<AuthController>();
      return await authController.validateToken();
    } catch (e) {
      return false;
    }
  }
}
