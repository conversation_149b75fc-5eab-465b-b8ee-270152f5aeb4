import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/reporting/report_result_model.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';

import '../../../constants/app_styles.dart';
import '../../../services/unified_permission_service.dart';

/// لوحة فلاتر التقرير
///
/// تعرض وتتيح تعديل فلاتر التقرير
class ReportFilterPanel extends StatefulWidget {
  /// فلاتر التقرير
  final List<ReportFilter> filters;

  /// فترة التقرير
  final ReportPeriod period;

  /// تاريخ بداية الفترة المخصصة
  final DateTime? customStartDate;

  /// تاريخ نهاية الفترة المخصصة
  final DateTime? customEndDate;

  /// حدث تغيير الفلاتر
  final Function(List<ReportFilter> filters) onFiltersChanged;

  /// حدث تغيير الفترة
  final Function(ReportPeriod period, DateTime? startDate, DateTime? endDate) onPeriodChanged;

  /// هل يمكن تعديل الفلاتر
  final bool isEditable;

  const ReportFilterPanel({
    super.key,
    required this.filters,
    required this.period,
    this.customStartDate,
    this.customEndDate,
    required this.onFiltersChanged,
    required this.onPeriodChanged,
    this.isEditable = true,
  });

  @override
  State<ReportFilterPanel> createState() => _ReportFilterPanelState();
}

class _ReportFilterPanelState extends State<ReportFilterPanel> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  late List<ReportFilter> _filters;
  late ReportPeriod _period;
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _filters = List.from(widget.filters);
    _period = widget.period;
    _customStartDate = widget.customStartDate;
    _customEndDate = widget.customEndDate;
  }

  @override
  void didUpdateWidget(ReportFilterPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filters != widget.filters) {
      _filters = List.from(widget.filters);
    }
    if (oldWidget.period != widget.period) {
      _period = widget.period;
    }
    if (oldWidget.customStartDate != widget.customStartDate) {
      _customStartDate = widget.customStartDate;
    }
    if (oldWidget.customEndDate != widget.customEndDate) {
      _customEndDate = widget.customEndDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس اللوحة
            Row(
              children: [
                const Icon(Icons.filter_list),
                const SizedBox(width: 8),
                 Text(
                  'الفلاتر',
                  style: AppStyles.titleMedium,
                ),
                const Spacer(),
                // زر التوسيع
                IconButton(
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  tooltip: _isExpanded ? 'طي' : 'توسيع',
                ),
              ],
            ),
            // فترة التقرير
            const SizedBox(height: 16),
            Text(
              'الفترة الزمنية',
              style: AppStyles.titleSmall,
            ),
            const SizedBox(height: 8),
            _buildPeriodSelector(),
            // الفلاتر
            if (_isExpanded) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              _buildFiltersList(),
              // زر إضافة فلتر
              if (widget.isEditable)
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة فلتر'),
                    onPressed: _addFilter,
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء محدد الفترة
  Widget _buildPeriodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // قائمة الفترات
        DropdownButtonFormField<ReportPeriod>(
          value: _period,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: ReportPeriod.values.map((period) {
            return DropdownMenuItem<ReportPeriod>(
              value: period,
              child: Text(_getPeriodName(period)),
            );
          }).toList(),
          onChanged: widget.isEditable
              ? (value) {
                  if (value != null) {
                    setState(() {
                      _period = value;
                      if (value != ReportPeriod.custom) {
                        _customStartDate = null;
                        _customEndDate = null;
                      }
                    });
                    widget.onPeriodChanged(_period, _customStartDate, _customEndDate);
                  }
                }
              : null,
        ),
        // حقول الفترة المخصصة
        if (_period == ReportPeriod.custom) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              // تاريخ البداية
              Expanded(
                child: InkWell(
                  onTap: widget.isEditable
                      ? () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _customStartDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );
                          if (date != null) {
                            setState(() {
                              _customStartDate = date;
                            });
                            widget.onPeriodChanged(_period, _customStartDate, _customEndDate);
                          }
                        }
                      : null,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'تاريخ البداية',
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(
                      _customStartDate != null
                          ? DateFormat('yyyy-MM-dd').format(_customStartDate!)
                          : 'اختر تاريخ البداية',
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // تاريخ النهاية
              Expanded(
                child: InkWell(
                  onTap: widget.isEditable
                      ? () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _customEndDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );
                          if (date != null) {
                            setState(() {
                              _customEndDate = date;
                            });
                            widget.onPeriodChanged(_period, _customStartDate, _customEndDate);
                          }
                        }
                      : null,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'تاريخ النهاية',
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(
                      _customEndDate != null
                          ? DateFormat('yyyy-MM-dd').format(_customEndDate!)
                          : 'اختر تاريخ النهاية',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء قائمة الفلاتر
  Widget _buildFiltersList() {
    if (_filters.isEmpty) {
      return  Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'لا توجد فلاتر',
            style: AppStyles.bodyMedium,
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _filters.length,
      itemBuilder: (context, index) {
        final filter = _filters[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                // حالة الفلتر
                _permissionService.canViewReports()
                    ? Checkbox(
                        value: filter.isActive,
                        onChanged: widget.isEditable
                            ? (value) {
                                if (value != null) {
                                  setState(() {
                                    _filters[index] = filter.copyWith(isActive: value);
                                  });
                                  widget.onFiltersChanged(_filters);
                                }
                              }
                            : null,
                      )
                    : Checkbox(
                        value: filter.isActive,
                        onChanged: null, // معطل
                      ),
                // اسم الفلتر
                Expanded(
                  child: Text(
                    filter.label,
                    style: AppStyles.bodyMedium,
                  ),
                ),
                // قيمة الفلتر
                Expanded(
                  child: Text(
                    _getFilterValueText(filter),
                    style: AppStyles.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // زر التعديل
                if (widget.isEditable)
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _editFilter(index),
                    tooltip: 'تعديل',
                  ),
                // زر الحذف
                if (widget.isEditable)
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () {
                      setState(() {
                        _filters.removeAt(index);
                      });
                      widget.onFiltersChanged(_filters);
                    },
                    tooltip: 'حذف',
                    color: Colors.red,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// إضافة فلتر جديد
  void _addFilter() {
    // سيتم تنفيذ هذا لاحقًا
  }

  /// تعديل فلتر
  void _editFilter(int index) {
    // سيتم تنفيذ هذا لاحقًا
  }

  /// الحصول على اسم الفترة
  String _getPeriodName(ReportPeriod period) {
    switch (period) {
      case ReportPeriod.today:
        return 'اليوم';
      case ReportPeriod.yesterday:
        return 'أمس';
      case ReportPeriod.thisWeek:
        return 'هذا الأسبوع';
      case ReportPeriod.lastWeek:
        return 'الأسبوع الماضي';
      case ReportPeriod.thisMonth:
        return 'هذا الشهر';
      case ReportPeriod.lastMonth:
        return 'الشهر الماضي';
      case ReportPeriod.thisQuarter:
        return 'هذا الربع';
      case ReportPeriod.lastQuarter:
        return 'الربع الماضي';
      case ReportPeriod.thisYear:
        return 'هذه السنة';
      case ReportPeriod.lastYear:
        return 'السنة الماضية';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على نص قيمة الفلتر
  String _getFilterValueText(ReportFilter filter) {
    switch (filter.operator) {
      case FilterOperator.equals:
        return '= ${filter.value}';
      case FilterOperator.notEquals:
        return '!= ${filter.value}';
      case FilterOperator.contains:
        return 'يحتوي على ${filter.value}';
      case FilterOperator.notContains:
        return 'لا يحتوي على ${filter.value}';
      case FilterOperator.startsWith:
        return 'يبدأ بـ ${filter.value}';
      case FilterOperator.endsWith:
        return 'ينتهي بـ ${filter.value}';
      case FilterOperator.greaterThan:
        return '> ${filter.value}';
      case FilterOperator.lessThan:
        return '< ${filter.value}';
      case FilterOperator.greaterOrEqual:
        return '>= ${filter.value}';
      case FilterOperator.lessOrEqual:
        return '<= ${filter.value}';
      case FilterOperator.between:
        return 'بين ${filter.value} و ${filter.value2}';
      case FilterOperator.isIn:
        return 'في [${filter.values?.join(', ')}]';
      case FilterOperator.notIn:
        return 'ليس في [${filter.values?.join(', ')}]';
      case FilterOperator.isNull:
        return 'فارغ';
      case FilterOperator.isNotNull:
        return 'غير فارغ';
      case FilterOperator.before:
        return 'قبل ${filter.value}';
      case FilterOperator.after:
        return 'بعد ${filter.value}';
      case FilterOperator.onOrBefore:
        return 'في أو قبل ${filter.value}';
      case FilterOperator.onOrAfter:
        return 'في أو بعد ${filter.value}';
      case FilterOperator.inRange:
        return 'في النطاق ${filter.value} - ${filter.value2}';
      default:
        return filter.value?.toString() ?? '';
    }
  }
}
