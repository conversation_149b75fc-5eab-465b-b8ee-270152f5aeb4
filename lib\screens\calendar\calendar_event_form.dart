import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/calendar_models.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controllers/task_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../constants/app_colors.dart';
import '../../services/unified_permission_service.dart';

/// نموذج إضافة وتعديل أحداث التقويم
class CalendarEventForm extends StatefulWidget {
  /// الحدث المراد تعديله (اختياري)
  final CalendarEvent? event;

  /// التاريخ المبدئي للحدث الجديد (اختياري)
  final DateTime? initialDate;

  /// دالة يتم استدعاؤها عند إنشاء حدث جديد
  final Function(CalendarEvent)? onEventCreated;

  /// دالة يتم استدعاؤها عند تحديث حدث
  final Function(CalendarEvent)? onEventUpdated;

  const CalendarEventForm({
    super.key,
    this.event,
    this.initialDate,
    this.onEventCreated,
    this.onEventUpdated,
  });

  @override
  State<CalendarEventForm> createState() => _CalendarEventFormState();
}

class _CalendarEventFormState extends State<CalendarEventForm> {
  final app_calendar.CalendarController _calendarController = Get.find<app_calendar.CalendarController>();
  final TaskController _taskController = Get.find<TaskController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  DateTime _startDate = DateTime.now();
  TimeOfDay _startTime = TimeOfDay.now();
  DateTime _endDate = DateTime.now().add(const Duration(hours: 1));
  TimeOfDay _endTime = TimeOfDay.fromDateTime(DateTime.now().add(const Duration(hours: 1)));

  CalendarEventType _eventType = CalendarEventType.task;
  String? _selectedTaskId;
  Color _selectedColor = AppColors.primary;

  // خصائص التكرار والتنبيهات
  EventRecurrencePattern _recurrencePattern = EventRecurrencePattern.none;
  int _recurrenceCount = 0;
  DateTime? _recurrenceEndDate;
  EventReminderTime _reminderTime = EventReminderTime.none;
  bool _isReminderEnabled = false;

  bool _isLoading = false;
  String _errorMessage = '';

  List<Task> _availableTasks = [];
  List<Task> _filteredTasks = [];

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadAvailableTasks();

    // إضافة مستمع لحقل البحث
    _searchController.addListener(_filterTasks);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // تصفية المهام حسب نص البحث
  void _filterTasks() {
    final String searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredTasks = _availableTasks;
      } else {
        _filteredTasks = _availableTasks.where((task) =>
          task.title.toLowerCase().contains(searchText) ||
          (task.description?.toLowerCase().contains(searchText) ?? false)
        ).toList();
      }
    });
  }

  // تهيئة النموذج
  void _initializeForm() {
    if (widget.event != null) {
      // تعديل حدث موجود
      _titleController.text = widget.event!.title;
      _descriptionController.text = widget.event!.description ?? '';

      // تحويل timestamp إلى DateTime
      _startDate = DateTime.fromMillisecondsSinceEpoch(widget.event!.startTime * 1000);
      _startTime = TimeOfDay.fromDateTime(_startDate);

      _endDate = DateTime.fromMillisecondsSinceEpoch(widget.event!.endTime * 1000);
      _endTime = TimeOfDay.fromDateTime(_endDate);

      _eventType = widget.event!.eventType;
      _selectedTaskId = widget.event!.taskId?.toString();
      _selectedColor = _parseColor(widget.event!.color) ?? _getEventTypeColor(_eventType);

      // تهيئة خصائص التكرار والتنبيهات
      _recurrencePattern = widget.event!.recurrencePattern;
      _recurrenceCount = widget.event!.recurrenceCount;
      _recurrenceEndDate = widget.event!.recurrenceEndDate;
      _reminderTime = widget.event!.reminderTime;
      _isReminderEnabled = widget.event!.isReminderEnabled;
    } else if (widget.initialDate != null) {
      // إنشاء حدث جديد بتاريخ محدد
      _startDate = widget.initialDate!;
      _startTime = TimeOfDay.fromDateTime(widget.initialDate!);

      _endDate = widget.initialDate!.add(const Duration(hours: 1));
      _endTime = TimeOfDay.fromDateTime(widget.initialDate!.add(const Duration(hours: 1)));
    }
  }

  // تحميل المهام المتاحة
  Future<void> _loadAvailableTasks() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تحميل المهام حسب صلاحيات المستخدم
      final authController = Get.find<AuthController>();
      if (authController.currentUser.value != null) {
        await _taskController.loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);
      }

      // الحصول على المهام من وحدة التحكم
      final List<Task> allTasks = _taskController.allTasks.toList();

      // الحصول على المستخدم الحالي
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        setState(() {
          _errorMessage = 'لم يتم تسجيل الدخول';
          _isLoading = false;
        });
        return;
      }

      // تصفية المهام النشطة أولاً
      List<Task> filteredTasks = [];
      for (var task in allTasks) {
        if (task.status != TaskStatus.completed.stringValue &&
            task.status != TaskStatus.cancelled.stringValue &&
            !task.isDeleted) {
          filteredTasks.add(task);
        }
      }

      final permissionService = Get.find<UnifiedPermissionService>();

      if (permissionService.canViewAllTasks()) {
        // المستخدم لديه صلاحية رؤية جميع المهام
        _availableTasks = filteredTasks;
      } else {
        // المستخدم يرى مهامه فقط أو مهام قسمه
        List<Task> userTasks = [];
        for (var task in filteredTasks) {
          if (task.assigneeId == currentUser.id ||
              task.creatorId == currentUser.id ||
              (permissionService.canAdminDepartments() &&
               task.departmentId == currentUser.departmentId)) {
            userTasks.add(task);
          }
        }
        _availableTasks = userTasks;
      }

      // ترتيب المهام حسب تاريخ الاستحقاق
      _availableTasks.sort((a, b) {
        if (a.dueDate == null && b.dueDate == null) return 0;
        if (a.dueDate == null) return 1;
        if (b.dueDate == null) return -1;
        return a.dueDate!.compareTo(b.dueDate!);
      });

      // تهيئة قائمة المهام المصفاة
      _filteredTasks = _availableTasks;

    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل المهام: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.event != null ? 'تعديل حدث' : 'إضافة حدث جديد'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildForm(),
    );
  }

  // بناء نموذج الإدخال
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الحدث
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الحدث *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال عنوان للحدث';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // وصف الحدث
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الحدث',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // نوع الحدث
            DropdownButtonFormField<CalendarEventType>(
              value: _eventType,
              decoration: const InputDecoration(
                labelText: 'نوع الحدث *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: CalendarEventType.values.map((type) {
                return DropdownMenuItem<CalendarEventType>(
                  value: type,
                  child: Text(_getEventTypeName(type)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _eventType = value!;
                  // تحديث اللون حسب نوع الحدث
                  _selectedColor = _getEventTypeColor(value);
                });
              },
            ),
            const SizedBox(height: 16),

            // المهمة المرتبطة (إذا كان نوع الحدث مهمة)
            if (_eventType == CalendarEventType.task) Column(
              children: [
                // حقل البحث عن المهام
                TextFormField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث عن مهمة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                    hintText: 'اكتب عنوان أو وصف المهمة للبحث',
                  ),
                ),
                const SizedBox(height: 16),

                // قائمة المهام المصفاة
                DropdownButtonFormField<String?>(
                  value: _selectedTaskId,
                  decoration: const InputDecoration(
                    labelText: 'المهمة المرتبطة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.task),
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('بدون مهمة'),
                    ),
                    ..._filteredTasks.map((task) {
                      return DropdownMenuItem<String?>(
                        value: task.id.toString(),
                        child: Text(task.title),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedTaskId = value;
                    });
                  },
                ),

                // عدد المهام المصفاة
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'عدد المهام المتاحة: ${_filteredTasks.length}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ),

                // تفاصيل المهمة المحددة
                if (_selectedTaskId != null)
                  _buildSelectedTaskDetails(),

                const SizedBox(height: 16),
              ],
            ),

            // تاريخ ووقت البداية
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ البداية *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        DateFormat('yyyy-MM-dd').format(_startDate),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartTime(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'وقت البداية *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.access_time),
                      ),
                      child: Text(
                        _startTime.format(context),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // تاريخ ووقت النهاية
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ النهاية *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        DateFormat('yyyy-MM-dd').format(_endDate),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndTime(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'وقت النهاية *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.access_time),
                      ),
                      child: Text(
                        _endTime.format(context),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // اختيار اللون
            const Text(
              'لون الحدث:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            _buildColorPicker(),
            const SizedBox(height: 16),

            // إعدادات التكرار
            if (_eventType == CalendarEventType.reminder || _eventType == CalendarEventType.meeting)
              _buildRecurrenceSettings(),

            // إعدادات التنبيهات
            _buildReminderSettings(),
            const SizedBox(height: 24),

            // رسالة الخطأ
            if (_errorMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.red.shade100,
                width: double.infinity,
                child: Text(
                  _errorMessage,
                  style: TextStyle(color: Colors.red.shade900),
                ),
              ),

            // زر الحفظ
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (widget.event != null
                    ? _permissionService.canEditCalendarEvents()
                    : _permissionService.canCreateCalendarEvents())
                    ? _saveEvent
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  widget.event != null ? 'تحديث الحدث' : 'إضافة الحدث',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء إعدادات التكرار
  Widget _buildRecurrenceSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 8),
        const Text(
          'إعدادات التكرار:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // نمط التكرار
        DropdownButtonFormField<EventRecurrencePattern>(
          value: _recurrencePattern,
          decoration: const InputDecoration(
            labelText: 'نمط التكرار',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.repeat),
          ),
          items: EventRecurrencePattern.values.map((pattern) {
            return DropdownMenuItem<EventRecurrencePattern>(
              value: pattern,
              child: Text(_getRecurrencePatternName(pattern)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _recurrencePattern = value!;

              // إذا تم اختيار "لا تكرار"، إعادة تعيين الإعدادات الأخرى
              if (value == EventRecurrencePattern.none) {
                _recurrenceCount = 0;
                _recurrenceEndDate = null;
              } else if (_recurrenceCount == 0) {
                // تعيين قيمة افتراضية إذا كانت صفر
                _recurrenceCount = 1;
              }
            });
          },
        ),
        const SizedBox(height: 16),

        // إعدادات التكرار الإضافية (تظهر فقط إذا تم اختيار نمط تكرار)
        if (_recurrencePattern != EventRecurrencePattern.none)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عدد مرات التكرار
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: _recurrenceCount.toString(),
                      decoration: const InputDecoration(
                        labelText: 'عدد مرات التكرار',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.repeat_one),
                        hintText: 'أدخل -1 للتكرار غير المحدود',
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        setState(() {
                          _recurrenceCount = int.tryParse(value) ?? 0;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  // تاريخ انتهاء التكرار
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectRecurrenceEndDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ انتهاء التكرار',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.event_busy),
                        ),
                        child: Text(
                          _recurrenceEndDate != null
                              ? DateFormat('yyyy-MM-dd').format(_recurrenceEndDate!)
                              : 'غير محدد',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'ملاحظة: إذا تم تحديد عدد مرات التكرار وتاريخ انتهاء، سيتم استخدام الشرط الذي يتحقق أولاً.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  // بناء إعدادات التنبيهات
  Widget _buildReminderSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 8),
        const Text(
          'إعدادات التنبيه:',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // تفعيل/تعطيل التنبيه
        SwitchListTile(
          title: const Text('تفعيل التنبيه'),
          subtitle: const Text('سيتم تنبيهك قبل موعد الحدث'),
          value: _isReminderEnabled,
          activeColor: AppColors.primary,
          contentPadding: EdgeInsets.zero,
          onChanged: (value) {
            setState(() {
              _isReminderEnabled = value;

              // إذا تم تفعيل التنبيه وكان الوقت "لا تنبيه"، تعيين قيمة افتراضية
              if (value && _reminderTime == EventReminderTime.none) {
                _reminderTime = EventReminderTime.fifteenMinutes;
              }
            });
          },
        ),

        // وقت التنبيه (يظهر فقط إذا كان التنبيه مفعلاً)
        if (_isReminderEnabled)
          DropdownButtonFormField<EventReminderTime>(
            value: _reminderTime,
            decoration: const InputDecoration(
              labelText: 'وقت التنبيه',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.alarm),
            ),
            items: EventReminderTime.values.where((time) => time != EventReminderTime.none).map((time) {
              return DropdownMenuItem<EventReminderTime>(
                value: time,
                child: Text(_getReminderTimeName(time)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _reminderTime = value!;
              });
            },
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  // اختيار تاريخ انتهاء التكرار
  Future<void> _selectRecurrenceEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _recurrenceEndDate ?? _endDate.add(const Duration(days: 30)),
      firstDate: _endDate,
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _recurrenceEndDate = picked;
      });
    }
  }

  // الحصول على اسم نمط التكرار
  String _getRecurrencePatternName(EventRecurrencePattern pattern) {
    switch (pattern) {
      case EventRecurrencePattern.none:
        return 'لا تكرار';
      case EventRecurrencePattern.daily:
        return 'يومي';
      case EventRecurrencePattern.weekly:
        return 'أسبوعي';
      case EventRecurrencePattern.monthly:
        return 'شهري';
      case EventRecurrencePattern.yearly:
        return 'سنوي';
      default:
        return 'لا تكرار';
    }
  }

  // الحصول على اسم وقت التنبيه
  String _getReminderTimeName(EventReminderTime time) {
    switch (time) {
      case EventReminderTime.none:
        return 'لا تنبيه';
      case EventReminderTime.fiveMinutes:
        return 'قبل 5 دقائق';
      case EventReminderTime.fifteenMinutes:
        return 'قبل 15 دقيقة';
      case EventReminderTime.thirtyMinutes:
        return 'قبل 30 دقيقة';
      case EventReminderTime.oneHour:
        return 'قبل ساعة';
      case EventReminderTime.oneDay:
        return 'قبل يوم';
      default:
        return 'لا تنبيه';
    }
  }

  // بناء منتقي الألوان
  Widget _buildColorPicker() {
    final List<Color> colors = [
      AppColors.primary,
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        return InkWell(
          onTap: () {
            setState(() {
              _selectedColor = color;
            });
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: _selectedColor == color ? Colors.white : Colors.transparent,
                width: 2,
              ),
              boxShadow: [
                if (_selectedColor == color)
                  BoxShadow(
                    color: Colors.black.withAlpha(76),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
              ],
            ),
            child: _selectedColor == color
                ? const Icon(Icons.check, color: Colors.white)
                : null,
          ),
        );
      }).toList(),
    );
  }

  // اختيار تاريخ البداية
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          _startTime.hour,
          _startTime.minute,
        );

        // إذا كان تاريخ البداية بعد تاريخ النهاية، نحدث تاريخ النهاية
        if (_startDate.isAfter(_endDate)) {
          _endDate = _startDate.add(const Duration(hours: 1));
          _endTime = TimeOfDay.fromDateTime(_endDate);
        }
      });
    }
  }

  // اختيار وقت البداية
  Future<void> _selectStartTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _startTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _startTime) {
      setState(() {
        _startTime = picked;
        _startDate = DateTime(
          _startDate.year,
          _startDate.month,
          _startDate.day,
          _startTime.hour,
          _startTime.minute,
        );

        // إذا كان وقت البداية بعد وقت النهاية في نفس اليوم، نحدث وقت النهاية
        final DateTime endDateTime = DateTime(
          _endDate.year,
          _endDate.month,
          _endDate.day,
          _endTime.hour,
          _endTime.minute,
        );

        if (_startDate.isAfter(endDateTime)) {
          _endDate = _startDate.add(const Duration(hours: 1));
          _endTime = TimeOfDay.fromDateTime(_endDate);
        }
      });
    }
  }

  // اختيار تاريخ النهاية
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          _endTime.hour,
          _endTime.minute,
        );
      });
    }
  }

  // اختيار وقت النهاية
  Future<void> _selectEndTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _endTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _endTime) {
      setState(() {
        _endTime = picked;
        _endDate = DateTime(
          _endDate.year,
          _endDate.month,
          _endDate.day,
          _endTime.hour,
          _endTime.minute,
        );

        // إذا كان وقت النهاية قبل وقت البداية في نفس اليوم، نحدث تاريخ النهاية
        if (_endDate.isBefore(_startDate)) {
          _endDate = _startDate.add(const Duration(days: 1));
        }
      });
    }
  }

  // حفظ الحدث
  Future<void> _saveEvent() async {
    if (_formKey.currentState!.validate()) {
      // التحقق من صحة التواريخ
      final DateTime startDateTime = DateTime(
        _startDate.year,
        _startDate.month,
        _startDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final DateTime endDateTime = DateTime(
        _endDate.year,
        _endDate.month,
        _endDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      if (endDateTime.isBefore(startDateTime)) {
        setState(() {
          _errorMessage = 'يجب أن يكون وقت النهاية بعد وقت البداية';
        });
        return;
      }

      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      try {
        if (widget.event != null) {
          // تحديث حدث موجود
          final updatedEvent = widget.event!.copyWith(
            title: _titleController.text,
            description: _descriptionController.text,
            startTime: startDateTime.millisecondsSinceEpoch ~/ 1000,
            endTime: endDateTime.millisecondsSinceEpoch ~/ 1000,
            color: _colorToHex(_selectedColor),
            taskId: _selectedTaskId != null ? int.tryParse(_selectedTaskId!) : null,
            eventType: _eventType,
            recurrencePattern: _recurrencePattern,
            recurrenceCount: _recurrenceCount,
            recurrenceEndDate: _recurrenceEndDate,
            reminderTime: _reminderTime,
            isReminderEnabled: _isReminderEnabled,
          );

          await _calendarController.updateEvent(updatedEvent);

          Get.back();
          Get.snackbar(
            'تم التحديث',
            'تم تحديث الحدث بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          if (widget.onEventUpdated != null) {
            widget.onEventUpdated!(updatedEvent);
          }
        } else {
          // إنشاء حدث جديد
          final newEvent = CalendarEvent(
            id: 0, // سيتم تعيينه من الخادم
            title: _titleController.text,
            description: _descriptionController.text,
            startTime: startDateTime.millisecondsSinceEpoch ~/ 1000,
            endTime: endDateTime.millisecondsSinceEpoch ~/ 1000,
            userId: 1, // TODO: الحصول على معرف المستخدم الحالي
            taskId: _selectedTaskId != null ? int.tryParse(_selectedTaskId!) : null,
            color: _colorToHex(_selectedColor),
            eventType: _eventType,
            recurrencePattern: _recurrencePattern,
            recurrenceCount: _recurrenceCount,
            recurrenceEndDate: _recurrenceEndDate,
            reminderTime: _reminderTime,
            isReminderEnabled: _isReminderEnabled,
            createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
          );

          await _calendarController.addEvent(newEvent);

          Get.back();
          Get.snackbar(
            'تمت الإضافة',
            'تم إضافة الحدث بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          if (widget.onEventCreated != null) {
            widget.onEventCreated!(newEvent);
          }
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'حدث خطأ: $e';
          _isLoading = false;
        });
      }
    }
  }

  // بناء تفاصيل المهمة المحددة
  Widget _buildSelectedTaskDetails() {
    // البحث عن المهمة المحددة
    final selectedTask = _availableTasks.firstWhere(
      (task) => task.id.toString() == _selectedTaskId,
      orElse: () => _availableTasks.first,
    );

    // تحديث تواريخ الحدث بناءً على المهمة المحددة
    if (selectedTask.startDate != null && selectedTask.dueDate != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _startDate = DateTime.fromMillisecondsSinceEpoch(selectedTask.startDate! * 1000);
          _startTime = TimeOfDay.fromDateTime(_startDate);
          _endDate = DateTime.fromMillisecondsSinceEpoch(selectedTask.dueDate! * 1000);
          _endTime = TimeOfDay.fromDateTime(_endDate);
        });
      });
    }

    // بناء بطاقة تفاصيل المهمة
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل المهمة المحددة:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.title, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedTask.title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            if (selectedTask.description?.isNotEmpty == true) ...[
              const SizedBox(height: 4),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.description, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      selectedTask.description!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.priority_high, size: 16),
                const SizedBox(width: 8),
                Text('الأولوية: ${_getPriorityName(TaskPriority.fromName(selectedTask.priority))}'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.check_circle_outline, size: 16),
                const SizedBox(width: 8),
                Text('الحالة: ${_getStatusName(TaskStatus.fromName(selectedTask.status))}'),
              ],
            ),
            if (selectedTask.startDate != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16),
                  const SizedBox(width: 8),
                  Text('تاريخ البدء: ${DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(selectedTask.startDate! * 1000))}'),
                ],
              ),
            ],
            if (selectedTask.dueDate != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.event, size: 16),
                  const SizedBox(width: 8),
                  Text('تاريخ الاستحقاق: ${DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(selectedTask.dueDate! * 1000))}'),
                ],
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.percent, size: 16),
                const SizedBox(width: 8),
                Text('نسبة الإنجاز: ${selectedTask.completionPercentage.toStringAsFixed(0)}%'),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: selectedTask.completionPercentage / 100.0,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor(selectedTask.completionPercentage.toDouble())),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على لون التقدم حسب النسبة
  Color _getProgressColor(double percentage) {
    if (percentage < 25) {
      return Colors.red;
    } else if (percentage < 50) {
      return Colors.orange;
    } else if (percentage < 75) {
      return Colors.blue;
    } else {
      return Colors.green;
    }
  }

  // الحصول على اسم الأولوية
  String _getPriorityName(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return 'منخفضة';
      case TaskPriority.medium:
        return 'متوسطة';
      case TaskPriority.high:
        return 'عالية';
      case TaskPriority.urgent:
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }

  // الحصول على اسم الحالة
  String _getStatusName(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغاة';
      case TaskStatus.news:
        return 'جديدة';
      default:
        return 'قيد الانتظار';
    }
  }

  // الحصول على اسم نوع الحدث
  String _getEventTypeName(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return 'مهمة';
      case CalendarEventType.meeting:
        return 'اجتماع';
      case CalendarEventType.reminder:
        return 'تذكير';
      case CalendarEventType.vacation:
        return 'إجازة';
      case CalendarEventType.other:
        return 'أخرى';
      default:
        return 'مهمة';
    }
  }

  // الحصول على لون حسب نوع الحدث
  Color _getEventTypeColor(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return Colors.orange;
      case CalendarEventType.reminder:
        return Colors.purple;
      case CalendarEventType.vacation:
        return Colors.green;
      case CalendarEventType.other:
        return Colors.grey;
      default:
        return AppColors.primary;
    }
  }

  // تحويل اللون إلى نص hex
  String _colorToHex(Color color) {
    return '#${color.toARGB32().toRadixString(16).substring(2).toUpperCase()}';
  }

  // تحويل النص إلى لون
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
