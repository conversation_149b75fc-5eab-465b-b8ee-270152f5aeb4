/// معايير التقرير
class ReportCriteria {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? userIds;
  final List<String>? departmentIds;
  final List<String>? taskIds;
  final Map<String, dynamic>? additionalFilters;

  const ReportCriteria({
    this.startDate,
    this.endDate,
    this.userIds,
    this.departmentIds,
    this.taskIds,
    this.additionalFilters,
  });

  factory ReportCriteria.fromJson(Map<String, dynamic> json) {
    return ReportCriteria(
      startDate: json['startDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['startDate'] as int)
          : null,
      endDate: json['endDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['endDate'] as int)
          : null,
      userIds: json['userIds'] != null 
          ? List<String>.from(json['userIds'] as List)
          : null,
      departmentIds: json['departmentIds'] != null 
          ? List<String>.from(json['departmentIds'] as List)
          : null,
      taskIds: json['taskIds'] != null 
          ? List<String>.from(json['taskIds'] as List)
          : null,
      additionalFilters: json['additionalFilters'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'userIds': userIds,
      'departmentIds': departmentIds,
      'taskIds': taskIds,
      'additionalFilters': additionalFilters,
    };
  }

  ReportCriteria copyWith({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? userIds,
    List<String>? departmentIds,
    List<String>? taskIds,
    Map<String, dynamic>? additionalFilters,
  }) {
    return ReportCriteria(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userIds: userIds ?? this.userIds,
      departmentIds: departmentIds ?? this.departmentIds,
      taskIds: taskIds ?? this.taskIds,
      additionalFilters: additionalFilters ?? this.additionalFilters,
    );
  }
}
