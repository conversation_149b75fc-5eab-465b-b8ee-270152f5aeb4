import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_quill/flutter_quill.dart';
import '../../models/text_document_model.dart';
import '../../models/archive_models.dart';
import 'archive_documents_api_service.dart';


/// خدمة API موحدة للمستندات النصية والأرشيف
/// تدمج TextDocument مع ArchiveDocument API بشكل سلس
class UnifiedDocumentApiService {
  final ArchiveDocumentsApiService _archiveService = ArchiveDocumentsApiService();

  /// الحصول على جميع المستندات النصية
  Future<List<TextDocument>> getAllTextDocuments() async {
    try {
      final archiveDocuments = await _archiveService.getAllDocuments();
      return archiveDocuments
          .where((doc) => _isTextDocument(doc))
          .map((doc) => _convertToTextDocument(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات النصية: $e');
      rethrow;
    }
  }

  /// الحصول على مستند نصي بواسطة المعرف
  Future<TextDocument?> getTextDocumentById(int id) async {
    try {
      final archiveDocument = await _archiveService.getDocumentById(id);
      if (archiveDocument != null && _isTextDocument(archiveDocument)) {
        return _convertToTextDocument(archiveDocument);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل المستند النصي: $e');
      return null;
    }
  }

  /// إنشاء مستند نصي جديد
  Future<TextDocument?> createTextDocument(TextDocument document) async {
    try {
      // تحويل TextDocument إلى ArchiveDocument
      final archiveDocument = _convertToArchiveDocument(document);
      final createdDocument = await _archiveService.createDocument(archiveDocument);
      
      if (createdDocument != null) {
        return _convertToTextDocument(createdDocument);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في إنشاء المستند النصي: $e');
      rethrow;
    }
  }

  /// تحديث مستند نصي
  Future<TextDocument?> updateTextDocument(TextDocument document) async {
    try {
      final archiveDocument = _convertToArchiveDocument(document);
      final updatedDocument = await _archiveService.updateDocument(archiveDocument);
      
      if (updatedDocument != null) {
        return _convertToTextDocument(updatedDocument);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحديث المستند النصي: $e');
      rethrow;
    }
  }

  /// حذف مستند نصي
  Future<bool> deleteTextDocument(int id) async {
    try {
      return await _archiveService.deleteDocument(id);
    } catch (e) {
      debugPrint('خطأ في حذف المستند النصي: $e');
      return false;
    }
  }

  /// البحث في المستندات النصية
  Future<List<TextDocument>> searchTextDocuments(String query) async {
    try {
      final archiveDocuments = await _archiveService.searchDocuments(query);
      return archiveDocuments
          .where((doc) => _isTextDocument(doc))
          .map((doc) => _convertToTextDocument(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن المستندات النصية: $e');
      rethrow;
    }
  }

  /// الحصول على المستندات النصية المرتبطة بمهمة
  Future<List<TextDocument>> getTextDocumentsByTaskId(int taskId) async {
    try {
      // البحث عن المستندات التي تحتوي على معرف المهمة في البيانات الوصفية
      final searchQuery = 'task_$taskId';
      final documents = await searchTextDocuments(searchQuery);
      
      // تصفية إضافية للتأكد من الربط الصحيح
      return documents.where((doc) => doc.taskId == taskId).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
      return [];
    }
  }

  /// الحصول على المستندات النصية حسب النوع
  Future<List<TextDocument>> getTextDocumentsByType(TextDocumentType type) async {
    try {
      final allDocuments = await getAllTextDocuments();
      return allDocuments.where((doc) => doc.type == type).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات النوع ${type.value}: $e');
      return [];
    }
  }

  /// الحصول على المستندات النصية المشتركة
  Future<List<TextDocument>> getSharedTextDocuments() async {
    try {
      final allDocuments = await getAllTextDocuments();
      return allDocuments.where((doc) => doc.isShared).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة: $e');
      return [];
    }
  }

  /// الحصول على المستندات النصية الحديثة
  Future<List<TextDocument>> getRecentTextDocuments({int limit = 10}) async {
    try {
      final allDocuments = await getAllTextDocuments();
      allDocuments.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return allDocuments.take(limit).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة: $e');
      return [];
    }
  }

  /// رفع مستند نصي مع ملف
  Future<TextDocument?> uploadTextDocument({
    required String title,
    required File file,
    required TextDocumentType type,
    String? description,
    int? categoryId,
    int? taskId,
    bool isShared = false,
    required int createdBy,
  }) async {
    try {
      // إنشاء metadata يحتوي على معلومات المستند النصي
      final metadataJson = jsonEncode({
        'type': type.value,
        'taskId': taskId,
        'isShared': isShared,
      });

      final archiveDocument = await _archiveService.uploadDocument(
        title: title,
        categoryId: categoryId ?? 1, // فئة افتراضية للمستندات النصية
        file: file,
        fileName: file.path.split('/').last,
        fileType: 'text/plain',
        description: description,
        createdBy: createdBy,
      );

      // تحديث metadata إذا تم إنشاء المستند بنجاح
      if (archiveDocument != null) {
        // يمكن إضافة تحديث metadata هنا إذا لزم الأمر
        debugPrint('تم إنشاء مستند بـ metadata: $metadataJson');
      }

      if (archiveDocument != null) {
        return _convertToTextDocument(archiveDocument);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في رفع المستند النصي: $e');
      rethrow;
    }
  }

  // ==================== دوال دعم Flutter Quill ====================

  /// حفظ محتوى Quill Delta
  Future<TextDocument?> saveQuillContent({
    required int documentId,
    required String deltaJson,
    String? plainText,
  }) async {
    try {
      // الحصول على المستند الحالي
      final document = await getTextDocumentById(documentId);
      if (document == null) return null;

      // تحديث المحتوى
      final updatedDocument = document.copyWith(
        content: deltaJson,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      return await updateTextDocument(updatedDocument);
    } catch (e) {
      debugPrint('خطأ في حفظ محتوى Quill: $e');
      rethrow;
    }
  }

  /// إنشاء مستند جديد بمحتوى Quill
  Future<TextDocument?> createQuillDocument({
    required String title,
    required String deltaJson,
    required TextDocumentType type,
    String? description,
    String? plainText,
    int? categoryId,
    int? taskId,
    bool isShared = false,
    required int createdBy,
  }) async {
    try {
      // إنشاء metadata مع معلومات Quill
      final metadata = jsonEncode({
        'type': type.value,
        'taskId': taskId,
        'isShared': isShared,
        'format': 'quill_delta',
        'version': '1.0',
      });

      // إنشاء مستند جديد
      final document = TextDocument(
        id: 0, // سيتم تعيينه من الخادم
        title: title,
        description: description,
        fileName: '${title.replaceAll(' ', '_')}.json',
        filePath: '',
        fileType: 'application/json',
        fileSize: deltaJson.length,
        categoryId: categoryId,
        metadata: metadata,
        content: deltaJson,
        uploadedBy: createdBy,
        uploadedAt: DateTime.now().millisecondsSinceEpoch,
        createdBy: createdBy,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: null,
        isDeleted: false,
        type: type,
        taskId: taskId,
        isShared: isShared,
      );

      return await createTextDocument(document);
    } catch (e) {
      debugPrint('خطأ في إنشاء مستند Quill: $e');
      rethrow;
    }
  }

  /// تحديث محتوى مستند بشكل تلقائي
  Future<bool> autoSaveDocument({
    required int documentId,
    required String deltaJson,
    String? plainText,
  }) async {
    try {
      final result = await saveQuillContent(
        documentId: documentId,
        deltaJson: deltaJson,
        plainText: plainText,
      );
      return result != null;
    } catch (e) {
      debugPrint('خطأ في الحفظ التلقائي: $e');
      return false;
    }
  }

  /// تصدير مستند Quill إلى تنسيقات مختلفة
  Future<String?> exportQuillDocument({
    required int documentId,
    required String format, // 'pdf', 'html', 'docx', 'txt'
  }) async {
    try {
      final document = await getTextDocumentById(documentId);
      if (document == null) return null;

      switch (format.toLowerCase()) {
        case 'txt':
          return _extractPlainTextFromDelta(document.content ?? '');
        case 'html':
          return _convertDeltaToHtml(document.content ?? '');
        case 'json':
          return document.content;
        default:
          debugPrint('تنسيق التصدير غير مدعوم: $format');
          return null;
      }
    } catch (e) {
      debugPrint('خطأ في تصدير المستند: $e');
      return null;
    }
  }

  /// استخراج النص العادي من Delta JSON
  String _extractPlainTextFromDelta(String deltaJson) {
    try {
      if (deltaJson.isEmpty) return '';

      final deltaData = jsonDecode(deltaJson);
      final document = Document.fromJson(deltaData);
      return document.toPlainText();
    } catch (e) {
      debugPrint('خطأ في استخراج النص العادي: $e');
      return deltaJson; // إرجاع النص كما هو في حالة الخطأ
    }
  }

  /// تحويل Delta إلى HTML
  String _convertDeltaToHtml(String deltaJson) {
    try {
      if (deltaJson.isEmpty) return '';

      // تحويل بسيط - يمكن تحسينه لاحقاً
      final plainText = _extractPlainTextFromDelta(deltaJson);
      return '''
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
          <meta charset="UTF-8">
          <title>مستند</title>
          <style>
              body {
                  font-family: Arial, sans-serif;
                  direction: rtl;
                  line-height: 1.6;
                  margin: 20px;
              }
          </style>
      </head>
      <body>
          <div>${plainText.replaceAll('\n', '<br>')}</div>
      </body>
      </html>
      ''';
    } catch (e) {
      debugPrint('خطأ في تحويل Delta إلى HTML: $e');
      return '';
    }
  }

  /// التحقق من كون المستند مستنداً نصياً
  bool _isTextDocument(ArchiveDocument document) {
    // التحقق من نوع الملف أو البيانات الوصفية
    return document.fileType.startsWith('text/') ||
           document.metadata?.contains('note') == true ||
           document.metadata?.contains('report') == true ||
           document.metadata?.contains('template') == true ||
           document.metadata?.contains('letter') == true ||
           document.metadata?.contains('memo') == true ||
           document.metadata?.contains('contract') == true;
  }

  /// تحويل ArchiveDocument إلى TextDocument
  TextDocument _convertToTextDocument(ArchiveDocument archiveDoc) {
    // استخراج نوع المستند من البيانات الوصفية
    final type = archiveDoc.metadata != null
        ? TextDocumentType.fromValue(archiveDoc.metadata!)
        : TextDocumentType.note;

    // استخراج معرف المهمة من البيانات الوصفية إذا كان موجوداً
    int? taskId;
    bool isShared = false;
    
    if (archiveDoc.metadata != null) {
      try {
        // محاولة استخراج معلومات إضافية من البيانات الوصفية
        // يمكن تحسين هذا لاحقاً لاستخدام JSON
        if (archiveDoc.metadata!.contains('task_')) {
          final taskMatch = RegExp(r'task_(\d+)').firstMatch(archiveDoc.metadata!);
          if (taskMatch != null) {
            taskId = int.tryParse(taskMatch.group(1)!);
          }
        }
        isShared = archiveDoc.metadata!.contains('shared');
      } catch (e) {
        debugPrint('خطأ في تحليل البيانات الوصفية: $e');
      }
    }

    return TextDocument(
      id: archiveDoc.id,
      title: archiveDoc.title,
      description: archiveDoc.description,
      fileName: archiveDoc.fileName,
      filePath: archiveDoc.filePath,
      fileType: archiveDoc.fileType,
      fileSize: archiveDoc.fileSize,
      categoryId: archiveDoc.categoryId,
      metadata: archiveDoc.metadata,
      content: archiveDoc.content,
      uploadedBy: archiveDoc.uploadedBy,
      uploadedAt: archiveDoc.uploadedAt,
      createdBy: archiveDoc.createdBy,
      createdAt: archiveDoc.createdAt,
      updatedAt: archiveDoc.updatedAt,
      isDeleted: archiveDoc.isDeleted,
      type: type,
      taskId: taskId,
      isShared: isShared,
      category: archiveDoc.category,
      uploadedByUser: archiveDoc.uploadedByUser,
      tags: archiveDoc.tags,
    );
  }

  /// تحويل TextDocument إلى ArchiveDocument
  ArchiveDocument _convertToArchiveDocument(TextDocument textDoc) {
    // إنشاء metadata يحتوي على معلومات المستند النصي
    final metadataMap = {
      'type': textDoc.type.value,
      if (textDoc.taskId != null) 'taskId': textDoc.taskId,
      'isShared': textDoc.isShared,
    };

    return ArchiveDocument(
      id: textDoc.id,
      title: textDoc.title,
      description: textDoc.description,
      fileName: textDoc.fileName,
      filePath: textDoc.filePath,
      fileType: textDoc.fileType,
      fileSize: textDoc.fileSize,
      categoryId: textDoc.categoryId,
      metadata: textDoc.metadata ?? jsonEncode(metadataMap),
      content: textDoc.content,
      uploadedBy: textDoc.uploadedBy,
      uploadedAt: textDoc.uploadedAt,
      createdBy: textDoc.createdBy,
      createdAt: textDoc.createdAt,
      updatedAt: textDoc.updatedAt,
      isDeleted: textDoc.isDeleted,
      category: textDoc.category,
      uploadedByUser: textDoc.uploadedByUser,
      tags: textDoc.tags,
    );
  }
}
