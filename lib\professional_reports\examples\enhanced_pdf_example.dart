/// مثال متقدم لإنشاء تقارير PDF احترافية
/// 
/// يحتوي على تصميم متطور مع مخططات بيانية وتخطيط مرن

library;

import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

/// فئة وهمية لتمثيل بيانات المستخدم للمثال
class ExampleUser {
  final String name;
  final String email;
  final DateTime createdAt;
  final String role;
  final bool isActive;

  ExampleUser({
    required this.name,
    required this.email,
    required this.createdAt,
    required this.role,
    required this.isActive,
  });
}

/// دالة إنشاء PDF محسّنة مع تصميم احترافي
Future<Uint8List> generateEnhancedPdfExample() async {
  final pdf = pw.Document();

  // تحميل الخطوط - استخدام Google Fonts بدلاً من الخطوط المحلية
  final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
  final arabicBoldFont = await PdfGoogleFonts.notoSansArabicBold();

  // بيانات وهمية للمثال
  final List<ExampleUser> users = [
    ExampleUser(
      name: 'أحمد محمود', 
      email: '<EMAIL>', 
      createdAt: DateTime.now().subtract(const Duration(days: 10)), 
      role: 'مسؤول', 
      isActive: true
    ),
    ExampleUser(
      name: 'فاطمة الزهراء', 
      email: '<EMAIL>', 
      createdAt: DateTime.now().subtract(const Duration(days: 25)), 
      role: 'مستخدم', 
      isActive: true
    ),
    ExampleUser(
      name: 'خالد بن الوليد', 
      email: '<EMAIL>', 
      createdAt: DateTime.now().subtract(const Duration(days: 5)), 
      role: 'مستخدم', 
      isActive: false
    ),
    ExampleUser(
      name: 'عائشة علي', 
      email: '<EMAIL>', 
      createdAt: DateTime.now().subtract(const Duration(days: 40)), 
      role: 'مشرف', 
      isActive: true
    ),
    ExampleUser(
      name: 'عمر الفاروق', 
      email: '<EMAIL>', 
      createdAt: DateTime.now().subtract(const Duration(days: 120)), 
      role: 'مستخدم', 
      isActive: false
    ),
  ];

  final int totalUsers = users.length;
  final int activeUsers = users.where((u) => u.isActive).length;
  final int inactiveUsers = totalUsers - activeUsers;

  // تعريف لوحة الألوان الاحترافية
  const PdfColor primaryColor = PdfColors.blueGrey800;
  const PdfColor secondaryColor = PdfColors.blueGrey50;
  const PdfColor accentColor = PdfColors.teal400;

  // إضافة صفحة إلى المستند
  pdf.addPage(
    pw.MultiPage(
      pageTheme: _buildTheme(PdfPageFormat.a4, arabicFont, arabicBoldFont),
      textDirection: pw.TextDirection.rtl,
      header: (pw.Context context) => _buildHeader(context, 'تقرير المستخدمين المتطور', primaryColor, arabicBoldFont),
      footer: (pw.Context context) => _buildFooter(context, arabicFont),
      build: (pw.Context context) {
        return [
          // القسم العلوي: معلومات التقرير والمخطط
          _buildInfoAndChartSection(
            totalUsers,
            activeUsers,
            inactiveUsers,
            primaryColor,
            accentColor,
            arabicFont,
            arabicBoldFont,
          ),
          
          pw.SizedBox(height: 30),

          // عنوان جدول المستخدمين
          _buildSectionTitle('قائمة المستخدمين التفصيلية', primaryColor, arabicBoldFont),
          
          pw.SizedBox(height: 15),
          
          // جدول بيانات المستخدمين المحسّن
          _buildUsersTable(users, primaryColor, secondaryColor, arabicFont, arabicBoldFont),
        ];
      },
    ),
  );

  return pdf.save();
}

/// بناء ثيم الصفحة الاحترافي
pw.PageTheme _buildTheme(PdfPageFormat format, pw.Font base, pw.Font bold) {
  return pw.PageTheme(
    pageFormat: format.copyWith(
      marginBottom: 1.5 * PdfPageFormat.cm,
      marginTop: 1.5 * PdfPageFormat.cm,
      marginLeft: 2 * PdfPageFormat.cm,
      marginRight: 2 * PdfPageFormat.cm,
    ),
    theme: pw.ThemeData.withFont(base: base, bold: bold),
    textDirection: pw.TextDirection.rtl,
  );
}

/// بناء رأس الصفحة الاحترافي
pw.Widget _buildHeader(pw.Context context, String title, PdfColor primaryColor, pw.Font boldFont) {
  return pw.Container(
    alignment: pw.Alignment.centerRight,
    padding: const pw.EdgeInsets.only(bottom: 8),
    decoration: const pw.BoxDecoration(
      border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey300, width: 2)),
    ),
    child: pw.Text(
      title,
      style: pw.TextStyle(font: boldFont, fontSize: 18, color: primaryColor),
      textDirection: pw.TextDirection.rtl,
    ),
  );
}

/// بناء تذييل الصفحة
pw.Widget _buildFooter(pw.Context context, pw.Font arabicFont) {
  return pw.Container(
    alignment: pw.Alignment.centerRight,
    margin: const pw.EdgeInsets.only(top: 1.0 * PdfPageFormat.cm),
    child: pw.Text(
      'صفحة ${context.pageNumber} من ${context.pagesCount}',
      style: pw.TextStyle(font: arabicFont, fontSize: 10),
      textDirection: pw.TextDirection.rtl,
    ),
  );
}

/// بناء قسم المعلومات والمخطط البياني المتطور
pw.Widget _buildInfoAndChartSection(
  int totalUsers,
  int activeUsers,
  int inactiveUsers,
  PdfColor primaryColor,
  PdfColor accentColor,
  pw.Font arabicFont,
  pw.Font arabicBoldFont,
) {
  return pw.Partitions(
    children: [
      // القسم الأيمن: معلومات وإحصائيات سريعة
      pw.Partition(
        width: 200,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('ملخص التقرير', primaryColor, arabicBoldFont),
            pw.SizedBox(height: 10),
            _buildInfoRow('تاريخ الإنشاء:', _formatDate(DateTime.now()), arabicFont),
            pw.SizedBox(height: 5),
            _buildInfoRow('إصدار التقرير:', '2.0.0', arabicFont),
            pw.Divider(height: 30, color: PdfColors.grey300),
            _buildSectionTitle('إحصائيات سريعة', primaryColor, arabicBoldFont),
            pw.SizedBox(height: 15),
            _buildStatCard('إجمالي المستخدمين', totalUsers.toString(), PdfColors.blueGrey, arabicFont, arabicBoldFont),
            pw.SizedBox(height: 10),
            _buildStatCard('المستخدمون النشطون', activeUsers.toString(), PdfColors.green, arabicFont, arabicBoldFont),
            pw.SizedBox(height: 10),
            _buildStatCard('المستخدمون غير النشطين', inactiveUsers.toString(), PdfColors.red, arabicFont, arabicBoldFont),
          ],
        ),
      ),
      // فاصل بين القسمين
      pw.Partition(
        width: 20,
        child: pw.Container(
          margin: const pw.EdgeInsets.symmetric(horizontal: 5),
          decoration: const pw.BoxDecoration(
            border: pw.Border(right: pw.BorderSide(color: PdfColors.grey300, width: 1)),
          ),
        ),
      ),
      // القسم الأيسر: مخطط بياني بسيط (بديل للمخطط الدائري)
      pw.Partition(
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('توزيع حالة المستخدمين', primaryColor, arabicBoldFont),
            pw.SizedBox(height: 20),
            // مخطط بياني بسيط باستخدام أشرطة ملونة
            pw.SizedBox(
              height: 150,
              child: pw.Column(
                children: [
                  // شريط المستخدمين النشطين
                  _buildSimpleChart('المستخدمون النشطون', activeUsers, totalUsers, PdfColors.green400, arabicFont, arabicBoldFont),
                  pw.SizedBox(height: 15),
                  // شريط المستخدمين غير النشطين
                  _buildSimpleChart('المستخدمون غير النشطين', inactiveUsers, totalUsers, PdfColors.red400, arabicFont, arabicBoldFont),
                  pw.SizedBox(height: 20),
                  // إحصائيات مئوية
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildPercentageInfo('نشط', (activeUsers / totalUsers * 100).toStringAsFixed(1), PdfColors.green400, arabicFont),
                      _buildPercentageInfo('غير نشط', (inactiveUsers / totalUsers * 100).toStringAsFixed(1), PdfColors.red400, arabicFont),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

/// بناء عنوان القسم
pw.Widget _buildSectionTitle(String title, PdfColor color, pw.Font boldFont) {
  return pw.Text(
    title,
    style: pw.TextStyle(
      font: boldFont,
      fontSize: 14,
      color: color,
    ),
    textDirection: pw.TextDirection.rtl,
  );
}

/// بناء صف معلومات
pw.Widget _buildInfoRow(String label, String value, pw.Font arabicFont) {
  return pw.Row(
    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
    children: [
      pw.Text(
        label,
        style: pw.TextStyle(font: arabicFont, fontSize: 10, color: PdfColors.grey600),
        textDirection: pw.TextDirection.rtl,
      ),
      pw.Text(
        value,
        style: pw.TextStyle(font: arabicFont, fontSize: 10),
        textDirection: pw.TextDirection.rtl,
      ),
    ],
  );
}

/// بناء بطاقة إحصائية محسّنة
pw.Widget _buildStatCard(String title, String value, PdfColor color, pw.Font regularFont, pw.Font boldFont) {
  return pw.Container(
    padding: const pw.EdgeInsets.all(10),
    decoration: pw.BoxDecoration(
      color: color.shade(0.1), // لون خلفية خفيف
      border: pw.Border(right: pw.BorderSide(color: color, width: 4)),
      borderRadius: pw.BorderRadius.circular(4),
    ),
    child: pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(font: regularFont, fontSize: 11, color: PdfColors.grey800),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Text(
          value,
          style: pw.TextStyle(font: boldFont, fontSize: 18, color: color),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    ),
  );
}

/// بناء مخطط بياني بسيط باستخدام أشرطة ملونة
pw.Widget _buildSimpleChart(String label, int value, int total, PdfColor color, pw.Font regularFont, pw.Font boldFont) {
  final percentage = (value / total);
  return pw.Row(
    children: [
      pw.SizedBox(
        width: 80,
        child: pw.Text(
          label,
          style: pw.TextStyle(font: regularFont, fontSize: 10),
          textDirection: pw.TextDirection.rtl,
        ),
      ),
      pw.SizedBox(width: 10),
      pw.Expanded(
        child: pw.Stack(
          children: [
            // الخلفية الرمادية للشريط
            pw.Container(
              height: 20,
              decoration: pw.BoxDecoration(
                color: PdfColors.grey200,
                borderRadius: pw.BorderRadius.circular(10),
              ),
            ),
            // الشريط الملون بالنسبة المحددة
            pw.Container(
              height: 20,
              width: 120 * percentage, // عرض ثابت مضروب في النسبة
              decoration: pw.BoxDecoration(
                color: color,
                borderRadius: pw.BorderRadius.circular(10),
              ),
            ),
          ],
        ),
      ),
      pw.SizedBox(width: 10),
      pw.Text(
        value.toString(),
        style: pw.TextStyle(font: boldFont, fontSize: 12, color: color),
        textDirection: pw.TextDirection.rtl,
      ),
    ],
  );
}

/// بناء معلومات النسبة المئوية
pw.Widget _buildPercentageInfo(String label, String percentage, PdfColor color, pw.Font regularFont) {
  return pw.Container(
    padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: pw.BoxDecoration(
      color: color.shade(0.1),
      borderRadius: pw.BorderRadius.circular(15),
      border: pw.Border.all(color: color, width: 1),
    ),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.min,
      children: [
        pw.Container(
          width: 8,
          height: 8,
          decoration: pw.BoxDecoration(
            color: color,
            shape: pw.BoxShape.circle,
          ),
        ),
        pw.SizedBox(width: 5),
        pw.Text(
          '$label: $percentage%',
          style: pw.TextStyle(font: regularFont, fontSize: 9, color: color),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    ),
  );
}

/// بناء جدول المستخدمين المحسّن
pw.Widget _buildUsersTable(List<ExampleUser> users, PdfColor primaryColor, PdfColor secondaryColor, pw.Font regularFont, pw.Font boldFont) {
  final headers = ['#', 'الاسم', 'البريد الإلكتروني', 'تاريخ الإنشاء', 'الدور', 'الحالة'];

  final data = users.asMap().entries.map((entry) {
    final index = entry.key + 1;
    final user = entry.value;
    return [
      index.toString(),
      user.name,
      user.email,
      _formatDate(user.createdAt),
      user.role,
      user.isActive ? 'نشط' : 'غير نشط',
    ];
  }).toList();

  return pw.TableHelper.fromTextArray(
    headers: headers,
    data: data,
    border: pw.TableBorder.all(color: PdfColors.grey300, width: 1),
    headerStyle: pw.TextStyle(
      font: boldFont,
      fontSize: 11,
      color: PdfColors.white,
    ),
    headerDecoration: pw.BoxDecoration(
      color: primaryColor,
    ),
    cellStyle: pw.TextStyle(
      font: regularFont,
      fontSize: 10,
    ),
    cellAlignments: {
      0: pw.Alignment.center,
      1: pw.Alignment.centerRight,
      2: pw.Alignment.centerRight,
      3: pw.Alignment.center,
      4: pw.Alignment.center,
      5: pw.Alignment.center,
    },
    // تطبيق ألوان مختلفة للصفوف الفردية والزوجية
    rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
    oddRowDecoration: pw.BoxDecoration(color: secondaryColor),
  );
}

/// تنسيق التاريخ
String _formatDate(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
}