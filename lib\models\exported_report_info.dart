import 'enhanced_report_model.dart';

/// معلومات التقرير المصدر
/// 
/// يحتوي على معلومات التقرير المصدر مثل العنوان والمسار وتاريخ التصدير
class ExportedReportInfo {
  final String id;
  final String title;
  final String filePath;
  final ReportFormat format;
  final int fileSize;
  final DateTime exportedAt;
  final String? description;
  final String? reportId;

  const ExportedReportInfo({
    required this.id,
    required this.title,
    required this.filePath,
    required this.format,
    required this.fileSize,
    required this.exportedAt,
    this.description,
    this.reportId,
  });

  /// إنشاء من JSON
  factory ExportedReportInfo.fromJson(Map<String, dynamic> json) {
    return ExportedReportInfo(
      id: json['id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      filePath: json['filePath']?.toString() ?? '',
      format: _parseFormat(json['format']?.toString()),
      fileSize: json['fileSize'] as int? ?? 0,
      exportedAt: json['exportedAt'] != null 
          ? DateTime.parse(json['exportedAt'].toString())
          : DateTime.now(),
      description: json['description']?.toString(),
      reportId: json['reportId']?.toString(),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'filePath': filePath,
      'format': format.name,
      'fileSize': fileSize,
      'exportedAt': exportedAt.toIso8601String(),
      'description': description,
      'reportId': reportId,
    };
  }

  /// نسخ مع تعديل بعض الخصائص
  ExportedReportInfo copyWith({
    String? id,
    String? title,
    String? filePath,
    ReportFormat? format,
    int? fileSize,
    DateTime? exportedAt,
    String? description,
    String? reportId,
  }) {
    return ExportedReportInfo(
      id: id ?? this.id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      format: format ?? this.format,
      fileSize: fileSize ?? this.fileSize,
      exportedAt: exportedAt ?? this.exportedAt,
      description: description ?? this.description,
      reportId: reportId ?? this.reportId,
    );
  }

  /// تحليل تنسيق التقرير من النص
  static ReportFormat _parseFormat(String? formatString) {
    if (formatString == null) return ReportFormat.pdf;
    
    switch (formatString.toLowerCase()) {
      case 'pdf':
        return ReportFormat.pdf;
      case 'excel':
      case 'xlsx':
        return ReportFormat.excel;
      case 'csv':
        return ReportFormat.csv;
      case 'json':
        return ReportFormat.json;
      case 'xml':
        return ReportFormat.xml;
      case 'html':
        return ReportFormat.html;
      default:
        return ReportFormat.pdf;
    }
  }

  /// الحصول على امتداد الملف
  String get fileExtension {
    switch (format) {
      case ReportFormat.pdf:
        return '.pdf';
      case ReportFormat.excel:
        return '.xlsx';
      case ReportFormat.csv:
        return '.csv';
      case ReportFormat.json:
        return '.json';
      case ReportFormat.xml:
        return '.xml';
      case ReportFormat.html:
        return '.html';
    }
  }

  /// الحصول على اسم التنسيق المعروض
  String get formatDisplayName {
    switch (format) {
      case ReportFormat.pdf:
        return 'PDF';
      case ReportFormat.excel:
        return 'Excel';
      case ReportFormat.csv:
        return 'CSV';
      case ReportFormat.json:
        return 'JSON';
      case ReportFormat.xml:
        return 'XML';
      case ReportFormat.html:
        return 'HTML';
    }
  }

  @override
  String toString() {
    return 'ExportedReportInfo(id: $id, title: $title, format: $format, fileSize: $fileSize)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExportedReportInfo &&
        other.id == id &&
        other.title == title &&
        other.filePath == filePath &&
        other.format == format &&
        other.fileSize == fileSize &&
        other.exportedAt == exportedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      filePath,
      format,
      fileSize,
      exportedAt,
    );
  }
}
