import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';

import '../../controllers/power_bi_controller.dart';

import '../../models/power_bi_models.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/empty_state_widget.dart';
import '../widgets/reporting/visualizations/bar_chart_visualization.dart';
import '../widgets/reporting/visualizations/line_chart_visualization.dart';
import '../widgets/reporting/visualizations/pie_chart_visualization.dart';

/// شاشة تكامل Power BI
/// تعرض تقارير Power BI المدمجة مع النظام
class PowerBIIntegrationScreen extends StatefulWidget {
  final String? reportId;
  final String? dashboardId;

  const PowerBIIntegrationScreen({
    super.key,
    this.reportId,
    this.dashboardId,
  });

  @override
  State<PowerBIIntegrationScreen> createState() => _PowerBIIntegrationScreenState();
}

class _PowerBIIntegrationScreenState extends State<PowerBIIntegrationScreen> with SingleTickerProviderStateMixin {
  final PowerBIController _powerBIController = Get.find<PowerBIController>();

  late TabController _tabController;

  bool _isLoading = true;
  String? _errorMessage;
  PowerBIReport? _currentReport;
  Map<String, dynamic> _chartData = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializePowerBI();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تهيئة Power BI
  Future<void> _initializePowerBI() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل التقارير
      await _powerBIController.loadMyReports();
      await _powerBIController.loadSharedReports();

      // إذا تم تحديد تقرير، قم بتحميله
      if (widget.reportId != null) {
        await _loadReport(widget.reportId!);
      }

    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل تقرير محدد
  Future<void> _loadReport(String reportId) async {
    try {
      await _powerBIController.loadReport(reportId);
      setState(() {
        _currentReport = _powerBIController.currentReport.value;
        _chartData = _powerBIController.chartData;
      });
    } catch (e) {
      throw Exception('فشل في تحميل التقرير: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تكامل Power BI'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializePowerBI,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: 'الإعدادات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'العارض'),
            Tab(text: 'التقارير'),
            Tab(text: 'لوحات المعلومات'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _initializePowerBI,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildViewerTab(),
        _buildReportsTab(),
        _buildDashboardsTab(),
      ],
    );
  }

  /// بناء تبويب العارض
  Widget _buildViewerTab() {
    if (_currentReport == null) {
      return const EmptyStateWidget(
        icon: Icons.dashboard,
        message: 'لا يوجد محتوى للعرض',
        description: 'يرجى اختيار تقرير من التبويبات الأخرى.',
      );
    }

    return _buildChartVisualization(_currentReport!, _chartData);
  }

  /// بناء تصور الرسم البياني
  Widget _buildChartVisualization(PowerBIReport report, Map<String, dynamic> data) {
    if (data.isEmpty) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    final chartData = data['data'] as List<dynamic>? ?? [];
    if (chartData.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.bar_chart,
        message: 'لا توجد بيانات',
        description: 'لا توجد بيانات متاحة لعرض هذا التقرير.',
      );
    }

    // تحويل البيانات إلى تنسيق مناسب للرسوم البيانية
    final visualizationData = chartData.map((item) => {
      'label': item['label']?.toString() ?? '',
      'value': (item['value'] as num?)?.toDouble() ?? 0.0,
    }).toList();

    switch (report.chartType) {
      case PowerBIChartType.bar:
        return BarChartVisualization(
          title: report.title,
          description: report.description,
          data: visualizationData,
          xAxisField: 'label',
          yAxisField: 'value',
        );
      case PowerBIChartType.line:
        return LineChartVisualization(
          title: report.title,
          description: report.description,
          data: visualizationData,
          xAxisField: 'label',
          yAxisFields: ['value'],
        );
      case PowerBIChartType.pie:
        return PieChartVisualization(
          title: report.title,
          description: report.description,
          data: visualizationData,
          valueField: 'value',
          labelField: 'label',
        );
      default:
        return BarChartVisualization(
          title: report.title,
          description: report.description,
          data: visualizationData,
          xAxisField: 'label',
          yAxisField: 'value',
        );
    }
  }

  /// بناء تبويب التقارير
  Widget _buildReportsTab() {
    return Obx(() {
      final myReports = _powerBIController.myReports;
      final sharedReports = _powerBIController.sharedReports;
      final allReports = [...myReports, ...sharedReports];

      if (allReports.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.bar_chart,
          message: 'لا توجد تقارير',
          description: 'لا توجد تقارير Power BI متاحة.',
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: allReports.length,
        itemBuilder: (context, index) {
          final report = allReports[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: Icon(
                _getChartIcon(report.chartType),
                color: Colors.blue,
              ),
              title: Text(
                report.title,
                style: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (report.description != null)
                    Text(
                      report.description!,
                      style: AppStyles.bodySmall,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    'نوع الرسم: ${_getChartTypeName(report.chartType)}',
                    style: AppStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _openReport(report.id),
            ),
          );
        },
      );
    });
  }

  /// بناء تبويب لوحات المعلومات
  Widget _buildDashboardsTab() {
    return const EmptyStateWidget(
      icon: Icons.dashboard,
      message: 'لوحات المعلومات غير متاحة',
      description: 'هذه الميزة ستكون متاحة في التحديثات القادمة.',
    );
  }

  /// فتح تقرير
  Future<void> _openReport(String reportId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _loadReport(reportId);
      _tabController.animateTo(0); // الانتقال إلى تبويب العارض
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في فتح التقرير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض الإعدادات
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات Power BI'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.info),
              title: Text('معلومات الاتصال'),
              subtitle: Text('متصل بـ Power BI'),
            ),
            ListTile(
              leading: Icon(Icons.security),
              title: Text('الأمان'),
              subtitle: Text('رمز الوصول صالح'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _initializePowerBI();
            },
            child: const Text('إعادة الاتصال'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع الرسم البياني
  IconData _getChartIcon(PowerBIChartType chartType) {
    switch (chartType) {
      case PowerBIChartType.bar:
        return Icons.bar_chart;
      case PowerBIChartType.line:
        return Icons.show_chart;
      case PowerBIChartType.pie:
        return Icons.pie_chart;
      case PowerBIChartType.scatter:
        return Icons.scatter_plot;
      case PowerBIChartType.bubble:
        return Icons.bubble_chart;
      case PowerBIChartType.radar:
        return Icons.radar;
      case PowerBIChartType.table:
        return Icons.table_chart;
      case PowerBIChartType.heatmap:
        return Icons.grid_on;
      case PowerBIChartType.treemap:
        return Icons.account_tree;
      case PowerBIChartType.gauge:
        return Icons.speed;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على اسم نوع الرسم البياني
  String _getChartTypeName(PowerBIChartType chartType) {
    return chartType.displayName;
  }
}
