import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

import '../performance_reports/workload_distribution_report.dart';
import '../../services/api/task_api_service.dart';
import '../../services/api/user_api_service.dart';
import '../../services/api/departments_api_service.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';

// فئة بيانات للرسوم البيانية
class WorkloadChartData {
  final String name;
  final double value;
  final Color color;

  WorkloadChartData({
    required this.name,
    required this.value,
    required this.color,
  });
}

class WorkloadDistributionReportScreen extends StatefulWidget {
  const WorkloadDistributionReportScreen({super.key});

  @override
  State<WorkloadDistributionReportScreen> createState() =>
      _WorkloadDistributionReportScreenState();
}

class _WorkloadDistributionReportScreenState
    extends State<WorkloadDistributionReportScreen> {
  final TaskApiService _taskApiService = TaskApiService();
  final UserApiService _userApiService = UserApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();

  late Future<WorkloadDistributionReport> _reportFuture;
  List<User> _allUsers = [];
  List<Department> _allDepartments = [];

  // متغيرات التصفية
  DateTime? _startDate;
  DateTime? _endDate;
  Set<int> _selectedUserIds = {};
  Set<int> _selectedDepartmentIds = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() async {
    try {
      _allUsers = await _userApiService.getAllUsers();
      _allDepartments = await _departmentsApiService.getAllDepartments();
      _reportFuture = _getReport();
      setState(() {});
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الأولية: $e');
      Get.snackbar(
        'خطأ',
        'فشل في تحميل البيانات الأولية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<WorkloadDistributionReport> _getReport() async {
    try {
      final tasks = await _taskApiService.getAllTasks();
      
      // تطبيق تصفية المستخدمين والأقسام
      final filteredUsers = _selectedUserIds.isEmpty 
          ? _allUsers 
          : _allUsers.where((user) => _selectedUserIds.contains(user.id)).toList();
      
      final filteredDepartments = _selectedDepartmentIds.isEmpty 
          ? _allDepartments 
          : _allDepartments.where((dept) => _selectedDepartmentIds.contains(dept.id)).toList();

      return WorkloadDistributionReport.fromTasks(
        tasks,
        filteredUsers,
        filteredDepartments,
        startDate: _startDate,
        endDate: _endDate,
        period: _getPeriodText(),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  String _getPeriodText() {
    if (_startDate != null && _endDate != null) {
      final formatter = DateFormat('yyyy/MM/dd');
      return 'من ${formatter.format(_startDate!)} إلى ${formatter.format(_endDate!)}';
    }
    return 'الشهر الحالي';
  }

  void _refreshReport() {
    setState(() {
      _reportFuture = _getReport();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير توزيع عبء العمل'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshReport,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          Expanded(
            child: FutureBuilder<WorkloadDistributionReport>(
              future: _reportFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('جاري تحليل توزيع عبء العمل...'),
                      ],
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error, size: 64, color: Colors.red),
                        SizedBox(height: 16),
                        Text('خطأ: ${snapshot.error}'),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _refreshReport,
                          child: Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                } else if (snapshot.hasData) {
                  final report = snapshot.data!;
                  return _buildReportContent(report);
                } else {
                  return const Center(child: Text('لا توجد بيانات'));
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    final hasFilters = _startDate != null || 
                      _endDate != null || 
                      _selectedUserIds.isNotEmpty || 
                      _selectedDepartmentIds.isNotEmpty;

    if (!hasFilters) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(8.0),
      color: Colors.grey.shade100,
      child: Wrap(
        spacing: 8.0,
        children: [
          if (_startDate != null || _endDate != null)
            Chip(
              label: Text(_getPeriodText()),
              deleteIcon: const Icon(Icons.close, size: 18),
              onDeleted: () {
                setState(() {
                  _startDate = null;
                  _endDate = null;
                  _refreshReport();
                });
              },
            ),
          if (_selectedUserIds.isNotEmpty)
            Chip(
              label: Text('${_selectedUserIds.length} مستخدم محدد'),
              deleteIcon: const Icon(Icons.close, size: 18),
              onDeleted: () {
                setState(() {
                  _selectedUserIds.clear();
                  _refreshReport();
                });
              },
            ),
          if (_selectedDepartmentIds.isNotEmpty)
            Chip(
              label: Text('${_selectedDepartmentIds.length} قسم محدد'),
              deleteIcon: const Icon(Icons.close, size: 18),
              onDeleted: () {
                setState(() {
                  _selectedDepartmentIds.clear();
                  _refreshReport();
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildReportContent(WorkloadDistributionReport report) {
    return DefaultTabController(
      length: 4,
      child: Column(
        children: [
          Container(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: const TabBar(
              labelColor: Colors.black87,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
                Tab(text: 'المستخدمين', icon: Icon(Icons.people)),
                Tab(text: 'الأقسام', icon: Icon(Icons.business)),
                Tab(text: 'التوزيع الزمني', icon: Icon(Icons.timeline)),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildOverviewTab(report),
                _buildUsersTab(report),
                _buildDepartmentsTab(report),
                _buildTimeDistributionTab(report),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(WorkloadDistributionReport report) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة
          _buildStatisticsCards(report),
          const SizedBox(height: 24),

          // الرسم البياني الدائري لتوزيع المهام
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.pie_chart, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      const Text(
                        'توزيع المهام حسب الحالة',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    height: 300,
                    child: SfCircularChart(
                      title: ChartTitle(text: 'نسب حالات المهام'),
                      legend: Legend(
                        isVisible: true,
                        position: LegendPosition.bottom,
                      ),
                      tooltipBehavior: TooltipBehavior(enable: true),
                      series: <CircularSeries>[
                        PieSeries<WorkloadChartData, String>(
                          dataSource: _buildTaskStatusChartData(report),
                          xValueMapper: (WorkloadChartData data, _) => data.name,
                          yValueMapper: (WorkloadChartData data, _) => data.value,
                          pointColorMapper: (WorkloadChartData data, _) => data.color,
                          dataLabelSettings: const DataLabelSettings(
                            isVisible: true,
                            labelPosition: ChartDataLabelPosition.outside,
                          ),
                          explode: true,
                          explodeIndex: 0,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // أهم النتائج
          _buildKeyInsights(report),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards(WorkloadDistributionReport report) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المهام',
                report.totalTasks.toString(),
                Icons.task,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'المستخدمين النشطين',
                report.totalActiveUsers.toString(),
                Icons.people,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'متوسط المهام/مستخدم',
                report.averageWorkloadPerUser.toStringAsFixed(1),
                Icons.assignment,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'معدل الاستغلال العام',
                '${report.systemUtilization.toStringAsFixed(1)}%',
                Icons.speed,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<WorkloadChartData> _buildTaskStatusChartData(WorkloadDistributionReport report) {
    int totalCompleted = 0;
    int totalActive = 0;
    int totalOverdue = 0;
    int totalPending = 0;

    for (var workload in report.userWorkloads) {
      totalCompleted += workload.completedTasks;
      totalActive += workload.activeTasks;
      totalOverdue += workload.overdueTasks;
      totalPending += workload.pendingTasks;
    }

    return [
      WorkloadChartData(name: 'مكتملة', value: totalCompleted.toDouble(), color: Colors.green),
      WorkloadChartData(name: 'نشطة', value: totalActive.toDouble(), color: Colors.blue),
      WorkloadChartData(name: 'متأخرة', value: totalOverdue.toDouble(), color: Colors.red),
      WorkloadChartData(name: 'معلقة', value: totalPending.toDouble(), color: Colors.grey),
    ].where((data) => data.value > 0).toList();
  }

  Widget _buildKeyInsights(WorkloadDistributionReport report) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'أهم النتائج والتوصيات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (report.mostOverloadedUser != null) ...[
              _buildInsightItem(
                'أكثر المستخدمين انشغالاً',
                '${report.mostOverloadedUser!.user.name} - ${report.mostOverloadedUser!.totalTasks} مهمة',
                Icons.person,
                Colors.red,
              ),
            ],
            
            if (report.leastLoadedUser != null) ...[
              _buildInsightItem(
                'أقل المستخدمين انشغالاً',
                '${report.leastLoadedUser!.user.name} - ${report.leastLoadedUser!.totalTasks} مهمة',
                Icons.person_outline,
                Colors.green,
              ),
            ],
            
            if (report.busiestDepartment != null) ...[
              _buildInsightItem(
                'أكثر الأقسام انشغالاً',
                '${report.busiestDepartment!.department.name} - ${report.busiestDepartment!.totalTasks} مهمة',
                Icons.business,
                Colors.orange,
              ),
            ],
            
            _buildInsightItem(
              'توصية',
              _getRecommendation(report),
              Icons.recommend,
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightItem(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(description, style: const TextStyle(color: Colors.grey)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getRecommendation(WorkloadDistributionReport report) {
    if (report.systemUtilization > 80) {
      return 'النظام محمل بشكل مفرط، يُنصح بإعادة توزيع المهام أو زيادة الموارد البشرية.';
    } else if (report.systemUtilization < 30) {
      return 'النظام غير مستغل بكفاءة، يمكن توزيع مهام إضافية أو تحسين تخصيص الموارد.';
    } else {
      return 'توزيع عبء العمل متوازن نسبياً، يُنصح بمراقبة المستخدمين المحملين بشكل مفرط.';
    }
  }

  Widget _buildUsersTab(WorkloadDistributionReport report) {
    return Column(
      children: [
        // رسم بياني شريطي لتوزيع المهام بين المستخدمين
        Expanded(
          flex: 1,
          child: Card(
            margin: const EdgeInsets.all(16.0),
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.bar_chart, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      const Text(
                        'توزيع المهام بين المستخدمين',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SfCartesianChart(
                      primaryXAxis: CategoryAxis(
                        title: AxisTitle(text: 'المستخدمين'),
                        labelStyle: const TextStyle(fontSize: 10),
                        labelIntersectAction: AxisLabelIntersectAction.rotate45,
                      ),
                      primaryYAxis: NumericAxis(
                        title: AxisTitle(text: 'عدد المهام'),
                      ),
                      title: ChartTitle(text: 'عبء العمل لكل مستخدم'),
                      legend: Legend(isVisible: true, position: LegendPosition.bottom),
                      tooltipBehavior: TooltipBehavior(enable: true),
                      series: <CartesianSeries>[
                        StackedColumnSeries<UserWorkload, String>(
                          name: 'مكتملة',
                          dataSource: report.userWorkloads.take(10).toList(),
                          xValueMapper: (UserWorkload workload, _) => 
                            workload.user.name.length > 10 
                                ? '${workload.user.name.substring(0, 10)}...'
                                : workload.user.name,
                          yValueMapper: (UserWorkload workload, _) => workload.completedTasks,
                          color: Colors.green,
                        ),
                        StackedColumnSeries<UserWorkload, String>(
                          name: 'نشطة',
                          dataSource: report.userWorkloads.take(10).toList(),
                          xValueMapper: (UserWorkload workload, _) => 
                            workload.user.name.length > 10 
                                ? '${workload.user.name.substring(0, 10)}...'
                                : workload.user.name,
                          yValueMapper: (UserWorkload workload, _) => workload.activeTasks,
                          color: Colors.blue,
                        ),
                        StackedColumnSeries<UserWorkload, String>(
                          name: 'متأخرة',
                          dataSource: report.userWorkloads.take(10).toList(),
                          xValueMapper: (UserWorkload workload, _) => 
                            workload.user.name.length > 10 
                                ? '${workload.user.name.substring(0, 10)}...'
                                : workload.user.name,
                          yValueMapper: (UserWorkload workload, _) => workload.overdueTasks,
                          color: Colors.red,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // جدول تفصيلي للمستخدمين
        Expanded(
          flex: 1,
          child: _buildUsersTable(report),
        ),
      ],
    );
  }

  Widget _buildUsersTable(WorkloadDistributionReport report) {
    final columns = [
      PlutoColumn(
        title: 'المستخدم',
        field: 'user',
        type: PlutoColumnType.text(),
        width: 120,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'إجمالي المهام',
        field: 'totalTasks',
        type: PlutoColumnType.number(),
        width: 100,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'نشطة',
        field: 'activeTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'مكتملة',
        field: 'completedTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'متأخرة',
        field: 'overdueTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'معلقة',
        field: 'pendingTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'نسبة العبء (%)',
        field: 'workloadPercentage',
        type: PlutoColumnType.text(),
        width: 120,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'معدل الاستغلال (%)',
        field: 'utilizationRate',
        type: PlutoColumnType.text(),
        width: 130,
        enableEditingMode: false,
      ),
    ];

    final rows = report.userWorkloads.map((workload) {
      return PlutoRow(
        cells: {
          'user': PlutoCell(value: workload.user.name),
          'totalTasks': PlutoCell(value: workload.totalTasks),
          'activeTasks': PlutoCell(value: workload.activeTasks),
          'completedTasks': PlutoCell(value: workload.completedTasks),
          'overdueTasks': PlutoCell(value: workload.overdueTasks),
          'pendingTasks': PlutoCell(value: workload.pendingTasks),
          'workloadPercentage': PlutoCell(value: '${workload.workloadPercentage.toStringAsFixed(1)}%'),
          'utilizationRate': PlutoCell(value: '${workload.utilizationRate.toStringAsFixed(1)}%'),
        },
      );
    }).toList();

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل عبء العمل للمستخدمين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير Excel'),
                  onPressed: () => _exportUsersToExcel(report),
                ),
              ],
            ),
          ),
          Expanded(
            child: PlutoGrid(
              columns: columns,
              rows: rows,
              configuration: PlutoGridConfiguration(
                style: PlutoGridStyleConfig(
                  gridBorderColor: Colors.grey.shade300,
                  activatedBorderColor: Theme.of(context).primaryColor,
                  rowColor: Colors.white,
                  checkedColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                ),
                localeText: const PlutoGridLocaleText.arabic(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentsTab(WorkloadDistributionReport report) {
    return const Center(child: Text('تبويب الأقسام - قيد التطوير'));
  }

  Widget _buildTimeDistributionTab(WorkloadDistributionReport report) {
    return const Center(child: Text('تبويب التوزيع الزمني - قيد التطوير'));
  }

  void _showFilterDialog() {
    Get.snackbar('فلاتر', 'حوار الفلاتر - قيد التطوير');
  }

  void _exportUsersToExcel(WorkloadDistributionReport report) {
    Get.snackbar('تصدير', 'وظيفة تصدير Excel - قيد التطوير');
  }
}