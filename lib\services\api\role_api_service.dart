import 'package:flutter/foundation.dart';
import 'api_service.dart';
import '../../models/user_model.dart';
import '../../models/permission_models.dart';

/// خدمة API للأدوار - متطابقة مع ASP.NET Core API
/// يتعامل مع UserRole enum والصلاحيات بدلاً من نموذج Role منفصل
class RoleApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع أدوار المستخدمين المتاحة
  /// يرجع قائمة بـ UserRole enum values
  List<UserRole> getAllUserRoles() {
    return UserRole.values;
  }

  /// الحصول على معلومات دور مستخدم بواسطة القيمة
  UserRole? getUserRoleByValue(int value) {
    try {
      return UserRole.fromInt(value);
    } catch (e) {
      debugPrint('خطأ في الحصول على دور المستخدم: $e');
      return null;
    }
  }

  /// الحصول على اسم الدور للعرض
  String getRoleDisplayName(UserRole role) {
    return role.displayName;
  }

  /// التحقق من صلاحية دور معين
  bool isRoleAuthorized(UserRole userRole, List<UserRole> requiredRoles) {
    return requiredRoles.contains(userRole);
  }

  /// التحقق من كون المستخدم مدير أو أعلى
  bool isManagerOrAbove(UserRole role) {
    return [UserRole.manager, UserRole.admin, UserRole.superAdmin].contains(role);
  }

  /// التحقق من كون المستخدم مشرف أو أعلى
  bool isSupervisorOrAbove(UserRole role) {
    return [UserRole.supervisor, UserRole.manager, UserRole.admin, UserRole.superAdmin].contains(role);
  }

  /// التحقق من كون المستخدم مدير عام أو أعلى
  bool isAdminOrAbove(UserRole role) {
    return [UserRole.admin, UserRole.superAdmin].contains(role);
  }

  /// التحقق من كون المستخدم مدير النظام
  bool isSuperAdmin(UserRole role) {
    return role == UserRole.superAdmin;
  }

  // ===== طرق إدارة الصلاحيات =====

  /// الحصول على جميع الصلاحيات المتاحة
  Future<List<Permission>> getAllPermissions() async {
    try {
      final response = await _apiService.get('/api/Permissions');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحية بواسطة المعرف
  Future<Permission?> getPermissionById(int id) async {
    try {
      final response = await _apiService.get('/api/Permissions/$id');
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحية: $e');
      return null;
    }
  }

  /// البحث في الصلاحيات
  Future<List<Permission>> searchPermissions(String query) async {
    try {
      final response = await _apiService.get('/api/Permissions/search', queryParams: {
        'searchTerm': query,
      });
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن الصلاحيات: $e');
      return [];
    }
  }

  /// الحصول على الصلاحيات حسب المجموعة
  Future<List<Permission>> getPermissionsByGroup(String group) async {
    try {
      final response = await _apiService.get('/api/Permissions/group/$group');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المجموعة: $e');
      return [];
    }
  }

  // ===== طرق إدارة المستخدمين والأدوار =====

  /// تحديث دور مستخدم
  Future<bool> updateUserRole(int userId, UserRole newRole) async {
    try {
      final response = await _apiService.put(
        '/api/Users/<USER>',
        {'role': newRole.value},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين حسب الدور
  Future<List<User>> getUsersByRole(UserRole role) async {
    try {
      final response = await _apiService.get('/api/Users/<USER>/${role.value}');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين حسب الدور: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات الأدوار
  Future<Map<String, int>> getRoleStatistics() async {
    try {
      final users = await _getAllUsers();
      final statistics = <String, int>{};

      for (final role in UserRole.values) {
        final count = users.where((user) => user.roleId == role.value).length;
        statistics[role.displayName] = count;
      }

      return statistics;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأدوار: $e');
      return {};
    }
  }

  /// طريقة مساعدة للحصول على جميع المستخدمين
  Future<List<User>> _getAllUsers() async {
    try {
      final response = await _apiService.get('/api/Users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      return [];
    }
  }

  /// التحقق من صلاحية المستخدم لعملية معينة
  bool canUserPerformAction(UserRole userRole, String action) {
    switch (action.toLowerCase()) {
      case 'create_user':
      case 'edit_user':
      case 'delete_user':
        return isManagerOrAbove(userRole);
      case 'view_all_tasks':
        return isSupervisorOrAbove(userRole);
      case 'manage_system':
        return isAdminOrAbove(userRole);
      case 'super_admin_actions':
        return isSuperAdmin(userRole);
      default:
        return false;
    }
  }

  /// الحصول على قائمة الإجراءات المسموحة للدور
  List<String> getAllowedActionsForRole(UserRole role) {
    final actions = <String>[];

    // إجراءات أساسية لجميع المستخدمين
    actions.addAll(['view_own_tasks', 'edit_own_profile']);

    if (isSupervisorOrAbove(role)) {
      actions.addAll(['view_all_tasks', 'assign_tasks']);
    }

    if (isManagerOrAbove(role)) {
      actions.addAll(['create_user', 'edit_user', 'manage_department']);
    }

    if (isAdminOrAbove(role)) {
      actions.addAll(['delete_user', 'manage_system', 'view_reports']);
    }

    if (isSuperAdmin(role)) {
      actions.addAll(['super_admin_actions', 'manage_roles', 'system_backup']);
    }

    return actions;
  }
}
