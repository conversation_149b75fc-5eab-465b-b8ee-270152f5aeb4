import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../models/archive_models.dart';
import '../../utils/app_config.dart';
import 'api_service.dart';

/// خدمة API لمستندات الأرشيف - متطابقة مع ASP.NET Core API
class ArchiveDocumentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع مستندات الأرشيف
  Future<List<ArchiveDocument>> getAllDocuments() async {
    try {
      final response = await _apiService.get('/api/ArchiveDocuments');
      return _apiService.handleListResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على مستند أرشيف بواسطة المعرف
  Future<ArchiveDocument?> getDocumentById(int id) async {
    try {
      final response = await _apiService.get('/api/ArchiveDocuments/$id');
      return _apiService.handleResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستند الأرشيف: $e');
      return null;
    }
  }

  /// رفع مستند أرشيف جديد مع ملف
  Future<ArchiveDocument?> uploadDocument({
    required String title,
    required int categoryId,
    required File file,
    required String fileName,
    required String fileType,
    String? description,
    List<int>? tagIds,
    String? documentNumber,
    DateTime? documentDate,
    String? issuer,
    String? recipient,
    required int createdBy,
  }) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('${AppConfig.apiUrl}/api/ArchiveDocuments/upload'));

      // إضافة الهيدرز
      request.headers['Accept'] = 'application/json';
      if (_apiService.accessToken != null) {
        request.headers['Authorization'] = 'Bearer ${_apiService.accessToken}';
      }

      // إضافة الملف
      request.files.add(await http.MultipartFile.fromPath('file', file.path, filename: fileName));

      // إضافة البيانات
      request.fields['title'] = title;
      request.fields['categoryId'] = categoryId.toString();
      request.fields['createdBy'] = createdBy.toString();

      if (description != null) request.fields['description'] = description;
      if (documentNumber != null) request.fields['documentNumber'] = documentNumber;
      if (documentDate != null) request.fields['documentDate'] = documentDate.millisecondsSinceEpoch.toString();
      if (issuer != null) request.fields['issuer'] = issuer;
      if (recipient != null) request.fields['recipient'] = recipient;
      if (tagIds != null) request.fields['tagIds'] = tagIds.join(',');

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return _apiService.handleResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في رفع مستند الأرشيف: $e');
      rethrow;
    }
  }

  /// إنشاء مستند أرشيف جديد
  Future<ArchiveDocument?> createDocument(ArchiveDocument document) async {
    try {
      final response = await _apiService.post(
        '/api/ArchiveDocuments',
        document.toJson(),
      );
      return _apiService.handleResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء مستند الأرشيف: $e');
      rethrow;
    }
  }

  /// تحديث مستند أرشيف
  Future<ArchiveDocument?> updateDocument(ArchiveDocument document) async {
    try {
      final response = await _apiService.put(
        '/api/ArchiveDocuments/${document.id}',
        document.toJson(),
      );
      return _apiService.handleResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث مستند الأرشيف: $e');
      rethrow;
    }
  }

  /// حذف مستند أرشيف
  Future<bool> deleteDocument(int id) async {
    try {
      final response = await _apiService.delete('/api/ArchiveDocuments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف مستند الأرشيف: $e');
      return false;
    }
  }

  /// الحصول على المستندات بفئة معينة
  Future<List<ArchiveDocument>> getDocumentsByCategory(int categoryId) async {
    try {
      final response = await _apiService.get('/api/ArchiveDocuments/category/$categoryId');
      return _apiService.handleListResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات الفئة: $e');
      rethrow;
    }
  }

  /// البحث في مستندات الأرشيف
  Future<List<ArchiveDocument>> searchDocuments(String query) async {
    try {
      final response = await _apiService.get('/ArchiveDocuments/search?searchTerm=$query');
      return _apiService.handleListResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن مستندات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على المستندات بعلامة معينة
  Future<List<ArchiveDocument>> getDocumentsByTag(int tagId) async {
    try {
      final response = await _apiService.get('/ArchiveDocuments/tag/$tagId');
      return _apiService.handleListResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات العلامة: $e');
      rethrow;
    }
  }

  /// الحصول على المستندات في فترة زمنية
  Future<List<ArchiveDocument>> getDocumentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/ArchiveDocuments/date-range?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      return _apiService.handleListResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات الفترة الزمنية: $e');
      rethrow;
    }
  }

  /// أرشفة مستند
  Future<bool> archiveDocument(int documentId) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocuments/$documentId/archive',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في أرشفة المستند: $e');
      return false;
    }
  }

  /// إلغاء أرشفة مستند
  Future<bool> unarchiveDocument(int documentId) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocuments/$documentId/unarchive',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء أرشفة المستند: $e');
      return false;
    }
  }

  /// إضافة علامة لمستند
  Future<bool> addTagToDocument(int documentId, int tagId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocuments/$documentId/tags',
        {'tagId': tagId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إضافة علامة للمستند: $e');
      return false;
    }
  }

  /// إزالة علامة من مستند
  Future<bool> removeTagFromDocument(int documentId, int tagId) async {
    try {
      final response = await _apiService.delete('/ArchiveDocuments/$documentId/tags/$tagId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة علامة من المستند: $e');
      return false;
    }
  }

  /// الحصول على علامات مستند
  Future<List<Map<String, dynamic>>> getDocumentTags(int documentId) async {
    try {
      final response = await _apiService.get('/ArchiveDocuments/$documentId/tags');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على علامات المستند: $e');
      return [];
    }
  }

  /// تحديث فئة مستند
  Future<bool> updateDocumentCategory(int documentId, int categoryId) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocuments/$documentId/category',
        {'categoryId': categoryId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث فئة المستند: $e');
      return false;
    }
  }

  /// تحديث حالة نشاط المستند
  Future<bool> updateDocumentActiveStatus(int documentId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocuments/$documentId/active',
        {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة نشاط المستند: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المستندات
  Future<Map<String, dynamic>?> getDocumentStatistics() async {
    try {
      final response = await _apiService.get('/ArchiveDocuments/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستندات: $e');
      return null;
    }
  }

  /// تصدير مستندات الأرشيف
  Future<String?> exportDocuments(String format, List<int>? documentIds) async {
    try {
      var url = '/ArchiveDocuments/export?format=$format';
      if (documentIds != null && documentIds.isNotEmpty) {
        url += '&documentIds=${documentIds.join(',')}';
      }
      
      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير مستندات الأرشيف: $e');
      return null;
    }
  }

  /// استيراد مستندات الأرشيف
  Future<bool> importDocuments(String data, String format) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocuments/import',
        {
          'data': data,
          'format': format,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد مستندات الأرشيف: $e');
      return false;
    }
  }

  /// نسخ مستند
  Future<ArchiveDocument?> duplicateDocument(int documentId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveDocuments/$documentId/duplicate',
        {},
      );
      return _apiService.handleResponse<ArchiveDocument>(
        response,
        (json) => ArchiveDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في نسخ المستند: $e');
      return null;
    }
  }

  /// نقل مستندات إلى فئة أخرى
  Future<bool> moveDocuments(List<int> documentIds, int newCategoryId) async {
    try {
      final response = await _apiService.put(
        '/ArchiveDocuments/move',
        {
          'documentIds': documentIds,
          'newCategoryId': newCategoryId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المستندات: $e');
      return false;
    }
  }
}
