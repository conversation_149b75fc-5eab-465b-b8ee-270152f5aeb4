import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة تصدير PDF محسنة
///
/// توفر وظائف متقدمة لتصدير التقارير بتنسيق PDF مع دعم أفضل للغة العربية
class EnhancedPdfExportService {
  /// تصدير تقرير بتنسيق PDF
  ///
  /// [title] عنوان التقرير
  /// [data] بيانات التقرير (يمكن أن تكون Map أو List)
  /// [summary] ملخص التقرير (اختياري)
  /// [charts] الرسوم البيانية (اختياري)
  /// [fileName] اسم الملف (اختياري)
  Future<String?> exportToPdf({
    required String title,
    required dynamic data,
    Map<String, dynamic>? summary,
    List<pw.Widget>? charts,
    String? fileName,
  }) async {
    try {
      // تحميل الخط العربي مع نظام احتياطي محسن
      pw.Font arabicFont;
      pw.Font arabicBoldFont;
      
      try {
        // محاولة تحميل خط Cairo أولاً
        try {
          final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
          final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
          arabicFont = pw.Font.ttf(fontData);
          arabicBoldFont = pw.Font.ttf(boldFontData);
          debugPrint('✅ تم تحميل خط Cairo بنجاح');
        } catch (e) {
          // محاولة تحميل خط NotoSansArabic كبديل
          debugPrint('⚠️ لم يتم العثور على خط Cairo، محاولة تحميل NotoSansArabic...');
          final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
          final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
          arabicFont = pw.Font.ttf(fontData);
          arabicBoldFont = pw.Font.ttf(boldFontData);
          debugPrint('✅ تم تحميل خط NotoSansArabic بنجاح');
        }
      } catch (e) {
        debugPrint('❌ لم يتم العثور على أي خط عربي، استخدام الخط الافتراضي: $e');
        arabicFont = pw.Font.courier();
        arabicBoldFont = pw.Font.courier();
      }

      // إنشاء مستند PDF مع دعم الخطوط العربية
      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(
          base: arabicFont,
          bold: arabicBoldFont,
        ),
      );

      // إضافة صفحة الغلاف
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    title,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'تاريخ التقرير: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 14,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  if (summary != null) ...[
                    pw.SizedBox(height: 40),
                    pw.Text(
                      'ملخص التقرير',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.SizedBox(height: 10),
                    _buildSummarySection(summary, arabicFont),
                  ],
                ],
              ),
            );
          },
        ),
      );

      // إضافة صفحات البيانات
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'بيانات التقرير',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 20),
                  _buildDataSection(data is Map<String, dynamic> ? data : {'items': data}, arabicFont),
                ],
              ),
            );
          },
        ),
      );

      // إضافة صفحات الرسوم البيانية إذا كانت متوفرة
      if (charts != null && charts.isNotEmpty) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Padding(
                padding: const pw.EdgeInsets.all(20),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'الرسوم البيانية',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.SizedBox(height: 20),
                    ...charts,
                  ],
                ),
              );
            },
          ),
        );
      }

      // حفظ الملف
      final directory = await _getExportDirectory();
      final outputFileName = fileName ?? 'report_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final file = File('${directory.path}/$outputFileName');
      await file.writeAsBytes(await pdf.save());

      return file.path;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير بتنسيق PDF: $e');
      return null;
    }
  }

  /// بناء قسم الملخص
  pw.Widget _buildSummarySection(Map<String, dynamic> summary, pw.Font font) {
    final List<pw.Widget> summaryWidgets = [];

    summary.forEach((key, value) {
      summaryWidgets.add(
        pw.Padding(
          padding: const pw.EdgeInsets.symmetric(vertical: 4),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text(
                value.toString(),
                style: pw.TextStyle(font: font, fontSize: 12),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                '$key:',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ),
      );
    });

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: summaryWidgets,
    );
  }

  /// بناء قسم البيانات
  pw.Widget _buildDataSection(Map<String, dynamic> data, pw.Font font) {
    // تحويل البيانات إلى جدول إذا كانت مناسبة
    if (data.containsKey('rows') && data.containsKey('columns')) {
      return _buildDataTable(data, font);
    }

    // معالجة قائمة العناصر إذا كانت موجودة
    if (data.containsKey('items') && data['items'] is List) {
      final items = data['items'] as List;

      if (items.isEmpty) {
        return pw.Text(
          'لا توجد بيانات',
          style: pw.TextStyle(font: font, fontSize: 12),
          textDirection: pw.TextDirection.rtl,
        );
      }

      if (items.first is Map) {
        // إنشاء جدول من قائمة الكائنات
        return _buildListObjectsTable(items.cast<Map<dynamic, dynamic>>(), font);
      } else {
        // عرض قائمة العناصر البسيطة
        return _buildSimpleListData(items, font);
      }
    }

    // عرض البيانات كقائمة
    final List<pw.Widget> dataWidgets = [];

    data.forEach((key, value) {
      if (value is Map) {
        dataWidgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 10),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  key,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 5),
                _buildNestedData(value, font),
              ],
            ),
          ),
        );
      } else if (value is List) {
        dataWidgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 10),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  key,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 5),
                _buildListData(value, font),
              ],
            ),
          ),
        );
      } else {
        dataWidgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.symmetric(vertical: 4),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.end,
              children: [
                pw.Text(
                  value.toString(),
                  style: pw.TextStyle(font: font, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(width: 10),
                pw.Text(
                  '$key:',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ),
        );
      }
    });

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: dataWidgets,
    );
  }

  /// بناء جدول من قائمة الكائنات
  pw.Widget _buildListObjectsTable(List<Map<dynamic, dynamic>> items, pw.Font font) {
    // استخراج أسماء الأعمدة من المفاتيح في العنصر الأول
    final columns = items.first.keys.toList();

    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: columns.map((column) {
            return pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Text(
                column.toString(),
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
                textAlign: pw.TextAlign.center,
              ),
            );
          }).toList(),
        ),
        // صفوف البيانات
        ...items.map((item) {
          return pw.TableRow(
            children: columns.map((column) {
              return pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: pw.Text(
                  item[column].toString(),
                  style: pw.TextStyle(font: font, fontSize: 8),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.center,
                ),
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  /// بناء قائمة بيانات بسيطة
  pw.Widget _buildSimpleListData(List<dynamic> items, pw.Font font) {
    final List<pw.Widget> listWidgets = [];

    for (int i = 0; i < items.length; i++) {
      listWidgets.add(
        pw.Padding(
          padding: const pw.EdgeInsets.symmetric(vertical: 2),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text(
                items[i].toString(),
                style: pw.TextStyle(font: font, fontSize: 10),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                '${i + 1}.',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ),
      );
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: listWidgets,
    );
  }

  /// بناء بيانات متداخلة
  pw.Widget _buildNestedData(Map<dynamic, dynamic> data, pw.Font font) {
    final List<pw.Widget> nestedWidgets = [];

    data.forEach((key, value) {
      nestedWidgets.add(
        pw.Padding(
          padding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 10),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text(
                value.toString(),
                style: pw.TextStyle(font: font, fontSize: 10),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(width: 10),
              pw.Text(
                '$key:',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ),
      );
    });

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: nestedWidgets,
    );
  }

  /// بناء بيانات قائمة
  pw.Widget _buildListData(List<dynamic> data, pw.Font font) {
    final List<pw.Widget> listWidgets = [];

    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      if (item is Map) {
        listWidgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 10),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'العنصر ${i + 1}:',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                _buildNestedData(item.cast<dynamic, dynamic>(), font),
              ],
            ),
          ),
        );
      } else {
        listWidgets.add(
          pw.Padding(
            padding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 10),
            child: pw.Text(
              '${i + 1}. ${item.toString()}',
              style: pw.TextStyle(font: font, fontSize: 10),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        );
      }
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: listWidgets,
    );
  }

  /// بناء جدول البيانات
  pw.Widget _buildDataTable(Map<String, dynamic> data, pw.Font font) {
    final List<dynamic> columns = data['columns'];
    final List<dynamic> rows = data['rows'];

    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: columns.map((column) {
            return pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Text(
                column.toString(),
                style: pw.TextStyle(
                  font: font,
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                textDirection: pw.TextDirection.rtl,
                textAlign: pw.TextAlign.center,
              ),
            );
          }).toList(),
        ),
        // صفوف البيانات
        ...rows.map((row) {
          return pw.TableRow(
            children: row.map((cell) {
              return pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: pw.Text(
                  cell.toString(),
                  style: pw.TextStyle(font: font, fontSize: 8),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.center,
                ),
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  /// الحصول على مسار دليل التصدير
  Future<Directory> _getExportDirectory() async {
    Directory directory;

    try {
      // محاولة الحصول على دليل المستندات
      directory = await getApplicationDocumentsDirectory();

      // إنشاء مجلد التصدير إذا لم يكن موجودًا
      final exportDir = Directory('${directory.path}/exports');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      return exportDir;
    } catch (e) {
      debugPrint('خطأ في الحصول على دليل التصدير: $e');

      // استخدام دليل مؤقت كبديل
      directory = await getTemporaryDirectory();
      return directory;
    }
  }
}
