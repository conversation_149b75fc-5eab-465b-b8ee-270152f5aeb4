import 'user_model.dart';
import 'message_models.dart';
import 'group_member_models.dart';

/// نموذج مجموعة الدردشة - متطابق مع ASP.NET Core API
class ChatGroup {
  final int id;
  final String name;
  final String? description;
  final bool isDirectMessage; // متطابق مع API
  final bool isPrivate; // للتوافق مع الشاشات
  final int createdBy;
  final int createdAt; // long في API
  final int? updatedAt; // long في API
  final bool isDeleted;
  final String? avatarUrl; // للتوافق مع الشاشات
  final String groupType; // نوع المجموعة
  final int? maxMembers; // الحد الأقصى للأعضاء
  final bool isArchived; // هل المجموعة مؤرشفة
  final String? imageUrl; // صورة المجموعة

  // Navigation properties
  final User? createdByNavigation;
  final List<GroupMember>? groupMembers;
  final List<Message>? messages;

  const ChatGroup({
    required this.id,
    required this.name,
    this.description,
    this.isDirectMessage = false,
    this.isPrivate = false,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.avatarUrl,
    this.groupType = 'general',
    this.maxMembers,
    this.isArchived = false,
    this.imageUrl,
    this.createdByNavigation,
    this.groupMembers,
    this.messages,
  });

  factory ChatGroup.fromJson(Map<String, dynamic> json) {
    return ChatGroup(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      isDirectMessage: json['isDirectMessage'] as bool? ?? false,
      isPrivate: json['isPrivate'] as bool? ?? json['isDirectMessage'] as bool? ?? false,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      avatarUrl: json['avatarUrl'] as String?,
      groupType: json['groupType'] as String? ?? 'general',
      maxMembers: json['maxMembers'] as int?,
      isArchived: json['isArchived'] as bool? ?? false,
      imageUrl: json['imageUrl'] as String?,
      createdByNavigation: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      groupMembers: json['groupMembers'] != null
          ? (json['groupMembers'] as List)
              .map((m) => GroupMember.fromJson(m as Map<String, dynamic>))
              .toList()
          : null,
      messages: json['messages'] != null
          ? (json['messages'] as List)
              .map((m) => Message.fromJson(m as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isDirectMessage': isDirectMessage,
      'isPrivate': isPrivate,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'avatarUrl': avatarUrl,
      'groupType': groupType,
      'maxMembers': maxMembers,
      'isArchived': isArchived,
      'imageUrl': imageUrl,
    };
  }

  ChatGroup copyWith({
    int? id,
    String? name,
    String? description,
    bool? isDirectMessage,
    bool? isPrivate,
    int? createdBy,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    String? avatarUrl,
    String? groupType,
    int? maxMembers,
    bool? isArchived,
    String? imageUrl,
    User? createdByNavigation,
    List<GroupMember>? groupMembers,
    List<Message>? messages,
  }) {
    return ChatGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isDirectMessage: isDirectMessage ?? this.isDirectMessage,
      isPrivate: isPrivate ?? this.isPrivate,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      groupType: groupType ?? this.groupType,
      maxMembers: maxMembers ?? this.maxMembers,
      isArchived: isArchived ?? this.isArchived,
      imageUrl: imageUrl ?? this.imageUrl,
      createdByNavigation: createdByNavigation ?? this.createdByNavigation,
      groupMembers: groupMembers ?? this.groupMembers,
      messages: messages ?? this.messages,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على عدد الأعضاء النشطين
  int get activeMemberCount => groupMembers?.where((m) => !m.isDeleted && m.leftAt == null).length ?? 0;

  /// الحصول على عدد الأعضاء (للتوافق مع الشاشات)
  int get memberCount => activeMemberCount;

  /// الحصول على عدد الرسائل
  int get messageCount => messages?.where((m) => !m.isDeleted).length ?? 0;

  /// التحقق من كون المجموعة محادثة مباشرة
  bool get isPrivateChat => isDirectMessage;

  /// التحقق من كون المجموعة مجموعة عامة
  bool get isGroupChat => !isDirectMessage;

  /// الحصول على آخر رسالة
  Message? get lastMessage {
    if (messages == null || messages!.isEmpty) return null;
    var sortedMessages = messages!.where((m) => !m.isDeleted).toList();
    sortedMessages.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedMessages.isNotEmpty ? sortedMessages.first : null;
  }

  /// الحصول على المديرين
  List<GroupMember> get admins => groupMembers?.where((m) => m.isAdmin && m.isActive).toList() ?? [];

  /// الحصول على المشرفين
  List<GroupMember> get moderators => groupMembers?.where((m) => m.isModerator && m.isActive).toList() ?? [];

  /// التحقق من كون المستخدم عضو في المجموعة
  bool isMember(int userId) {
    return groupMembers?.any((m) => m.userId == userId && m.isActive) ?? false;
  }

  /// التحقق من كون المستخدم مدير أو مشرف
  bool hasAdminPermissions(int userId) {
    var member = groupMembers?.firstWhere(
      (m) => m.userId == userId && m.isActive,
      orElse: () => throw StateError('User not found'),
    );
    return member?.hasAdminPermissions ?? false;
  }

  @override
  String toString() {
    return 'ChatGroup(id: $id, name: $name, memberCount: $activeMemberCount, isDirectMessage: $isDirectMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatGroup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مجموعة دردشة
class CreateChatGroupRequest {
  final String name;
  final String? description;
  final bool isDirectMessage;
  final bool isPrivate;
  final String groupType;
  final int? maxMembers;
  final List<int>? initialMemberIds;

  const CreateChatGroupRequest({
    required this.name,
    this.description,
    this.isDirectMessage = false,
    this.isPrivate = false,
    this.groupType = 'general',
    this.maxMembers,
    this.initialMemberIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'isDirectMessage': isDirectMessage,
      'isPrivate': isPrivate,
      'groupType': groupType,
      'maxMembers': maxMembers,
      'initialMemberIds': initialMemberIds,
    };
  }
}

/// نموذج طلب تحديث مجموعة دردشة
class UpdateChatGroupRequest {
  final int groupId;
  final String? name;
  final String? description;

  const UpdateChatGroupRequest({
    required this.groupId,
    this.name,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'groupId': groupId,
      'name': name,
      'description': description,
    };
  }
}

/// إحصائيات مجموعة الدردشة
class ChatGroupStatistics {
  final int groupId;
  final int totalMembers;
  final int activeMembers;
  final int totalMessages;
  final int todayMessages;
  final int weekMessages;
  final int monthMessages;
  final DateTime? lastMessageDate;
  final DateTime? lastActivityDate;
  final User? mostActiveUser;

  const ChatGroupStatistics({
    required this.groupId,
    required this.totalMembers,
    required this.activeMembers,
    required this.totalMessages,
    required this.todayMessages,
    required this.weekMessages,
    required this.monthMessages,
    this.lastMessageDate,
    this.lastActivityDate,
    this.mostActiveUser,
  });

  factory ChatGroupStatistics.fromJson(Map<String, dynamic> json) {
    return ChatGroupStatistics(
      groupId: json['groupId'] as int,
      totalMembers: json['totalMembers'] as int,
      activeMembers: json['activeMembers'] as int,
      totalMessages: json['totalMessages'] as int,
      todayMessages: json['todayMessages'] as int,
      weekMessages: json['weekMessages'] as int,
      monthMessages: json['monthMessages'] as int,
      lastMessageDate: json['lastMessageDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastMessageDate'] as int) * 1000)
          : null,
      lastActivityDate: json['lastActivityDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastActivityDate'] as int) * 1000)
          : null,
      mostActiveUser: json['mostActiveUser'] != null
          ? User.fromJson(json['mostActiveUser'] as Map<String, dynamic>)
          : null,
    );
  }

  /// نسبة النشاط اليومي
  double get dailyActivityRate => totalMessages > 0 ? (todayMessages / totalMessages) * 100 : 0;

  /// متوسط الرسائل لكل عضو
  double get averageMessagesPerMember => activeMembers > 0 ? totalMessages / activeMembers : 0;

  @override
  String toString() {
    return 'ChatGroupStatistics(groupId: $groupId, members: $activeMembers, messages: $totalMessages)';
  }
}

/// نموذج استجابة مجموعة الدردشة
class ChatGroupResponse {
  final bool success;
  final String? message;
  final ChatGroup? group;
  final ChatGroupStatistics? statistics;
  final String? error;

  const ChatGroupResponse({
    required this.success,
    this.message,
    this.group,
    this.statistics,
    this.error,
  });

  factory ChatGroupResponse.fromJson(Map<String, dynamic> json) {
    return ChatGroupResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      group: json['group'] != null
          ? ChatGroup.fromJson(json['group'] as Map<String, dynamic>)
          : null,
      statistics: json['statistics'] != null
          ? ChatGroupStatistics.fromJson(json['statistics'] as Map<String, dynamic>)
          : null,
      error: json['error'] as String?,
    );
  }
}
