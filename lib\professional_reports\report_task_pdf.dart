import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'report_task_model.dart';

/// دالة توليد تقرير PDF شامل من ReportTaskModel
Future<pw.Document> generateReportTaskPdf(ReportTaskModel reportTask, {String? username}) async {
  final pdf = pw.Document();

  // تحميل الخطوط العربية مع نظام احتياطي
  pw.Font arabicFont;
  pw.Font boldFont;
  
  try {
    // محاولة تحميل خط Cairo أولاً
    try {
      final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
      final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      arabicFont = pw.Font.ttf(fontData);
      boldFont = pw.Font.ttf(boldFontData);
      print('✅ تم تحميل خط Cairo بنجاح');
    } catch (e) {
      // محاولة تحميل خط NotoSansArabic كبديل
      print('⚠️ لم يتم العثور على خط Cairo، محاولة تحميل NotoSansArabic...');
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
      arabicFont = pw.Font.ttf(fontData);
      boldFont = pw.Font.ttf(boldFontData);
      print('✅ تم تحميل خط NotoSansArabic بنجاح');
    }
  } catch (e) {
    print('❌ لم يتم العثور على أي خط عربي، استخدام الخط الافتراضي: $e');
    arabicFont = pw.Font.courier();
    boldFont = pw.Font.courier();
  }

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      header: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('تقرير المهمة', style: pw.TextStyle(font: boldFont, fontSize: 22, color: PdfColors.teal800)),
            pw.SizedBox(height: 4),
            pw.Text('اسم المستخدم: ${username ?? "غير محدد"}', style: pw.TextStyle(font: arabicFont, fontSize: 10)),
            pw.Text('تاريخ الطباعة: ${_formatDateTime(DateTime.now())}', style: pw.TextStyle(font: arabicFont, fontSize: 10)),
          ],
        );
      },
      build: (pw.Context context) {
        return [
          pw.Text('بيانات المهمة الأساسية', style: pw.TextStyle(font: boldFont, fontSize: 16, color: PdfColors.teal800)),
          pw.SizedBox(height: 8),
          pw.Table.fromTextArray(
            headers: ['العنصر', 'القيمة'],
            data: [
              ['العنوان', reportTask.title],
              ['الوصف', reportTask.description ?? '-'],
              ['الحالة', reportTask.status],
              ['الأولوية', reportTask.priority],
              ['النسبة %', reportTask.completionPercentage.toString()],
              ['تاريخ الإنشاء', _formatDate(reportTask.createdAt)],
              ['تاريخ البداية', reportTask.startDate != null ? _formatDate(reportTask.startDate!) : '-'],
              ['تاريخ الاستحقاق', reportTask.dueDate != null ? _formatDate(reportTask.dueDate!) : '-'],
              ['تاريخ الإنجاز', reportTask.completedAt != null ? _formatDate(reportTask.completedAt!) : '-'],
              ['المنشئ', reportTask.creatorName ?? '-'],
              ['المسؤول', reportTask.assigneeName ?? '-'],
              ['القسم', reportTask.departmentName ?? '-'],
              ['نوع المهمة', reportTask.taskTypeName ?? '-'],
            ],
            cellStyle: pw.TextStyle(font: arabicFont, fontSize: 12),
            headerStyle: pw.TextStyle(font: boldFont, fontSize: 13),
            cellAlignment: pw.Alignment.centerRight,
            headerDecoration: const pw.BoxDecoration(color: PdfColors.green100),
            rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
            oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey100),
          ),
          pw.SizedBox(height: 18),
          // المساهمون
          pw.Text('المساهمون', style: pw.TextStyle(font: boldFont, fontSize: 15, color: PdfColors.teal800)),
          reportTask.contributors.isNotEmpty
              ? pw.Column(
                  children: reportTask.contributors.map((name) => pw.Bullet(text: name, style: pw.TextStyle(font: arabicFont, fontSize: 12))).toList(),
                )
              : pw.Text('لا يوجد مساهمون', style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey600)),
          pw.SizedBox(height: 18),
          // المهام الفرعية
          pw.Text('المهام الفرعية', style: pw.TextStyle(font: boldFont, fontSize: 15, color: PdfColors.teal800)),
          reportTask.subtasks.isNotEmpty
              ? pw.Column(
                  children: reportTask.subtasks.map((s) => pw.Bullet(text: s.title + (s.isCompleted ? ' (منجزة)' : ''), style: pw.TextStyle(font: arabicFont, fontSize: 12))).toList(),
                )
              : pw.Text('لا توجد مهام فرعية', style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey600)),
          pw.SizedBox(height: 18),
          // التعليقات
          pw.Text('التعليقات', style: pw.TextStyle(font: boldFont, fontSize: 15, color: PdfColors.teal800)),
          reportTask.comments.isNotEmpty
              ? pw.Column(
                  children: reportTask.comments.map((c) => pw.Bullet(text: (c.userName != null ? '[${c.userName}] ' : '') + c.text, style: pw.TextStyle(font: arabicFont, fontSize: 12))).toList(),
                )
              : pw.Text('لا توجد تعليقات', style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey600)),
          pw.SizedBox(height: 18),
          // المرفقات
          pw.Text('المرفقات', style: pw.TextStyle(font: boldFont, fontSize: 15, color: PdfColors.teal800)),
          reportTask.attachments.isNotEmpty
              ? pw.Column(
                  children: reportTask.attachments.map((a) => pw.Bullet(text: a.fileName, style: pw.TextStyle(font: arabicFont, fontSize: 12))).toList(),
                )
              : pw.Text('لا توجد مرفقات', style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey600)),
        ];
      },
    ),
  );
  return pdf;
}

String _formatDate(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
}
String _formatDateTime(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}  ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
}
