import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'quill_error_handler.dart';

/// مدير البحث والاستبدال لمحرر Quill
/// يوفر وظائف متقدمة للبحث والاستبدال في النصوص
class QuillSearchManager {
  final QuillController controller;
  final BuildContext context;

  QuillSearchManager({
    required this.controller,
    required this.context,
  });

  /// عرض حوار البحث والاستبدال
  Future<void> showSearchDialog() async {
    final searchController = TextEditingController();
    final replaceController = TextEditingController();
    bool caseSensitive = false;
    bool wholeWord = false;
    int currentMatchIndex = -1;
    List<int> matchPositions = [];

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('بحث واستبدال'),
          content: SizedBox(
            width: 500,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // حقل البحث
                TextField(
                  controller: searchController,
                  decoration: const InputDecoration(
                    labelText: 'البحث عن',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  autofocus: true,
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      matchPositions = _findAllMatches(value, caseSensitive, wholeWord);
                      setState(() {});
                    } else {
                      matchPositions.clear();
                      currentMatchIndex = -1;
                      setState(() {});
                    }
                  },
                ),

                const SizedBox(height: 16),

                // حقل الاستبدال
                TextField(
                  controller: replaceController,
                  decoration: const InputDecoration(
                    labelText: 'استبدال بـ',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.find_replace),
                  ),
                ),

                const SizedBox(height: 16),

                // خيارات البحث
                Column(
                  children: [
                    CheckboxListTile(
                      title: const Text('مطابقة حالة الأحرف'),
                      value: caseSensitive,
                      onChanged: (value) {
                        caseSensitive = value ?? false;
                        if (searchController.text.isNotEmpty) {
                          matchPositions = _findAllMatches(
                            searchController.text, 
                            caseSensitive, 
                            wholeWord
                          );
                        }
                        setState(() {});
                      },
                      dense: true,
                    ),
                    CheckboxListTile(
                      title: const Text('كلمة كاملة فقط'),
                      value: wholeWord,
                      onChanged: (value) {
                        wholeWord = value ?? false;
                        if (searchController.text.isNotEmpty) {
                          matchPositions = _findAllMatches(
                            searchController.text, 
                            caseSensitive, 
                            wholeWord
                          );
                        }
                        setState(() {});
                      },
                      dense: true,
                    ),
                  ],
                ),

                // عرض نتائج البحث
                if (matchPositions.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'تم العثور على ${matchPositions.length} نتيجة',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
            if (matchPositions.isNotEmpty) ...[
              TextButton(
                onPressed: () {
                  _findPrevious(matchPositions, currentMatchIndex, setState);
                },
                child: const Text('السابق'),
              ),
              TextButton(
                onPressed: () {
                  currentMatchIndex = _findNext(matchPositions, currentMatchIndex);
                  setState(() {});
                },
                child: const Text('التالي'),
              ),
              TextButton(
                onPressed: () {
                  if (currentMatchIndex >= 0 && currentMatchIndex < matchPositions.length) {
                    _replaceOne(
                      searchController.text,
                      replaceController.text,
                      matchPositions[currentMatchIndex],
                      caseSensitive,
                      wholeWord,
                    );
                    // إعادة البحث بعد الاستبدال
                    matchPositions = _findAllMatches(
                      searchController.text, 
                      caseSensitive, 
                      wholeWord
                    );
                    if (matchPositions.isEmpty) {
                      currentMatchIndex = -1;
                    } else if (currentMatchIndex >= matchPositions.length) {
                      currentMatchIndex = matchPositions.length - 1;
                    }
                    setState(() {});
                  }
                },
                child: const Text('استبدال'),
              ),
              ElevatedButton(
                onPressed: () {
                  final count = _replaceAll(
                    searchController.text,
                    replaceController.text,
                    caseSensitive,
                    wholeWord,
                  );
                  Navigator.of(context).pop();
                  QuillErrorHandler.showSuccess(
                    context,
                    'تم استبدال $count تطابق',
                  );
                },
                child: const Text('استبدال الكل'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// البحث عن جميع التطابقات
  List<int> _findAllMatches(String searchText, bool caseSensitive, bool wholeWord) {
    try {
      final plainText = controller.document.toPlainText();
      final searchIn = caseSensitive ? plainText : plainText.toLowerCase();
      final searchFor = caseSensitive ? searchText : searchText.toLowerCase();
      
      final List<int> positions = [];
      int startIndex = 0;
      
      while (true) {
        int index = searchIn.indexOf(searchFor, startIndex);
        if (index == -1) break;
        
        // التحقق من الكلمة الكاملة إذا كان مطلوباً
        if (wholeWord) {
          bool isWordStart = index == 0 || !_isWordCharacter(searchIn[index - 1]);
          bool isWordEnd = index + searchFor.length >= searchIn.length || 
                          !_isWordCharacter(searchIn[index + searchFor.length]);
          
          if (isWordStart && isWordEnd) {
            positions.add(index);
          }
        } else {
          positions.add(index);
        }
        
        startIndex = index + 1;
      }
      
      return positions;
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
      return [];
    }
  }

  /// التحقق من أن الحرف جزء من كلمة
  bool _isWordCharacter(String char) {
    return RegExp(r'[a-zA-Z0-9\u0600-\u06FF]').hasMatch(char);
  }

  /// البحث عن التطابق التالي
  int _findNext(List<int> positions, int currentIndex) {
    if (positions.isEmpty) return -1;
    
    int nextIndex = currentIndex + 1;
    if (nextIndex >= positions.length) {
      nextIndex = 0; // العودة إلى البداية
    }
    
    // تحديد النص المطابق
    _highlightMatch(positions[nextIndex]);
    
    return nextIndex;
  }

  /// البحث عن التطابق السابق
  void _findPrevious(List<int> positions, int currentIndex, StateSetter setState) {
    if (positions.isEmpty) return;
    
    int prevIndex = currentIndex - 1;
    if (prevIndex < 0) {
      prevIndex = positions.length - 1; // الذهاب إلى النهاية
    }
    
    // تحديد النص المطابق
    _highlightMatch(positions[prevIndex]);
    
    setState(() {
      currentIndex = prevIndex;
    });
  }

  /// تمييز التطابق
  void _highlightMatch(int position) {
    try {
      controller.updateSelection(
        TextSelection(baseOffset: position, extentOffset: position),
        ChangeSource.local,
      );
      
      // التمرير إلى الموضع
      _scrollToPosition(position);
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
    }
  }

  /// التمرير إلى موضع معين
  void _scrollToPosition(int position) {
    try {
      // محاولة تقدير موضع التمرير بناءً على موضع النص
      // هذا تقدير تقريبي يمكن تحسينه لاحقاً
      final totalText = controller.document.toPlainText();
      final textBeforePosition = totalText.substring(0, position.clamp(0, totalText.length));
      final lines = textBeforePosition.split('\n').length;

      // تقدير ارتفاع السطر (تقريبي)
      const estimatedLineHeight = 20.0;
      final estimatedScrollOffset = lines * estimatedLineHeight;

      // محاولة التمرير (إذا كان هناك scroll controller متاح)
      // هذا يحتاج إلى تمرير scroll controller من المحرر الرئيسي
      debugPrint('تم تقدير موضع التمرير: $estimatedScrollOffset للموضع $position');
    } catch (e) {
      debugPrint('خطأ في التمرير إلى الموضع: $e');
    }
  }

  /// استبدال تطابق واحد
  void _replaceOne(String searchText, String replaceText, int position, 
                   bool caseSensitive, bool wholeWord) {
    try {
      controller.document.delete(position, searchText.length);
      controller.document.insert(position, replaceText);
      
      QuillErrorHandler.showSuccess(context, 'تم الاستبدال');
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
    }
  }

  /// استبدال جميع التطابقات
  int _replaceAll(String searchText, String replaceText, bool caseSensitive, bool wholeWord) {
    try {
      final plainText = controller.document.toPlainText();
      int count = 0;
      String newText = plainText;

      if (wholeWord) {
        // استبدال الكلمات الكاملة فقط
        final pattern = caseSensitive 
            ? '\\b${RegExp.escape(searchText)}\\b'
            : '(?i)\\b${RegExp.escape(searchText)}\\b';
        final regex = RegExp(pattern);
        final matches = regex.allMatches(plainText);
        count = matches.length;
        newText = plainText.replaceAllMapped(regex, (match) => replaceText);
      } else {
        if (caseSensitive) {
          newText = newText.replaceAll(searchText, replaceText);
          count = searchText.allMatches(plainText).length;
        } else {
          final regex = RegExp(RegExp.escape(searchText), caseSensitive: false);
          final matches = regex.allMatches(plainText);
          count = matches.length;
          newText = plainText.replaceAllMapped(regex, (match) => replaceText);
        }
      }

      if (count > 0) {
        // استبدال المحتوى بالكامل
        controller.document.delete(0, plainText.length);
        controller.document.insert(0, newText);
      }

      return count;
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
      return 0;
    }
  }

  /// بحث سريع (بدون حوار)
  bool quickFind(String searchText, {bool caseSensitive = false}) {
    try {
      final positions = _findAllMatches(searchText, caseSensitive, false);
      if (positions.isNotEmpty) {
        _highlightMatch(positions[0]);
        return true;
      }
      return false;
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
      return false;
    }
  }

  /// استبدال سريع (بدون حوار)
  int quickReplace(String searchText, String replaceText, {bool caseSensitive = false}) {
    try {
      return _replaceAll(searchText, replaceText, caseSensitive, false);
    } catch (e) {
      QuillErrorHandler.handleSearchError(context, e);
      return 0;
    }
  }
}
