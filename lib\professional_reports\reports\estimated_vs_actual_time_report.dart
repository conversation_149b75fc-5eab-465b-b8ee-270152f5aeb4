import 'package:flutter_application_2/models/task_model.dart';

class EstimatedVsActualTimeReport {
  final List<TaskTimeComparison> comparisons;

  EstimatedVsActualTimeReport({
    required this.comparisons,
  });

  factory EstimatedVsActualTimeReport.fromTasks(List<Task> tasks) {
    final comparisons = tasks.map((task) {
      // نفترض أن لديك حقلين في نموذج المهمة:
      // estimatedTime (الوقت المقدر بالساعات)
      // actualTime (الوقت الفعلي المستغرق بالساعات)
      // يجب عليك تعديل هذا الجزء بناءً على نموذج البيانات الفعلي لديك
      final estimatedTime = task.estimatedTime ?? 0.0; // قيمة افتراضية
      final actualTime = task.actualTime ?? 0.0;       // قيمة افتراضية
      final difference = actualTime - estimatedTime;
      final percentageDifference = estimatedTime != 0
          ? (difference / estimatedTime) * 100
          : 0.0;

      return TaskTimeComparison(
        taskTitle: task.title,
        estimatedTime: estimatedTime.toDouble(),
        actualTime: actualTime.toDouble(),
        difference: difference.toDouble(),
        percentageDifference: percentageDifference,
      );
    }).toList();

    return EstimatedVsActualTimeReport(comparisons: comparisons);
  }
}

class TaskTimeComparison {
  final String taskTitle;
  final double estimatedTime;
  final double actualTime;
  final double difference;
  final double percentageDifference;

  TaskTimeComparison({
    required this.taskTitle,
    required this.estimatedTime,
    required this.actualTime,
    required this.difference,
    required this.percentageDifference,
  });
}