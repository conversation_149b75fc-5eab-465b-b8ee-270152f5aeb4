import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../utils/responsive_helper.dart';
import '../../models/archive_models.dart';
import '../../controllers/archive_documents_controller.dart';
import '../../controllers/archive_categories_controller.dart';
import '../../controllers/archive_tags_controller.dart';
import '../../services/unified_permission_service.dart';
import '../widgets/custom_app_bar.dart';

import 'document_detail_screen.dart';

/// تعداد مستوى السرية للوثيقة
enum ArchiveDocumentConfidentiality {
  normal,
  confidential,
  highlyConfidential,
  topSecret,
}

/// تعداد مستوى الأهمية للوثيقة
enum ArchiveDocumentImportance {
  low,
  normal,
  high,
  urgent,
}

/// شاشة رفع وثيقة جديدة إلى الأرشيف
class DocumentUploadScreen extends StatefulWidget {
  const DocumentUploadScreen({super.key});

  @override
  State<DocumentUploadScreen> createState() => _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends State<DocumentUploadScreen> {
  final ArchiveDocumentsController _documentsController = Get.find<ArchiveDocumentsController>();
  final ArchiveCategoriesController _categoriesController = Get.find<ArchiveCategoriesController>();
  final ArchiveTagsController _tagsController = Get.find<ArchiveTagsController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // بيانات الوثيقة
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _documentNumberController = TextEditingController();
  final TextEditingController _issuerController = TextEditingController();
  final TextEditingController _recipientController = TextEditingController();

  ArchiveCategory? _selectedCategory;
  final List<ArchiveTag> _selectedTags = [];
  DateTime? _documentDate;
  ArchiveDocumentConfidentiality _confidentiality = ArchiveDocumentConfidentiality.normal;
  ArchiveDocumentImportance _importance = ArchiveDocumentImportance.normal;

  // بيانات الملف
  File? _file;
  String _fileName = '';
  String _fileType = '';
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    // تحميل التصنيفات والوسوم
    _categoriesController.loadAllCategories();
    _tagsController.loadAllTags();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _documentNumberController.dispose();
    _issuerController.dispose();
    _recipientController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        setState(() {
          if (file.path != null) {
            _file = File(file.path!);
          }
          _fileName = file.name;
          _fileType = file.extension ?? '';

          // اقتراح عنوان الوثيقة من اسم الملف
          if (_titleController.text.isEmpty) {
            _titleController.text = _fileName.split('.').first;
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الملف: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    }
  }

  Future<void> _uploadDocument() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_file == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار ملف للرفع',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedCategory == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار تصنيف للوثيقة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      final document = await _documentsController.uploadDocument(
        title: _titleController.text,
        categoryId: _selectedCategory!.id,
        file: _file as File,
        fileName: _fileName,
        fileType: _fileType,
        description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
        tagIds: _selectedTags.isEmpty ? null : _selectedTags.map((tag) => tag.id).toList(),
        documentNumber: _documentNumberController.text.isEmpty ? null : _documentNumberController.text,
        documentDate: _documentDate,
        issuer: _issuerController.text.isEmpty ? null : _issuerController.text,
        recipient: _recipientController.text.isEmpty ? null : _recipientController.text,
        confidentiality: _confidentiality,
        importance: _importance,
      );

      if (document != null) {
        Get.back(); // العودة إلى الشاشة السابقة

        Get.snackbar(
          'تم الرفع',
          'تم رفع الوثيقة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withAlpha(178),
          colorText: Colors.white,
        );

        // الانتقال إلى شاشة تفاصيل الوثيقة
        Get.to(() => DocumentDetailScreen(document: document));
      } else {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء رفع الوثيقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('خطأ في رفع الوثيقة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء رفع الوثيقة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'رفع وثيقة جديدة',
      ),
      body: _isUploading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'جاري رفع الوثيقة...',
                    style: AppStyles.subtitle1,
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: isSmallScreen
                    ? _buildFormContent()
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // منطقة رفع الملف
                          Expanded(
                            flex: 2,
                            child: _buildFileUploadArea(),
                          ),
                          const SizedBox(width: 24),
                          // نموذج البيانات
                          Expanded(
                            flex: 3,
                            child: _buildFormContent(showFileUpload: false),
                          ),
                        ],
                      ),
              ),
            ),
      bottomNavigationBar: BottomAppBar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text('إلغاء'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: (_isUploading || !_permissionService.canUploadToArchive()) ? null : _uploadDocument,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('رفع الوثيقة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormContent({bool showFileUpload = true}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showFileUpload) _buildFileUploadArea(),
        if (showFileUpload) const SizedBox(height: 24),

        // معلومات الوثيقة الأساسية
        Text(
          'معلومات الوثيقة',
          style: AppStyles.headline6,
        ),
        const SizedBox(height: 16),

        // عنوان الوثيقة
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'عنوان الوثيقة *',
            hintText: 'أدخل عنوان الوثيقة',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى إدخال عنوان الوثيقة';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // وصف الوثيقة
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف الوثيقة',
            hintText: 'أدخل وصف الوثيقة',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        // التصنيف
        GetBuilder<ArchiveCategoriesController>(
          builder: (controller) => DropdownButtonFormField<ArchiveCategory>(
            decoration: const InputDecoration(
              labelText: 'التصنيف',
              border: OutlineInputBorder(),
            ),
            value: _selectedCategory,
            items: controller.allCategories.map((category) {
              return DropdownMenuItem<ArchiveCategory>(
                value: category,
                child: Text(category.name),
              );
            }).toList(),
            onChanged: (category) {
              setState(() {
                _selectedCategory = category;
              });
            },
          ),
        ),
        const SizedBox(height: 16),

        // الوسوم - مبسط
        GetBuilder<ArchiveTagsController>(
          builder: (controller) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('الوسوم'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: controller.allTags.map((tag) {
                  final isSelected = _selectedTags.contains(tag);
                  return FilterChip(
                    label: Text(tag.name),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedTags.add(tag);
                        } else {
                          _selectedTags.remove(tag);
                        }
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // معلومات إضافية
        Text(
          'معلومات إضافية',
          style: AppStyles.headline6,
        ),
        const SizedBox(height: 16),

        // رقم الوثيقة
        TextFormField(
          controller: _documentNumberController,
          decoration: const InputDecoration(
            labelText: 'رقم الوثيقة',
            hintText: 'أدخل رقم الوثيقة',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // تاريخ الوثيقة
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _documentDate ?? DateTime.now(),
              firstDate: DateTime(1900),
              lastDate: DateTime.now(),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: const ColorScheme.light(
                      primary: AppColors.primary,
                    ),
                  ),
                  child: child!,
                );
              },
            );

            if (date != null) {
              setState(() {
                _documentDate = date;
              });
            }
          },
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'تاريخ الوثيقة',
              hintText: 'اختر تاريخ الوثيقة',
              border: OutlineInputBorder(),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _documentDate != null
                      ? DateFormat('yyyy/MM/dd').format(_documentDate!)
                      : 'اختر تاريخ الوثيقة',
                  style: _documentDate != null
                      ? AppStyles.body1
                      : AppStyles.body1.copyWith(color: Colors.grey),
                ),
                const Icon(Icons.calendar_today),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // الجهة المصدرة
        TextFormField(
          controller: _issuerController,
          decoration: const InputDecoration(
            labelText: 'الجهة المصدرة',
            hintText: 'أدخل اسم الجهة المصدرة',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // الجهة المستلمة
        TextFormField(
          controller: _recipientController,
          decoration: const InputDecoration(
            labelText: 'الجهة المستلمة',
            hintText: 'أدخل اسم الجهة المستلمة',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // مستوى السرية
        DropdownButtonFormField<ArchiveDocumentConfidentiality>(
          value: _confidentiality,
          decoration: const InputDecoration(
            labelText: 'مستوى السرية',
            border: OutlineInputBorder(),
          ),
          items: ArchiveDocumentConfidentiality.values.map((confidentiality) {
            return DropdownMenuItem<ArchiveDocumentConfidentiality>(
              value: confidentiality,
              child: Text(_getConfidentialityText(confidentiality)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _confidentiality = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),

        // مستوى الأهمية
        DropdownButtonFormField<ArchiveDocumentImportance>(
          value: _importance,
          decoration: const InputDecoration(
            labelText: 'مستوى الأهمية',
            border: OutlineInputBorder(),
          ),
          items: ArchiveDocumentImportance.values.map((importance) {
            return DropdownMenuItem<ArchiveDocumentImportance>(
              value: importance,
              child: Text(_getImportanceText(importance)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _importance = value;
              });
            }
          },
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildFileUploadArea() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رفع الملف',
          style: AppStyles.headline6,
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: _pickFile,
          child: Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _file != null ? AppColors.primary : Colors.grey.shade300,
                width: 2,
                style: BorderStyle.solid,
              ),
            ),
            child: _buildFileContent(),
          ),
        ),
      ],
    );
  }

  String _getConfidentialityText(ArchiveDocumentConfidentiality confidentiality) {
    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        return 'عادي';
      case ArchiveDocumentConfidentiality.confidential:
        return 'سري';
      case ArchiveDocumentConfidentiality.highlyConfidential:
        return 'سري للغاية';
      case ArchiveDocumentConfidentiality.topSecret:
        return 'سري جداً';
    }
  }

  String _getImportanceText(ArchiveDocumentImportance importance) {
    switch (importance) {
      case ArchiveDocumentImportance.low:
        return 'منخفضة';
      case ArchiveDocumentImportance.normal:
        return 'عادية';
      case ArchiveDocumentImportance.high:
        return 'مرتفعة';
      case ArchiveDocumentImportance.urgent:
        return 'عاجلة';
    }
  }

  /// بناء محتوى منطقة الملف
  Widget _buildFileContent() {
    if (_file != null) {
      // عرض معاينة الملف
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(_fileType),
            size: 64,
            color: _getFileColor(_fileType),
          ),
          const SizedBox(height: 16),
          Text(
            _fileName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'انقر لاختيار ملف آخر',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      );
    } else {
      // عرض تلميح الإفلات
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_upload,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          const Text(
            'انقر لاختيار ملف',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'أو اسحب وأفلت ملفًا هنا',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      );
    }
  }

  /// الحصول على أيقونة الملف حسب نوعه
  IconData _getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return Icons.image;
      case 'doc':
      case 'docx':
      case 'txt':
        return Icons.description;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'ogg':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// الحصول على لون الملف حسب نوعه
  Color _getFileColor(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return Colors.blue;
      case 'doc':
      case 'docx':
      case 'txt':
        return Colors.indigo;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return Colors.green;
      case 'ppt':
      case 'pptx':
        return Colors.orange;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Colors.purple;
      case 'mp3':
      case 'wav':
      case 'ogg':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }
}
