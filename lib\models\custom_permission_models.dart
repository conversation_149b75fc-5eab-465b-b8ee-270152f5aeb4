import 'package:flutter_application_2/models/interface_models.dart';

import 'user_model.dart';

/// تعداد أنواع الصلاحيات المفصلة
enum PermissionType {
  view('عرض', 'view'),
  create('إضافة', 'create'),
  edit('تعديل', 'edit'),
  delete('حذف', 'delete'),
  approve('موافقة', 'approve'),
  export('تصدير', 'export'),
  import('استيراد', 'import'),
  admin('إدارة', 'admin'),
  assign('إسناد', 'assign'),
  transfer('نقل', 'transfer'),
  comment('تعليق', 'comment'),
  attach('إرفاق', 'attach'),
  viewAll('عرض الكل', 'viewAll'),
  viewOwn('عرض الخاص', 'viewOwn'),
  interfaceAccess('الوصول للواجهة', 'interfaceAccess'),
  download('تحميل', 'download'),
  upload('رفع', 'upload'),
  share('مشاركة', 'share'),
  print('طباعة', 'print'),
  ganttChart('مخطط جانت', 'ganttChart'),
  powerBI('تقارير BI', 'powerBI'),
  calendar('التقويم', 'calendar');

  const PermissionType(this.displayName, this.value);
  final String displayName;
  final String value;

  static PermissionType fromValue(String value) {
    return PermissionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PermissionType.view,
    );
  }
}

/// تعداد مجالات الصلاحيات
enum PermissionScope {
  tasks('المهام', 'tasks'),
  users('المستخدمين', 'users'),
  departments('الأقسام', 'departments'),
  messages('الرسائل', 'messages'),
  reports('التقارير', 'reports'),
  settings('الإعدادات', 'settings'),
  backups('النسخ الاحتياطية', 'backups'),
  notifications('الإشعارات', 'notifications'),
  logs('السجلات', 'logs'),
  dashboard('لوحة التحكم', 'dashboard'),
  analytics('التحليلات', 'analytics'),
  interfaces('الواجهات', 'interfaces'),
  attachments('المرفقات', 'attachments'),
  files('الملفات', 'files'),
  ganttChart('مخطط جانت', 'ganttChart'),
  powerBI('تقارير BI', 'powerBI'),
  calendar('التقويم', 'calendar'),
  database('قاعدة البيانات', 'database'),
  roles('الأدوار', 'roles'),
  archive('الأرشيف', 'archive');

  const PermissionScope(this.displayName, this.value);
  final String displayName;
  final String value;

  static PermissionScope fromValue(String value) {
    return PermissionScope.values.firstWhere(
      (scope) => scope.value == value,
      orElse: () => PermissionScope.tasks,
    );
  }
}

/// نموذج الصلاحية المخصصة
class CustomPermission {
  final String id;
  final String customRoleId;
  final PermissionType type;
  final PermissionScope scope;
  final bool isGranted;
  final String? description;
  final int createdAt;
  final int? updatedAt;
  final int? grantedBy;

  // Navigation properties
  final CustomRole? customRole;
  final User? grantedByNavigation;

  const CustomPermission({
    required this.id,
    required this.customRoleId,
    required this.type,
    required this.scope,
    this.isGranted = false,
    this.description,
    required this.createdAt,
    this.updatedAt,
    this.grantedBy,
    this.customRole,
    this.grantedByNavigation,
  });

  /// إنشاء صلاحية جديدة
  factory CustomPermission.create({
    required String customRoleId,
    required PermissionType type,
    required PermissionScope scope,
    bool isGranted = false,
    String? description,
    int? grantedBy,
  }) {
    return CustomPermission(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customRoleId: customRoleId,
      type: type,
      scope: scope,
      isGranted: isGranted,
      description: description,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      grantedBy: grantedBy,
    );
  }

  /// إنشاء CustomPermission من JSON (من API)
  factory CustomPermission.fromJson(Map<String, dynamic> json) {
    return CustomPermission(
      id: json['id']?.toString() ?? '',
      customRoleId: json['customRoleId']?.toString() ?? '',
      type: PermissionType.fromValue(json['type'] as String? ?? 'view'),
      scope: PermissionScope.fromValue(json['scope'] as String? ?? 'tasks'),
      isGranted: json['isGranted'] as bool? ?? false,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      grantedBy: json['grantedBy'] as int?,
      customRole: json['customRole'] != null
          ? CustomRole.fromJson(json['customRole'] as Map<String, dynamic>)
          : null,
      grantedByNavigation: json['grantedByNavigation'] != null
          ? User.fromJson(json['grantedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customRoleId': customRoleId,
      'type': type.value,
      'scope': scope.value,
      'isGranted': isGranted,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'grantedBy': grantedBy,
    };
  }

  /// إنشاء نسخة محدثة من الصلاحية
  CustomPermission copyWith({
    String? id,
    String? customRoleId,
    PermissionType? type,
    PermissionScope? scope,
    bool? isGranted,
    String? description,
    int? createdAt,
    int? updatedAt,
    int? grantedBy,
    CustomRole? customRole,
    User? grantedByNavigation,
  }) {
    return CustomPermission(
      id: id ?? this.id,
      customRoleId: customRoleId ?? this.customRoleId,
      type: type ?? this.type,
      scope: scope ?? this.scope,
      isGranted: isGranted ?? this.isGranted,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      grantedBy: grantedBy ?? this.grantedBy,
      customRole: customRole ?? this.customRole,
      grantedByNavigation: grantedByNavigation ?? this.grantedByNavigation,
    );
  }

  /// الحصول على اسم الصلاحية المركب
  String get displayName => '${type.displayName} - ${scope.displayName}';

  /// الحصول على مفتاح الصلاحية الفريد
  String get permissionKey => '${type.value}_${scope.value}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomPermission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CustomPermission(id: $id, type: ${type.value}, scope: ${scope.value}, isGranted: $isGranted)';
  }
}

/// نموذج صلاحية الواجهة
class InterfacePermission {
  final String id;
  final String interfaceName;
  final String interfaceRoute;
  final String? description;
  final String? icon;
  final String? category;
  final bool isActive;
  final int createdAt;

  const InterfacePermission({
    required this.id,
    required this.interfaceName,
    required this.interfaceRoute,
    this.description,
    this.icon,
    this.category,
    this.isActive = true,
    required this.createdAt,
  });

  /// إنشاء صلاحية واجهة جديدة
  factory InterfacePermission.create({
    required String interfaceName,
    required String interfaceRoute,
    String? description,
    String? icon,
    String? category,
  }) {
    return InterfacePermission(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      interfaceName: interfaceName,
      interfaceRoute: interfaceRoute,
      description: description,
      icon: icon,
      category: category,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );
  }

  /// إنشاء InterfacePermission من JSON (من API)
  factory InterfacePermission.fromJson(Map<String, dynamic> json) {
    return InterfacePermission(
      id: json['id']?.toString() ?? '',
      interfaceName: json['interfaceName'] as String,
      interfaceRoute: json['interfaceRoute'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      category: json['category'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
    );
  }

  /// تحويل إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'interfaceName': interfaceName,
      'interfaceRoute': interfaceRoute,
      'description': description,
      'icon': icon,
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InterfacePermission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'InterfacePermission(id: $id, name: $interfaceName, route: $interfaceRoute)';
  }
}

/// نموذج طلب تحديث الصلاحيات
class UpdatePermissionsRequest {
  final String customRoleId;
  final List<CustomPermission> permissions;

  const UpdatePermissionsRequest({
    required this.customRoleId,
    required this.permissions,
  });

  Map<String, dynamic> toJson() {
    return {
      'customRoleId': customRoleId,
      'permissions': permissions.map((p) => p.toJson()).toList(),
    };
  }
}
