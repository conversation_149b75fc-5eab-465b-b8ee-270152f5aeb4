// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../../services/sync_service.dart';
// import '../../controllers/auth_controller.dart';
// import '../widgets/app_drawer.dart';
// import '../widgets/custom_app_bar.dart';
// import '../../utils/app_colors.dart';
// import '../../utils/app_styles.dart';
// import '../widgets/custom_dropdown.dart';

// /// شاشة إعدادات التزامن
// class SyncSettingsScreen extends StatelessWidget {
//   const SyncSettingsScreen({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     // محاولة الحصول على خدمة التزامن
//     SyncService? syncService;
//     try {
//       syncService = Get.find<SyncService>();
//     } catch (e) {
//       // إذا لم تكن خدمة التزامن متاحة، حاول إنشاءها
//       try {
//         final authController = Get.find<AuthController>();
//         if (authController.currentUser.value != null) {
//           syncService = SyncService(authController.currentUser.value!.id);
//           Get.put(syncService);
//         }
//       } catch (e) {
//         debugPrint('خطأ في إنشاء خدمة التزامن: $e');
//       }
//     }

//     return Scaffold(
//       appBar: CustomAppBar(
//         title: 'إعدادات التزامن',
//         automaticallyImplyLeading: true,
//       ),
//       drawer: const AppDrawer(),
//       body: syncService == null
//           ? _buildNoSyncServiceView()
//           : _buildSyncSettingsView(syncService),
//     );
//   }

//   /// بناء واجهة عندما تكون خدمة التزامن غير متاحة
//   Widget _buildNoSyncServiceView() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(
//             Icons.sync_problem,
//             size: 64,
//             color: Colors.grey,
//           ),
//           const SizedBox(height: 16),
//           Text(
//             'خدمة التزامن غير متاحة',
//             style: AppStyles.headingMedium,
//           ),
//           const SizedBox(height: 8),
//           Text(
//             'يرجى تسجيل الدخول وإعادة تشغيل التطبيق',
//             style: AppStyles.bodyMedium,
//           ),
//           const SizedBox(height: 24),
//           ElevatedButton(
//             onPressed: () {
//               Get.back();
//             },
//             child: const Text('العودة'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// بناء واجهة إعدادات التزامن
//   Widget _buildSyncSettingsView(SyncService syncService) {
//     // قائمة خيارات الفاصل الزمني للمزامنة
//     final syncIntervalOptions = [
//       DropdownMenuItem(value: 500, child: const Text('نصف ثانية (500 مللي ثانية)')),
//       DropdownMenuItem(value: 1000, child: const Text('ثانية واحدة (1000 مللي ثانية)')),
//       DropdownMenuItem(value: 2000, child: const Text('ثانيتان (2000 مللي ثانية)')),
//       DropdownMenuItem(value: 3000, child: const Text('3 ثواني (3000 مللي ثانية)')),
//       DropdownMenuItem(value: 5000, child: const Text('5 ثواني (5000 مللي ثانية)')),
//       DropdownMenuItem(value: 10000, child: const Text('10 ثواني (10000 مللي ثانية)')),
//     ];

//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // معلومات التزامن
//             Card(
//               elevation: 2,
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'حالة التزامن',
//                       style: AppStyles.headingMedium,
//                     ),
//                     const SizedBox(height: 16),
//                     Obx(() => _buildSyncStatusRow(
//                           'حالة الاتصال:',
//                           syncService.isSyncing.value
//                               ? 'جاري التزامن'
//                               : 'متصل',
//                           syncService.isSyncing.value
//                               ? Colors.blue
//                               : Colors.green,
//                         )),
//                     const SizedBox(height: 8),
//                     Obx(() => _buildSyncStatusRow(
//                           'آخر تحديث:',
//                           syncService.syncStatus.value.isEmpty
//                               ? 'لا يوجد تحديث'
//                               : syncService.syncStatus.value,
//                           Colors.black,
//                         )),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(height: 24),

//             // إعدادات التزامن
//             Card(
//               elevation: 2,
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'إعدادات التزامن',
//                       style: AppStyles.headingMedium,
//                     ),
//                     const SizedBox(height: 16),

//                     // الفاصل الزمني للمزامنة
//                     Obx(() => CustomDropdown<int>(
//                       label: 'الفاصل الزمني للمزامنة',
//                       value: syncService.syncIntervalMs.value,
//                       items: syncIntervalOptions,
//                       onChanged: (value) {
//                         if (value != null) {
//                           syncService.syncIntervalMs.value = value;
//                           syncService.saveSyncSettings();
//                         }
//                       },
//                       hint: 'اختر الفاصل الزمني للمزامنة',
//                     )),
//                     const SizedBox(height: 16),

//                     // إعادة المحاولة التلقائية
//                     Obx(() => SwitchListTile(
//                       title: const Text('إعادة المحاولة التلقائية'),
//                       subtitle: const Text('إعادة المحاولة تلقائيًا عند فشل المزامنة'),
//                       value: syncService.autoRetry.value,
//                       onChanged: (value) {
//                         syncService.autoRetry.value = value;
//                         syncService.saveSyncSettings();
//                       },
//                       activeColor: AppColors.primary,
//                     )),

//                     // ملاحظة حول إزالة الأقفال
//                     const SizedBox(height: 16),
//                     Container(
//                       padding: const EdgeInsets.all(12),
//                       decoration: BoxDecoration(
//                         color: Colors.blue.shade50,
//                         borderRadius: BorderRadius.circular(8),
//                         border: Border.all(color: Colors.blue.shade200),
//                       ),
//                       child: Row(
//                         children: [
//                           Icon(Icons.info_outline, color: Colors.blue.shade700),
//                           const SizedBox(width: 12),
//                           Expanded(
//                             child: Text(
//                               'تم إزالة نظام الأقفال من التطبيق لتحسين الأداء وتجنب مشكلات الانتظار.',
//                               style: TextStyle(color: Colors.blue.shade700),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(height: 24),

//             // معلومات عن التزامن
//             Card(
//               elevation: 2,
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'معلومات عن التزامن',
//                       style: AppStyles.headingMedium,
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       'يتيح نظام التزامن للمستخدمين العمل معًا في نفس الوقت على نفس البيانات. '
//                       'يتم تزامن التغييرات تلقائيًا بين جميع المستخدمين المتصلين بنفس قاعدة البيانات.',
//                       style: AppStyles.bodyMedium,
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       'البيانات التي يتم تزامنها:',
//                       style: AppStyles.bodyMedium.copyWith(
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     _buildSyncFeatureRow('الرسائل والمحادثات'),
//                     _buildSyncFeatureRow('المهام وحالاتها'),
//                     _buildSyncFeatureRow('التعليقات والمرفقات'),
//                     _buildSyncFeatureRow('الإشعارات'),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(height: 24),

//             // زر إعادة التزامن
//             Center(
//               child: ElevatedButton.icon(
//                 onPressed: () {
//                   // إعادة تشغيل خدمة التزامن
//                   Get.delete<SyncService>();
//                   final authController = Get.find<AuthController>();
//                   if (authController.currentUser.value != null) {
//                     final newSyncService = SyncService(authController.currentUser.value!.id);
//                     Get.put(newSyncService);
//                     Get.snackbar(
//                       'إعادة تشغيل التزامن',
//                       'تم إعادة تشغيل خدمة التزامن بنجاح',
//                       snackPosition: SnackPosition.BOTTOM,
//                       backgroundColor: Colors.green.shade100,
//                       colorText: Colors.green.shade800,
//                     );
//                   }
//                 },
//                 icon: const Icon(Icons.refresh),
//                 label: const Text('إعادة تشغيل التزامن'),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: AppColors.primary,
//                   foregroundColor: Colors.white,
//                   padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   /// بناء صف لعرض حالة التزامن
//   Widget _buildSyncStatusRow(String label, String value, Color valueColor) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Text(
//           label,
//           style: AppStyles.bodyMedium,
//         ),
//         Text(
//           value,
//           style: AppStyles.bodyMedium.copyWith(
//             color: valueColor,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       ],
//     );
//   }

//   /// بناء صف لعرض ميزة التزامن
//   Widget _buildSyncFeatureRow(String feature) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8.0),
//       child: Row(
//         children: [
//           const Icon(
//             Icons.check_circle,
//             color: AppColors.success,
//             size: 20,
//           ),
//           const SizedBox(width: 8),
//           Text(
//             feature,
//             style: AppStyles.bodyMedium,
//           ),
//         ],
//       ),
//     );
//   }
// }
