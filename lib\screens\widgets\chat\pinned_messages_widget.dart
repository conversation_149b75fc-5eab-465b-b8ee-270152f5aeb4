import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/date_time_formatter.dart';
import 'package:flutter_application_2/models/message_models.dart';
import 'package:flutter_application_2/models/user_model.dart';
import 'package:flutter_application_2/services/pinned_message_service.dart';
import 'package:flutter_application_2/services/user_service.dart';



/// ويدجت لعرض الرسائل المثبتة
class PinnedMessagesWidget extends StatefulWidget {
  /// معرف المجموعة
  final String groupId;

  /// دالة يتم استدعاؤها عند النقر على رسالة مثبتة
  final Function(Message) onMessageTap;

  /// دالة يتم استدعاؤها عند إلغاء تثبيت رسالة
  final Function(Message)? onUnpinMessage;

  /// معرف المستخدم الحالي
  final String currentUserId;

  /// ما إذا كان المستخدم الحالي مشرفًا
  final bool isAdmin;

  const PinnedMessagesWidget({
    super.key,
    required this.groupId,
    required this.onMessageTap,
    required this.currentUserId,
    this.isAdmin = false,
    this.onUnpinMessage,
  });

  @override
  State<PinnedMessagesWidget> createState() => _PinnedMessagesWidgetState();
}

class _PinnedMessagesWidgetState extends State<PinnedMessagesWidget> {
  final PinnedMessageService _pinnedMessageService = PinnedMessageService();
  final UserService _userService = UserService();

  List<Message> _pinnedMessages = [];
  Map<int, User?> _users = {};
  bool _isLoading = true;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadPinnedMessages();
  }

  @override
  void didUpdateWidget(PinnedMessagesWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.groupId != widget.groupId) {
      _loadPinnedMessages();
    }
  }

  /// تحميل الرسائل المثبتة
  Future<void> _loadPinnedMessages() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على الرسائل المثبتة
      final messages = await _pinnedMessageService.getPinnedMessagesForGroup(widget.groupId);

      // الحصول على معلومات المستخدمين
      final users = <int, User?>{};
      for (final message in messages) {
        if (!users.containsKey(message.senderId)) {
          users[message.senderId] = await _userService.getUserById(message.senderId);
        }

        if (message.pinnedBy != null && !users.containsKey(message.pinnedBy!)) {
          users[message.pinnedBy!] = await _userService.getUserById(message.pinnedBy!);
        }
      }

      if (mounted) {
        setState(() {
          _pinnedMessages = messages;
          _users = users;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إلغاء تثبيت رسالة
  Future<void> _unpinMessage(Message message) async {
    try {
      final success = await _pinnedMessageService.unpinMessage(message.id, widget.currentUserId);

      if (success) {
        if (widget.onUnpinMessage != null) {
          widget.onUnpinMessage!(message);
        }

        _loadPinnedMessages();
      }
    } catch (e) {
      // التعامل مع الخطأ
    }
  }

  /// الحصول على نص مختصر للرسالة
  String _getMessagePreview(Message message) {
    switch (message.contentType) {
      case MessageContentType.image:
        return 'صورة';
      case MessageContentType.video:
        return 'فيديو';
      case MessageContentType.voice:
        return 'تسجيل صوتي';
      case MessageContentType.file:
        return 'ملف';
      case MessageContentType.location:
        return 'موقع';
      case MessageContentType.text:
      default:
        // اقتصاص النص إذا كان طويلاً
        if (message.content.length > 50) {
          return '${message.content.substring(0, 47)}...';
        }
        return message.content;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_pinnedMessages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          ListTile(
            title: Text(
              'الرسائل المثبتة (${_pinnedMessages.length})',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
          ),
          if (_isExpanded)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _pinnedMessages.length,
              itemBuilder: (context, index) {
                final message = _pinnedMessages[index];
                final sender = _users[message.senderId];
                final pinner = message.pinnedBy != null ? _users[message.pinnedBy!] : null;

                return ListTile(
                  leading: CircleAvatar(
                    backgroundImage: sender?.profileImage != null
                        ? NetworkImage(sender!.profileImage!)
                        : null,
                    child: sender?.profileImage == null
                        ? Text(sender?.name.substring(0, 1) ?? '?')
                        : null,
                  ),
                  title: Text(
                    sender?.name ?? 'مستخدم غير معروف',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(_getMessagePreview(message)),
                      Text(
                        'ثبتها ${pinner?.name ?? 'مستخدم'} ${DateTimeFormatter.formatRelativeTime(DateTime.fromMillisecondsSinceEpoch((message.pinnedAt ?? message.createdAt) * 1000))}',
                        style: TextStyle(
                          fontSize: 12.0,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  trailing: widget.isAdmin
                      ? IconButton(
                          icon: const Icon(Icons.push_pin_outlined),
                          onPressed: () => _unpinMessage(message),
                        )
                      : null,
                  onTap: () => widget.onMessageTap(message),
                );
              },
            ),
        ],
      ),
    );
  }
}
