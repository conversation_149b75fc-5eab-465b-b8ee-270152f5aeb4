import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/permission_models.dart';
import 'package:get/get.dart';
import '../services/api/permissions_api_service.dart';

/// متحكم الصلاحيات
class PermissionsController extends GetxController {
  final PermissionsApiService _apiService = PermissionsApiService();

  // قوائم الصلاحيات
  final RxList<Permission> _allPermissions = <Permission>[].obs;
  final RxList<Permission> _filteredPermissions = <Permission>[].obs;
  final RxList<Permission> _rolePermissions = <Permission>[].obs;

  // الصلاحية الحالية
  final Rx<Permission?> _currentPermission = Rx<Permission?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _moduleFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<Permission> get allPermissions => _allPermissions;
  List<Permission> get filteredPermissions => _filteredPermissions;
  List<Permission> get rolePermissions => _rolePermissions;
  Permission? get currentPermission => _currentPermission.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get moduleFilter => _moduleFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllPermissions();
  }

  /// تحميل جميع الصلاحيات
  Future<void> loadAllPermissions() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getAllPermissions();
      _allPermissions.assignAll(permissions);
      _applyFilters();
      debugPrint('تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على صلاحية بالمعرف
  Future<void> getPermissionById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permission = await _apiService.getPermissionById(id);
      _currentPermission.value = permission;
      debugPrint('تم تحميل الصلاحية: ${permission?.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحية: $e';
      debugPrint('خطأ في تحميل الصلاحية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء صلاحية جديدة
  Future<bool> createPermission(Permission permission) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newPermission = await _apiService.createPermission(permission);
      _allPermissions.add(newPermission);
      _applyFilters();
      debugPrint('تم إنشاء صلاحية جديدة: ${newPermission.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء الصلاحية: $e';
      debugPrint('خطأ في إنشاء الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث صلاحية
  Future<bool> updatePermission(int id, Permission permission) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updatePermission(id, permission);
      final index = _allPermissions.indexWhere((p) => p.id == id);
      if (index != -1) {
        _allPermissions[index] = permission;
        _applyFilters();
      }
      debugPrint('تم تحديث الصلاحية: ${permission.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الصلاحية: $e';
      debugPrint('خطأ في تحديث الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف صلاحية
  Future<bool> deletePermission(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deletePermission(id);
      _allPermissions.removeWhere((p) => p.id == id);
      _applyFilters();
      debugPrint('تم حذف الصلاحية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الصلاحية: $e';
      debugPrint('خطأ في حذف الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على صلاحيات الدور
  Future<void> getPermissionsByRole(int roleId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getRolePermissions(roleId);
      _rolePermissions.assignAll(permissions);
      debugPrint('تم تحميل ${permissions.length} صلاحية للدور $roleId');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات الدور: $e';
      debugPrint('خطأ في تحميل صلاحيات الدور: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من الصلاحية
  Future<bool> checkPermission(int userId, String permissionName) async {
    try {
      final hasPermission = await _apiService.checkPermission(userId, permissionName);
      debugPrint('التحقق من الصلاحية $permissionName للمستخدم $userId: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// منح صلاحية لدور (ملاحظة: يجب تمرير رقم الدور)
  Future<bool> grantPermissionToRole(int roleId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.grantPermissionToRole(roleId, permissionId);
      debugPrint('تم منح الصلاحية للدور $roleId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحية للدور: $e';
      debugPrint('خطأ في منح الصلاحية للدور: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء صلاحية من دور (ملاحظة: يجب تمرير رقم الدور)
  Future<bool> revokePermissionFromRole(int roleId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.revokePermissionFromRole(roleId, permissionId);
      debugPrint('تم إلغاء الصلاحية من الدور $roleId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إلغاء الصلاحية من الدور: $e';
      debugPrint('خطأ في إلغاء الصلاحية من الدور: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPermissions.where((permission) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!permission.name.toLowerCase().contains(query) &&
            !(permission.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح المجموعة
      if (_moduleFilter.value != null && permission.permissionGroup != _moduleFilter.value) {
        return false;
      }

      // مرشح النشط فقط (استخدام isDefault كبديل)
      if (_showActiveOnly.value && !permission.isDefault) {
        return false;
      }

      return true;
    }).toList();

    _filteredPermissions.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح الوحدة
  void setModuleFilter(String? module) {
    _moduleFilter.value = module;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _moduleFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllPermissions();
  }
}
