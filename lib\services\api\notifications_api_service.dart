import 'package:flutter/foundation.dart';
import '../../models/notification_models.dart';
import 'api_service.dart';

/// خدمة API للإشعارات - متطابقة مع ASP.NET Core API
class NotificationsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الإشعارات
  Future<List<NotificationModel>> getAllNotifications() async {
    try {
      final response = await _apiService.get('/api/Notifications');
      return _apiService.handleListResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات: $e');
      rethrow;
    }
  }

  /// الحصول على إشعار بواسطة المعرف
  Future<NotificationModel?> getNotificationById(int id) async {
    try {
      final response = await _apiService.get('/api/Notifications/$id');
      return _apiService.handleResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعار $id: $e');
      return null;
    }
  }

  /// الحصول على الإشعارات غير المقروءة للمستخدم الحالي
  Future<List<NotificationModel>> getUnreadNotifications(int userId) async {
    try {
      final response = await _apiService.get('/api/Notifications/user/$userId/unread');
      return _apiService.handleListResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات غير المقروءة: $e');
      rethrow;
    }
  }

  /// الحصول على إشعارات مستخدم محدد
  Future<List<NotificationModel>> getNotificationsByUserId(int userId) async {
    try {
      final response = await _apiService.get('/api/Notifications/user/$userId');
      return _apiService.handleListResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إشعارات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// إنشاء إشعار جديد
  Future<NotificationModel> createNotification(NotificationModel notification) async {
    try {
      final response = await _apiService.post(
        '/api/Notifications',
        notification.toJson(),
      );
      return _apiService.handleResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الإشعار: $e');
      rethrow;
    }
  }

  /// تحديث إشعار
  Future<NotificationModel> updateNotification(int id, NotificationModel notification) async {
    try {
      final response = await _apiService.put(
        '/api/Notifications/$id',
        notification.toJson(),
      );
      return _apiService.handleResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الإشعار $id: $e');
      rethrow;
    }
  }

  /// تحديد إشعار كمقروء
  Future<bool> markAsRead(int id) async {
    try {
      final response = await _apiService.patch(
        '/api/Notifications/$id/mark-read',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة للمستخدم الحالي
  Future<bool> markAllAsRead(int userId) async {
    try {
      final response = await _apiService.patch(
        '/api/Notifications/user/$userId/mark-all-read',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد جميع الإشعارات كمقروءة: $e');
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(int id) async {
    try {
      final response = await _apiService.delete('/api/Notifications/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار $id: $e');
      return false;
    }
  }

  /// حذف جميع الإشعارات للمستخدم الحالي
  Future<bool> deleteAllNotifications() async {
    try {
      final response = await _apiService.delete('/api/Notifications/delete-all');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف جميع الإشعارات: $e');
      return false;
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadCount() async {
    try {
      final response = await _apiService.get('/api/Notifications/unread-count');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['count'] as int? ?? 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// البحث في الإشعارات
  Future<List<NotificationModel>> searchNotifications(String query) async {
    try {
      final response = await _apiService.get('/api/Notifications/search?q=$query');
      return _apiService.handleListResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في الإشعارات: $e');
      rethrow;
    }
  }

  /// الحصول على الإشعارات بحسب النوع
  Future<List<NotificationModel>> getNotificationsByType(String type) async {
    try {
      final response = await _apiService.get('/api/Notifications/type/$type');
      return _apiService.handleListResponse<NotificationModel>(
        response,
        (json) => NotificationModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات بحسب النوع $type: $e');
      rethrow;
    }
  }
}
