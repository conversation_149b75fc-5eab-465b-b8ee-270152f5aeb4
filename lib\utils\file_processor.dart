import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;

/// معالج الملفات
/// يوفر وظائف لمعالجة الملفات مثل تحويل حجم الصور وإنشاء صور مصغرة
class FileProcessor {
  /// تحويل حجم الصورة
  /// يقوم بتحويل حجم الصورة إلى الحجم المطلوب مع الحفاظ على نسبة العرض إلى الارتفاع
  ///
  /// [imagePath] مسار الصورة الأصلية
  /// [maxWidth] الحد الأقصى للعرض
  /// [maxHeight] الحد الأقصى للارتفاع
  /// [quality] جودة الصورة (0-100)
  ///
  /// يعيد مسار الصورة المحولة
  static Future<String?> resizeImage({
    required String imagePath,
    required int maxWidth,
    required int maxHeight,
    int quality = 85,
  }) async {
    try {
      // قراءة الصورة
      final File imageFile = File(imagePath);
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('فشل في قراءة الصورة: $imagePath');
        return null;
      }

      // حساب الأبعاد الجديدة مع الحفاظ على نسبة العرض إلى الارتفاع
      final double aspectRatio = originalImage.width / originalImage.height;
      int newWidth = maxWidth;
      int newHeight = (newWidth / aspectRatio).round();

      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = (newHeight * aspectRatio).round();
      }

      // تحويل حجم الصورة
      final img.Image resizedImage = img.copyResize(
        originalImage,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.average,
      );

      // تحويل الصورة إلى بيانات ثنائية
      final Uint8List resizedImageBytes = Uint8List.fromList(
        img.encodeJpg(resizedImage, quality: quality),
      );

      // إنشاء مسار للصورة المحولة
      final String fileName = path.basename(imagePath);
      final String fileNameWithoutExtension = path.basenameWithoutExtension(fileName);
      final String fileExtension = path.extension(fileName);
      final String resizedFileName = '${fileNameWithoutExtension}_${newWidth}x$newHeight$fileExtension';

      final Directory appDir = await getApplicationDocumentsDirectory();
      final String resizedDirPath = path.join(appDir.path, 'resized_images');
      final Directory resizedDir = Directory(resizedDirPath);

      if (!await resizedDir.exists()) {
        await resizedDir.create(recursive: true);
      }

      final String resizedImagePath = path.join(resizedDirPath, resizedFileName);
      final File resizedImageFile = File(resizedImagePath);
      await resizedImageFile.writeAsBytes(resizedImageBytes);

      return resizedImagePath;
    } catch (e) {
      debugPrint('خطأ في تحويل حجم الصورة: $e');
      return null;
    }
  }

  /// إنشاء صورة مصغرة
  /// يقوم بإنشاء صورة مصغرة للصورة المحددة
  ///
  /// [imagePath] مسار الصورة الأصلية
  /// [thumbnailSize] حجم الصورة المصغرة
  /// [quality] جودة الصورة (0-100)
  ///
  /// يعيد مسار الصورة المصغرة
  static Future<String?> createThumbnail({
    required String imagePath,
    int thumbnailSize = 150,
    int quality = 70,
  }) async {
    try {
      return await resizeImage(
        imagePath: imagePath,
        maxWidth: thumbnailSize,
        maxHeight: thumbnailSize,
        quality: quality,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الصورة المصغرة: $e');
      return null;
    }
  }

  /// التحقق من نوع الملف
  /// يقوم بالتحقق من نوع الملف بناءً على امتداده
  ///
  /// [filePath] مسار الملف
  ///
  /// يعيد نوع الملف (صورة، مستند، فيديو، صوت، أرشيف، كود، أخرى)
  static String getFileType(String filePath) {
    final String extension = path.extension(filePath).toLowerCase();

    // صور
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.ico', '.heic'].contains(extension)) {
      return 'image';
    }

    // مستندات نصية
    if (['.txt', '.rtf', '.md', '.log', '.csv', '.json', '.xml'].contains(extension)) {
      return 'text';
    }

    // مستندات PDF
    if (['.pdf'].contains(extension)) {
      return 'pdf';
    }

    // مستندات Word
    if (['.doc', '.docx', '.odt', '.pages'].contains(extension)) {
      return 'word';
    }

    // جداول بيانات
    if (['.xls', '.xlsx', '.csv', '.ods', '.numbers'].contains(extension)) {
      return 'spreadsheet';
    }

    // عروض تقديمية
    if (['.ppt', '.pptx', '.odp', '.key'].contains(extension)) {
      return 'presentation';
    }

    // فيديو
    if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp', '.mpeg', '.mpg'].contains(extension)) {
      return 'video';
    }

    // صوت
    if (['.mp3', '.wav', '.ogg', '.aac', '.wma', '.flac', '.m4a', '.opus'].contains(extension)) {
      return 'audio';
    }

    // ملفات مضغوطة
    if (['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.iso'].contains(extension)) {
      return 'archive';
    }

    // ملفات برمجية
    if (['.html', '.css', '.js', '.ts', '.jsx', '.tsx', '.php', '.py', '.java', '.c', '.cpp', '.cs', '.swift', '.kt', '.dart', '.go', '.rb'].contains(extension)) {
      return 'code';
    }

    // ملفات قواعد بيانات
    if (['.db', '.sqlite', '.sql', '.mdb', '.accdb'].contains(extension)) {
      return 'database';
    }

    // ملفات تصميم
    if (['.psd', '.ai', '.xd', '.sketch', '.fig', '.indd', '.cdr'].contains(extension)) {
      return 'design';
    }

    // ملفات ثلاثية الأبعاد
    if (['.obj', '.fbx', '.stl', '.blend', '.3ds', '.dae'].contains(extension)) {
      return '3d';
    }

    // أخرى
    return 'other';
  }

  /// الحصول على أيقونة الملف
  /// يقوم بإرجاع أيقونة مناسبة لنوع الملف
  ///
  /// [filePath] مسار الملف أو نوع الملف
  /// [color] لون الأيقونة
  ///
  /// يعيد أيقونة مناسبة لنوع الملف
  static IconData getFileIcon(String filePathOrType, {Color? color}) {
    final String fileType = filePathOrType.contains('.') ? getFileType(filePathOrType) : filePathOrType;
    final String extension = filePathOrType.contains('.') ? path.extension(filePathOrType).toLowerCase() : '';

    switch (fileType) {
      case 'image':
        return Icons.image;
      case 'text':
        return Icons.text_snippet;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'word':
        return Icons.description;
      case 'spreadsheet':
        return Icons.table_chart;
      case 'presentation':
        return Icons.slideshow;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      case 'archive':
        return Icons.folder_zip;
      case 'code':
        return Icons.code;
      case 'database':
        return Icons.storage;
      case 'design':
        return Icons.brush;
      case '3d':
        return Icons.view_in_ar;
      default:
        // Iconos específicos para extensiones comunes
        if (extension == '.json') {
          return Icons.data_object;
        } else if (extension == '.xml') {
          return Icons.code;
        } else if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
          return Icons.image;
        } else if (['.mp4', '.avi', '.mov'].contains(extension)) {
          return Icons.movie;
        } else if (['.mp3', '.wav', '.ogg'].contains(extension)) {
          return Icons.music_note;
        } else if (['.zip', '.rar', '.7z'].contains(extension)) {
          return Icons.archive;
        } else if (['.html', '.htm'].contains(extension)) {
          return Icons.html;
        } else if (['.css'].contains(extension)) {
          return Icons.css;
        } else if (['.js', '.ts'].contains(extension)) {
          return Icons.javascript;
        } else if (['.dart'].contains(extension)) {
          return Icons.flutter_dash;
        }

        return Icons.insert_drive_file;
    }
  }

  /// تنسيق حجم الملف
  /// يقوم بتنسيق حجم الملف بشكل مقروء
  ///
  /// [bytes] حجم الملف بالبايت
  ///
  /// يعيد حجم الملف بتنسيق مقروء (مثل 1.2 MB)
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final double kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final double mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final double gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// الحصول على لون الملف
  /// يقوم بإرجاع لون مناسب لنوع الملف
  ///
  /// [filePathOrType] مسار الملف أو نوع الملف
  ///
  /// يعيد لون مناسب لنوع الملف
  static Color getFileColor(String filePathOrType) {
    final String fileType = filePathOrType.contains('.') ? getFileType(filePathOrType) : filePathOrType;

    switch (fileType) {
      case 'image':
        return Colors.blue;
      case 'text':
        return Colors.grey;
      case 'pdf':
        return Colors.red;
      case 'word':
        return Colors.indigo;
      case 'spreadsheet':
        return Colors.green;
      case 'presentation':
        return Colors.orange;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      case 'archive':
        return Colors.brown;
      case 'code':
        return Colors.blueGrey;
      case 'database':
        return Colors.deepPurple;
      case 'design':
        return Colors.pink;
      case '3d':
        return Colors.cyan;
      default:
        return Colors.grey.shade700;
    }
  }

  /// تنظيف اسم الملف
  /// يقوم بإزالة الأحرف غير المسموح بها في اسم الملف
  ///
  /// [fileName] اسم الملف
  ///
  /// يعيد اسم الملف بعد التنظيف
  static String sanitizeFileName(String fileName) {
    // إزالة الأحرف غير المسموح بها في اسم الملف
    final RegExp invalidChars = RegExp(r'[\\/:*?"<>|]');
    String sanitized = fileName.replaceAll(invalidChars, '_');

    // التأكد من أن اسم الملف لا يبدأ أو ينتهي بمسافة أو نقطة
    sanitized = sanitized.trim();
    while (sanitized.endsWith('.')) {
      sanitized = sanitized.substring(0, sanitized.length - 1);
    }

    // التأكد من أن اسم الملف لا يتجاوز 255 حرفًا
    if (sanitized.length > 255) {
      final String extension = path.extension(sanitized);
      final String nameWithoutExtension = path.basenameWithoutExtension(sanitized);
      sanitized = nameWithoutExtension.substring(0, 255 - extension.length) + extension;
    }

    // إذا كان اسم الملف فارغًا، استخدم اسمًا افتراضيًا
    if (sanitized.isEmpty) {
      sanitized = 'file_${DateTime.now().millisecondsSinceEpoch}';
    }

    return sanitized;
  }
}
