import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/message_models.dart';
import '../services/api/messages_api_service.dart';

/// متحكم الرسائل
class MessagesController extends GetxController {
  final MessagesApiService _apiService = MessagesApiService();

  // قوائم الرسائل
  final RxList<Message> _allMessages = <Message>[].obs;
  final RxList<Message> _filteredMessages = <Message>[].obs;
  final RxList<Message> _groupMessages = <Message>[].obs;
  final RxList<Message> _unreadMessages = <Message>[].obs;

  // الرسالة الحالية
  final Rx<Message?> _currentMessage = Rx<Message?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _groupFilter = Rx<int?>(null);
  final Rx<int?> _senderFilter = Rx<int?>(null);
  final RxBool _showUnreadOnly = false.obs;

  // Getters
  List<Message> get allMessages => _allMessages;
  List<Message> get filteredMessages => _filteredMessages;
  List<Message> get groupMessages => _groupMessages;
  List<Message> get unreadMessages => _unreadMessages;
  Message? get currentMessage => _currentMessage.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get groupFilter => _groupFilter.value;
  int? get senderFilter => _senderFilter.value;
  bool get showUnreadOnly => _showUnreadOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllMessages();
    loadUnreadMessages();
  }

  /// تحميل جميع الرسائل
  Future<void> loadAllMessages() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final messages = await _apiService.getAllMessages();
      _allMessages.assignAll(messages);
      _applyFilters();
      debugPrint('تم تحميل ${messages.length} رسالة');
    } catch (e) {
      _error.value = 'خطأ في تحميل الرسائل: $e';
      debugPrint('خطأ في تحميل الرسائل: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الرسائل غير المقروءة
  Future<void> loadUnreadMessages() async {
    try {
      final messages = await _apiService.getUnreadMessages();
      _unreadMessages.assignAll(messages);
      debugPrint('تم تحميل ${messages.length} رسالة غير مقروءة');
    } catch (e) {
      debugPrint('خطأ في تحميل الرسائل غير المقروءة: $e');
    }
  }

  /// الحصول على رسالة بالمعرف
  Future<void> getMessageById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final message = await _apiService.getMessageById(id);
      _currentMessage.value = message;
      debugPrint('تم تحميل الرسالة: ${message?.content ?? "رسالة فارغة"}');
    } catch (e) {
      _error.value = 'خطأ في تحميل الرسالة: $e';
      debugPrint('خطأ في تحميل الرسالة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إرسال رسالة جديدة
  Future<bool> sendMessage(Message message) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newMessage = await _apiService.sendMessage(message);
      _allMessages.add(newMessage);
      _applyFilters();
      debugPrint('تم إرسال رسالة جديدة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إرسال الرسالة: $e';
      debugPrint('خطأ في إرسال الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث رسالة
  Future<bool> updateMessage(int id, Message message) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateMessage(id, message);
      final index = _allMessages.indexWhere((m) => m.id == id);
      if (index != -1) {
        _allMessages[index] = message;
        _applyFilters();
      }
      debugPrint('تم تحديث الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الرسالة: $e';
      debugPrint('خطأ في تحديث الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteMessage(id);
      _allMessages.removeWhere((m) => m.id == id);
      _applyFilters();
      debugPrint('تم حذف الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف الرسالة: $e';
      debugPrint('خطأ في حذف الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على رسائل المجموعة
  Future<void> getMessagesByGroup(int groupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final messages = await _apiService.getGroupMessages(groupId);
      _groupMessages.assignAll(messages);
      debugPrint('تم تحميل ${messages.length} رسالة للمجموعة $groupId');
    } catch (e) {
      _error.value = 'خطأ في تحميل رسائل المجموعة: $e';
      debugPrint('خطأ في تحميل رسائل المجموعة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديد الرسالة كمقروءة
  Future<bool> markAsRead(int messageId) async {
    try {
      await _apiService.markMessageAsRead(messageId);
      final index = _allMessages.indexWhere((m) => m.id == messageId);
      if (index != -1) {
        _allMessages[index] = _allMessages[index].copyWith(isRead: true);
        _applyFilters();
      }
      await loadUnreadMessages();
      debugPrint('تم تحديد الرسالة كمقروءة');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة كمقروءة: $e');
      return false;
    }
  }

  /// تحديد جميع الرسائل كمقروءة
  Future<bool> markAllAsRead() async {
    try {
      // تحديد جميع الرسائل في المجموعة الحالية كمقروءة
      if (_groupFilter.value != null) {
        await _apiService.markGroupMessagesAsRead(_groupFilter.value!);
      }
      for (int i = 0; i < _allMessages.length; i++) {
        _allMessages[i] = _allMessages[i].copyWith(isRead: true);
      }
      _applyFilters();
      await loadUnreadMessages();
      debugPrint('تم تحديد جميع الرسائل كمقروءة');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد جميع الرسائل كمقروءة: $e');
      return false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allMessages.where((message) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!message.content.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المجموعة
      if (_groupFilter.value != null && message.groupId != _groupFilter.value) {
        return false;
      }

      // مرشح المرسل
      if (_senderFilter.value != null && message.senderId != _senderFilter.value) {
        return false;
      }

      // مرشح غير المقروءة فقط
      if (_showUnreadOnly.value && message.isRead) {
        return false;
      }

      return true;
    }).toList();

    _filteredMessages.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المجموعة
  void setGroupFilter(int? groupId) {
    _groupFilter.value = groupId;
    _applyFilters();
  }

  /// تعيين مرشح المرسل
  void setSenderFilter(int? senderId) {
    _senderFilter.value = senderId;
    _applyFilters();
  }

  /// تعيين مرشح غير المقروءة فقط
  void setUnreadFilter(bool showUnreadOnly) {
    _showUnreadOnly.value = showUnreadOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _groupFilter.value = null;
    _senderFilter.value = null;
    _showUnreadOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllMessages(),
      loadUnreadMessages(),
    ]);
  }
}
