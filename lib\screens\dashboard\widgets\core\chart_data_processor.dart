import 'package:flutter/material.dart';
import '../../../../models/chart_enums.dart';
import '../../../../models/advanced_filter_options.dart';

/// معالج البيانات الموحد للمخططات
/// 
/// يوفر معالجة موحدة لجميع أنواع البيانات المستخدمة في المخططات
class ChartDataProcessor {
  /// تحويل البيانات الخام إلى تنسيق مناسب للمخطط
  static Map<String, dynamic> processData({
    required ChartType chartType,
    required Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  }) {
    switch (chartType) {
      case ChartType.bar:
        return _processBarChartData(rawData, filters);
      case ChartType.pie:
        return _processPieChartData(rawData, filters);
      case ChartType.line:
        return _processLineChartData(rawData, filters);
      case ChartType.radar:
        return _processRadarChartData(rawData, filters);
      case ChartType.bubble:
        return _processBubbleChartData(rawData, filters);
      default:
        return _processGenericData(rawData, filters);
    }
  }

  /// معالجة بيانات المخطط الشريطي
  static Map<String, dynamic> _processBarChartData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    final Map<String, double> data = {};
    
    if (rawData.containsKey('categories') && rawData.containsKey('values')) {
      final categories = rawData['categories'] as List;
      final values = rawData['values'] as List;
      
      for (int i = 0; i < categories.length && i < values.length; i++) {
        final category = categories[i].toString();
        final value = _parseDouble(values[i]);
        if (value != null) {
          data[category] = value;
        }
      }
    } else if (rawData is Map<String, dynamic>) {
      rawData.forEach((key, value) {
        final parsedValue = _parseDouble(value);
        if (parsedValue != null) {
          data[key] = parsedValue;
        }
      });
    }

    return {
      'data': data,
      'total': data.values.fold(0.0, (sum, value) => sum + value),
      'count': data.length,
      'max': data.values.isNotEmpty ? data.values.reduce((a, b) => a > b ? a : b) : 0.0,
      'min': data.values.isNotEmpty ? data.values.reduce((a, b) => a < b ? a : b) : 0.0,
    };
  }

  /// معالجة بيانات المخطط الدائري
  static Map<String, dynamic> _processPieChartData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    final barData = _processBarChartData(rawData, filters);
    final data = barData['data'] as Map<String, double>;
    final total = barData['total'] as double;

    // حساب النسب المئوية
    final Map<String, double> percentages = {};
    data.forEach((key, value) {
      percentages[key] = total > 0 ? (value / total) * 100 : 0.0;
    });

    return {
      'data': data,
      'percentages': percentages,
      'total': total,
      'count': data.length,
    };
  }

  /// معالجة بيانات المخطط الخطي
  static Map<String, dynamic> _processLineChartData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    final Map<String, List<ChartPoint>> series = {};
    
    if (rawData.containsKey('series')) {
      final seriesData = rawData['series'] as Map<String, dynamic>;
      
      seriesData.forEach((seriesName, points) {
        final List<ChartPoint> chartPoints = [];
        
        if (points is Map<String, dynamic>) {
          points.forEach((x, y) {
            final parsedY = _parseDouble(y);
            if (parsedY != null) {
              chartPoints.add(ChartPoint(x, parsedY));
            }
          });
        } else if (points is List) {
          for (int i = 0; i < points.length; i++) {
            final point = points[i];
            if (point is Map<String, dynamic>) {
              final x = point['x'];
              final y = _parseDouble(point['y']);
              if (y != null) {
                chartPoints.add(ChartPoint(x, y));
              }
            }
          }
        }
        
        if (chartPoints.isNotEmpty) {
          series[seriesName] = chartPoints;
        }
      });
    }

    return {
      'series': series,
      'seriesCount': series.length,
      'totalPoints': series.values.fold(0, (sum, points) => sum + points.length),
    };
  }

  /// معالجة بيانات المخطط الراداري
  static Map<String, dynamic> _processRadarChartData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    final Map<String, List<double>> data = {};
    final List<String> categories = [];

    if (rawData.containsKey('categories')) {
      categories.addAll((rawData['categories'] as List).map((e) => e.toString()));
    }

    if (rawData.containsKey('series')) {
      final seriesData = rawData['series'] as Map<String, dynamic>;
      
      seriesData.forEach((seriesName, values) {
        final List<double> seriesValues = [];
        if (values is List) {
          for (var value in values) {
            final parsedValue = _parseDouble(value);
            if (parsedValue != null) {
              seriesValues.add(parsedValue);
            }
          }
        }
        if (seriesValues.isNotEmpty) {
          data[seriesName] = seriesValues;
        }
      });
    }

    return {
      'data': data,
      'categories': categories,
      'seriesCount': data.length,
      'maxValue': data.values.isNotEmpty 
          ? data.values.expand((list) => list).reduce((a, b) => a > b ? a : b)
          : 0.0,
    };
  }

  /// معالجة بيانات المخطط الفقاعي
  static Map<String, dynamic> _processBubbleChartData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    final List<BubblePoint> points = [];

    if (rawData.containsKey('points')) {
      final pointsData = rawData['points'] as List;
      
      for (var point in pointsData) {
        if (point is Map<String, dynamic>) {
          final x = _parseDouble(point['x']);
          final y = _parseDouble(point['y']);
          final size = _parseDouble(point['size']);
          final label = point['label']?.toString() ?? '';
          
          if (x != null && y != null && size != null) {
            points.add(BubblePoint(x, y, size, label));
          }
        }
      }
    }

    return {
      'points': points,
      'count': points.length,
      'maxX': points.isNotEmpty ? points.map((p) => p.x).reduce((a, b) => a > b ? a : b) : 0.0,
      'maxY': points.isNotEmpty ? points.map((p) => p.y).reduce((a, b) => a > b ? a : b) : 0.0,
      'maxSize': points.isNotEmpty ? points.map((p) => p.size).reduce((a, b) => a > b ? a : b) : 0.0,
    };
  }

  /// معالجة البيانات العامة
  static Map<String, dynamic> _processGenericData(
    Map<String, dynamic> rawData,
    AdvancedFilterOptions? filters,
  ) {
    return {
      'rawData': rawData,
      'isEmpty': rawData.isEmpty,
      'keys': rawData.keys.toList(),
    };
  }

  /// تحويل القيمة إلى double
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  /// تطبيق الفلاتر على البيانات
  static Map<String, dynamic> applyFilters(
    Map<String, dynamic> data,
    AdvancedFilterOptions filters,
  ) {
    // TODO: تنفيذ منطق التصفية
    return data;
  }

  /// تنسيق القيم للعرض
  static String formatValue(double value, {String? format}) {
    if (format == 'percentage') {
      return '${value.toStringAsFixed(1)}%';
    } else if (format == 'currency') {
      return '${value.toStringAsFixed(2)} ر.س';
    } else if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}م';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}ك';
    }
    return value.toStringAsFixed(0);
  }
}

/// نقطة بيانات للمخطط الخطي
class ChartPoint {
  final dynamic x;
  final double y;

  ChartPoint(this.x, this.y);
}

/// نقطة بيانات للمخطط الفقاعي
class BubblePoint {
  final double x;
  final double y;
  final double size;
  final String label;

  BubblePoint(this.x, this.y, this.size, this.label);
}
