import 'package:flutter/material.dart';
import '../models/report_models.dart';
import '../services/api/reports_api_service.dart';

/// مستودع التقارير
class ReportRepository {
  final ReportsApiService _apiService = ReportsApiService();

  /// الحصول على تقرير بالمعرف
  Future<Report?> getReportById(String reportId) async {
    try {
      final id = int.tryParse(reportId);
      if (id == null) {
        debugPrint('معرف التقرير غير صحيح: $reportId');
        return null;
      }
      
      return await _apiService.getReport(id);
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير $reportId: $e');
      return null;
    }
  }

  /// الحصول على جميع التقارير
  Future<List<Report>> getAllReports() async {
    try {
      return await _apiService.getReports();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      return [];
    }
  }

  /// الحصول على التقارير العامة
  Future<List<Report>> getPublicReports() async {
    try {
      return await _apiService.getPublicReports();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير العامة: $e');
      return [];
    }
  }

  /// الحصول على تقارير المستخدم
  Future<List<Report>> getUserReports(int userId) async {
    try {
      return await _apiService.getUserReports(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المستخدم $userId: $e');
      return [];
    }
  }

  /// إنشاء تقرير جديد
  Future<Report?> createReport(Report report) async {
    try {
      return await _apiService.createReport(report);
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      return null;
    }
  }

  /// تحديث تقرير
  Future<Report?> updateReport(int id, Report report) async {
    try {
      return await _apiService.updateReport(id, report);
    } catch (e) {
      debugPrint('خطأ في تحديث التقرير: $e');
      return null;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(int id) async {
    try {
      await _apiService.deleteReport(id);
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير: $e');
      return false;
    }
  }

  /// البحث في التقارير
  Future<List<Report>> searchReports(String query) async {
    try {
      return await _apiService.searchReports(query);
    } catch (e) {
      debugPrint('خطأ في البحث في التقارير: $e');
      return [];
    }
  }

  /// الحصول على التقارير المفضلة
  Future<List<Report>> getFavoriteReports() async {
    try {
      return await _apiService.getFavoriteReports();
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير المفضلة: $e');
      return [];
    }
  }

  /// إضافة تقرير للمفضلة
  Future<bool> addToFavorites(int reportId) async {
    try {
      return await _apiService.addToFavorites(reportId);
    } catch (e) {
      debugPrint('خطأ في إضافة التقرير للمفضلة: $e');
      return false;
    }
  }

  /// إزالة تقرير من المفضلة
  Future<bool> removeFromFavorites(int reportId) async {
    try {
      return await _apiService.removeFromFavorites(reportId);
    } catch (e) {
      debugPrint('خطأ في إزالة التقرير من المفضلة: $e');
      return false;
    }
  }

  /// الحصول على التقارير حسب المنشئ
  Future<List<Report>> getReportsByCreator(int creatorId) async {
    try {
      return await _apiService.getUserReports(creatorId);
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المنشئ $creatorId: $e');
      return [];
    }
  }

  /// الحصول على التقارير المشتركة مع المستخدم
  Future<List<Report>> getReportsSharedWithUser(int userId) async {
    try {
      // في الوقت الحالي، نعيد قائمة فارغة حتى يتم تطوير هذه الميزة في API
      // يمكن تطوير هذا لاحقاً عندما يتم إضافة endpoint مخصص للتقارير المشتركة
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير المشتركة للمستخدم $userId: $e');
      return [];
    }
  }

  /// مشاركة تقرير مع مستخدمين
  Future<bool> shareReportWithUsers(int reportId, List<String> userIds) async {
    try {
      // في الوقت الحالي، نعيد true حتى يتم تطوير هذه الميزة في API
      // يمكن تطوير هذا لاحقاً عندما يتم إضافة endpoint مخصص لمشاركة التقارير
      debugPrint('مشاركة التقرير $reportId مع المستخدمين: $userIds');
      return true;
    } catch (e) {
      debugPrint('خطأ في مشاركة التقرير $reportId: $e');
      return false;
    }
  }
}
