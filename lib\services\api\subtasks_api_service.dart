import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/subtask_models.dart';

import 'api_service.dart';

/// خدمة API للمهام الفرعية - متطابقة مع ASP.NET Core API
class SubtasksApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المهام الفرعية
  Future<List<Subtask>> getAllSubtasks() async {
    try {
      final response = await _apiService.get('/api/Subtasks');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية: $e');
      rethrow;
    }
  }

  /// الحصول على مهمة فرعية بواسطة المعرف
  Future<Subtask?> getSubtaskById(int id) async {
    try {
      final response = await _apiService.get('/api/Subtasks/$id');
      return _apiService.handleResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهمة الفرعية $id: $e');
      return null;
    }
  }

  /// الحصول على المهام الفرعية لمهمة محددة
  Future<List<Subtask>> getSubtasksByTask(int taskId) async {
    try {
      final response = await _apiService.get('/api/Subtasks/task/$taskId');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية للمهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام الفرعية المكتملة لمهمة محددة
  Future<List<Subtask>> getCompletedSubtasksByTask(int taskId) async {
    try {
      final response = await _apiService.get('/api/Subtasks/task/$taskId/completed');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية المكتملة للمهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام الفرعية غير المكتملة لمهمة محددة
  Future<List<Subtask>> getPendingSubtasksByTask(int taskId) async {
    try {
      final response = await _apiService.get('/api/Subtasks/task/$taskId/pending');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية غير المكتملة للمهمة $taskId: $e');
      rethrow;
    }
  }

  /// إنشاء مهمة فرعية جديدة
  Future<Subtask> createSubtask(int taskId, String title) async {
    try {
      final requestData = {
        'taskId': taskId,
        'title': title,
      };
      debugPrint('JSON المرسل لإنشاء المهمة الفرعية: $requestData');
      debugPrint('TaskId: $taskId, Title: $title');
      final response = await _apiService.post(
        '/api/Subtasks',
        requestData,
      );
      debugPrint('📥 استجابة الخادم: ${response.statusCode}');
      debugPrint('📄 محتوى الاستجابة: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final result = _apiService.handleResponse<Subtask>(
            response,
            (json) {
              debugPrint('🔍 تحليل JSON للمهمة الفرعية: $json');
              return Subtask.fromJson(json);
            },
          );
          debugPrint('✅ تم تحليل المهمة الفرعية بنجاح');
          return result;
        } catch (parseError) {
          debugPrint('❌ خطأ في تحليل استجابة المهمة الفرعية: $parseError');
          debugPrint('📄 البيانات الخام: ${response.body}');
          rethrow;
        }
      } else {
        throw Exception('فشل في إنشاء المهمة الفرعية: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء المهمة الفرعية: $e');
      rethrow;
    }
  }

  /// تحديث مهمة فرعية
  Future<Subtask> updateSubtask(int id, Subtask subtask) async {
    try {
      debugPrint('تحديث المهمة الفرعية: Id=$id, Title=${subtask.title}');
      final response = await _apiService.put(
        '/api/Subtasks/$id',
        subtask.toJson(),
      );

      debugPrint('استجابة تحديث المهمة الفرعية: ${response.statusCode}');

      // التحقق من نجاح العملية (200 OK أو 204 No Content)
      if (response.statusCode == 204) {
        // إرجاع المهمة الفرعية المحدثة مع البيانات المرسلة
        debugPrint('تم التحديث بنجاح (204 No Content)');
        return subtask;
      }

      // إذا كانت الاستجابة تحتوي على بيانات، قم بتحليلها
      return _apiService.handleResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المهمة الفرعية $id: $e');
      rethrow;
    }
  }

  /// حذف مهمة فرعية
  Future<bool> deleteSubtask(int id) async {
    try {
      final response = await _apiService.delete('/api/Subtasks/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المهمة الفرعية $id: $e');
      return false;
    }
  }

  /// تحديد مهمة فرعية كمكتملة
  Future<bool> completeSubtask(int id) async {
    try {
      final response = await _apiService.patch('/api/Subtasks/$id/complete', {});
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد المهمة الفرعية كمكتملة: $e');
      return false;
    }
  }

  /// إلغاء إكمال مهمة فرعية
  Future<bool> uncompleteSubtask(int id) async {
    try {
      final response = await _apiService.patch('/api/Subtasks/$id/uncomplete', {});
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء إكمال المهمة الفرعية: $e');
      return false;
    }
  }

  /// تحديث ترتيب المهام الفرعية
  Future<bool> updateSubtasksOrder(int taskId, List<int> subtaskIds) async {
    try {
      final response = await _apiService.put(
        '/api/Subtasks/task/$taskId/reorder',
        {
          'subtaskIds': subtaskIds,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث ترتيب المهام الفرعية: $e');
      return false;
    }
  }

  /// نسخ المهام الفرعية من مهمة إلى أخرى
  Future<List<Subtask>> copySubtasks(int fromTaskId, int toTaskId) async {
    try {
      final response = await _apiService.post(
        '/api/Subtasks/copy',
        {
          'fromTaskId': fromTaskId,
          'toTaskId': toTaskId,
        },
      );
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في نسخ المهام الفرعية: $e');
      rethrow;
    }
  }

  /// نقل المهام الفرعية من مهمة إلى أخرى
  Future<bool> moveSubtasks(List<int> subtaskIds, int toTaskId) async {
    try {
      final response = await _apiService.put(
        '/api/Subtasks/move',
        {
          'subtaskIds': subtaskIds,
          'toTaskId': toTaskId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المهام الفرعية: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المهام الفرعية لمهمة محددة
  Future<Map<String, dynamic>> getSubtaskStatistics(int taskId) async {
    try {
      final response = await _apiService.get('/api/Subtasks/task/$taskId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المهام الفرعية: $e');
      return {};
    }
  }

  /// البحث في المهام الفرعية
  Future<List<Subtask>> searchSubtasks(String query) async {
    try {
      final response = await _apiService.get('/api/Subtasks/search?q=$query');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في المهام الفرعية: $e');
      return [];
    }
  }

  /// الحصول على المهام الفرعية المتأخرة
  Future<List<Subtask>> getOverdueSubtasks() async {
    try {
      final response = await _apiService.get('/api/Subtasks/overdue');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على المهام الفرعية المكتملة
  Future<List<Subtask>> getCompletedSubtasks() async {
    try {
      final response = await _apiService.get('/api/Subtasks/completed');
      return _apiService.handleListResponse<Subtask>(
        response,
        (json) => Subtask.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام الفرعية المكتملة: $e');
      return [];
    }
  }

  /// حذف جميع المهام الفرعية المكتملة لمهمة محددة
  Future<bool> deleteCompletedSubtasks(int taskId) async {
    try {
      final response = await _apiService.delete('/api/Subtasks/task/$taskId/completed');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المهام الفرعية المكتملة: $e');
      return false;
    }
  }

  /// تحديث نسبة إنجاز المهام الفرعية
  Future<bool> updateSubtaskProgress(int id, int percentage) async {
    try {
      final response = await _apiService.put(
        '/api/Subtasks/$id/progress',
        {
          'completionPercentage': percentage,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث نسبة إنجاز المهمة الفرعية: $e');
      return false;
    }
  }
}
