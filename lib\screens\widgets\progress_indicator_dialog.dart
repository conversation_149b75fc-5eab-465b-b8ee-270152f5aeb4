import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';

/// مربع حوار مؤشر التقدم
/// يعرض شريط تقدم للعمليات الطويلة مثل الرفع والتنزيل
/// 
/// TODO: تنفيذ مربع حوار مؤشر التقدم
/// - عرض شريط تقدم للعمليات الطويلة
/// - عرض نسبة التقدم والوقت المتبقي
/// - إمكانية إلغاء العملية
class ProgressIndicatorDialog extends StatelessWidget {
  /// عنوان مربع الحوار
  final String title;
  
  /// وصف العملية
  final String message;
  
  /// نسبة التقدم (0.0 - 1.0)
  final double progress;
  
  /// الوقت المتبقي بالثواني
  final int? remainingSeconds;
  
  /// دالة يتم استدعاؤها عند إلغاء العملية
  final VoidCallback? onCancel;
  
  /// إنشاء مربع حوار مؤشر التقدم
  const ProgressIndicatorDialog({
    super.key,
    required this.title,
    required this.message,
    required this.progress,
    this.remainingSeconds,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(message),
          const SizedBox(height: 16),
          
          // شريط التقدم
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            minHeight: 10,
            borderRadius: BorderRadius.circular(5),
          ),
          const SizedBox(height: 8),
          
          // نسبة التقدم والوقت المتبقي
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('${(progress * 100).toInt()}%'),
              if (remainingSeconds != null)
                Text(_formatRemainingTime(remainingSeconds!)),
            ],
          ),
        ],
      ),
      actions: [
        if (onCancel != null)
          TextButton(
            onPressed: () {
              onCancel!();
              Get.back();
            },
            child: Text('إلغاء'.tr),
          ),
      ],
    );
  }
  
  /// تنسيق الوقت المتبقي
  String _formatRemainingTime(int seconds) {
    if (seconds < 60) {
      return '$seconds ثانية'.tr;
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '$minutes دقيقة و $remainingSeconds ثانية'.tr;
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '$hours ساعة و $minutes دقيقة'.tr;
    }
  }
}
