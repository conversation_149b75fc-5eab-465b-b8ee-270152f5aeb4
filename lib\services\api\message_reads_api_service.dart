import 'package:flutter/foundation.dart';
import '../../models/message_read_models.dart';
import 'api_service.dart';

/// خدمة API لقراءة الرسائل - متطابقة مع MessageReadsController
class MessageReadsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع قراءات الرسائل
  Future<List<MessageRead>> getAllMessageReads() async {
    try {
      final response = await _apiService.get('/api/MessageReads');
      return _apiService.handleListResponse<MessageRead>(
        response,
        (json) => MessageRead.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل قراءات الرسائل: $e');
      rethrow;
    }
  }

  /// الحصول على قراءة رسالة محددة
  Future<MessageRead?> getMessageRead(int id) async {
    try {
      final response = await _apiService.get('/api/MessageReads/$id');
      return _apiService.handleResponse<MessageRead>(
        response,
        (json) => MessageRead.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل قراءة الرسالة $id: $e');
      return null;
    }
  }

  /// الحصول على قراءات رسالة محددة
  Future<List<MessageReadWithUser>> getMessageReadsByMessage(int messageId) async {
    try {
      final response = await _apiService.get('/api/MessageReads/message/$messageId');
      return _apiService.handleListResponse<MessageReadWithUser>(
        response,
        (json) => MessageReadWithUser.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل قراءات الرسالة $messageId: $e');
      return [];
    }
  }

  /// الحصول على قراءات مستخدم محدد
  Future<List<MessageRead>> getMessageReadsByUser(int userId) async {
    try {
      final response = await _apiService.get('/api/MessageReads/user/$userId');
      return _apiService.handleListResponse<MessageRead>(
        response,
        (json) => MessageRead.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل قراءات المستخدم $userId: $e');
      return [];
    }
  }

  /// تحديد رسالة كمقروءة
  Future<MessageRead?> markMessageAsRead(int messageId, int userId) async {
    try {
      final response = await _apiService.post('/api/MessageReads/mark-read', {
        'messageId': messageId,
        'userId': userId,
      });
      return _apiService.handleResponse<MessageRead>(
        response,
        (json) => MessageRead.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة $messageId كمقروءة للمستخدم $userId: $e');
      return null;
    }
  }

  /// حذف قراءة رسالة
  Future<bool> deleteMessageRead(int id) async {
    try {
      final response = await _apiService.delete('/api/MessageReads/$id');
      return response.statusCode == 204;
    } catch (e) {
      debugPrint('خطأ في حذف قراءة الرسالة $id: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات قراءة الرسائل
  Future<MessageReadStatistics?> getMessageReadStatistics() async {
    try {
      final response = await _apiService.get('/api/MessageReads/statistics');
      return _apiService.handleResponse<MessageReadStatistics>(
        response,
        (json) => MessageReadStatistics.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات قراءة الرسائل: $e');
      return null;
    }
  }

  /// التحقق من قراءة رسالة من قبل مستخدم
  Future<bool> isMessageReadByUser(int messageId, int userId) async {
    try {
      final reads = await getMessageReadsByMessage(messageId);
      return reads.any((read) => read.userId == userId);
    } catch (e) {
      debugPrint('خطأ في التحقق من قراءة الرسالة $messageId للمستخدم $userId: $e');
      return false;
    }
  }

  /// الحصول على عدد القراءات لرسالة
  Future<int> getMessageReadCount(int messageId) async {
    try {
      final reads = await getMessageReadsByMessage(messageId);
      return reads.length;
    } catch (e) {
      debugPrint('خطأ في تحميل عدد قراءات الرسالة $messageId: $e');
      return 0;
    }
  }

  /// الحصول على الرسائل غير المقروءة لمستخدم
  Future<List<int>> getUnreadMessageIds(int userId, List<int> messageIds) async {
    try {
      final userReads = await getMessageReadsByUser(userId);
      final readMessageIds = userReads.map((read) => read.messageId).toSet();
      return messageIds.where((id) => !readMessageIds.contains(id)).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الرسائل غير المقروءة للمستخدم $userId: $e');
      return messageIds; // إرجاع جميع الرسائل كغير مقروءة في حالة الخطأ
    }
  }

  /// تحديد عدة رسائل كمقروءة
  Future<List<MessageRead>> markMultipleMessagesAsRead(
    List<int> messageIds, 
    int userId
  ) async {
    final results = <MessageRead>[];
    
    for (final messageId in messageIds) {
      try {
        final result = await markMessageAsRead(messageId, userId);
        if (result != null) {
          results.add(result);
        }
      } catch (e) {
        debugPrint('خطأ في تحديد الرسالة $messageId كمقروءة: $e');
      }
    }
    
    return results;
  }

  /// الحصول على آخر رسائل مقروءة لمستخدم
  Future<List<MessageRead>> getRecentReadMessages(
    int userId, {
    int limit = 20,
  }) async {
    try {
      final allReads = await getMessageReadsByUser(userId);
      allReads.sort((a, b) => b.readAt.compareTo(a.readAt));
      return allReads.take(limit).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل آخر الرسائل المقروءة للمستخدم $userId: $e');
      return [];
    }
  }
}
