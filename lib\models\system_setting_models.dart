import 'user_model.dart';

/// نموذج إعدادات النظام - متطابق مع ASP.NET Core API
class SystemSetting {
  final int id;
  final String settingKey;
  final String settingValue;
  final String? settingGroup;
  final String? description;
  final int createdAt;
  final int? updatedAt;
  final int? createdBy;

  // Navigation properties
  final User? createdByUser;

  const SystemSetting({
    required this.id,
    required this.settingKey,
    required this.settingValue,
    this.settingGroup,
    this.description,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.createdByUser,
  });

  /// إنشاء SystemSetting من JSON (من API)
  factory SystemSetting.fromJson(Map<String, dynamic> json) {
    return SystemSetting(
      id: json['id'] as int,
      settingKey: json['settingKey'] as String,
      settingValue: json['settingValue'] as String,
      settingGroup: json['settingGroup'] as String?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      createdBy: json['createdBy'] as int?,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  /// تحويل SystemSetting إلى JSON (للإرسال إلى API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'settingKey': settingKey,
      'settingValue': settingValue,
      'settingGroup': settingGroup,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'createdBy': createdBy,
    };
  }

  /// إنشاء نسخة معدلة من SystemSetting
  SystemSetting copyWith({
    int? id,
    String? settingKey,
    String? settingValue,
    String? settingGroup,
    String? description,
    int? createdAt,
    int? updatedAt,
    int? createdBy,
    User? createdByUser,
  }) {
    return SystemSetting(
      id: id ?? this.id,
      settingKey: settingKey ?? this.settingKey,
      settingValue: settingValue ?? this.settingValue,
      settingGroup: settingGroup ?? this.settingGroup,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      createdByUser: createdByUser ?? this.createdByUser,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime =>
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemSetting && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SystemSetting(id: $id, key: $settingKey, value: $settingValue, group: $settingGroup)';
  }
}

/// نموذج طلب إنشاء إعداد نظام
class CreateSystemSettingRequest {
  final String settingKey;
  final String settingValue;
  final String? settingGroup;
  final String? description;

  const CreateSystemSettingRequest({
    required this.settingKey,
    required this.settingValue,
    this.settingGroup,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'settingKey': settingKey,
      'settingValue': settingValue,
      'settingGroup': settingGroup,
      'description': description,
    };
  }
}

/// نموذج طلب تحديث إعداد نظام
class UpdateSystemSettingRequest {
  final String? settingKey;
  final String? settingValue;
  final String? settingGroup;
  final String? description;

  const UpdateSystemSettingRequest({
    this.settingKey,
    this.settingValue,
    this.settingGroup,
    this.description,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (settingKey != null) data['settingKey'] = settingKey;
    if (settingValue != null) data['settingValue'] = settingValue;
    if (settingGroup != null) data['settingGroup'] = settingGroup;
    if (description != null) data['description'] = description;
    return data;
  }
}

/// مجموعات الإعدادات الشائعة
class SystemSettingGroups {
  static const String general = 'general';
  static const String security = 'security';
  static const String notifications = 'notifications';
  static const String email = 'email';
  static const String backup = 'backup';
  static const String ui = 'ui';
  static const String api = 'api';
  static const String database = 'database';
}

/// مفاتيح الإعدادات الشائعة
class SystemSettingKeys {
  // إعدادات عامة
  static const String appName = 'app_name';
  static const String appVersion = 'app_version';
  static const String defaultLanguage = 'default_language';
  static const String timezone = 'timezone';
  
  // إعدادات الأمان
  static const String passwordMinLength = 'password_min_length';
  static const String sessionTimeout = 'session_timeout';
  static const String maxLoginAttempts = 'max_login_attempts';
  
  // إعدادات الإشعارات
  static const String enableEmailNotifications = 'enable_email_notifications';
  static const String enablePushNotifications = 'enable_push_notifications';
  
  // إعدادات البريد الإلكتروني
  static const String smtpServer = 'smtp_server';
  static const String smtpPort = 'smtp_port';
  static const String smtpname = 'smtp_name';
  
  // إعدادات النسخ الاحتياطي
  static const String autoBackupEnabled = 'auto_backup_enabled';
  static const String backupInterval = 'backup_interval';
  static const String maxBackupFiles = 'max_backup_files';
}
