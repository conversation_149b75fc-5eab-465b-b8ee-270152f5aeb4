import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_models.dart';

import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';

/// عنصر شبكة وثيقة
///
/// يعرض معلومات الوثيقة في شكل بطاقة
class DocumentGridItem extends StatelessWidget {
  /// وثيقة الأرشيف
  final ArchiveDocument document;

  /// دالة يتم استدعاؤها عند النقر على العنصر
  final VoidCallback? onTap;

  const DocumentGridItem({
    super.key,
    required this.document,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final fileType = FileProcessor.getFileType(document.filePath);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة الملف
              Align(
                alignment: Alignment.center,
                child: Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: _getFileColor(fileType).withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getFileIcon(fileType),
                    color: _getFileColor(fileType),
                    size: 32,
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // عنوان الوثيقة
              Text(
                document.title,
                style: AppStyles.subtitle1,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),

              // وصف الوثيقة
              if (document.description != null && document.description!.isNotEmpty) ...[
                Text(
                  document.description!,
                  style: AppStyles.body2,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
              ],

              const Spacer(),

              // معلومات إضافية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(document.createdAt)),
                    style: AppStyles.caption,
                  ),
                  Text(
                    FileProcessor.formatFileSize(document.fileSize),
                    style: AppStyles.caption,
                  ),
                ],
              ),


            ],
          ),
        ),
      ),
    );
  }



  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }
}
