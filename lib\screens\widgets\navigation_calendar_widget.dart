import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/calendar_controller.dart' as app_calendar;
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../routes/app_routes.dart';
import '../../constants/app_colors.dart';
import '../calendar/calendar_event_form.dart';
import '../../utils/responsive_helper.dart';
import '../../models/calendar_models.dart';
import '../../services/unified_permission_service.dart';

/// ويدجت تقويم مصغر للشريط العلوي
/// يعرض التاريخ الحالي والأحداث القادمة بشكل احترافي
class NavigationCalendarWidget extends StatefulWidget {
  /// عرض الويدجت
  final double width;

  /// ارتفاع الويدجت
  final double height;

  /// عدد الأحداث التي يتم عرضها
  final int maxEvents;

  /// دالة يتم استدعاؤها عند النقر على التقويم
  final VoidCallback? onTap;

  /// ما إذا كان الويدجت في شريط التنقل
  final bool isInAppBar;

  const NavigationCalendarWidget({
    super.key,
    this.width = 300,
    this.height = 40,
    this.maxEvents = 2,
    this.onTap,
    this.isInAppBar = false,
  });

  @override
  State<NavigationCalendarWidget> createState() => _NavigationCalendarWidgetState();
}

class _NavigationCalendarWidgetState extends State<NavigationCalendarWidget> with SingleTickerProviderStateMixin {
  final app_calendar.CalendarController _calendarController = Get.find<app_calendar.CalendarController>();
  final _permissionService = Get.find<UnifiedPermissionService>();

  // تاريخ اليوم
  final DateTime _today = DateTime.now();

  // منسق التاريخ
  late final DateFormat _dateFormat;
  late final DateFormat _dayFormat;

  // متحكم الحركة
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _dateFormat = DateFormat.yMMMd(); // مثال: Jan 20, 2023
    _dayFormat = DateFormat.E(); // مثال: Mon

    // إعداد متحكم الحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // إنشاء حركة نبض خفيفة
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // تشغيل الحركة بشكل متكرر
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// عرض قائمة منبثقة بالأحداث القادمة
  void _showEventsPopupMenu(BuildContext context) {
    final events = _calendarController.getEventsForDay(_today);
    final upcomingEvents = events.where((e) =>
      e.startDateTime.isAfter(DateTime.now()) ||
      (e.startDateTime.isBefore(DateTime.now()) && e.endDateTime.isAfter(DateTime.now()))
    ).toList();

    upcomingEvents.sort((a, b) => a.startDateTime.compareTo(b.startDateTime));

    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final headerColor = isDarkMode
        ? AppColors.primary.withValues(alpha: 77) // 0.3 * 255 = 77
        : AppColors.primary.withValues(alpha: 26); // 0.1 * 255 = 26

    showMenu<String>(
      context: context,
      position: position,
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDarkMode ? Colors.grey.shade900 : Colors.white,
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          enabled: false,
          value: '',
          height: 48,
          child: Container(
            decoration: BoxDecoration(
              color: headerColor,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                Icon(
                  Icons.event,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'أحداث اليوم',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Text(
                  _dateFormat.format(_today),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
        const PopupMenuDivider(),
        if (upcomingEvents.isEmpty)
          PopupMenuItem<String>(
            enabled: false,
            value: 'none',
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.event_busy,
                    color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'لا توجد أحداث لهذا اليوم',
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          ...upcomingEvents.take(5).map((event) => PopupMenuItem<String>(
            value: event.id.toString(),
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  // أيقونة نوع الحدث
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: _parseColor(event.color)?.withValues(alpha: 51) ??
                             Theme.of(context).colorScheme.primary.withValues(alpha: 51),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        _getEventTypeIcon(event.eventType),
                        color: _parseColor(event.color) ?? Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event.title,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 12,
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${DateFormat.Hm().format(event.startDateTime)} - ${DateFormat.Hm().format(event.endDateTime)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).textTheme.bodySmall?.color,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ],
              ),
            ),
          )),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'add',
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 26), // 0.1 * 255 = 26
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_circle_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'إضافة حدث جديد',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    ).then((value) {
      if (value != null) {
        if (value == 'add') {
          // الانتقال إلى شاشة التقويم وإضافة حدث جديد
          Get.toNamed(AppRoutes.calendar);
          // استخدام Future.delayed لإعطاء وقت للانتقال إلى شاشة التقويم
          Future.delayed(const Duration(milliseconds: 300), () {
            // استدعاء دالة إضافة حدث جديد من خلال إنشاء نموذج جديد مباشرة
            Get.to(() => CalendarEventForm(
              onEventCreated: (event) {
                // تحديث التقويم بعد إضافة الحدث
                final calendarController = Get.find<app_calendar.CalendarController>();
                calendarController.loadEvents();
              },
            ));
          });
        } else {
          // الانتقال إلى تفاصيل الحدث
          // البحث عن الحدث أولاً
          final calendarController = Get.find<app_calendar.CalendarController>();
          final event = calendarController.events.firstWhere((e) => e.id.toString() == value);

          // الانتقال إلى شاشة تفاصيل الحدث
          Get.toNamed(AppRoutes.calendarEventDetails, arguments: {
            'event': event,
            'onEventUpdated': (updatedEvent) {
              calendarController.loadEvents();
            },
            'onEventDeleted': () {
              calendarController.loadEvents();
            },
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isSmallScreen = ResponsiveHelper.isMobile(context) && ResponsiveHelper.screenWidth(context) < 360;

    // تعديل العرض حسب حجم الشاشة وما إذا كان في شريط التنقل
    final effectiveWidth = widget.isInAppBar
        ? (isSmallScreen ? 180.0 : 220.0)
        : widget.width;

    // تعديل الارتفاع حسب ما إذا كان في شريط التنقل
    final effectiveHeight = widget.isInAppBar ? 36.0 : widget.height;

    // تحديد ألوان مختلفة حسب وضع السمة وما إذا كان في شريط التنقل
    final containerColor = widget.isInAppBar
        ? (isDarkMode
            ? AppColors.primary.withValues(alpha: 51) // 0.2 * 255 = 51
            : AppColors.primary.withValues(alpha: 38)) // 0.15 * 255 = 38
        : (isDarkMode
            ? Colors.grey.shade800.withValues(alpha: 77) // 0.3 * 255 = 77
            : Colors.white.withValues(alpha: 51)); // 0.2 * 255 = 51

    final borderColor = widget.isInAppBar
        ? (isDarkMode
            ? AppColors.primary.withValues(alpha: 77) // 0.3 * 255 = 77
            : AppColors.primary.withValues(alpha: 51)) // 0.2 * 255 = 51
        : (isDarkMode
            ? Colors.grey.shade700
            : Colors.white.withValues(alpha: 77)); // 0.3 * 255 = 77

    final textColor = widget.isInAppBar
        ? (isDarkMode ? Colors.white : Colors.white)
        : (isDarkMode ? Colors.white : AppColors.primary);

    return Hero(
      tag: 'navigation_calendar',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _permissionService.canManageCalendar() ? () {
            if (widget.onTap != null) {
              widget.onTap!();
            } else {
              // الانتقال إلى شاشة التقويم
              Get.toNamed(AppRoutes.calendar);
            }
          } : null,
          onLongPress: () {
            // عرض قائمة منبثقة بالأحداث القادمة
            _showEventsPopupMenu(context);
          },
          child: Container(
            width: effectiveWidth,
            height: effectiveHeight,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              gradient: widget.isInAppBar
                ? LinearGradient(
                    colors: [
                      isDarkMode
                        ? AppColors.primary.withValues(alpha: 51) // 0.2 * 255 = 51
                        : AppColors.primary.withValues(alpha: 38), // 0.15 * 255 = 38
                      isDarkMode
                        ? AppColors.primary.withValues(alpha: 77) // 0.3 * 255 = 77
                        : AppColors.primary.withValues(alpha: 64), // 0.25 * 255 = 64
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
              color: widget.isInAppBar ? null : containerColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: borderColor,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 51) // 0.2 * 255 = 51
                      : Colors.black.withValues(alpha: 26), // 0.1 * 255 = 26
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة التقويم مع حركة نبض
                _buildCalendarIcon(context, isDarkMode),
                const SizedBox(width: 8),

                // معلومات الأحداث
                Expanded(
                  child: GetBuilder<app_calendar.CalendarController>(
                    builder: (controller) {
                      final events = controller.getEventsForDay(_today);
                      final upcomingEvents = events.where((e) =>
                        e.startDateTime.isAfter(DateTime.now()) ||
                        (e.startDateTime.isBefore(DateTime.now()) && e.endDateTime.isAfter(DateTime.now()))
                      ).toList();

                      upcomingEvents.sort((a, b) => a.startDateTime.compareTo(b.startDateTime));

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _dateFormat.format(_today),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: textColor,
                            ),
                          ),
                          _buildEventCountText(upcomingEvents, isDarkMode, textColor),
                        ],
                      );
                    },
                  ),
                ),

                // أيقونة الانتقال
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: textColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة التقويم المتحركة
  Widget _buildCalendarIcon(BuildContext context, bool isDarkMode) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary.withValues(alpha: 179), // 0.7 * 255 = 179
                  Theme.of(context).colorScheme.primary,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 77), // 0.3 * 255 = 77
                  blurRadius: 4 * _pulseAnimation.value,
                  spreadRadius: 1 * _pulseAnimation.value,
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _dayFormat.format(_today),
                      style: const TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _today.day.toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),

                // إضافة مؤشر للأحداث
                Positioned(
                  top: 2,
                  right: 2,
                  child: GetBuilder<app_calendar.CalendarController>(
                    builder: (controller) {
                      final events = controller.getEventsForDay(_today);
                      if (events.isEmpty) return const SizedBox.shrink();

                      return Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// الحصول على أيقونة مناسبة لنوع الحدث
  IconData _getEventTypeIcon(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Icons.task_alt;
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.reminder:
        return Icons.notifications_active;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
      default:
        return Icons.event;
    }
  }

  /// بناء نص عدد الأحداث
  Widget _buildEventCountText(List<CalendarEvent> upcomingEvents, bool isDarkMode, Color textColor) {
    if (upcomingEvents.isEmpty) {
      return Text(
        'لا توجد أحداث اليوم',
        style: TextStyle(
          fontSize: 10,
          color: textColor.withValues(alpha: 204), // 0.8 * 255 = 204
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // تصنيف الأحداث حسب النوع
    final int meetingCount = upcomingEvents.where((e) => e.eventType == CalendarEventType.meeting).length;
    final int taskCount = upcomingEvents.where((e) => e.eventType == CalendarEventType.task).length;
    final int otherCount = upcomingEvents.length - meetingCount - taskCount;

    // إذا كان هناك نوع واحد فقط من الأحداث
    if (meetingCount > 0 && taskCount == 0 && otherCount == 0) {
      return Text(
        meetingCount > 1 ? '$meetingCount اجتماعات' : 'اجتماع واحد',
        style: TextStyle(
          fontSize: 10,
          color: textColor.withValues(alpha: 204), // 0.8 * 255 = 204
        ),
        overflow: TextOverflow.ellipsis,
      );
    } else if (taskCount > 0 && meetingCount == 0 && otherCount == 0) {
      return Text(
        taskCount > 1 ? '$taskCount مهام' : 'مهمة واحدة',
        style: TextStyle(
          fontSize: 10,
          color: textColor.withValues(alpha: 204), // 0.8 * 255 = 204
        ),
        overflow: TextOverflow.ellipsis,
      );
    } else {
      // إذا كان هناك أنواع مختلفة من الأحداث
      return Text(
        upcomingEvents.length > 1
            ? '${upcomingEvents.length} أحداث اليوم'
            : 'حدث واحد اليوم',
        style: TextStyle(
          fontSize: 10,
          color: textColor.withValues(alpha: 204), // 0.8 * 255 = 204
        ),
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  /// تحليل اللون من النص
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      // إزالة # إذا كانت موجودة
      String cleanColor = colorString.replaceAll('#', '');

      // إضافة FF للشفافية إذا لم تكن موجودة
      if (cleanColor.length == 6) {
        cleanColor = 'FF$cleanColor';
      }

      return Color(int.parse(cleanColor, radix: 16));
    } catch (e) {
      return null;
    }
  }
}
