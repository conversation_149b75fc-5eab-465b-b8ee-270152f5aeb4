import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../utils/app_config.dart';
import '../../models/auth_models.dart';
import '../storage_service.dart';


/// خدمة API الأساسية للتعامل مع ASP.NET Core API
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  StorageService? _storageService;
  
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiresAt;

  /// الحصول على رمز الوصول الحالي
  String? get accessToken => _accessToken;

  /// تهيئة الخدمة وتحميل الرموز المحفوظة
  Future<void> initialize() async {
    try {
      _storageService = StorageService.instance;
    } catch (e) {
      debugPrint('تحذير: لم يتم تهيئة StorageService: $e');
    }
    await _loadTokensFromStorage();
  }

  /// تحميل الرموز من التخزين المحلي
  Future<void> _loadTokensFromStorage() async {
    if (_storageService == null) {
      debugPrint('StorageService غير متاح');
      return;
    }

    try {
      final sessionData = await _storageService!.getSessionData();
      if (sessionData != null && !sessionData.isExpired) {
        _accessToken = sessionData.accessToken;
        _refreshToken = sessionData.refreshToken;
        _tokenExpiresAt = sessionData.expiresAt;
        debugPrint('تم تحميل الرموز من التخزين المحلي');
      } else {
        debugPrint('لا توجد جلسة صالحة في التخزين المحلي');
        await clearTokens();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الرموز: $e');
      await clearTokens();
    }
  }

  /// حفظ الرموز في التخزين المحلي
  Future<void> _saveTokensToStorage(AuthResponse authResponse) async {
    if (authResponse.isValid && _storageService != null) {
      _accessToken = authResponse.accessToken;
      _refreshToken = authResponse.refreshToken;
      _tokenExpiresAt = authResponse.expiresAt;

      final sessionData = SessionData(
        accessToken: authResponse.accessToken!,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt!,
        user: authResponse.user!,
        savedAt: DateTime.now(),
      );

      await _storageService!.saveSessionData(sessionData);
      debugPrint('تم حفظ الرموز في التخزين المحلي');
    }
  }

  /// مسح الرموز
  Future<void> clearTokens() async {
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiresAt = null;
    if (_storageService != null) {
      await _storageService!.clearSessionData();
    }
    debugPrint('تم مسح الرموز');
  }

  /// التحقق من صحة الرمز وتحديثه إذا لزم الأمر
  Future<bool> _ensureValidToken() async {
    if (_accessToken == null) {
      debugPrint('❌ لا يوجد رمز وصول - المستخدم غير مسجل دخول');
      return false;
    }

    debugPrint('✅ يوجد رمز وصول - التحقق من صلاحيته');

    // التحقق من انتهاء صلاحية الرمز
    if (_tokenExpiresAt != null && DateTime.now().isAfter(_tokenExpiresAt!)) {
      debugPrint('انتهت صلاحية الرمز، محاولة التحديث...');
      return await _refreshAccessToken();
    }

    // التحقق من الحاجة لتحديث الرمز (قبل انتهاء الصلاحية بـ 5 دقائق)
    if (_tokenExpiresAt != null) {
      final refreshTime = _tokenExpiresAt!.subtract(const Duration(minutes: 5));
      if (DateTime.now().isAfter(refreshTime)) {
        debugPrint('الرمز يحتاج للتحديث قريباً، محاولة التحديث...');
        await _refreshAccessToken(); // لا نرجع false إذا فشل التحديث
      }
    }

    return true;
  }

  /// تحديث رمز الوصول
  Future<bool> _refreshAccessToken() async {
    if (_refreshToken == null) {
      debugPrint('لا يوجد رمز تحديث');
      return false;
    }

    try {
      final request = RefreshTokenRequest(refreshToken: _refreshToken!);
      final response = await _makeRequest(
        'POST',
        '/api/Auth/refresh-token',
        body: request.toJson(),
        requireAuth: false, // لا نحتاج مصادقة لتحديث الرمز
      );

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(jsonDecode(response.body));
        if (authResponse.isValid) {
          await _saveTokensToStorage(authResponse);
          debugPrint('تم تحديث الرمز بنجاح');
          return true;
        }
      }

      debugPrint('فشل في تحديث الرمز: ${response.statusCode}');
      await clearTokens();
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث الرمز: $e');
      await clearTokens();
      return false;
    }
  }

  /// إنشاء طلب HTTP
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    bool requireAuth = true,
  }) async {
    // التحقق من الرمز إذا كان مطلوباً
    if (requireAuth && !await _ensureValidToken()) {
      throw ApiException('غير مصرح - يرجى تسجيل الدخول مرة أخرى', 401);
    }

    // إنشاء URI مع query parameters إذا كانت موجودة
    Uri uri;
    if (queryParams != null && queryParams.isNotEmpty) {
      // تحويل Map<String, dynamic> إلى Map<String, String>
      final stringParams = queryParams.map((key, value) => MapEntry(key, value.toString()));
      uri = Uri.parse('${AppConfig.apiUrl}$endpoint').replace(queryParameters: stringParams);
    } else {
      uri = Uri.parse('${AppConfig.apiUrl}$endpoint');
    }
    
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...?headers,
    };

    // إضافة رمز المصادقة إذا كان متوفراً ومطلوباً
    if (requireAuth && _accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $_accessToken';
      debugPrint('🔐 تم إضافة رمز المصادقة للطلب');
    } else if (requireAuth) {
      debugPrint('⚠️ مطلوب مصادقة لكن لا يوجد رمز وصول');
    }

    http.Response response;
    final bodyJson = body != null ? jsonEncode(body) : null;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'PUT':
          response = await http.put(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'PATCH':
          response = await http.patch(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw ApiException('طريقة HTTP غير مدعومة: $method', 400);
      }

      debugPrint('API Request: $method $endpoint - Status: ${response.statusCode}');

      // معالجة خاصة لحالة 307 Redirect
      if (response.statusCode == 307) {
        debugPrint('تم اكتشاف إعادة توجيه 307. محاولة إعادة الطلب...');

        // محاولة الحصول على عنوان إعادة التوجيه من headers
        final location = response.headers['location'];
        if (location != null) {
          debugPrint('عنوان إعادة التوجيه: $location');

          // إعادة إنشاء الطلب مع العنوان الجديد
          final newUri = Uri.parse(location);

          switch (method.toUpperCase()) {
            case 'GET':
              response = await http.get(newUri, headers: requestHeaders);
              break;
            case 'POST':
              response = await http.post(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'PUT':
              response = await http.put(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'PATCH':
              response = await http.patch(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'DELETE':
              response = await http.delete(newUri, headers: requestHeaders);
              break;
          }

          debugPrint('API Request (بعد إعادة التوجيه): $method $location - Status: ${response.statusCode}');
        }
      }

      return response;
    } on SocketException {
      throw ApiException('لا يمكن الاتصال بالخادم. تحقق من اتصال السرفر.', 0);
    } on HttpException catch (e) {
      throw ApiException('خطأ في الشبكة: ${e.message}', 0);
    } catch (e) {
      throw ApiException('خطأ غير متوقع: $e', 0);
    }
  }

  /// طلب GET
  Future<http.Response> get(String endpoint, {Map<String, String>? headers, Map<String, dynamic>? queryParams, bool requireAuth = true}) {
    return _makeRequest('GET', endpoint, headers: headers, queryParams: queryParams, requireAuth: requireAuth);
  }

  /// طلب POST
  Future<http.Response> post(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
    bool requireAuth = true,
  }) {
    return _makeRequest('POST', endpoint,
        body: body, headers: headers, requireAuth: requireAuth);
  }

  /// طلب PUT
  Future<http.Response> put(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
  }) {
    return _makeRequest('PUT', endpoint, body: body, headers: headers);
  }

  /// طلب PATCH
  Future<http.Response> patch(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
  }) {
    return _makeRequest('PATCH', endpoint, body: body, headers: headers);
  }

  /// طلب DELETE
  Future<http.Response> delete(String endpoint, {Map<String, String>? headers}) {
    return _makeRequest('DELETE', endpoint, headers: headers);
  }

  /// طلب POST لـ multipart/form-data (لرفع الملفات)
  Future<http.Response> postMultipart(
    String endpoint, {
    required File file,
    Map<String, String>? fields,
    Map<String, String>? headers,
    bool requireAuth = true,
    Function(double)? onUploadProgress,
  }) async {
    if (requireAuth && !await _ensureValidToken()) {
      throw ApiException('غير مصرح - يرجى تسجيل الدخول مرة أخرى', 401);
    }

    final uri = Uri.parse('${AppConfig.apiUrl}$endpoint');
    final request = http.MultipartRequest('POST', uri);

    // إضافة الحقول النصية
    if (fields != null) {
      request.fields.addAll(fields);
    }

    // إضافة الملف
    request.files.add(await http.MultipartFile.fromPath(
      'file', // اسم الحقل للملف في الخادم
      file.path,
      // filename: file.path.split('/').last, // يمكنك تحديد اسم الملف إذا لزم الأمر
    ));

    // إضافة الهيدرز
    final requestHeaders = <String, String>{
      // 'Content-Type': 'multipart/form-data' يتم تعيينه تلقائياً بواسطة http.MultipartRequest
      'Accept': 'application/json',
      ...?headers,
    };
    if (requireAuth && _accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $_accessToken';
    }
    request.headers.addAll(requestHeaders);

    try {
      // TODO: Implement onUploadProgress if http package supports it directly or use a different client
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      debugPrint('API Multipart Request: POST $endpoint - Status: ${response.statusCode}');
      return response;
    } on SocketException {
      throw ApiException('لا يمكن الاتصال بالخادم. تحقق من اتصال السرفر.', 0);
    } catch (e) {
      throw ApiException('خطأ غير متوقع في طلب multipart: $e', 0);
    }
  }

  /// معالجة استجابة API وإرجاع البيانات أو رمي استثناء
  T handleResponse<T>(
    http.Response response,
    T Function(dynamic) fromJson,
  ) {
    debugPrint('API Response Status: ${response.statusCode}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        // تحسين معالجة الترميز العربي للاستجابات المفردة
        String responseBodyString;
        try {
          // محاولة فك الترميز بـ UTF-8 أولاً
          responseBodyString = utf8.decode(response.bodyBytes);
          debugPrint('✅ تم فك الترميز بـ UTF-8 بنجاح للاستجابة المفردة');
        } catch (e) {
          // في حالة فشل UTF-8، استخدام النص الخام
          responseBodyString = response.body;
          debugPrint('⚠️ فشل فك الترميز بـ UTF-8 للاستجابة المفردة، استخدام النص الخام: $e');
        }

        debugPrint('API Response Body: ${responseBodyString.length > 500 ? '${responseBodyString.substring(0, 500)}...' : responseBodyString}');

        final data = jsonDecode(responseBodyString);

        // طباعة عينة من البيانات للتحقق من الترميز
        if (data is Map<String, dynamic> && data.containsKey('title')) {
          debugPrint('🔤 عنوان المهمة في الاستجابة المفردة: ${data['title']}');
        }

        return fromJson(data);
      } catch (e) {
        debugPrint('❌ خطأ في تحليل البيانات: $e');
        debugPrint('📄 البيانات الخام: ${response.body}');
        debugPrint('🔍 نوع البيانات المتوقع: $T');

        // معلومات إضافية للتشخيص
        if (e is FormatException) {
          debugPrint('💡 خطأ في تنسيق JSON');
        } else if (e.toString().contains('type')) {
          debugPrint('💡 خطأ في نوع البيانات - تحقق من تطابق النموذج');
        }

        throw ApiException('خطأ في تحليل البيانات من الخادم: ${e.toString()}', response.statusCode);
      }
    } else {
      String errorMessage = _extractErrorMessage(response);
      debugPrint('API Error: $errorMessage (Status: ${response.statusCode})');
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// استخراج رسالة الخطأ من استجابة الخادم
  String _extractErrorMessage(http.Response response) {
    try {
      // محاولة فك الترميز أولاً
      String responseBody;
      try {
        responseBody = utf8.decode(response.bodyBytes);
      } catch (e) {
        responseBody = response.body;
      }

      if (responseBody.isEmpty) {
        return _getDefaultErrorMessage(response.statusCode);
      }

      // محاولة تحليل JSON
      dynamic errorData;
      try {
        errorData = jsonDecode(responseBody);
      } catch (e) {
        debugPrint('فشل في تحليل JSON، الاستجابة كنص: $responseBody');
        // إذا فشل تحليل JSON، فالاستجابة قد تكون string مباشرة
        String message = responseBody.trim();
        // إزالة علامات الاقتباس إذا كانت موجودة
        if (message.startsWith('"') && message.endsWith('"')) {
          message = message.substring(1, message.length - 1);
        }
        return message.isNotEmpty ? message : _getDefaultErrorMessage(response.statusCode);
      }

      // إذا كانت البيانات Map
      if (errorData is Map<String, dynamic>) {
        // محاولة استخراج الرسالة من مختلف الحقول المحتملة
        if (errorData.containsKey('message') && errorData['message'] != null) {
          return errorData['message'].toString();
        }

        if (errorData.containsKey('Message') && errorData['Message'] != null) {
          return errorData['Message'].toString();
        }

        if (errorData.containsKey('error') && errorData['error'] != null) {
          return errorData['error'].toString();
        }

        if (errorData.containsKey('title') && errorData['title'] != null) {
          return errorData['title'].toString();
        }

        // إذا كانت الاستجابة تحتوي على كائن AuthResponse
        if (errorData.containsKey('success') && errorData['success'] == false) {
          return errorData['message']?.toString() ?? 'فشل في العملية';
        }

        // إذا لم نجد رسالة محددة، نحاول استخدام أول قيمة string
        for (var value in errorData.values) {
          if (value is String && value.isNotEmpty) {
            return value;
          }
        }
      } else if (errorData is String) {
        // إذا كانت البيانات string مباشرة
        return errorData.isNotEmpty ? errorData : _getDefaultErrorMessage(response.statusCode);
      }

    } catch (e) {
      debugPrint('خطأ في تحليل رسالة الخطأ: $e');
    }

    return _getDefaultErrorMessage(response.statusCode);
  }

  /// الحصول على رسالة خطأ افتراضية حسب رمز الحالة
  String _getDefaultErrorMessage(int statusCode) {
    // رسائل خطأ افتراضية حسب رمز الحالة
    switch (statusCode) {
      case 400:
        return 'بيانات غير صحيحة';
      case 401:
        return 'غير مصرح - يرجى المحاولة مرة أخرى';
      case 403:
        return 'غير مصرح لك بالوصول';
      case 404:
        return 'الصفحة المطلوبة غير موجودة';
      case 307:
        return 'خطأ في عنوان الخادم - يرجى التحقق من إعدادات الاتصال';
      case 409:
        return 'البيانات موجودة بالفعل';
      case 500:
        return 'خطأ في الخادم';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  /// معالجة استجابة قائمة من API
  List<T> handleListResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    debugPrint('🔍 معالجة استجابة القائمة - Status: ${response.statusCode}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        if (response.body.isEmpty) {
          debugPrint('⚠️ استجابة فارغة من الخادم');
          return [];
        }

        // تحسين معالجة الترميز العربي
        String responseBodyString;
        try {
          // محاولة فك الترميز بـ UTF-8 أولاً
          responseBodyString = utf8.decode(response.bodyBytes);
          debugPrint('✅ تم فك الترميز بـ UTF-8 بنجاح');
        } catch (e) {
          // في حالة فشل UTF-8، استخدام النص الخام
          responseBodyString = response.body;
          debugPrint('⚠️ فشل فك الترميز بـ UTF-8، استخدام النص الخام: $e');
        }

        final responseBody = jsonDecode(responseBodyString);
        debugPrint('📊 نوع البيانات المستلمة: ${responseBody.runtimeType}');

        // طباعة عينة من البيانات للتحقق من الترميز
        if (responseBody is List && responseBody.isNotEmpty) {
          final firstItem = responseBody.first;
          if (firstItem is Map<String, dynamic> && firstItem.containsKey('title')) {
            debugPrint('🔤 عينة من عنوان المهمة: ${firstItem['title']}');
          }
        }

        // التحقق من نوع الاستجابة
        if (responseBody is List) {
          // إذا كانت الاستجابة قائمة مباشرة
          debugPrint('📋 معالجة قائمة مباشرة تحتوي على ${responseBody.length} عنصر');
          return responseBody.map((item) {
            try {
              return fromJson(item as Map<String, dynamic>);
            } catch (e) {
              debugPrint('❌ خطأ في تحليل عنصر: $e');
              debugPrint('📄 بيانات العنصر: $item');
              rethrow;
            }
          }).toList();
        } else if (responseBody is Map<String, dynamic>) {
          // إذا كانت الاستجابة كائن يحتوي على قائمة في خاصية 'data'
          if (responseBody.containsKey('data') && responseBody['data'] is List) {
            final data = responseBody['data'] as List<dynamic>;
            debugPrint('📋 معالجة قائمة في خاصية data تحتوي على ${data.length} عنصر');
            return data.map((item) {
              try {
                return fromJson(item as Map<String, dynamic>);
              } catch (e) {
                debugPrint('❌ خطأ في تحليل عنصر من data: $e');
                debugPrint('📄 بيانات العنصر: $item');
                rethrow;
              }
            }).toList();
          } else {
            // إذا كان الكائن لا يحتوي على 'data'، نتحقق من الخصائص الأخرى
            debugPrint('⚠️ الكائن لا يحتوي على خاصية data');
            debugPrint('🔑 المفاتيح المتاحة: ${responseBody.keys.toList()}');

            // محاولة البحث عن مفاتيح أخرى محتملة
            final possibleKeys = ['items', 'results', 'list', 'tasks', 'users'];
            for (final key in possibleKeys) {
              if (responseBody.containsKey(key) && responseBody[key] is List) {
                final data = responseBody[key] as List<dynamic>;
                debugPrint('📋 وجدت قائمة في خاصية $key تحتوي على ${data.length} عنصر');
                return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
              }
            }

            // إذا لم نجد أي قائمة، إرجاع قائمة فارغة
            debugPrint('📭 لم يتم العثور على قائمة في الاستجابة');
            return [];
          }
        } else {
          debugPrint('❌ تنسيق استجابة غير متوقع: ${responseBody.runtimeType}');
          throw ApiException('تنسيق استجابة غير متوقع', response.statusCode);
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحليل البيانات: $e');
        debugPrint('📄 محتوى الاستجابة: ${response.body.substring(0, response.body.length > 1000 ? 1000 : response.body.length)}');
        throw ApiException('خطأ في تحليل البيانات: $e', response.statusCode);
      }
    } else {
      String errorMessage = _extractErrorMessage(response);
      debugPrint('❌ خطأ في الخادم: $errorMessage (Status: ${response.statusCode})');
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// حفظ بيانات المصادقة من AuthResponse
  Future<void> saveAuthResponse(AuthResponse authResponse) async {
    await _saveTokensToStorage(authResponse);
  }

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _accessToken != null &&
      (_tokenExpiresAt == null || DateTime.now().isBefore(_tokenExpiresAt!));

  /// تسجيل الخروج
  Future<void> logout() async {
    await clearTokens();
  }
}

/// استثناء API
class ApiException implements Exception {
  final String message;
  final int statusCode;

  const ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';

  /// التحقق من كون الخطأ خطأ مصادقة
  bool get isAuthError => statusCode == 401 || statusCode == 403;

  /// التحقق من كون الخطأ خطأ شبكة
  bool get isNetworkError => statusCode == 0;
}
