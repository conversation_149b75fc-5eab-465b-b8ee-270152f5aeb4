import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path/path.dart' as path;

/// خدمة التعامل مع الملفات
/// 
/// توفر وظائف للتعامل مع الملفات مثل المشاركة والفتح والحذف
class FileHandlerService {
  
  /// مشاركة ملف
  /// 
  /// [filePath] مسار الملف المراد مشاركته
  /// [subject] موضوع المشاركة (اختياري)
  /// [text] نص إضافي للمشاركة (اختياري)
  Future<void> shareFile(
    String filePath, {
    String? subject,
    String? text,
  }) async {
    try {
      final file = File(filePath);
      
      // التحقق من وجود الملف
      if (!await file.exists()) {
        throw Exception('الملف غير موجود: $filePath');
      }

      // الحصول على اسم الملف
      final fileName = path.basename(filePath);
      
      // مشاركة الملف
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject ?? 'مشاركة ملف: $fileName',
        text: text ?? 'تم مشاركة الملف: $fileName',
      );
      
      debugPrint('تم مشاركة الملف بنجاح: $filePath');
    } catch (e) {
      debugPrint('خطأ في مشاركة الملف: $e');
      rethrow;
    }
  }

  /// مشاركة عدة ملفات
  /// 
  /// [filePaths] قائمة بمسارات الملفات المراد مشاركتها
  /// [subject] موضوع المشاركة (اختياري)
  /// [text] نص إضافي للمشاركة (اختياري)
  Future<void> shareMultipleFiles(
    List<String> filePaths, {
    String? subject,
    String? text,
  }) async {
    try {
      final xFiles = <XFile>[];
      
      // التحقق من وجود الملفات وإضافتها للقائمة
      for (final filePath in filePaths) {
        final file = File(filePath);
        if (await file.exists()) {
          xFiles.add(XFile(filePath));
        } else {
          debugPrint('تحذير: الملف غير موجود: $filePath');
        }
      }
      
      if (xFiles.isEmpty) {
        throw Exception('لا توجد ملفات صالحة للمشاركة');
      }
      
      // مشاركة الملفات
      await Share.shareXFiles(
        xFiles,
        subject: subject ?? 'مشاركة ملفات (${xFiles.length})',
        text: text ?? 'تم مشاركة ${xFiles.length} ملف',
      );
      
      debugPrint('تم مشاركة ${xFiles.length} ملف بنجاح');
    } catch (e) {
      debugPrint('خطأ في مشاركة الملفات: $e');
      rethrow;
    }
  }

  /// حذف ملف
  /// 
  /// [filePath] مسار الملف المراد حذفه
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      
      if (await file.exists()) {
        await file.delete();
        debugPrint('تم حذف الملف بنجاح: $filePath');
        return true;
      } else {
        debugPrint('الملف غير موجود: $filePath');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// نسخ ملف
  /// 
  /// [sourcePath] مسار الملف المصدر
  /// [destinationPath] مسار الملف الوجهة
  Future<bool> copyFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      
      if (!await sourceFile.exists()) {
        throw Exception('الملف المصدر غير موجود: $sourcePath');
      }
      
      await sourceFile.copy(destinationPath);
      debugPrint('تم نسخ الملف بنجاح من $sourcePath إلى $destinationPath');
      return true;
    } catch (e) {
      debugPrint('خطأ في نسخ الملف: $e');
      return false;
    }
  }

  /// نقل ملف
  /// 
  /// [sourcePath] مسار الملف المصدر
  /// [destinationPath] مسار الملف الوجهة
  Future<bool> moveFile(String sourcePath, String destinationPath) async {
    try {
      final sourceFile = File(sourcePath);
      
      if (!await sourceFile.exists()) {
        throw Exception('الملف المصدر غير موجود: $sourcePath');
      }
      
      await sourceFile.rename(destinationPath);
      debugPrint('تم نقل الملف بنجاح من $sourcePath إلى $destinationPath');
      return true;
    } catch (e) {
      debugPrint('خطأ في نقل الملف: $e');
      return false;
    }
  }

  /// الحصول على معلومات الملف
  /// 
  /// [filePath] مسار الملف
  Future<Map<String, dynamic>?> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        return null;
      }
      
      final stat = await file.stat();
      final fileName = path.basename(filePath);
      final fileExtension = path.extension(filePath);
      
      return {
        'name': fileName,
        'path': filePath,
        'extension': fileExtension,
        'size': stat.size,
        'modified': stat.modified,
        'accessed': stat.accessed,
        'changed': stat.changed,
        'type': stat.type.toString(),
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الملف: $e');
      return null;
    }
  }

  /// تنسيق حجم الملف
  /// 
  /// [sizeInBytes] حجم الملف بالبايت
  String formatFileSize(int sizeInBytes) {
    if (sizeInBytes < 1024) {
      return '$sizeInBytes بايت';
    } else if (sizeInBytes < 1024 * 1024) {
      final sizeInKB = (sizeInBytes / 1024).toStringAsFixed(1);
      return '$sizeInKB كيلوبايت';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      final sizeInMB = (sizeInBytes / (1024 * 1024)).toStringAsFixed(1);
      return '$sizeInMB ميجابايت';
    } else {
      final sizeInGB = (sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1);
      return '$sizeInGB جيجابايت';
    }
  }

  /// التحقق من وجود الملف
  /// 
  /// [filePath] مسار الملف
  Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الملف: $e');
      return false;
    }
  }

  /// إنشاء مجلد إذا لم يكن موجوداً
  /// 
  /// [directoryPath] مسار المجلد
  Future<bool> createDirectoryIfNotExists(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
        debugPrint('تم إنشاء المجلد: $directoryPath');
      }
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء المجلد: $e');
      return false;
    }
  }
}
