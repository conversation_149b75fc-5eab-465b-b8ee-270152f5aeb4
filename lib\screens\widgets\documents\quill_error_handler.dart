import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// معالج الأخطاء الموحد لمحرر Quill
/// يوفر معالجة موحدة ومبسطة للأخطاء مع رسائل واضحة للمستخدم
class QuillErrorHandler {
  /// تسجيل وعرض خطأ عام
  static void handleError(
    BuildContext? context,
    String operation,
    dynamic error, {
    StackTrace? stackTrace,
    bool showToUser = true,
  }) {
    // تسجيل الخطأ في وحدة التحكم
    debugPrint('❌ خطأ في $operation: $error');
    if (stackTrace != null) {
      debugPrint('📍 Stack trace: $stackTrace');
    }

    // عرض رسالة للمستخدم إذا كان مطلوباً
    if (showToUser && context != null) {
      _showErrorToUser(context, operation, error);
    }
  }

  /// معالجة أخطاء تصدير PDF
  static void handlePdfError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'تصدير PDF',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء رفع الملفات
  static void handleUploadError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'رفع الملف',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء الحفظ التلقائي
  static void handleAutoSaveError(
    BuildContext? context,
    dynamic error, {
    bool showToUser = false,
  }) {
    handleError(
      context,
      'الحفظ التلقائي',
      error,
      showToUser: showToUser,
    );
  }

  /// معالجة أخطاء إدراج الجداول
  static void handleTableError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'إدراج الجدول',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء إدراج الصور
  static void handleImageError(
    BuildContext? context,
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    handleError(
      context,
      'إدراج الصورة',
      error,
      stackTrace: stackTrace,
      showToUser: true,
    );
  }

  /// معالجة أخطاء البحث والاستبدال
  static void handleSearchError(
    BuildContext? context,
    dynamic error,
  ) {
    handleError(
      context,
      'البحث والاستبدال',
      error,
      showToUser: true,
    );
  }

  /// عرض رسالة خطأ للمستخدم
  static void _showErrorToUser(
    BuildContext context,
    String operation,
    dynamic error,
  ) {
    try {
      final errorMessage = _getSimplifiedErrorMessage(operation, error);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'إغلاق',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة الخطأ للمستخدم: $e');
    }
  }

  /// تبسيط رسالة الخطأ للمستخدم
  static String _getSimplifiedErrorMessage(String operation, dynamic error) {
    final errorString = error.toString().toLowerCase();

    // أخطاء الشبكة
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return 'خطأ في الاتصال بالشبكة. تحقق من اتصال الإنترنت وحاول مرة أخرى.';
    }

    // أخطاء الملفات
    if (errorString.contains('file') || 
        errorString.contains('path') ||
        errorString.contains('permission')) {
      return 'خطأ في الوصول إلى الملف. تحقق من الصلاحيات وحاول مرة أخرى.';
    }

    // أخطاء الذاكرة
    if (errorString.contains('memory') || 
        errorString.contains('out of memory')) {
      return 'الملف كبير جداً. حاول تقليل حجم المحتوى أو إعادة تشغيل التطبيق.';
    }

    // أخطاء التنسيق
    if (errorString.contains('format') || 
        errorString.contains('parse') ||
        errorString.contains('json')) {
      return 'خطأ في تنسيق البيانات. حاول إعادة تحميل المستند.';
    }

    // أخطاء الصلاحيات
    if (errorString.contains('permission') || 
        errorString.contains('unauthorized') ||
        errorString.contains('forbidden')) {
      return 'ليس لديك صلاحية لتنفيذ هذا الإجراء.';
    }

    // رسالة عامة حسب نوع العملية
    switch (operation) {
      case 'تصدير PDF':
        return 'فشل في تصدير PDF. حاول مرة أخرى أو قلل من حجم المحتوى.';
      case 'رفع الملف':
        return 'فشل في رفع الملف. تحقق من حجم الملف واتصال الإنترنت.';
      case 'إدراج الجدول':
        return 'فشل في إدراج الجدول. حاول مرة أخرى.';
      case 'إدراج الصورة':
        return 'فشل في إدراج الصورة. تحقق من صيغة الصورة وحجمها.';
      case 'البحث والاستبدال':
        return 'فشل في عملية البحث والاستبدال. حاول مرة أخرى.';
      case 'الحفظ التلقائي':
        return 'فشل في الحفظ التلقائي. تحقق من اتصال الإنترنت.';
      default:
        return 'حدث خطأ في $operation. حاول مرة أخرى.';
    }
  }

  /// عرض رسالة نجاح
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: duration,
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة النجاح: $e');
    }
  }

  /// عرض رسالة تحذير
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange,
          duration: duration,
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة التحذير: $e');
    }
  }

  /// عرض حوار تحذير للنصوص الطويلة
  static Future<bool> showLongTextWarning(
    BuildContext context,
    int textLength,
  ) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تحذير: نص طويل'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('النص طويل جداً (${textLength.toString()} حرف).'),
              const SizedBox(height: 8),
              const Text('قد تستغرق العملية وقتاً أطول من المعتاد.'),
              const SizedBox(height: 8),
              const Text('هل تريد المتابعة؟'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('متابعة'),
            ),
          ],
        ),
      );
      return result ?? false;
    } catch (e) {
      debugPrint('خطأ في عرض تحذير النص الطويل: $e');
      return false;
    }
  }

  /// عرض حوار التحميل
  static void showLoadingDialog(
    BuildContext context,
    String message,
  ) {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Expanded(child: Text(message)),
            ],
          ),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض حوار التحميل: $e');
    }
  }

  /// إخفاء حوار التحميل
  static void hideLoadingDialog(BuildContext context) {
    try {
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('خطأ في إخفاء حوار التحميل: $e');
    }
  }
}
