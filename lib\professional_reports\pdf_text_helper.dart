import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// مساعد لإدارة النصوص في ملفات PDF مع دعم الخطوط العربية والرموز
class PdfTextHelper {
  final pw.Font? arabicFont;
  final pw.Font? boldFont;
  final List<pw.Font> fontFallbacks;

  const PdfTextHelper({
    required this.arabicFont,
    required this.boldFont,
    required this.fontFallbacks,
  });

  /// إنشاء TextStyle عربي عادي
  pw.TextStyle arabicStyle(double fontSize, {PdfColor? color, pw.FontWeight? fontWeight}) {
    return pw.TextStyle(
      font: arabicFont,
      fontFallback: fontFallbacks,
      fontSize: fontSize,
      color: color ?? PdfColors.black,
      fontWeight: fontWeight,
    );
  }

  /// إنشاء TextStyle عربي عريض
  pw.TextStyle boldArabicStyle(double fontSize, {PdfColor? color}) {
    return pw.TextStyle(
      font: boldFont,
      fontFallback: fontFallbacks,
      fontSize: fontSize,
      color: color ?? PdfColors.black,
      fontWeight: pw.FontWeight.bold,
    );
  }

  /// تنظيف النص من الرموز غير المدعومة
  String cleanText(String text) {
    return text
      .replaceAll('←', 'إلى')
      .replaceAll('→', 'من')
      .replaceAll('⏳', '[قيد التنفيذ]')
      .replaceAll('✅', '[مكتملة]')
      .replaceAll('❌', '[ملغية]')
      .replaceAll('⚠️', '[تحذير]')
      .replaceAll('📝', '[ملاحظة]')
      .replaceAll('📎', '[مرفق]')
      .replaceAll('👤', '[مستخدم]')
      .replaceAll('📅', '[تاريخ]')
      .replaceAll('🔄', '[تحديث]')
      .replaceAll('💬', '[تعليق]');
  }

  /// إنشاء نص مع تنظيف تلقائي
  pw.Text createText(String text, double fontSize, {PdfColor? color, pw.FontWeight? fontWeight, pw.TextAlign? textAlign}) {
    return pw.Text(
      cleanText(text),
      style: arabicStyle(fontSize, color: color, fontWeight: fontWeight),
      textAlign: textAlign ?? pw.TextAlign.right,
    );
  }

  /// إنشاء نص عريض مع تنظيف تلقائي
  pw.Text createBoldText(String text, double fontSize, {PdfColor? color, pw.TextAlign? textAlign}) {
    return pw.Text(
      cleanText(text),
      style: boldArabicStyle(fontSize, color: color),
      textAlign: textAlign ?? pw.TextAlign.right,
    );
  }
}