  import 'package:flutter/material.dart';
import 'package:flutter_application_2/enums/task_enums.dart';

String getStatusText(String status) {
    // تطبيع الحالة للتعامل مع جميع الاحتمالات
    final normalizedStatus = status.toLowerCase().trim();

    debugPrint('🔍 _getStatusText: الحالة الأصلية: "$status" -> المطبعة: "$normalizedStatus"');

    switch (normalizedStatus) {
      // حالات الانتظار
      case 'pending':
      case 'new':
      case 'news':
      case 'جديد':
      case 'جديدة':
        return 'قيد الانتظار';

      // حالات التنفيذ
      case 'inprogress':
      case 'in_progress':
      case 'in-progress':
      case 'قيد التنفيذ':
        return 'قيد التنفيذ';

      // حالات انتظار المعلومات
      case 'waitingforinfo':
      case 'waiting_for_info':
      case 'waiting-for-info':
      case 'في انتظار معلومات':
        return 'في انتظار معلومات';

      // حالات الاكتمال
      case 'completed':
      case 'done':
      case 'مكتملة':
        return 'مكتملة';

      // حالات الإلغاء
      case 'cancelled':
      case 'canceled':
      case 'ملغاة':
        return 'ملغاة';

      default:
        debugPrint('⚠️ حالة غير معروفة: "$status"');
        return 'غير محدد ($status)';
    }
  }

  String getPriorityText(dynamic priority) {
    if (priority is TaskPriority) {
      switch (priority) {
        case TaskPriority.low:
          return 'منخفضة';
        case TaskPriority.medium:
          return 'متوسطة';
        case TaskPriority.high:
          return 'عالية';
        case TaskPriority.urgent:
          return 'عاجلة';
      }
    } else if (priority is int) {
      final taskPriority = TaskPriority.fromLevel(priority);
      return getPriorityText(taskPriority);
    } else if (priority is String) {
      // حاول التحويل باستخدام fromString (يدعم stringValue واسم enum)
      final taskPriority = TaskPriority.fromString(priority);
      return getPriorityText(taskPriority);
    }
    return 'غير محدد';
  }

  /// الحصول على أيقونة الأولوية
  IconData getPriorityIcon(dynamic priority) {
    if (priority is TaskPriority) {
      switch (priority) {
        case TaskPriority.low:
          return Icons.arrow_downward;
        case TaskPriority.medium:
          return Icons.remove;
        case TaskPriority.high:
          return Icons.arrow_upward;
        case TaskPriority.urgent:
          return Icons.priority_high;
      }
    } else if (priority is int) {
      final taskPriority = TaskPriority.fromLevel(priority);
      return getPriorityIcon(taskPriority);
    }
    return Icons.help;
  }

  /// أيقونة الحالة
  IconData getStatusIcon(String status) {
    // تطبيع الحالة للتعامل مع جميع الاحتمالات
    final normalizedStatus = status.toLowerCase().trim();

    switch (normalizedStatus) {
      // حالات الانتظار
      case 'pending':
      case 'new':
      case 'news':
      case 'جديد':
      case 'جديدة':
        return Icons.hourglass_empty;

      // حالات التنفيذ
      case 'inprogress':
      case 'in_progress':
      case 'in-progress':
      case 'قيد التنفيذ':
        return Icons.play_circle_outline;

      // حالات انتظار المعلومات
      case 'waitingforinfo':
      case 'waiting_for_info':
      case 'waiting-for-info':
      
      case 'في انتظار معلومات':
        return Icons.info_outline;

      // حالات الاكتمال
      case 'completed':
      case 'done':
      case 'مكتملة':
        return Icons.check_circle_outline;

      // حالات الإلغاء
      case 'cancelled':
      case 'canceled':
      case 'ملغاة':
        return Icons.cancel_outlined;

      default:
        debugPrint('⚠️ أيقونة غير معروفة للحالة: "$status"');
        return Icons.help_outline;
    }
  }

Color getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

 

  // Color getStatusColor(TaskStatus status) {
  //   switch (status) {
  //     case TaskStatus.pending:
  //       return Colors.grey;
  //     case TaskStatus.inProgress:
  //       return Colors.blue;
  //     case TaskStatus.waitingForInfo:
  //       return Colors.orange;
  //     case TaskStatus.completed:
  //       return Colors.green;
  //     case TaskStatus.cancelled:
  //       return Colors.red;
  //     case TaskStatus.news:
  //       return Colors.purple;
  //     default:
  //       return Colors.grey;
  //   }
  // }
  