import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../models/role_model.dart';
import '../../../models/permission_models.dart';
import '../../../controllers/admin_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../services/api/roles_api_service.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_form_widget.dart';

/// حوار إنشاء أو تعديل دور
class RoleFormDialog extends StatefulWidget {
  final Role? role; // null للإنشاء، Role للتعديل
  final VoidCallback? onSuccess;

  const RoleFormDialog({
    super.key,
    this.role,
    this.onSuccess,
  });

  @override
  State<RoleFormDialog> createState() => _RoleFormDialogState();
}

class _RoleFormDialogState extends State<RoleFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();
  final RolesApiService _rolesApiService = RolesApiService();

  // Controllers للحقول
  late final TextEditingController _nameController;
  late final TextEditingController _displayNameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _levelController;

  // حالة الحوار
  final RxBool _isLoading = false.obs;
  final RxBool _isActive = true.obs;
  final RxSet<int> _selectedPermissions = <int>{}.obs;

  // متغيرات للتحقق من صحة البيانات
  String? _nameError;
  String? _levelError;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPermissions();
  }

  /// تهيئة Controllers
  void _initializeControllers() {
    final role = widget.role;
    _nameController = TextEditingController(text: role?.name ?? '');
    _displayNameController = TextEditingController(text: role?.displayName ?? '');
    _descriptionController = TextEditingController(text: role?.description ?? '');
    _levelController = TextEditingController(text: role?.level.toString() ?? '1');
    _isActive.value = role?.isActive ?? true;

    // تحميل الصلاحيات المحددة للدور (في حالة التعديل)
    if (role != null) {
      _loadRolePermissions(role.id);
    }
  }

  /// تحميل جميع الصلاحيات المتاحة
  Future<void> _loadPermissions() async {
    try {
      await _adminController.loadPermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    }
  }

  /// تحميل صلاحيات الدور (للتعديل)
  Future<void> _loadRolePermissions(int roleId) async {
    try {
      final permissions = await _rolesApiService.getRolePermissions(roleId);
      _selectedPermissions.assignAll(permissions.map((p) => p.id));
    } catch (e) {
      debugPrint('خطأ في تحميل صلاحيات الدور: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _descriptionController.dispose();
    _levelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.role != null;
    final title = isEditing ? 'تعديل الدور' : 'إنشاء دور جديد';

    return AlertDialog(
      title: Text(title),
      content: SizedBox(
        width: double.maxFinite,
        height: 600,
        child: Obx(() {
          if (_isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات الدور الأساسية
                  _buildBasicInfoSection(),
                  
                  const SizedBox(height: 24),
                  
                  // الصلاحيات
                  _buildPermissionsSection(),
                ],
              ),
            ),
          );
        }),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('إلغاء'),
        ),
        Obx(() => ElevatedButton(
          onPressed: _isLoading.value ? null : _saveRole,
          child: _isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'تحديث' : 'إنشاء'),
        )),
      ],
    );
  }

  /// بناء قسم المعلومات الأساسية
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // اسم الدور
        AdminTextField(
          controller: _nameController,
          label: 'اسم الدور',
          hint: 'مثال: manager, supervisor',
          enabled: true, // يمكن الآن تعديل اسم الدور
          isRequired: true,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم الدور مطلوب';
            }
            if (value.trim().length < 3) {
              return 'اسم الدور يجب أن يكون 3 أحرف على الأقل';
            }
            return _nameError;
          },
        ),
        
        const SizedBox(height: 16),
        
        // الاسم المعروض
        AdminTextField(
          controller: _displayNameController,
          label: 'الاسم المعروض',
          hint: 'مثال: مدير، مشرف',
          isRequired: true,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'الاسم المعروض مطلوب';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // الوصف
        AdminTextField(
          controller: _descriptionController,
          label: 'الوصف',
          hint: 'وصف مختصر للدور ومسؤولياته',
          maxLines: 3,
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            // مستوى الدور
            Expanded(
              child: AdminTextField(
                controller: _levelController,
                label: 'مستوى الدور',
                hint: '1-100',
                keyboardType: TextInputType.number,
                isRequired: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'مستوى الدور مطلوب';
                  }
                  final level = int.tryParse(value);
                  if (level == null || level < 1 || level > 100) {
                    return 'المستوى يجب أن يكون بين 1 و 100';
                  }
                  return _levelError;
                },
              ),
            ),
            
            const SizedBox(width: 16),
            
            // حالة الدور
            Expanded(
              child: Obx(() => AdminSwitchField(
                label: 'الدور نشط',
                value: _isActive.value,
                onChanged: (value) => _isActive.value = value,
              )),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم الصلاحيات
  Widget _buildPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'الصلاحيات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: _selectAllPermissions,
              child: const Text('تحديد الكل'),
            ),
            TextButton(
              onPressed: _clearAllPermissions,
              child: const Text('إلغاء الكل'),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Obx(() {
          final permissions = _adminController.permissions;
          if (permissions.isEmpty) {
            return const Center(
              child: Text('لا توجد صلاحيات متاحة'),
            );
          }

          // تجميع الصلاحيات حسب المجموعة
          final groupedPermissions = <String, List<Permission>>{};
          for (final permission in permissions) {
            final group = permission.permissionGroup;
            groupedPermissions.putIfAbsent(group, () => []).add(permission);
          }

          return Column(
            children: groupedPermissions.entries.map((entry) {
              return _buildPermissionGroup(entry.key, entry.value);
            }).toList(),
          );
        }),
      ],
    );
  }

  /// بناء مجموعة صلاحيات
  Widget _buildPermissionGroup(String groupName, List<Permission> permissions) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          groupName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Obx(() {
          final selectedCount = permissions
              .where((p) => _selectedPermissions.contains(p.id))
              .length;
          return Text('$selectedCount من ${permissions.length} محدد');
        }),
        children: permissions.map((permission) {
          return Obx(() => CheckboxListTile(
            title: Text(permission.name),
            subtitle: permission.description != null
                ? Text(permission.description!)
                : null,
            value: _selectedPermissions.contains(permission.id),
            onChanged: (value) {
              if (value == true) {
                _selectedPermissions.add(permission.id);
              } else {
                _selectedPermissions.remove(permission.id);
              }
            },
          ));
        }).toList(),
      ),
    );
  }

  /// تحديد جميع الصلاحيات
  void _selectAllPermissions() {
    final allPermissionIds = _adminController.permissions.map((p) => p.id).toSet();
    _selectedPermissions.assignAll(allPermissionIds);
  }

  /// إلغاء تحديد جميع الصلاحيات
  void _clearAllPermissions() {
    _selectedPermissions.clear();
  }

  /// حفظ الدور
  Future<void> _saveRole() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;
    _nameError = null;
    _levelError = null;

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final isEditing = widget.role != null;

      if (isEditing) {
        // تحديث الدور
        await _updateRole(currentUser.id);
      } else {
        // إنشاء دور جديد
        await _createRole(currentUser.id);
      }

      // إعادة تحميل الأدوار
      await _adminController.loadRoles();

      // إغلاق الحوار وإظهار رسالة نجاح
      Get.back();
      AdminMessageDialog.showSuccess(
        title: 'تم بنجاح',
        message: isEditing ? 'تم تحديث الدور بنجاح' : 'تم إنشاء الدور بنجاح',
      );

      // استدعاء callback النجاح
      widget.onSuccess?.call();

    } catch (e) {
      debugPrint('خطأ في حفظ الدور: $e');
      
      // معالجة أخطاء محددة
      final errorMessage = e.toString();
      if (errorMessage.contains('اسم الدور موجود')) {
        setState(() {
          _nameError = 'اسم الدور موجود بالفعل';
        });
        _formKey.currentState!.validate();
      } else if (errorMessage.contains('مستوى الدور موجود')) {
        setState(() {
          _levelError = 'مستوى الدور موجود بالفعل';
        });
        _formKey.currentState!.validate();
      } else {
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حفظ الدور: $e',
        );
      }
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء دور جديد
  Future<void> _createRole(int userId) async {
    await _rolesApiService.createRole(
      name: _nameController.text.trim(),
      displayName: _displayNameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      level: int.parse(_levelController.text),
      isActive: _isActive.value,
      createdBy: userId,
      defaultPermissionIds: _selectedPermissions.toList(),
    );
  }

  /// تحديث دور موجود
  Future<void> _updateRole(int userId) async {
    final role = widget.role!;
    
    // تحديث معلومات الدور
    await _rolesApiService.updateRole(
      id: role.id,
      displayName: _displayNameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      level: int.parse(_levelController.text),
      isActive: _isActive.value,
      updatedBy: userId,
    );

    // تحديث الصلاحيات
    await _rolesApiService.grantMultiplePermissionsToRole(
      roleId: role.id,
      permissionIds: _selectedPermissions.toList(),
      userId: userId,
      replaceExisting: true, // استبدال الصلاحيات الحالية
    );

    // 🔄 إعادة تحميل صلاحيات المستخدم الحالي إذا تم تعديل دوره
    try {
      final permissionService = Get.find<UnifiedPermissionService>();
      await permissionService.refreshCurrentUserPermissions();
      debugPrint('✅ تم تحديث صلاحيات المستخدم الحالي بعد تعديل الدور');
    } catch (e) {
      debugPrint('⚠️ خطأ في تحديث صلاحيات المستخدم: $e');
    }
  }
}
