import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../models/power_bi_models.dart';
import '../../controllers/power_bi_controller.dart';

/// شاشة إنشاء تقرير باور بي آي
///
/// توفر واجهة لإنشاء أو تعديل تقارير باور بي آي
class CreatePowerBIReportScreen extends StatefulWidget {
  final PowerBIReport? report;

  const CreatePowerBIReportScreen({super.key, this.report});

  @override
  State<CreatePowerBIReportScreen> createState() =>
      _CreatePowerBIReportScreenState();
}

class _CreatePowerBIReportScreenState extends State<CreatePowerBIReportScreen> {
  final PowerBIController _powerBIController = Get.find<PowerBIController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _filterCriteriaController =
      TextEditingController();

  PowerBIChartType _selectedChartType = PowerBIChartType.bar;
  String _selectedTable = '';
  List<String> _selectedColumns = [];
  String _xAxisColumn = '';
  String _yAxisColumn = '';
  AggregateFunction _yAxisAggregateFunction = AggregateFunction.none;
  String _sizeColumn = '';
  String _colorColumn = '';

  // متغيرات للجداول المتعددة
  bool _useMultipleTables = false;
  List<String> _relatedTables = [];
  List<String> _joinConditions = [];
  List<String> _joinTypes = [];
  List<Map<String, String>> _suggestedRelations = [];

  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // تأخير تحميل البيانات حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });

    // إذا كان هناك تقرير للتعديل، قم بتعبئة الحقول
    if (widget.report != null) {
      _titleController.text = widget.report!.title;
      _descriptionController.text = widget.report!.description ?? '';
      _selectedChartType = widget.report!.chartType;
      _selectedTable = widget.report!.tableName;
      _selectedColumns = List.from(widget.report!.columnNames);
      _xAxisColumn = widget.report!.xAxisColumn;
      _yAxisColumn = widget.report!.yAxisColumn;
      _yAxisAggregateFunction =
          widget.report!.yAxisAggregateFunction ?? AggregateFunction.none;
      _sizeColumn = widget.report!.sizeColumn ?? '';
      _colorColumn = widget.report!.colorColumn ?? '';
      _filterCriteriaController.text = widget.report!.filterCriteria ?? '';

      // تحميل بيانات الجداول المتعددة
      if (widget.report!.relatedTables != null &&
          widget.report!.relatedTables!.isNotEmpty) {
        _useMultipleTables = true;
        _relatedTables = List.from(widget.report!.relatedTables!);
        _joinConditions = List.from(widget.report!.joinConditions ?? []);
        _joinTypes = List.from(widget.report!.joinTypes ?? []);
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _filterCriteriaController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
    });

    try {
      await _powerBIController.loadAvailableTables();

      if (_selectedTable.isNotEmpty) {
        await _powerBIController.loadTableColumns(_selectedTable);

        // تحميل العلاقات المقترحة إذا كان هناك جداول مرتبطة
        if (_useMultipleTables && _relatedTables.isNotEmpty) {
          for (final relatedTable in _relatedTables) {
            await _powerBIController.loadTableColumns(relatedTable);
          }
        }
      }
    } catch (e) {
      // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _errorMessage = 'خطأ في تحميل البيانات: $e';
        });
      });
    } finally {
      // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isLoading = false;
        });
      });
    }
  }

  /// تحميل العلاقات المقترحة بين الجداول
  Future<void> _loadSuggestedRelations(String relatedTable) async {
    if (_selectedTable.isEmpty || relatedTable.isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
      _suggestedRelations = [];
    });

    try {
      final suggestions = await _powerBIController.suggestTableRelations(
          _selectedTable, relatedTable);

      // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
      if (mounted) {
        setState(() {
          _suggestedRelations = suggestions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في تحميل العلاقات المقترحة: $e';
          _isLoading = false;
        });
      }
      debugPrint('خطأ في تحميل العلاقات المقترحة: $e');
    }
  }

  /// عرض العلاقات المقترحة في نافذة منبثقة
  void _showRelationSuggestions(String relatedTable, int index) async {
    // تحميل العلاقات المقترحة
    await _loadSuggestedRelations(relatedTable);

    if (!mounted) return;

    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    if (_suggestedRelations.isNotEmpty) {
      // عرض العلاقات المقترحة في نافذة منبثقة
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('اختر طريقة ربط البيانات'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestedRelations.length,
              itemBuilder: (context, i) {
                final relation = _suggestedRelations[i];

                // تحويل أسماء الجداول والأعمدة إلى أسماء عرض مفهومة
                final table1Display = tableDisplayNames[relation['table1']] ??
                    relation['table1']!;
                final table2Display = tableDisplayNames[relation['table2']] ??
                    relation['table2']!;

                // تبسيط عرض العلاقة
                String relationDisplay = 'ربط $table1Display مع $table2Display';

                return ListTile(
                  leading: const Icon(Icons.link, color: Colors.blue),
                  title: Text(relationDisplay),
                  subtitle: const Text('(اختيار تلقائي لأفضل طريقة ربط)'),
                  onTap: () {
                    // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      setState(() {
                        if (index < _joinConditions.length) {
                          _joinConditions[index] = relation['joinCondition']!;
                        } else {
                          _joinConditions.add(relation['joinCondition']!);
                        }
                      });
                    });
                    Navigator.of(dialogContext).pop();
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    } else {
      // إذا لم يتم العثور على علاقات مقترحة
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('لا توجد علاقات'),
          content: const Text(
              'لم نتمكن من إيجاد طريقة تلقائية لربط هذه البيانات. يرجى اختيار قسم بيانات آخر.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(widget.report == null ? 'إنشاء تقرير جديد' : 'تعديل التقرير'),
        actions: [
          ElevatedButton.icon(
            onPressed: _saveReport,
            icon: const Icon(Icons.save),
            label: const Text('حفظ'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 10),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : _buildForm(),
    );
  }

  /// بناء نموذج إنشاء التقرير
  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات التقرير
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات التقرير',
                      style: AppStyles.titleMedium,
                    ),
                    const Divider(),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان التقرير',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال عنوان التقرير';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'وصف التقرير',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // اختيار نوع الرسم البياني
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نوع الرسم البياني',
                      style: AppStyles.titleMedium,
                    ),
                    const Divider(),
                    const SizedBox(height: 16),
                    _buildChartTypeSelector(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // اختيار مصدر البيانات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مصدر البيانات',
                      style: AppStyles.titleMedium,
                    ),
                    const Divider(),
                    const SizedBox(height: 16),

                    // خيار استخدام جداول متعددة
                    SwitchListTile(
                      title: const Text('دمج بيانات من أقسام مختلفة'),
                      subtitle: const Text(
                          'إضافة بيانات من أقسام أخرى مثل المستخدمين، المهام، الأقسام، إلخ'),
                      value: _useMultipleTables,
                      onChanged: (value) {
                        // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          setState(() {
                            _useMultipleTables = value;
                          });
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // اختيار الجدول الرئيسي
                    _buildTableSelector(),

                    if (_selectedTable.isNotEmpty) ...[
                      const SizedBox(height: 16),

                      // إضافة جداول مرتبطة إذا تم تفعيل الخيار
                      if (_useMultipleTables) _buildRelatedTablesSelector(),

                      const SizedBox(height: 16),
                      _buildColumnSelector(),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // إعدادات الرسم البياني
            if (_selectedTable.isNotEmpty && _selectedColumns.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إعدادات الرسم البياني',
                        style: AppStyles.titleMedium,
                      ),
                      const Divider(),
                      const SizedBox(height: 16),
                      _buildChartSettings(),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 16),

            // معايير التصفية
            if (_selectedTable.isNotEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معايير التصفية',
                        style: AppStyles.titleMedium,
                      ),
                      const Divider(),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _filterCriteriaController,
                        decoration: const InputDecoration(
                          labelText: 'معايير التصفية (SQL WHERE)',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: column1 > 10 AND column2 = "value"',
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 32),

            // زر الحفظ في نهاية النموذج
            Center(
              child: ElevatedButton.icon(
                onPressed: _saveReport,
                icon: const Icon(Icons.save),
                label: const Text('حفظ التقرير'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Theme.of(context).primaryColor,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  textStyle: const TextStyle(fontSize: 18),
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// بناء محدد نوع الرسم البياني
  Widget _buildChartTypeSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildChartTypeOption(PowerBIChartType.bar, Icons.bar_chart, 'شريطي'),
        _buildChartTypeOption(PowerBIChartType.line, Icons.show_chart, 'خطي'),
        _buildChartTypeOption(PowerBIChartType.pie, Icons.pie_chart, 'دائري'),
        _buildChartTypeOption(
            PowerBIChartType.scatter, Icons.scatter_plot, 'نقطي'),
        _buildChartTypeOption(
            PowerBIChartType.bubble, Icons.bubble_chart, 'فقاعي'),
        _buildChartTypeOption(
            PowerBIChartType.table, Icons.table_chart, 'جدول'),
      ],
    );
  }

  /// بناء خيار نوع الرسم البياني
  Widget _buildChartTypeOption(
      PowerBIChartType type, IconData icon, String label) {
    final isSelected = _selectedChartType == type;

    return InkWell(
      onTap: () {
        // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedChartType = type;
          });
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withAlpha(51)
              : null, // 0.2 * 255 = 51
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Theme.of(context).primaryColor : null,
                fontWeight: isSelected ? FontWeight.bold : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محدد الجدول
  Widget _buildTableSelector() {
    // استخدام متغير محلي بدلاً من Obx
    final tables = _powerBIController.availableTables;

    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    // التحقق من صحة القيمة المحددة
    String? selectedValue;

    // إذا كانت القيمة المحددة غير فارغة وموجودة في القائمة
    if (_selectedTable.isNotEmpty && tables.contains(_selectedTable)) {
      selectedValue = _selectedTable;
    } else {
      selectedValue = null;

      // إذا كانت القيمة المحددة غير فارغة ولكن غير موجودة في القائمة، قم بإعادة تعيينها
      if (_selectedTable.isNotEmpty) {
        // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedTable = '';
          });
        });
      }
    }

    // إذا كانت القائمة فارغة، أضف عنصرًا افتراضيًا
    final List<DropdownMenuItem<String>> dropdownItems = [];

    if (tables.isEmpty) {
      dropdownItems.add(const DropdownMenuItem<String>(
        value: null,
        enabled: false,
        child: Text('لا توجد جداول متاحة'),
      ));
    } else {
      for (final table in tables) {
        dropdownItems.add(DropdownMenuItem<String>(
          value: table,
          child: Text(tableDisplayNames[table] ?? table),
        ));
      }
    }

    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'اختر قسم البيانات الرئيسي',
        hintText: 'مثال: المهام، المستخدمين، الأقسام',
        border: OutlineInputBorder(),
      ),
      value: selectedValue,
      items: dropdownItems,
      onChanged: tables.isEmpty
          ? null
          : (value) {
              if (value != null) {
                // استخدام متغير محلي لتخزين القيمة الحالية
                final newSelectedTable = value;

                // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {
                    _selectedTable = newSelectedTable;
                    _selectedColumns = [];
                    _xAxisColumn = '';
                    _yAxisColumn = '';
                    _sizeColumn = '';
                    _colorColumn = '';

                    // إعادة تعيين الجداول المرتبطة
                    _relatedTables = [];
                    _joinConditions = [];
                    _joinTypes = [];
                    _suggestedRelations = [];
                  });

                  _powerBIController.loadTableColumns(newSelectedTable);
                });
              }
            },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار جدول';
        }
        return null;
      },
    );
  }

  /// بناء محدد الجداول المرتبطة
  Widget _buildRelatedTablesSelector() {
    // استخدام متغير محلي بدلاً من Obx
    final tables = _powerBIController.availableTables;

    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    // قائمة بأنواع الربط المعروضة للمستخدم
    final Map<String, String> joinTypeDisplayNames = {
      'INNER JOIN': 'ربط البيانات المتطابقة فقط',
      'LEFT JOIN': 'عرض كل البيانات من القسم الرئيسي',
      'RIGHT JOIN': 'عرض كل البيانات من القسم المضاف',
    };

    // التحقق من صحة الجداول المرتبطة
    for (int i = 0; i < _relatedTables.length; i++) {
      if (!tables.contains(_relatedTables[i])) {
        // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _relatedTables.removeAt(i);
            if (i < _joinConditions.length) {
              _joinConditions.removeAt(i);
            }
            if (i < _joinTypes.length) {
              _joinTypes.removeAt(i);
            }
          });
        });
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إضافة بيانات من أقسام أخرى:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // عرض الجداول المرتبطة المضافة
        if (_relatedTables.isNotEmpty) ...[
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _relatedTables.length,
            itemBuilder: (context, index) {
              final relatedTable = _relatedTables[index];
              final displayName =
                  tableDisplayNames[relatedTable] ?? relatedTable;

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getTableIcon(relatedTable),
                                color: Colors.blue,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'بيانات من: $displayName',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            tooltip: 'إزالة هذا القسم',
                            onPressed: () {
                              // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  // حذف الجدول المرتبط وشروط الربط المرتبطة به
                                  _joinConditions.removeAt(index);
                                  _joinTypes.removeAt(index);
                                  _relatedTables.removeAt(index);
                                });
                              });
                            },
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 8),

                      // نوع الربط
                      const Text(
                        'كيفية ربط البيانات:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'اختر طريقة الربط',
                          border: OutlineInputBorder(),
                        ),
                        value: index < _joinTypes.length
                            ? _joinTypes[index]
                            : 'INNER JOIN',
                        items: joinTypeDisplayNames.entries.map((entry) {
                          return DropdownMenuItem<String>(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              setState(() {
                                if (index < _joinTypes.length) {
                                  _joinTypes[index] = value;
                                } else {
                                  _joinTypes.add(value);
                                }
                              });
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // زر اقتراح العلاقات
                      ElevatedButton.icon(
                        icon: const Icon(Icons.auto_fix_high),
                        label: const Text('ربط البيانات تلقائياً'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          // تحميل العلاقات المقترحة وعرضها
                          _showRelationSuggestions(
                              _relatedTables[index], index);
                        },
                      ),

                      // عرض حالة الربط الحالية
                      if (index < _joinConditions.length &&
                          _joinConditions[index].isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.check_circle,
                                  color: Colors.green),
                              const SizedBox(width: 8),
                              const Expanded(
                                child: Text(
                                  'تم ربط البيانات بنجاح',
                                  style: TextStyle(color: Colors.green),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete,
                                    color: Colors.red, size: 16),
                                tooltip: 'إلغاء الربط',
                                onPressed: () {
                                  WidgetsBinding.instance
                                      .addPostFrameCallback((_) {
                                    setState(() {
                                      _joinConditions[index] = '';
                                    });
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
        ],

        // زر إضافة جدول مرتبط
        _buildAddTableWidget(tables, tableDisplayNames),
      ],
    );
  }

  /// بناء ويدجت إضافة جدول مرتبط
  Widget _buildAddTableWidget(
      List<String> tables, Map<String, String> tableDisplayNames) {
    // تحضير قائمة الجداول المتاحة للإضافة
    final availableTables = tables
        .where((table) =>
            table != _selectedTable && !_relatedTables.contains(table))
        .toList();

    // التحقق من وجود جداول متاحة للإضافة
    if (availableTables.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Expanded(
              child: Text('لا توجد أقسام بيانات إضافية متاحة للإضافة'),
            ),
          ],
        ),
      );
    } else {
      return DropdownButtonFormField<String>(
        decoration: const InputDecoration(
          labelText: 'إضافة بيانات من قسم آخر',
          hintText: 'اختر قسم لإضافة بياناته',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.add_circle),
        ),
        value: null,
        items: availableTables.map((table) {
          return DropdownMenuItem<String>(
            value: table,
            child: Text(tableDisplayNames[table] ?? table),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _relatedTables.add(value);
                _joinConditions.add('');
                _joinTypes.add('INNER JOIN');
              });

              // تحميل أعمدة الجدول المرتبط
              _powerBIController.loadTableColumns(value);

              // تحميل العلاقات المقترحة وعرضها
              final newIndex = _relatedTables.length - 1;
              _showRelationSuggestions(value, newIndex);
            });
          }
        },
      );
    }
  }

  /// الحصول على أيقونة مناسبة للجدول
  IconData _getTableIcon(String tableName) {
    switch (tableName) {
      case 'tasks':
        return Icons.task;
      case 'users':
        return Icons.people;
      case 'departments':
        return Icons.business;
      case 'task_types':
        return Icons.category;
      case 'task_statuses':
        return Icons.pending_actions;
      case 'task_priorities':
        return Icons.priority_high;
      case 'task_comments':
        return Icons.comment;
      case 'task_attachments':
        return Icons.attach_file;
      default:
        return Icons.table_chart;
    }
  }

  /// بناء محدد الأعمدة
  Widget _buildColumnSelector() {
    // استخدام متغير محلي بدلاً من Obx
    final columns = _powerBIController.tableColumns[_selectedTable] ?? [];

    // قائمة بأسماء الأعمدة المعروضة للمستخدم
    final Map<String, String> columnDisplayNames = {
      'id': 'المعرف',
      'title': 'العنوان',
      'name': 'الاسم',
      'description': 'الوصف',
      'status': 'الحالة',
      'priority': 'الأولوية',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'due_date': 'تاريخ الاستحقاق',
      'completed_at': 'تاريخ الإكمال',
      'user_id': 'المستخدم',
      'department_id': 'القسم',
      'task_type_id': 'نوع المهمة',
      'task_status_id': 'حالة المهمة',
      'task_priority_id': 'أولوية المهمة',
      'assigned_to': 'مسند إلى',
      'created_by': 'أنشئت بواسطة',
      'completion_percentage': 'نسبة الإكمال',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'address': 'العنوان',
      'role': 'الدور',
      'is_active': 'نشط',
      'count': 'العدد',
      'total': 'المجموع',
      'average': 'المتوسط',
    };

    if (columns.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: const [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Text('جاري تحميل البيانات...'),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.view_column, color: Colors.blue, size: 20),
            const SizedBox(width: 8),
            const Text(
              'اختر البيانات التي تريد عرضها:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // شرح بسيط
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade100),
          ),
          child: Row(
            children: const [
              Icon(Icons.lightbulb, color: Colors.amber),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'اختر البيانات التي تريد تضمينها في التقرير. يمكنك اختيار عدة عناصر.',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // تصنيف الأعمدة إلى مجموعات
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // زر تحديد الكل
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'البيانات المتاحة:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextButton.icon(
                    icon: Icon(
                      _selectedColumns.length == columns.length
                          ? Icons.deselect
                          : Icons.select_all,
                      size: 18,
                    ),
                    label: Text(
                      _selectedColumns.length == columns.length
                          ? 'إلغاء تحديد الكل'
                          : 'تحديد الكل',
                    ),
                    onPressed: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        setState(() {
                          if (_selectedColumns.length == columns.length) {
                            // إلغاء تحديد الكل
                            _selectedColumns.clear();
                            _xAxisColumn = '';
                            _yAxisColumn = '';
                            _sizeColumn = '';
                            _colorColumn = '';
                          } else {
                            // تحديد الكل
                            _selectedColumns = columns
                                .map((col) => col['name'] as String)
                                .toList();
                          }
                        });
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // عرض الأعمدة
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: columns.map((column) {
                  final columnName = column['name'] as String;
                  final displayName =
                      columnDisplayNames[columnName] ?? columnName;
                  final isSelected = _selectedColumns.contains(columnName);

                  // تحديد لون ورمز مناسب للعمود
                  Color chipColor;
                  IconData chipIcon;

                  if (columnName.contains('date') ||
                      columnName.contains('_at') ||
                      columnName.contains('time')) {
                    chipColor = Colors.purple;
                    chipIcon = Icons.calendar_today;
                  } else if (columnName.contains('id')) {
                    chipColor = Colors.grey;
                    chipIcon = Icons.key;
                  } else if (columnName.contains('name') ||
                      columnName.contains('title') ||
                      columnName.contains('description')) {
                    chipColor = Colors.green;
                    chipIcon = Icons.text_fields;
                  } else if (columnName.contains('count') ||
                      columnName.contains('total') ||
                      columnName.contains('sum') ||
                      columnName.contains('average') ||
                      columnName.contains('percentage')) {
                    chipColor = Colors.orange;
                    chipIcon = Icons.numbers;
                  } else if (columnName.contains('status') ||
                      columnName.contains('priority') ||
                      columnName.contains('type')) {
                    chipColor = Colors.blue;
                    chipIcon = Icons.category;
                  } else if (columnName.contains('user') ||
                      columnName.contains('assigned') ||
                      columnName.contains('created_by')) {
                    chipColor = Colors.teal;
                    chipIcon = Icons.person;
                  } else {
                    chipColor = Colors.blueGrey;
                    chipIcon = Icons.data_array;
                  }

                  return FilterChip(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          chipIcon,
                          size: 16,
                          color: isSelected ? Colors.white : chipColor,
                        ),
                        const SizedBox(width: 4),
                        Text(displayName),
                      ],
                    ),
                    selected: isSelected,
                    checkmarkColor: Colors.white,
                    selectedColor: chipColor,
                    backgroundColor: Colors.grey.shade100,
                    labelStyle: TextStyle(
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    onSelected: (selected) {
                      // استخدام متغير محلي لتخزين القيمة الحالية
                      final currentColumnName = columnName;

                      // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        setState(() {
                          if (selected) {
                            if (!_selectedColumns.contains(currentColumnName)) {
                              _selectedColumns.add(currentColumnName);
                            }
                          } else {
                            _selectedColumns.remove(currentColumnName);

                            // إزالة العمود من الإعدادات إذا كان محددًا
                            if (_xAxisColumn == currentColumnName) {
                              _xAxisColumn = '';
                            }
                            if (_yAxisColumn == currentColumnName) {
                              _yAxisColumn = '';
                            }
                            if (_sizeColumn == currentColumnName) {
                              _sizeColumn = '';
                            }
                            if (_colorColumn == currentColumnName) {
                              _colorColumn = '';
                            }
                          }
                        });
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ),
        ),

        // عرض الأعمدة المحددة
        if (_selectedColumns.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'تم اختيار ${_selectedColumns.length} عنصر',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// بناء إعدادات الرسم البياني
  Widget _buildChartSettings() {
    // قائمة بأسماء الأعمدة المعروضة للمستخدم
    final Map<String, String> columnDisplayNames = {
      'id': 'المعرف',
      'title': 'العنوان',
      'name': 'الاسم',
      'description': 'الوصف',
      'status': 'الحالة',
      'priority': 'الأولوية',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'due_date': 'تاريخ الاستحقاق',
      'completed_at': 'تاريخ الإكمال',
      'user_id': 'المستخدم',
      'department_id': 'القسم',
      'task_type_id': 'نوع المهمة',
      'task_status_id': 'حالة المهمة',
      'task_priority_id': 'أولوية المهمة',
      'assigned_to': 'مسند إلى',
      'created_by': 'أنشئت بواسطة',
      'completion_percentage': 'نسبة الإكمال',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'address': 'العنوان',
      'role': 'الدور',
      'is_active': 'نشط',
      'count': 'العدد',
      'total': 'المجموع',
      'average': 'المتوسط',
    };

    // تحديد أيقونة مناسبة لكل نوع من أنواع الرسوم البيانية
    final Map<PowerBIChartType, IconData> chartTypeIcons = {
      PowerBIChartType.bar: Icons.bar_chart,
      PowerBIChartType.line: Icons.show_chart,
      PowerBIChartType.pie: Icons.pie_chart,
      PowerBIChartType.scatter: Icons.scatter_plot,
      PowerBIChartType.bubble: Icons.bubble_chart,
      PowerBIChartType.table: Icons.table_chart,
    };

    // تحديد اسم عرض لكل نوع من أنواع الرسوم البيانية
    final Map<PowerBIChartType, String> chartTypeDisplayNames = {
      PowerBIChartType.bar: 'رسم بياني شريطي',
      PowerBIChartType.line: 'رسم بياني خطي',
      PowerBIChartType.pie: 'رسم بياني دائري',
      PowerBIChartType.scatter: 'رسم بياني نقطي',
      PowerBIChartType.bubble: 'رسم بياني فقاعي',
      PowerBIChartType.table: 'جدول بيانات',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Row(
          children: [
            Icon(Icons.settings, color: Colors.blue, size: 20),
            const SizedBox(width: 8),
            const Text(
              'إعدادات الرسم البياني:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // اختيار نوع الرسم البياني
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'نوع الرسم البياني:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              // أنواع الرسوم البيانية
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: PowerBIChartType.values.map((type) {
                  final isSelected = _selectedChartType == type;
                  final icon = chartTypeIcons[type] ?? Icons.insert_chart;
                  final displayName =
                      chartTypeDisplayNames[type] ?? type.toString();

                  return InkWell(
                    onTap: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        setState(() {
                          _selectedChartType = type;
                        });
                      });
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              isSelected ? Colors.blue : Colors.grey.shade300,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: Colors.blue
                                      .withAlpha(51), // 0.2 * 255 = 51
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            icon,
                            color: isSelected ? Colors.white : Colors.blue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            displayName,
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.black87,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // شرح بسيط
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade100),
          ),
          child: Row(
            children: [
              const Icon(Icons.lightbulb, color: Colors.amber),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getChartTypeDescription(_selectedChartType),
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // إعدادات محاور الرسم البياني
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'محاور الرسم البياني:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // اختيار عمود المحور الأفقي (X)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'X',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المحور الأفقي:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستظهر على المحور الأفقي (X)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _xAxisColumn.isNotEmpty &&
                                  _selectedColumns.contains(_xAxisColumn)
                              ? _xAxisColumn
                              : null,
                          items: _selectedColumns.isEmpty
                              ? [
                                  const DropdownMenuItem<String>(
                                      value: null,
                                      child: Text('لا توجد بيانات متاحة'))
                                ]
                              : _selectedColumns.map((column) {
                                  final displayName =
                                      columnDisplayNames[column] ?? column;
                                  return DropdownMenuItem<String>(
                                    value: column,
                                    child: Text(displayName),
                                  );
                                }).toList(),
                          onChanged: _selectedColumns.isEmpty
                              ? null
                              : (value) {
                                  if (value != null) {
                                    // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      setState(() {
                                        _xAxisColumn = value;
                                      });
                                    });
                                  }
                                },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار بيانات المحور الأفقي';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // اختيار عمود المحور الرأسي (Y)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'Y',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المحور الرأسي:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستظهر على المحور الرأسي (Y)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _yAxisColumn.isNotEmpty &&
                                  _selectedColumns.contains(_yAxisColumn)
                              ? _yAxisColumn
                              : null,
                          items: _selectedColumns.isEmpty
                              ? [
                                  const DropdownMenuItem<String>(
                                      value: null,
                                      child: Text('لا توجد بيانات متاحة'))
                                ]
                              : _selectedColumns.map((column) {
                                  final displayName =
                                      columnDisplayNames[column] ?? column;
                                  return DropdownMenuItem<String>(
                                    value: column,
                                    child: Text(displayName),
                                  );
                                }).toList(),
                          onChanged: _selectedColumns.isEmpty
                              ? null
                              : (value) {
                                  if (value != null) {
                                    // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      setState(() {
                                        _yAxisColumn = value;
                                      });
                                    });
                                  }
                                },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار بيانات المحور الرأسي';
                            }
                            return null;
                          },
                        ),

                        // اختيار الدالة التجميعية للمحور الرأسي
                        const SizedBox(height: 16),
                        const Text(
                          'الدالة التجميعية:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر كيفية تجميع البيانات في المحور الرأسي (مثل عدد، مجموع، متوسط)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<AggregateFunction>(
                          decoration: const InputDecoration(
                            labelText: 'الدالة التجميعية',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _yAxisAggregateFunction,
                          items: AggregateFunction.values.map((function) {
                            return DropdownMenuItem<AggregateFunction>(
                              value: function,
                              child: Text(getAggregateFunctionName(function)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  _yAxisAggregateFunction = value;
                                });
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 8),
                        Text(
                          getAggregateFunctionDescription(
                              _yAxisAggregateFunction),
                          style:
                              const TextStyle(fontSize: 12, color: Colors.blue),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // اختيار عمود الحجم (للرسوم البيانية الفقاعية)
              if (_selectedChartType == PowerBIChartType.bubble) ...[
                const SizedBox(height: 20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.bubble_chart,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'حجم الفقاعات:',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'اختر البيانات التي ستحدد حجم الفقاعات في الرسم البياني',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'اختر البيانات',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 12),
                            ),
                            value: _sizeColumn.isEmpty
                                ? null
                                : (_selectedColumns.contains(_sizeColumn)
                                    ? _sizeColumn
                                    : null),
                            items: [
                              const DropdownMenuItem<String>(
                                value: null,
                                child: Text('بدون'),
                              ),
                              ..._selectedColumns.map((column) {
                                final displayName =
                                    columnDisplayNames[column] ?? column;
                                return DropdownMenuItem<String>(
                                  value: column,
                                  child: Text(displayName),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  _sizeColumn = value ?? '';
                                });
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              // اختيار عمود اللون (للتصنيف)
              const SizedBox(height: 20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.color_lens,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تصنيف البيانات بالألوان:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستستخدم لتصنيف وتلوين العناصر في الرسم البياني',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _colorColumn.isEmpty
                              ? null
                              : (_selectedColumns.contains(_colorColumn)
                                  ? _colorColumn
                                  : null),
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('بدون'),
                            ),
                            ..._selectedColumns.map((column) {
                              final displayName =
                                  columnDisplayNames[column] ?? column;
                              return DropdownMenuItem<String>(
                                value: column,
                                child: Text(displayName),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            // تأخير تحديث الحالة حتى اكتمال بناء الواجهة
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              setState(() {
                                _colorColumn = value ?? '';
                              });
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// الحصول على وصف لنوع الرسم البياني
  String _getChartTypeDescription(PowerBIChartType chartType) {
    switch (chartType) {
      case PowerBIChartType.bar:
        return 'الرسم البياني الشريطي مناسب لمقارنة القيم بين فئات مختلفة، مثل عدد المهام لكل قسم.';
      case PowerBIChartType.line:
        return 'الرسم البياني الخطي مناسب لعرض البيانات على مدار الزمن، مثل تطور عدد المهام المكتملة شهريًا.';
      case PowerBIChartType.pie:
        return 'الرسم البياني الدائري مناسب لعرض النسب المئوية، مثل توزيع المهام حسب الحالة.';
      case PowerBIChartType.scatter:
        return 'الرسم البياني النقطي مناسب لعرض العلاقة بين متغيرين، مثل العلاقة بين مدة المهمة ونسبة إكمالها.';
      case PowerBIChartType.bubble:
        return 'الرسم البياني الفقاعي مناسب لعرض ثلاثة متغيرات، حيث يمثل حجم الفقاعة المتغير الثالث.';
      case PowerBIChartType.table:
        return 'جدول البيانات مناسب لعرض البيانات التفصيلية بشكل مباشر.';
      default:
        return 'اختر نوع الرسم البياني المناسب لبياناتك.';
    }
  }

  /// حفظ التقرير
  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedColumns.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار عمود واحد على الأقل',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_xAxisColumn.isEmpty || _yAxisColumn.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار عمودي المحور الأفقي والرأسي',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // التحقق من شروط الربط إذا كانت هناك جداول مرتبطة
    if (_useMultipleTables && _relatedTables.isNotEmpty) {
      for (int i = 0; i < _relatedTables.length; i++) {
        if (i >= _joinConditions.length || _joinConditions[i].isEmpty) {
          Get.snackbar(
            'خطأ',
            'يرجى إدخال شروط الربط لجميع الجداول المرتبطة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }
      }
    }

    // تعيين حالة التحميل مباشرة
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.report == null) {
        // إنشاء تقرير جديد
        final report = await _powerBIController.createReport(
          title: _titleController.text,
          description: _descriptionController.text,
          chartType: _selectedChartType,
          tableName: _selectedTable,
          columnNames: _selectedColumns,
          xAxisColumn: _xAxisColumn,
          yAxisColumn: _yAxisColumn,
          yAxisAggregateFunction: _yAxisAggregateFunction,
          sizeColumn: _sizeColumn.isEmpty ? null : _sizeColumn,
          colorColumn: _colorColumn.isEmpty ? null : _colorColumn,
          filterCriteria: _filterCriteriaController.text.isEmpty
              ? null
              : _filterCriteriaController.text,
          relatedTables: _useMultipleTables && _relatedTables.isNotEmpty
              ? _relatedTables
              : null,
          joinConditions: _useMultipleTables && _joinConditions.isNotEmpty
              ? _joinConditions
              : null,
          joinTypes:
              _useMultipleTables && _joinTypes.isNotEmpty ? _joinTypes : null,
        );

        if (report != null) {
          Get.back();
          Get.snackbar(
            'نجاح',
            'تم إنشاء التقرير بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        // تحديث تقرير موجود
        final updatedReport = widget.report!.copyWith(
          title: _titleController.text,
          description: _descriptionController.text,
          chartType: _selectedChartType,
          tableName: _selectedTable,
          columnNames: _selectedColumns,
          xAxisColumn: _xAxisColumn,
          yAxisColumn: _yAxisColumn,
          yAxisAggregateFunction: _yAxisAggregateFunction,
          sizeColumn: _sizeColumn.isEmpty ? null : _sizeColumn,
          colorColumn: _colorColumn.isEmpty ? null : _colorColumn,
          filterCriteria: _filterCriteriaController.text.isEmpty
              ? null
              : _filterCriteriaController.text,
          relatedTables: _useMultipleTables && _relatedTables.isNotEmpty
              ? _relatedTables
              : null,
          joinConditions: _useMultipleTables && _joinConditions.isNotEmpty
              ? _joinConditions
              : null,
          joinTypes:
              _useMultipleTables && _joinTypes.isNotEmpty ? _joinTypes : null,
          updatedAt: DateTime.now(),
        );

        final success = await _powerBIController.updateReport(updatedReport);

        if (success != null) {
          Get.back();
          Get.snackbar(
            'نجاح',
            'تم تحديث التقرير بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حفظ التقرير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      // تعيين حالة التحميل مباشرة
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// الحصول على اسم دالة التجميع
  String getAggregateFunctionName(AggregateFunction function) {
    return function.displayName;
  }

  /// الحصول على وصف دالة التجميع
  String getAggregateFunctionDescription(AggregateFunction function) {
    switch (function) {
      case AggregateFunction.none:
        return 'عرض القيم كما هي بدون تجميع';
      case AggregateFunction.sum:
        return 'جمع جميع القيم';
      case AggregateFunction.count:
        return 'عدد العناصر';
      case AggregateFunction.avg:
        return 'متوسط القيم';
      case AggregateFunction.min:
        return 'أصغر قيمة';
      case AggregateFunction.max:
        return 'أكبر قيمة';
      case AggregateFunction.distinct:
        return 'عدد القيم المميزة';
    }
  }
}
