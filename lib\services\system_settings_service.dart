import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/system_settings_model.dart';
import 'admin/unified_admin_api_service.dart';

/// خدمة إدارة إعدادات النظام - محسنة ومتصلة بالكامل مع الباك إند
class SystemSettingsService extends GetxService {
  final UnifiedAdminApiService _apiService = Get.find<UnifiedAdminApiService>();

  // تخزين الإعدادات محلياً مع تخزين مؤقت
  final RxMap<String, SystemSettings> _settings = <String, SystemSettings>{}.obs;
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  DateTime? _lastLoadTime;

  // مدة صلاحية التخزين المؤقت (5 دقائق)
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// الحصول على جميع الإعدادات
  Map<String, SystemSettings> get settings => _settings;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;
  
  /// تحميل جميع الإعدادات من الخادم مع تخزين مؤقت
  Future<void> loadSettings({bool forceRefresh = false}) async {
    // التحقق من صلاحية التخزين المؤقت
    if (!forceRefresh && _isCacheValid() && _settings.isNotEmpty) {
      debugPrint('📋 استخدام الإعدادات من التخزين المؤقت');
      return;
    }

    if (_isLoading.value) {
      debugPrint('⏳ تحميل الإعدادات قيد التنفيذ...');
      return;
    }

    _isLoading.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 تحميل إعدادات النظام من الخادم...');

      // تحميل الإعدادات من الخادم
      final settingsData = await _apiService.getSystemSettings();

      _settings.clear();
      for (final setting in settingsData) {
        final systemSetting = SystemSettings(
          id: setting.id,
          key: setting.settingKey,
          value: setting.settingValue,
          description: setting.description,
          category: setting.settingGroup ?? SettingsCategory.general,
          dataType: _determineDataType(setting.settingValue),
          isEditable: true, // سيتم تحديدها من الخادم لاحقاً
          isVisible: true, // سيتم تحديدها من الخادم لاحقاً
          createdAt: setting.createdAt,
          updatedAt: setting.updatedAt,
        );
        _settings[setting.settingKey] = systemSetting;
      }

      _lastLoadTime = DateTime.now();
      debugPrint('✅ تم تحميل ${_settings.length} إعداد من الخادم');

    } catch (e) {
      _error.value = 'فشل في تحميل الإعدادات: $e';
      debugPrint('❌ خطأ في تحميل الإعدادات: $e');

      // في حالة الفشل، اترك الإعدادات فارغة أو استخدم المحفوظة
      if (_settings.isEmpty) {
        debugPrint('⚠️ لا توجد إعدادات محفوظة، يجب الاتصال بالخادم');
      }
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من صلاحية التخزين المؤقت
  bool _isCacheValid() {
    if (_lastLoadTime == null) return false;
    return DateTime.now().difference(_lastLoadTime!) < _cacheValidDuration;
  }

  /// تحديد نوع البيانات تلقائياً
  String _determineDataType(String value) {
    if (value.toLowerCase() == 'true' || value.toLowerCase() == 'false') {
      return SettingsDataType.boolean;
    }
    if (int.tryParse(value) != null) {
      return SettingsDataType.integer;
    }
    if (double.tryParse(value) != null) {
      return SettingsDataType.decimal;
    }
    return SettingsDataType.string;
  }
  

  
  /// الحصول على إعداد بالمفتاح
  SystemSettings? getSetting(String key) {
    return _settings[key];
  }
  
  /// الحصول على قيمة إعداد كنص
  String getStringValue(String key, {String defaultValue = ''}) {
    final setting = _settings[key];
    return setting?.value ?? defaultValue;
  }
  
  /// الحصول على قيمة إعداد كـ bool
  bool getBoolValue(String key, {bool defaultValue = false}) {
    final setting = _settings[key];
    return setting?.boolValue ?? defaultValue;
  }
  
  /// الحصول على قيمة إعداد كـ int
  int getIntValue(String key, {int defaultValue = 0}) {
    final setting = _settings[key];
    return setting?.intValue ?? defaultValue;
  }
  
  /// الحصول على قيمة إعداد كـ double
  double getDoubleValue(String key, {double defaultValue = 0.0}) {
    final setting = _settings[key];
    return setting?.doubleValue ?? defaultValue;
  }
  
  /// تحديث إعداد مع التحقق من الصحة
  Future<bool> updateSetting(String key, String value) async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية تحديث أخرى قيد التنفيذ...');
      return false;
    }

    try {
      final setting = _settings[key];
      if (setting == null) {
        _error.value = 'الإعداد غير موجود: $key';
        debugPrint('❌ الإعداد غير موجود: $key');
        return false;
      }

      if (!setting.isEditable) {
        _error.value = 'الإعداد غير قابل للتعديل: $key';
        debugPrint('❌ الإعداد غير قابل للتعديل: $key');
        return false;
      }

      // التحقق من صحة القيمة
      if (!validateSettingValue(setting, value)) {
        _error.value = 'القيمة المدخلة غير صحيحة للإعداد: $key';
        debugPrint('❌ قيمة غير صحيحة: $key = $value');
        return false;
      }

      _isLoading.value = true;
      _error.value = '';

      // تحديث الإعداد عبر API
      await _apiService.updateSystemSetting(key, value);

      // تحديث محلي بعد نجاح API
      final updatedSetting = setting.copyWith(
        value: value,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      _settings[key] = updatedSetting;

      // تحديث وقت التخزين المؤقت
      _lastLoadTime = DateTime.now();

      debugPrint('✅ تم تحديث الإعداد بنجاح: $key = $value');
      return true;

    } catch (e) {
      _error.value = 'فشل في تحديث الإعداد: $e';
      debugPrint('❌ خطأ في تحديث الإعداد: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// إضافة إعداد جديد
  Future<bool> addSetting(SystemSettings setting) async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية أخرى قيد التنفيذ...');
      return false;
    }

    try {
      if (_settings.containsKey(setting.key)) {
        _error.value = 'الإعداد موجود بالفعل: ${setting.key}';
        debugPrint('❌ الإعداد موجود بالفعل: ${setting.key}');
        return false;
      }

      _isLoading.value = true;
      _error.value = '';

      // إنشاء الإعداد عبر API
      await _apiService.createSystemSetting(setting);

      // إضافة محلية بعد نجاح API
      final newSetting = setting.copyWith(
        id: DateTime.now().millisecondsSinceEpoch,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      _settings[setting.key] = newSetting;

      debugPrint('✅ تم إضافة الإعداد: ${setting.key}');
      return true;
    } catch (e) {
      _error.value = 'فشل في إضافة الإعداد: $e';
      debugPrint('❌ خطأ في إضافة الإعداد: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// حذف إعداد
  Future<bool> deleteSetting(String key) async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية أخرى قيد التنفيذ...');
      return false;
    }

    try {
      final setting = _settings[key];
      if (setting == null) {
        _error.value = 'الإعداد غير موجود: $key';
        debugPrint('❌ الإعداد غير موجود: $key');
        return false;
      }

      if (!setting.isEditable) {
        _error.value = 'الإعداد غير قابل للحذف: $key';
        debugPrint('❌ الإعداد غير قابل للحذف: $key');
        return false;
      }

      _isLoading.value = true;
      _error.value = '';

      // حذف الإعداد عبر API
      await _apiService.deleteSystemSetting(key);

      // حذف محلي بعد نجاح API
      _settings.remove(key);

      debugPrint('✅ تم حذف الإعداد: $key');
      return true;
    } catch (e) {
      _error.value = 'فشل في حذف الإعداد: $e';
      debugPrint('❌ خطأ في حذف الإعداد: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// الحصول على الإعدادات حسب الفئة
  List<SystemSettings> getSettingsByCategory(String category) {
    return _settings.values
        .where((setting) => setting.category == category && setting.isVisible)
        .toList();
  }
  
  /// الحصول على جميع الفئات
  List<String> getCategories() {
    return _settings.values
        .map((setting) => setting.category)
        .toSet()
        .toList();
  }
  
  /// إعادة تعيين الإعدادات للافتراضية
  Future<bool> resetToDefaults() async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية أخرى قيد التنفيذ...');
      return false;
    }

    try {
      _isLoading.value = true;
      _error.value = '';

      // إعادة تعيين الإعدادات عبر API
      await _apiService.resetSystemSettings();

      // إعادة تحميل الإعدادات من الخادم
      await loadSettings(forceRefresh: true);

      debugPrint('✅ تم إعادة تعيين الإعدادات للافتراضية');
      return true;
    } catch (e) {
      _error.value = 'فشل في إعادة تعيين الإعدادات: $e';
      debugPrint('❌ خطأ في إعادة تعيين الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// تصدير الإعدادات
  Future<Map<String, dynamic>> exportSettings() async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية أخرى قيد التنفيذ...');
      return {};
    }

    try {
      _isLoading.value = true;
      _error.value = '';

      // تصدير الإعدادات عبر API
      final exportedData = await _apiService.exportSystemSettings();

      debugPrint('✅ تم تصدير الإعدادات بنجاح');
      return exportedData;
    } catch (e) {
      _error.value = 'فشل في تصدير الإعدادات: $e';
      debugPrint('❌ خطأ في تصدير الإعدادات: $e');

      // في حالة الفشل، تصدير البيانات المحلية
      return {
        'settings': _settings.values.map((s) => s.toJson()).toList(),
        'exported_at': DateTime.now().toIso8601String(),
        'version': '1.0.0',
      };
    } finally {
      _isLoading.value = false;
    }
  }

  /// استيراد الإعدادات
  Future<bool> importSettings(Map<String, dynamic> data) async {
    if (_isLoading.value) {
      debugPrint('⏳ عملية أخرى قيد التنفيذ...');
      return false;
    }

    try {
      final settingsData = data['settings'] as List?;
      if (settingsData == null) {
        _error.value = 'بيانات الإعدادات غير صحيحة';
        debugPrint('❌ بيانات الإعدادات غير صحيحة');
        return false;
      }

      _isLoading.value = true;
      _error.value = '';

      // استيراد الإعدادات عبر API
      await _apiService.importSystemSettings(data);

      // إعادة تحميل الإعدادات من الخادم
      await loadSettings(forceRefresh: true);

      debugPrint('✅ تم استيراد الإعدادات بنجاح');
      return true;
    } catch (e) {
      _error.value = 'فشل في استيراد الإعدادات: $e';
      debugPrint('❌ خطأ في استيراد الإعدادات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// التحقق من صحة قيمة الإعداد
  bool validateSettingValue(SystemSettings setting, String value) {
    switch (setting.dataType) {
      case SettingsDataType.boolean:
        return value.toLowerCase() == 'true' || 
               value.toLowerCase() == 'false' ||
               value == '1' || value == '0';
      
      case SettingsDataType.integer:
        return int.tryParse(value) != null;
      
      case SettingsDataType.decimal:
        return double.tryParse(value) != null;
      
      case SettingsDataType.string:
      case SettingsDataType.password:
      case SettingsDataType.json:
      default:
        return value.isNotEmpty;
    }
  }
  
  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }
}
