import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/export_import_service.dart';
import '../../controllers/admin_controller.dart';
import '../../widgets/loading_indicator.dart';

/// شاشة التصدير والاستيراد
class ExportImportScreen extends StatefulWidget {
  const ExportImportScreen({super.key});

  @override
  State<ExportImportScreen> createState() => _ExportImportScreenState();
}

class _ExportImportScreenState extends State<ExportImportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ExportImportService _exportImportService = ExportImportService();
  final AdminController _adminController = Get.find<AdminController>();

  bool _isLoading = false;
  String? _lastExportPath;
  Map<String, dynamic>? _lastImportResult;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التصدير والاستيراد'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.file_upload), text: 'تصدير البيانات'),
            Tab(icon: Icon(Icons.file_download), text: 'استيراد البيانات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildExportTab(),
                _buildImportTab(),
              ],
            ),
    );
  }

  /// تبويب التصدير
  Widget _buildExportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تصدير البيانات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'اختر نوع البيانات والصيغة المطلوبة للتصدير',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // بطاقات أنواع البيانات
          _buildDataTypeCard(
            title: 'المستخدمين',
            icon: Icons.people,
            description: 'تصدير بيانات جميع المستخدمين',
            dataType: DataType.users,
          ),
          const SizedBox(height: 16),
          
          _buildDataTypeCard(
            title: 'الأقسام',
            icon: Icons.business,
            description: 'تصدير بيانات الأقسام والهيكل التنظيمي',
            dataType: DataType.departments,
          ),
          const SizedBox(height: 16),
          
          _buildDataTypeCard(
            title: 'المهام',
            icon: Icons.task,
            description: 'تصدير بيانات المهام والمشاريع',
            dataType: DataType.tasks,
          ),
          const SizedBox(height: 16),
          
          _buildDataTypeCard(
            title: 'الأدوار والصلاحيات',
            icon: Icons.security,
            description: 'تصدير بيانات الأدوار والصلاحيات',
            dataType: DataType.roles,
          ),

          if (_lastExportPath != null) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green),
                      SizedBox(width: 8),
                      Text(
                        'تم التصدير بنجاح',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'مسار الملف: $_lastExportPath',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// تبويب الاستيراد
  Widget _buildImportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'استيراد البيانات',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'اختر ملف لاستيراد البيانات منه',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // بطاقة اختيار الملف
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(
                    Icons.file_upload,
                    size: 64,
                    color: Colors.blue,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'اختر ملف للاستيراد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'الصيغ المدعومة: CSV, Excel, JSON',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _pickAndImportFile,
                    icon: const Icon(Icons.folder_open),
                    label: const Text('اختيار ملف'),
                  ),
                ],
              ),
            ),
          ),

          if (_lastImportResult != null) ...[
            const SizedBox(height: 24),
            _buildImportResult(_lastImportResult!),
          ],

          const SizedBox(height: 24),
          _buildImportInstructions(),
        ],
      ),
    );
  }

  /// بناء بطاقة نوع البيانات للتصدير
  Widget _buildDataTypeCard({
    required String title,
    required IconData icon,
    required String description,
    required DataType dataType,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 32, color: Colors.blue),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        description,
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildExportButton('CSV', ExportFormat.csv, dataType),
                const SizedBox(width: 8),
                _buildExportButton('Excel', ExportFormat.excel, dataType),
                const SizedBox(width: 8),
                _buildExportButton('PDF', ExportFormat.pdf, dataType),
                const SizedBox(width: 8),
                _buildExportButton('JSON', ExportFormat.json, dataType),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر التصدير
  Widget _buildExportButton(
    String label,
    ExportFormat format,
    DataType dataType,
  ) {
    return Expanded(
      child: ElevatedButton(
        onPressed: () => _exportData(dataType, format),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 8),
        ),
        child: Text(label, style: const TextStyle(fontSize: 12)),
      ),
    );
  }

  /// بناء نتيجة الاستيراد
  Widget _buildImportResult(Map<String, dynamic> result) {
    final imported = result['imported'] ?? 0;
    final failed = result['failed'] ?? 0;
    final total = result['total'] ?? 0;
    final errors = result['errors'] as List<String>? ?? [];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: imported > 0 ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: imported > 0 ? Colors.green.withValues(alpha: 0.3) : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                imported > 0 ? Icons.check_circle : Icons.error,
                color: imported > 0 ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                'نتيجة الاستيراد',
                style: TextStyle(
                  color: imported > 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text('إجمالي العناصر: $total'),
          Text('تم استيرادها: $imported'),
          if (failed > 0) Text('فشل في استيرادها: $failed'),
          
          if (errors.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Text(
              'الأخطاء:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            ...errors.take(5).map((error) => Text(
              '• $error',
              style: const TextStyle(fontSize: 12, color: Colors.red),
            )),
            if (errors.length > 5)
              Text(
                'و ${errors.length - 5} أخطاء أخرى...',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
          ],
        ],
      ),
    );
  }

  /// بناء تعليمات الاستيراد
  Widget _buildImportInstructions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'تعليمات الاستيراد',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text('• تأكد من أن الملف يحتوي على البيانات بالتنسيق الصحيح'),
            const Text('• يجب أن تكون أسماء الأعمدة متطابقة مع النظام'),
            const Text('• سيتم تجاهل البيانات المكررة أو غير الصحيحة'),
            const Text('• يمكنك تصدير البيانات أولاً لمعرفة التنسيق المطلوب'),
          ],
        ),
      ),
    );
  }

  /// تصدير البيانات
  Future<void> _exportData(
    DataType dataType,
    ExportFormat format,
  ) async {
    setState(() => _isLoading = true);

    try {
      final filePath = await _exportImportService.exportData(
        dataType: dataType,
        format: format,
      );

      setState(() {
        _lastExportPath = filePath;
        _isLoading = false;
      });

      Get.snackbar(
        'تم بنجاح',
        'تم تصدير البيانات بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green,
      );
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar(
        'خطأ',
        'فشل في تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red,
      );
    }
  }

  /// اختيار واستيراد ملف
  Future<void> _pickAndImportFile() async {
    final filePath = await _exportImportService.pickFileForImport();
    if (filePath == null) return;

    setState(() => _isLoading = true);

    try {
      // تحديد نوع البيانات بناءً على اسم الملف أو محتواه
      // هنا نفترض أنه مستخدمين، يمكن تحسين هذا لاحقاً
      final result = await _exportImportService.importData(
        dataType: DataType.users,
        filePath: filePath,
      );

      setState(() {
        _lastImportResult = result;
        _isLoading = false;
      });

      // إعادة تحميل البيانات
      await _adminController.loadUsers();

      Get.snackbar(
        'تم بنجاح',
        'تم استيراد ${result['imported']} عنصر بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withValues(alpha: 0.1),
        colorText: Colors.green,
      );
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar(
        'خطأ',
        'فشل في استيراد البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(alpha: 0.1),
        colorText: Colors.red,
      );
    }
  }
}
