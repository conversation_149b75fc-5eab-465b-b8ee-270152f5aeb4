import 'dart:convert';
import 'user_model.dart';

/// نموذج لوحة التحكم - متطابق مع ASP.NET Core API
class Dashboard {
  final int id;
  final String title; // title في API بدلاً من name
  final String? description;
  final int ownerId; // ownerId في API بدلاً من createdBy
  final int createdAt; // long في API
  final int? updatedAt; // long في API
  final bool isDefault;
  final bool isShared; // isShared في API بدلاً من isPublic
  final int gridRows;
  final int gridColumns;
  final String? settings; // JSON string
  final String? color;
  final String? icon;
  final bool isDeleted;

  // Navigation properties
  final User? owner;
  final List<DashboardWidget>? dashboardWidgets;

  const Dashboard({
    required this.id,
    required this.title,
    this.description,
    required this.ownerId,
    required this.createdAt,
    this.updatedAt,
    this.isDefault = false,
    this.isShared = false,
    this.gridRows = 12,
    this.gridColumns = 12,
    this.settings,
    this.color,
    this.icon,
    this.isDeleted = false,
    this.owner,
    this.dashboardWidgets,
  });

  factory Dashboard.fromJson(Map<String, dynamic> json) {
    return Dashboard(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      ownerId: json['ownerId'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDefault: json['isDefault'] as bool? ?? false,
      isShared: json['isShared'] as bool? ?? false,
      gridRows: json['gridRows'] as int? ?? 12,
      gridColumns: json['gridColumns'] as int? ?? 12,
      settings: json['settings'] as String?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      owner: json['owner'] != null
          ? User.fromJson(json['owner'] as Map<String, dynamic>)
          : null,
      dashboardWidgets: json['dashboardWidgets'] != null
          ? (json['dashboardWidgets'] as List)
              .map((w) => DashboardWidget.fromJson(w as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'ownerId': ownerId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDefault': isDefault,
      'isShared': isShared,
      'gridRows': gridRows,
      'gridColumns': gridColumns,
      'settings': settings,
      'color': color,
      'icon': icon,
      'isDeleted': isDeleted,
    };
  }

  Dashboard copyWith({
    int? id,
    String? title,
    String? description,
    int? ownerId,
    int? createdAt,
    int? updatedAt,
    bool? isDefault,
    bool? isShared,
    int? gridRows,
    int? gridColumns,
    String? settings,
    String? color,
    String? icon,
    bool? isDeleted,
    User? owner,
    List<DashboardWidget>? dashboardWidgets,
  }) {
    return Dashboard(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ownerId: ownerId ?? this.ownerId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
      isShared: isShared ?? this.isShared,
      gridRows: gridRows ?? this.gridRows,
      gridColumns: gridColumns ?? this.gridColumns,
      settings: settings ?? this.settings,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isDeleted: isDeleted ?? this.isDeleted,
      owner: owner ?? this.owner,
      dashboardWidgets: dashboardWidgets ?? this.dashboardWidgets,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على اسم لوحة المعلومات (alias لـ title)
  String get name => title;

  /// الحصول على حالة النشاط
  bool get isActive => !isDeleted;

  /// الحصول على عدد الويدجت
  int get widgetCount => dashboardWidgets?.length ?? 0;

  @override
  String toString() {
    return 'Dashboard(id: $id, title: $title, widgetCount: $widgetCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dashboard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج ويدجت لوحة التحكم
class DashboardWidget {
  final int id;
  final int dashboardId;
  final String type; // chart, table, metric, etc.
  final String title;
  final String? config; // JSON configuration
  final int positionX;
  final int positionY;
  final int width;
  final int height;
  final bool isVisible;
  final int createdAt;
  final int? updatedAt;

  // Navigation properties
  final Dashboard? dashboard;

  const DashboardWidget({
    required this.id,
    required this.dashboardId,
    required this.type,
    required this.title,
    this.config,
    required this.positionX,
    required this.positionY,
    required this.width,
    required this.height,
    this.isVisible = true,
    required this.createdAt,
    this.updatedAt,
    this.dashboard,
  });

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
      id: json['id'] as int,
      dashboardId: json['dashboardId'] as int,
      type: json['type'] as String,
      title: json['title'] as String,
      config: json['config'] as String?,
      positionX: json['positionX'] as int,
      positionY: json['positionY'] as int,
      width: json['width'] as int,
      height: json['height'] as int,
      isVisible: json['isVisible'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      dashboard: json['dashboard'] != null 
          ? Dashboard.fromJson(json['dashboard'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'type': type,
      'title': title,
      'config': config,
      'positionX': positionX,
      'positionY': positionY,
      'width': width,
      'height': height,
      'isVisible': isVisible,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  DashboardWidget copyWith({
    int? id,
    int? dashboardId,
    String? type,
    String? title,
    String? config,
    int? positionX,
    int? positionY,
    int? width,
    int? height,
    bool? isVisible,
    int? createdAt,
    int? updatedAt,
    Dashboard? dashboard,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      dashboardId: dashboardId ?? this.dashboardId,
      type: type ?? this.type,
      title: title ?? this.title,
      config: config ?? this.config,
      positionX: positionX ?? this.positionX,
      positionY: positionY ?? this.positionY,
      width: width ?? this.width,
      height: height ?? this.height,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dashboard: dashboard ?? this.dashboard,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// الحصول على الوصف (alias)
  String? get description => null; // يمكن إضافة هذا الحقل لاحقاً

  /// الحصول على حالة النشاط
  bool get isActive => isVisible && !isDeleted;

  /// التحقق من كون العنصر محذوف
  bool get isDeleted => false; // يمكن إضافة هذا الحقل لاحقاً

  /// الحصول على الإحداثيات x (alias لـ positionX)
  int get x => positionX;

  /// الحصول على الإحداثيات y (alias لـ positionY)
  int get y => positionY;

  /// الحصول على الإعدادات كـ Map
  Map<String, dynamic>? get settings {
    if (config == null) return null;
    try {
      return Map<String, dynamic>.from(
        jsonDecode(config!)
      );
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'DashboardWidget(id: $id, type: $type, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardWidget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء لوحة تحكم
class CreateDashboardRequest {
  final String name;
  final String? description;
  final bool isPublic;
  final List<int>? sharedWithUserIds;

  const CreateDashboardRequest({
    required this.name,
    this.description,
    this.isPublic = false,
    this.sharedWithUserIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'isPublic': isPublic,
      'sharedWithUserIds': sharedWithUserIds,
    };
  }
}

/// نموذج طلب إنشاء ويدجت
class CreateDashboardWidgetRequest {
  final int dashboardId;
  final String type;
  final String title;
  final String? config;
  final int positionX;
  final int positionY;
  final int width;
  final int height;

  const CreateDashboardWidgetRequest({
    required this.dashboardId,
    required this.type,
    required this.title,
    this.config,
    required this.positionX,
    required this.positionY,
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() {
    return {
      'dashboardId': dashboardId,
      'type': type,
      'title': title,
      'config': config,
      'positionX': positionX,
      'positionY': positionY,
      'width': width,
      'height': height,
    };
  }
}
