import 'package:intl/intl.dart';

class DateFormatter {
  // Format date as "Jan 1, 2023"
  static String formatShortDate(DateTime date) {
    return DateFormat.yMMMd().format(date);
  }

  // Format date as "January 1, 2023"
  static String formatLongDate(DateTime date) {
    return DateFormat.yMMMMd().format(date);
  }

  // Format date as "01/01/2023"
  static String formatNumericDate(DateTime date) {
    return DateFormat.yMd().format(date);
  }

  // Format date as "2023-01-01"
  static String formatIsoDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // General purpose date formatter (MMM dd, yyyy)
  static String formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  // Format date and time as "Jan 1, 2023 12:30 PM"
  static String formatDateTime(DateTime dateTime) {
    return DateFormat.yMMMd().add_jm().format(dateTime);
  }

  // Format time as "12:30 PM"
  static String formatTime(DateTime time) {
    return DateFormat.jm().format(time);
  }

  /// تنسيق وقت الرسالة بالعربية
  static String formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      // اليوم: عرض الوقت فقط
      return DateFormat.jm().format(dateTime);
    } else if (messageDate == yesterday) {
      // الأمس: عرض "الأمس"
      return 'الأمس';
    } else if (now.difference(dateTime).inDays < 7) {
      // خلال الأسبوع: عرض اسم اليوم
      return _getArabicDayName(dateTime.weekday);
    } else {
      // أكثر من أسبوع: عرض التاريخ
      return DateFormat('yyyy/MM/dd').format(dateTime);
    }
  }

  /// الحصول على اسم اليوم بالعربية
  static String _getArabicDayName(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'الإثنين';
      case DateTime.tuesday:
        return 'الثلاثاء';
      case DateTime.wednesday:
        return 'الأربعاء';
      case DateTime.thursday:
        return 'الخميس';
      case DateTime.friday:
        return 'الجمعة';
      case DateTime.saturday:
        return 'السبت';
      case DateTime.sunday:
        return 'الأحد';
      default:
        return '';
    }
  }

  // Get relative time (e.g., "2 hours ago", "Yesterday", etc.)
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 2) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }

  // Check if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  // Check if a date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
  }

  // Check if a date is within the last week
  static bool isWithinLastWeek(DateTime date) {
    final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
    return date.isAfter(oneWeekAgo);
  }

  // Get days remaining until a date
  static int getDaysRemaining(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);
    return difference.inDays;
  }
}
