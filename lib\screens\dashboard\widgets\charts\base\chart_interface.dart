import 'package:flutter/material.dart';

/// واجهة المخططات الأساسية
/// 
/// تحدد العمليات الأساسية المطلوبة لكل مخطط
abstract class ChartInterface {
  /// البيانات المطلوبة للمخطط
  final Map<String, dynamic> data;
  
  /// إعدادات المخطط
  final Map<String, dynamic> settings;
  
  /// سمة المخطط
  final ChartTheme theme;

  ChartInterface({
    required this.data,
    required this.settings,
    required this.theme,
  });

  /// بناء المخطط
  Widget build();

  /// التحقق من صحة البيانات
  bool validateData(Map<String, dynamic> data);

  /// الحصول على عنوان المخطط
  String getTitle() {
    return settings['title'] ?? 'مخطط';
  }

  /// الحصول على ارتفاع المخطط
  double getHeight() {
    return settings['height']?.toDouble() ?? 300.0;
  }

  /// الحصول على عرض المخطط
  double getWidth() {
    return settings['width']?.toDouble() ?? double.infinity;
  }

  /// هل يظهر العنوان
  bool showTitle() {
    return settings['showTitle'] ?? true;
  }

  /// هل يظهر المفتاح
  bool showLegend() {
    return settings['showLegend'] ?? true;
  }

  /// الحصول على ألوان المخطط
  List<Color> getColors() {
    return theme.colorPalette;
  }
}

/// سمة المخطط
class ChartTheme {
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color textColor;
  final List<Color> colorPalette;
  final TextStyle titleStyle;
  final TextStyle labelStyle;
  final double borderRadius;
  final double elevation;

  const ChartTheme({
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.textColor,
    required this.colorPalette,
    required this.titleStyle,
    required this.labelStyle,
    this.borderRadius = 8.0,
    this.elevation = 2.0,
  });

  /// السمة الافتراضية
  factory ChartTheme.defaultTheme() {
    return ChartTheme(
      primaryColor: Colors.blue,
      secondaryColor: Colors.blue.shade300,
      backgroundColor: Colors.white,
      textColor: Colors.black87,
      colorPalette: [
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.red,
        Colors.purple,
        Colors.teal,
        Colors.amber,
        Colors.indigo,
      ],
      titleStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      labelStyle: const TextStyle(
        fontSize: 12,
        color: Colors.black54,
      ),
    );
  }

  /// سمة داكنة
  factory ChartTheme.darkTheme() {
    return ChartTheme(
      primaryColor: Colors.blue.shade300,
      secondaryColor: Colors.blue.shade500,
      backgroundColor: Colors.grey.shade900,
      textColor: Colors.white,
      colorPalette: [
        Colors.blue.shade300,
        Colors.green.shade300,
        Colors.orange.shade300,
        Colors.red.shade300,
        Colors.purple.shade300,
        Colors.teal.shade300,
        Colors.amber.shade300,
        Colors.indigo.shade300,
      ],
      titleStyle: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      labelStyle: const TextStyle(
        fontSize: 12,
        color: Colors.white70,
      ),
    );
  }
}
