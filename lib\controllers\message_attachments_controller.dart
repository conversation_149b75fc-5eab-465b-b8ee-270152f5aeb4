import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/message_attachment_models.dart';
import '../services/api/message_attachments_api_service.dart';

/// متحكم مرفقات الرسائل
class MessageAttachmentsController extends GetxController {
  final MessageAttachmentsApiService _apiService = MessageAttachmentsApiService();

  // قوائم المرفقات
  final RxList<MessageAttachment> _allAttachments = <MessageAttachment>[].obs;
  final RxList<MessageAttachment> _filteredAttachments = <MessageAttachment>[].obs;
  final RxList<MessageAttachment> _messageAttachments = <MessageAttachment>[].obs;

  // المرفق الحالي
  final Rx<MessageAttachment?> _currentAttachment = Rx<MessageAttachment?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _messageFilter = Rx<int?>(null);
  final Rx<String?> _fileTypeFilter = Rx<String?>(null);
  final Rx<int?> _uploaderFilter = Rx<int?>(null);

  // Getters
  List<MessageAttachment> get allAttachments => _allAttachments;
  List<MessageAttachment> get filteredAttachments => _filteredAttachments;
  List<MessageAttachment> get messageAttachments => _messageAttachments;
  MessageAttachment? get currentAttachment => _currentAttachment.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get messageFilter => _messageFilter.value;
  String? get fileTypeFilter => _fileTypeFilter.value;
  int? get uploaderFilter => _uploaderFilter.value;

  @override
  void onInit() {
    super.onInit();
    loadAllAttachments();
  }

  /// تحميل جميع مرفقات الرسائل
  Future<void> loadAllAttachments() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachments = await _apiService.getAllAttachments();
      _allAttachments.assignAll(attachments);
      _applyFilters();
      debugPrint('تم تحميل ${attachments.length} مرفق رسالة');
    } catch (e) {
      _error.value = 'خطأ في تحميل مرفقات الرسائل: $e';
      debugPrint('خطأ في تحميل مرفقات الرسائل: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مرفق رسالة بالمعرف
  Future<void> getAttachmentById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachment = await _apiService.getAttachmentById(id);
      _currentAttachment.value = attachment;
      debugPrint('تم تحميل مرفق الرسالة: ${attachment.fileName}');
    } catch (e) {
      _error.value = 'خطأ في تحميل مرفق الرسالة: $e';
      debugPrint('خطأ في تحميل مرفق الرسالة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// رفع مرفق جديد للرسالة
  Future<bool> uploadAttachment(int messageId, String filePath, String fileName) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newAttachment = await _apiService.uploadAttachment(messageId, filePath, fileName);
      _allAttachments.add(newAttachment);
      _applyFilters();
      debugPrint('تم رفع مرفق رسالة جديد: ${newAttachment.fileName}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في رفع مرفق الرسالة: $e';
      debugPrint('خطأ في رفع مرفق الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مرفق رسالة
  Future<bool> updateAttachment(MessageAttachment attachment) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedAttachment = await _apiService.updateAttachment(attachment);
      final index = _allAttachments.indexWhere((a) => a.id == attachment.id);
      if (index != -1) {
        _allAttachments[index] = updatedAttachment;
        _applyFilters();
      }
      debugPrint('تم تحديث مرفق الرسالة: ${attachment.fileName}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث مرفق الرسالة: $e';
      debugPrint('خطأ في تحديث مرفق الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مرفق رسالة
  Future<bool> deleteAttachment(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteAttachment(id);
      _allAttachments.removeWhere((a) => a.id == id);
      _applyFilters();
      debugPrint('تم حذف مرفق الرسالة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف مرفق الرسالة: $e';
      debugPrint('خطأ في حذف مرفق الرسالة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مرفقات رسالة محددة
  Future<void> getAttachmentsByMessage(int messageId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final attachments = await _apiService.getAttachmentsByMessage(messageId);
      _messageAttachments.assignAll(attachments);
      debugPrint('تم تحميل ${attachments.length} مرفق للرسالة $messageId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مرفقات الرسالة: $e';
      debugPrint('خطأ في تحميل مرفقات الرسالة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تنزيل مرفق
  Future<String?> downloadAttachment(int attachmentId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.downloadAttachment(attachmentId);
      debugPrint('تم تنزيل المرفق: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تنزيل المرفق: $e';
      debugPrint('خطأ في تنزيل المرفق: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على رابط المعاينة للمرفق
  Future<String?> getPreviewUrl(int attachmentId) async {
    try {
      final previewUrl = await _apiService.getAttachmentPreview(attachmentId);
      debugPrint('تم الحصول على رابط المعاينة: $previewUrl');
      return previewUrl;
    } catch (e) {
      debugPrint('خطأ في الحصول على رابط المعاينة: $e');
      return null;
    }
  }

  /// فحص المرفق للفيروسات
  Future<bool> scanAttachment(int attachmentId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final isClean = await _apiService.validateAttachment(attachmentId);
      if (isClean) {
        debugPrint('المرفق آمن');
      } else {
        _error.value = 'تم اكتشاف تهديد في المرفق';
        debugPrint('تم اكتشاف تهديد في المرفق');
      }
      return isClean;
    } catch (e) {
      _error.value = 'خطأ في فحص المرفق: $e';
      debugPrint('خطأ في فحص المرفق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// ضغط المرفق (محاكاة - غير متوفر في API)
  Future<bool> compressAttachment(int attachmentId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاكاة عملية الضغط
      await Future.delayed(const Duration(seconds: 2));
      debugPrint('تم ضغط المرفق بنجاح (محاكاة)');
      return true;
    } catch (e) {
      _error.value = 'خطأ في ضغط المرفق: $e';
      debugPrint('خطأ في ضغط المرفق: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على إحصائيات المرفقات
  Future<Map<String, dynamic>?> getAttachmentStatistics() async {
    try {
      final stats = await _apiService.getAttachmentsStats();
      debugPrint('تم تحميل إحصائيات المرفقات');
      return stats;
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المرفقات: $e');
      return null;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allAttachments.where((attachment) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!attachment.fileName.toLowerCase().contains(query) &&
            !attachment.fileType.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح الرسالة
      if (_messageFilter.value != null && attachment.messageId != _messageFilter.value) {
        return false;
      }

      // مرشح نوع الملف
      if (_fileTypeFilter.value != null && attachment.fileType != _fileTypeFilter.value) {
        return false;
      }

      // مرشح الرافع
      if (_uploaderFilter.value != null && attachment.uploadedBy != _uploaderFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredAttachments.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح الرسالة
  void setMessageFilter(int? messageId) {
    _messageFilter.value = messageId;
    _applyFilters();
  }

  /// تعيين مرشح نوع الملف
  void setFileTypeFilter(String? fileType) {
    _fileTypeFilter.value = fileType;
    _applyFilters();
  }

  /// تعيين مرشح الرافع
  void setUploaderFilter(int? uploaderId) {
    _uploaderFilter.value = uploaderId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _messageFilter.value = null;
    _fileTypeFilter.value = null;
    _uploaderFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllAttachments();
  }
}
