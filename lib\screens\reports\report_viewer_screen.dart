import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';

import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';
import '../../repositories/report_repository.dart';
import '../../services/report_export_service.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/empty_state_widget.dart';
import '../widgets/reporting/report_visualization_widget.dart';

/// شاشة عرض التقرير
/// تعرض تفاصيل التقرير والبيانات والتصورات المرئية
class ReportViewerScreen extends StatefulWidget {
  final String reportId;

  const ReportViewerScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ReportViewerScreen> createState() => _ReportViewerScreenState();
}

class _ReportViewerScreenState extends State<ReportViewerScreen> with SingleTickerProviderStateMixin {
  final ReportRepository _reportRepository = ReportRepository();
  final ReportExportService _exportService = ReportExportService();


  late TabController _tabController;
  EnhancedReport? _report;
  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _reportData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReport();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل التقرير
  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final reportId = int.tryParse(widget.reportId);
      if (reportId == null) {
        throw Exception('معرف التقرير غير صحيح');
      }

      final report = await _reportRepository.getReportById(reportId.toString());
      if (report != null) {
        _report = EnhancedReport.fromReport(report);
      }
      if (_report == null) {
        throw Exception('التقرير غير موجود');
      }

      // تحميل بيانات التقرير
      await _loadReportData();
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل بيانات التقرير
  Future<void> _loadReportData() async {
    try {
      // في الوقت الحالي، نستخدم بيانات وهمية
      // يمكن تطوير هذا لاحقاً لتحميل البيانات الفعلية من API
      _reportData = _generateSampleData();
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات التقرير: $e');
      _reportData = [];
    }
  }

  /// توليد بيانات عينة للتقرير
  List<Map<String, dynamic>> _generateSampleData() {
    switch (_report!.type) {
      case ReportType.taskStatus:
        return [
          {'status': 'مكتملة', 'count': 45, 'percentage': 60.0},
          {'status': 'قيد التنفيذ', 'count': 20, 'percentage': 26.7},
          {'status': 'معلقة', 'count': 10, 'percentage': 13.3},
        ];
      case ReportType.userPerformance:
        return [
          {'userName': 'أحمد محمد', 'completionRate': 85.5, 'tasksCompleted': 17},
          {'userName': 'فاطمة علي', 'completionRate': 92.3, 'tasksCompleted': 24},
          {'userName': 'محمد حسن', 'completionRate': 78.9, 'tasksCompleted': 15},
        ];
      default:
        return [
          {'label': 'عنصر 1', 'value': 100},
          {'label': 'عنصر 2', 'value': 200},
          {'label': 'عنصر 3', 'value': 150},
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_report?.title ?? 'عرض التقرير'),
        actions: [
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReport,
            tooltip: 'تحديث',
          ),
          // زر التصدير
          PopupMenuButton<ReportFormat>(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير',
            onSelected: _exportReport,
            itemBuilder: (context) => _exportService.getSupportedFormats()
                .map((format) => PopupMenuItem<ReportFormat>(
                      value: format,
                      child: Row(
                        children: [
                          Icon(_exportService.getFormatIcon(format)),
                          const SizedBox(width: 8),
                          Text(_exportService.getFormatName(format)),
                        ],
                      ),
                    ))
                .toList(),
          ),
          // زر المشاركة
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareReport,
            tooltip: 'مشاركة',
          ),
        ],
        bottom: _report != null
            ? TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'الملخص'),
                  Tab(text: 'البيانات'),
                  Tab(text: 'الرسوم البيانية'),
                ],
              )
            : null,
      ),
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadReport,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_report == null) {
      return const EmptyStateWidget(
        icon: Icons.bar_chart,
        message: 'التقرير غير موجود',
        description: 'لم يتم العثور على التقرير المطلوب.',
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildSummaryTab(),
        _buildDataTab(),
        _buildChartsTab(),
      ],
    );
  }

  /// بناء تبويب الملخص
  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات التقرير
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات التقرير',
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow('العنوان', _report!.title),
                  if (_report!.description != null) ...[
                    const SizedBox(height: 8),
                    _buildInfoRow('الوصف', _report!.description!),
                  ],
                  const SizedBox(height: 8),
                  _buildInfoRow('النوع', _getReportTypeName(_report!.type)),
                  const SizedBox(height: 8),
                  _buildInfoRow('تاريخ الإنشاء',
                    DateFormat('yyyy/MM/dd HH:mm').format(_report!.createdAt)
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow('آخر تحديث',
                    _report!.updatedAt != null
                        ? DateFormat('yyyy/MM/dd HH:mm').format(_report!.updatedAt!)
                        : 'غير محدد'
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // إحصائيات سريعة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إحصائيات سريعة',
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildStatsGrid(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب البيانات
  Widget _buildDataTab() {
    if (_reportData.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.table_chart,
        message: 'لا توجد بيانات',
        description: 'لا توجد بيانات متاحة لهذا التقرير.',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columns: _buildDataColumns(),
            rows: _buildDataRows(),
          ),
        ),
      ),
    );
  }

  /// بناء تبويب الرسوم البيانية
  Widget _buildChartsTab() {
    if (_report!.visualizations.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.insert_chart,
        message: 'لا توجد رسوم بيانية',
        description: 'لا توجد تصورات مرئية محددة لهذا التقرير.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _report!.visualizations.length,
      itemBuilder: (context, index) {
        final visualization = _report!.visualizations[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  visualization.title,
                  style: AppStyles.titleMedium,
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 300,
                  child: ReportVisualizationWidget(
                    visualization: visualization,
                    data: _reportData,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: AppStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppStyles.bodyMedium,
          ),
        ),
      ],
    );
  }

  /// بناء شبكة الإحصائيات
  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: ResponsiveHelper.isMobile(context) ? 2 : 4,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('إجمالي السجلات', '${_reportData.length}', Icons.list),
        _buildStatCard('آخر تحديث', 'اليوم', Icons.update),
        _buildStatCard('الحالة', 'نشط', Icons.check_circle),
        _buildStatCard('المشاهدات', '0', Icons.visibility),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: AppColors.primary),
            const SizedBox(height: 4),
            Text(
              value,
              style: AppStyles.titleSmall,
              textAlign: TextAlign.center,
            ),
            Text(
              title,
              style: AppStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أعمدة الجدول
  List<DataColumn> _buildDataColumns() {
    if (_reportData.isEmpty) return [];

    return _reportData.first.keys
        .map((key) => DataColumn(label: Text(key)))
        .toList();
  }

  /// بناء صفوف الجدول
  List<DataRow> _buildDataRows() {
    return _reportData.map((row) {
      return DataRow(
        cells: row.values
            .map((value) => DataCell(Text(value.toString())))
            .toList(),
      );
    }).toList();
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'تقرير حالة المهام';
      case ReportType.userPerformance:
        return 'تقرير أداء المستخدمين';
      case ReportType.departmentPerformance:
        return 'تقرير أداء الأقسام';
      case ReportType.timeTracking:
        return 'تقرير تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقرير تقدم المهام';
      case ReportType.taskDetails:
        return 'تقرير تفاصيل المهام';
      case ReportType.taskCompletion:
        return 'تقرير إكمال المهام';
      case ReportType.userActivity:
        return 'تقرير نشاط المستخدمين';
      case ReportType.departmentWorkload:
        return 'تقرير عبء العمل للأقسام';
      case ReportType.projectStatus:
        return 'تقرير حالة المشاريع';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'تقرير';
    }
  }

  /// تصدير التقرير
  Future<void> _exportReport(ReportFormat format) async {
    try {
      final reportId = int.parse(widget.reportId);
      final filePath = await _exportService.exportReport(
        reportId: reportId,
        format: format,
      );

      if (filePath != null) {
        Get.snackbar(
          'نجح التصدير',
          'تم تصدير التقرير بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        throw Exception('فشل في تصدير التقرير');
      }
    } catch (e) {
      Get.snackbar(
        'خطأ في التصدير',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// مشاركة التقرير
  void _shareReport() {
    // تنفيذ مشاركة التقرير
    Get.snackbar(
      'مشاركة التقرير',
      'سيتم تطوير هذه الميزة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
