import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../reports/task_completion_report.dart';
import '../../services/api/task_api_service.dart';

class TaskCompletionReportScreen extends StatelessWidget {
  const TaskCompletionReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير إنجاز المهام'),
        actions: [_buildRefreshButton()],
      ),
      body: FutureBuilder<TaskCompletionReport>(
        future: _getReport(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } 
          else if (snapshot.hasData) {
            final report = snapshot.data!;
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('إجمالي المهام: ${report.totalTasks}', style: const TextStyle(fontSize: 16)),
                  Text('المهام المنجزة: ${report.completedTasks}', style: const TextStyle(fontSize: 16)),
                  Text('نسبة الإنجاز: ${report.completionRate.toStringAsFixed(2)}%', style: const TextStyle(fontSize: 16)),
                  Text('متوسط وقت الإنجاز: ${report.averageCompletionTime.toStringAsFixed(2)} ساعة', style: const TextStyle(fontSize: 16)),
                  const SizedBox(height: 20),
                  _buildCompletionChart(report.completedTasks, report.totalTasks),
                ],
              ),
            );
          } else {
            return const Center(child: Text('No data'));
          }
        },
      ),
    );
  }

  Widget _buildCompletionChart(int completed, int total) {
    return SizedBox(
      height: 200,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 150,
            height: 150,
            child: CircularProgressIndicator(
                value: total > 0 ? completed / total : 0,
       ),
      ),
 ],
              ),
            );
          } 
          
          
          
        
      
    
  }

  Future<TaskCompletionReport> _getReport() async {
    final tasks = await TaskApiService().getAllTasks();
    return TaskCompletionReport.fromTasks(tasks);
  }

  Widget _buildRefreshButton() {
    return IconButton(
      icon: const Icon(Icons.refresh),
      onPressed: () {
        // يمكنك تحديث الشاشة عن طريق استدعاء نفس الشاشة مرة أخرى
        Get.offAll(() => const TaskCompletionReportScreen());
      },
    );
  }
