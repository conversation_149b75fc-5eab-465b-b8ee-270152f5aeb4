import 'package:flutter/material.dart';
import 'inherited_widget_helper.dart';

/// Helper class for responsive design
class ResponsiveHelper {
  /// Screen width breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Check if the screen is mobile size
  static bool isMobile(BuildContext context) {
    final width = _getScreenWidth(context);
    return width < mobileBreakpoint;
  }

  /// Check if the screen is small (for compatibility with archive module)
  static bool isSmallScreen(BuildContext context) {
    final width = _getScreenWidth(context);
    return width < mobileBreakpoint;
  }

  /// Check if the screen is medium (for compatibility with archive module)
  static bool isMediumScreen(BuildContext context) {
    final width = _getScreenWidth(context);
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if the screen is large (for compatibility with archive module)
  static bool isLargeScreen(BuildContext context) {
    final width = _getScreenWidth(context);
    return width >= desktopBreakpoint;
  }

  /// Check if the screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = _getScreenWidth(context);
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if the screen is desktop size
  static bool isDesktop(BuildContext context) {
    final width = _getScreenWidth(context);
    return width >= desktopBreakpoint;
  }

  /// Get the screen width
  static double screenWidth(BuildContext context) {
    return _getScreenWidth(context);
  }

  /// Get the screen height
  static double screenHeight(BuildContext context) {
    return _getScreenHeight(context);
  }

  /// Get the appropriate padding based on screen size
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24.0);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  /// Safely get screen width using InheritedWidgetHelper
  static double _getScreenWidth(BuildContext context) {
    try {
      // First try to get it safely
      final mediaQuery = InheritedWidgetHelper.getMediaQueryDataSafely(context);
      if (mediaQuery != null) {
        return mediaQuery.size.width;
      }

      // If that fails, try the direct approach (might throw an error)
      return MediaQuery.of(context).size.width;
    } catch (e) {
      // Return a default value if all else fails
      debugPrint('Error getting screen width: $e');
      return 360.0; // Default mobile width
    }
  }

  /// Safely get screen height using InheritedWidgetHelper
  static double _getScreenHeight(BuildContext context) {
    try {
      // First try to get it safely
      final mediaQuery = InheritedWidgetHelper.getMediaQueryDataSafely(context);
      if (mediaQuery != null) {
        return mediaQuery.size.height;
      }

      // If that fails, try the direct approach (might throw an error)
      return MediaQuery.of(context).size.height;
    } catch (e) {
      // Return a default value if all else fails
      debugPrint('Error getting screen height: $e');
      return 640.0; // Default mobile height
    }
  }

  /// Get the appropriate column count for a grid based on screen size
  static int getGridColumnCount(BuildContext context) {
    if (isMobile(context)) {
      return 1;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 3;
    }
  }

  /// Get the appropriate font size based on screen size
  static double getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    final width = MediaQuery.of(context).size.width;

    // Scale font size based on screen width, with limits
    if (width < mobileBreakpoint) {
      // For very small screens, reduce font size slightly
      return baseFontSize * 0.9;
    } else if (width > desktopBreakpoint) {
      // For very large screens, increase font size slightly
      return baseFontSize * 1.1;
    }

    // For medium screens, use the base font size
    return baseFontSize;
  }

  /// Get the appropriate widget based on screen size
  static Widget getResponsiveWidget({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    if (isDesktop(context) && desktop != null) {
      return desktop;
    } else if (isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }
}

/// A widget that adapts its layout based on screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveHelper.getResponsiveWidget(
      context: context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}
