import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../models/chart_enums.dart';
import '../../../../models/advanced_filter_options.dart';
import 'chart_factory.dart';
import 'chart_theme_manager.dart';
import 'chart_error_handler.dart';


/// المكون الموحد للمخططات
/// 
/// يوفر واجهة موحدة لعرض جميع أنواع المخططات مع دعم:
/// - التصفية المتقدمة
/// - التصدير
/// - معالجة الأخطاء
/// - التخزين المؤقت
/// - التحديث التلقائي
class UnifiedChartWidget extends StatefulWidget {
  /// نوع المخطط
  final ChartType chartType;
  
  /// عنوان المخطط
  final String title;
  
  /// مصدر البيانات
  final String dataSource;
  
  /// خيارات التصفية
  final AdvancedFilterOptions? filterOptions;
  
  /// إعدادات المخطط
  final Map<String, dynamic> settings;
  
  /// دالة تحميل البيانات
  final Future<Map<String, dynamic>> Function(AdvancedFilterOptions?) dataLoader;
  
  /// دالة التصدير
  final Future<String?> Function(String format)? onExport;
  
  /// دالة تحديث الإعدادات
  final Function(Map<String, dynamic>)? onSettingsChanged;
  
  /// هل يظهر شريط الأدوات
  final bool showToolbar;
  
  /// هل يظهر لوحة التصفية
  final bool showFilterPanel;
  
  /// ارتفاع المخطط
  final double? height;
  
  /// عرض المخطط
  final double? width;

  const UnifiedChartWidget({
    super.key,
    required this.chartType,
    required this.title,
    required this.dataSource,
    required this.dataLoader,
    this.filterOptions,
    this.settings = const {},
    this.onExport,
    this.onSettingsChanged,
    this.showToolbar = true,
    this.showFilterPanel = false,
    this.height,
    this.width,
  });

  @override
  State<UnifiedChartWidget> createState() => _UnifiedChartWidgetState();
}

class _UnifiedChartWidgetState extends State<UnifiedChartWidget> 
    with AutomaticKeepAliveClientMixin {
  
  // حالة المكون
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _data;
  AdvancedFilterOptions? _currentFilters;
  
  // مدراء النظام
  late final ChartThemeManager _themeManager;
  late final ChartErrorHandler _errorHandler;
  
  // مفتاح المخطط للتصدير
  final GlobalKey _chartKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _themeManager = ChartThemeManager();
    _errorHandler = ChartErrorHandler();
    _currentFilters = widget.filterOptions;
    _loadData();
  }

  @override
  bool get wantKeepAlive => true;

  /// تحميل البيانات
  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final data = await widget.dataLoader(_currentFilters);
      
      if (mounted) {
        setState(() {
          _data = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = _errorHandler.getErrorMessage(e);
          _isLoading = false;
        });
      }
    }
  }

  /// تحديث الفلاتر
  void _updateFilters(AdvancedFilterOptions filters) {
    setState(() {
      _currentFilters = filters;
    });
    _loadData();
  }

  /// تصدير المخطط
  Future<void> _exportChart(String format) async {
    if (widget.onExport != null) {
      try {
        final result = await widget.onExport!(format);
        if (result != null && mounted) {
          Get.snackbar(
            'تم التصدير',
            'تم حفظ الملف في: $result',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );
        }
      } catch (e) {
        if (mounted) {
          Get.snackbar(
            'خطأ في التصدير',
            _errorHandler.getErrorMessage(e),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Container(
      height: widget.height,
      width: widget.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // شريط الأدوات
          if (widget.showToolbar) _buildToolbar(),
          
          // لوحة التصفية
          if (widget.showFilterPanel) _buildFilterPanel(),
          
          // محتوى المخطط
          Expanded(child: _buildChartContent()),
        ],
      ),
    );
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _themeManager.getHeaderColor(),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          // عنوان المخطط
          Expanded(
            child: Text(
              widget.title,
              style: _themeManager.getTitleStyle(),
            ),
          ),
          
          // أدوات التحكم
          ExportControls(
            onExport: _exportChart,
            chartKey: _chartKey,
          ),
          
          // زر إعادة التحميل
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'إعادة تحميل',
          ),
          
          // زر الإعدادات
          if (widget.onSettingsChanged != null)
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _showSettingsDialog(),
              tooltip: 'الإعدادات',
            ),
        ],
      ),
    );
  }

  /// بناء لوحة التصفية
  Widget _buildFilterPanel() {
    return FilterPanel(
      currentFilters: _currentFilters,
      onFiltersChanged: _updateFilters,
    );
  }

  /// بناء محتوى المخطط
  Widget _buildChartContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return _errorHandler.buildErrorWidget(
        _error!,
        onRetry: _loadData,
      );
    }

    if (_data == null || _data!.isEmpty) {
      return _buildEmptyState();
    }

    return RepaintBoundary(
      key: _chartKey,
      child: ChartFactory.createChart(
        type: widget.chartType,
        data: _data!,
        settings: widget.settings,
        theme: _themeManager.getCurrentTheme(),
      ),
    );
  }

  /// بناء حالة البيانات الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات لعرضها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog() {
    // TODO: تنفيذ حوار الإعدادات
  }
}
