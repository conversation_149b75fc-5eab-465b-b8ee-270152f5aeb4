using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using webApi.Models.Auth;

namespace webApi.Attributes;

/// <summary>
/// خاصية التفويض المبني على الأدوار
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class RoleAuthorizeAttribute : Attribute, IAuthorizationFilter
{
    private readonly UserRole[] _allowedRoles;

    /// <summary>
    /// إنشاء خاصية التفويض مع الأدوار المسموحة
    /// </summary>
    /// <param name="allowedRoles">الأدوار المسموحة</param>
    public RoleAuthorizeAttribute(params UserRole[] allowedRoles)
    {
        _allowedRoles = allowedRoles;
    }

    /// <summary>
    /// تنفيذ التفويض
    /// </summary>
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // التحقق من وجود المصادقة
        if (!context.HttpContext.User.Identity?.IsAuthenticated ?? true)
        {
            context.Result = new UnauthorizedObjectResult(new
            {
                message = "يجب تسجيل الدخول للوصول إلى هذا المورد",
                error = "Unauthorized"
            });
            return;
        }

        // الحصول على دور المستخدم
        var userRoleClaim = context.HttpContext.User.FindFirst("UserRole")?.Value;
        if (!int.TryParse(userRoleClaim, out int roleValue) || !Enum.IsDefined(typeof(UserRole), roleValue))
        {
            context.Result = new ForbidResult();
            return;
        }

        var userRole = (UserRole)roleValue;

        // التحقق من الصلاحية
        if (!_allowedRoles.Contains(userRole))
        {
            context.Result = new ObjectResult(new
            {
                message = "ليس لديك صلاحية للوصول إلى هذا المورد",
                error = "Forbidden",
                requiredRoles = _allowedRoles.Select(r => r.ToString()).ToArray(),
                userRole = userRole.ToString()
            })
            {
                StatusCode = 403
            };
            return;
        }
    }
}

/// <summary>
/// خاصية التفويض للمديرين فقط
/// </summary>
public class AdminOnlyAttribute : RoleAuthorizeAttribute
{
    public AdminOnlyAttribute() : base(UserRole.Admin, UserRole.SuperAdmin)
    {
    }
}

/// <summary>
/// خاصية التفويض للمديرين والمشرفين
/// </summary>
public class ManagerOrAboveAttribute : RoleAuthorizeAttribute
{
    public ManagerOrAboveAttribute() : base(UserRole.Manager, UserRole.Admin, UserRole.SuperAdmin)
    {
    }
}

/// <summary>
/// خاصية التفويض للمشرفين وما فوق
/// </summary>
public class SupervisorOrAboveAttribute : RoleAuthorizeAttribute
{
    public SupervisorOrAboveAttribute() : base(UserRole.Supervisor, UserRole.Manager, UserRole.Admin, UserRole.SuperAdmin)
    {
    }
}

/// <summary>
/// خاصية التفويض لمدير النظام فقط
/// </summary>
public class SuperAdminOnlyAttribute : RoleAuthorizeAttribute
{
    public SuperAdminOnlyAttribute() : base(UserRole.SuperAdmin)
    {
    }
}
