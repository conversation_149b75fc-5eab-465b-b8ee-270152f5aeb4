import 'dart:typed_data';

/// ملف بديل لـ dart:io على الويب
///
/// يوفر فئة File وDirectory بديلة للاستخدام على الويب
class WebFile {
  final String path;
  final Uint8List? bytes;
  final String? name;

  WebFile(this.path, {this.bytes, this.name});

  /// الحصول على حجم الملف
  Future<int> length() async {
    return bytes?.length ?? 0;
  }

  /// التحقق من وجود الملف
  Future<bool> exists() async {
    return bytes != null;
  }

  /// التحقق من وجود الملف بشكل متزامن
  bool existsSync() {
    return bytes != null;
  }

  /// نسخ الملف إلى مسار آخر
  Future<WebFile> copy(String newPath) async {
    return WebFile(newPath, bytes: bytes, name: name);
  }

  /// قراءة الملف كمصفوفة بايت
  Future<List<int>> readAsBytes() async {
    return bytes?.toList() ?? [];
  }

  /// كتابة مصفوفة بايت إلى الملف
  Future<WebFile> writeAsBytes(List<int> bytes) async {
    return WebFile(path, bytes: Uint8List.fromList(bytes), name: name);
  }

  /// حذف الملف
  Future<void> delete() async {
    // على الويب، لا يمكننا حذف الملفات
    // لذلك لا نفعل شيئًا
  }

  /// الحصول على اسم الملف
  String get fileName => name ?? path.split('/').last;
}

/// فئة Directory بديلة للاستخدام على الويب
class Directory {
  final String path;

  Directory(this.path);

  /// التحقق من وجود المجلد
  Future<bool> exists() async {
    // على الويب، لا يمكننا التحقق من وجود المجلدات
    // لذلك نفترض أنها موجودة دائمًا
    return true;
  }

  /// إنشاء المجلد
  Future<Directory> create({bool recursive = false}) async {
    // على الويب، لا يمكننا إنشاء المجلدات
    // لذلك نعيد هذا المجلد
    return this;
  }
}

/// Alias للتوافق مع الكود الموجود
typedef File = WebFile;
