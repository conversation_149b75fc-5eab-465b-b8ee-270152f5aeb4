using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;
using System.ComponentModel.DataAnnotations;
using TaskModel = webApi.Models.Task;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة المهام الفرعية
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class SubtasksController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly INotificationService _notificationService;

        public SubtasksController(TasksDbContext context, INotificationService notificationService)
        {
            _context = context;
            _notificationService = notificationService;
        }

        /// <summary>
        /// الحصول على جميع المهام الفرعية
        /// </summary>
        /// <returns>قائمة بجميع المهام الفرعية</returns>
        /// <response code="200">إرجاع قائمة المهام الفرعية</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Subtask>>> GetSubtasks()
        {
            return await _context.Subtasks
                .Include(s => s.Task)
                .OrderByDescending(s => s.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على مهمة فرعية محددة
        /// </summary>
        /// <param name="id">معرف المهمة الفرعية</param>
        /// <returns>المهمة الفرعية المطلوبة</returns>
        /// <response code="200">إرجاع المهمة الفرعية</response>
        /// <response code="404">المهمة الفرعية غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Subtask>> GetSubtask(int id)
        {
            var subtask = await _context.Subtasks
                .Include(s => s.Task)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (subtask == null)
            {
                return NotFound();
            }

            return subtask;
        }

        /// <summary>
        /// الحصول على المهام الفرعية لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>قائمة المهام الفرعية للمهمة</returns>
        /// <response code="200">إرجاع قائمة المهام الفرعية</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Subtask>>> GetSubtasksByTask(int taskId)
        {
            return await _context.Subtasks
                .Where(s => s.TaskId == taskId)
                .OrderBy(s => s.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على المهام الفرعية المكتملة لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>قائمة المهام الفرعية المكتملة</returns>
        /// <response code="200">إرجاع قائمة المهام الفرعية المكتملة</response>
        [HttpGet("task/{taskId}/completed")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Subtask>>> GetCompletedSubtasksByTask(int taskId)
        {
            return await _context.Subtasks
                .Where(s => s.TaskId == taskId && s.IsCompleted)
                .OrderBy(s => s.CompletedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على المهام الفرعية غير المكتملة لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>قائمة المهام الفرعية غير المكتملة</returns>
        /// <response code="200">إرجاع قائمة المهام الفرعية غير المكتملة</response>
        [HttpGet("task/{taskId}/pending")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Subtask>>> GetPendingSubtasksByTask(int taskId)
        {
            return await _context.Subtasks
                .Where(s => s.TaskId == taskId && !s.IsCompleted)
                .OrderBy(s => s.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// عدد المهام الفرعية لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>عدد المهام الفرعية</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("task/{taskId}/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetSubtasksCount(int taskId)
        {
            var count = await _context.Subtasks
                .CountAsync(s => s.TaskId == taskId);

            return count;
        }

        /// <summary>
        /// عدد المهام الفرعية المكتملة لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>عدد المهام الفرعية المكتملة</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("task/{taskId}/completed/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetCompletedSubtasksCount(int taskId)
        {
            var count = await _context.Subtasks
                .CountAsync(s => s.TaskId == taskId && s.IsCompleted);

            return count;
        }

        /// <summary>
        /// نسبة إنجاز المهام الفرعية لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة الرئيسية</param>
        /// <returns>نسبة الإنجاز (0-100)</returns>
        /// <response code="200">إرجاع نسبة الإنجاز</response>
        [HttpGet("task/{taskId}/completion-percentage")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<double>> GetSubtasksCompletionPercentage(int taskId)
        {
            var totalCount = await _context.Subtasks
                .CountAsync(s => s.TaskId == taskId);

            if (totalCount == 0)
            {
                return 0;
            }

            var completedCount = await _context.Subtasks
                .CountAsync(s => s.TaskId == taskId && s.IsCompleted);

            var percentage = (double)completedCount / totalCount * 100;
            return Math.Round(percentage, 2);
        }

        /// <summary>
        /// إنشاء مهمة فرعية جديدة
        /// </summary>
        /// <param name="request">بيانات المهمة الفرعية</param>
        /// <returns>المهمة الفرعية المُنشأة</returns>
        /// <response code="201">تم إنشاء المهمة الفرعية بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Subtask>> PostSubtask(CreateSubtaskRequest request)
        {
            try
            {
                // طباعة البيانات المستلمة للتشخيص
                Console.WriteLine($"استلام طلب إنشاء مهمة فرعية: TaskId={request.TaskId}, Title={request.Title}");

                if (!ModelState.IsValid)
                {
                    Console.WriteLine("ModelState غير صالح:");
                    foreach (var error in ModelState)
                    {
                        Console.WriteLine($"  {error.Key}: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                    return BadRequest(ModelState);
                }

                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(request.Title))
                {
                    return BadRequest("عنوان المهمة الفرعية مطلوب");
                }

                if (request.TaskId <= 0)
                {
                    return BadRequest("معرف المهمة الرئيسية غير صالح");
                }

                // التحقق من وجود المهمة الرئيسية
                var taskExists = await _context.Tasks.AnyAsync(t => t.Id == request.TaskId && !t.IsDeleted);
                if (!taskExists)
                {
                    Console.WriteLine($"المهمة الرئيسية غير موجودة: TaskId={request.TaskId}");
                    return BadRequest("المهمة الرئيسية غير موجودة");
                }

                // إنشاء كائن المهمة الفرعية
                var subtask = new Subtask
                {
                    TaskId = request.TaskId,
                    Title = request.Title,
                    IsCompleted = false,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    CompletedAt = null
                };

                Console.WriteLine($"إضافة المهمة الفرعية إلى قاعدة البيانات...");
                _context.Subtasks.Add(subtask);
                await _context.SaveChangesAsync();

                Console.WriteLine($"تم إنشاء المهمة الفرعية بنجاح: Id={subtask.Id}");

                // إرسال إشعارات للمستخدمين المعنيين بالمهمة الرئيسية
                try
                {
                    await CreateSubtaskNotifications(subtask);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في إرسال إشعارات المهمة الفرعية: {ex.Message}");
                }

                return CreatedAtAction("GetSubtask", new { id = subtask.Id }, subtask);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء المهمة الفرعية: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                return StatusCode(500, $"خطأ داخلي في الخادم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مهمة فرعية
        /// </summary>
        /// <param name="id">معرف المهمة الفرعية</param>
        /// <param name="subtask">بيانات المهمة الفرعية المحدثة</param>
        /// <returns>المهمة الفرعية المحدثة</returns>
        /// <response code="200">تم تحديث المهمة الفرعية بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">المهمة الفرعية غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Subtask>> PutSubtask(int id, Subtask subtask)
        {
            try
            {
                Console.WriteLine($"تحديث المهمة الفرعية: Id={id}, Title={subtask.Title}");

                if (id != subtask.Id)
                {
                    return BadRequest("معرف المهمة الفرعية لا يتطابق");
                }

                // التحقق من وجود المهمة الفرعية
                var existingSubtask = await _context.Subtasks.FindAsync(id);
                if (existingSubtask == null)
                {
                    return NotFound("المهمة الفرعية غير موجودة");
                }

                // تحديث البيانات
                existingSubtask.Title = subtask.Title;
                existingSubtask.IsCompleted = subtask.IsCompleted;
                if (subtask.IsCompleted && existingSubtask.CompletedAt == null)
                {
                    existingSubtask.CompletedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }
                else if (!subtask.IsCompleted)
                {
                    existingSubtask.CompletedAt = null;
                }

                await _context.SaveChangesAsync();

                Console.WriteLine($"تم تحديث المهمة الفرعية بنجاح: Id={id}");

                // إرسال إشعارات للمستخدمين المعنيين بالمهمة الرئيسية
                try
                {
                    await UpdateSubtaskNotifications(existingSubtask);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في إرسال إشعارات تحديث المهمة الفرعية: {ex.Message}");
                }

                return Ok(existingSubtask);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!SubtaskExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث المهمة الفرعية: {ex.Message}");
                return StatusCode(500, $"خطأ داخلي في الخادم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديد مهمة فرعية كمكتملة
        /// </summary>
        /// <param name="id">معرف المهمة الفرعية</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث المهمة الفرعية بنجاح</response>
        /// <response code="404">المهمة الفرعية غير موجودة</response>
        [HttpPatch("{id}/complete")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CompleteSubtask(int id)
        {
            var subtask = await _context.Subtasks.FindAsync(id);
            if (subtask == null)
            {
                return NotFound();
            }

            subtask.IsCompleted = true;
            subtask.CompletedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// تحديد مهمة فرعية كغير مكتملة
        /// </summary>
        /// <param name="id">معرف المهمة الفرعية</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث المهمة الفرعية بنجاح</response>
        /// <response code="404">المهمة الفرعية غير موجودة</response>
        [HttpPatch("{id}/uncomplete")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UncompleteSubtask(int id)
        {
            var subtask = await _context.Subtasks.FindAsync(id);
            if (subtask == null)
            {
                return NotFound();
            }

            subtask.IsCompleted = false;
            subtask.CompletedAt = null;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف مهمة فرعية
        /// </summary>
        /// <param name="id">معرف المهمة الفرعية</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف المهمة الفرعية بنجاح</response>
        /// <response code="404">المهمة الفرعية غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteSubtask(int id)
        {
            var subtask = await _context.Subtasks.FindAsync(id);
            if (subtask == null)
            {
                return NotFound();
            }

            _context.Subtasks.Remove(subtask);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool SubtaskExists(int id)
        {
            return _context.Subtasks.Any(e => e.Id == id);
        }

        /// <summary>
        /// إرسال إشعارات إنشاء مهمة فرعية جديدة
        /// </summary>
        private async System.Threading.Tasks.Task CreateSubtaskNotifications(Subtask subtask)
        {
            try
            {
                // الحصول على المهمة الرئيسية والمستخدمين المعنيين
                TaskModel? task = await _context.Tasks
                    .Include(t => t.TaskAccessUsers)
                    .FirstOrDefaultAsync(t => t.Id == subtask.TaskId);

                if (task == null) return;

                // جمع معرفات المستخدمين المعنيين (باستثناء منشئ المهمة الفرعية)
                var userIds = new List<int>();

                // إضافة منشئ المهمة الأساسية (إذا لم يكن هو منشئ المهمة الفرعية)
                if (task.CreatorId != subtask.CreatedAt)
                {
                    userIds.Add(task.CreatorId);
                }

                // إضافة المسند له إذا كان موجوداً (وليس منشئ المهمة الفرعية)
                if (task.AssigneeId.HasValue &&
                    task.AssigneeId.Value != subtask.CreatedAt &&
                    !userIds.Contains(task.AssigneeId.Value))
                {
                    userIds.Add(task.AssigneeId.Value);
                }

                // إضافة المستخدمين الذين لهم وصول للمهمة (باستثناء منشئ المهمة الفرعية)
                if (task.TaskAccessUsers != null)
                {
                    userIds.AddRange(task.TaskAccessUsers
                        .Where(tu => tu.UserId != subtask.CreatedAt && !userIds.Contains(tu.UserId))
                        .Select(tu => tu.UserId));
                }

                if (userIds.Any())
                {
                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        "مهمة فرعية جديدة",
                        $"المهمة رقم #{task.Id}: تم إضافة مهمة فرعية جديدة '{subtask.Title}'",
                        "subtask_created",
                        task.Id
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إرسال إشعارات المهمة الفرعية: {ex.Message}");
            }
        }

        /// <summary>
        /// إرسال إشعارات تحديث مهمة فرعية
        /// </summary>
        private async System.Threading.Tasks.Task UpdateSubtaskNotifications(Subtask subtask)
        {
            try
            {
                // الحصول على المهمة الرئيسية والمستخدمين المعنيين
                TaskModel? task = await _context.Tasks
                    .Include(t => t.TaskAccessUsers)
                    .FirstOrDefaultAsync(t => t.Id == subtask.TaskId);

                if (task == null) return;

                // جمع معرفات المستخدمين المعنيين (باستثناء من قام بالتحديث)
                var userIds = new List<int>();

                // إضافة منشئ المهمة الأساسية (إذا لم يكن هو من قام بالتحديث)
                if (task.CreatorId != subtask.CreatedAt)
                {
                    userIds.Add(task.CreatorId);
                }

                // إضافة المسند له إذا كان موجوداً (وليس من قام بالتحديث)
                if (task.AssigneeId.HasValue &&
                    task.AssigneeId.Value != subtask.CreatedAt &&
                    !userIds.Contains(task.AssigneeId.Value))
                {
                    userIds.Add(task.AssigneeId.Value);
                }

                // إضافة المستخدمين الذين لهم وصول للمهمة (باستثناء من قام بالتحديث)
                if (task.TaskAccessUsers != null)
                {
                    userIds.AddRange(task.TaskAccessUsers
                        .Where(tu => tu.UserId != subtask.CreatedAt && !userIds.Contains(tu.UserId))
                        .Select(tu => tu.UserId));
                }

                if (userIds.Any())
                {
                    string notificationType = subtask.IsCompleted ? "subtask_completed" : "subtask_updated";
                    string title = subtask.IsCompleted ? "تم إكمال مهمة فرعية" : "تم تحديث مهمة فرعية";
                    string message = subtask.IsCompleted
                        ? $"المهمة رقم #{task.Id}: تم إكمال المهمة الفرعية '{subtask.Title}'"
                        : $"المهمة رقم #{task.Id}: تم تحديث المهمة الفرعية '{subtask.Title}'";

                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        title,
                        message,
                        notificationType,
                        task.Id
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إرسال إشعارات تحديث المهمة الفرعية: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// نموذج طلب إنشاء مهمة فرعية
    /// </summary>
    public class CreateSubtaskRequest
    {
        [Required(ErrorMessage = "معرف المهمة مطلوب")]
        public int TaskId { get; set; }

        [Required(ErrorMessage = "عنوان المهمة الفرعية مطلوب")]
        [StringLength(255, ErrorMessage = "عنوان المهمة الفرعية يجب أن يكون أقل من 255 حرف")]
        public string Title { get; set; } = string.Empty;
    }
}
