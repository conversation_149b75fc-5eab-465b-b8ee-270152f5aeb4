import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

class NotificationsService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();
  static bool _initialized = false;

  static Future<void> initialize(BuildContext context) async {
    if (_initialized) return;

    try {
      // إعدادات التهيئة لكل منصة
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) async {
          // يمكنك هنا توجيه المستخدم إلى شاشة معينة عند الضغط على الإشعار
          debugPrint('تم الضغط على الإشعار: ${response.payload}');
        },
      );

      _initialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
      // تعيين _initialized = true حتى لو فشلت التهيئة لتجنب محاولات متكررة
      _initialized = true;
    }
  }

  static Future<void> showSimpleNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_initialized) {
      debugPrint('⚠️ خدمة الإشعارات غير مهيئة، سيتم تجاهل الإشعار');
      return;
    }

    try {
      // NotificationDetails مع صوت افتراضي لجميع الأنظمة المدعومة
      const NotificationDetails notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel_id',
          'الإشعارات',
          channelDescription: 'إشعارات النظام',
          importance: Importance.max,
          priority: Priority.high,
          playSound: true,
        ),
        iOS: DarwinNotificationDetails(
          presentSound: true,
        ),
        // لا يوجد windows هنا، الصوت الافتراضي سيعمل في ويندوز إذا كان النظام يسمح بذلك
      );

      await _notificationsPlugin.show(
        0,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint('✅ تم إرسال الإشعار: $title');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
      // لا نرمي الخطأ لتجنب توقف التطبيق
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _initialized;

  /// إعادة تعيين حالة التهيئة (للاختبار)
  static void reset() {
    _initialized = false;
  }
}
