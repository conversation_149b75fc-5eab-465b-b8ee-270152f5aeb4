/// أنواع عناصر لوحة المعلومات الأساسية
enum BasicDashboardWidgetType {
  taskStatusChart,
  taskProgressChart,
  taskCounters,
  userPerformanceChart,
  departmentPerformanceChart,
  timeTrackingChart,
  taskList,
  kpi,
  custom,
}

/// نموذج لوحة المعلومات
class Dashboard {
  final String id;
  final String title;
  final String description;
  final List<SimpleDashboardWidget> widgets;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDefault;
  final int gridRows;
  final int gridColumns;

  const Dashboard({
    required this.id,
    required this.title,
    required this.description,
    required this.widgets,
    required this.createdAt,
    required this.updatedAt,
    this.isDefault = false,
    this.gridRows = 12,
    this.gridColumns = 12,
  });

  factory Dashboard.fromJson(Map<String, dynamic> json) {
    return Dashboard(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      widgets: (json['widgets'] as List?)
          ?.map((w) => SimpleDashboardWidget.fromJson(w as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDefault: json['isDefault'] as bool? ?? false,
      gridRows: json['gridRows'] as int? ?? 12,
      gridColumns: json['gridColumns'] as int? ?? 12,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'widgets': widgets.map((w) => w.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isDefault': isDefault,
      'gridRows': gridRows,
      'gridColumns': gridColumns,
    };
  }

  Dashboard copyWith({
    String? id,
    String? title,
    String? description,
    List<SimpleDashboardWidget>? widgets,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDefault,
    int? gridRows,
    int? gridColumns,
  }) {
    return Dashboard(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      widgets: widgets ?? this.widgets,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
      gridRows: gridRows ?? this.gridRows,
      gridColumns: gridColumns ?? this.gridColumns,
    );
  }

  @override
  String toString() {
    return 'Dashboard(id: $id, title: $title, widgetCount: ${widgets.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dashboard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج عنصر لوحة المعلومات البسيط
class SimpleDashboardWidget {
  final String id;
  final String title;
  final String type; // chart, table, metric, etc.
  final int row;
  final int column;
  final int width;
  final int height;
  final Map<String, dynamic> settings;
  final bool isExpandable;
  final bool isExpanded;
  final bool isRefreshable;

  const SimpleDashboardWidget({
    required this.id,
    required this.title,
    required this.type,
    required this.row,
    required this.column,
    required this.width,
    required this.height,
    required this.settings,
    this.isExpandable = true,
    this.isExpanded = false,
    this.isRefreshable = true,
  });

  factory SimpleDashboardWidget.fromJson(Map<String, dynamic> json) {
    return SimpleDashboardWidget(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      row: json['row'] as int,
      column: json['column'] as int,
      width: json['width'] as int,
      height: json['height'] as int,
      settings: Map<String, dynamic>.from(json['settings'] as Map),
      isExpandable: json['isExpandable'] as bool? ?? true,
      isExpanded: json['isExpanded'] as bool? ?? false,
      isRefreshable: json['isRefreshable'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'settings': settings,
      'row': row,
      'column': column,
      'width': width,
      'height': height,
      'isExpandable': isExpandable,
      'isExpanded': isExpanded,
      'isRefreshable': isRefreshable,
    };
  }

  SimpleDashboardWidget copyWith({
    String? id,
    String? title,
    String? type,
    Map<String, dynamic>? settings,
    int? row,
    int? column,
    int? width,
    int? height,
    bool? isExpandable,
    bool? isExpanded,
    bool? isRefreshable,
  }) {
    return SimpleDashboardWidget(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      settings: settings ?? this.settings,
      row: row ?? this.row,
      column: column ?? this.column,
      width: width ?? this.width,
      height: height ?? this.height,
      isExpandable: isExpandable ?? this.isExpandable,
      isExpanded: isExpanded ?? this.isExpanded,
      isRefreshable: isRefreshable ?? this.isRefreshable,
    );
  }

  /// الحصول على الإعدادات كـ Map
  Map<String, dynamic> get settingsMap => settings;

  @override
  String toString() {
    return 'SimpleDashboardWidget(id: $id, title: $title, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimpleDashboardWidget && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
