import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/chat_groups_controller.dart';
import '../controllers/group_members_controller.dart';
import '../controllers/messages_controller.dart';
import '../controllers/unified_chat_controller.dart';
import '../controllers/task_messages_controller.dart';
import '../services/api/chat_groups_api_service.dart';
import '../services/api/messages_api_service.dart';
import '../services/api/task_messages_api_service.dart';
import '../services/api/group_members_api_service.dart';
import '../services/unified_signalr_service.dart';

/// رابط نظام المحادثات
/// يضمن تسجيل جميع الخدمات والمتحكمات اللازمة لنظام المحادثات
class ChatBinding extends Bindings {
  @override
  void dependencies() {
    // التأكد من وجود متحكم المصادقة
    if (!Get.isRegistered<AuthController>()) {
      Get.put(AuthController(), permanent: true);
    }

    // تسجيل خدمات API للمحادثات
    Get.lazyPut<ChatGroupsApiService>(
      () => ChatGroupsApiService(),
      fenix: true,
    );

    Get.lazyPut<MessagesApiService>(
      () => MessagesApiService(),
      fenix: true,
    );

    Get.lazyPut<TaskMessagesApiService>(
      () => TaskMessagesApiService(),
      fenix: true,
    );

    Get.lazyPut<GroupMembersApiService>(
      () => GroupMembersApiService(),
      fenix: true,
    );

    // تسجيل خدمة SignalR الموحدة
    if (!Get.isRegistered<UnifiedSignalRService>()) {
      Get.put(UnifiedSignalRService(), permanent: true);
    }

    // تسجيل متحكمات المحادثات
    Get.lazyPut<ChatGroupsController>(
      () => ChatGroupsController(),
      fenix: true,
    );

    Get.lazyPut<GroupMembersController>(
      () => GroupMembersController(),
      fenix: true,
    );

    Get.lazyPut<MessagesController>(
      () => MessagesController(),
      fenix: true,
    );

    Get.lazyPut<UnifiedChatController>(
      () => UnifiedChatController(),
      fenix: true,
    );

    Get.lazyPut<TaskMessagesController>(
      () => TaskMessagesController(),
      fenix: true,
    );
  }
}
