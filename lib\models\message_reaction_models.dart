import 'user_model.dart';
import 'message_models.dart';

/// نموذج تفاعل الرسالة - متطابق مع ASP.NET Core API
class MessageReaction {
  final int id;
  final int messageId;
  final int userId;
  final String reaction; // الإيموجي أو نوع التفاعل
  final int createdAt; // long في API
  final bool isDeleted;
  final String? reactionType; // نوع التفاعل (like, love, angry, etc.)

  // Navigation properties
  final Message? message;
  final User? user;

  const MessageReaction({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.reaction,
    required this.createdAt,
    this.isDeleted = false,
    this.reactionType,
    this.message,
    this.user,
  });

  factory MessageReaction.fromJson(Map<String, dynamic> json) {
    return MessageReaction(
      id: json['id'] as int,
      messageId: json['messageId'] as int,
      userId: json['userId'] as int,
      reaction: json['reaction'] as String,
      createdAt: json['createdAt'] as int,
      isDeleted: json['isDeleted'] as bool? ?? false,
      reactionType: json['reactionType'] as String?,
      message: json['message'] != null
          ? Message.fromJson(json['message'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'reaction': reaction,
      'createdAt': createdAt,
      'isDeleted': isDeleted,
      'reactionType': reactionType,
    };
  }

  MessageReaction copyWith({
    int? id,
    int? messageId,
    int? userId,
    String? reaction,
    int? createdAt,
    bool? isDeleted,
    String? reactionType,
    Message? message,
    User? user,
  }) {
    return MessageReaction(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      userId: userId ?? this.userId,
      reaction: reaction ?? this.reaction,
      createdAt: createdAt ?? this.createdAt,
      isDeleted: isDeleted ?? this.isDeleted,
      reactionType: reactionType ?? this.reactionType,
      message: message ?? this.message,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'MessageReaction(id: $id, messageId: $messageId, userId: $userId, reaction: $reaction)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أنواع التفاعلات المتاحة
enum ReactionType {
  like('👍', 'like', 'إعجاب'),
  love('❤️', 'love', 'حب'),
  laugh('😂', 'laugh', 'ضحك'),
  wow('😮', 'wow', 'تعجب'),
  sad('😢', 'sad', 'حزن'),
  angry('😡', 'angry', 'غضب'),
  thumbsDown('👎', 'thumbs_down', 'عدم إعجاب'),
  fire('🔥', 'fire', 'رائع'),
  clap('👏', 'clap', 'تصفيق'),
  thinking('🤔', 'thinking', 'تفكير');

  const ReactionType(this.emoji, this.value, this.displayName);
  
  final String emoji;
  final String value;
  final String displayName;

  static ReactionType fromValue(String value) {
    return ReactionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ReactionType.like,
    );
  }

  static ReactionType fromEmoji(String emoji) {
    return ReactionType.values.firstWhere(
      (type) => type.emoji == emoji,
      orElse: () => ReactionType.like,
    );
  }
}

/// نموذج طلب إضافة تفاعل
class AddMessageReactionRequest {
  final int messageId;
  final String reaction;
  final String? reactionType;

  const AddMessageReactionRequest({
    required this.messageId,
    required this.reaction,
    this.reactionType,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'reaction': reaction,
      'reactionType': reactionType,
    };
  }
}

/// نموذج طلب حذف تفاعل
class RemoveMessageReactionRequest {
  final int messageId;
  final int userId;
  final String? reaction;

  const RemoveMessageReactionRequest({
    required this.messageId,
    required this.userId,
    this.reaction,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'userId': userId,
      'reaction': reaction,
    };
  }
}

/// إحصائيات تفاعلات الرسالة
class MessageReactionSummary {
  final int messageId;
  final Map<String, int> reactionCounts; // الإيموجي -> العدد
  final Map<String, List<User>> reactionUsers; // الإيموجي -> قائمة المستخدمين
  final int totalReactions;
  final bool hasUserReacted;
  final String? userReaction;

  const MessageReactionSummary({
    required this.messageId,
    required this.reactionCounts,
    required this.reactionUsers,
    required this.totalReactions,
    this.hasUserReacted = false,
    this.userReaction,
  });

  factory MessageReactionSummary.fromJson(Map<String, dynamic> json) {
    return MessageReactionSummary(
      messageId: json['messageId'] as int,
      reactionCounts: Map<String, int>.from(json['reactionCounts'] as Map),
      reactionUsers: (json['reactionUsers'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          key,
          (value as List).map((u) => User.fromJson(u as Map<String, dynamic>)).toList(),
        ),
      ),
      totalReactions: json['totalReactions'] as int,
      hasUserReacted: json['hasUserReacted'] as bool? ?? false,
      userReaction: json['userReaction'] as String?,
    );
  }

  /// الحصول على أكثر التفاعلات شيوعاً
  List<MapEntry<String, int>> get topReactions {
    var entries = reactionCounts.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  /// التحقق من وجود تفاعل معين
  bool hasReaction(String reaction) {
    return reactionCounts.containsKey(reaction) && reactionCounts[reaction]! > 0;
  }

  /// الحصول على عدد تفاعل معين
  int getReactionCount(String reaction) {
    return reactionCounts[reaction] ?? 0;
  }

  /// الحصول على المستخدمين الذين تفاعلوا بتفاعل معين
  List<User> getUsersForReaction(String reaction) {
    return reactionUsers[reaction] ?? [];
  }

  @override
  String toString() {
    return 'MessageReactionSummary(messageId: $messageId, totalReactions: $totalReactions, hasUserReacted: $hasUserReacted)';
  }
}

/// نموذج استجابة تفاعل الرسالة
class MessageReactionResponse {
  final bool success;
  final String? message;
  final MessageReaction? reaction;
  final MessageReactionSummary? summary;
  final String? error;

  const MessageReactionResponse({
    required this.success,
    this.message,
    this.reaction,
    this.summary,
    this.error,
  });

  factory MessageReactionResponse.fromJson(Map<String, dynamic> json) {
    return MessageReactionResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      reaction: json['reaction'] != null
          ? MessageReaction.fromJson(json['reaction'] as Map<String, dynamic>)
          : null,
      summary: json['summary'] != null
          ? MessageReactionSummary.fromJson(json['summary'] as Map<String, dynamic>)
          : null,
      error: json['error'] as String?,
    );
  }
}
