import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Privacy Policy',
              style: AppStyles.headingMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: AppStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              title: '1. Introduction',
              content: 'Welcome to Task Management System. We respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you use our application and tell you about your privacy rights and how the law protects you.',
            ),
            
            _buildSection(
              title: '2. The data we collect about you',
              content: 'Personal data, or personal information, means any information about an individual from which that person can be identified. We may collect, use, store and transfer different kinds of personal data about you which we have grouped together as follows:\n\n• Identity Data includes first name, last name, username or similar identifier.\n• Contact Data includes email address and telephone numbers.\n• Technical Data includes internet protocol (IP) address, your login data, browser type and version, time zone setting and location, browser plug-in types and versions, operating system and platform, and other technology on the devices you use to access this application.\n• Profile Data includes your username and password, your preferences, feedback and survey responses.\n• Usage Data includes information about how you use our application and services.',
            ),
            
            _buildSection(
              title: '3. How we use your personal data',
              content: 'We will only use your personal data when the law allows us to. Most commonly, we will use your personal data in the following circumstances:\n\n• Where we need to perform the contract we are about to enter into or have entered into with you.\n• Where it is necessary for our legitimate interests (or those of a third party) and your interests and fundamental rights do not override those interests.\n• Where we need to comply with a legal obligation.',
            ),
            
            _buildSection(
              title: '4. Data security',
              content: 'We have put in place appropriate security measures to prevent your personal data from being accidentally lost, used or accessed in an unauthorized way, altered or disclosed. In addition, we limit access to your personal data to those employees, agents, contractors and other third parties who have a business need to know. They will only process your personal data on our instructions and they are subject to a duty of confidentiality.',
            ),
            
            _buildSection(
              title: '5. Data retention',
              content: 'We will only retain your personal data for as long as reasonably necessary to fulfill the purposes we collected it for, including for the purposes of satisfying any legal, regulatory, tax, accounting or reporting requirements. We may retain your personal data for a longer period in the event of a complaint or if we reasonably believe there is a prospect of litigation in respect to our relationship with you.',
            ),
            
            _buildSection(
              title: '6. Your legal rights',
              content: 'Under certain circumstances, you have rights under data protection laws in relation to your personal data. These include the right to:\n\n• Request access to your personal data.\n• Request correction of your personal data.\n• Request erasure of your personal data.\n• Object to processing of your personal data.\n• Request restriction of processing your personal data.\n• Request transfer of your personal data.\n• Right to withdraw consent.',
            ),
            
            _buildSection(
              title: '7. Third-party links',
              content: 'This application may include links to third-party websites, plug-ins and applications. Clicking on those links or enabling those connections may allow third parties to collect or share data about you. We do not control these third-party websites and are not responsible for their privacy statements. When you leave our application, we encourage you to read the privacy policy of every website you visit.',
            ),
            
            _buildSection(
              title: '8. Cookies',
              content: 'Cookies are small files that a site or its service provider transfers to your computer\'s hard drive through your Web browser (if you allow) that enables the site\'s or service provider\'s systems to recognize your browser and capture and remember certain information. We use cookies to help us remember and process the items in your shopping cart, understand and save your preferences for future visits, keep track of advertisements and compile aggregate data about site traffic and site interaction so that we can offer better site experiences and tools in the future.',
            ),
            
            _buildSection(
              title: '9. Changes to the privacy policy',
              content: 'We may update our privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page. We will let you know via email and/or a prominent notice on our application, prior to the change becoming effective and update the "last updated" date at the top of this privacy policy.',
            ),
            
            _buildSection(
              title: '10. Contact us',
              content: 'If you have any questions about this privacy policy or our privacy practices, please contact us at:\n\nEmail: <EMAIL>\nPhone: +****************\nAddress: 123 Privacy Street, City, Country',
            ),
            
            const SizedBox(height: 32),
            
            // Data protection officer
            Text(
              'Data Protection Officer',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            const Text(
              'We have appointed a data protection officer (DPO) who is responsible for overseeing questions in relation to this privacy policy. If you have any questions about this privacy policy, including any requests to exercise your legal rights, please contact the DPO using the details set out below:',
            ),
            const SizedBox(height: 8),
            const Text('Name: John Doe'),
            const Text('Email: <EMAIL>'),
            const Text('Phone: +****************'),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: AppStyles.bodyMedium,
          ),
        ],
      ),
    );
  }
}
