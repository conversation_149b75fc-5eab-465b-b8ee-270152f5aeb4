import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../models/unified_models.dart';
import '../models/time_tracking_models.dart' as time_models;
import '../services/api/task_api_service.dart';
import '../services/api/task_comments_api_service.dart';
import '../../services/api/task_history_api_service.dart';
import '../../services/api/time_tracking_api_service.dart';
import '../../services/api/subtasks_api_service.dart';
import '../../services/api/attachments_api_service.dart';
import '../../services/api/user_api_service.dart';
import '../../services/api/departments_api_service.dart';
import '../../services/api/task_access_api_service.dart';
import '../../services/api/task_documents_api_service.dart';
import 'report_filter_dialog.dart';
import '../../services/api/task_progress_trackers_api_service.dart';
import '../../screens/widgets/unified_pdf_viewer.dart';

/// خدمة إنشاء التقرير الشامل والاحترافي للمهام
/// نسخة مبسطة وفعالة
class SimpleComprehensiveTaskReportService {
  static final SimpleComprehensiveTaskReportService _instance = SimpleComprehensiveTaskReportService._internal();
  factory SimpleComprehensiveTaskReportService() => _instance;
  SimpleComprehensiveTaskReportService._internal();

  // الخدمات المطلوبة
  final TaskApiService _taskApiService = TaskApiService();
  final TaskCommentsApiService _commentsApiService = TaskCommentsApiService();
  final TaskHistoryApiService _historyApiService = TaskHistoryApiService();
  final TimeTrackingApiService _timeTrackingApiService = TimeTrackingApiService();
  final SubtasksApiService _subtasksApiService = SubtasksApiService();
  final AttachmentsApiService _attachmentsApiService = AttachmentsApiService();
  final UserApiService _userApiService = UserApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();
  final TaskAccessApiService _taskAccessApiService = TaskAccessApiService();
  final TaskProgressTrackersApiService _progressTrackersApiService = TaskProgressTrackersApiService();
  final TaskDocumentsApiService _taskDocumentsApiService = TaskDocumentsApiService();

  // الخطوط العربية
  pw.Font? _arabicFont;
  pw.Font? _arabicBoldFont;

  // متغير مؤقت لحفظ بيانات التقرير للاستخدام في دوال الحساب
  Map<String, dynamic>? _tempReportData;

  /// إنشاء وعرض التقرير الشامل مع فلتر
  static Future<void> generateAndShowReportWithFilter(BuildContext context, int taskId) async {
    try {
      final service = SimpleComprehensiveTaskReportService();

      // جمع البيانات أولاً
      final reportData = await service._collectReportData(taskId);

      // عرض حوار الفلتر
      final filterOptions = await showDialog<ReportFilterOptions>(
        context: context,
        builder: (context) => ReportFilterDialog(
          initialOptions: ReportFilterOptions(),
          reportData: reportData,
        ),
      );

      // إذا ألغى المستخدم أو لم يختر أي قسم
      if (filterOptions == null || !filterOptions.hasAnySelection) {
        return;
      }

      // إنشاء التقرير مع الفلتر
      final pdfBytes = await service._generateFilteredReport(reportData, filterOptions);

      // عرض التقرير
      Get.to(() => UnifiedPdfViewer(
        fileName: 'التقرير_الشامل_للمهمة_$taskId.pdf',
        pdfData: pdfBytes,
        title: 'التقرير الشامل للمهمة',
      ));

    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء التقرير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// إنشاء التقرير الشامل للمهمة (الطريقة الأصلية)
  Future<void> generateComprehensiveReport(int taskId) async {
    try {
      debugPrint('🚀 بدء إنشاء التقرير الشامل للمهمة $taskId...');

      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري إنشاء التقرير الشامل...',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'يرجى الانتظار، هذا قد يستغرق بضع ثوانٍ',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // تحميل الخطوط العربية
      await _loadArabicFonts();

      // جمع جميع البيانات المطلوبة
      final reportData = await _collectReportData(taskId);

      // إنشاء مستند PDF
      final pdfDocument = await _createPdfDocument(reportData);

      // حفظ البيانات
      final pdfBytes = await pdfDocument.save();

      // إغلاق مؤشر التحميل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // فتح التقرير في العارض الموحد
      Get.to(() => UnifiedPdfViewer(
        pdfData: pdfBytes,
        fileName: 'التقرير_الشامل_للمهمة_$taskId.pdf',
        title: 'التقرير الشامل - مهمة $taskId',
        showToolbar: true,
        showPrintButton: true,
        showShareButton: true,
        showSaveButton: true,
      ));

      debugPrint('✅ تم إنشاء التقرير الشامل بنجاح');

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجح',
        'تم إنشاء التقرير الشامل بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
        duration: const Duration(seconds: 3),
      );

    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في إنشاء التقرير الشامل: $e');
      debugPrint('Stack trace: $stackTrace');

      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء التقرير الشامل: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// تحميل الخطوط العربية
  Future<void> _loadArabicFonts() async {
    try {
      debugPrint('🔤 تحميل الخطوط العربية...');

      // محاولة تحميل خط Cairo
      try {
        final regularFontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');

        _arabicFont = pw.Font.ttf(regularFontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);

        debugPrint('✅ تم تحميل خطوط Cairo بنجاح');
        return;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على خطوط Cairo، محاولة تحميل NotoSansArabic...');
      }

      // محاولة تحميل خط NotoSansArabic كبديل
      try {
        final regularFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');

        _arabicFont = pw.Font.ttf(regularFontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);

        debugPrint('✅ تم تحميل خطوط NotoSansArabic بنجاح');
        return;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على خطوط NotoSansArabic، محاولة تحميل Amiri...');
      }

      // محاولة تحميل خط Amiri كبديل ثالث
      try {
        final regularFontData = await rootBundle.load('assets/fonts/Amiri-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/Amiri-Bold.ttf');

        _arabicFont = pw.Font.ttf(regularFontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);

        debugPrint('✅ تم تحميل خطوط Amiri بنجاح');
        return;
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على خطوط Amiri، استخدام الخطوط الافتراضية');
      }

      // استخدام الخطوط الافتراضية
      _arabicFont = pw.Font.courier();
      _arabicBoldFont = pw.Font.courierBold();

      debugPrint('⚠️ تم استخدام الخطوط الافتراضية');

    } catch (e) {
      debugPrint('❌ خطأ في تحميل الخطوط: $e');
      // استخدام خطوط احتياطية
      _arabicFont = pw.Font.courier();
      _arabicBoldFont = pw.Font.courierBold();
    }
  }

  /// جمع جميع البيانات المطلوبة للتقرير
  Future<Map<String, dynamic>> _collectReportData(int taskId) async {
    debugPrint('📊 جمع بيانات التقرير...');

    final data = <String, dynamic>{};

    try {
   //   // البيانات الأساسية للمهمة//
      final task = await _taskApiService.getTaskById(taskId);
      data['task'] = task;

      // التعليقات
      try {
        data['comments'] = await _commentsApiService.getCommentsByTask(taskId);
        debugPrint('✅ تم جمع ${data['comments'].length} تعليق');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع التعليقات: $e');
        data['comments'] = <TaskComment>[];
      }

      // تاريخ المهمة
      try {
        data['history'] = await _historyApiService.getHistoryByTask(taskId);
        debugPrint('✅ تم جمع ${data['history'].length} حدث تاريخي');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع التاريخ: $e');
        data['history'] = <TaskHistory>[];
      }

      // تتبع الوقت
      try {
        data['timeTracking'] = await _timeTrackingApiService.getTimeEntriesByTask(taskId);
        debugPrint('✅ تم جمع بيانات تتبع الوقت');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع تتبع الوقت: $e');
        data['timeTracking'] = <time_models.TimeTrackingEntry>[];
      }

      // المهام الفرعية
      try {
        data['subtasks'] = await _subtasksApiService.getSubtasksByTask(taskId);
        debugPrint('✅ تم جمع ${data['subtasks'].length} مهمة فرعية');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع المهام الفرعية: $e');
        data['subtasks'] = <Subtask>[];
      }

      // المرفقات
      try {
        data['attachments'] = await _attachmentsApiService.getAttachmentsByTask(taskId);
        debugPrint('✅ تم جمع ${data['attachments'].length} مرفق');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع المرفقات: $e');
        data['attachments'] = <Attachment>[];
      }

      // بيانات المستخدمين
      try {
        final allUsers = await _userApiService.getAllUsers();
        data['users'] = {for (var user in allUsers) user.id: user};
        debugPrint('✅ تم جمع بيانات ${allUsers.length} مستخدم');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع بيانات المستخدمين: $e');
        data['users'] = <int, User>{};
      }

      // بيانات الأقسام
      try {
        final allDepartments = await _departmentsApiService.getAllDepartments();
        data['departments'] = {for (var dept in allDepartments) dept.id: dept};
        debugPrint('✅ تم جمع بيانات ${allDepartments.length} قسم');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع بيانات الأقسام: $e');
        data['departments'] = <int, Department>{};
      }

      // متتبعات التقدم (يجب جمعها قبل المساهمين)
      try {
        data['progressTrackers'] = await _progressTrackersApiService.getTrackersByTask(taskId);
        debugPrint('✅ تم جمع ${data['progressTrackers'].length} متتبع تقدم');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع متتبعات التقدم: $e');
        data['progressTrackers'] = <TaskProgressTracker>[];
      }

      // المستندات المرتبطة بالمهمة (يجب جمعها قبل المساهمين)
      try {
        data['taskDocuments'] = await _taskDocumentsApiService.getTaskDocuments(taskId);
        debugPrint('✅ تم جمع ${data['taskDocuments'].length} مستند مرتبط بالمهمة');
      } catch (e) {
        debugPrint('⚠️ خطأ في جمع المستندات: $e');
        data['taskDocuments'] = <TaskDocument>[];
      }

      // المساهمون (آخراً لأنهم يحتاجون البيانات السابقة)
      try {
        debugPrint('🔍 جمع المساهمين للمهمة $taskId...');
        final basicContributors = await _taskAccessApiService.getTaskContributors(taskId);
        debugPrint('🔍 تم جمع ${basicContributors.length} مساهم أساسي');
        debugPrint('🔍 نوع البيانات المرجعة: ${basicContributors.runtimeType}');

        if (basicContributors.isNotEmpty) {
          debugPrint('🔍 عينة من أول مساهم:');
          debugPrint('   - نوع البيانات: ${basicContributors.first.runtimeType}');
          debugPrint('   - المحتوى: ${basicContributors.first}');
        }

        // تحويل المساهمين الأساسيين إلى مساهمين للتقرير
        data['contributors'] = _convertBasicContributorsToReportContributors(basicContributors, data);
        debugPrint('✅ تم تحويل ${data['contributors'].length} مساهم للتقرير');

        // إضافة معرفات المستخدمين للتوافق (من المساهمين المحولين)
        final convertedContributors = data['contributors'] as List<TaskContributor>;
        final userIds = convertedContributors.map((c) => c.userId).toList();
        data['accessUsers'] = userIds;

      } catch (e) {
        debugPrint('⚠️ خطأ في جمع المساهمين: $e');
        data['contributors'] = <TaskContributor>[];
        data['accessUsers'] = <int>[];
      }

      debugPrint('✅ تم جمع جميع البيانات بنجاح');
      return data;

    } catch (e) {
      debugPrint('❌ خطأ في جمع البيانات: $e');
      rethrow;
    }
  }

  /// إنشاء مستند PDF
  Future<pw.Document> _createPdfDocument(Map<String, dynamic> data) async {
    final pdf = pw.Document(
      title: 'التقرير الشامل للمهمة: ${data['task'].title}',
      author: 'نظام إدارة المهام',
      subject: 'تقرير مفصل شامل',
      creator: 'SimpleComprehensiveTaskReportService',
      producer: 'Flutter PDF Library',
      keywords: 'مهمة, تقرير, شامل, إدارة',
    );

    final theme = pw.ThemeData.withFont(
      base: _arabicFont!,
      bold: _arabicBoldFont!,
    );

    // صفحة الغلاف
    pdf.addPage(_buildCoverPage(data, theme));

    // صفحة تفاصيل المهمة
    pdf.addPage(_buildTaskDetailsPage(data, theme));

    // صفحات المساهمين (ديناميكية)
    final contributors = data['contributors'] as List;
    debugPrint('🔍 فحص المساهمين للتقرير:');
    debugPrint('   - عدد المساهمين: ${contributors.length}');
    debugPrint('   - نوع البيانات: ${contributors.runtimeType}');
    if (contributors.isNotEmpty) {
      debugPrint('   - أول مساهم: ${contributors.first}');
      final contributorsPages = _buildContributorsPages(data, theme);
      for (final page in contributorsPages) {
        pdf.addPage(page);
      }
      debugPrint('✅ تم إضافة ${contributorsPages.length} صفحة للمساهمين');
    } else {
      debugPrint('⚠️ لا توجد مساهمين لإضافتهم للتقرير');
    }

    // صفحات التقدم والإحصائيات (ديناميكية)
    if ((data['progressTrackers'] as List).isNotEmpty) {
      final progressPages = _buildProgressPages(data, theme);
      for (final page in progressPages) {
        pdf.addPage(page);
      }
    }

    // صفحات التعليقات (ديناميكية)
    if ((data['comments'] as List).isNotEmpty) {
      final commentsPages = _buildCommentsPages(data, theme);
      for (final page in commentsPages) {
        pdf.addPage(page);
      }
    }

    // صفحات المرفقات (ديناميكية)
    if ((data['attachments'] as List).isNotEmpty) {
      final attachmentsPages = _buildAttachmentsPages(data, theme);
      for (final page in attachmentsPages) {
        pdf.addPage(page);
      }
    }

    // صفحات المهام الفرعية (ديناميكية)
    if ((data['subtasks'] as List).isNotEmpty) {
      final subtasksPages = _buildSubtasksPages(data, theme);
      for (final page in subtasksPages) {
        pdf.addPage(page);
      }
    }

    // صفحات تتبع الوقت (ديناميكية)
    if ((data['timeTracking'] as List).isNotEmpty) {
      final timeTrackingPages = _buildTimeTrackingPages(data, theme);
      for (final page in timeTrackingPages) {
        pdf.addPage(page);
      }
    }

    // صفحات المستندات (ديناميكية)
    if ((data['taskDocuments'] as List).isNotEmpty) {
      final documentsPages = _buildDocumentsPages(data, theme);
      for (final page in documentsPages) {
        pdf.addPage(page);
      }
    }

    // صفحات سجل الأحداث والتحويلات (ديناميكية)
    if ((data['history'] as List).isNotEmpty) {
      final historyPages = _buildHistoryPages(data, theme);
      for (final page in historyPages) {
        pdf.addPage(page);
      }
    }

    return pdf;
  }

  /// بناء تذييل الصفحة مع رقم الصفحة
  pw.Widget _buildPageFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: pw.EdgeInsets.only(top: 20),
      child: pw.Text(
        'صفحة ${context.pageNumber} من ${context.pagesCount}',
        style: pw.TextStyle(
          fontSize: 10,
          color: PdfColors.grey600,
        ),
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// صفحة الغلاف
  pw.Page _buildCoverPage(Map<String, dynamic> data, pw.ThemeData theme) {
    final task = data['task'] as Task;

    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Container(
        decoration: pw.BoxDecoration(
          gradient: pw.LinearGradient(
            begin: pw.Alignment.topRight,
            end: pw.Alignment.bottomLeft,
            colors: [
              PdfColors.blue900,
              PdfColors.blue700,
              PdfColors.blue500,
            ],
          ),
        ),
        child: pw.Column(
          children: [
            // الرأس العلوي
            pw.Container(
              height: 150,
              child: pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'التقرير الشامل للمهمة',
                      style: pw.TextStyle(
                        fontSize: 32,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Container(
                      width: 200,
                      height: 3,
                      color: PdfColors.orange,
                    ),
                  ],
                ),
              ),
            ),

            // المحتوى الرئيسي
            pw.Expanded(
              child: pw.Container(
                margin: pw.EdgeInsets.all(40),
                padding: pw.EdgeInsets.all(30),
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  borderRadius: pw.BorderRadius.circular(15),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    // عنوان المهمة
                    pw.Container(
                      width: double.infinity,
                      padding: pw.EdgeInsets.all(20),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.grey100,
                        borderRadius: pw.BorderRadius.circular(10),
                        border: pw.Border.all(color: PdfColors.blue200),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text(
                            'عنوان المهمة',
                            style: pw.TextStyle(
                              fontSize: 14,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.grey600,
                            ),
                          ),
                          pw.SizedBox(height: 8),
                          pw.Text(
                            task.title,
                            style: pw.TextStyle(
                              fontSize: 24,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.blue900,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    pw.SizedBox(height: 30),

                    // معلومات أساسية
                    _buildInfoRow('رقم المهمة:', '${task.id}'),
                    _buildInfoRow('الحالة:', _translateTaskStatus(task.status)),
                    _buildInfoRow('الأولوية:', _translateTaskPriority(task.priority)),
                    _buildInfoRow('نسبة الإنجاز:', '${task.completionPercentage}%'),

                    pw.SizedBox(height: 40),

                    // معلومات التقرير
                    pw.Container(
                      width: double.infinity,
                      padding: pw.EdgeInsets.all(15),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue50,
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text(
                            'معلومات التقرير',
                            style: pw.TextStyle(
                              fontSize: 16,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.blue900,
                            ),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            'تاريخ الإنشاء: ${_formatDateTime(DateTime.now())}',
                            style: pw.TextStyle(
                              fontSize: 12,
                              color: PdfColors.grey700,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                          pw.SizedBox(height: 5),
                          pw.Text(
                            'نوع التقرير: تقرير شامل ومفصل',
                            style: pw.TextStyle(
                              fontSize: 12,
                              color: PdfColors.grey700,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                          pw.SizedBox(height: 5),
                          pw.Text(
                            'مصدر البيانات: قاعدة البيانات المباشرة',
                            style: pw.TextStyle(
                              fontSize: 12,
                              color: PdfColors.grey700,
                            ),
                            textAlign: pw.TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات (مع تبديل مواضع العنوان والقيمة)
  pw.Widget _buildInfoRow(String label, String value) {
    return pw.Padding(
      padding: pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue900,
              ),
            ),
          ),
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: pw.TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// صفحة تفاصيل المهمة
  pw.Page _buildTaskDetailsPage(Map<String, dynamic> data, pw.ThemeData theme) {
    final task = data['task'] as Task;
    final users = data['users'] as Map<int, User>;
    final departments = data['departments'] as Map<int, Department>;

    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          // رأس الصفحة
          _buildSectionHeaderCentered('تفاصيل المهمة', PdfColors.blue900),

          pw.SizedBox(height: 20),

          // معلومات المهمة الأساسية
          _buildSection(
            'المعلومات الأساسية',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildDetailRow('رقم المهمة:', '${task.id}'),
                _buildDetailRow('العنوان:', task.title),
                _buildDetailRow('الوصف:', task.description ?? 'لا يوجد وصف'),
                _buildDetailRow('الحالة:', _translateTaskStatus(task.status)),
                _buildDetailRow('الأولوية:', _translateTaskPriority(task.priority)),
                _buildDetailRow('نسبة الإنجاز:', '${task.completionPercentage}%'),
              ],
            ),
          ),

          pw.SizedBox(height: 15),

          // معلومات المستخدمين
          _buildSection(
            'معلومات المستخدمين',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                if (task.creatorId != null)
                  _buildDetailRow('منشئ المهمة:', users[task.creatorId]?.name ?? 'غير معروف'),
                if (task.assigneeId != null)
                  _buildDetailRow('المكلف بالمهمة:', users[task.assigneeId]?.name ?? 'غير معروف'),
                if (task.departmentId != null)
                  _buildDetailRow('القسم:', departments[task.departmentId]?.name ?? 'غير معروف'),
              ],
            ),
          ),

          pw.SizedBox(height: 15),

          // معلومات التوقيتات
          _buildSection(
            'التوقيتات',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildDetailRow('تاريخ الإنشاء:', _formatTimestamp(task.createdAt)),
                if (task.startDate != null)
                  _buildDetailRow('تاريخ البداية:', _formatTimestamp(task.startDate!)),
                if (task.dueDate != null)
                  _buildDetailRow('تاريخ الاستحقاق:', _formatTimestamp(task.dueDate!)),
                if (task.completedAt != null)
                  _buildDetailRow('تاريخ الإنجاز:', _formatTimestamp(task.completedAt!)),
              ],
            ),
          ),

          pw.SizedBox(height: 15),

          // معلومات إضافية
          if (task.note != null || task.incoming != null)
            _buildSection(
              'معلومات إضافية',
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  if (task.note != null && task.note!.isNotEmpty)
                    _buildDetailRow('الملاحظات:', task.note!),
                  if (task.incoming != null && task.incoming!.isNotEmpty)
                    _buildDetailRow('مصدر المهمة:', task.incoming!),
                ],
              ),
            ),

          pw.SizedBox(height: 15),

          // إحصائيات سريعة
          _buildSection(
            'إحصائيات سريعة',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildDetailRow('عدد التعليقات:', '${(data['comments'] as List).length}'),
                _buildDetailRow('عدد المرفقات:', '${(data['attachments'] as List).length}'),
                _buildDetailRow('عدد المهام الفرعية:', '${(data['subtasks'] as List).length}'),
                _buildDetailRow('عدد المساهمين:', '${(data['contributors'] as List).length}'),
                _buildDetailRow('عدد تحديثات التقدم:', '${(data['progressTrackers'] as List).length}'),
                _buildDetailRow('عدد سجلات الوقت:', '${(data['timeTracking'] as List).length}'),
                _buildDetailRow('عدد المستندات:', '${(data['taskDocuments'] as List).length}'),
                _buildDetailRow('عدد الأحداث:', '${(data['history'] as List).length}'),
              ],
            ),
          ),

          // تذييل الصفحة مع رقم الصفحة
          pw.Spacer(),
          _buildPageFooter(context),
        ],
      ),
    );
  }

  /// بناء رأس القسم
  pw.Widget _buildSectionHeader(String title, PdfColor color) {
    return pw.Container(
      width: double.infinity,
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: color,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          fontSize: 18,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.white,
        ),
      ),
    );
  }

  /// بناء رأس القسم في المنتصف
  pw.Widget _buildSectionHeaderCentered(String title, PdfColor color) {
    return pw.Container(
      width: double.infinity,
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: color,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          fontSize: 18,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء قسم (مع عنوان محاذي لليسار)
  pw.Widget _buildSection(String title, pw.Widget content) {
    return pw.Container(
      width: double.infinity,
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 10),
          content,
        ],
      ),
    );
  }

  /// بناء صف تفاصيل (مع تبديل مواضع العنوان والقيمة)
  pw.Widget _buildDetailRow(String label, String value) {
    return pw.Padding(
      padding: pw.EdgeInsets.only(bottom: 5),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            flex: 3,
            child: pw.Text(
              value,
              style: pw.TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق الطابع الزمني
  String _formatTimestamp(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return _formatDateTime(dateTime);
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes بايت';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
  }

  /// صفحات التعليقات الديناميكية
  List<pw.Page> _buildCommentsPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final comments = data['comments'] as List<TaskComment>;
    final users = data['users'] as Map<int, User>;
    final pages = <pw.Page>[];

    if (comments.isEmpty) return pages;

    const int commentsPerPage = 12; // عدد التعليقات في كل صفحة (زيادة من 8 إلى 12)
    final int totalPages = (comments.length / commentsPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * commentsPerPage;
      final endIndex = (startIndex + commentsPerPage).clamp(0, comments.length);
      final pageComments = comments.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'التعليقات والملاحظات'
                  : 'التعليقات والملاحظات (تابع)',
                PdfColors.green600
              ),

              pw.SizedBox(height: 20),

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    if (pageIndex == 0)
                      pw.Text(
                        'إجمالي التعليقات: ${comments.length}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green700,
                        ),
                      ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // التعليقات
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageComments.length,
                  itemBuilder: (context, index) {
                    final comment = pageComments[index];
                    final user = users[comment.userId];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 8), // تقليص من 12 إلى 8
                      padding: pw.EdgeInsets.all(8), // تقليص من 12 إلى 8
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.green300),
                        borderRadius: pw.BorderRadius.circular(6), // تقليص من 8 إلى 6
                        color: PdfColors.green50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس التعليق
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1), // تقليص
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.green600,
                                  borderRadius: pw.BorderRadius.circular(7), // تقليص من 10 إلى 7
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 6, // تقليص من 8 إلى 6
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 7), // تقليص من 10 إلى 7
                              pw.Text(
                                user?.name ?? 'مستخدم غير معروف',
                                style: pw.TextStyle(
                                  fontSize: 8, // تقليص من 11 إلى 8
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.green700,
                                ),
                              ),
                              pw.Spacer(),
                              pw.Text(
                                _formatTimestamp(comment.createdAt),
                                style: pw.TextStyle(
                                  fontSize: 7, // تقليص من 9 إلى 7
                                  color: PdfColors.grey600,
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 5), // تقليص من 8 إلى 5

                          // محتوى التعليق
                          pw.Text(
                            comment.content,
                            style: pw.TextStyle(
                              fontSize: 7, // تقليص من 10 إلى 7
                              color: PdfColors.grey800,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // تذييل الصفحة مع رقم الصفحة
              pw.Spacer(),
              _buildPageFooter(context),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// صفحات المرفقات الديناميكية
  List<pw.Page> _buildAttachmentsPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final attachments = data['attachments'] as List<Attachment>;
    final users = data['users'] as Map<int, User>;
    final pages = <pw.Page>[];

    if (attachments.isEmpty) return pages;

    const int attachmentsPerPage = 6; // عدد المرفقات في كل صفحة
    final int totalPages = (attachments.length / attachmentsPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * attachmentsPerPage;
      final endIndex = (startIndex + attachmentsPerPage).clamp(0, attachments.length);
      final pageAttachments = attachments.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'المرفقات والوثائق'
                  : 'المرفقات والوثائق (تابع)',
                PdfColors.orange600
              ),

              pw.SizedBox(height: 20),

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.orange50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    if (pageIndex == 0)
                      pw.Text(
                        'إجمالي المرفقات: ${attachments.length}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.orange700,
                        ),
                      ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // المرفقات
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageAttachments.length,
                  itemBuilder: (context, index) {
                    final attachment = pageAttachments[index];
                    final uploader = users[attachment.uploadedBy];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 12),
                      padding: pw.EdgeInsets.all(12),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.orange300),
                        borderRadius: pw.BorderRadius.circular(8),
                        color: PdfColors.orange50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس المرفق
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.orange600,
                                  borderRadius: pw.BorderRadius.circular(10),
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 8,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 10),
                              pw.Expanded(
                                child: pw.Text(
                                  attachment.fileName,
                                  style: pw.TextStyle(
                                    fontSize: 12,
                                    fontWeight: pw.FontWeight.bold,
                                    color: PdfColors.orange700,
                                  ),
                                ),
                              ),
                              pw.Text(
                                _formatFileSize(attachment.fileSize),
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey600,
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 8),

                          // معلومات المرفق
                          pw.Row(
                            children: [
                              pw.Text(
                                'نوع الملف: ${attachment.fileType}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey700,
                                ),
                              ),
                              pw.Spacer(),
                              pw.Text(
                                'رفع بواسطة: ${uploader?.name ?? 'مستخدم غير معروف'}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.blue700,
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 5),

                          pw.Text(
                            'تاريخ الرفع: ${_formatTimestamp(attachment.uploadedAt)}',
                            style: pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.grey600,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // تذييل الصفحة مع رقم الصفحة
              pw.Spacer(),
              _buildPageFooter(context),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// صفحات المهام الفرعية الديناميكية
  List<pw.Page> _buildSubtasksPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final subtasks = data['subtasks'] as List<Subtask>;
    final pages = <pw.Page>[];

    if (subtasks.isEmpty) return pages;

    const int subtasksPerPage = 7; // عدد المهام الفرعية في كل صفحة
    final int totalPages = (subtasks.length / subtasksPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * subtasksPerPage;
      final endIndex = (startIndex + subtasksPerPage).clamp(0, subtasks.length);
      final pageSubtasks = subtasks.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'المهام الفرعية'
                  : 'المهام الفرعية (تابع)',
                PdfColors.purple600
              ),

              pw.SizedBox(height: 20),

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.purple50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    if (pageIndex == 0)
                      pw.Text(
                        'إجمالي المهام الفرعية: ${subtasks.length}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.purple700,
                        ),
                      ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // المهام الفرعية
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageSubtasks.length,
                  itemBuilder: (context, index) {
                    final subtask = pageSubtasks[index];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 12),
                      padding: pw.EdgeInsets.all(12),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.purple300),
                        borderRadius: pw.BorderRadius.circular(8),
                        color: subtask.isCompleted ? PdfColors.green50 : PdfColors.purple50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس المهمة الفرعية
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.purple600,
                                  borderRadius: pw.BorderRadius.circular(10),
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 8,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 10),
                              pw.Expanded(
                                child: pw.Text(
                                  subtask.title,
                                  style: pw.TextStyle(
                                    fontSize: 12,
                                    fontWeight: pw.FontWeight.bold,
                                    color: PdfColors.purple700,
                                  ),
                                ),
                              ),
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: pw.BoxDecoration(
                                  color: subtask.isCompleted ? PdfColors.green600 : PdfColors.orange600,
                                  borderRadius: pw.BorderRadius.circular(12),
                                ),
                                child: pw.Text(
                                  subtask.isCompleted ? 'مكتملة' : 'قيد التنفيذ',
                                  style: pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 8),

                          // معلومات المهمة الفرعية
                          pw.Row(
                            children: [
                              pw.Text(
                                'تاريخ الإنشاء: ${_formatTimestamp(subtask.createdAt)}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey600,
                                ),
                              ),
                              pw.Spacer(),
                              if (subtask.completedAt != null)
                                pw.Text(
                                  'تاريخ الإنجاز: ${_formatTimestamp(subtask.completedAt!)}',
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    color: PdfColors.green600,
                                  ),
                                ),
                            ],
                          ),

                          // معلومات إضافية
                          pw.SizedBox(height: 5),
                          pw.Text(
                            'معرف المهمة الفرعية: ${subtask.id}',
                            style: pw.TextStyle(
                              fontSize: 9,
                              color: PdfColors.grey700,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // تذييل الصفحة مع رقم الصفحة
              pw.Spacer(),
              _buildPageFooter(context),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// صفحات تتبع الوقت الديناميكية
  List<pw.Page> _buildTimeTrackingPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final timeTracking = data['timeTracking'] as List<time_models.TimeTrackingEntry>;
    final users = data['users'] as Map<int, User>;
    final pages = <pw.Page>[];

    if (timeTracking.isEmpty) return pages;

    // حساب إجمالي الوقت
    final totalMinutes = timeTracking.fold<int>(0, (sum, entry) => sum + (entry.duration ?? 0));
    final totalHours = totalMinutes / 60;

    const int entriesPerPage = 10; // عدد سجلات الوقت في كل صفحة
    final int totalPages = (timeTracking.length / entriesPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * entriesPerPage;
      final endIndex = (startIndex + entriesPerPage).clamp(0, timeTracking.length);
      final pageEntries = timeTracking.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'تتبع الوقت'
                  : 'تتبع الوقت (تابع)',
                PdfColors.teal600
              ),

              pw.SizedBox(height: 20),

              // إحصائيات الوقت (في الصفحة الأولى فقط)
              if (pageIndex == 0) ...[
                _buildSection(
                  'إحصائيات الوقت',
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      _buildDetailRow('إجمالي سجلات الوقت:', '${timeTracking.length}'),
                      _buildDetailRow('إجمالي الوقت المسجل:', '${totalHours.toStringAsFixed(1)} ساعة'),
                      _buildDetailRow('متوسط الوقت لكل جلسة:', timeTracking.isNotEmpty ? '${(totalMinutes / timeTracking.length).toStringAsFixed(1)} دقيقة' : '0 دقيقة'),
                    ],
                  ),
                ),
                pw.SizedBox(height: 15),
              ],

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.teal50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    pw.Text(
                      'سجلات الوقت التفصيلية',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.teal700,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // سجلات الوقت
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageEntries.length,
                  itemBuilder: (context, index) {
                    final entry = pageEntries[index];
                    final user = users[entry.userId];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 10),
                      padding: pw.EdgeInsets.all(12),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.teal300),
                        borderRadius: pw.BorderRadius.circular(8),
                        color: PdfColors.teal50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس السجل
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.teal600,
                                  borderRadius: pw.BorderRadius.circular(10),
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 8,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 10),
                              pw.Text(
                                user?.name ?? 'مستخدم غير معروف',
                                style: pw.TextStyle(
                                  fontSize: 12,
                                  fontWeight: pw.FontWeight.bold,
                                  color: PdfColors.blue900,
                                ),
                              ),
                              pw.Spacer(),
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.teal600,
                                  borderRadius: pw.BorderRadius.circular(12),
                                ),
                                child: pw.Text(
                                  '${entry.duration ?? 0} دقيقة',
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 8),

                          // أوقات البداية والنهاية
                          pw.Row(
                            children: [
                              pw.Text(
                                'بدأ: ${_formatTimestamp(entry.startTime)}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey600,
                                ),
                              ),
                              pw.Spacer(),
                              if (entry.endTime != null)
                                pw.Text(
                                  'انتهى: ${_formatTimestamp(entry.endTime!)}',
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    color: PdfColors.grey600,
                                  ),
                                ),
                            ],
                          ),

                          // الوصف إذا وجد
                          if (entry.description != null && entry.description!.isNotEmpty) ...[
                            pw.SizedBox(height: 5),
                            pw.Text(
                              'الوصف: ${entry.description}',
                              style: pw.TextStyle(
                                fontSize: 10,
                                color: PdfColors.grey700,
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),

              // تذييل الصفحة مع رقم الصفحة
              pw.Spacer(),
              _buildPageFooter(context),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// صفحات المستندات الديناميكية
  List<pw.Page> _buildDocumentsPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final taskDocuments = data['taskDocuments'] as List<TaskDocument>;
    final users = data['users'] as Map<int, User>;
    final pages = <pw.Page>[];

    if (taskDocuments.isEmpty) return pages;

    const int documentsPerPage = 6; // عدد المستندات في كل صفحة
    final int totalPages = (taskDocuments.length / documentsPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * documentsPerPage;
      final endIndex = (startIndex + documentsPerPage).clamp(0, taskDocuments.length);
      final pageDocuments = taskDocuments.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'المستندات المرتبطة'
                  : 'المستندات المرتبطة (تابع)',
                PdfColors.indigo600
              ),

              pw.SizedBox(height: 20),

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.indigo50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    if (pageIndex == 0)
                      pw.Text(
                        'إجمالي المستندات: ${taskDocuments.length}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.indigo700,
                        ),
                      ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // المستندات
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageDocuments.length,
                  itemBuilder: (context, index) {
                    final document = pageDocuments[index];
                    final creator = users[document.createdBy];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 12),
                      padding: pw.EdgeInsets.all(12),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.indigo300),
                        borderRadius: pw.BorderRadius.circular(8),
                        color: PdfColors.indigo50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس المستند
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.indigo600,
                                  borderRadius: pw.BorderRadius.circular(10),
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 8,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 10),
                              pw.Expanded(
                                child: pw.Text(
                                  document.archiveDocument?.title ?? 'مستند بدون عنوان',
                                  style: pw.TextStyle(
                                    fontSize: 12,
                                    fontWeight: pw.FontWeight.bold,
                                    color: PdfColors.indigo700,
                                  ),
                                ),
                              ),
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: pw.BoxDecoration(
                                  color: _getDocumentTypeColor(document.type),
                                  borderRadius: pw.BorderRadius.circular(12),
                                ),
                                child: pw.Text(
                                  _translateDocumentType(document.type),
                                  style: pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 8),

                          // معلومات المستند
                          if (document.description != null && document.description!.isNotEmpty)
                            pw.Padding(
                              padding: pw.EdgeInsets.only(bottom: 5),
                              child: pw.Text(
                                'الوصف: ${document.description}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.grey700,
                                ),
                              ),
                            ),

                          pw.Row(
                            children: [
                              pw.Text(
                                'منشئ المستند: ${creator?.name ?? 'مستخدم غير معروف'}',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  color: PdfColors.blue700,
                                ),
                              ),
                              pw.Spacer(),
                              pw.Text(
                                'تاريخ الإنشاء: ${_formatTimestamp(document.createdAt)}',
                                style: pw.TextStyle(
                                  fontSize: 9,
                                  color: PdfColors.grey600,
                                ),
                              ),
                            ],
                          ),

                          if (document.isShared)
                            pw.Padding(
                              padding: pw.EdgeInsets.only(top: 5),
                              child: pw.Row(
                                children: [
                                  pw.Container(
                                    padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: pw.BoxDecoration(
                                      color: PdfColors.green600,
                                      borderRadius: pw.BorderRadius.circular(8),
                                    ),
                                    child: pw.Text(
                                      'مشترك',
                                      style: pw.TextStyle(
                                        fontSize: 8,
                                        color: PdfColors.white,
                                        fontWeight: pw.FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  pw.SizedBox(width: 10),
                                  pw.Text(
                                    'الصلاحية: ${_translatePermission(document.permission)}',
                                    style: pw.TextStyle(
                                      fontSize: 9,
                                      color: PdfColors.grey600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // تذييل الصفحة مع رقم الصفحة
              pw.Spacer(),
              _buildPageFooter(context),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// الحصول على لون نوع المستند
  PdfColor _getDocumentTypeColor(TaskDocumentType type) {
    switch (type) {
      case TaskDocumentType.report:
        return PdfColors.blue600;
      case TaskDocumentType.analysis:
        return PdfColors.purple600;
      case TaskDocumentType.plan:
        return PdfColors.green600;
      case TaskDocumentType.attachment:
        return PdfColors.orange600;
      case TaskDocumentType.note:
        return PdfColors.yellow600;
      case TaskDocumentType.specification:
        return PdfColors.red600;
      case TaskDocumentType.documentation:
        return PdfColors.indigo600;
    }
  }

  /// ترجمة نوع المستند
  String _translateDocumentType(TaskDocumentType type) {
    switch (type) {
      case TaskDocumentType.report:
        return 'تقرير';
      case TaskDocumentType.analysis:
        return 'تحليل';
      case TaskDocumentType.plan:
        return 'خطة';
      case TaskDocumentType.attachment:
        return 'مرفق';
      case TaskDocumentType.note:
        return 'ملاحظة';
      case TaskDocumentType.specification:
        return 'مواصفات';
      case TaskDocumentType.documentation:
        return 'توثيق';
    }
  }

  /// ترجمة صلاحية المستند
  String _translatePermission(TaskDocumentPermission permission) {
    switch (permission) {
      case TaskDocumentPermission.read:
        return 'قراءة فقط';
      case TaskDocumentPermission.write:
        return 'قراءة وكتابة';
      case TaskDocumentPermission.admin:
        return 'إدارة كاملة';
    }
  }

  /// الحصول على لون مستوى المساهم
  PdfColor _getContributorLevelColor(ContributorLevel level) {
    switch (level) {
      case ContributorLevel.basic:
        return PdfColors.grey600;
      case ContributorLevel.medium:
        return PdfColors.blue600;
      case ContributorLevel.active:
        return PdfColors.green600;
      case ContributorLevel.veryActive:
        return PdfColors.purple600;
    }
  }

  /// الحصول على نص مستوى المساهم
  String _getContributorLevelText(ContributorLevel level) {
    return level.displayName;
  }

  /// صفحات سجل الأحداث والتحويلات الديناميكية
  List<pw.Page> _buildHistoryPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final history = data['history'] as List<TaskHistory>;
    final users = data['users'] as Map<int, User>;
    final pages = <pw.Page>[];

    if (history.isEmpty) return pages;

    // فصل التحويلات عن باقي الأحداث
    final transfers = <TaskHistory>[];
    final otherEvents = <TaskHistory>[];

    for (final event in history) {
      if (_isTransferEvent(event)) {
        transfers.add(event);
      } else {
        otherEvents.add(event);
      }
    }

    const int transfersPerPage = 6; // عدد التحويلات في كل صفحة (زيادة من 4 إلى 6)
    const int eventsPerPage = 12; // عدد الأحداث الأخرى في كل صفحة

    // صفحة الإحصائيات والتحويلات
    if (transfers.isNotEmpty) {
      final transferPages = (transfers.length / transfersPerPage).ceil();

      for (int pageIndex = 0; pageIndex < transferPages; pageIndex++) {
        final startIndex = pageIndex * transfersPerPage;
        final endIndex = (startIndex + transfersPerPage).clamp(0, transfers.length);
        final pageTransfers = transfers.sublist(startIndex, endIndex);

        pages.add(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            theme: theme,
            build: (context) => pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                // رأس الصفحة
                _buildSectionHeaderCentered(
                  pageIndex == 0
                    ? 'سجل الأحداث والتحويلات'
                    : 'سجل التحويلات (تابع)',
                  PdfColors.red600
                ),

                pw.SizedBox(height: 20),

                // إحصائيات (في الصفحة الأولى فقط)
                if (pageIndex == 0) ...[
                  _buildSection(
                    'إحصائيات الأحداث',
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        _buildDetailRow('إجمالي الأحداث:', '${history.length}'),
                        _buildDetailRow('عدد التحويلات:', '${transfers.length}'),
                        _buildDetailRow('أحداث أخرى:', '${otherEvents.length}'),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 15),
                ],

                // معلومات الصفحة
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.orange50,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Row(
                    children: [
                      pw.Text(
                        'الصفحة ${pageIndex + 1} من $transferPages',
                        style: pw.TextStyle(
                          fontSize: 10,
                          color: PdfColors.grey600,
                        ),
                      ),
                      pw.Spacer(),
                      pw.Text(
                        'سجل تحويلات المهمة (${transfers.length} تحويل)',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.orange700,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 15),

                // التحويلات
                pw.Expanded(
                  child: pw.ListView.builder(
                    itemCount: pageTransfers.length,
                    itemBuilder: (context, index) {
                      final transfer = pageTransfers[index];
                      final globalIndex = startIndex + index + 1;

                      return pw.Container(
                        margin: pw.EdgeInsets.only(bottom: 12),
                        padding: pw.EdgeInsets.all(8),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.orange300),
                          borderRadius: pw.BorderRadius.circular(8),
                          color: PdfColors.orange50,
                        ),
                        child: _buildCompactTransferCard(transfer, users, globalIndex),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // صفحات الأحداث الأخرى
    if (otherEvents.isNotEmpty) {
      final eventPages = (otherEvents.length / eventsPerPage).ceil();

      for (int pageIndex = 0; pageIndex < eventPages; pageIndex++) {
        final startIndex = pageIndex * eventsPerPage;
        final endIndex = (startIndex + eventsPerPage).clamp(0, otherEvents.length);
        final pageEvents = otherEvents.sublist(startIndex, endIndex);

        pages.add(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            theme: theme,
            build: (context) => pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                // رأس الصفحة
                _buildSectionHeaderCentered(
                  pageIndex == 0 && transfers.isEmpty
                    ? 'سجل الأحداث والتحويلات'
                    : 'سجل الأحداث الأخرى${pageIndex > 0 ? ' (تابع)' : ''}',
                  PdfColors.blue600
                ),

                pw.SizedBox(height: 20),

                // معلومات الصفحة
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Row(
                    children: [
                      pw.Text(
                        'الصفحة ${pageIndex + 1} من $eventPages',
                        style: pw.TextStyle(
                          fontSize: 10,
                          color: PdfColors.grey600,
                        ),
                      ),
                      pw.Spacer(),
                      pw.Text(
                        'سجل الأحداث الأخرى (${otherEvents.length} حدث)',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue700,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 15),

                // الأحداث
                pw.Expanded(
                  child: pw.ListView.builder(
                    itemCount: pageEvents.length,
                    itemBuilder: (context, index) {
                      final event = pageEvents[index];
                      final user = users[event.changedBy] ?? users[event.userId];
                      final globalIndex = startIndex + index + 1;

                      return pw.Container(
                        margin: pw.EdgeInsets.only(bottom: 8),
                        padding: pw.EdgeInsets.all(10),
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(color: PdfColors.grey300),
                          borderRadius: pw.BorderRadius.circular(8),
                          color: _getEventColor(event.action),
                        ),
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            // رأس الحدث
                            pw.Row(
                              children: [
                                pw.Container(
                                  padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: pw.BoxDecoration(
                                    color: _getEventTextColor(event.action),
                                    borderRadius: pw.BorderRadius.circular(10),
                                  ),
                                  child: pw.Text(
                                    '$globalIndex',
                                    style: pw.TextStyle(
                                      fontSize: 8,
                                      color: PdfColors.white,
                                      fontWeight: pw.FontWeight.bold,
                                    ),
                                  ),
                                ),
                                pw.SizedBox(width: 10),
                                pw.Expanded(
                                  child: pw.Text(
                                    event.actionDescription,
                                    style: pw.TextStyle(
                                      fontSize: 11,
                                      fontWeight: pw.FontWeight.bold,
                                      color: _getEventTextColor(event.action),
                                    ),
                                  ),
                                ),
                                pw.Text(
                                  _formatTimestamp(event.changedAt ?? event.timestamp),
                                  style: pw.TextStyle(
                                    fontSize: 9,
                                    color: PdfColors.grey600,
                                  ),
                                ),
                              ],
                            ),

                            pw.SizedBox(height: 5),

                            // معلومات إضافية
                            if (user != null)
                              pw.Text(
                                'بواسطة: ${user.name}',
                                style: pw.TextStyle(
                                  fontSize: 9,
                                  color: PdfColors.blue700,
                                ),
                              ),

                            if (event.details != null && event.details!.isNotEmpty) ...[
                              pw.SizedBox(height: 3),
                              pw.Text(
                                'التفاصيل: ${event.details}',
                                style: pw.TextStyle(
                                  fontSize: 9,
                                  color: PdfColors.grey700,
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return pages;
  }

  /// الحصول على لون الخلفية حسب نوع الحدث
  PdfColor _getEventColor(String changeType) {
    switch (changeType.toLowerCase()) {
      case 'created':
      case 'إنشاء':
        return PdfColors.green50;
      case 'updated':
      case 'تحديث':
        return PdfColors.blue50;
      case 'deleted':
      case 'حذف':
        return PdfColors.red50;
      case 'completed':
      case 'إنجاز':
        return PdfColors.purple50;
      default:
        return PdfColors.grey50;
    }
  }

  /// الحصول على لون النص حسب نوع الحدث
  PdfColor _getEventTextColor(String changeType) {
    switch (changeType.toLowerCase()) {
      case 'created':
      case 'إنشاء':
        return PdfColors.green700;
      case 'updated':
      case 'تحديث':
        return PdfColors.blue700;
      case 'deleted':
      case 'حذف':
        return PdfColors.red700;
      case 'completed':
      case 'إنجاز':
        return PdfColors.purple700;
      case 'assigned':
      case 'تحويل':
      case 'تكليف':
        return PdfColors.orange700;
      default:
        return PdfColors.grey700;
    }
  }

  /// التحقق من كون الحدث متعلق بالتحويل
  bool _isTransferEvent(TaskHistory event) {
    final action = event.action.toLowerCase();
    final changeType = (event.changeType ?? '').toLowerCase();

    return action.contains('assign') ||
           action.contains('transfer') ||
           action.contains('reassign') ||
           action.contains('تحويل') ||
           action.contains('تكليف') ||
           action.contains('إسناد') ||
           action == 'assigned' ||
           changeType.contains('assign') ||
           changeType.contains('تحويل') ||
           (event.oldValue != null && event.newValue != null &&
            event.oldValue != event.newValue &&
            (action.contains('assign') || changeType.contains('مكلف')));
  }



  /// بناء بطاقة تحويل مضغوطة (50% من الحجم الأصلي)
  pw.Widget _buildCompactTransferCard(TaskHistory transfer, Map<int, User> users, int transferNumber) {
    final executor = users[transfer.changedBy] ?? users[transfer.userId];

    // محاولة الحصول على أسماء المستخدمين من البيانات المختلفة
    String fromUserName = _getUserNameFromTransferValue(transfer.oldValue, users) ?? 'غير محدد';
    String toUserName = _getUserNameFromTransferValue(transfer.newValue, users) ?? 'غير محدد';

    return pw.Container(
      height: 50, // زيادة الارتفاع من 38 إلى 50 لعرض الأسماء بوضوح
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // رأس التحويل المضغوط
          pw.Row(
            children: [
              pw.Container(
                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: pw.BoxDecoration(
                  color: PdfColors.orange600,
                  borderRadius: pw.BorderRadius.circular(6),
                ),
                child: pw.Text(
                  'تحويل $transferNumber',
                  style: pw.TextStyle(
                    fontSize: 7, // زيادة من 6 إلى 7
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Spacer(),
              pw.Text(
                _formatTimestamp(transfer.changedAt ?? transfer.timestamp),
                style: pw.TextStyle(
                  fontSize: 8, // زيادة من 7 إلى 8
                  color: PdfColors.grey600,
                ),
              ),
            ],
          ),

          pw.SizedBox(height: 4), // زيادة من 2 إلى 4 للوضوح

          // مخطط التحويل المضغوط جداً
          pw.Expanded(
            child: pw.Row(
              children: [
                // المرسل (من) - في اليسار
                pw.Expanded(
                  flex: 4,
                  child: pw.Container(
                    padding: pw.EdgeInsets.all(2),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.red100,
                      borderRadius: pw.BorderRadius.circular(3),
                      border: pw.Border.all(color: PdfColors.red300, width: 0.5),
                    ),
                    child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Text(
                          'من',
                          style: pw.TextStyle(
                            fontSize: 6, // زيادة من 5 إلى 6
                            color: PdfColors.red700,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 2), // زيادة من 1 إلى 2
                        pw.Text(
                          _truncateText(fromUserName, 10), // تقليل عدد الأحرف من 12 إلى 10
                          style: pw.TextStyle(
                            fontSize: 9, // زيادة من 7 إلى 9
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.red800,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                // سهم التحويل
                pw.Container(
                  width: 25,
                  child: pw.Center(
                    child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Text(
                          '<<',
                          style: pw.TextStyle(
                            fontSize: 7, // تقليص من 8 إلى 7
                            color: PdfColors.orange600,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          'تحويل',
                          style: pw.TextStyle(
                            fontSize: 3, // تقليص من 4 إلى 3
                            color: PdfColors.orange600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // المستلم (إلى) - في اليمين
                pw.Expanded(
                  flex: 4,
                  child: pw.Container(
                    padding: pw.EdgeInsets.all(2),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.green100,
                      borderRadius: pw.BorderRadius.circular(3),
                      border: pw.Border.all(color: PdfColors.green300, width: 0.5),
                    ),
                    child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Text(
                          'إلى',
                          style: pw.TextStyle(
                            fontSize: 6, // زيادة من 5 إلى 6
                            color: PdfColors.green700,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 2), // زيادة من 1 إلى 2
                        pw.Text(
                          _truncateText(toUserName, 10), // تقليل عدد الأحرف من 12 إلى 10
                          style: pw.TextStyle(
                            fontSize: 9, // زيادة من 7 إلى 9
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green800,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                // معلومات إضافية في الجانب
                if (executor != null)
                  pw.Expanded(
                    flex: 3,
                    child: pw.Container(
                      padding: pw.EdgeInsets.all(2),
                      child: pw.Text(
                        'بواسطة: ${_truncateText(executor.name, 8)}', // تقليل عدد الأحرف من 10 إلى 8
                        style: pw.TextStyle(
                          fontSize: 7, // زيادة من 5 إلى 7
                          color: PdfColors.blue700,
                        ),
                        textAlign: pw.TextAlign.left,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// قطع النص إذا كان طويلاً
  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }

  /// الحصول على اسم المستخدم من قيمة التحويل
  String? _getUserNameFromTransferValue(String? value, Map<int, User> users) {
    if (value == null || value.isEmpty) return null;

    // أولاً: محاولة استخراج معرف المستخدم من النص
    final userId = _extractUserIdFromValue(value);
    if (userId != null && users[userId] != null) {
      return users[userId]!.name;
    }

    // ثانياً: البحث عن المستخدم بالاسم في قائمة المستخدمين
    for (final user in users.values) {
      if (user.name.toLowerCase().contains(value.toLowerCase()) ||
          value.toLowerCase().contains(user.name.toLowerCase())) {
        return user.name;
      }
    }

    // ثالثاً: إرجاع القيمة كما هي إذا لم نجد مطابقة
    return value;
  }

  /// استخراج معرف المستخدم من القيمة النصية
  int? _extractUserIdFromValue(String? value) {
    if (value == null) return null;

    // محاولة استخراج الرقم من النص
    final regex = RegExp(r'\d+');
    final match = regex.firstMatch(value);
    if (match != null) {
      return int.tryParse(match.group(0)!);
    }

    return null;
  }

  /// ترجمة حالة المهمة إلى العربية
  static String _translateTaskStatus(String? status) {
    if (status == null || status.isEmpty) return 'غير محدد';

    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'in_progress':
      case 'in progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
      case 'canceled':
        return 'ملغية';
      case 'on_hold':
      case 'on hold':
        return 'معلقة';
      case 'review':
        return 'قيد المراجعة';
      case 'approved':
        return 'معتمدة';
      case 'rejected':
        return 'مرفوضة';
      default:
        return status; // إرجاع النص الأصلي إذا لم توجد ترجمة
    }
  }

  /// ترجمة أولوية المهمة إلى العربية
  static String _translateTaskPriority(String? priority) {
    if (priority == null || priority.isEmpty) return 'غير محدد';

    switch (priority.toLowerCase()) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      case 'critical':
        return 'حرجة';
      default:
        return priority; // إرجاع النص الأصلي إذا لم توجد ترجمة
    }
  }



  /// إنشاء التقرير مع الفلتر
  Future<Uint8List> _generateFilteredReport(Map<String, dynamic> data, ReportFilterOptions filterOptions) async {
    debugPrint('📄 إنشاء التقرير المفلتر...');

    // تحميل الخطوط
    await _loadArabicFonts();

    // إنشاء موضوع PDF
    final theme = pw.ThemeData.withFont(
      base: _arabicFont!,
      bold: _arabicBoldFont!,
    );

    // إنشاء مستند PDF
    final pdf = pw.Document();

    // صفحة الغلاف (دائماً مضمنة)
    pdf.addPage(_buildCoverPage(data, theme));

    // إضافة الصفحات حسب الفلتر
    if (filterOptions.includeTaskDetails) {
      pdf.addPage(_buildTaskDetailsPage(data, theme));
    }

    if (filterOptions.includeComments && (data['comments'] as List).isNotEmpty) {
      final commentsPages = _buildCommentsPages(data, theme);
      for (final page in commentsPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeAttachments && (data['attachments'] as List).isNotEmpty) {
      final attachmentsPages = _buildAttachmentsPages(data, theme);
      for (final page in attachmentsPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeSubtasks && (data['subtasks'] as List).isNotEmpty) {
      final subtasksPages = _buildSubtasksPages(data, theme);
      for (final page in subtasksPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeTimeTracking && (data['timeTracking'] as List).isNotEmpty) {
      final timeTrackingPages = _buildTimeTrackingPages(data, theme);
      for (final page in timeTrackingPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeHistory && (data['history'] as List).isNotEmpty) {
      final historyPages = _buildHistoryPages(data, theme);
      for (final page in historyPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeContributors && (data['contributors'] as List).isNotEmpty) {
      final contributorsPages = _buildContributorsPages(data, theme);
      for (final page in contributorsPages) {
        pdf.addPage(page);
      }
    }

    if (filterOptions.includeProgress && (data['progressTrackers'] as List).isNotEmpty) {
      final progressPages = _buildProgressPages(data, theme);
      for (final page in progressPages) {
        pdf.addPage(page);
      }
    }

    debugPrint('✅ تم إنشاء التقرير المفلتر بنجاح');
    return pdf.save();
  }

  /// صفحات المساهمين الديناميكية
  List<pw.Page> _buildContributorsPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final contributors = data['contributors'] as List<TaskContributor>;
    final pages = <pw.Page>[];

    if (contributors.isEmpty) return pages;

    const int contributorsPerPage = 12; // عدد المساهمين في كل صفحة (زيادة من 8 إلى 12)
    final int totalPages = (contributors.length / contributorsPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * contributorsPerPage;
      final endIndex = (startIndex + contributorsPerPage).clamp(0, contributors.length);
      final pageContributors = contributors.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'المساهمون في المهمة'
                  : 'المساهمون في المهمة (تابع)',
                PdfColors.indigo600
              ),

              pw.SizedBox(height: 20),

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.indigo50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    if (pageIndex == 0)
                      pw.Text(
                        'إجمالي المساهمين: ${contributors.length}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.indigo700,
                        ),
                      ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // المساهمون
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageContributors.length,
                  itemBuilder: (context, index) {
                    final contributor = pageContributors[index];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 8), // تقليص من 12 إلى 8
                      padding: pw.EdgeInsets.all(8), // تقليص من 12 إلى 8
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.indigo300),
                        borderRadius: pw.BorderRadius.circular(6), // تقليص من 8 إلى 6
                        color: PdfColors.indigo50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس المساهم
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1), // تقليص
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.indigo600,
                                  borderRadius: pw.BorderRadius.circular(7), // تقليص من 10 إلى 7
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 6, // تقليص من 8 إلى 6
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 7), // تقليص من 10 إلى 7
                              pw.Expanded(
                                child: pw.Text(
                                  contributor.userName,
                                  style: pw.TextStyle(
                                    fontSize: 10, // تقليص من 14 إلى 10
                                    fontWeight: pw.FontWeight.bold,
                                    color: PdfColors.blue900,
                                  ),
                                ),
                              ),
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 3), // تقليص
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.green600,
                                  borderRadius: pw.BorderRadius.circular(8), // تقليص من 12 إلى 8
                                ),
                                child: pw.Text(
                                  '${contributor.percentage.toStringAsFixed(1)}%',
                                  style: pw.TextStyle(
                                    fontSize: 7, // تقليص من 10 إلى 7
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 5), // تقليص من 8 إلى 5

                          // إجمالي المساهمات
                          pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.center,
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.blue600,
                                  borderRadius: pw.BorderRadius.circular(8),
                                ),
                                child: pw.Text(
                                  'إجمالي: ${contributor.totalContributions} (${contributor.percentage.toStringAsFixed(1)}%)',
                                  style: pw.TextStyle(
                                    fontSize: 8,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 4),

                          // تفاصيل أنواع المساهمات - الصف الأول
                          pw.Row(
                            children: [
                              // التعليقات (دائماً تظهر)
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: pw.BoxDecoration(
                                  color: contributor.commentsCount > 0 ? PdfColors.green100 : PdfColors.grey100,
                                  borderRadius: pw.BorderRadius.circular(4),
                                  border: pw.Border.all(
                                    color: contributor.commentsCount > 0 ? PdfColors.green300 : PdfColors.grey300
                                  ),
                                ),
                                child: pw.Text(
                                  'تعليقات: ${contributor.commentsCount}',
                                  style: pw.TextStyle(
                                    fontSize: 7,
                                    color: contributor.commentsCount > 0 ? PdfColors.green700 : PdfColors.grey600
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 4),

                              // المرفقات (دائماً تظهر)
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: pw.BoxDecoration(
                                  color: contributor.attachmentsCount > 0 ? PdfColors.orange100 : PdfColors.grey100,
                                  borderRadius: pw.BorderRadius.circular(4),
                                  border: pw.Border.all(
                                    color: contributor.attachmentsCount > 0 ? PdfColors.orange300 : PdfColors.grey300
                                  ),
                                ),
                                child: pw.Text(
                                  'مرفقات: ${contributor.attachmentsCount}',
                                  style: pw.TextStyle(
                                    fontSize: 7,
                                    color: contributor.attachmentsCount > 0 ? PdfColors.orange700 : PdfColors.grey600
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 4),

                              // المستندات (دائماً تظهر)
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: pw.BoxDecoration(
                                  color: contributor.documentsCount > 0 ? PdfColors.purple100 : PdfColors.grey100,
                                  borderRadius: pw.BorderRadius.circular(4),
                                  border: pw.Border.all(
                                    color: contributor.documentsCount > 0 ? PdfColors.purple300 : PdfColors.grey300
                                  ),
                                ),
                                child: pw.Text(
                                  'مستندات: ${contributor.documentsCount}',
                                  style: pw.TextStyle(
                                    fontSize: 7,
                                    color: contributor.documentsCount > 0 ? PdfColors.purple700 : PdfColors.grey600
                                  ),
                                ),
                              ),
                            ],
                          ),

                          // تفاصيل أنواع المساهمات - الصف الثاني
                          pw.SizedBox(height: 3),
                          pw.Row(
                            children: [
                              // الأنشطة (دائماً تظهر)
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: pw.BoxDecoration(
                                  color: contributor.activitiesCount > 0 ? PdfColors.indigo100 : PdfColors.grey100,
                                  borderRadius: pw.BorderRadius.circular(4),
                                  border: pw.Border.all(
                                    color: contributor.activitiesCount > 0 ? PdfColors.indigo300 : PdfColors.grey300
                                  ),
                                ),
                                child: pw.Text(
                                  'أنشطة: ${contributor.activitiesCount}',
                                  style: pw.TextStyle(
                                    fontSize: 7,
                                    color: contributor.activitiesCount > 0 ? PdfColors.indigo700 : PdfColors.grey600
                                  ),
                                ),
                              ),
                              pw.Spacer(),

                              // مستوى المساهم
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: pw.BoxDecoration(
                                  color: _getContributorLevelColor(contributor.level),
                                  borderRadius: pw.BorderRadius.circular(6),
                                ),
                                child: pw.Text(
                                  _getContributorLevelText(contributor.level),
                                  style: pw.TextStyle(
                                    fontSize: 7,
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          // البريد الإلكتروني (مختصر)
                          if (contributor.userEmail.isNotEmpty) ...[
                            pw.SizedBox(height: 3),
                            pw.Text(
                              'البريد: ${contributor.userEmail}',
                              style: pw.TextStyle(
                                fontSize: 7,
                                color: PdfColors.grey500,
                              ),
                              maxLines: 1,
                              overflow: pw.TextOverflow.clip,
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// صفحات التقدم والإحصائيات الديناميكية
  List<pw.Page> _buildProgressPages(Map<String, dynamic> data, pw.ThemeData theme) {
    final progressTrackers = data['progressTrackers'] as List<TaskProgressTracker>;
    final users = data['users'] as Map<int, User>;
    final task = data['task'] as Task;
    final pages = <pw.Page>[];

    if (progressTrackers.isEmpty) return pages;

    const int trackersPerPage = 15; // عدد تحديثات التقدم في كل صفحة (زيادة من 10 إلى 15)
    final int totalPages = (progressTrackers.length / trackersPerPage).ceil();

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * trackersPerPage;
      final endIndex = (startIndex + trackersPerPage).clamp(0, progressTrackers.length);
      final pageTrackers = progressTrackers.sublist(startIndex, endIndex);

      pages.add(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: theme,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              // رأس الصفحة
              _buildSectionHeaderCentered(
                pageIndex == 0
                  ? 'تقدم المهمة والإحصائيات'
                  : 'تقدم المهمة والإحصائيات (تابع)',
                PdfColors.purple600
              ),

              pw.SizedBox(height: 20),

              // إحصائيات عامة (في الصفحة الأولى فقط)
              if (pageIndex == 0) ...[
                _buildSection(
                  'الإحصائيات العامة',
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      _buildDetailRow('نسبة الإنجاز الحالية:', '${task.completionPercentage}%'),
                      _buildDetailRow('عدد تحديثات التقدم:', '${progressTrackers.length}'),
                      if (task.estimatedTime != null)
                        _buildDetailRow('الوقت المقدر:', '${task.estimatedTime} دقيقة'),
                      if (task.actualTime != null)
                        _buildDetailRow('الوقت الفعلي:', '${task.actualTime} دقيقة'),
                    ],
                  ),
                ),
                pw.SizedBox(height: 15),
              ],

              // معلومات الصفحة
              pw.Container(
                padding: pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.purple50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  children: [
                    pw.Text(
                      'الصفحة ${pageIndex + 1} من $totalPages',
                      style: pw.TextStyle(
                        fontSize: 10,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Spacer(),
                    pw.Text(
                      'سجل تحديثات التقدم',
                      style: pw.TextStyle(
                        fontSize: 12,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.purple700,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 15),

              // تحديثات التقدم
              pw.Expanded(
                child: pw.ListView.builder(
                  itemCount: pageTrackers.length,
                  itemBuilder: (context, index) {
                    final tracker = pageTrackers[index];
                    final user = users[tracker.updatedBy];
                    final globalIndex = startIndex + index + 1;

                    return pw.Container(
                      margin: pw.EdgeInsets.only(bottom: 7), // تقليص من 10 إلى 7
                      padding: pw.EdgeInsets.all(8), // تقليص من 12 إلى 8
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.purple300),
                        borderRadius: pw.BorderRadius.circular(6), // تقليص من 8 إلى 6
                        color: PdfColors.purple50,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          // رأس التحديث
                          pw.Row(
                            children: [
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 4, vertical: 1), // تقليص
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.purple600,
                                  borderRadius: pw.BorderRadius.circular(7), // تقليص من 10 إلى 7
                                ),
                                child: pw.Text(
                                  '$globalIndex',
                                  style: pw.TextStyle(
                                    fontSize: 6, // تقليص من 8 إلى 6
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                              pw.SizedBox(width: 7), // تقليص من 10 إلى 7
                              pw.Text(
                                user?.name ?? 'مستخدم غير معروف',
                                style: pw.TextStyle(
                                  fontSize: 8, // تقليص من 11 إلى 8
                                  color: PdfColors.blue700,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                              pw.Spacer(),
                              pw.Container(
                                padding: pw.EdgeInsets.symmetric(horizontal: 6, vertical: 3), // تقليص
                                decoration: pw.BoxDecoration(
                                  color: PdfColors.green600,
                                  borderRadius: pw.BorderRadius.circular(8), // تقليص من 12 إلى 8
                                ),
                                child: pw.Text(
                                  '${tracker.progressPercentage.toStringAsFixed(1)}%',
                                  style: pw.TextStyle(
                                    fontSize: 7, // تقليص من 10 إلى 7
                                    color: PdfColors.white,
                                    fontWeight: pw.FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 5), // تقليص من 8 إلى 5

                          // تاريخ التحديث
                          pw.Text(
                            'تاريخ التحديث: ${_formatTimestamp(tracker.updatedAt)}',
                            style: pw.TextStyle(
                              fontSize: 7, // تقليص من 10 إلى 7
                              color: PdfColors.grey600,
                            ),
                          ),

                          // الملاحظات إذا وجدت
                          if (tracker.notes != null && tracker.notes!.isNotEmpty) ...[
                            pw.SizedBox(height: 3), // تقليص من 5 إلى 3
                            pw.Text(
                              'الملاحظات: ${tracker.notes}',
                              style: pw.TextStyle(
                                fontSize: 7, // تقليص من 10 إلى 7
                                color: PdfColors.grey700,
                              ),
                            ),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    }

    return pages;
  }

  /// تحويل المساهمين الأساسيين إلى مساهمين للتقرير مع حساب المساهمات الإضافية
  List<TaskContributor> _convertBasicContributorsToReportContributors(List<dynamic> basicContributors, [Map<String, dynamic>? reportData]) {
    final reportContributors = <TaskContributor>[];
    _tempReportData = reportData; // حفظ البيانات للاستخدام في دوال الحساب

    // تسجيل تشخيصي لبنية البيانات المجمعة
    if (reportData != null) {
      debugPrint('🔍 فحص بنية البيانات المجمعة:');
      debugPrint('   - مفاتيح البيانات: ${reportData.keys.toList()}');

      // فحص التعليقات
      final comments = reportData['comments'] as List?;
      debugPrint('   - عدد التعليقات المجمعة: ${comments?.length ?? 0}');
      if (comments != null && comments.isNotEmpty) {
        debugPrint('   - نوع أول تعليق: ${comments.first.runtimeType}');
      }

      // فحص المرفقات
      final attachments = reportData['attachments'] as List?;
      debugPrint('   - عدد المرفقات المجمعة: ${attachments?.length ?? 0}');
      if (attachments != null && attachments.isNotEmpty) {
        debugPrint('   - نوع أول مرفق: ${attachments.first.runtimeType}');
      }

      // فحص المستندات
      final documents = reportData['taskDocuments'] as List?;
      debugPrint('   - عدد المستندات المجمعة: ${documents?.length ?? 0}');
      if (documents != null && documents.isNotEmpty) {
        debugPrint('   - نوع أول مستند: ${documents.first.runtimeType}');
      }

      // فحص متتبعات التقدم
      final progressTrackers = reportData['progressTrackers'] as List?;
      debugPrint('   - عدد متتبعات التقدم: ${progressTrackers?.length ?? 0}');
      if (progressTrackers != null && progressTrackers.isNotEmpty) {
        debugPrint('   - نوع أول متتبع: ${progressTrackers.first.runtimeType}');
      }

      // فحص الأحداث التاريخية
      final history = reportData['history'] as List?;
      debugPrint('   - عدد الأحداث التاريخية: ${history?.length ?? 0}');
      if (history != null && history.isNotEmpty) {
        debugPrint('   - نوع أول حدث: ${history.first.runtimeType}');
      }
    }

    debugPrint('🔍 بدء تحويل المساهمين:');
    debugPrint('   - عدد المساهمين الأساسيين: ${basicContributors.length}');
    debugPrint('   - نوع القائمة: ${basicContributors.runtimeType}');

    for (int i = 0; i < basicContributors.length; i++) {
      final basicContributor = basicContributors[i];
      debugPrint('🔍 معالجة المساهم $i:');
      debugPrint('   - نوع البيانات: ${basicContributor.runtimeType}');
      debugPrint('   - المحتوى: $basicContributor');

      // التعامل مع كائنات TaskContributor من task_contributor_model.dart
      if (basicContributor.runtimeType.toString().contains('TaskContributor')) {
        debugPrint('   ✅ المساهم هو TaskContributor - بدء التحويل');
        try {
          // استخراج البيانات من كائن TaskContributor
          final contributorMap = basicContributor.toJson() as Map<String, dynamic>;

          final userId = contributorMap['userId'] ?? 0;
          debugPrint('   - معرف المستخدم: $userId');
          debugPrint('   - اسم المستخدم: ${contributorMap['userName']}');
          debugPrint('   - البريد الإلكتروني: ${contributorMap['userEmail']}');
          debugPrint('   - الدور: ${contributorMap['userRole']}');

          // حساب المساهمات الإضافية من البيانات المجمعة
          final attachmentsCount = _countUserAttachmentsFromData(userId);
          final documentsCount = _countUserDocumentsFromData(userId);
          final realActivitiesCount = _countUserActivitiesFromData(userId);

          // حساب إجمالي المساهمات (استخدام البيانات الحقيقية)
          final apiCommentsCount = contributorMap['totalComments'] ?? 0; // من API
          final realCommentsCount = _countUserCommentsFromData(userId); // من البيانات المجمعة
          final apiActivitiesCount = contributorMap['totalUpdates'] ?? 0; // من API

          // استخدام البيانات الحقيقية المحسوبة
          final commentsCount = realCommentsCount > 0 ? realCommentsCount : apiCommentsCount;
          final totalContributions = commentsCount + attachmentsCount + documentsCount + realActivitiesCount;

          debugPrint('   💬 التعليقات (API): $apiCommentsCount');
          debugPrint('   💬 التعليقات (حقيقية): $realCommentsCount');
          debugPrint('   💬 التعليقات (مستخدمة): $commentsCount');
          debugPrint('   🔄 الأنشطة (API): $apiActivitiesCount');
          debugPrint('   🔄 الأنشطة (حقيقية): $realActivitiesCount');
          debugPrint('   📎 المرفقات: $attachmentsCount');
          debugPrint('   📄 المستندات: $documentsCount');
          debugPrint('   📊 إجمالي المساهمات: $totalContributions');

          // لا نحتاج لتحديد الدور - تم حذفه من العرض

          // تحويل المساهم الأساسي إلى مساهم للتقرير
          final reportContributor = TaskContributor(
            userId: userId,
            userName: contributorMap['userName'] ?? 'مستخدم غير معروف',
            userEmail: contributorMap['userEmail'] ?? '',
            role: 'مساهم', // دور افتراضي بسيط (لن يظهر في العرض)
            commentsCount: commentsCount,
            attachmentsCount: attachmentsCount,
            activitiesCount: realActivitiesCount, // استخدام الأنشطة الحقيقية
            documentsCount: documentsCount,
            totalContributions: totalContributions,
            level: ContributorLevel.fromContributions(totalContributions),
            percentage: contributorMap['contributionPercentage'] ?? 0.0,
          );

          reportContributors.add(reportContributor);
          debugPrint('   ✅ تم إضافة المساهم بنجاح');
        } catch (e) {
          debugPrint('   ❌ خطأ في تحويل المساهم: $e');
          debugPrint('   ❌ تفاصيل الخطأ: ${e.toString()}');
        }
      } else {
        debugPrint('   ❌ المساهم ليس TaskContributor - نوع البيانات: ${basicContributor.runtimeType}');
        debugPrint('   ❌ محتوى البيانات: $basicContributor');
      }
    }

    debugPrint('🔍 نتيجة التحويل:');
    debugPrint('   - عدد المساهمين المحولين: ${reportContributors.length}');
    debugPrint('   - قائمة المساهمين: ${reportContributors.map((c) => c.userName).toList()}');

    return reportContributors;
  }

  /// حساب عدد التعليقات للمستخدم من البيانات المجمعة
  int _countUserCommentsFromData(int userId) {
    if (_tempReportData == null) {
      debugPrint('   ⚠️ لا توجد بيانات مؤقتة للتعليقات');
      return 0;
    }

    try {
      final comments = _tempReportData!['comments'] as List?;
      if (comments == null) {
        debugPrint('   ⚠️ قائمة التعليقات فارغة أو غير موجودة');
        return 0;
      }

      debugPrint('   🔍 فحص ${comments.length} تعليق للمستخدم $userId');

      int count = 0;
      for (int i = 0; i < comments.length; i++) {
        final comment = comments[i];
        debugPrint('     - تعليق $i: نوع=${comment.runtimeType}');

        // التعامل مع كائنات TaskComment
        if (comment is TaskComment && comment.userId == userId) {
          count++;
          debugPrint('     ✅ تطابق! التعليق $i للمستخدم $userId (TaskComment)');
        }
        // التعامل مع Map كبديل
        else if (comment is Map<String, dynamic> && comment['userId'] == userId) {
          count++;
          debugPrint('     ✅ تطابق! التعليق $i للمستخدم $userId (Map)');
        }
        else {
          debugPrint('     - لا يطابق: userId=${comment is TaskComment ? comment.userId : (comment is Map ? comment['userId'] : 'غير متاح')}');
        }
      }
      debugPrint('   💬 التعليقات للمستخدم $userId: $count');
      return count;
    } catch (e) {
      debugPrint('⚠️ خطأ في حساب التعليقات للمستخدم $userId: $e');
      return 0;
    }
  }

  /// حساب عدد المرفقات للمستخدم من البيانات المجمعة
  int _countUserAttachmentsFromData(int userId) {
    if (_tempReportData == null) {
      debugPrint('   ⚠️ لا توجد بيانات مؤقتة للمرفقات');
      return 0;
    }

    try {
      final attachments = _tempReportData!['attachments'] as List?;
      if (attachments == null) {
        debugPrint('   ⚠️ قائمة المرفقات فارغة أو غير موجودة');
        return 0;
      }

      debugPrint('   🔍 فحص ${attachments.length} مرفق للمستخدم $userId');

      int count = 0;
      for (int i = 0; i < attachments.length; i++) {
        final attachment = attachments[i];
        debugPrint('     - مرفق $i: نوع=${attachment.runtimeType}');

        // التعامل مع كائنات Attachment
        if (attachment is Attachment && attachment.uploadedBy == userId) {
          count++;
          debugPrint('     ✅ تطابق! المرفق $i للمستخدم $userId (Attachment)');
        }
        // التعامل مع Map كبديل
        else if (attachment is Map<String, dynamic> && attachment['uploadedBy'] == userId) {
          count++;
          debugPrint('     ✅ تطابق! المرفق $i للمستخدم $userId (Map)');
        }
        else {
          debugPrint('     - لا يطابق: uploadedBy=${attachment is Attachment ? attachment.uploadedBy : (attachment is Map ? attachment['uploadedBy'] : 'غير متاح')}');
        }
      }
      debugPrint('   📎 المرفقات للمستخدم $userId: $count');
      return count;
    } catch (e) {
      debugPrint('⚠️ خطأ في حساب المرفقات للمستخدم $userId: $e');
      return 0;
    }
  }

  /// حساب عدد المستندات للمستخدم من البيانات المجمعة
  int _countUserDocumentsFromData(int userId) {
    if (_tempReportData == null) {
      debugPrint('   ⚠️ لا توجد بيانات مؤقتة للمستندات');
      return 0;
    }

    try {
      // البحث في مفاتيح مختلفة للمستندات
      List? documents = _tempReportData!['taskDocuments'] as List?;
      if (documents == null) {
        documents = _tempReportData!['documents'] as List?;
      }

      if (documents == null) {
        debugPrint('   ⚠️ قائمة المستندات فارغة أو غير موجودة');
        return 0;
      }

      debugPrint('   🔍 فحص ${documents.length} مستند للمستخدم $userId');

      int count = 0;
      for (int i = 0; i < documents.length; i++) {
        final document = documents[i];
        debugPrint('     - مستند $i: نوع=${document.runtimeType}');

        // التعامل مع كائنات TaskDocument
        if (document is TaskDocument && document.createdBy == userId) {
          count++;
          debugPrint('     ✅ تطابق! المستند $i للمستخدم $userId (TaskDocument)');
        }
        // التعامل مع Map كبديل
        else if (document is Map<String, dynamic> && document['createdBy'] == userId) {
          count++;
          debugPrint('     ✅ تطابق! المستند $i للمستخدم $userId (Map)');
        }
        else {
          debugPrint('     - لا يطابق: createdBy=${document is TaskDocument ? document.createdBy : (document is Map ? document['createdBy'] : 'غير متاح')}');
        }
      }
      debugPrint('   📄 المستندات للمستخدم $userId: $count');
      return count;
    } catch (e) {
      debugPrint('⚠️ خطأ في حساب المستندات للمستخدم $userId: $e');
      return 0;
    }
  }

  /// حساب عدد الأنشطة الفعلية للمستخدم من البيانات المجمعة
  /// الأنشطة تشمل: تحديثات التقدم، تغييرات الحالة، الأحداث التاريخية
  int _countUserActivitiesFromData(int userId) {
    if (_tempReportData == null) {
      debugPrint('   ⚠️ لا توجد بيانات مؤقتة للأنشطة');
      return 0;
    }

    try {
      int activitiesCount = 0;

      // حساب من TaskProgressTrackers (تحديثات التقدم)
      final progressTrackers = _tempReportData!['progressTrackers'] as List?;
      if (progressTrackers != null) {
        debugPrint('   🔍 فحص ${progressTrackers.length} متتبع تقدم للمستخدم $userId');
        for (int i = 0; i < progressTrackers.length; i++) {
          final tracker = progressTrackers[i];

          // التعامل مع كائنات TaskProgressTracker
          if (tracker is TaskProgressTracker && tracker.userId == userId) {
            activitiesCount++;
            debugPrint('     ✅ متتبع تقدم $i للمستخدم $userId (TaskProgressTracker)');
          }
          // التعامل مع Map كبديل
          else if (tracker is Map<String, dynamic> && tracker['userId'] == userId) {
            activitiesCount++;
            debugPrint('     ✅ متتبع تقدم $i للمستخدم $userId (Map)');
          }
        }
      }

      // حساب من TaskHistory (الأحداث التاريخية)
      final history = _tempReportData!['history'] as List?;
      if (history != null) {
        debugPrint('   🔍 فحص ${history.length} حدث تاريخي للمستخدم $userId');
        for (int i = 0; i < history.length; i++) {
          final event = history[i];

          // التعامل مع كائنات TaskHistory
          if (event is TaskHistory && event.userId == userId) {
            // استبعاد الأحداث التلقائية للنظام
            final action = event.action ?? '';
            if (!action.contains('system') && !action.contains('auto')) {
              activitiesCount++;
              debugPrint('     ✅ حدث تاريخي $i للمستخدم $userId (TaskHistory): $action');
            }
          }
          // التعامل مع Map كبديل
          else if (event is Map<String, dynamic> && event['userId'] == userId) {
            final action = event['action']?.toString() ?? '';
            if (!action.contains('system') && !action.contains('auto')) {
              activitiesCount++;
              debugPrint('     ✅ حدث تاريخي $i للمستخدم $userId (Map): $action');
            }
          }
        }
      }

      debugPrint('   🔄 الأنشطة للمستخدم $userId: $activitiesCount (تحديثات التقدم + الأحداث)');
      return activitiesCount;
    } catch (e) {
      debugPrint('⚠️ خطأ في حساب الأنشطة للمستخدم $userId: $e');
      return 0;
    }
  }


}
