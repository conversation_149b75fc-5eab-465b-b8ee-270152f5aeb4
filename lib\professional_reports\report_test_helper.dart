import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'report_helpers.dart';

/// فئة مساعدة لاختبار التحسينات في نظام التقارير
/// تتحقق من أن استخراج المساهمين وتنسيق التواريخ يعمل بشكل صحيح
class ReportTestHelper {
  
  /// اختبار استخراج المساهمين من accessUsers
  static void testContributorsExtraction(Task task) {
    if (!kDebugMode) return;
    
    print('🧪 اختبار استخراج المساهمين من task_access_users...');
    
    try {
      final taskJson = task.toJson();
      final contributors = ReportHelpers.extractContributorsFromAccessUsers(task, taskJson);
      
      print('✅ تم استخراج ${contributors.length} مساهم بنجاح');
      
      if (contributors.isNotEmpty) {
        print('📊 تفاصيل المساهمين:');
        for (var contributor in contributors.take(3)) { // عرض أول 3 مساهمين فقط
          print('   - ${contributor.userName} (${contributor.role}): ${contributor.totalContributions} مساهمة (${contributor.percentage.toStringAsFixed(1)}%)');
          print('     التفاصيل: ${contributor.commentsCount} تعليق، ${contributor.attachmentsCount} مرفق، ${contributor.activitiesCount} نشاط');
        }
        
        // التحقق من صحة النسب المئوية
        final totalPercentage = contributors.fold(0.0, (sum, c) => sum + c.percentage);
        if ((totalPercentage - 100.0).abs() < 0.1) {
          print('✅ النسب المئوية صحيحة (المجموع: ${totalPercentage.toStringAsFixed(1)}%)');
        } else {
          print('⚠️ خطأ في النسب المئوية (المجموع: ${totalPercentage.toStringAsFixed(1)}%)');
        }
      }
      
      // اختبار الطريقة الاحتياطية
      print('🧪 اختبار الطريقة الاحتياطية...');
      final taskJsonEmpty = <String, dynamic>{
        'id': task.id,
        'title': task.title,
        // بدون accessUsers لاختبار الطريقة الاحتياطية
      };
      final fallbackContributors = ReportHelpers.extractContributorsFromAccessUsers(task, taskJsonEmpty);
      print('✅ الطريقة الاحتياطية أنتجت ${fallbackContributors.length} مساهم');
      
    } catch (e) {
      print('❌ خطأ في اختبار استخراج المساهمين: $e');
    }
  }
  
  /// اختبار دوال تنسيق التواريخ
  static void testDateTimeFormatting() {
    if (!kDebugMode) return;
    
    print('🧪 اختبار دوال تنسيق التواريخ...');
    
    try {
      final now = DateTime.now();
      final timestamp = now.millisecondsSinceEpoch ~/ 1000;
      
      // اختبار التنسيقات المختلفة
      final dateOnly = DateTimeHelpers.formatDate(now);
      final dateTime = DateTimeHelpers.formatDateTime(now);
      final fromTimestamp = DateTimeHelpers.formatTimestamp(timestamp);
      final fromTimestampWithTime = DateTimeHelpers.formatTimestampWithTime(timestamp);
      
      print('✅ تنسيق التاريخ فقط: $dateOnly');
      print('✅ تنسيق التاريخ والوقت: $dateTime');
      print('✅ من timestamp (تاريخ فقط): $fromTimestamp');
      print('✅ من timestamp (تاريخ ووقت): $fromTimestampWithTime');
      
      // اختبار تنسيق المدة
      final duration1 = DateTimeHelpers.formatDuration(45); // 45 دقيقة
      final duration2 = DateTimeHelpers.formatDuration(125); // ساعة و 5 دقائق
      final duration3 = DateTimeHelpers.formatDuration(1500); // يوم و 1 ساعة
      
      print('✅ تنسيق المدة (45 دقيقة): $duration1');
      print('✅ تنسيق المدة (125 دقيقة): $duration2');
      print('✅ تنسيق المدة (1500 دقيقة): $duration3');
      
      // اختبار حساب الفرق بالأيام
      final yesterday = now.subtract(const Duration(days: 1));
      final daysDiff = DateTimeHelpers.daysBetween(yesterday, now);
      print('✅ الفرق بالأيام (أمس إلى اليوم): $daysDiff يوم');
      
      // اختبار التاريخ الآمن
      final safeDate1 = DateTimeHelpers.getSafeDate(null);
      final safeDate2 = DateTimeHelpers.getSafeDate(DateTime(1800)); // تاريخ غير صحيح
      final safeDate3 = DateTimeHelpers.getSafeDate(now);
      
      print('✅ التاريخ الآمن (null): ${DateTimeHelpers.formatDateTime(safeDate1)}');
      print('✅ التاريخ الآمن (1800): ${DateTimeHelpers.formatDateTime(safeDate2)}');
      print('✅ التاريخ الآمن (صحيح): ${DateTimeHelpers.formatDateTime(safeDate3)}');
      
    } catch (e) {
      print('❌ خطأ في اختبار تنسيق التواريخ: $e');
    }
  }
  
  /// اختبار شامل لجميع التحسينات
  static void runAllTests(Task task) {
    if (!kDebugMode) return;
    
    print('🚀 بدء الاختبارات الشاملة للتحسينات...');
    print('=' * 50);
    
    testContributorsExtraction(task);
    print('');
    testDateTimeFormatting();
    
    print('=' * 50);
    print('🎉 انتهت جميع الاختبارات');
  }
  
  /// إنشاء مهمة تجريبية للاختبار
  static Task createTestTask() {
    return Task(
      id: 1,
      title: 'مهمة اختبار التحسينات',
      description: 'هذه مهمة تجريبية لاختبار التحسينات في نظام التقارير',
      creatorId: 1,
      assigneeId: 2,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      status: 'نشطة',
      priority: 'عالية',
      completionPercentage: 75,
    );
  }
}