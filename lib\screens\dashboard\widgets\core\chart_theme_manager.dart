import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../charts/base/chart_interface.dart';

/// مدير ألوان وتصميم المخططات
/// 
/// يوفر إدارة موحدة للألوان والتصميم عبر جميع المخططات
class ChartThemeManager extends GetxService {
  // السمة الحالية
  final Rx<ChartTheme> _currentTheme = ChartTheme.defaultTheme().obs;
  
  // هل الوضع الداكن مفعل
  final RxBool _isDarkMode = false.obs;

  /// الحصول على السمة الحالية
  ChartTheme getCurrentTheme() => _currentTheme.value;

  /// تعيين السمة
  void setTheme(ChartTheme theme) {
    _currentTheme.value = theme;
  }

  /// تبديل الوضع الداكن/النهاري
  void toggleDarkMode() {
    _isDarkMode.value = !_isDarkMode.value;
    _currentTheme.value = _isDarkMode.value 
        ? ChartTheme.darkTheme() 
        : ChartTheme.defaultTheme();
  }

  /// الحصول على لون الرأس
  Color getHeaderColor() {
    return _currentTheme.value.primaryColor.withOpacity(0.1);
  }

  /// الحصول على نمط العنوان
  TextStyle getTitleStyle() {
    return _currentTheme.value.titleStyle;
  }

  /// الحصول على نمط التسمية
  TextStyle getLabelStyle() {
    return _currentTheme.value.labelStyle;
  }

  /// الحصول على لون الخلفية
  Color getBackgroundColor() {
    return _currentTheme.value.backgroundColor;
  }

  /// الحصول على لون النص
  Color getTextColor() {
    return _currentTheme.value.textColor;
  }

  /// الحصول على مجموعة الألوان
  List<Color> getColorPalette() {
    return _currentTheme.value.colorPalette;
  }

  /// الحصول على لون حسب الفهرس
  Color getColorByIndex(int index) {
    final colors = getColorPalette();
    return colors[index % colors.length];
  }

  /// إنشاء سمة مخصصة
  ChartTheme createCustomTheme({
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? textColor,
    List<Color>? colorPalette,
    TextStyle? titleStyle,
    TextStyle? labelStyle,
    double? borderRadius,
    double? elevation,
  }) {
    final current = _currentTheme.value;
    
    return ChartTheme(
      primaryColor: primaryColor ?? current.primaryColor,
      secondaryColor: secondaryColor ?? current.secondaryColor,
      backgroundColor: backgroundColor ?? current.backgroundColor,
      textColor: textColor ?? current.textColor,
      colorPalette: colorPalette ?? current.colorPalette,
      titleStyle: titleStyle ?? current.titleStyle,
      labelStyle: labelStyle ?? current.labelStyle,
      borderRadius: borderRadius ?? current.borderRadius,
      elevation: elevation ?? current.elevation,
    );
  }

  /// سمات محددة مسبقاً
  static ChartTheme get blueTheme => ChartTheme(
    primaryColor: Colors.blue,
    secondaryColor: Colors.blue.shade300,
    backgroundColor: Colors.white,
    textColor: Colors.black87,
    colorPalette: [
      Colors.blue.shade600,
      Colors.blue.shade400,
      Colors.blue.shade200,
      Colors.lightBlue.shade600,
      Colors.lightBlue.shade400,
      Colors.cyan.shade600,
      Colors.cyan.shade400,
      Colors.teal.shade600,
    ],
    titleStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: Colors.black87,
    ),
    labelStyle: const TextStyle(
      fontSize: 12,
      color: Colors.black54,
    ),
  );

  static ChartTheme get greenTheme => ChartTheme(
    primaryColor: Colors.green,
    secondaryColor: Colors.green.shade300,
    backgroundColor: Colors.white,
    textColor: Colors.black87,
    colorPalette: [
      Colors.green.shade600,
      Colors.green.shade400,
      Colors.green.shade200,
      Colors.lightGreen.shade600,
      Colors.lightGreen.shade400,
      Colors.lime.shade600,
      Colors.lime.shade400,
      Colors.teal.shade600,
    ],
    titleStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: Colors.black87,
    ),
    labelStyle: const TextStyle(
      fontSize: 12,
      color: Colors.black54,
    ),
  );

  static ChartTheme get orangeTheme => ChartTheme(
    primaryColor: Colors.orange,
    secondaryColor: Colors.orange.shade300,
    backgroundColor: Colors.white,
    textColor: Colors.black87,
    colorPalette: [
      Colors.orange.shade600,
      Colors.orange.shade400,
      Colors.orange.shade200,
      Colors.deepOrange.shade600,
      Colors.deepOrange.shade400,
      Colors.amber.shade600,
      Colors.amber.shade400,
      Colors.yellow.shade600,
    ],
    titleStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: Colors.black87,
    ),
    labelStyle: const TextStyle(
      fontSize: 12,
      color: Colors.black54,
    ),
  );

  /// الحصول على قائمة السمات المتاحة
  static List<ChartTheme> getAvailableThemes() {
    return [
      ChartTheme.defaultTheme(),
      blueTheme,
      greenTheme,
      orangeTheme,
      ChartTheme.darkTheme(),
    ];
  }

  /// الحصول على أسماء السمات
  static List<String> getThemeNames() {
    return [
      'افتراضي',
      'أزرق',
      'أخضر',
      'برتقالي',
      'داكن',
    ];
  }

  /// تطبيق سمة حسب الاسم
  void applyThemeByName(String themeName) {
    final themes = getAvailableThemes();
    final names = getThemeNames();
    
    final index = names.indexOf(themeName);
    if (index >= 0 && index < themes.length) {
      setTheme(themes[index]);
    }
  }

  /// الحصول على ألوان متدرجة
  List<Color> getGradientColors(Color baseColor) {
    return [
      baseColor.withOpacity(0.8),
      baseColor.withOpacity(0.6),
      baseColor.withOpacity(0.4),
      baseColor.withOpacity(0.2),
    ];
  }

  /// الحصول على لون متباين
  Color getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
}
