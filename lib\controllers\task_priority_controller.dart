import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/task_priority_models.dart';
import '../models/task_status_enum.dart' as task_enums;
import 'package:get/get.dart';

/// متحكم أولويات المهام
class TaskPriorityController extends GetxController {
  // استخدم قائمة ثابتة أو enum فقط
  final RxList<dynamic> _allPriorities = <dynamic>[].obs;
  final RxList<dynamic> _filteredPriorities = <dynamic>[].obs;

  // أولوية المهمة الحالية
  final Rx<TaskPriority?> _currentPriority = Rx<TaskPriority?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _errorMessage = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<task_enums.TaskPriority> get allPriorities => _allPriorities.cast<task_enums.TaskPriority>();
  List<task_enums.TaskPriority> get taskPriorities => _filteredPriorities.cast<task_enums.TaskPriority>();
  List<task_enums.TaskPriority> get filteredPriorities => _filteredPriorities.cast<task_enums.TaskPriority>();
  task_enums.TaskPriority? get currentPriority => _currentPriority.value as task_enums.TaskPriority?;
  RxBool get isLoading => _isLoading;
  RxString get errorMessage => _errorMessage;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  bool get showActiveOnly => _showActiveOnly.value;

  static final List<task_enums.TaskPriority> _enumPriorities = [
    task_enums.TaskPriority.low,
    task_enums.TaskPriority.medium,
    task_enums.TaskPriority.high,
    task_enums.TaskPriority.urgent,
  ];

  @override
  void onInit() {
    super.onInit();
    _allPriorities.assignAll(_enumPriorities);
    _applyFilters();
  }

  /// الحصول على أولوية مهمة بالمعرف
  Future<void> getPriority(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final priority = _allPriorities.firstWhereOrNull((p) => p.id == id);
      if (priority != null) {
        _currentPriority.value = priority;
        debugPrint('تم تحميل أولوية المهمة: ${priority.name}');
      } else {
        _error.value = 'أولوية المهمة غير موجودة';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل أولوية المهمة: $e';
      debugPrint('خطأ في تحميل أولوية المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<bool> createPriority(TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // محاكاة إنشاء أولوية جديدة
      final newPriority = priority.copyWith(id: DateTime.now().millisecondsSinceEpoch);
      _allPriorities.add(newPriority);
      _applyFilters();
      debugPrint('تم إنشاء أولوية مهمة جديدة: ${newPriority.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء أولوية المهمة: $e';
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updatePriority(TaskPriority priority) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final index = _allPriorities.indexWhere((p) => p.id == priority.id);
      if (index != -1) {
        _allPriorities[index] = priority;
        _applyFilters();
        debugPrint('تم تحديث أولوية المهمة: ${priority.name}');
        return true;
      }
      _error.value = 'أولوية المهمة غير موجودة';
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث أولوية المهمة: $e';
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة بمعاملات مسماة
  Future<bool> createTaskPriority({
    required String name,
    required String description,
    required int level,
    String? color,
    String? icon,
    bool isDefault = false,
  }) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      // إنشاء كائن TaskPriority جديد
      final newPriority = TaskPriority(
        id: 0, // سيتم تعيينه من قبل الخادم
        name: name,
        description: description,
        level: level,
        color: color,
        icon: icon,
        isDefault: isDefault,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isActive: true,
      );

      // محاكاة إنشاء أولوية جديدة
      final createdPriority = newPriority.copyWith(id: DateTime.now().millisecondsSinceEpoch);
      _allPriorities.add(createdPriority);
      _applyFilters();
      debugPrint('تم إنشاء أولوية المهمة بنجاح');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في إنشاء أولوية المهمة: $e';
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updateTaskPriority(TaskPriority taskPriority) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      final index = _allPriorities.indexWhere((p) => p.id == taskPriority.id);
      if (index != -1) {
        _allPriorities[index] = taskPriority;
        _applyFilters();
        debugPrint('تم تحديث أولوية المهمة بنجاح');
        return true;
      }
      _error.value = 'فشل في تحديث أولوية المهمة';
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في تحديث أولوية المهمة: $e';
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deleteTaskPriority(int id) async {
    _isLoading.value = true;
    _error.value = '';
    _errorMessage.value = '';

    try {
      _allPriorities.removeWhere((p) => p.id == id);
      _applyFilters();
      debugPrint('تم حذف أولوية المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف أولوية المهمة: $e';
      _errorMessage.value = 'خطأ في حذف أولوية المهمة: $e';
      debugPrint('خطأ في حذف أولوية المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة (الطريقة القديمة للتوافق)
  Future<bool> deletePriority(int id) async {
    return await deleteTaskPriority(id);
  }

  /// الحصول على الأولويات النشطة فقط
  List<task_enums.TaskPriority> get activePriorities {
    return _allPriorities.where((priority) => priority.isActive).cast<task_enums.TaskPriority>().toList();
  }

  /// الحصول على أولوية افتراضية
  TaskPriority? get defaultPriority {
    return _allPriorities.firstWhereOrNull((priority) => priority.isDefault);
  }

  /// ترتيب الأولويات حسب المستوى
  List<TaskPriority> get prioritiesByLevel {
    var sorted = List<TaskPriority>.from(_allPriorities);
    sorted.sort((a, b) => a.level.compareTo(b.level));
    return sorted;
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPriorities.where((priority) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!priority.name.toLowerCase().contains(query) &&
            !(priority.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !priority.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredPriorities.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  // @override
  // Future<void> refresh() async {
  //   await loadAllPriorities();
  // }
}
