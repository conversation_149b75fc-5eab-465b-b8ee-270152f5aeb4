import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_history_models.dart';

/// مكون عرض تاريخ تحويلات المهمة
/// يعرض تاريخ تحويلات المهمة بشكل مرئي مع تفاصيل كل تحويل
class TaskTransferHistoryWidget extends StatelessWidget {
  final List<TaskHistory> transferHistory;
  final bool showTitle;
  final bool isCompact;

  const TaskTransferHistoryWidget({
    super.key,
    required this.transferHistory,
    this.showTitle = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    if (transferHistory.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              if (showTitle) ...[
                Row(
                  children: [
                    const Icon(Icons.history, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'تاريخ التحويلات',
                      style: AppStyles.headingMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              const Center(
                child: Text(
                  'لا توجد تحويلات لهذه المهمة',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Row(
                children: [
                  const Icon(Icons.history, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تاريخ التحويلات',
                    style: AppStyles.headingMedium,
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            SizedBox(
              height: isCompact ? 200 : 400,
              child: isCompact 
                  ? _buildCompactTransferHistory()
                  : _buildDetailedTransferHistory(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عرض مفصل لتاريخ التحويلات
  Widget _buildDetailedTransferHistory() {
    // ترتيب التحويلات من الأحدث إلى الأقدم
    final sortedHistory = List<TaskHistory>.from(transferHistory)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedHistory.length,
      itemBuilder: (context, index) {
        final transfer = sortedHistory[index];
        final isLast = index == sortedHistory.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // خط الزمن
            Column(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 60,
                    color: Colors.grey[300],
                  ),
              ],
            ),
            const SizedBox(width: 16),
            
            // محتوى التحويل
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات التحويل
                    Row(
                      children: [
                        const Icon(Icons.person, size: 16, color: AppColors.primary),
                        const SizedBox(width: 4),
                        Expanded(
                          child: FutureBuilder<String>(
                            future: _getUserName(transfer.userId.toString()),
                            builder: (context, snapshot) {
                              return Text(
                                'من: ${snapshot.data ?? 'غير معروف'}',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    FutureBuilder<String>(
                      future: _getNewAssigneeId(transfer).then((id) => _getUserName(id)),
                      builder: (context, snapshot) {
                        return Text(
                          'إلى: ${snapshot.data ?? 'مستخدم غير معروف'}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    
                    // التاريخ والوقت
                    Row(
                      children: [
                        const Icon(Icons.access_time, size: 14, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          _formatDateTime(DateTime.fromMillisecondsSinceEpoch(transfer.timestamp * 1000)),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),

                    // التعليق إذا كان موجوداً
                    if (_hasNote(transfer)) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.blue.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.comment,
                                  color: Colors.blue,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  'تعليق:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getNote(transfer),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    // المرفقات إذا كانت موجودة
                    if (_hasAttachments(transfer)) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.purple.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.purple.shade100),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.attach_file,
                                  color: Colors.purple,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                const Text(
                                  'المرفقات:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                    color: Colors.purple,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            _buildAttachmentsList(_getAttachments(transfer)),
                          ],
                        ),
                      ),
                    ],
                    
                    // عرض نسبة المساهمة إذا كانت موجودة
                    if (_hasContribution(transfer)) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.amber.shade100),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'المساهمة: ${_getContributionPercentage(transfer)}%',
                              style: TextStyle(
                                color: Colors.amber.shade800,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء قائمة المرفقات
  Widget _buildAttachmentsList(List<String> attachments) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (var attachment in attachments)
          Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.purple.shade100.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.purple.shade200),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getFileIcon(attachment),
                  size: 14,
                  color: Colors.purple,
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    attachment,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.purple,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
  
  /// الحصول على أيقونة الملف بناءً على نوعه
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
        return Icons.folder_zip;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// بناء عرض مختصر لتاريخ التحويلات
  Widget _buildCompactTransferHistory() {
    // ترتيب التحويلات من الأحدث إلى الأقدم
    final sortedHistory = List<TaskHistory>.from(transferHistory)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedHistory.length,
      itemBuilder: (context, index) {
        final transfer = sortedHistory[index];

        return ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppColors.primary,
            child: Icon(Icons.send, color: Colors.white, size: 16),
          ),
          title: Row(
            children: [
              FutureBuilder<String>(
                future: _getUserName(transfer.userId.toString()),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data ?? 'مستخدم غير معروف',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
              const Text(' ← '),
              FutureBuilder<String>(
                future: _getNewAssigneeId(transfer).then((id) => _getUserName(id)),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data ?? 'مستخدم غير معروف',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
            ],
          ),
          subtitle: Text(
            _formatDateTime(DateTime.fromMillisecondsSinceEpoch(transfer.timestamp * 1000)),
            style: const TextStyle(fontSize: 12),
          ),
          trailing: _hasNote(transfer)
              ? const Icon(Icons.comment, size: 16, color: Colors.grey)
              : null,
        );
      },
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// الحصول على اسم المستخدم
  Future<String> _getUserName(String userId) async {
    try {
      final userController = Get.find<UserController>();
      return await userController.getUserNameById(userId);
    } catch (e) {
      return 'مستخدم غير معروف';
    }
  }

  /// دوال مساعدة للتعامل مع details
  Future<String> _getNewAssigneeId(TaskHistory history) async {
    if (history.details == null) {
      // استخدام newValue كبديل إذا كان متوفراً
      return history.newValue ?? 'غير محدد';
    }

    try {
      // محاولة تحليل details كـ JSON
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final newAssigneeId = detailsMap['newAssigneeId']?.toString();

        // التحقق من صحة المعرف
        if (newAssigneeId != null && newAssigneeId != '0' && newAssigneeId.isNotEmpty) {
          return newAssigneeId;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل details: $e');
    }

    // استخدام newValue كبديل
    if (history.newValue != null && history.newValue != '0' && history.newValue!.isNotEmpty) {
      return history.newValue!;
    }

    return 'غير محدد';
  }

  bool _hasNote(TaskHistory history) {
    if (history.details == null) return false;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final note = detailsMap['note']?.toString();
        return note != null && note.isNotEmpty;
      }
    } catch (e) {
      debugPrint('خطأ في تحليل الملاحظة: $e');
    }
    return false;
  }

  String _getNote(TaskHistory history) {
    if (history.details == null) return '';

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        return detailsMap['note']?.toString() ?? '';
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على الملاحظة: $e');
    }
    return '';
  }
  
  /// التحقق من وجود مرفقات
  bool _hasAttachments(TaskHistory history) {
    if (history.details == null) return false;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        final attachments = detailsMap['attachments']?.toString() ?? '';
        return attachments.isNotEmpty && attachments != '0';
      }
    } catch (e) {
      debugPrint('خطأ في تحليل المرفقات: $e');
    }
    return false;
  }
  
  /// الحصول على قائمة المرفقات
  List<String> _getAttachments(TaskHistory history) {
    if (history.details == null) return [];

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = Map<String, dynamic>.from(
          Uri.splitQueryString(history.details!)
        );
        final attachmentsStr = detailsMap['attachments']?.toString() ?? '';
        if (attachmentsStr.isEmpty) return [];
        
        return attachmentsStr.split(',').where((s) => s.isNotEmpty).toList();
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
    return [];
  }
  
  /// التحقق من وجود مساهمة مسجلة
  bool _hasContribution(TaskHistory history) {
    if (history.details == null) return false;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = json.decode(history.details!) as Map<String, dynamic>;
        return detailsMap.containsKey('contributionRecorded') &&
               detailsMap['contributionRecorded'] == 'true' &&
               detailsMap.containsKey('contributionPercentage');
      }
    } catch (e) {
      debugPrint('خطأ في تحليل المساهمة: $e');
    }
    return false;
  }
  
  /// الحصول على نسبة المساهمة
  double _getContributionPercentage(TaskHistory history) {
    if (history.details == null) return 0.0;

    try {
      if (history.details!.startsWith('{')) {
        final detailsMap = Map<String, dynamic>.from(
          Uri.splitQueryString(history.details!)
        );
        if (detailsMap.containsKey('contributionPercentage')) {
          return double.tryParse(detailsMap['contributionPercentage'].toString()) ?? 0.0;
        }
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
    return 0.0;
  }
}
