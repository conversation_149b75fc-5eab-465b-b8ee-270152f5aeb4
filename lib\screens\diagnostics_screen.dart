import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/task_controller.dart';
import '../controllers/auth_controller.dart';
import '../utils/task_troubleshooter.dart';

class DiagnosticsScreen extends StatefulWidget {
  const DiagnosticsScreen({super.key});

  @override
  State<DiagnosticsScreen> createState() => _DiagnosticsScreenState();
}

class _DiagnosticsScreenState extends State<DiagnosticsScreen> {
  bool _isRunningDiagnostics = false;
  bool _isFixing = false;
  Map<String, dynamic>? _diagnosticsResults;
  Map<String, dynamic>? _fixResults;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص المشاكل'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeaderCard(),
            const SizedBox(height: 16),
            _buildQuickActionsCard(),
            const SizedBox(height: 16),
            if (_diagnosticsResults != null) _buildDiagnosticsResultsCard(),
            if (_fixResults != null) _buildFixResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_services, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                const Text(
                  'أداة تشخيص المشاكل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'هذه الأداة تساعد في تشخيص وحل مشاكل تحميل المهام والاتصال بالخادم.',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجراءات السريعة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRunningDiagnostics ? null : _runDiagnostics,
                    icon: _isRunningDiagnostics
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(_isRunningDiagnostics ? 'جاري التشخيص...' : 'تشخيص المشاكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isFixing ? null : _attemptFix,
                    icon: _isFixing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.build),
                    label: Text(_isFixing ? 'جاري الإصلاح...' : 'إصلاح تلقائي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _refreshTasks,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة تحميل المهام'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade700,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticsResultsCard() {
    final analysis = _diagnosticsResults!['analysis'] as Map<String, dynamic>?;
    final hasIssues = analysis?['hasIssues'] ?? false;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasIssues ? Icons.warning : Icons.check_circle,
                  color: hasIssues ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نتائج التشخيص',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (analysis != null) ...[
              _buildStatusRow('الحالة العامة', hasIssues ? 'يوجد مشاكل' : 'سليم', hasIssues),
              _buildStatusRow('مستوى الخطورة', analysis['severity'] ?? 'غير محدد', hasIssues),
              
              const SizedBox(height: 16),
              
              if (analysis['recommendations'] != null && 
                  (analysis['recommendations'] as List).isNotEmpty) ...[
                const Text(
                  'التوصيات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...((analysis['recommendations'] as List).map((rec) => 
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• ', style: TextStyle(color: Colors.orange)),
                        Expanded(child: Text(rec.toString())),
                      ],
                    ),
                  ),
                )),
              ],
            ],
            
            const SizedBox(height: 16),
            _buildDetailedResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildFixResultsCard() {
    final success = _fixResults!['success'] ?? false;
    final fixedIssues = _fixResults!['fixedIssues'] as List<dynamic>? ?? [];
    final failedFixes = _fixResults!['failedFixes'] as List<dynamic>? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: success ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نتائج الإصلاح',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (fixedIssues.isNotEmpty) ...[
              const Text(
                'تم إصلاحها:',
                style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
              ),
              const SizedBox(height: 8),
              ...fixedIssues.map((issue) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.check, color: Colors.green, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(issue.toString())),
                    ],
                  ),
                ),
              ),
            ],
            
            if (failedFixes.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'فشل في الإصلاح:',
                style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
              ),
              const SizedBox(height: 8),
              ...failedFixes.map((issue) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(issue.toString())),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool hasIssue) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: hasIssue ? Colors.orange.shade100 : Colors.green.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: hasIssue ? Colors.orange.shade800 : Colors.green.shade800,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedResults() {
    return ExpansionTile(
      title: const Text('تفاصيل التشخيص'),
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _diagnosticsResults.toString(),
            style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
      ],
    );
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunningDiagnostics = true;
      _diagnosticsResults = null;
    });

    try {
      final results = await TaskTroubleshooter.diagnoseTasksIssues();
      TaskTroubleshooter.printDiagnosisReport(results);
      
      setState(() {
        _diagnosticsResults = results;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إكمال التشخيص'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التشخيص: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRunningDiagnostics = false;
      });
    }
  }

  Future<void> _attemptFix() async {
    setState(() {
      _isFixing = true;
      _fixResults = null;
    });

    try {
      final results = await TaskTroubleshooter.attemptAutoFix();
      
      setState(() {
        _fixResults = results;
      });
      
      final success = results['success'] ?? false;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم الإصلاح بنجاح' : 'فشل في بعض عمليات الإصلاح'),
          backgroundColor: success ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الإصلاح: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  Future<void> _refreshTasks() async {
    try {
      if (Get.isRegistered<TaskController>()) {
        final taskController = Get.find<TaskController>();
        final authController = Get.find<AuthController>();

        // استخدام الدالة الموحدة بدلاً من loadAllTasks
        if (authController.currentUser.value != null) {
          await taskController.loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إعادة تحميل المهام'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة التحميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
