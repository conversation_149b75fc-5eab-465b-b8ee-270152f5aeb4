// نماذج قراءة الرسائل - متطابقة مع ASP.NET Core API

/// نموذج قراءة الرسالة الأساسي
class MessageRead {
  final int id;
  final int messageId;
  final int userId;
  final int readAt;

  const MessageRead({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.readAt,
  });

  factory MessageRead.fromJson(Map<String, dynamic> json) {
    return MessageRead(
      id: json['id'] ?? 0,
      messageId: json['messageId'] ?? 0,
      userId: json['userId'] ?? 0,
      readAt: json['readAt'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'readAt': readAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageRead &&
        other.id == id &&
        other.messageId == messageId &&
        other.userId == userId &&
        other.readAt == readAt;
  }

  @override
  int get hashCode {
    return Object.hash(id, messageId, userId, readAt);
  }

  @override
  String toString() {
    return 'MessageRead(id: $id, messageId: $messageId, userId: $userId, readAt: $readAt)';
  }

  /// نسخ مع تعديل بعض الخصائص
  MessageRead copyWith({
    int? id,
    int? messageId,
    int? userId,
    int? readAt,
  }) {
    return MessageRead(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      userId: userId ?? this.userId,
      readAt: readAt ?? this.readAt,
    );
  }
}

/// نموذج قراءة الرسالة مع معلومات المستخدم
class MessageReadWithUser {
  final int id;
  final int messageId;
  final int userId;
  final String userName;
  final String userEmail;
  final String? userImageUrl;
  final int readAt;

  const MessageReadWithUser({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.userName,
    required this.userEmail,
    this.userImageUrl,
    required this.readAt,
  });

  factory MessageReadWithUser.fromJson(Map<String, dynamic> json) {
    return MessageReadWithUser(
      id: json['id'] ?? 0,
      messageId: json['messageId'] ?? 0,
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? 'غير معروف',
      userEmail: json['userEmail'] ?? 'غير معروف',
      userImageUrl: json['userImageUrl'],
      readAt: json['readAt'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'userImageUrl': userImageUrl,
      'readAt': readAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReadWithUser &&
        other.id == id &&
        other.messageId == messageId &&
        other.userId == userId &&
        other.userName == userName &&
        other.userEmail == userEmail &&
        other.userImageUrl == userImageUrl &&
        other.readAt == readAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      messageId,
      userId,
      userName,
      userEmail,
      userImageUrl,
      readAt,
    );
  }

  @override
  String toString() {
    return 'MessageReadWithUser(id: $id, messageId: $messageId, userId: $userId, userName: $userName, userEmail: $userEmail, readAt: $readAt)';
  }

  /// تحويل إلى MessageRead أساسي
  MessageRead toMessageRead() {
    return MessageRead(
      id: id,
      messageId: messageId,
      userId: userId,
      readAt: readAt,
    );
  }
}

/// نموذج إحصائيات قراءة الرسائل
class MessageReadStatistics {
  final int totalReads;
  final int totalMessages;
  final int totalUnreadMessages;
  final double readPercentage;

  const MessageReadStatistics({
    required this.totalReads,
    required this.totalMessages,
    required this.totalUnreadMessages,
    required this.readPercentage,
  });

  factory MessageReadStatistics.fromJson(Map<String, dynamic> json) {
    return MessageReadStatistics(
      totalReads: json['totalReads'] ?? 0,
      totalMessages: json['totalMessages'] ?? 0,
      totalUnreadMessages: json['totalUnreadMessages'] ?? 0,
      readPercentage: (json['readPercentage'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalReads': totalReads,
      'totalMessages': totalMessages,
      'totalUnreadMessages': totalUnreadMessages,
      'readPercentage': readPercentage,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReadStatistics &&
        other.totalReads == totalReads &&
        other.totalMessages == totalMessages &&
        other.totalUnreadMessages == totalUnreadMessages &&
        other.readPercentage == readPercentage;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalReads,
      totalMessages,
      totalUnreadMessages,
      readPercentage,
    );
  }

  @override
  String toString() {
    return 'MessageReadStatistics(totalReads: $totalReads, totalMessages: $totalMessages, totalUnreadMessages: $totalUnreadMessages, readPercentage: $readPercentage%)';
  }

  /// نسبة القراءة كنص
  String get readPercentageText {
    return '${readPercentage.toStringAsFixed(1)}%';
  }

  /// هل معدل القراءة جيد؟
  bool get isGoodReadRate {
    return readPercentage >= 70.0;
  }

  /// هل معدل القراءة ممتاز؟
  bool get isExcellentReadRate {
    return readPercentage >= 90.0;
  }
}

/// نموذج طلب تحديد رسالة كمقروءة
class MarkAsReadRequest {
  final int messageId;
  final int userId;

  const MarkAsReadRequest({
    required this.messageId,
    required this.userId,
  });

  factory MarkAsReadRequest.fromJson(Map<String, dynamic> json) {
    return MarkAsReadRequest(
      messageId: json['messageId'] ?? 0,
      userId: json['userId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'userId': userId,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkAsReadRequest &&
        other.messageId == messageId &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    return Object.hash(messageId, userId);
  }

  @override
  String toString() {
    return 'MarkAsReadRequest(messageId: $messageId, userId: $userId)';
  }
}
