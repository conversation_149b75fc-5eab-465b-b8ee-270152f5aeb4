import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_message_models.dart';
import '../services/api/task_messages_api_service.dart';
// import '../services/signalr_service.dart'; // معلق مؤقتاً
import '../services/unified_signalr_service.dart'; // الخدمة الموحدة الجديدة
import '../controllers/auth_controller.dart';

/// متحكم رسائل المهام - للمحادثات الفورية المرتبطة بالمهام
class TaskMessagesController extends GetxController {
  final TaskMessagesApiService _apiService = TaskMessagesApiService();

  // قوائم الرسائل
  final RxMap<int, RxList<TaskMessageResponse>> _taskMessages = 
      <int, RxList<TaskMessageResponse>>{}.obs;
  final RxList<TaskMessageResponse> _pinnedMessages = <TaskMessageResponse>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isSending = false.obs;
  final RxString _error = ''.obs;

  // حالة الكتابة
  final RxMap<int, RxList<String>> _typingUsers = <int, RxList<String>>{}.obs;

  // الرسالة المحددة للرد
  final Rx<TaskMessageResponse?> _replyToMessage = Rx<TaskMessageResponse?>(null);

  // متغير لتتبع ScrollController من الواجهة
  ScrollController? _scrollController;

  /// تعيين ScrollController من الواجهة
  void setScrollController(ScrollController? controller) {
    _scrollController = controller;
  }

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isSending => _isSending.value;
  String get error => _error.value;
  List<TaskMessageResponse> get pinnedMessages => _pinnedMessages;
  TaskMessageResponse? get replyToMessage => _replyToMessage.value;

  /// الحصول على رسائل مهمة محددة
  RxList<TaskMessageResponse> getTaskMessages(int taskId) {
    if (!_taskMessages.containsKey(taskId)) {
      _taskMessages[taskId] = <TaskMessageResponse>[].obs;
    }
    return _taskMessages[taskId]!;
  }

  /// الحصول على المستخدمين الذين يكتبون في مهمة محددة
  List<String> getTypingUsers(int taskId) {
    return _typingUsers[taskId]?.toList() ?? [];
  }

  Timer? _signalRTimer;

  @override
  void onInit() {
    super.onInit();
    _setupSignalRListeners();
  }

  @override
  void onClose() {
    _signalRTimer?.cancel();
    _taskMessages.clear();
    _pinnedMessages.clear();
    super.onClose();
  }

  /// إعداد مستمعي SignalR للتحديثات الفورية
  void _setupSignalRListeners() {
    try {
      // استخدام الخدمة الموحدة الجديدة
      final signalR = Get.find<UnifiedSignalRService>();

      // إلغاء المؤقت السابق إذا كان موجوداً
      _signalRTimer?.cancel();

      // مراقبة الرسائل الجديدة فورياً مع تحديث أقل تكراراً (كل 3 ثوانٍ بدلاً من 500ms)
      _signalRTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        try {
          // التحقق من أن الخدمة ما زالت متاحة
          if (!Get.isRegistered<TaskMessagesController>()) {
            timer.cancel();
            return;
          }

          final messages = signalR.newTaskMessages;
          if (messages.isNotEmpty) {
            for (final messageData in messages) {
              try {
                final message = TaskMessageResponse.fromJson(messageData);
                _addMessageToTask(message.taskId, message);
                // تحديث الواجهة فوراً
                update();
                // التمرير التلقائي إلى أسفل عند وصول رسالة جديدة
                _scrollToBottomAfterNewMessage();
                debugPrint('📨 تم استقبال رسالة جديدة فورياً: \\${message.id}');
              } catch (e) {
                debugPrint('خطأ في تحويل رسالة المهمة: \\${e.toString()}');
              }
            }

            // مسح الرسائل بعد المعالجة
            signalR.clearEvents();
          }
        } catch (e) {
          if (e is SocketException) {
            debugPrint('تم إغلاق الاتصال بـ SignalR (SocketException): \\${e.message}');
            timer.cancel();
          } else {
            debugPrint('خطأ في معالجة رسائل SignalR: \\${e.toString()}');
          }
        }
      });

      debugPrint('تم إعداد مستمعي SignalR لرسائل المهام بنجاح');

    } catch (e) {
      debugPrint('خطأ في إعداد مستمعي SignalR: \\${e.toString()}');
    }
  }

  /// تحميل رسائل مهمة محددة
  Future<void> loadTaskMessages(int taskId, {int page = 1, int pageSize = 50}) async {
    try {
      if (page == 1) {
        _isLoading.value = true;
        _error.value = '';
      }

      final messages = await _apiService.getTaskMessages(
        taskId,
        page: page,
        pageSize: pageSize,
      );

      if (page == 1) {
        // استبدال الرسائل الموجودة
        if (!_taskMessages.containsKey(taskId)) {
          _taskMessages[taskId] = <TaskMessageResponse>[].obs;
        }
        _taskMessages[taskId]!.assignAll(messages);
      } else {
        // إضافة رسائل جديدة (للتصفح)
        if (_taskMessages.containsKey(taskId)) {
          _taskMessages[taskId]!.addAll(messages);
        }
      }

      debugPrint('تم تحميل ${messages.length} رسالة للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل الرسائل: $e';
      debugPrint('خطأ في تحميل رسائل المهمة $taskId: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إرسال رسالة جديدة
  Future<bool> sendMessage({
    required int taskId,
    required String content,
    int contentType = 1,
    int? replyToMessageId,
    int priority = 0,
    List<int>? mentionedUserIds,
    List<int>? attachmentIds,
  }) async {
    try {
      _isSending.value = true;
      _error.value = '';

      final request = SendTaskMessageRequest(
        taskId: taskId,
        content: content,
        contentType: contentType,
        replyToMessageId: replyToMessageId,
        priority: priority,
        mentionedUserIds: mentionedUserIds,
        attachmentIds: attachmentIds,
      );

      final message = await _apiService.sendMessage(request);
      
      // إضافة الرسالة محلياً (ستصل أيضاً عبر SignalR)
      _addMessageToTask(taskId, message);

      // مسح الرد المحدد
      _replyToMessage.value = null;

      debugPrint('تم إرسال رسالة للمهمة $taskId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إرسال الرسالة: $e';
      debugPrint('خطأ في إرسال رسالة للمهمة $taskId: $e');
      return false;
    } finally {
      _isSending.value = false;
    }
  }

  /// تحديث رسالة موجودة
  Future<bool> updateMessage(int messageId, String newContent) async {
    try {
      final request = UpdateTaskMessageRequest(content: newContent);
      final updatedMessage = await _apiService.updateMessage(messageId, request);
      
      // تحديث الرسالة محلياً
      _updateMessageInTask(updatedMessage.taskId, updatedMessage);
      
      debugPrint('تم تحديث الرسالة $messageId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الرسالة: $e';
      debugPrint('خطأ في تحديث الرسالة $messageId: $e');
      return false;
    }
  }

  /// حذف رسالة
  Future<bool> deleteMessage(int messageId) async {
    try {
      final success = await _apiService.deleteMessage(messageId);
      if (success) {
        _removeMessageFromAllTasks(messageId);
        debugPrint('تم حذف الرسالة $messageId');
      }
      return success;
    } catch (e) {
      _error.value = 'خطأ في حذف الرسالة: $e';
      debugPrint('خطأ في حذف الرسالة $messageId: $e');
      return false;
    }
  }

  /// تثبيت أو إلغاء تثبيت رسالة
  Future<bool> pinMessage(int messageId, bool isPinned) async {
    try {
      final updatedMessage = await _apiService.pinMessage(messageId, isPinned);
      
      // تحديث الرسالة محلياً
      _updateMessageInTask(updatedMessage.taskId, updatedMessage);
      
      // تحديث قائمة الرسائل المثبتة
      if (isPinned) {
        _addToPinnedMessages(updatedMessage);
      } else {
        _removeFromPinnedMessages(messageId);
      }
      
      debugPrint('تم ${isPinned ? 'تثبيت' : 'إلغاء تثبيت'} الرسالة $messageId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تثبيت الرسالة: $e';
      debugPrint('خطأ في تثبيت الرسالة $messageId: $e');
      return false;
    }
  }

  /// تحديد رسالة للمتابعة
  Future<bool> markForFollowUp(int messageId, bool isMarkedForFollowUp, {int? followUpAt}) async {
    try {
      final updatedMessage = await _apiService.markForFollowUp(
        messageId,
        isMarkedForFollowUp,
        followUpAt: followUpAt,
      );

      // تحديث الرسالة محلياً
      _updateMessageInTask(updatedMessage.taskId, updatedMessage);

      debugPrint('تم ${isMarkedForFollowUp ? 'تحديد' : 'إلغاء تحديد'} الرسالة $messageId للمتابعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديد الرسالة للمتابعة: $e';
      debugPrint('خطأ في تحديد الرسالة للمتابعة $messageId: $e');
      return false;
    }
  }

  /// تحديد رسالة كمقروءة
  Future<bool> markAsRead(int messageId) async {
    try {
      final success = await _apiService.markAsRead(messageId);
      if (success) {
        final currentUserId = Get.find<AuthController>().currentUser.value?.id;
        if (currentUserId != null) {
          _updateMessageReadStatus(messageId, currentUserId);
        }
        debugPrint('تم تحديد الرسالة $messageId كمقروءة');
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في تحديد الرسالة كمقروءة $messageId: $e');
      return false;
    }
  }

  /// تحميل الرسائل المثبتة لمهمة
  Future<void> loadPinnedMessages(int taskId) async {
    try {
      final messages = await _apiService.getPinnedMessages(taskId);
      _pinnedMessages.assignAll(messages);
      debugPrint('تم تحميل ${messages.length} رسالة مثبتة للمهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في تحميل الرسائل المثبتة للمهمة $taskId: $e');
    }
  }

  /// تحديد رسالة للرد عليها
  void setReplyToMessage(TaskMessageResponse? message) {
    _replyToMessage.value = message;
  }

  /// مسح الرد المحدد
  void clearReplyToMessage() {
    _replyToMessage.value = null;
  }

  /// الانضمام لمحادثة مهمة عبر SignalR
  Future<void> joinTaskConversation(int taskId) async {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      await signalR.joinTaskGroup(taskId.toString());
      debugPrint('تم الانضمام لمحادثة المهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في الانضمام لمحادثة المهمة $taskId: $e');
    }
  }

  /// مغادرة محادثة مهمة عبر SignalR
  Future<void> leaveTaskConversation(int taskId) async {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      await signalR.leaveTaskGroup(taskId.toString());
      debugPrint('تم مغادرة محادثة المهمة $taskId');
    } catch (e) {
      debugPrint('خطأ في مغادرة محادثة المهمة $taskId: $e');
    }
  }

  /// إرسال إشعار الكتابة
  Future<void> sendTypingIndicator(int taskId, String userName) async {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      await signalR.sendTypingIndicator(taskId.toString(), userName);
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار الكتابة: $e');
    }
  }

  /// إرسال إشعار توقف الكتابة
  Future<void> sendStoppedTypingIndicator(int taskId, String userName) async {
    try {
      final signalR = Get.find<UnifiedSignalRService>();
      await signalR.sendStoppedTypingIndicator(taskId.toString(), userName);
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار توقف الكتابة: $e');
    }
  }

  // الوظائف المساعدة الخاصة

  /// إضافة رسالة لمهمة محددة
  void _addMessageToTask(int taskId, TaskMessageResponse message) {
    if (!_taskMessages.containsKey(taskId)) {
      _taskMessages[taskId] = <TaskMessageResponse>[].obs;
    }

    // التحقق من عدم وجود الرسالة مسبقاً
    final existingIndex = _taskMessages[taskId]!.indexWhere((m) => m.id == message.id);
    if (existingIndex == -1) {
      // إضافة الرسالة في المكان المناسب حسب التاريخ
      final messages = _taskMessages[taskId]!;
      int insertIndex = 0;
      for (int i = 0; i < messages.length; i++) {
        if (messages[i].createdAt < message.createdAt) {
          insertIndex = i;
          break;
        }
        insertIndex = i + 1;
      }
      messages.insert(insertIndex, message);
    }
  }

  /// تحديث رسالة في مهمة محددة
  void _updateMessageInTask(int taskId, TaskMessageResponse updatedMessage) {
    if (_taskMessages.containsKey(taskId)) {
      final messages = _taskMessages[taskId]!;
      final index = messages.indexWhere((m) => m.id == updatedMessage.id);
      if (index != -1) {
        messages[index] = updatedMessage;
      }
    }
  }

  /// إزالة رسالة من جميع المهام
  void _removeMessageFromAllTasks(int messageId) {
    for (final taskMessages in _taskMessages.values) {
      taskMessages.removeWhere((m) => m.id == messageId);
    }
    _removeFromPinnedMessages(messageId);
  }

  /// إضافة رسالة للرسائل المثبتة
  void _addToPinnedMessages(TaskMessageResponse message) {
    final existingIndex = _pinnedMessages.indexWhere((m) => m.id == message.id);
    if (existingIndex == -1) {
      _pinnedMessages.add(message);
    } else {
      _pinnedMessages[existingIndex] = message;
    }
  }

  /// إزالة رسالة من الرسائل المثبتة
  void _removeFromPinnedMessages(int messageId) {
    _pinnedMessages.removeWhere((m) => m.id == messageId);
  }

  /// تحديث حالة قراءة رسالة
  void _updateMessageReadStatus(int messageId, int userId) {
    for (final taskMessages in _taskMessages.values) {
      final messageIndex = taskMessages.indexWhere((m) => m.id == messageId);
      if (messageIndex != -1) {
        // هنا يمكن تحديث عداد القراءة إذا كان متاحاً في النموذج
        // أو إرسال إشعار للواجهة
        break;
      }
    }
  }

  /// إضافة مستخدم للقائمة الكتابة
  void addTypingUser(int taskId, String userName) {
    if (!_typingUsers.containsKey(taskId)) {
      _typingUsers[taskId] = <String>[].obs;
    }
    if (!_typingUsers[taskId]!.contains(userName)) {
      _typingUsers[taskId]!.add(userName);
    }
    update(); // تحديث الواجهة فوراً
  }

  /// إزالة مستخدم من قائمة الكتابة
  void removeTypingUser(int taskId, String userName) {
    if (_typingUsers.containsKey(taskId)) {
      _typingUsers[taskId]!.remove(userName);
    }
    update(); // تحديث الواجهة فوراً
  }

  /// مسح جميع البيانات
  void clearAllData() {
    _taskMessages.clear();
    _pinnedMessages.clear();
    _typingUsers.clear();
    _replyToMessage.value = null;
    _error.value = '';
  }

  /// التمرير التلقائي إلى أسفل عند وصول رسالة جديدة
  /// ملاحظة: محادثات المهام تستخدم reverse: true، لذا نحتاج للتمرير إلى 0
  void _scrollToBottomAfterNewMessage() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      debugPrint('ScrollController غير متاح للتمرير التلقائي في محادثات المهام');
      return;
    }

    // تأخير قصير للتأكد من اكتمال تحديث القائمة
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController != null && _scrollController!.hasClients) {
        try {
          // التمرير إلى أعلى القائمة (0) لأن reverse: true
          _scrollController!.animateTo(
            0.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
          debugPrint('تم التمرير التلقائي إلى أسفل بعد وصول رسالة مهمة جديدة');
        } catch (e) {
          debugPrint('خطأ في التمرير التلقائي لمحادثات المهام: $e');
          // محاولة بديلة باستخدام jumpTo
          try {
            _scrollController!.jumpTo(0.0);
          } catch (e2) {
            debugPrint('خطأ في التمرير البديل لمحادثات المهام: $e2');
          }
        }
      }
    });
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    // إعادة تحميل جميع الرسائل
    for (final taskId in _taskMessages.keys) {
      await loadTaskMessages(taskId);
    }
  }
}
