import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../controllers/auth_controller.dart';

/// خدمة لوحة المعلومات الموحدة
class DashboardService extends GetxService {
  // المتحكمات
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  late final AuthController _authController;

  // الحالة التفاعلية
  final Rx<DashboardStateModel> _state = const DashboardStateModel(
    state: DashboardState.loading,
  ).obs;

  final Rx<FilterOptions> _currentFilters = FilterOptions().obs;

  // Getters
  DashboardStateModel get state => _state.value;
  List<DashboardItem> get items => _state.value.items;
  FilterOptions get currentFilters => _currentFilters.value;
  bool get isLoading => _state.value.state == DashboardState.loading;
  bool get hasError => _state.value.state == DashboardState.error;
  bool get isEmpty => _state.value.state == DashboardState.empty;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    loadDashboard();
  }

  /// تهيئة المتحكمات
  void _initializeControllers() {
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();
    _authController = Get.find<AuthController>();
  }

  /// تحميل لوحة المعلومات
  Future<void> loadDashboard() async {
    try {
      _updateState(state.copyWith(state: DashboardState.loading));
      
      // تحميل البيانات الأساسية
      await _loadBasicData();
      
      // إنشاء العناصر الافتراضية
      final items = _createDefaultItems();
      
      if (items.isEmpty) {
        _updateState(state.copyWith(state: DashboardState.empty));
      } else {
        _updateState(state.copyWith(
          state: DashboardState.loaded,
          items: items,
        ));
      }
    } catch (e) {
      debugPrint('خطأ في تحميل لوحة المعلومات: $e');
      _updateState(state.copyWith(
        state: DashboardState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// تحميل البيانات الأساسية
  Future<void> _loadBasicData() async {
    final userId = _authController.currentUser.value?.id;
    if (userId == null) throw Exception('لم يتم العثور على معرف المستخدم');

    await Future.wait([
      _taskController.loadTasksByUserPermissions(userId),
      _userController.loadAllUsers(),
      _departmentController.loadAllDepartments(),
    ]);
  }

  /// إنشاء العناصر الافتراضية
  List<DashboardItem> _createDefaultItems() {
    final now = DateTime.now();
    
    return [
      DashboardItem(
        id: 'task_status',
        title: 'توزيع المهام حسب الحالة',
        chartType: ChartType.pie,
        createdAt: now,
        updatedAt: now,
      ),
      DashboardItem(
        id: 'monthly_tasks',
        title: 'المهام الشهرية',
        chartType: ChartType.bar,
        createdAt: now,
        updatedAt: now,
      ),
      DashboardItem(
        id: 'task_completion',
        title: 'معدل إكمال المهام',
        chartType: ChartType.gauge,
        createdAt: now,
        updatedAt: now,
      ),
      DashboardItem(
        id: 'user_performance',
        title: 'أداء المستخدمين',
        chartType: ChartType.bar,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// إعادة تحميل لوحة المعلومات
  Future<void> refreshDashboard() async {
    _updateState(state.copyWith(isRefreshing: true));
    await loadDashboard();
    _updateState(state.copyWith(isRefreshing: false));
  }

  /// تحديث الفلاتر
  void updateFilters(FilterOptions filters) {
    _currentFilters.value = filters;
    // إعادة تحميل البيانات مع الفلاتر الجديدة
    loadDashboard();
  }

  /// الحصول على بيانات المخطط
  Future<List<ChartData>> getChartData(String itemId, FilterOptions? filters) async {
    try {
      final filterOptions = filters ?? currentFilters;
      
      switch (itemId) {
        case 'task_status':
          return _getTaskStatusData(filterOptions);
        case 'monthly_tasks':
          return _getMonthlyTasksData(filterOptions);
        case 'task_completion':
          return _getTaskCompletionData(filterOptions);
        case 'user_performance':
          return _getUserPerformanceData(filterOptions);
        default:
          return [];
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المخطط $itemId: $e');
      return [];
    }
  }

  /// بيانات توزيع المهام حسب الحالة
  List<ChartData> _getTaskStatusData(FilterOptions filters) {
    final tasks = _getFilteredTasks(filters);
    final statusCounts = <String, int>{};

    for (final task in tasks) {
      final status = _getTaskStatusName(task.status);
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }

    return statusCounts.entries.map((entry) {
      return ChartData(
        label: entry.key,
        value: entry.value.toDouble(),
        color: _getStatusColor(entry.key),
      );
    }).toList();
  }

  /// بيانات المهام الشهرية
  List<ChartData> _getMonthlyTasksData(FilterOptions filters) {
    final tasks = _getFilteredTasks(filters);
    final monthlyCounts = <String, int>{};

    for (final task in tasks) {
      final month = '${task.createdAt.year}-${task.createdAt.month.toString().padLeft(2, '0')}';
      monthlyCounts[month] = (monthlyCounts[month] ?? 0) + 1;
    }

    return monthlyCounts.entries.map((entry) {
      return ChartData(
        label: entry.key,
        value: entry.value.toDouble(),
        color: Colors.blue,
      );
    }).toList();
  }

  /// بيانات معدل إكمال المهام
  List<ChartData> _getTaskCompletionData(FilterOptions filters) {
    final tasks = _getFilteredTasks(filters);
    final totalTasks = tasks.length;
    final completedTasks = tasks.where((task) => task.status == 'completed').length;
    
    final completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0;
    
    return [
      ChartData(
        label: 'معدل الإكمال',
        value: completionRate,
        color: Colors.green,
      ),
    ];
  }

  /// بيانات أداء المستخدمين
  List<ChartData> _getUserPerformanceData(FilterOptions filters) {
    final tasks = _getFilteredTasks(filters);
    final userCounts = <String, int>{};

    for (final task in tasks) {
      if (task.assigneeId != null) {
        final user = _userController.users.firstWhereOrNull(
          (u) => u.id == task.assigneeId,
        );
        if (user != null) {
          userCounts[user.name] = (userCounts[user.name] ?? 0) + 1;
        }
      }
    }

    return userCounts.entries.take(5).map((entry) {
      return ChartData(
        label: entry.key,
        value: entry.value.toDouble(),
        color: Colors.orange,
      );
    }).toList();
  }

  /// تصفية المهام حسب الفلاتر
  List<dynamic> _getFilteredTasks(FilterOptions filters) {
    var tasks = _taskController.allTasks.where((task) => !task.isDeleted);

    // تصفية حسب التاريخ
    if (filters.startDate != null) {
      tasks = tasks.where((task) => task.createdAtDateTime.isAfter(filters.startDate!));
    }
    if (filters.endDate != null) {
      tasks = tasks.where((task) => task.createdAtDateTime.isBefore(filters.endDate!));
    }

    // تصفية حسب القسم
    if (filters.departmentIds != null && filters.departmentIds!.isNotEmpty) {
      tasks = tasks.where((task) => 
        task.departmentId != null && 
        filters.departmentIds!.contains(task.departmentId.toString())
      );
    }

    // تصفية حسب المستخدم
    if (filters.userIds != null && filters.userIds!.isNotEmpty) {
      tasks = tasks.where((task) => 
        task.assigneeId != null && 
        filters.userIds!.contains(task.assigneeId.toString())
      );
    }

    return tasks.toList();
  }

  /// الحصول على اسم حالة المهمة
  String _getTaskStatusName(String status) {
    switch (status) {
      case 'pending': return 'معلقة';
      case 'in_progress': return 'قيد التنفيذ';
      case 'waiting_for_info': return 'في انتظار معلومات';
      case 'completed': return 'مكتملة';
      case 'cancelled': return 'ملغاة';
      default: return 'غير محدد';
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    switch (status) {
      case 'معلقة': return Colors.orange;
      case 'قيد التنفيذ': return Colors.blue;
      case 'في انتظار معلومات': return Colors.amber;
      case 'مكتملة': return Colors.green;
      case 'ملغاة': return Colors.red;
      default: return Colors.grey;
    }
  }

  /// تحديث الحالة
  void _updateState(DashboardStateModel newState) {
    _state.value = newState;
  }
}
