import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter_application_2/models/department_model.dart';
import 'package:flutter_application_2/services/api/departments_api_service.dart';
import 'package:flutter/services.dart';

/// دالة توليد تقرير PDF شامل للأقسام مع المستخدمين والمهام
/// تأكد من إضافة خطوط عربية مثل NotoNaskhArabic إلى assets/fonts وتسجيلها في pubspec.yaml
Future<pw.Document> generateDepartmentsPdfReport({
  List<int>? departmentIds,
  DateTime? fromDate,
  DateTime? toDate,
  List<String>? taskTypes, // أنواع المهام المطلوبة
  List<String>? taskStatuses, // حالات المهام المطلوبة
}) async {
  final doc = pw.Document();
  final departmentsService = DepartmentsApiService();

  // تحميل الخطوط العربية
  final fontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Regular.ttf');
  final boldFontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Bold.ttf');
  final arabicFont = pw.Font.ttf(fontData);
  final arabicFontBold = pw.Font.ttf(boldFontData);

  // تحميل خط لاتيني يدعم الرموز الشجرية

  // جلب شجرة الأقسام الكاملة
  List<Department> departments = await departmentsService.getDepartmentHierarchy();
  if (departmentIds != null && departmentIds.isNotEmpty) {
    departments = departments.where((d) => departmentIds.contains(d.id)).toList();
  }

  // دالة مساعدة لإرجاع الحرف الأبجدي حسب الفهرس
  String arabicLetter(int index) {
    const letters = [
      'أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح', 'ط', 'ي',
      'ك', 'ل', 'م', 'ن', 'س', 'ع', 'ف', 'ص', 'ق', 'ر',
      'ش', 'ت', 'ث', 'خ', 'ذ', 'ض', 'ظ', 'غ'
    ];
    return (index < letters.length) ? letters[index] : '؟';
  }

  // دالة recursive لطباعة القسم وأبنائه مع ترقيم واضح
  Future<List<pw.Widget>> buildDepartmentSection(Department dept, int level, {String numbering = ''}) async {
    final users = await departmentsService.getDepartmentEmployees(dept.id);
    final tasks = await departmentsService.getDepartmentTasks(dept.id);
    // تصفية المهام حسب التاريخ، النوع، والحالة
    final filteredTasks = tasks.where((t) {
      bool dateOk = true;
      if (fromDate != null && toDate != null) {
        final created = parseAnyDate(t['createdAt']) ?? DateTime.now();
        dateOk = !created.isBefore(fromDate) && !created.isAfter(toDate);
      }
      bool typeOk = taskTypes == null || taskTypes.isEmpty || (t['type'] != null && taskTypes.contains(t['type']));
      bool statusOk = taskStatuses == null || taskStatuses.isEmpty || (t['status'] != null && taskStatuses.contains(t['status']));
      return dateOk && typeOk && statusOk;
    }).toList();
    final widgets = <pw.Widget>[];

    // تدرج لوني للخلفية حسب المستوى
    final bgColors = [
      PdfColor.fromInt(0xFFF4E8E8), // أبيض
      PdfColor.fromInt(0xFFF1F8E9), // أخضر فاتح
      PdfColor.fromInt(0xFFE3F2FD), // أزرق فاتح
      PdfColor.fromInt(0xFFFFF9C4), // أصفر فاتح
      PdfColor.fromInt(0xFFFFEBEE), // وردي فاتح
    ];
    final bgColor = bgColors[level % bgColors.length];

    widgets.add(
      pw.Container(
        color: bgColor,
        margin: pw.EdgeInsets.only(right: 16.0 * level, top: 12, bottom: 4),
        padding: pw.EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        child: (level > 0)
            ? pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  if (numbering.isNotEmpty)
                    pw.Text('$numbering /  ', style: pw.TextStyle(font: arabicFontBold, fontSize: 15, color: PdfColors.blue900)),
                  pw.Text(
                    'القسم: ${dept.name}',
                    style: pw.TextStyle(font: arabicFontBold, fontSize: 18.0 - level * 1.5, color: PdfColors.blue900),
                  ),
                ],
              )
            : pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  if (numbering.isNotEmpty)
                    pw.Text(' /  $numbering ', style: pw.TextStyle(font: arabicFontBold, fontSize: 15, color: PdfColors.blue900)),
                  pw.Expanded(
                    child: pw.Text(
                      'القسم: ${dept.name}',
                      style: pw.TextStyle(font: arabicFontBold, fontSize: 18.0 - level * 1.5, color: PdfColors.blue900),
                    ),
                  ),
                ],
              ),
      ),
    );
    widgets.add(
      pw.Padding(
        padding: pw.EdgeInsets.only(right: 20.0 * level),
        child: pw.Text('الوصف: ${dept.description ?? '-'}', style: pw.TextStyle(font: arabicFont)),
      ),
    );
    widgets.add(
      pw.Padding(
        padding: pw.EdgeInsets.only(right: 20.0 * level),
        child: pw.Text('عدد المستخدمين: ${users.length}', style: pw.TextStyle(font: arabicFont)),
      ),
    );
    widgets.add(
      pw.Padding(
        padding: pw.EdgeInsets.only(right: 20.0 * level),
        child: pw.Text('عدد المهام: ${filteredTasks.length}', style: pw.TextStyle(font: arabicFont)),
      ),
    );
    widgets.add(
      pw.Padding(
        padding: pw.EdgeInsets.only(right: 20.0 * level, top: 4),
        child: pw.Text('قائمة المستخدمين:', style: pw.TextStyle(font: arabicFontBold)),
      ),
    );
    widgets.add(
      pw.Padding(
        padding: pw.EdgeInsets.only(right: 20.0 * level),
        child: pw.Align(
          alignment: pw.Alignment.centerRight,
          child: pw.Bullet(
            text: users.map((u) => u['name'] ?? '').join(', '),
            style: pw.TextStyle(font: arabicFont),
          ),
        ),
      ),
    );
    if (filteredTasks.isNotEmpty) {
      widgets.add(
        pw.Padding(
          padding: pw.EdgeInsets.only(right: 20.0 * level, top: 8),
          child: pw.Text('جدول المهام:', style: pw.TextStyle(font: arabicFontBold, fontSize: 16)),
        ),
      );
      widgets.add(
        pw.Padding(
          padding: pw.EdgeInsets.only(right: 20.0 * level),
          child: pw.Container(
            width: double.infinity,
            child: pw.Table(
              border: pw.TableBorder.all(color: PdfColor.fromInt(0xFFBDBDBD), width: 0.7),
              columnWidths: {
                0: pw.FlexColumnWidth(1.5), // تاريخ الاستحقاق
                1: pw.FlexColumnWidth(1.5), // تاريخ الإنشاء
                2: pw.FlexColumnWidth(1.2), // المكلف
                3: pw.FlexColumnWidth(1),   // الحالة
                4: pw.FlexColumnWidth(5),   // العنوان (أكبر)
                5: const pw.FixedColumnWidth(30), // رقم المهمة (أصغر)
                6: const pw.FixedColumnWidth(30), // م
              },
              children: [
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColor.fromInt(0xFFE0F7FA)),
                  children: [
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('تاريخ الاستحقاق', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('تاريخ الإنشاء', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('المكلف', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('الحالة', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('العنوان', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('رقم المهمة', style: pw.TextStyle(font: arabicFontBold))),
                    pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text('م', style: pw.TextStyle(font: arabicFontBold))),
                  ],
                ),
                ...filteredTasks.asMap().entries.map((entry) {
                  final i = entry.key;
                  final t = entry.value;
                  return pw.TableRow(
                    children: [
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text(_formatDateField(t['dueDate']), style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text(_formatDateField(t['createdAt']), style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text(
                        // المكلف: جرب assigneeName أو assignee أو userName أو اسم المستخدم من التاسك
                        extractAssigneeName(t),
                        style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text(
                        translateStatus(t['status']?.toString()),
                        style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text((t['title'] ?? '-').toString(), style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text((t['id'] ?? '-').toString(), style: pw.TextStyle(font: arabicFont))),
                      pw.Padding(padding: const pw.EdgeInsets.all(4), child: pw.Text((i + 1).toString(), style: pw.TextStyle(font: arabicFont), textAlign: pw.TextAlign.center)),
                    ],
                  );
                }),
              ],
            ),
          ),
        ),
      );
    }
    // ملخص احترافي وجذاب لأداء القسم
    if (filteredTasks.isNotEmpty) {
      final total = filteredTasks.length;
      final completed = filteredTasks.where((t) => (t['status'] == 'completed' || t['status'] == 'approved')).length;
      final delayed = filteredTasks.where((t) => (t['status'] == 'delayed')).length;
      final inProgress = filteredTasks.where((t) => (t['status'] == 'in_progress' || t['status'] == 'inProgress')).length;
      final percent = total > 0 ? ((completed / total) * 100).round() : 0;
      String note = '';
      String symbol = '';
      PdfColor bgColor = PdfColors.white;
      if (percent >= 80 && delayed == 0) {
        note = 'الأداء ممتاز، لا توجد مهام متأخرة.';
        symbol = '★';
        bgColor = PdfColors.lightGreen100;
      } else if (percent >= 60) {
        note = 'الأداء جيد، يفضل معالجة المهام المتأخرة.';
        symbol = '✓';
        bgColor = PdfColors.yellow100;
      } else {
        note = 'هناك حاجة لتحسين الأداء وتسريع إنجاز المهام.';
        symbol = '!';
        bgColor = PdfColors.red100;
      }
      widgets.add(
        pw.Container(
          margin: pw.EdgeInsets.only(right: 20.0 * level, top: 8, bottom: 8),
          padding: pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: bgColor,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.blueGrey, width: 0.7),
          ),
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(symbol, style: pw.TextStyle(font: arabicFontBold, fontSize: 18, color: PdfColors.blueGrey)),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('ملخص أداء القسم:', style: pw.TextStyle(font: arabicFontBold, fontSize: 13, color: PdfColors.blue900)),
                    pw.SizedBox(height: 2),
                    pw.Text('- عدد المهام: $total | المنجزة: $completed | المتأخرة: $delayed | قيد التنفيذ: $inProgress', style: pw.TextStyle(font: arabicFont, fontSize: 11)),
                    pw.Text('- نسبة الإنجاز: $percent%', style: pw.TextStyle(font: arabicFont, fontSize: 11)),
                    pw.Text('- $note', style: pw.TextStyle(font: arabicFont, fontSize: 11, color: PdfColors.indigo)),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
    // خط فاصل خفيف بين الأقسام
    widgets.add(pw.Divider(thickness: 0.5, color: PdfColor.fromInt(0xFFBDBDBD)));

    // عرض الأقسام الأبناء بشكل متداخل مع ترقيم أبجدي
    if (dept.children.isNotEmpty) {
      for (int i = 0; i < dept.children.length; i++) {
        final child = dept.children[i];
        String childNumbering = '';
        if (level == 0) {
          childNumbering = arabicLetter(i); // أبناء القسم الرئيسي أبجدي
        } else if (level == 1) {
          childNumbering = '$numbering-${arabicLetter(i)}'; // أبناء الأبناء: 1-أ، 1-ب ...
        } else {
          childNumbering = numbering + '-' + (i + 1).toString(); // مستويات أعمق: 1-أ-1 ...
        }
        widgets.addAll(await buildDepartmentSection(child, level + 1, numbering: childNumbering));
      }
    }
    return widgets;
  }

  // توليد نص العنوان بناءً على الفلتر
  String reportTitle = 'تقرير الأقسام ومهامها';
  if (fromDate != null && toDate != null) {
    reportTitle +=
        ' للفترة من ${fromDate.year}/${fromDate.month.toString().padLeft(2, '0')}/${fromDate.day.toString().padLeft(2, '0')}'
        ' إلى ${toDate.year}/${toDate.month.toString().padLeft(2, '0')}/${toDate.day.toString().padLeft(2, '0')}';
  } else if (fromDate != null) {
    reportTitle +=
        ' من ${fromDate.year}/${fromDate.month.toString().padLeft(2, '0')}/${fromDate.day.toString().padLeft(2, '0')}';
  } else if (toDate != null) {
    reportTitle +=
        ' حتى ${toDate.year}/${toDate.month.toString().padLeft(2, '0')}/${toDate.day.toString().padLeft(2, '0')}';
  }
  if (taskTypes != null && taskTypes.isNotEmpty) {
    reportTitle += ' | نوع المهمة: ${taskTypes.join(", ")}';
  }
  if (taskStatuses != null && taskStatuses.isNotEmpty) {
    reportTitle += ' | حالة المهام: ${taskStatuses.map(translateStatus).join(", ")}';
  }

  // بناء التقرير لكل قسم رئيسي
  for (int i = 0; i < departments.length; i++) {
    final dept = departments[i];
    final sectionWidgets = await buildDepartmentSection(dept, 0, numbering: (i + 1).toString());
    doc.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 20), // هامش جانبي 16
        build: (context) => [
          // إضافة عنوان التقرير في الأعلى
          pw.Center(
            child: pw.Text(
              reportTitle,
              style: pw.TextStyle(font: arabicFontBold, fontSize: 20, color: PdfColors.blue900),
            ),
          ),
          pw.SizedBox(height: 16),
          ...sectionWidgets,
        ],
      ),
    );
  }

  // يمكن إضافة صفحة ملخص لأفضل الأقسام أو إحصائيات عامة
  // ...

  return doc;
}

// دالة مساعدة لتحويل التاريخ من int أو String إلى نص منسق
String _formatDateField(dynamic value) {
  if (value == null) return '-';
  if (value is int) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(value * 1000);
      return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
    } catch (_) {
      return value.toString();
    }
  }
  if (value is String && value.length > 6 && int.tryParse(value) != null) {
    // إذا كان نصًا رقميًا
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(int.parse(value) * 1000);
      return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
    } catch (_) {
      return value;
    }
  }
  return value.toString();
}

// دالة مساعدة لتحويل أي قيمة إلى DateTime؟
DateTime? parseAnyDate(dynamic value) {
  if (value == null) return null;
  if (value is DateTime) return value;
  if (value is int) {
    // إذا كان timestamp بالثواني أو المللي ثانية
    if (value > 1000000000000) {
      // milliseconds
      return DateTime.fromMillisecondsSinceEpoch(value);
    } else {
      // seconds
      return DateTime.fromMillisecondsSinceEpoch(value * 1000);
    }
  }
  if (value is String) {
    if (int.tryParse(value) != null) {
      final intVal = int.parse(value);
      if (value.length > 12) {
        return DateTime.fromMillisecondsSinceEpoch(intVal);
      } else {
        return DateTime.fromMillisecondsSinceEpoch(intVal * 1000);
      }
    }
    // ISO string
    return DateTime.tryParse(value);
  }
  return null;
}

// دالة ترجمة حالة المهمة للعربية
String translateStatus(String? status) {
  switch (status) {
    case 'pending':
      return 'قيد الانتظار';
    case 'in_progress':
      return 'قيد التنفيذ';
    case 'completed':
      return 'منجزة';
    case 'cancelled':
      return 'ملغاة';
    case 'waitingForInfo':
      return 'في انتظار معلومات';
    case 'waiting_for_info':
     return 'في انتظار معلومات';
    case 'inProgress':
      return 'قيد التنفيذ';
    case 'delayed':
      return 'متأخرة';
    default:
      return status ?? '-';
  }
}

// دالة مساعدة لاستخراج اسم المكلف بشكل آمن
String extractAssigneeName(Map t) {
  final fields = ['assigneeName', 'assignee', 'userName', 'user'];
  for (final f in fields) {
    final v = t[f];
    if (v == null) continue;
    if (v is String && v.trim().isNotEmpty) return v;
    if (v is Map && v['name'] != null && v['name'] is String) return v['name'];
    if (v is List && v.isNotEmpty && v[0] is String) return v[0];
    // إذا كان نوع آخر، جرب toString فقط إذا لم يكن Map أو List
    if (v is! Map && v is! List) return v.toString();
  }
  return '-';
}

// ملاحظة: يمكن تطوير التقرير ليشمل مزيد من التفاصيل والإحصائيات لاحقاً.
// استخدم التعليقات لتوضيح أي جزء من الكود أو لتسهيل التطوير لاحقاً.
