import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// معالج أخطاء المخططات الموحد
/// 
/// يوفر معالجة موحدة لجميع أنواع الأخطاء في المخططات
class ChartErrorHandler {
  /// الحصول على رسالة خطأ مناسبة
  String getErrorMessage(dynamic error) {
    if (error is String) {
      return error;
    } else if (error is Exception) {
      return _getExceptionMessage(error);
    } else {
      return 'حدث خطأ غير متوقع';
    }
  }

  /// الحصول على رسالة خطأ من Exception
  String _getExceptionMessage(Exception exception) {
    final message = exception.toString();
    
    if (message.contains('network') || message.contains('connection')) {
      return 'خطأ في الاتصال بالشبكة';
    } else if (message.contains('timeout')) {
      return 'انتهت مهلة الاتصال';
    } else if (message.contains('format') || message.contains('parse')) {
      return 'خطأ في تنسيق البيانات';
    } else if (message.contains('permission') || message.contains('access')) {
      return 'ليس لديك صلاحية للوصول لهذه البيانات';
    } else {
      return 'حدث خطأ في تحميل البيانات';
    }
  }

  /// بناء مكون عرض الخطأ
  Widget buildErrorWidget(String errorMessage, {VoidCallback? onRetry}) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مكون البيانات الفارغة
  Widget buildEmptyDataWidget({
    String? message,
    IconData? icon,
    VoidCallback? onRefresh,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.bar_chart,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message ?? 'لا توجد بيانات لعرضها في الوقت الحالي',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRefresh != null) ...[
              const SizedBox(height: 16),
              TextButton.icon(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مكون التحميل
  Widget buildLoadingWidget({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// عرض رسالة خطأ في Snackbar
  void showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      icon: Icon(
        Icons.error_outline,
        color: Colors.red.shade600,
      ),
      duration: const Duration(seconds: 4),
    );
  }

  /// عرض رسالة نجاح في Snackbar
  void showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      icon: Icon(
        Icons.check_circle_outline,
        color: Colors.green.shade600,
      ),
      duration: const Duration(seconds: 3),
    );
  }

  /// عرض رسالة تحذير في Snackbar
  void showWarningSnackbar(String message) {
    Get.snackbar(
      'تحذير',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.shade100,
      colorText: Colors.orange.shade800,
      icon: Icon(
        Icons.warning_outlined,
        color: Colors.orange.shade600,
      ),
      duration: const Duration(seconds: 3),
    );
  }

  /// معالجة الأخطاء مع إظهار رسالة مناسبة
  void handleError(dynamic error, {bool showSnackbar = true}) {
    final message = getErrorMessage(error);
    
    // طباعة الخطأ في وحدة التحكم للتطوير
    debugPrint('Chart Error: $error');
    
    if (showSnackbar) {
      showErrorSnackbar(message);
    }
  }

  /// التحقق من صحة البيانات
  bool validateChartData(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return false;
    }

    // التحقق من وجود البيانات الأساسية
    if (data.containsKey('data')) {
      final chartData = data['data'];
      if (chartData is Map && chartData.isEmpty) {
        return false;
      }
      if (chartData is List && chartData.isEmpty) {
        return false;
      }
    }

    return true;
  }

  /// بناء مكون خطأ التحقق من البيانات
  Widget buildValidationErrorWidget(String validationMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_outlined,
              size: 64,
              color: Colors.amber.shade600,
            ),
            const SizedBox(height: 16),
            Text(
              'بيانات غير صحيحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.amber.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              validationMessage,
              style: TextStyle(
                fontSize: 14,
                color: Colors.amber.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
