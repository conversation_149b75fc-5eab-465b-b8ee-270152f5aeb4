import 'dart:async';
import 'package:flutter/foundation.dart';
import 'api/api_service.dart';

/// خدمة إصلاح قاعدة البيانات
///
/// تتيح إصلاح مشاكل قاعدة البيانات ونظام الأرشفة الإلكتروني
/// متوافقة مع ASP.NET Core API backend
class DatabaseRepair {
  final ApiService _apiService = ApiService();

  /// إصلاح قاعدة البيانات العامة
  ///
  /// يقوم بإصلاح المشاكل الشائعة في قاعدة البيانات
  Future<bool> repairDatabase() async {
    try {
      debugPrint('بدء إصلاح قاعدة البيانات...');

      // إصلاح الجداول المفقودة
      final tablesRepaired = await _repairMissingTables();
      if (!tablesRepaired) {
        debugPrint('فشل في إصلاح الجداول المفقودة');
        return false;
      }

      // إصلاح الحقول المفقودة
      final fieldsRepaired = await _repairMissingFields();
      if (!fieldsRepaired) {
        debugPrint('فشل في إصلاح الحقول المفقودة');
        return false;
      }

      // إصلاح الفهارس
      final indexesRepaired = await _repairIndexes();
      if (!indexesRepaired) {
        debugPrint('فشل في إصلاح الفهارس');
        return false;
      }

      // إصلاح العلاقات
      final relationshipsRepaired = await _repairRelationships();
      if (!relationshipsRepaired) {
        debugPrint('فشل في إصلاح العلاقات');
        return false;
      }

      debugPrint('تم إصلاح قاعدة البيانات بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إصلاح قاعدة البيانات: $e');
      return false;
    }
  }

  /// اختبار نظام الأرشفة الإلكتروني
  ///
  /// يقوم باختبار سلامة نظام الأرشفة الإلكتروني
  Future<bool> testArchiveSystem() async {
    try {
      debugPrint('بدء اختبار نظام الأرشفة الإلكتروني...');

      // اختبار جداول الأرشفة
      final archiveTablesTest = await _testArchiveTables();
      if (!archiveTablesTest) {
        debugPrint('فشل اختبار جداول الأرشفة');
        return false;
      }

      // اختبار الفئات
      final categoriesTest = await _testArchiveCategories();
      if (!categoriesTest) {
        debugPrint('فشل اختبار فئات الأرشفة');
        return false;
      }

      // اختبار الوثائق
      final documentsTest = await _testArchiveDocuments();
      if (!documentsTest) {
        debugPrint('فشل اختبار وثائق الأرشفة');
        return false;
      }

      // اختبار العلامات
      final tagsTest = await _testArchiveTags();
      if (!tagsTest) {
        debugPrint('فشل اختبار علامات الأرشفة');
        return false;
      }

      // اختبار المرفقات
      final attachmentsTest = await _testArchiveAttachments();
      if (!attachmentsTest) {
        debugPrint('فشل اختبار مرفقات الأرشفة');
        return false;
      }

      debugPrint('تم اختبار نظام الأرشفة الإلكتروني بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في اختبار نظام الأرشفة الإلكتروني: $e');
      return false;
    }
  }

  /// إصلاح نظام الأرشفة الإلكتروني
  ///
  /// يقوم بإصلاح مشاكل نظام الأرشفة الإلكتروني
  Future<bool> repairArchiveSystem() async {
    try {
      debugPrint('بدء إصلاح نظام الأرشفة الإلكتروني...');

      // إصلاح جداول الأرشفة
      final archiveTablesRepaired = await _repairArchiveTables();
      if (!archiveTablesRepaired) {
        debugPrint('فشل في إصلاح جداول الأرشفة');
        return false;
      }

      // إصلاح الفئات
      final categoriesRepaired = await _repairArchiveCategories();
      if (!categoriesRepaired) {
        debugPrint('فشل في إصلاح فئات الأرشفة');
        return false;
      }

      // إصلاح الوثائق
      final documentsRepaired = await _repairArchiveDocuments();
      if (!documentsRepaired) {
        debugPrint('فشل في إصلاح وثائق الأرشفة');
        return false;
      }

      // إصلاح العلامات
      final tagsRepaired = await _repairArchiveTags();
      if (!tagsRepaired) {
        debugPrint('فشل في إصلاح علامات الأرشفة');
        return false;
      }

      // إصلاح المرفقات
      final attachmentsRepaired = await _repairArchiveAttachments();
      if (!attachmentsRepaired) {
        debugPrint('فشل في إصلاح مرفقات الأرشفة');
        return false;
      }

      // إصلاح الفهارس والعلاقات
      final indexesRepaired = await _repairArchiveIndexes();
      if (!indexesRepaired) {
        debugPrint('فشل في إصلاح فهارس الأرشفة');
        return false;
      }

      debugPrint('تم إصلاح نظام الأرشفة الإلكتروني بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إصلاح نظام الأرشفة الإلكتروني: $e');
      return false;
    }
  }

  /// إصلاح الجداول المفقودة
  Future<bool> _repairMissingTables() async {
    try {
      // قائمة الجداول المطلوبة
      final requiredTables = [
        'sync_changes',
        'sync_locks',
        'time_tracking_entries',
        'power_bi_reports',
      ];

      for (final table in requiredTables) {
        final response = await _apiService.post('/api/database/repair/table', {
          'tableName': table,
          'action': 'create_if_missing'
        });

        if (response.statusCode != 200) {
          debugPrint('فشل في إصلاح الجدول: $table');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إصلاح الجداول المفقودة: $e');
      return false;
    }
  }

  /// إصلاح الحقول المفقودة
  Future<bool> _repairMissingFields() async {
    try {
      // إضافة حقل isRead المفقود في جدول messages
      final response = await _apiService.post('/api/database/repair/field', {
        'tableName': 'messages',
        'fieldName': 'isRead',
        'fieldType': 'boolean',
        'defaultValue': false,
        'action': 'add_if_missing'
      });

      if (response.statusCode != 200) {
        debugPrint('فشل في إضافة حقل isRead');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إصلاح الحقول المفقودة: $e');
      return false;
    }
  }

  /// إصلاح الفهارس
  Future<bool> _repairIndexes() async {
    try {
      final response = await _apiService.post('/api/database/repair/indexes', {
        'action': 'rebuild_all'
      });

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح الفهارس: $e');
      return false;
    }
  }

  /// إصلاح العلاقات
  Future<bool> _repairRelationships() async {
    try {
      final response = await _apiService.post('/api/database/repair/relationships', {
        'action': 'validate_and_repair'
      });

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح العلاقات: $e');
      return false;
    }
  }

  /// اختبار جداول الأرشفة
  Future<bool> _testArchiveTables() async {
    try {
      final response = await _apiService.get('/api/archive/test/tables');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في اختبار جداول الأرشفة: $e');
      return false;
    }
  }

  /// اختبار فئات الأرشفة
  Future<bool> _testArchiveCategories() async {
    try {
      final response = await _apiService.get('/api/ArchiveCategories');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في اختبار فئات الأرشفة: $e');
      return false;
    }
  }

  /// اختبار وثائق الأرشفة
  Future<bool> _testArchiveDocuments() async {
    try {
      final response = await _apiService.get('/api/ArchiveDocuments');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في اختبار وثائق الأرشفة: $e');
      return false;
    }
  }

  /// اختبار علامات الأرشفة
  Future<bool> _testArchiveTags() async {
    try {
      final response = await _apiService.get('/api/ArchiveTags');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في اختبار علامات الأرشفة: $e');
      return false;
    }
  }

  /// اختبار مرفقات الأرشفة
  Future<bool> _testArchiveAttachments() async {
    try {
      final response = await _apiService.get('/api/Attachments');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في اختبار مرفقات الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح جداول الأرشفة
  Future<bool> _repairArchiveTables() async {
    try {
      final response = await _apiService.post('/api/archive/repair/tables', {
        'action': 'repair_all'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح جداول الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح فئات الأرشفة
  Future<bool> _repairArchiveCategories() async {
    try {
      final response = await _apiService.post('/api/archive/repair/categories', {
        'action': 'validate_and_repair'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح فئات الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح وثائق الأرشفة
  Future<bool> _repairArchiveDocuments() async {
    try {
      final response = await _apiService.post('/api/archive/repair/documents', {
        'action': 'validate_and_repair'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح وثائق الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح علامات الأرشفة
  Future<bool> _repairArchiveTags() async {
    try {
      final response = await _apiService.post('/api/archive/repair/tags', {
        'action': 'validate_and_repair'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح علامات الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح مرفقات الأرشفة
  Future<bool> _repairArchiveAttachments() async {
    try {
      final response = await _apiService.post('/api/archive/repair/attachments', {
        'action': 'validate_and_repair'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح مرفقات الأرشفة: $e');
      return false;
    }
  }

  /// إصلاح فهارس الأرشفة
  Future<bool> _repairArchiveIndexes() async {
    try {
      final response = await _apiService.post('/api/archive/repair/indexes', {
        'action': 'rebuild_all'
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إصلاح فهارس الأرشفة: $e');
      return false;
    }
  }
}
