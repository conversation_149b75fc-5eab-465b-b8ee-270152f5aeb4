import 'package:flutter/material.dart';

/// Helper class to safely access inherited widgets like MediaQuery and Theme
///
/// This class provides safe methods to access MediaQuery and Theme data
/// without causing the "dependOnInheritedWidgetOfExactType was called before initState() completed" error
class InheritedWidgetHelper {
  /// Safely get MediaQuery data
  ///
  /// Use this method instead of directly calling MediaQuery.of(context) in initState
  /// Returns null if MediaQuery is not available
  static MediaQueryData? getMediaQueryDataSafely(BuildContext? context) {
    if (context == null) return null;

    try {
      return MediaQuery.maybeOf(context);
    } catch (e) {
      debugPrint('Error accessing MediaQuery: $e');
      return null;
    }
  }

  /// Safely get Theme data
  ///
  /// Use this method instead of directly calling Theme.of(context) in initState
  /// Returns null if Theme is not available
  static ThemeData? getThemeDataSafely(BuildContext? context) {
    if (context == null) return null;

    try {
      // Use a try-catch block to safely access Theme.of
      return Theme.of(context);
    } catch (e) {
      debugPrint('Error accessing Theme: $e');
      return null;
    }
  }

  /// Safely get screen width
  ///
  /// Returns a default value if MediaQuery is not available
  static double getScreenWidth(BuildContext? context, {double defaultValue = 360.0}) {
    final mediaQuery = getMediaQueryDataSafely(context);
    return mediaQuery?.size.width ?? defaultValue;
  }

  /// Safely get screen height
  ///
  /// Returns a default value if MediaQuery is not available
  static double getScreenHeight(BuildContext? context, {double defaultValue = 640.0}) {
    final mediaQuery = getMediaQueryDataSafely(context);
    return mediaQuery?.size.height ?? defaultValue;
  }

  /// Safely check if the current theme is dark
  ///
  /// Returns a default value if Theme is not available
  static bool isDarkTheme(BuildContext? context, {bool defaultValue = false}) {
    final theme = getThemeDataSafely(context);
    if (theme == null) return defaultValue;
    return theme.brightness == Brightness.dark;
  }
}

/// A builder widget that safely provides MediaQuery and Theme data
///
/// Use this widget to wrap parts of your UI that need to access MediaQuery or Theme
/// but might be built before these inherited widgets are fully initialized
class SafeInheritedWidgetBuilder extends StatefulWidget {
  /// The builder function that receives safe access to MediaQuery and Theme
  final Widget Function(
    BuildContext context,
    MediaQueryData? mediaQuery,
    ThemeData? theme
  ) builder;

  /// Creates a SafeInheritedWidgetBuilder
  const SafeInheritedWidgetBuilder({
    super.key,
    required this.builder,
  });

  @override
  State<SafeInheritedWidgetBuilder> createState() => _SafeInheritedWidgetBuilderState();
}

class _SafeInheritedWidgetBuilderState extends State<SafeInheritedWidgetBuilder> {
  MediaQueryData? _mediaQuery;
  ThemeData? _theme;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Access inherited widgets here, which is safe
    try {
      _mediaQuery = MediaQuery.maybeOf(context);
      _theme = Theme.of(context);
    } catch (e) {
      debugPrint('Error accessing inherited widgets: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(context, _mediaQuery, _theme);
  }
}
