// import 'package:flutter/material.dart';

// /// Scaffold موحد لتبويبات لوحة التحكم الإدارية
// /// يوفر عنوان، منطق تحميل/خطأ/إعادة محاولة، محتوى مخصص، وزر تحديث اختياري
// class AdminTabScaffold extends StatelessWidget {
//   final String title;
//   final bool isLoading;
//   final String? errorMessage;
//   final VoidCallback? onRetry;
//   final VoidCallback? onRefresh;
//   final List<Widget>? actions;
//   final Widget child;

//   const AdminTabScaffold({
//     super.key,
//     required this.title,
//     required this.child,
//     this.isLoading = false,
//     this.errorMessage,
//     this.onRetry,
//     this.onRefresh,
//     this.actions,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//           child: Row(
//             children: [
//               Text(
//                 title,
//                 style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
//               ),
//               const Spacer(),
//               if (onRefresh != null)
//                 IconButton(
//                   icon: const Icon(Icons.refresh),
//                   tooltip: 'تحديث',
//                   onPressed: onRefresh,
//                 ),
//               if (actions != null) ...actions!,
//             ],
//           ),
//         ),
//         if (isLoading)
//           const Expanded(
//             child: Center(child: CircularProgressIndicator()),
//           )
//         else if (errorMessage != null && errorMessage!.isNotEmpty)
//           Expanded(
//             child: Center(
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Icon(Icons.error_outline, color: Colors.red, size: 40),
//                   const SizedBox(height: 12),
//                   Text(errorMessage!, style: const TextStyle(color: Colors.red)),
//                   const SizedBox(height: 16),
//                   if (onRetry != null)
//                     ElevatedButton.icon(
//                       icon: const Icon(Icons.refresh),
//                       label: const Text('إعادة المحاولة'),
//                       onPressed: onRetry,
//                     ),
//                 ],
//               ),
//             ),
//           )
//         else
//           Expanded(child: child),
//       ],
//     );
//   }
// }
