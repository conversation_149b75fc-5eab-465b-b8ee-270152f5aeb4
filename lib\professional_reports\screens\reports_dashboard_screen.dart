/// لوحة تحكم تقرير المستخدمين البسيط
/// 
/// شاشة بسيطة لعرض تقرير المستخدمين

library;

import 'package:flutter/material.dart';
import 'package:flutter_application_2/professional_reports/core.dart';
import 'package:flutter_application_2/professional_reports/departments_report_filter_widget.dart';
import 'package:flutter_application_2/professional_reports/screens/overdue_tasks_report_screen.dart';
import 'package:flutter_application_2/professional_reports/screens/task_completion_report_screen.dart';
import 'package:flutter_application_2/professional_reports/screens/user_performance_comparison_report_screen.dart';
import 'package:flutter_application_2/professional_reports/screens/workload_distribution_report_screen.dart';
import 'package:flutter_application_2/professional_reports/task_full_report.dart';
import 'package:flutter_application_2/professional_reports/task_report_filter_dialog.dart';
import 'package:flutter_application_2/professional_reports/user_full_report.dart';
import 'package:flutter_application_2/professional_reports/user_report_filter_dialog.dart';
import 'package:flutter_application_2/services/api/departments_api_service.dart';
import 'package:get/get.dart';
import '../../models/user_model.dart';

// استيراد نموذج المهمة
import '../../services/api/user_api_service.dart';
// استيراد تقرير المهام
import 'package:flutter_application_2/professional_reports/report_departments.dart'; // استيراد تقرير الأقسام

// تأكد من استيراد الشاشة التجريبية

// import '../widgets/report_preview_widget.dart'; // تم تعليق هذا الاستيراد لأننا لن نستخدمه

/// تحكم في لوحة التقارير
class ReportsDashboardController extends GetxController {
  /// خدمة API للمستخدمين
  final UserApiService _userApiService = UserApiService();
  
  /// حالة التحميل
  final isLoading = false.obs;
  
  /// قائمة المستخدمين
  final users = <User>[].obs;

  @override
  void onInit() {
    super.onInit();
    _loadUsers();
  }

  /// تحميل قائمة المستخدمين
  Future<void> _loadUsers() async {
    try {
      isLoading.value = true;
      
      // جلب المستخدمين من API
      final usersList = await _userApiService.getAllUsers();
      users.assignAll(usersList);
      
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحميل المستخدمين: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }


  /// تحديث البيانات
  Future<void> refreshData() async {
    await _loadUsers();
  }

}


/// شاشة لوحة تحكم التقارير
class ReportsDashboardScreen extends StatelessWidget {
  const ReportsDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ReportsDashboardController());

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم التقارير',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        actions: [
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: controller.refreshData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
           
            
              // بطاقة التقارير المتاحة
              _buildAvailableReportsCard(controller),
            ],
          ),
        );
      }),
    );
  }


  
  /// بناء بطاقة التقارير المتاحة
  Widget _buildAvailableReportsCard(ReportsDashboardController controller) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقارير المتاحة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            // زر تقرير الأقسام
            ListTile(
              key: const ValueKey('departments_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.teal,
                child: Icon(Icons.apartment, color: Colors.white),
              ),
              title: const Text(
                'تقرير الأقسام',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('عرض تقرير شامل لجميع الأقسام مع المستخدمين والمهام وإمكانية التصفية'),
              trailing: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () async {
                    // جلب الأقسام من API
                    final departments = await DepartmentsApiService().getDepartmentHierarchy();
                    await showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: const Text('تصفية تقرير الأقسام'),
                          content: SizedBox(
                            width: 400,
                            child: DepartmentsReportFilterWidget(
                              allDepartments: departments,
                              onGenerateReport: ({departmentIds, fromDate, toDate, taskTypes, taskStatuses}) async {
                                Navigator.of(context).pop();
                                await showPdfReport(
                                  generatePdf: () => generateDepartmentsPdfReport(
                                    departmentIds: departmentIds,
                                    fromDate: fromDate,
                                    toDate: toDate,
                                    taskTypes: taskTypes,
                                    taskStatuses: taskStatuses,
                                  ),
                                  fileNamePrefix: 'تقرير_الأقسام',
                                );
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('عرض التقرير'),
                ),
              ),
            ),
            const Divider(),
            // تقرير المستخدمين الشامل
            ListTile(
              key: const ValueKey('users_full_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.indigo,
                child: Icon(Icons.person_search, color: Colors.white),
              ),
              title: const Text(
                'تقرير المستخدمين الشامل',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('عرض تقرير احترافي لكل مستخدم مع ملخص المهام والمشاركات والأداء'),
              trailing: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () async {
                    // جلب المستخدمين والأقسام
                    final users = await controller._userApiService.getAllUsers();
                    final departments = await DepartmentsApiService().getAllDepartments();
                    // عرض فلتر المستخدمين
                    final selectedUserIds = await showUserReportFilterDialog(
                      context: context,
                      users: users,
                      departments: departments,
                    );
                    if (selectedUserIds == null || selectedUserIds.isEmpty) return;
                    await showPdfReport(
                      generatePdf: () => generateUsersFullPdfReport(userIds: selectedUserIds),
                      fileNamePrefix: 'تقرير_المستخدمين_الشامل',
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('عرض التقرير'),
                ),
              ),
            ),
            const Divider(),
            // تقرير المهام الشامل والاحترافي
            ListTile(
              key: const ValueKey('tasks_full_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.deepPurple,
                child: Icon(Icons.analytics, color: Colors.white),
              ),
              title: const Text(
                'تقرير المهام الشامل والاحترافي',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('تقرير عصري ذكي شامل للمهام مع فلاتر متقدمة لجميع المستويات الإدارية'),
              trailing: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () async {
                    // جلب المستخدمين والأقسام
                    final users = await controller._userApiService.getAllUsers();
                    final departments = await DepartmentsApiService().getAllDepartments();
                    // عرض فلتر المهام
                    final filterResult = await showTaskReportFilterDialog(
                      context: context,
                      users: users,
                      departments: departments,
                    );
                    if (filterResult == null) return;
                    await showPdfReport(
                      generatePdf: () => generateTaskFullPdfReport(
                        userIds: filterResult['userIds'],
                        departmentIds: filterResult['departmentIds'],
                        statuses: filterResult['statuses'],
                      ),
                      fileNamePrefix: 'تقرير_المهام_الشامل',
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('عرض التقرير'),
                ),
              ),
            ),
            // إضافة فواصل بين التقارير الجديدة والقديمة
            const Divider(thickness: 2),
            const Text(
              'التقارير الجديدة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 8),
            // تقرير إنجاز المهام
            ListTile(
              key: const ValueKey('task_completion_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.task_alt, color: Colors.white),
              ),
              title: const Text('تقرير إنجاز المهام'),
              subtitle: const Text('مقارنة بين المهام المنشأة والمنجزة.'),              trailing: ElevatedButton(
                onPressed: () => Get.to(() => const TaskCompletionReportScreen()),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('عرض'),
              ),
            ),
             ListTile(
                key: const ValueKey('overdue_tasks_report'),
                leading: const CircleAvatar(
                  backgroundColor: Colors.redAccent,
                  child: Icon(Icons.timer_off, color: Colors.white),
                ),
                title: const Text('تقرير المهام المتأخرة'),
                subtitle: const Text('عرض المهام التي تجاوزت تاريخ الاستحقاق.'),
                trailing: ElevatedButton(
                  onPressed: () {
                    Get.to(() => const OverdueTasksReportScreen());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('عرض'),
                ),
              ),
            // تقرير أداء المستخدمين المقارن
            ListTile(
              key: const ValueKey('user_performance_comparison_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.blueGrey,
                child: Icon(Icons.people, color: Colors.white),
              ),
              title: const Text('تقرير أداء المستخدمين المقارن'),
              subtitle: const Text('مقارنة أداء عدة مستخدمين.'),
              trailing: ElevatedButton(
                onPressed: () {
                  Get.to(() => const UserPerformanceComparisonReportScreen());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueGrey,
                  foregroundColor: Colors.white,
                ),
                child: const Text('عرض'),
              ),
            ),
            // تقرير الوقت المستغرق (مثال بسيط، يحتاج إلى نموذج TimeTrackingEntry)
            ListTile(
              key: const ValueKey('time_log_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.cyan,
                child: Icon(Icons.timer, color: Colors.white),
              ),
              title: const Text('تقرير الوقت المستغرق'),
              subtitle: const Text('عرض الوقت المستغرق على المهام (قيد التطوير).'),
              trailing: ElevatedButton(
                onPressed: () {
                  // هذا التقرير يحتاج إلى نموذج TimeTrackingEntry وبياناته
                  Get.snackbar('معلومة', 'هذا التقرير قيد التطوير ويتطلب نموذج تتبع الوقت.');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan,
                  foregroundColor: Colors.white,
                ),
                child: const Text('قريباً'),
              ),
            ),
            // تقرير مقارنة الوقت المقدر بالفعلي (يحتاج إلى حقول إضافية في نموذج المهمة)
            ListTile(
              key: const ValueKey('estimated_vs_actual_time_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.lime,
                child: Icon(Icons.schedule, color: Colors.white),
              ),
              title: const Text('مقارنة الوقت المقدر بالفعلي'),
              subtitle: const Text('مقارنة تقديرات الوقت بالوقت الفعلي (يتطلب حقول إضافية).'),
              trailing: ElevatedButton(
                onPressed: () {
                  Get.snackbar('معلومة', 'هذا التقرير يتطلب إضافة حقول "الوقت المقدر" و "الوقت الفعلي" إلى نموذج المهمة.');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.lime,
                  foregroundColor: Colors.white,
                ),
                child: const Text('قريباً'),
              ),
            ),
            // تقرير توزيع عبء العمل
            ListTile(
              key: const ValueKey('workload_distribution_report'),
              leading: const CircleAvatar(
                backgroundColor: Colors.tealAccent,
                child: Icon(Icons.work, color: Colors.white),
              ),
              title: const Text('تقرير توزيع عبء العمل'),
              subtitle: const Text('عرض توزيع المهام المفتوحة على المستخدمين والأقسام.'),
              trailing: ElevatedButton(
                onPressed: () {
                  Get.to(() => const WorkloadDistributionReportScreen());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.tealAccent,
                  foregroundColor: Colors.white,
                ),
                child: const Text('عرض'),
              ),
            ),
            // تقرير سجل النظام والنشاط وتقارير صحة النظام (تحتاج إلى مزيد من التطوير)
            // يمكن إضافتها لاحقًا بنفس الطريقة
          ],
        ),
      ),
    );
  }  
}

