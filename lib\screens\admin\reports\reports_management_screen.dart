import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_card_widget.dart';

/// شاشة إدارة التقارير والإحصائيات
class ReportsManagementScreen extends StatefulWidget {
  const ReportsManagementScreen({super.key});

  @override
  State<ReportsManagementScreen> createState() => _ReportsManagementScreenState();
}

class _ReportsManagementScreenState extends State<ReportsManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadReportsData();
  }

  /// تحميل بيانات التقارير
  Future<void> _loadReportsData() async {
    await _adminController.refreshAllData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير والإحصائيات'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportsData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadReportsData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات سريعة
              _buildQuickStatsSection(),
              
              const SizedBox(height: 24),
              
              // تقارير المستخدمين
              _buildUsersReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير الأدوار والصلاحيات
              _buildRolesPermissionsReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير النظام
              _buildSystemReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير مخصصة
              _buildCustomReportsSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          final stats = _adminController.statistics;
          return GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'المستخدمين',
                '${stats['totalUsers'] ?? 0}',
                '${stats['activeUsers'] ?? 0} نشط',
                Icons.people,
                Colors.blue,
              ),
              _buildStatCard(
                'الأدوار',
                '${stats['totalRoles'] ?? 0}',
                '${stats['activeRoles'] ?? 0} نشط',
                Icons.security,
                Colors.green,
              ),
              _buildStatCard(
                'الصلاحيات',
                '${stats['totalPermissions'] ?? 0}',
                'إجمالي الصلاحيات',
                Icons.admin_panel_settings,
                Colors.orange,
              ),
              _buildStatCard(
                'آخر تحديث',
                _formatLastUpdate(stats['lastUpdated']),
                'تحديث البيانات',
                Icons.update,
                Colors.purple,
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم تقارير المستخدمين
  Widget _buildUsersReportsSection() {
    return _buildReportSection(
      title: 'تقارير المستخدمين',
      icon: Icons.people,
      reports: [
        ReportItem(
          title: 'تقرير نشاط المستخدمين',
          subtitle: 'إحصائيات نشاط المستخدمين خلال فترة محددة',
          icon: Icons.analytics,
          color: Colors.blue,
          onTap: () => _showUserActivityReport(),
        ),
        ReportItem(
          title: 'توزيع المستخدمين حسب الأقسام',
          subtitle: 'عدد المستخدمين في كل قسم',
          icon: Icons.pie_chart,
          color: Colors.green,
          onTap: () => _showUserDistributionReport(),
        ),
        ReportItem(
          title: 'المستخدمين الجدد',
          subtitle: 'المستخدمين المضافين حديثاً',
          icon: Icons.person_add,
          color: Colors.orange,
          onTap: () => _showNewUsersReport(),
        ),
      ],
    );
  }

  /// بناء قسم تقارير الأدوار والصلاحيات
  Widget _buildRolesPermissionsReportsSection() {
    return _buildReportSection(
      title: 'تقارير الأدوار والصلاحيات',
      icon: Icons.security,
      reports: [
        ReportItem(
          title: 'توزيع الأدوار',
          subtitle: 'عدد المستخدمين لكل دور',
          icon: Icons.donut_small,
          color: Colors.purple,
          onTap: () => _showRoleDistributionReport(),
        ),
        ReportItem(
          title: 'استخدام الصلاحيات',
          subtitle: 'الصلاحيات الأكثر استخداماً',
          icon: Icons.bar_chart,
          color: Colors.teal,
          onTap: () => _showPermissionUsageReport(),
        ),
        ReportItem(
          title: 'الأدوار المخصصة',
          subtitle: 'تقرير الأدوار المخصصة وصلاحياتها',
          icon: Icons.settings,
          color: Colors.indigo,
          onTap: () => _showCustomRolesReport(),
        ),
      ],
    );
  }

  /// بناء قسم تقارير النظام
  Widget _buildSystemReportsSection() {
    return _buildReportSection(
      title: 'تقارير النظام',
      icon: Icons.computer,
      reports: [
        ReportItem(
          title: 'صحة النظام',
          subtitle: 'حالة النظام والأداء',
          icon: Icons.health_and_safety,
          color: Colors.green,
          onTap: () => _showSystemHealthReport(),
        ),
        ReportItem(
          title: 'سجلات النشاط',
          subtitle: 'سجلات العمليات والأنشطة',
          icon: Icons.history,
          color: Colors.blue,
          onTap: () => _showActivityLogsReport(),
        ),
        ReportItem(
          title: 'النسخ الاحتياطية',
          subtitle: 'تقرير النسخ الاحتياطية',
          icon: Icons.backup,
          color: Colors.orange,
          onTap: () => _showBackupsReport(),
        ),
      ],
    );
  }

  /// بناء قسم التقارير المخصصة
  Widget _buildCustomReportsSection() {
    return _buildReportSection(
      title: 'تقارير مخصصة',
      icon: Icons.dashboard_customize,
      reports: [
        ReportItem(
          title: 'إنشاء تقرير مخصص',
          subtitle: 'إنشاء تقرير حسب المعايير المحددة',
          icon: Icons.add_chart,
          color: Colors.purple,
          onTap: () => _showCustomReportBuilder(),
        ),
        ReportItem(
          title: 'التقارير المحفوظة',
          subtitle: 'عرض التقارير المحفوظة مسبقاً',
          icon: Icons.bookmark,
          color: Colors.teal,
          onTap: () => _showSavedReports(),
        ),
        ReportItem(
          title: 'تصدير البيانات',
          subtitle: 'تصدير البيانات بصيغ مختلفة',
          icon: Icons.file_download,
          color: Colors.indigo,
          onTap: () => _showDataExport(),
        ),
      ],
    );
  }

  /// بناء قسم تقرير
  Widget _buildReportSection({
    required String title,
    required IconData icon,
    required List<ReportItem> reports,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...reports.map((report) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: AdminCardWidget(
            title: report.title,
            subtitle: report.subtitle,
            icon: report.icon,
            iconColor: report.color,
            onTap: report.onTap,
          ),
        )),
      ],
    );
  }

  // ===== طرق عرض التقارير =====

  void _showUserActivityReport() {
    Get.dialog(
      AlertDialog(
        title: const Text('تقرير نشاط المستخدمين'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildReportRow('إجمالي المستخدمين', '${_adminController.totalUsers}'),
                _buildReportRow('المستخدمين النشطين', '${_adminController.activeUsers}'),
                _buildReportRow('نسبة النشاط', '${_calculateActivityRate()}%'),
                const Divider(),
                Text(
                  'آخر تحديث: ${DateTime.now().toString().split('.')[0]}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar('قيد التطوير', 'ستتوفر ميزة التصدير قريباً');
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  void _showUserDistributionReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.pie_chart, color: Colors.blue),
            SizedBox(width: 8),
            Text('تقرير توزيع المستخدمين'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final users = _adminController.users;
            final departments = _adminController.departments;

            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إحصائيات عامة
                  _buildUserStatsSection(users),
                  const SizedBox(height: 16),

                  // توزيع حسب الأقسام
                  _buildDepartmentDistribution(users, departments),
                  const SizedBox(height: 16),

                  // توزيع حسب الحالة
                  _buildStatusDistribution(users),
                ],
              ),
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _exportUserDistributionReport(),
            child: const Text('تصدير'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showNewUsersReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.person_add, color: Colors.green),
            SizedBox(width: 8),
            Text('تقرير المستخدمين الجدد'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final users = _adminController.users;
            final newUsers = _getNewUsers(users);

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // إحصائيات المستخدمين الجدد
                _buildNewUsersStats(newUsers),
                const SizedBox(height: 16),

                // قائمة المستخدمين الجدد
                Expanded(
                  child: _buildNewUsersList(newUsers),
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => _exportNewUsersReport(),
            child: const Text('تصدير'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showRoleDistributionReport() {
    Get.dialog(
      AlertDialog(
        title: const Text('توزيع الأدوار'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildReportRow('إجمالي الأدوار', '${_adminController.totalRoles}'),
                _buildReportRow('الأدوار النشطة', '${_adminController.activeRoles}'),
                const Divider(),
                Text(
                  'تفاصيل التوزيع ستتوفر في التحديث القادم',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showPermissionUsageReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.orange),
            SizedBox(width: 8),
            Text('تقرير استخدام الصلاحيات'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final permissions = _adminController.permissions;

            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPermissionStatsSection(permissions),
                  const SizedBox(height: 16),
                  _buildPermissionUsageList(permissions),
                ],
              ),
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showCustomRolesReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.admin_panel_settings, color: Colors.purple),
            SizedBox(width: 8),
            Text('تقرير الأدوار المخصصة'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final roles = _adminController.roles;
            final customRoles = roles.where((role) => role.name != 'Admin' && role.name != 'User').toList();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCustomRolesStats(customRoles),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildCustomRolesList(customRoles),
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSystemHealthReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.health_and_safety, color: Colors.green),
            SizedBox(width: 8),
            Text('تقرير صحة النظام'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSystemHealthOverview(),
                const SizedBox(height: 16),
                _buildSystemHealthDetails(),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showActivityLogsReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.history, color: Colors.blue),
            SizedBox(width: 8),
            Text('تقرير سجلات النشاط'),
          ],
        ),
        content: SizedBox(
          width: 700,
          height: 500,
          child: Obx(() {
            final logs = _adminController.activityLogs;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildActivityLogsStats(logs),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildActivityLogsList(logs),
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showBackupsReport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.backup, color: Colors.teal),
            SizedBox(width: 8),
            Text('تقرير النسخ الاحتياطية'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Obx(() {
            final backups = _adminController.backups;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBackupsStats(backups),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildBackupsList(backups),
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showCustomReportBuilder() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.build, color: Colors.amber),
            SizedBox(width: 8),
            Text('منشئ التقارير المخصص'),
          ],
        ),
        content: SizedBox(
          width: 700,
          height: 500,
          child: _buildCustomReportBuilder(),
        ),
        actions: [
          TextButton(
            onPressed: () => _generateCustomReport(),
            child: const Text('إنشاء التقرير'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSavedReports() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.save, color: Colors.indigo),
            SizedBox(width: 8),
            Text('التقارير المحفوظة'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: _buildSavedReportsList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showDataExport() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.download, color: Colors.green),
            SizedBox(width: 8),
            Text('تصدير البيانات'),
          ],
        ),
        content: SizedBox(
          width: 600,
          height: 400,
          child: _buildDataExportOptions(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // ===== طرق مساعدة =====

  Widget _buildReportRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  String _formatLastUpdate(dynamic timestamp) {
    if (timestamp == null) return 'غير محدد';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp as int);
      return '${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  double _calculateActivityRate() {
    final total = _adminController.totalUsers;
    final active = _adminController.activeUsers;
    if (total == 0) return 0.0;
    return (active / total * 100);
  }

  // ===== طرق مساعدة للتقارير =====

  /// بناء قسم إحصائيات المستخدمين
  Widget _buildUserStatsSection(List<dynamic> users) {
    final activeUsers = users.where((user) => user.isActive == true).length;
    final inactiveUsers = users.length - activeUsers;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات عامة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('إجمالي المستخدمين', '${users.length}'),
            _buildReportRow('المستخدمون النشطون', '$activeUsers'),
            _buildReportRow('المستخدمون غير النشطين', '$inactiveUsers'),
            _buildReportRow('نسبة النشاط', '${((activeUsers / users.length) * 100).toStringAsFixed(1)}%'),
          ],
        ),
      ),
    );
  }

  /// بناء توزيع الأقسام
  Widget _buildDepartmentDistribution(List<dynamic> users, List<dynamic> departments) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('توزيع حسب الأقسام', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            ...departments.map((dept) {
              final deptUsers = users.where((user) => user.departmentId == dept.id).length;
              return _buildReportRow(dept.name, '$deptUsers مستخدم');
            }),
          ],
        ),
      ),
    );
  }

  /// بناء توزيع الحالة
  Widget _buildStatusDistribution(List<dynamic> users) {
    final activeUsers = users.where((user) => user.isActive == true).length;
    final inactiveUsers = users.length - activeUsers;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('توزيع حسب الحالة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('نشط', '$activeUsers'),
            _buildReportRow('غير نشط', '$inactiveUsers'),
          ],
        ),
      ),
    );
  }

  /// تصدير تقرير توزيع المستخدمين
  void _exportUserDistributionReport() {
    Get.snackbar('تصدير', 'تم تصدير تقرير توزيع المستخدمين');
  }

  /// الحصول على المستخدمين الجدد
  List<dynamic> _getNewUsers(List<dynamic> users) {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    return users.where((user) {
      try {
        final createdAt = DateTime.parse(user.createdAt.toString());
        return createdAt.isAfter(thirtyDaysAgo);
      } catch (e) {
        return false;
      }
    }).toList();
  }

  /// بناء إحصائيات المستخدمين الجدد
  Widget _buildNewUsersStats(List<dynamic> newUsers) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات المستخدمين الجدد', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('المستخدمون الجدد (30 يوم)', '${newUsers.length}'),
            _buildReportRow('متوسط يومي', '${(newUsers.length / 30).toStringAsFixed(1)}'),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المستخدمين الجدد
  Widget _buildNewUsersList(List<dynamic> newUsers) {
    if (newUsers.isEmpty) {
      return const Center(
        child: Text('لا توجد مستخدمين جدد في آخر 30 يوم'),
      );
    }

    return ListView.builder(
      itemCount: newUsers.length,
      itemBuilder: (context, index) {
        final user = newUsers[index];
        return ListTile(
          leading: CircleAvatar(
            child: Text(user.name.substring(0, 1)),
          ),
          title: Text(user.name),
          subtitle: Text(user.email),
          trailing: Text(_formatDate(user.createdAt)),
        );
      },
    );
  }

  /// تصدير تقرير المستخدمين الجدد
  void _exportNewUsersReport() {
    Get.snackbar('تصدير', 'تم تصدير تقرير المستخدمين الجدد');
  }

  /// تنسيق التاريخ
  String _formatDate(dynamic date) {
    try {
      final dateTime = DateTime.parse(date.toString());
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// بناء قسم إحصائيات الصلاحيات
  Widget _buildPermissionStatsSection(List<dynamic> permissions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات الصلاحيات', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('إجمالي الصلاحيات', '${permissions.length}'),
            _buildReportRow('الصلاحيات النشطة', '${permissions.where((p) => p.isActive == true).length}'),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة استخدام الصلاحيات
  Widget _buildPermissionUsageList(List<dynamic> permissions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('استخدام الصلاحيات', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            ...permissions.take(10).map((permission) {
              return _buildReportRow(permission.name, 'مستخدمة');
            }),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائيات الأدوار المخصصة
  Widget _buildCustomRolesStats(List<dynamic> customRoles) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات الأدوار المخصصة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('الأدوار المخصصة', '${customRoles.length}'),
            _buildReportRow('الأدوار النشطة', '${customRoles.where((r) => r.isActive == true).length}'),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الأدوار المخصصة
  Widget _buildCustomRolesList(List<dynamic> customRoles) {
    if (customRoles.isEmpty) {
      return const Center(
        child: Text('لا توجد أدوار مخصصة'),
      );
    }

    return ListView.builder(
      itemCount: customRoles.length,
      itemBuilder: (context, index) {
        final role = customRoles[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: role.isActive ? Colors.green : Colors.grey,
            child: const Icon(Icons.admin_panel_settings, color: Colors.white),
          ),
          title: Text(role.name),
          subtitle: Text(role.description ?? 'لا يوجد وصف'),
          trailing: Text(role.isActive ? 'نشط' : 'غير نشط'),
        );
      },
    );
  }

  /// بناء نظرة عامة على صحة النظام
  Widget _buildSystemHealthOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('نظرة عامة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildHealthIndicator('حالة النظام', 'جيد', Colors.green),
            _buildHealthIndicator('استخدام المعالج', '45%', Colors.blue),
            _buildHealthIndicator('استخدام الذاكرة', '68%', Colors.orange),
            _buildHealthIndicator('مساحة القرص', '23%', Colors.purple),
          ],
        ),
      ),
    );
  }

  /// بناء تفاصيل صحة النظام
  Widget _buildSystemHealthDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('تفاصيل إضافية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('وقت التشغيل', '15 يوم، 6 ساعات'),
            _buildReportRow('آخر إعادة تشغيل', '15/12/2024 10:30'),
            _buildReportRow('عدد المستخدمين المتصلين', '23'),
            _buildReportRow('عدد الجلسات النشطة', '45'),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر الصحة
  Widget _buildHealthIndicator(String title, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إحصائيات سجلات النشاط
  Widget _buildActivityLogsStats(List<dynamic> logs) {
    final todayLogs = logs.where((log) {
      try {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.timestamp);
        final today = DateTime.now();
        return logDate.day == today.day && logDate.month == today.month && logDate.year == today.year;
      } catch (e) {
        return false;
      }
    }).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات سجلات النشاط', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('إجمالي السجلات', '${logs.length}'),
            _buildReportRow('سجلات اليوم', '$todayLogs'),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة سجلات النشاط
  Widget _buildActivityLogsList(List<dynamic> logs) {
    if (logs.isEmpty) {
      return const Center(
        child: Text('لا توجد سجلات نشاط'),
      );
    }

    return ListView.builder(
      itemCount: logs.take(20).length,
      itemBuilder: (context, index) {
        final log = logs[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getLogTypeColor(log.action),
            child: Icon(_getLogTypeIcon(log.action), color: Colors.white, size: 16),
          ),
          title: Text(log.action),
          subtitle: Text(log.details ?? 'لا يوجد تفاصيل'),
          trailing: Text(_formatLogTime(log.timestamp)),
        );
      },
    );
  }

  /// الحصول على لون نوع السجل
  Color _getLogTypeColor(String action) {
    switch (action.toLowerCase()) {
      case 'create': return Colors.green;
      case 'update': return Colors.blue;
      case 'delete': return Colors.red;
      case 'login': return Colors.purple;
      default: return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع السجل
  IconData _getLogTypeIcon(String action) {
    switch (action.toLowerCase()) {
      case 'create': return Icons.add;
      case 'update': return Icons.edit;
      case 'delete': return Icons.delete;
      case 'login': return Icons.login;
      default: return Icons.info;
    }
  }

  /// تنسيق وقت السجل
  String _formatLogTime(dynamic timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return '${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// بناء إحصائيات النسخ الاحتياطية
  Widget _buildBackupsStats(List<dynamic> backups) {
    final totalSize = backups.fold<double>(0, (sum, backup) => sum + (backup.size ?? 0));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إحصائيات النسخ الاحتياطية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            _buildReportRow('إجمالي النسخ', '${backups.length}'),
            _buildReportRow('الحجم الإجمالي', _formatFileSize(totalSize)),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة النسخ الاحتياطية
  Widget _buildBackupsList(List<dynamic> backups) {
    if (backups.isEmpty) {
      return const Center(
        child: Text('لا توجد نسخ احتياطية'),
      );
    }

    return ListView.builder(
      itemCount: backups.length,
      itemBuilder: (context, index) {
        final backup = backups[index];
        return ListTile(
          leading: const CircleAvatar(
            backgroundColor: Colors.teal,
            child: Icon(Icons.backup, color: Colors.white),
          ),
          title: Text(backup.description ?? 'نسخة احتياطية'),
          subtitle: Text('الحجم: ${_formatFileSize(backup.size ?? 0)}'),
          trailing: Text(_formatDate(backup.createdAt)),
        );
      },
    );
  }

  /// تنسيق حجم الملف
  String _formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toStringAsFixed(0)} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// بناء منشئ التقارير المخصص
  Widget _buildCustomReportBuilder() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('إنشاء تقرير مخصص', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        // اختيار نوع البيانات
        _buildReportBuilderSection('نوع البيانات', [
          'المستخدمين',
          'المهام',
          'الأقسام',
          'الصلاحيات',
          'سجلات النشاط',
        ]),

        // اختيار الفترة الزمنية
        _buildReportBuilderSection('الفترة الزمنية', [
          'آخر 7 أيام',
          'آخر 30 يوم',
          'آخر 3 أشهر',
          'السنة الحالية',
          'فترة مخصصة',
        ]),

        // اختيار تنسيق التصدير
        _buildReportBuilderSection('تنسيق التصدير', [
          'PDF',
          'Excel',
          'CSV',
          'JSON',
        ]),
      ],
    );
  }

  /// بناء قسم منشئ التقارير
  Widget _buildReportBuilderSection(String title, List<String> options) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            ...options.map((option) => CheckboxListTile(
              title: Text(option),
              value: false,
              onChanged: (value) {},
            )),
          ],
        ),
      ),
    );
  }

  /// إنشاء تقرير مخصص
  void _generateCustomReport() {
    Get.back();
    Get.snackbar('إنشاء التقرير', 'تم إنشاء التقرير المخصص بنجاح');
  }

  /// بناء قائمة التقارير المحفوظة
  Widget _buildSavedReportsList() {
    final savedReports = [
      {'name': 'تقرير المستخدمين الشهري', 'date': '15/12/2024', 'type': 'PDF'},
      {'name': 'تقرير المهام الأسبوعي', 'date': '10/12/2024', 'type': 'Excel'},
      {'name': 'تقرير النشاط اليومي', 'date': '08/12/2024', 'type': 'CSV'},
    ];

    return ListView.builder(
      itemCount: savedReports.length,
      itemBuilder: (context, index) {
        final report = savedReports[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: Colors.indigo,
            child: Icon(_getReportTypeIcon(report['type']!), color: Colors.white),
          ),
          title: Text(report['name']!),
          subtitle: Text('تاريخ الإنشاء: ${report['date']}'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.download),
                onPressed: () => _downloadSavedReport(report['name']!),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _deleteSavedReport(report['name']!),
              ),
            ],
          ),
        );
      },
    );
  }

  /// الحصول على أيقونة نوع التقرير
  IconData _getReportTypeIcon(String type) {
    switch (type) {
      case 'PDF': return Icons.picture_as_pdf;
      case 'Excel': return Icons.table_chart;
      case 'CSV': return Icons.text_snippet;
      default: return Icons.description;
    }
  }

  /// تحميل تقرير محفوظ
  void _downloadSavedReport(String reportName) {
    Get.snackbar('تحميل', 'تم تحميل $reportName');
  }

  /// حذف تقرير محفوظ
  void _deleteSavedReport(String reportName) {
    Get.snackbar('حذف', 'تم حذف $reportName');
  }

  /// بناء خيارات تصدير البيانات
  Widget _buildDataExportOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('اختر البيانات للتصدير', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        Expanded(
          child: ListView(
            children: [
              _buildExportOption('المستخدمين', 'تصدير جميع بيانات المستخدمين', Icons.people),
              _buildExportOption('المهام', 'تصدير جميع المهام والمشاريع', Icons.task),
              _buildExportOption('الأقسام', 'تصدير الهيكل التنظيمي', Icons.business),
              _buildExportOption('الصلاحيات', 'تصدير الأدوار والصلاحيات', Icons.security),
              _buildExportOption('سجلات النشاط', 'تصدير سجلات النظام', Icons.history),
              _buildExportOption('النسخ الاحتياطية', 'تصدير معلومات النسخ', Icons.backup),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خيار التصدير
  Widget _buildExportOption(String title, String description, IconData icon) {
    return Card(
      child: ListTile(
        leading: Icon(icon, color: Colors.green),
        title: Text(title),
        subtitle: Text(description),
        trailing: ElevatedButton(
          onPressed: () => _exportData(title),
          child: const Text('تصدير'),
        ),
      ),
    );
  }

  /// تصدير البيانات
  void _exportData(String dataType) {
    Get.snackbar('تصدير', 'تم تصدير $dataType بنجاح');
  }
}

/// نموذج عنصر التقرير
class ReportItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const ReportItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
