import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../config/api_config.dart';

/// مساعد للتعامل مع الصور
class ImageHelper {
  /// التحقق من صحة امتداد الصورة
  static bool isValidImageExtension(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension);
  }

  /// الحصول على حجم الصورة
  static Future<Size?> getImageSize(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) return null;

      // TODO: تنفيذ قراءة أبعاد الصورة
      // يمكن استخدام مكتبة image package لهذا الغرض
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على حجم الصورة: $e');
      return null;
    }
  }

  /// تحويل مسار الصورة إلى URL للخادم
  static String? getImageUrl(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) return null;

    // إذا كان المسار يحتوي على http أو https، فهو URL صالح
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // تحويل أي مسار آخر إلى URL للخادم
    final fileName = path.basename(imagePath);
    return '${ApiConfig.baseUrl}/uploads/profile_images/$fileName';
  }



  /// بناء عنصر صورة موحد يحمل الصور من الخادم فقط
  static Widget buildProfileImage({
    String? imagePath,
    double radius = 25,
    String? fallbackText,
    Color? backgroundColor,
    Color? textColor,
    IconData? fallbackIcon,
  }) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      child: _buildImageContent(
        imagePath: imagePath,
        radius: radius,
        fallbackText: fallbackText,
        textColor: textColor,
        fallbackIcon: fallbackIcon,
      ),
    );
  }

  /// بناء محتوى الصورة
  static Widget _buildImageContent({
    String? imagePath,
    double radius = 25,
    String? fallbackText,
    Color? textColor,
    IconData? fallbackIcon,
  }) {
    // إذا لم يكن هناك مسار صورة، عرض النص أو الأيقونة الافتراضية
    if (imagePath == null || imagePath.isEmpty) {
      return _buildFallbackContent(
        fallbackText: fallbackText,
        textColor: textColor,
        fallbackIcon: fallbackIcon,
        radius: radius,
      );
    }

    // تحويل جميع المسارات إلى URLs للخادم - لا نتعامل مع ملفات محلية
    final imageUrl = getImageUrl(imagePath);
    if (imageUrl != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: Image.network(
          imageUrl,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return SizedBox(
              width: radius * 2,
              height: radius * 2,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  strokeWidth: 2,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint('خطأ في تحميل الصورة من الخادم: $error');
            return _buildFallbackContent(
              fallbackText: fallbackText,
              textColor: textColor,
              fallbackIcon: fallbackIcon,
              radius: radius,
            );
          },
        ),
      );
    }

    // في حالة عدم وجود مسار صالح، عرض المحتوى الاحتياطي
    return _buildFallbackContent(
      fallbackText: fallbackText,
      textColor: textColor,
      fallbackIcon: fallbackIcon,
      radius: radius,
    );
  }

  /// بناء المحتوى الاحتياطي
  static Widget _buildFallbackContent({
    String? fallbackText,
    Color? textColor,
    IconData? fallbackIcon,
    double radius = 25,
  }) {
    if (fallbackText != null && fallbackText.isNotEmpty) {
      return Text(
        fallbackText.substring(0, 1).toUpperCase(),
        style: TextStyle(
          color: textColor ?? Colors.white,
          fontSize: radius * 0.6,
          fontWeight: FontWeight.bold,
        ),
      );
    }
    return Icon(
      fallbackIcon ?? Icons.person,
      size: radius * 0.8,
      color: textColor ?? Colors.white,
    );
  }
}
