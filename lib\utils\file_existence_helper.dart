import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// أداة تساعد في التحقق من وجود الملف محليًا
class FileExistenceHelper {
  static Future<bool> fileExists(Attachment attachment) async {
    if (kIsWeb) return true; // في الويب نعتبر الملف دائمًا موجود (رابط)
    try {
      // تحقق أولاً من المسار الموجود في attachment.filePath
      final file = File(attachment.filePath);
      if (await file.exists()) return true;
      // إذا لم يوجد، تحقق من مجلد التنزيلات باسم الملف
      final downloadsDir = await getDownloadPath();
      final downloadFile = File(path.join(downloadsDir, attachment.fileName));
      return await downloadFile.exists();
    } catch (_) {
      return false;
    }
  }

  static Future<String> getDownloadPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        return directory != null ? '${directory.path}/Downloads' : '/storage/emulated/0/Download';
      } else if (Platform.isIOS) {
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      } else {
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      }
    } catch (_) {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
  }

  static Future<String?> getLocalFilePath(Attachment attachment) async {
    if (kIsWeb) return attachment.filePath;
    try {
      // تحقق أولاً من المسار الموجود في attachment.filePath
      final file = File(attachment.filePath);
      if (await file.exists()) return file.path;
      // إذا لم يوجد، تحقق من مجلد التنزيلات باسم الملف
      final downloadsDir = await getDownloadPath();
      final downloadFile = File(path.join(downloadsDir, attachment.fileName));
      if (await downloadFile.exists()) return downloadFile.path;
      return null;
    } catch (_) {
      return null;
    }
  }
}
