import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:math' as math;
import 'quill_error_handler.dart';
import 'editable_table_widget.dart';

/// معلومات الجدول في المستند
class TableInfo {
  final int startLine;
  final int endLine;
  final List<List<String>> data;
  final String originalText;

  TableInfo({
    required this.startLine,
    required this.endLine,
    required this.data,
    required this.originalText,
  });
}

/// مدير الجداول لمحرر Quill
/// يوفر وظائف متقدمة لإنشاء وإدارة الجداول في المحرر
class QuillTableManager {
  final QuillController controller;
  final BuildContext context;

  QuillTableManager({
    required this.controller,
    required this.context,
  });

  /// إدراج جدول جديد
  Future<void> insertTable() async {
    try {
      // عرض حوار تخصيص الجدول
      final tableConfig = await _showTableConfigDialog();
      if (tableConfig == null) return;

      // عرض الجدول القابل للتحرير في حوار
      await _showEditableTableDialog(
        tableConfig['rows'] as int,
        tableConfig['columns'] as int,
        tableConfig['hasHeader'] as bool,
      );

    } catch (e, stackTrace) {
      if (context.mounted) {
        QuillErrorHandler.handleTableError(
          context,
          e,
          stackTrace: stackTrace,
        );
      }
    }
  }

  /// عرض حوار الجدول القابل للتحرير
  Future<void> _showEditableTableDialog(int rows, int columns, bool hasHeader) async {
    List<List<String>>? finalTableData;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحرير الجدول'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.6,
          child: EditableTableWidget(
            initialRows: rows,
            initialColumns: columns,
            hasHeader: hasHeader,
            onDataChanged: (data) {
              finalTableData = data;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (finalTableData != null) {
                _insertTableIntoDocument(finalTableData!, hasHeader);
              }
            },
            child: const Text('إدراج الجدول'),
          ),
        ],
      ),
    );
  }

  /// إدراج الجدول في المستند
  void _insertTableIntoDocument(List<List<String>> tableData, bool hasHeader) {
    try {
      final index = controller.selection.baseOffset;

      // إدراج سطر فارغ قبل الجدول
      controller.document.insert(index, '\n');

      // إنشاء محتوى الجدول المحسن
      final tableContent = _generateEnhancedTable(tableData, hasHeader);

      // إدراج محتوى الجدول
      controller.document.insert(index + 1, tableContent);

      // إدراج سطر فارغ بعد الجدول
      controller.document.insert(index + 1 + tableContent.length, '\n');

      // تحديث موضع المؤشر
      controller.updateSelection(
        TextSelection.collapsed(offset: index + 1 + tableContent.length + 1),
        ChangeSource.local,
      );

      if (context.mounted) {
        QuillErrorHandler.showSuccess(
          context,
          'تم إدراج الجدول بنجاح',
        );
      }
    } catch (e) {
      if (context.mounted) {
        QuillErrorHandler.handleTableError(context, e);
      }
    }
  }

  /// إنشاء جدول محسن من البيانات
  String _generateEnhancedTable(List<List<String>> tableData, bool hasHeader) {
    if (tableData.isEmpty) return '';

    final buffer = StringBuffer();
    final columns = tableData[0].length;

    // حساب عرض كل عمود بناءً على أطول محتوى
    final columnWidths = List<int>.filled(columns, 10);
    for (final row in tableData) {
      for (int col = 0; col < row.length && col < columns; col++) {
        columnWidths[col] = math.max(columnWidths[col], row[col].length + 2);
      }
    }

    // إضافة عنوان الجدول
    final totalWidth = columnWidths.reduce((a, b) => a + b) + columns + 1;
    buffer.writeln('┌${'─' * (totalWidth - 2)}┐');
    buffer.writeln('│${' ' * ((totalWidth - 20) ~/ 2)}📊 جدول البيانات${' ' * ((totalWidth - 20) ~/ 2)}│');
    buffer.writeln('├${'─' * (totalWidth - 2)}┤');

    // إنشاء الجدول
    for (int rowIndex = 0; rowIndex < tableData.length; rowIndex++) {
      final row = tableData[rowIndex];

      // بناء الصف
      buffer.write('│');
      for (int colIndex = 0; colIndex < columns; colIndex++) {
        final cellContent = colIndex < row.length ? row[colIndex] : '';
        final paddedContent = cellContent.padRight(columnWidths[colIndex]);
        buffer.write(' $paddedContent');
        if (colIndex < columns - 1) buffer.write('│');
      }
      buffer.writeln(' │');

      // إضافة خط فاصل بعد العنوان أو بين الصفوف
      if ((hasHeader && rowIndex == 0) || (rowIndex < tableData.length - 1)) {
        buffer.write('├');
        for (int colIndex = 0; colIndex < columns; colIndex++) {
          buffer.write('─' * (columnWidths[colIndex] + 1));
          if (colIndex < columns - 1) {
            buffer.write(hasHeader && rowIndex == 0 ? '┼' : '┼');
          }
        }
        buffer.writeln('┤');
      }
    }

    // إغلاق الجدول
    buffer.write('└');
    for (int colIndex = 0; colIndex < columns; colIndex++) {
      buffer.write('─' * (columnWidths[colIndex] + 1));
      if (colIndex < columns - 1) buffer.write('┴');
    }
    buffer.writeln('┘');

    return buffer.toString();
  }

  /// البحث عن الجداول في المستند وتحريرها
  Future<void> editExistingTable() async {
    try {
      final plainText = controller.document.toPlainText();
      final tables = _findTablesInText(plainText);

      if (tables.isEmpty) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد جداول في المستند'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // عرض قائمة الجداول للاختيار
      final selectedTable = await _showTableSelectionDialog(tables);
      if (selectedTable != null) {
        await _editSelectedTable(selectedTable);
      }
    } catch (e) {
      if (context.mounted) {
        QuillErrorHandler.handleTableError(context, e);
      }
    }
  }

  /// البحث عن الجداول في النص
  List<TableInfo> _findTablesInText(String text) {
    final tables = <TableInfo>[];
    final lines = text.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      // البحث عن بداية الجدول
      if (line.contains('┌') && line.contains('─')) {
        final tableStart = i;
        int tableEnd = i;

        // البحث عن نهاية الجدول
        for (int j = i + 1; j < lines.length; j++) {
          if (lines[j].contains('└') && lines[j].contains('─')) {
            tableEnd = j;
            break;
          }
        }

        if (tableEnd > tableStart) {
          final tableLines = lines.sublist(tableStart, tableEnd + 1);
          final tableData = _parseTableFromLines(tableLines);

          if (tableData.isNotEmpty) {
            tables.add(TableInfo(
              startLine: tableStart,
              endLine: tableEnd,
              data: tableData,
              originalText: tableLines.join('\n'),
            ));
          }
        }
      }
    }

    return tables;
  }

  /// تحليل بيانات الجدول من الأسطر
  List<List<String>> _parseTableFromLines(List<String> lines) {
    final data = <List<String>>[];

    for (final line in lines) {
      if (line.contains('│') && !line.contains('─')) {
        // هذا سطر بيانات
        final cells = line.split('│')
            .map((cell) => cell.trim())
            .where((cell) => cell.isNotEmpty)
            .toList();

        if (cells.isNotEmpty) {
          data.add(cells);
        }
      }
    }

    return data;
  }

  /// عرض حوار اختيار الجدول
  Future<TableInfo?> _showTableSelectionDialog(List<TableInfo> tables) async {
    return await showDialog<TableInfo>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر جدول للتحرير'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: ListView.builder(
            itemCount: tables.length,
            itemBuilder: (context, index) {
              final table = tables[index];
              return ListTile(
                title: Text('جدول ${index + 1}'),
                subtitle: Text('${table.data.length} صف × ${table.data.isNotEmpty ? table.data[0].length : 0} عمود'),
                onTap: () => Navigator.of(context).pop(table),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// تحرير الجدول المحدد
  Future<void> _editSelectedTable(TableInfo tableInfo) async {
    List<List<String>>? updatedData;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحرير الجدول'),
        content: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.6,
          child: EditableTableWidget(
            initialRows: tableInfo.data.length,
            initialColumns: tableInfo.data.isNotEmpty ? tableInfo.data[0].length : 1,
            hasHeader: true,
            initialData: tableInfo.data,
            onDataChanged: (data) {
              updatedData = data;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (updatedData != null) {
                _replaceTableInDocument(tableInfo, updatedData!);
              }
            },
            child: const Text('حفظ التغييرات'),
          ),
        ],
      ),
    );
  }

  /// استبدال الجدول في المستند
  void _replaceTableInDocument(TableInfo oldTable, List<List<String>> newData) {
    try {
      final plainText = controller.document.toPlainText();
      final lines = plainText.split('\n');

      // إنشاء الجدول الجديد
      final newTableText = _generateEnhancedTable(newData, true);

      // استبدال الجدول القديم بالجديد
      final beforeTable = lines.sublist(0, oldTable.startLine).join('\n');
      final afterTable = lines.sublist(oldTable.endLine + 1).join('\n');

      final newContent = '$beforeTable\n$newTableText\n$afterTable';

      // تحديث المستند
      controller.document.delete(0, controller.document.length - 1);
      controller.document.insert(0, newContent);

      if (context.mounted) {
        QuillErrorHandler.showSuccess(
          context,
          'تم تحديث الجدول بنجاح',
        );
      }
    } catch (e) {
      if (context.mounted) {
        QuillErrorHandler.handleTableError(context, e);
      }
    }
  }



  /// عرض حوار تكوين الجدول
  Future<Map<String, dynamic>?> _showTableConfigDialog() async {
    int rows = 3;
    int columns = 3;
    bool hasHeader = true;

    return await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إعدادات الجدول'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عدد الصفوف
              Row(
                children: [
                  const Text('عدد الصفوف: '),
                  const Spacer(),
                  IconButton(
                    onPressed: rows > 1 ? () => setState(() => rows--) : null,
                    icon: const Icon(Icons.remove),
                  ),
                  Text('$rows'),
                  IconButton(
                    onPressed: rows < 10 ? () => setState(() => rows++) : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),

              // عدد الأعمدة
              Row(
                children: [
                  const Text('عدد الأعمدة: '),
                  const Spacer(),
                  IconButton(
                    onPressed: columns > 1 ? () => setState(() => columns--) : null,
                    icon: const Icon(Icons.remove),
                  ),
                  Text('$columns'),
                  IconButton(
                    onPressed: columns < 10 ? () => setState(() => columns++) : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),

              // صف العنوان
              CheckboxListTile(
                title: const Text('إضافة صف عنوان'),
                value: hasHeader,
                onChanged: (value) => setState(() => hasHeader = value ?? true),
              ),

              // معاينة الجدول
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معاينة:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'جدول ${rows}x$columns ${hasHeader ? 'مع عنوان' : 'بدون عنوان'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop({
                'rows': rows,
                'columns': columns,
                'hasHeader': hasHeader,
              }),
              child: const Text('إدراج'),
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء جدول HTML (للتصدير)
  String generateHtmlTable(int rows, int columns, bool hasHeader) {
    final buffer = StringBuffer();
    
    buffer.writeln('<table border="1" style="border-collapse: collapse; width: 100%;">');
    
    // إنشاء صف العنوان
    if (hasHeader) {
      buffer.writeln('  <thead>');
      buffer.writeln('    <tr>');
      for (int col = 1; col <= columns; col++) {
        buffer.writeln('      <th style="padding: 8px; background-color: #f5f5f5;">عنوان $col</th>');
      }
      buffer.writeln('    </tr>');
      buffer.writeln('  </thead>');
      rows--; // تقليل عدد الصفوف
    }
    
    // إنشاء صفوف البيانات
    buffer.writeln('  <tbody>');
    for (int row = 1; row <= rows; row++) {
      buffer.writeln('    <tr>');
      for (int col = 1; col <= columns; col++) {
        final cellContent = 'خلية ${hasHeader ? row + 1 : row}-$col';
        buffer.writeln('      <td style="padding: 8px;">$cellContent</td>');
      }
      buffer.writeln('    </tr>');
    }
    buffer.writeln('  </tbody>');
    
    buffer.writeln('</table>');
    
    return buffer.toString();
  }

  /// إنشاء جدول CSV (للتصدير)
  String generateCsvTable(int rows, int columns, bool hasHeader) {
    final buffer = StringBuffer();
    
    // إنشاء صف العنوان
    if (hasHeader) {
      final headers = List.generate(columns, (index) => 'عنوان ${index + 1}');
      buffer.writeln(headers.join(','));
      rows--; // تقليل عدد الصفوف
    }
    
    // إنشاء صفوف البيانات
    for (int row = 1; row <= rows; row++) {
      final cells = List.generate(columns, (col) {
        return 'خلية ${hasHeader ? row + 1 : row}-${col + 1}';
      });
      buffer.writeln(cells.join(','));
    }
    
    return buffer.toString();
  }

  /// إدراج جدول من CSV
  Future<void> insertTableFromCsv(String csvData) async {
    try {
      final lines = csvData.split('\n').where((line) => line.trim().isNotEmpty).toList();
      if (lines.isEmpty) return;

      final index = controller.selection.baseOffset;
      
      // إدراج سطر فارغ قبل الجدول
      controller.document.insert(index, '\n');
      
      final buffer = StringBuffer();
      buffer.writeln('┌─ جدول من CSV ─┐');
      
      for (int i = 0; i < lines.length; i++) {
        final cells = lines[i].split(',');
        buffer.write('│ ');
        
        for (int j = 0; j < cells.length; j++) {
          buffer.write(cells[j].trim().padRight(15));
          if (j < cells.length - 1) buffer.write('│ ');
        }
        buffer.writeln(' │');
        
        // خط فاصل بين الصفوف
        if (i < lines.length - 1) {
          buffer.write('├');
          for (int j = 0; j < cells.length; j++) {
            buffer.write('─' * 15);
            if (j < cells.length - 1) buffer.write('┼');
          }
          buffer.writeln('┤');
        }
      }
      
      // إغلاق الجدول
      final firstLineCells = lines[0].split(',');
      buffer.write('└');
      for (int j = 0; j < firstLineCells.length; j++) {
        buffer.write('─' * 15);
        if (j < firstLineCells.length - 1) buffer.write('┴');
      }
      buffer.writeln('┘');
      
      final tableContent = buffer.toString();
      controller.document.insert(index + 1, tableContent);
      controller.document.insert(index + 1 + tableContent.length, '\n');
      
      // تحديث موضع المؤشر
      controller.updateSelection(
        TextSelection.collapsed(offset: index + 1 + tableContent.length + 1),
        ChangeSource.local,
      );

      QuillErrorHandler.showSuccess(context, 'تم إدراج الجدول من CSV بنجاح');

    } catch (e, stackTrace) {
      QuillErrorHandler.handleTableError(context, e, stackTrace: stackTrace);
    }
  }
}
