import 'user_model.dart';

/// نموذج فئة الأرشيف
class ArchiveCategory {
  final int id;
  final String name;
  final String? description;
  final int? parentId;
  final String? color;
  final String? icon;
  final bool isActive;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final User? createdByUser;
  final ArchiveCategory? parent;
  final List<ArchiveCategory>? children;
  final List<ArchiveDocument>? documents;

  const ArchiveCategory({
    required this.id,
    required this.name,
    this.description,
    this.parentId,
    this.color,
    this.icon,
    this.isActive = true,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.createdByUser,
    this.parent,
    this.children,
    this.documents,
  });

  factory ArchiveCategory.fromJson(Map<String, dynamic> json) {
    return ArchiveCategory(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      parentId: json['parentId'] as int?,
      color: json['color'] as String?,
      icon: json['icon'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      parent: json['parent'] != null 
          ? ArchiveCategory.fromJson(json['parent'] as Map<String, dynamic>)
          : null,
      children: json['children'] != null 
          ? (json['children'] as List)
              .map((c) => ArchiveCategory.fromJson(c as Map<String, dynamic>))
              .toList()
          : null,
      documents: json['archiveDocuments'] != null 
          ? (json['archiveDocuments'] as List)
              .map((d) => ArchiveDocument.fromJson(d as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'parentId': parentId,
      'color': color,
      'icon': icon,
      'isActive': isActive,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }

  /// التحقق من كون الفئة فئة جذر
  bool get isRoot => parentId == null;

  /// الحصول على عدد المستندات
  int get documentCount => documents?.length ?? 0;

  /// الحصول على عدد الفئات الفرعية
  int get childrenCount => children?.length ?? 0;
}

/// نموذج مستند الأرشيف
class ArchiveDocument {
  final int id;
  final String title;
  final String? description;
  final String fileName;
  final String filePath;
  final String fileType;
  final int fileSize;
  final int? categoryId;
  final String? metadata;
  final String? content;
  final int uploadedBy;
  final int uploadedAt;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final ArchiveCategory? category;
  final User? uploadedByUser;
  final List<ArchiveTag>? tags;

  const ArchiveDocument({
    required this.id,
    required this.title,
    this.description,
    required this.fileName,
    required this.filePath,
    required this.fileType,
    required this.fileSize,
    this.categoryId,
    this.metadata,
    this.content,
    required this.uploadedBy,
    required this.uploadedAt,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.category,
    this.uploadedByUser,
    this.tags,
  });

  factory ArchiveDocument.fromJson(Map<String, dynamic> json) {
    return ArchiveDocument(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileType: json['fileType'] as String,
      fileSize: json['fileSize'] as int,
      categoryId: json['categoryId'] as int?,
      metadata: json['metadata'] as String?,
      content: json['content'] as String?,
      uploadedBy: json['uploadedBy'] as int,
      uploadedAt: json['uploadedAt'] as int,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      category: json['category'] != null
          ? ArchiveCategory.fromJson(json['category'] as Map<String, dynamic>)
          : null,
      uploadedByUser: json['uploadedByNavigation'] != null
          ? User.fromJson(json['uploadedByNavigation'] as Map<String, dynamic>)
          : null,
      tags: json['tags'] != null
          ? (json['tags'] as List)
              .map((t) => ArchiveTag.fromJson(t as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'fileName': fileName,
      'filePath': filePath,
      'fileType': fileType,
      'fileSize': fileSize,
      'categoryId': categoryId,
      'metadata': metadata,
      'content': content,
      'uploadedBy': uploadedBy,
      'uploadedAt': uploadedAt,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على تاريخ الرفع كـ DateTime
  DateTime get uploadedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(uploadedAt * 1000);

  /// التحقق من كون الملف صورة
  bool get isImage => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(fileType.toLowerCase());

  /// التحقق من كون الملف PDF
  bool get isPdf => fileType.toLowerCase() == 'pdf';

  /// التحقق من كون الملف مستند نصي
  bool get isDocument => ['doc', 'docx', 'txt', 'rtf', 'odt'].contains(fileType.toLowerCase());
}

/// نموذج علامة الأرشيف
class ArchiveTag {
  final int id;
  final String name;
  final String? description;
  final String? color;
  final bool isActive;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final User? createdByUser;

  const ArchiveTag({
    required this.id,
    required this.name,
    this.description,
    this.color,
    this.isActive = true,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.createdByUser,
  });

  factory ArchiveTag.fromJson(Map<String, dynamic> json) {
    return ArchiveTag(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: json['color'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      createdByUser: json['createdByNavigation'] != null
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'isActive': isActive,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }
}

// تم نقل نموذج ArchiveDocumentTag إلى ملف منفصل: archive_document_tag_models.dart

/// نموذج طلب إنشاء فئة أرشيف
class CreateArchiveCategoryRequest {
  final String name;
  final String? description;
  final int? parentId;
  final String? color;
  final String? icon;

  const CreateArchiveCategoryRequest({
    required this.name,
    this.description,
    this.parentId,
    this.color,
    this.icon,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'parentId': parentId,
      'color': color,
      'icon': icon,
    };
  }
}

/// نموذج طلب رفع مستند أرشيف
class UploadArchiveDocumentRequest {
  final String title;
  final String? description;
  final int categoryId;
  final String? version;
  final bool isPublic;
  final List<int>? tagIds;

  const UploadArchiveDocumentRequest({
    required this.title,
    this.description,
    required this.categoryId,
    this.version,
    this.isPublic = false,
    this.tagIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'categoryId': categoryId,
      'version': version,
      'isPublic': isPublic,
      'tagIds': tagIds,
    };
  }
}

/// نموذج طلب إنشاء علامة أرشيف
class CreateArchiveTagRequest {
  final String name;
  final String? color;

  const CreateArchiveTagRequest({
    required this.name,
    this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'color': color,
    };
  }
}
