import 'package:flutter_application_2/models/message_models.dart';
import 'package:flutter_application_2/services/api/messages_api_service.dart';

/// خدمة إدارة الرسائل المثبتة
/// 
/// توفر هذه الخدمة واجهة مبسطة للتعامل مع الرسائل المثبتة
/// وتستخدم MessagesApiService للتواصل مع الـ API
class PinnedMessageService {
  final MessagesApiService _messagesApiService = MessagesApiService();

  /// الحصول على الرسائل المثبتة لمجموعة محددة
  /// 
  /// [groupId] معرف المجموعة
  /// يرجع قائمة بالرسائل المثبتة في المجموعة
  Future<List<Message>> getPinnedMessagesForGroup(String groupId) async {
    try {
      // تحويل groupId من String إلى int
      final groupIdInt = int.parse(groupId);
      
      // الحصول على الرسائل المثبتة من الـ API
      final pinnedMessages = await _messagesApiService.getPinnedMessages(groupIdInt);
      
      return pinnedMessages;
    } catch (e) {
      // في حالة الخطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// تثبيت رسالة
  ///
  /// [messageId] معرف الرسالة
  /// [userId] معرف المستخدم الذي يقوم بالتثبيت
  /// يرجع true في حالة النجاح، false في حالة الفشل
  Future<bool> pinMessage(int messageId, String userId) async {
    try {
      // تحويل userId من String إلى int
      final userIdInt = int.parse(userId);
      return await _messagesApiService.pinMessage(messageId, userIdInt);
    } catch (e) {
      return false;
    }
  }

  /// إلغاء تثبيت رسالة
  ///
  /// [messageId] معرف الرسالة
  /// [userId] معرف المستخدم الذي يقوم بإلغاء التثبيت
  /// يرجع true في حالة النجاح، false في حالة الفشل
  Future<bool> unpinMessage(int messageId, String userId) async {
    try {
      // تحويل userId من String إلى int
      final userIdInt = int.parse(userId);
      return await _messagesApiService.unpinMessage(messageId, userIdInt);
    } catch (e) {
      return false;
    }
  }

  /// الحصول على عدد الرسائل المثبتة في مجموعة
  /// 
  /// [groupId] معرف المجموعة
  /// يرجع عدد الرسائل المثبتة
  Future<int> getPinnedMessageCount(String groupId) async {
    try {
      final pinnedMessages = await getPinnedMessagesForGroup(groupId);
      return pinnedMessages.length;
    } catch (e) {
      return 0;
    }
  }

  /// التحقق من كون رسالة مثبتة أم لا
  /// 
  /// [messageId] معرف الرسالة
  /// يرجع true إذا كانت الرسالة مثبتة، false إذا لم تكن مثبتة
  Future<bool> isMessagePinned(int messageId) async {
    try {
      final message = await _messagesApiService.getMessageById(messageId);
      return message?.isPinned ?? false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على آخر الرسائل المثبتة
  /// 
  /// [groupId] معرف المجموعة
  /// [limit] عدد الرسائل المطلوب إرجاعها (افتراضي: 10)
  /// يرجع قائمة بآخر الرسائل المثبتة
  Future<List<Message>> getRecentPinnedMessages(String groupId, {int limit = 10}) async {
    try {
      final pinnedMessages = await getPinnedMessagesForGroup(groupId);
      
      // ترتيب الرسائل حسب تاريخ التثبيت (الأحدث أولاً)
      pinnedMessages.sort((a, b) {
        final aPinnedAt = a.pinnedAt ?? a.createdAt;
        final bPinnedAt = b.pinnedAt ?? b.createdAt;
        return bPinnedAt.compareTo(aPinnedAt);
      });
      
      // إرجاع العدد المطلوب فقط
      return pinnedMessages.take(limit).toList();
    } catch (e) {
      return [];
    }
  }
}
