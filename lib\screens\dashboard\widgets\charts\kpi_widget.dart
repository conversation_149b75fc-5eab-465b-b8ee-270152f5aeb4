import 'package:flutter/material.dart';
import '../../models/dashboard_models.dart';
import '../base_chart_widget.dart';
import '../../../../constants/app_styles.dart';
import '../../../../constants/app_colors.dart';

/// مكون مؤشر الأداء (KPI) بسيط باستخدام BaseChartWidget
class KpiWidget extends BaseChartWidget {
  const KpiWidget({
    super.key,
    required super.item,
    super.filters,
    super.showHeader = true,
    super.showFilters = false,
    super.onRefresh,
  });

  @override
  Widget buildChartContent(BuildContext context, List<ChartData> data) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        return _buildKpiCard(item, index);
      },
    );
  }

  /// بناء بطاقة مؤشر الأداء
  Widget _buildKpiCard(ChartData item, int index) {
    final color = item.color ?? _getDefaultColor(index);
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.1),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _getKpiIcon(item.label),
                color: color,
                size: 24,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // القيمة
            Text(
              _formatValue(item.value),
              style: AppStyles.headingLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 4),
            
            // التسمية
            Text(
              item.label,
              style: AppStyles.bodySmall.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق القيمة
  String _formatValue(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}م';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}ك';
    } else {
      return value.toInt().toString();
    }
  }

  /// الحصول على أيقونة مؤشر الأداء
  IconData _getKpiIcon(String label) {
    final lowerLabel = label.toLowerCase();
    
    if (lowerLabel.contains('مهام') || lowerLabel.contains('task')) {
      return Icons.task_alt;
    } else if (lowerLabel.contains('مستخدم') || lowerLabel.contains('user')) {
      return Icons.people;
    } else if (lowerLabel.contains('قسم') || lowerLabel.contains('department')) {
      return Icons.business;
    } else if (lowerLabel.contains('مكتمل') || lowerLabel.contains('complete')) {
      return Icons.check_circle;
    } else if (lowerLabel.contains('معلق') || lowerLabel.contains('pending')) {
      return Icons.pending;
    } else if (lowerLabel.contains('متأخر') || lowerLabel.contains('overdue')) {
      return Icons.warning;
    } else if (lowerLabel.contains('نشاط') || lowerLabel.contains('activity')) {
      return Icons.trending_up;
    } else if (lowerLabel.contains('تقرير') || lowerLabel.contains('report')) {
      return Icons.analytics;
    } else {
      return Icons.bar_chart;
    }
  }

  /// الحصول على لون افتراضي
  Color _getDefaultColor(int index) {
    final colors = [
      AppColors.primary,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.amber,
      Colors.indigo,
    ];
    return colors[index % colors.length];
  }
}
