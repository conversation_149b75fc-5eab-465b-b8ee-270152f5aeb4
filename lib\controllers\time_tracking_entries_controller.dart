import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/time_tracking_models.dart';
import '../services/api/time_tracking_api_service.dart';

/// متحكم إدخالات تتبع الوقت
class TimeTrackingEntriesController extends GetxController {
  final TimeTrackingApiService _apiService = TimeTrackingApiService();

  // قوائم إدخالات تتبع الوقت
  final RxList<TimeTrackingEntry> _allEntries = <TimeTrackingEntry>[].obs;
  final RxList<TimeTrackingEntry> _filteredEntries = <TimeTrackingEntry>[].obs;
  final RxList<TimeTrackingEntry> _myEntries = <TimeTrackingEntry>[].obs;
  final RxList<TimeTrackingEntry> _taskEntries = <TimeTrackingEntry>[].obs;

  // الإدخال الحالي
  final Rx<TimeTrackingEntry?> _currentEntry = Rx<TimeTrackingEntry?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _taskFilter = Rx<int?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<DateTime?> _dateFilter = Rx<DateTime?>(null);
  final RxBool _showMyEntriesOnly = false.obs;

  // إحصائيات
  final RxDouble _totalHours = 0.0.obs;
  final RxDouble _todayHours = 0.0.obs;
  final RxDouble _weekHours = 0.0.obs;

  // Getters
  List<TimeTrackingEntry> get allEntries => _allEntries;
  List<TimeTrackingEntry> get filteredEntries => _filteredEntries;
  List<TimeTrackingEntry> get myEntries => _myEntries;
  List<TimeTrackingEntry> get taskEntries => _taskEntries;
  TimeTrackingEntry? get currentEntry => _currentEntry.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get taskFilter => _taskFilter.value;
  int? get userFilter => _userFilter.value;
  DateTime? get dateFilter => _dateFilter.value;
  bool get showMyEntriesOnly => _showMyEntriesOnly.value;
  double get totalHours => _totalHours.value;
  double get todayHours => _todayHours.value;
  double get weekHours => _weekHours.value;

  @override
  void onInit() {
    super.onInit();
    loadAllEntries();
    loadMyEntries();
    loadStatistics();
  }

  /// تحميل جميع إدخالات تتبع الوقت
  Future<void> loadAllEntries() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final entries = await _apiService.getAllTimeEntries();
      _allEntries.assignAll(entries);
      _applyFilters();
      debugPrint('تم تحميل ${entries.length} إدخال تتبع وقت');
    } catch (e) {
      _error.value = 'خطأ في تحميل إدخالات تتبع الوقت: $e';
      debugPrint('خطأ في تحميل إدخالات تتبع الوقت: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل إدخالاتي
  Future<void> loadMyEntries() async {
    try {
      // سنحتاج لمعرف المستخدم الحالي - يمكن الحصول عليه من AuthController
      // مؤقتاً سنستخدم جميع الإدخالات
      final entries = await _apiService.getAllTimeEntries();
      _myEntries.assignAll(entries);
      debugPrint('تم تحميل ${entries.length} من إدخالاتي');
    } catch (e) {
      debugPrint('خطأ في تحميل إدخالاتي: $e');
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getTimeTrackingStatistics();
      if (stats != null) {
        _totalHours.value = (stats['totalHours'] as num?)?.toDouble() ?? 0.0;
        _todayHours.value = (stats['todayHours'] as num?)?.toDouble() ?? 0.0;
        _weekHours.value = (stats['weekHours'] as num?)?.toDouble() ?? 0.0;
      }
      debugPrint('تم تحميل إحصائيات تتبع الوقت');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات تتبع الوقت: $e');
    }
  }

  /// الحصول على إدخال تتبع وقت بالمعرف
  Future<void> getEntryById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final entry = await _apiService.getTimeEntryById(id);
      _currentEntry.value = entry;
      debugPrint('تم تحميل إدخال تتبع الوقت');
    } catch (e) {
      _error.value = 'خطأ في تحميل إدخال تتبع الوقت: $e';
      debugPrint('خطأ في تحميل إدخال تتبع الوقت: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء إدخال تتبع وقت جديد
  Future<bool> createEntry(TimeTrackingEntry entry) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newEntry = await _apiService.createTimeEntry(entry);
      if (newEntry != null) {
        _allEntries.add(newEntry);
        _applyFilters();
        await loadMyEntries();
        await loadStatistics();
        debugPrint('تم إنشاء إدخال تتبع وقت جديد');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء إدخال تتبع الوقت: $e';
      debugPrint('خطأ في إنشاء إدخال تتبع الوقت: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إدخال تتبع وقت
  Future<bool> updateEntry(int id, TimeTrackingEntry entry) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedEntry = await _apiService.updateTimeEntry(entry);
      if (updatedEntry != null) {
        final index = _allEntries.indexWhere((e) => e.id == id);
        if (index != -1) {
          _allEntries[index] = updatedEntry;
          _applyFilters();
        }
      }
      await loadMyEntries();
      await loadStatistics();
      debugPrint('تم تحديث إدخال تتبع الوقت');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث إدخال تتبع الوقت: $e';
      debugPrint('خطأ في تحديث إدخال تتبع الوقت: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف إدخال تتبع وقت
  Future<bool> deleteEntry(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _apiService.deleteTimeEntry(id);
      if (success) {
        _allEntries.removeWhere((e) => e.id == id);
        _applyFilters();
        await loadMyEntries();
        await loadStatistics();
        debugPrint('تم حذف إدخال تتبع الوقت');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف إدخال تتبع الوقت: $e';
      debugPrint('خطأ في حذف إدخال تتبع الوقت: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على إدخالات المهمة
  Future<void> getEntriesByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final entries = await _apiService.getTimeEntriesByTask(taskId);
      _taskEntries.assignAll(entries);
      debugPrint('تم تحميل ${entries.length} إدخال للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل إدخالات المهمة: $e';
      debugPrint('خطأ في تحميل إدخالات المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// بدء تتبع الوقت
  Future<bool> startTracking(int taskId, String description) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final entry = await _apiService.startTimeTracking(taskId, description);
      if (entry != null) {
        _allEntries.add(entry);
        _applyFilters();
        await loadMyEntries();
        debugPrint('تم بدء تتبع الوقت للمهمة $taskId');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في بدء تتبع الوقت: $e';
      debugPrint('خطأ في بدء تتبع الوقت: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إيقاف تتبع الوقت
  Future<bool> stopTracking(int entryId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final entry = await _apiService.stopTimeTracking(entryId);
      if (entry != null) {
        final index = _allEntries.indexWhere((e) => e.id == entryId);
        if (index != -1) {
          _allEntries[index] = entry;
          _applyFilters();
        }
        await loadMyEntries();
        await loadStatistics();
        debugPrint('تم إيقاف تتبع الوقت');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إيقاف تتبع الوقت: $e';
      debugPrint('خطأ في إيقاف تتبع الوقت: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allEntries.where((entry) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!(entry.description?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // مرشح المهمة
      if (_taskFilter.value != null && entry.taskId != _taskFilter.value) {
        return false;
      }

      // مرشح المستخدم
      if (_userFilter.value != null && entry.userId != _userFilter.value) {
        return false;
      }

      // مرشح التاريخ
      if (_dateFilter.value != null) {
        final entryDate = DateTime.fromMillisecondsSinceEpoch(entry.startTime * 1000);
        final filterDate = _dateFilter.value!;
        if (entryDate.year != filterDate.year ||
            entryDate.month != filterDate.month ||
            entryDate.day != filterDate.day) {
          return false;
        }
      }

      // مرشح إدخالاتي فقط
      if (_showMyEntriesOnly.value) {
        if (!_myEntries.any((myEntry) => myEntry.id == entry.id)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredEntries.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskFilter.value = taskId;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ
  void setDateFilter(DateTime? date) {
    _dateFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح إدخالاتي فقط
  void setMyEntriesFilter(bool showMyEntriesOnly) {
    _showMyEntriesOnly.value = showMyEntriesOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _taskFilter.value = null;
    _userFilter.value = null;
    _dateFilter.value = null;
    _showMyEntriesOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllEntries(),
      loadMyEntries(),
      loadStatistics(),
    ]);
  }
}
