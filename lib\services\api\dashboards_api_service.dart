import 'package:get/get.dart';
import '../../models/dashboard_models.dart';
import 'base_api_service.dart';

/// خدمة API للوحات المعلومات - متوافقة مع ASP.NET Core
class DashboardsApiService extends GetxService {
  late final BaseApiService _apiService;

  DashboardsApiService() {
    // محاولة الحصول على BaseApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<BaseApiService>();
    } catch (e) {
      _apiService = BaseApiService();
    }
  }

  /// الحصول على جميع لوحات المعلومات
  Future<List<Dashboard>> getAllDashboards() async {
    try {
      final response = await _apiService.get('/dashboards');
      return _apiService.handleListResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل لوحات المعلومات: $e');
    }
  }

  /// الحصول على لوحة معلومات بالمعرف
  Future<Dashboard> getDashboardById(int id) async {
    try {
      final response = await _apiService.get('/dashboards/$id');
      return _apiService.handleResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل لوحة المعلومات: $e');
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<Dashboard> createDashboard(Dashboard dashboard) async {
    try {
      final response = await _apiService.post('/dashboards', dashboard.toJson());
      return _apiService.handleResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء لوحة المعلومات: $e');
    }
  }

  /// تحديث لوحة معلومات
  Future<Dashboard> updateDashboard(Dashboard dashboard) async {
    try {
      final response = await _apiService.put('/dashboards/${dashboard.id}', dashboard.toJson());
      return _apiService.handleResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحديث لوحة المعلومات: $e');
    }
  }

  /// حذف لوحة معلومات
  Future<void> deleteDashboard(int id) async {
    try {
      await _apiService.delete('/dashboards/$id');
    } catch (e) {
      throw Exception('خطأ في حذف لوحة المعلومات: $e');
    }
  }

  /// الحصول على لوحات المعلومات الخاصة بالمستخدم
  Future<List<Dashboard>> getMyDashboards() async {
    try {
      final response = await _apiService.get('/dashboards/my');
      return _apiService.handleListResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل لوحات المعلومات الخاصة بي: $e');
    }
  }

  /// الحصول على لوحات المعلومات العامة
  Future<List<Dashboard>> getPublicDashboards() async {
    try {
      final response = await _apiService.get('/dashboards/public');
      return _apiService.handleListResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل لوحات المعلومات العامة: $e');
    }
  }

  /// مشاركة لوحة معلومات
  Future<void> shareDashboard(int dashboardId, List<int> userIds) async {
    try {
      await _apiService.post('/dashboards/$dashboardId/share', {
        'userIds': userIds,
      });
    } catch (e) {
      throw Exception('خطأ في مشاركة لوحة المعلومات: $e');
    }
  }

  /// إلغاء مشاركة لوحة معلومات
  Future<void> unshareDashboard(int dashboardId, int userId) async {
    try {
      await _apiService.delete('/dashboards/$dashboardId/share/$userId');
    } catch (e) {
      throw Exception('خطأ في إلغاء مشاركة لوحة المعلومات: $e');
    }
  }

  /// نسخ لوحة معلومات
  Future<Dashboard> duplicateDashboard(int dashboardId, String newName) async {
    try {
      final response = await _apiService.post('/dashboards/$dashboardId/duplicate', {
        'name': newName,
      });
      return _apiService.handleResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في نسخ لوحة المعلومات: $e');
    }
  }

  /// تصدير لوحة معلومات
  Future<String> exportDashboard(int dashboardId, String format) async {
    try {
      final response = await _apiService.get('/dashboards/$dashboardId/export?format=$format');
      return _apiService.handleResponse<String>(
        response,
        (json) => json['downloadUrl'] as String,
      );
    } catch (e) {
      throw Exception('خطأ في تصدير لوحة المعلومات: $e');
    }
  }

  /// استيراد لوحة معلومات
  Future<Dashboard> importDashboard(Map<String, dynamic> dashboardData) async {
    try {
      final response = await _apiService.post('/dashboards/import', dashboardData);
      return _apiService.handleResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في استيراد لوحة المعلومات: $e');
    }
  }

  /// البحث في لوحات المعلومات
  Future<List<Dashboard>> searchDashboards(String query) async {
    try {
      final response = await _apiService.get('/dashboards/search?q=$query');
      return _apiService.handleListResponse<Dashboard>(
        response,
        (json) => Dashboard.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في البحث في لوحات المعلومات: $e');
    }
  }

  /// الحصول على إحصائيات لوحة المعلومات
  Future<Map<String, dynamic>> getDashboardStats(int dashboardId) async {
    try {
      final response = await _apiService.get('/dashboards/$dashboardId/stats');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات لوحة المعلومات: $e');
    }
  }
}
