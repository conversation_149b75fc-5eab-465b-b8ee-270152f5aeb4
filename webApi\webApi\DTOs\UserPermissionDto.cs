using System.ComponentModel.DataAnnotations;

namespace webApi.DTOs
{
    /// <summary>
    /// DTO لإضافة صلاحية مستخدم - بدون Navigation Properties
    /// </summary>
    public class UserPermissionDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "معرف المستخدم مطلوب")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "معرف الصلاحية مطلوب")]
        public int PermissionId { get; set; }

        [Required(ErrorMessage = "معرف المانح مطلوب")]
        public int GrantedBy { get; set; }

        public long GrantedAt { get; set; }

        public bool IsActive { get; set; } = true;

        public long? ExpiresAt { get; set; }

        public bool IsDeleted { get; set; } = false;

        public long CreatedAt { get; set; }
    }
}
