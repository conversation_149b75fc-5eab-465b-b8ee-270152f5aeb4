import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/notification_models.dart';
import '../services/api/notifications_api_service.dart';
import '../controllers/auth_controller.dart';

/// متحكم الإشعارات - متوافق مع ASP.NET Core API
class NotificationController extends GetxController {
  final NotificationsApiService _apiService = NotificationsApiService();

  // قوائم البيانات
  final RxList<NotificationModel> _notifications = <NotificationModel>[].obs;
  final RxList<NotificationModel> _unreadNotifications = <NotificationModel>[].obs;

  // حالات التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get unreadNotifications => _unreadNotifications;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  int get unreadCount => _unreadNotifications.length;

  @override
  void onInit() {
    super.onInit();
    final authController = Get.find<AuthController>();
    // تحميل الإشعارات فقط عند توفر مستخدم مسجل
    ever(authController.currentUser, (user) {
      if (user != null) {
        loadNotifications(user.id);
        loadUnreadNotifications(user.id);
      } else {
        _notifications.clear();
        _unreadNotifications.clear();
      }
    });
  }

  /// تحميل جميع الإشعارات للمستخدم
  Future<void> loadNotifications(int userId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // استخدام getNotificationsByUserId
      final notificationModels = await _apiService.getNotificationsByUserId(userId);

      _notifications.assignAll(notificationModels);

      // تحديث الإشعارات غير المقروءة
      _updateUnreadNotifications();

      debugPrint('تم تحميل ${notificationModels.length} إشعار');
    } catch (e) {
      _error.value = 'خطأ في تحميل الإشعارات: $e';
      debugPrint(_error.value);
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإشعارات غير المقروءة فقط
  Future<void> loadUnreadNotifications(int userId) async {
    try {
      final unreadNotificationModels = await _apiService.getUnreadNotifications(userId);
      _unreadNotifications.assignAll(unreadNotificationModels);
      debugPrint('تم تحميل ${unreadNotificationModels.length} إشعار غير مقروء');
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات غير المقروءة: $e');
    }
  }

  /// إنشاء إشعار جديد
  Future<bool> createNotification({
    required int userId,
    required String title,
    required String message,
    String? type,
    String? actionUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // إنشاء NotificationModel مباشرة
      final notificationModel = NotificationModel(
        id: 0, // سيتم تعيينه من الخادم
        userId: userId,
        title: title,
        content: message,
        type: type ?? 'general',
        createdAt: DateTime.now(),
      );

      final createdNotification = await _apiService.createNotification(notificationModel);

      _notifications.insert(0, createdNotification);

      if (!createdNotification.isRead) {
        _unreadNotifications.insert(0, createdNotification);
      }

      debugPrint('تم إنشاء إشعار جديد: ${createdNotification.title}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء الإشعار: $e');
      return false;
    }
  }

  /// تحديد إشعار كمقروء
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
      // تحديث الحالة محلياً
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications[index];
        final updatedNotification = notification.copyWith(isRead: true);
        _notifications[index] = updatedNotification;
        // إزالة من قائمة غير المقروءة
        _unreadNotifications.removeWhere((n) => n.id == notificationId);
      }
      // إعادة تحميل الإشعارات من السيرفر لضمان التزامن
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      if (userId != null) {
        await loadNotifications(userId);
        await loadUnreadNotifications(userId);
      }
      debugPrint('تم تحديد الإشعار كمقروء');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllAsRead() async {
    try {
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      if (userId == null) {
        debugPrint('لا يوجد مستخدم مسجل حالياً');
        return false;
      }
      await _apiService.markAllAsRead(userId);

      // تحديث الحالة محلياً
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(isRead: true);
        }
      }

      // مسح قائمة غير المقروءة
      _unreadNotifications.clear();

      debugPrint('تم تحديد جميع الإشعارات كمقروءة');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديد جميع الإشعارات كمقروءة: $e');
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(int notificationId) async {
    try {
      await _apiService.deleteNotification(notificationId);
      
      // إزالة من القوائم محلياً
      _notifications.removeWhere((n) => n.id == notificationId);
      _unreadNotifications.removeWhere((n) => n.id == notificationId);
      
      debugPrint('تم حذف الإشعار');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار: $e');
      return false;
    }
  }

  /// تحديث قائمة الإشعارات غير المقروءة
  void _updateUnreadNotifications() {
    final unread = _notifications.where((n) => !n.isRead).toList();
    _unreadNotifications.assignAll(unread);
  }

  /// تحديث الإشعارات
  Future<void> refreshNotifications(int userId) async {
    await loadNotifications(userId);
  }

  /// مسح رسائل الخطأ
  void clearError() {
    _error.value = '';
  }
}
