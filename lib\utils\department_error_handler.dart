import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// معالج أخطاء الأقسام المحسن
class DepartmentErrorHandler {
  /// معالجة أخطاء API
  static void handleApiError(dynamic error, {String? context}) {
    String message = 'حدث خطأ غير متوقع';
    Color backgroundColor = Colors.red.shade100;
    Color textColor = Colors.red.shade800;
    IconData icon = Icons.error;

    if (error.toString().contains('SocketException')) {
      message = 'لا يمكن الاتصال بالخادم. تحقق من اتصال السرفر.';
      icon = Icons.wifi_off;
    } else if (error.toString().contains('TimeoutException')) {
      message = 'انتهت مهلة الاتصال. حاول مرة أخرى.';
      icon = Icons.timer_off;
    } else if (error.toString().contains('FormatException')) {
      message = 'خطأ في تنسيق البيانات المستلمة من الخادم.';
      icon = Icons.data_usage;
    } else if (error.toString().contains('401')) {
      message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
      icon = Icons.lock;
    } else if (error.toString().contains('403')) {
      message = 'ليس لديك صلاحية للوصول إلى هذه الميزة.';
      icon = Icons.security;
    } else if (error.toString().contains('404')) {
      message = 'البيانات المطلوبة غير موجودة.';
      icon = Icons.search_off;
    } else if (error.toString().contains('500')) {
      message = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
      icon = Icons.dns;
    }

    if (context != null) {
      message = '$context: $message';
    }

    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: backgroundColor,
      colorText: textColor,
      icon: Icon(icon, color: textColor),
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// معالجة أخطاء التحقق من صحة البيانات
  static void handleValidationError(String field, String message) {
    Get.snackbar(
      'خطأ في البيانات',
      '$field: $message',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.shade100,
      colorText: Colors.orange.shade800,
      icon: Icon(Icons.warning, color: Colors.orange.shade800),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// عرض رسالة نجاح
  static void showSuccess(String message) {
    Get.snackbar(
      'تم بنجاح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      icon: Icon(Icons.check_circle, color: Colors.green.shade800),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// عرض رسالة تحذير
  static void showWarning(String message) {
    Get.snackbar(
      'تحذير',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.amber.shade100,
      colorText: Colors.amber.shade800,
      icon: Icon(Icons.warning_amber, color: Colors.amber.shade800),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// عرض رسالة معلومات
  static void showInfo(String message) {
    Get.snackbar(
      'معلومات',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
      icon: Icon(Icons.info, color: Colors.blue.shade800),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// التحقق من صحة بيانات القسم
  static String? validateDepartmentName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'اسم القسم مطلوب';
    }
    if (name.trim().length < 2) {
      return 'اسم القسم يجب أن يكون أكثر من حرفين';
    }
    if (name.trim().length > 100) {
      return 'اسم القسم يجب أن يكون أقل من 100 حرف';
    }
    return null;
  }

  /// التحقق من صحة وصف القسم
  static String? validateDepartmentDescription(String? description) {
    if (description != null && description.trim().length > 500) {
      return 'وصف القسم يجب أن يكون أقل من 500 حرف';
    }
    return null;
  }

  /// التحقق من صحة مستوى القسم
  static String? validateDepartmentLevel(int? level) {
    if (level != null && (level < 0 || level > 10)) {
      return 'مستوى القسم يجب أن يكون بين 0 و 10';
    }
    return null;
  }

  /// التحقق من صحة ترتيب القسم
  static String? validateDepartmentSortOrder(int? sortOrder) {
    if (sortOrder != null && sortOrder < 0) {
      return 'ترتيب القسم يجب أن يكون رقم موجب';
    }
    return null;
  }

  /// عرض حوار تأكيد الحذف
  static Future<bool> confirmDelete(String departmentName) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القسم "$departmentName"؟\n\nهذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// عرض حوار تأكيد إلغاء التفعيل
  static Future<bool> confirmDeactivate(String departmentName) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد إلغاء التفعيل'),
        content: Text('هل أنت متأكد من إلغاء تفعيل القسم "$departmentName"؟\n\nسيتم إخفاء القسم من القوائم النشطة.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء التفعيل'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
