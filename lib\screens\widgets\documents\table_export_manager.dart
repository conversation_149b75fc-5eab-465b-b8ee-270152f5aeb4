import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

/// مدير تصدير الجداول
/// يوفر وظائف تصدير الجداول إلى تنسيقات مختلفة
class TableExportManager {
  static const String _csvSeparator = ',';
  static const String _tsvSeparator = '\t';

  /// تصدير الجدول إلى CSV
  static String exportToCsv(List<List<String>> tableData) {
    return _exportToDelimited(tableData, _csvSeparator);
  }

  /// تصدير الجدول إلى TSV (Tab-separated values)
  static String exportToTsv(List<List<String>> tableData) {
    return _exportToDelimited(tableData, _tsvSeparator);
  }

  /// تصدير الجدول إلى HTML
  static String exportToHtml(List<List<String>> tableData, {bool hasHeader = true}) {
    if (tableData.isEmpty) return '';

    final buffer = StringBuffer();
    buffer.writeln('<table border="1" style="border-collapse: collapse; width: 100%;">');

    for (int rowIndex = 0; rowIndex < tableData.length; rowIndex++) {
      final row = tableData[rowIndex];
      final isHeaderRow = hasHeader && rowIndex == 0;
      final tag = isHeaderRow ? 'th' : 'td';
      final style = isHeaderRow 
          ? 'style="background-color: #f2f2f2; font-weight: bold; padding: 8px; text-align: center;"'
          : 'style="padding: 8px; text-align: center;"';

      buffer.writeln('  <tr>');
      for (final cell in row) {
        final escapedCell = _escapeHtml(cell);
        buffer.writeln('    <$tag $style>$escapedCell</$tag>');
      }
      buffer.writeln('  </tr>');
    }

    buffer.writeln('</table>');
    return buffer.toString();
  }

  /// تصدير الجدول إلى Markdown
  static String exportToMarkdown(List<List<String>> tableData, {bool hasHeader = true}) {
    if (tableData.isEmpty) return '';

    final buffer = StringBuffer();
    
    // إضافة صف العنوان
    if (hasHeader && tableData.isNotEmpty) {
      final headerRow = tableData[0];
      buffer.write('| ');
      buffer.write(headerRow.map((cell) => _escapeMarkdown(cell)).join(' | '));
      buffer.writeln(' |');

      // إضافة خط الفصل
      buffer.write('|');
      for (int i = 0; i < headerRow.length; i++) {
        buffer.write(' --- |');
      }
      buffer.writeln();

      // إضافة باقي الصفوف
      for (int rowIndex = 1; rowIndex < tableData.length; rowIndex++) {
        final row = tableData[rowIndex];
        buffer.write('| ');
        buffer.write(row.map((cell) => _escapeMarkdown(cell)).join(' | '));
        buffer.writeln(' |');
      }
    } else {
      // جدول بدون عنوان
      for (final row in tableData) {
        buffer.write('| ');
        buffer.write(row.map((cell) => _escapeMarkdown(cell)).join(' | '));
        buffer.writeln(' |');
      }
    }

    return buffer.toString();
  }

  /// تصدير الجدول إلى JSON
  static String exportToJson(List<List<String>> tableData, {bool hasHeader = true}) {
    if (tableData.isEmpty) return '[]';

    if (hasHeader && tableData.length > 1) {
      final headers = tableData[0];
      final rows = tableData.sublist(1);
      
      final jsonData = rows.map((row) {
        final rowMap = <String, String>{};
        for (int i = 0; i < row.length && i < headers.length; i++) {
          rowMap[headers[i]] = row[i];
        }
        return rowMap;
      }).toList();

      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } else {
      // جدول بدون عنوان - تصدير كمصفوفة ثنائية الأبعاد
      return const JsonEncoder.withIndent('  ').convert(tableData);
    }
  }

  /// تصدير الجدول إلى XML
  static String exportToXml(List<List<String>> tableData, {bool hasHeader = true}) {
    if (tableData.isEmpty) return '<table></table>';

    final buffer = StringBuffer();
    buffer.writeln('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.writeln('<table>');

    if (hasHeader && tableData.isNotEmpty) {
      // إضافة العنوان
      buffer.writeln('  <header>');
      final headerRow = tableData[0];
      for (int i = 0; i < headerRow.length; i++) {
        final escapedCell = _escapeXml(headerRow[i]);
        buffer.writeln('    <column index="$i">$escapedCell</column>');
      }
      buffer.writeln('  </header>');

      // إضافة البيانات
      buffer.writeln('  <data>');
      for (int rowIndex = 1; rowIndex < tableData.length; rowIndex++) {
        final row = tableData[rowIndex];
        buffer.writeln('    <row index="$rowIndex">');
        for (int colIndex = 0; colIndex < row.length; colIndex++) {
          final escapedCell = _escapeXml(row[colIndex]);
          buffer.writeln('      <cell column="$colIndex">$escapedCell</cell>');
        }
        buffer.writeln('    </row>');
      }
      buffer.writeln('  </data>');
    } else {
      // جدول بدون عنوان
      for (int rowIndex = 0; rowIndex < tableData.length; rowIndex++) {
        final row = tableData[rowIndex];
        buffer.writeln('  <row index="$rowIndex">');
        for (int colIndex = 0; colIndex < row.length; colIndex++) {
          final escapedCell = _escapeXml(row[colIndex]);
          buffer.writeln('    <cell column="$colIndex">$escapedCell</cell>');
        }
        buffer.writeln('  </row>');
      }
    }

    buffer.writeln('</table>');
    return buffer.toString();
  }

  /// عرض حوار تصدير الجدول
  static Future<void> showExportDialog(
    BuildContext context,
    List<List<String>> tableData, {
    bool hasHeader = true,
  }) async {
    await showDialog(
      context: context,
      builder: (context) => _TableExportDialog(
        tableData: tableData,
        hasHeader: hasHeader,
      ),
    );
  }

  /// تصدير إلى تنسيق محدد بفاصل
  static String _exportToDelimited(List<List<String>> tableData, String separator) {
    return tableData.map((row) {
      return row.map((cell) {
        // إضافة علامات اقتباس إذا كانت الخلية تحتوي على الفاصل أو علامات اقتباس
        if (cell.contains(separator) || cell.contains('"') || cell.contains('\n')) {
          return '"${cell.replaceAll('"', '""')}"';
        }
        return cell;
      }).join(separator);
    }).join('\n');
  }

  /// تنظيف HTML
  static String _escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#39;');
  }

  /// تنظيف Markdown
  static String _escapeMarkdown(String text) {
    return text
        .replaceAll('|', '\\|')
        .replaceAll('\n', ' ');
  }

  /// تنظيف XML
  static String _escapeXml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&apos;');
  }
}

/// حوار تصدير الجدول
class _TableExportDialog extends StatefulWidget {
  final List<List<String>> tableData;
  final bool hasHeader;

  const _TableExportDialog({
    required this.tableData,
    required this.hasHeader,
  });

  @override
  State<_TableExportDialog> createState() => _TableExportDialogState();
}

class _TableExportDialogState extends State<_TableExportDialog> {
  String _selectedFormat = 'CSV';
  String _exportedContent = '';

  final Map<String, String> _formats = {
    'CSV': 'ملف CSV (فاصلة)',
    'TSV': 'ملف TSV (تبويب)',
    'HTML': 'جدول HTML',
    'Markdown': 'جدول Markdown',
    'JSON': 'بيانات JSON',
    'XML': 'ملف XML',
  };

  @override
  void initState() {
    super.initState();
    _updateExportContent();
  }

  void _updateExportContent() {
    switch (_selectedFormat) {
      case 'CSV':
        _exportedContent = TableExportManager.exportToCsv(widget.tableData);
        break;
      case 'TSV':
        _exportedContent = TableExportManager.exportToTsv(widget.tableData);
        break;
      case 'HTML':
        _exportedContent = TableExportManager.exportToHtml(widget.tableData, hasHeader: widget.hasHeader);
        break;
      case 'Markdown':
        _exportedContent = TableExportManager.exportToMarkdown(widget.tableData, hasHeader: widget.hasHeader);
        break;
      case 'JSON':
        _exportedContent = TableExportManager.exportToJson(widget.tableData, hasHeader: widget.hasHeader);
        break;
      case 'XML':
        _exportedContent = TableExportManager.exportToXml(widget.tableData, hasHeader: widget.hasHeader);
        break;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تصدير الجدول'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اختيار التنسيق
            const Text('اختر تنسيق التصدير:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButton<String>(
              value: _selectedFormat,
              isExpanded: true,
              items: _formats.entries.map((entry) => DropdownMenuItem(
                value: entry.key,
                child: Text(entry.value),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  _selectedFormat = value;
                  _updateExportContent();
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // معاينة المحتوى
            const Text('معاينة:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    _exportedContent,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: _exportedContent));
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم نسخ الجدول بتنسيق $_selectedFormat إلى الحافظة'),
                backgroundColor: Colors.green,
              ),
            );
          },
          icon: const Icon(Icons.copy),
          label: const Text('نسخ'),
        ),
      ],
    );
  }
}
