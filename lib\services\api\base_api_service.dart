import 'package:http/http.dart' as http;
import 'api_service.dart';

/// خدمة API الأساسية - تغليف لـ ApiService
class BaseApiService {
  final ApiService _apiService = ApiService();

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _apiService.initialize();
  }

  /// طلب GET
  Future<http.Response> get(String endpoint, {Map<String, String>? queryParams}) async {
    String url = endpoint;
    if (queryParams != null && queryParams.isNotEmpty) {
      final query = queryParams.entries
          .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
          .join('&');
      url = '$endpoint?$query';
    }
    return await _apiService.get(url);
  }

  /// طلب POST
  Future<http.Response> post(String endpoint, Map<String, dynamic>? body, {bool requireAuth = true}) async {
    return await _apiService.post(endpoint, body, requireAuth: requireAuth);
  }

  /// طلب PUT
  Future<http.Response> put(String endpoint, Map<String, dynamic> body) async {
    return await _apiService.put(endpoint, body);
  }

  /// طلب DELETE
  Future<http.Response> delete(String endpoint) async {
    return await _apiService.delete(endpoint);
  }

  /// معالجة الاستجابة للكائن الواحد
  T handleResponse<T>(http.Response response, T Function(dynamic) fromJson) {
    return _apiService.handleResponse<T>(response, fromJson);
  }

  /// معالجة الاستجابة للقائمة
  List<T> handleListResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    return _apiService.handleListResponse<T>(response, fromJson);
  }

  /// الحصول على رمز الوصول
  String? get accessToken => _apiService.accessToken;

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _apiService.isLoggedIn;

  /// حفظ بيانات المصادقة
  Future<void> saveAuthResponse(dynamic authResponse) async {
    await _apiService.saveAuthResponse(authResponse);
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    await _apiService.logout();
  }
}
