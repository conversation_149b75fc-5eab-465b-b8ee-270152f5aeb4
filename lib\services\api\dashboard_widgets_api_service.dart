import 'package:get/get.dart';
import '../../models/dashboard_models.dart';
import 'base_api_service.dart';

/// خدمة API لعناصر لوحة المعلومات - متوافقة مع ASP.NET Core
class DashboardWidgetsApiService extends GetxService {
  late final BaseApiService _apiService;

  DashboardWidgetsApiService() {
    // محاولة الحصول على BaseApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<BaseApiService>();
    } catch (e) {
      _apiService = BaseApiService();
    }
  }

  /// الحصول على جميع عناصر لوحة المعلومات
  Future<List<DashboardWidget>> getAllWidgets() async {
    try {
      final response = await _apiService.get('/api/dashboard-widgets');
      return _apiService.handleListResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل عناصر لوحة المعلومات: $e');
    }
  }

  /// الحصول على عناصر لوحة معلومات محددة
  Future<List<DashboardWidget>> getWidgetsByDashboard(int dashboardId) async {
    try {
      final response = await _apiService.get('/api/dashboard-widgets/dashboard/$dashboardId');
      return _apiService.handleListResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل عناصر لوحة المعلومات: $e');
    }
  }

  /// الحصول على عنصر بالمعرف
  Future<DashboardWidget> getWidgetById(int id) async {
    try {
      final response = await _apiService.get('/api/dashboard-widgets/$id');
      return _apiService.handleResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل عنصر لوحة المعلومات: $e');
    }
  }

  /// إنشاء عنصر جديد
  Future<DashboardWidget> createWidget(DashboardWidget widget) async {
    try {
      final response = await _apiService.post('/dashboard-widgets', widget.toJson());
      return _apiService.handleResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء عنصر لوحة المعلومات: $e');
    }
  }

  /// تحديث عنصر
  Future<DashboardWidget> updateWidget(DashboardWidget widget) async {
    try {
      final response = await _apiService.put('/dashboard-widgets/${widget.id}', widget.toJson());
      return _apiService.handleResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحديث عنصر لوحة المعلومات: $e');
    }
  }

  /// حذف عنصر
  Future<void> deleteWidget(int id) async {
    try {
      await _apiService.delete('/dashboard-widgets/$id');
    } catch (e) {
      throw Exception('خطأ في حذف عنصر لوحة المعلومات: $e');
    }
  }

  /// تحديث موقع وحجم العنصر
  Future<void> updateWidgetLayout(int widgetId, int x, int y, int width, int height) async {
    try {
      await _apiService.put('/dashboard-widgets/$widgetId/layout', {
        'x': x,
        'y': y,
        'width': width,
        'height': height,
      });
    } catch (e) {
      throw Exception('خطأ في تحديث تخطيط العنصر: $e');
    }
  }

  /// تحديث إعدادات العنصر
  Future<void> updateWidgetSettings(int widgetId, Map<String, dynamic> settings) async {
    try {
      await _apiService.put('/dashboard-widgets/$widgetId/settings', settings);
    } catch (e) {
      throw Exception('خطأ في تحديث إعدادات العنصر: $e');
    }
  }

  /// تحديث ترتيب العناصر
  Future<void> updateWidgetsOrder(List<Map<String, dynamic>> widgetsOrder) async {
    try {
      await _apiService.put('/dashboard-widgets/order', {
        'widgets': widgetsOrder,
      });
    } catch (e) {
      throw Exception('خطأ في تحديث ترتيب العناصر: $e');
    }
  }

  /// نسخ عنصر
  Future<DashboardWidget> duplicateWidget(int widgetId, int targetDashboardId) async {
    try {
      final response = await _apiService.post('/dashboard-widgets/$widgetId/duplicate', {
        'targetDashboardId': targetDashboardId,
      });
      return _apiService.handleResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في نسخ العنصر: $e');
    }
  }

  /// نقل عنصر إلى لوحة معلومات أخرى
  Future<void> moveWidget(int widgetId, int targetDashboardId) async {
    try {
      await _apiService.put('/dashboard-widgets/$widgetId/move', {
        'targetDashboardId': targetDashboardId,
      });
    } catch (e) {
      throw Exception('خطأ في نقل العنصر: $e');
    }
  }

  /// الحصول على بيانات العنصر
  Future<Map<String, dynamic>> getWidgetData(int widgetId, Map<String, dynamic>? filters) async {
    try {
      final queryParams = filters?.map((key, value) => MapEntry(key, value.toString())) ?? <String, String>{};
      final response = await _apiService.get('/dashboard-widgets/$widgetId/data', queryParams: queryParams);
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على بيانات العنصر: $e');
    }
  }

  /// تحديث بيانات العنصر
  Future<void> refreshWidgetData(int widgetId) async {
    try {
      await _apiService.post('/dashboard-widgets/$widgetId/refresh', {});
    } catch (e) {
      throw Exception('خطأ في تحديث بيانات العنصر: $e');
    }
  }

  /// الحصول على أنواع العناصر المتاحة
  Future<List<Map<String, dynamic>>> getAvailableWidgetTypes() async {
    try {
      final response = await _apiService.get('/dashboard-widgets/types');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على أنواع العناصر: $e');
    }
  }

  /// تصدير عنصر
  Future<String> exportWidget(int widgetId, String format) async {
    try {
      final response = await _apiService.get('/dashboard-widgets/$widgetId/export?format=$format');
      return _apiService.handleResponse<String>(
        response,
        (json) => json['downloadUrl'] as String,
      );
    } catch (e) {
      throw Exception('خطأ في تصدير العنصر: $e');
    }
  }

  /// استيراد عنصر
  Future<DashboardWidget> importWidget(int dashboardId, Map<String, dynamic> widgetData) async {
    try {
      final response = await _apiService.post('/dashboard-widgets/import', {
        'dashboardId': dashboardId,
        'widgetData': widgetData,
      });
      return _apiService.handleResponse<DashboardWidget>(
        response,
        (json) => DashboardWidget.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في استيراد العنصر: $e');
    }
  }
}
