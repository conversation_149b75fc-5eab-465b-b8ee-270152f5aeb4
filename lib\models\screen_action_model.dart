import 'screen_model.dart';
import 'action_entity_model.dart';

/// نموذج ScreenAction - يمثل الربط بين الشاشة والعملية
class ScreenAction {
  final int id;
  final int screenId;
  final int actionId;
  final bool isActive;
  final int createdAt;

  // العلاقات البرمجية
  final Screen? screen;
  final ActionEntity? action;

  const ScreenAction({
    required this.id,
    required this.screenId,
    required this.actionId,
    this.isActive = true,
    required this.createdAt,
    this.screen,
    this.action,
  });

  factory ScreenAction.fromJson(Map<String, dynamic> json) {
    return ScreenAction(
      id: json['id'] as int,
      screenId: json['screenId'] as int,
      actionId: json['actionId'] as int,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      screen: json['screen'] != null ? Screen.fromJson(json['screen']) : null,
      action: json['action'] != null ? ActionEntity.fromJson(json['action']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'screenId': screenId,
      'actionId': actionId,
      'isActive': isActive,
      'createdAt': createdAt,
      'screen': screen?.toJson(),
      'action': action?.toJson(),
    };
  }
}
