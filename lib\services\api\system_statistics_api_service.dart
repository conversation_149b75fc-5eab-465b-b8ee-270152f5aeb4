import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'base_api_service.dart';

/// خدمة API للإحصائيات العامة للنظام
class SystemStatisticsApiService extends GetxService {
  late final BaseApiService _apiService;

  SystemStatisticsApiService() {
    // محاولة الحصول على BaseApiService من GetX، أو إنشاء واحد جديد
    try {
      _apiService = Get.find<BaseApiService>();
    } catch (e) {
      _apiService = BaseApiService();
      // تهيئة BaseApiService
      _apiService.initialize();
    }
  }

  /// الحصول على الإحصائيات العامة للنظام
  Future<Map<String, dynamic>> getSystemStatistics() async {
    try {
      final response = await _apiService.get('/api/Dashboards/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على الإحصائيات العامة: $e');
    }
  }

  /// الحصول على إحصائيات المهام من Reports API
  Future<Map<String, dynamic>> getTasksStatistics() async {
    try {
      final response = await _apiService.get('/api/Reports/tasks-statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المهام: $e');
    }
  }

  /// الحصول على إحصائيات المستخدمين من Reports API
  Future<Map<String, dynamic>> getUsersStatistics() async {
    try {
      final response = await _apiService.get('/api/Reports/users-statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المستخدمين: $e');
    }
  }

  /// الحصول على إحصائيات الأقسام
  Future<Map<String, dynamic>> getDepartmentsStatistics() async {
    try {
      final response = await _apiService.get('/api/Departments/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات الأقسام: $e');
    }
  }

  /// الحصول على إحصائيات قسم محدد
  Future<Map<String, dynamic>> getDepartmentStatistics(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Departments/$departmentId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات القسم: $e');
    }
  }

  /// الحصول على إحصائيات شاملة مجمعة من جميع المصادر
  Future<Map<String, dynamic>> getComprehensiveStatistics() async {
    try {
      // جلب الإحصائيات من جميع المصادر بشكل متوازي
      final results = await Future.wait([
        getSystemStatistics(),
        getTasksStatistics(),
        getUsersStatistics(),
        getDepartmentsStatistics(),
      ]);

      final systemStats = results[0];
      final taskStats = results[1];
      final userStats = results[2];
      final departmentStats = results[3];

      // دمج جميع الإحصائيات في كائن واحد
      return {
        // الإحصائيات العامة من النظام
        'totalTasks': systemStats['totalTasks'] ?? taskStats['totalTasks'] ?? 0,
        'completedTasks': systemStats['completedTasks'] ?? taskStats['completedTasks'] ?? 0,
        'pendingTasks': systemStats['pendingTasks'] ?? taskStats['pendingTasks'] ?? 0,
        'overdueTasks': systemStats['overdueTasks'] ?? 0,
        'totalUsers': systemStats['totalUsers'] ?? userStats['totalUsers'] ?? 0,
        'activeUsers': systemStats['activeUsers'] ?? userStats['activeUsers'] ?? 0,
        'onlineUsers': systemStats['onlineUsers'] ?? userStats['onlineUsers'] ?? 0,
        'totalDepartments': systemStats['totalDepartments'] ?? departmentStats['totalDepartments'] ?? 0,
        'totalDashboards': systemStats['totalDashboards'] ?? 0,
        'sharedDashboards': systemStats['sharedDashboards'] ?? 0,

        // معدلات الإنجاز
        'taskCompletionRate': systemStats['taskCompletionRate'] ?? taskStats['completionRate'] ?? 0,
        'userActivityRate': systemStats['userActivityRate'] ?? 0,

        // إحصائيات مفصلة
        'tasksByStatus': systemStats['tasksByStatus'] ?? taskStats['tasksByStatus'] ?? [],
        'tasksByPriority': systemStats['tasksByPriority'] ?? taskStats['tasksByPriority'] ?? [],
        'tasksByDepartment': systemStats['tasksByDepartment'] ?? taskStats['tasksByDepartment'] ?? [],
        'usersByDepartment': systemStats['usersByDepartment'] ?? userStats['usersByDepartment'] ?? [],
        'usersByRole': userStats['usersByRole'] ?? [],
        'departmentStats': departmentStats['departmentStats'] ?? [],

        // معلومات إضافية
        'lastUpdated': systemStats['lastUpdated'] ?? DateTime.now().millisecondsSinceEpoch ~/ 1000,
      };
    } catch (e) {
      throw Exception('خطأ في الحصول على الإحصائيات الشاملة: $e');
    }
  }

  /// الحصول على إحصائيات مبسطة للوحة التحكم
  Future<Map<String, dynamic>> getDashboardStatistics() async {
    try {
      // استخدام endpoint واحد فقط بدلاً من getComprehensiveStatistics
      final stats = await getSystemStatistics();

      return {
        // الإحصائيات الأساسية
        'totalTasks': stats['totalTasks'] ?? 0,
        'completedTasks': stats['completedTasks'] ?? 0,
        'pendingTasks': stats['pendingTasks'] ?? 0,
        'overdueTasks': stats['overdueTasks'] ?? 0,
        'totalUsers': stats['totalUsers'] ?? 0,
        'activeUsers': stats['activeUsers'] ?? 0,
        'totalDepartments': stats['totalDepartments'] ?? 0,

        // معدلات الإنجاز
        'taskCompletionRate': stats['taskCompletionRate'] ?? 0.0,
        'userActivityRate': stats['userActivityRate'] ?? 0.0,

        // بيانات للرسوم البيانية
        'tasksByStatus': stats['tasksByStatus'] ?? [],
        'tasksByDepartment': stats['tasksByDepartment'] ?? [],
        'usersByDepartment': stats['usersByDepartment'] ?? [],
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات لوحة التحكم: $e');

      // إرجاع بيانات افتراضية في حالة الخطأ
      return {
        'totalTasks': 0,
        'completedTasks': 0,
        'pendingTasks': 0,
        'overdueTasks': 0,
        'totalUsers': 0,
        'activeUsers': 0,
        'totalDepartments': 0,
        'taskCompletionRate': 0.0,
        'userActivityRate': 0.0,
        'tasksByStatus': [],
        'tasksByDepartment': [],
        'usersByDepartment': [],
      };
    }
  }

  /// تحديث الإحصائيات (إعادة جلب البيانات)
  Future<Map<String, dynamic>> refreshStatistics() async {
    try {
      return await getComprehensiveStatistics();
    } catch (e) {
      throw Exception('خطأ في تحديث الإحصائيات: $e');
    }
  }

  /// التحقق من صحة الاتصال بجميع endpoints الإحصائيات
  Future<Map<String, bool>> checkStatisticsEndpoints() async {
    final results = <String, bool>{};
    
    try {
      await getSystemStatistics();
      results['systemStatistics'] = true;
    } catch (e) {
      results['systemStatistics'] = false;
    }
    
    try {
      await getTasksStatistics();
      results['tasksStatistics'] = true;
    } catch (e) {
      results['tasksStatistics'] = false;
    }
    
    try {
      await getUsersStatistics();
      results['usersStatistics'] = true;
    } catch (e) {
      results['usersStatistics'] = false;
    }
    
    try {
      await getDepartmentsStatistics();
      results['departmentsStatistics'] = true;
    } catch (e) {
      results['departmentsStatistics'] = false;
    }
    
    return results;
  }
}
