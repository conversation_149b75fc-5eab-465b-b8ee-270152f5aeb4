import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/search_models.dart';
import 'api/messages_api_service.dart';
import 'api/user_api_service.dart';
import 'api/archive_documents_api_service.dart';
import 'api/calendar_events_api_service.dart';
import 'api/reports_api_service.dart';
import '../controllers/task_controller.dart';
import '../controllers/auth_controller.dart';

/// خدمة البحث الموحد
class UnifiedSearchService {
  final MessagesApiService _messageService = MessagesApiService();
  final UserApiService _userService = UserApiService();
  final ArchiveDocumentsApiService _documentService = ArchiveDocumentsApiService();
  final CalendarEventsApiService _eventService = CalendarEventsApiService();
  final ReportsApiService _reportService = ReportsApiService();

  /// البحث الموحد
  Future<List<SearchResult>> search({
    required String query,
    List<SearchResultType>? types,
    int limit = 10,
    bool includeDeleted = false,
  }) async {
    final results = <SearchResult>[];
    final searchTypes = types ?? SearchResultType.values;

    // تنفيذ البحث بشكل متوازي
    final futures = <Future>[];

    if (searchTypes.contains(SearchResultType.task)) {
      futures.add(_searchTasks(query, limit).then((taskResults) {
        results.addAll(taskResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.message)) {
      futures.add(_searchMessages(query, limit).then((messageResults) {
        results.addAll(messageResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.user)) {
      futures.add(_searchUsers(query, limit).then((userResults) {
        results.addAll(userResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.document)) {
      futures.add(_searchDocuments(query, limit).then((documentResults) {
        results.addAll(documentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.event)) {
      futures.add(_searchEvents(query, limit).then((eventResults) {
        results.addAll(eventResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.report)) {
      futures.add(_searchReports(query, limit).then((reportResults) {
        results.addAll(reportResults);
      }));
    }

    // انتظار انتهاء جميع عمليات البحث
    await Future.wait(futures);

    // ترتيب النتائج حسب الصلة
    results.sort((a, b) => b.relevance.compareTo(a.relevance));

    return results;
  }

  /// البحث في المهام
  Future<List<SearchResult>> _searchTasks(String query, int limit) async {
    try {
      // استخدام TaskController بدلاً من TaskApiService مباشرة
      final taskController = Get.find<TaskController>();
      final authController = Get.find<AuthController>();

      // تحميل المهام حسب صلاحيات المستخدم أولاً
      if (authController.currentUser.value != null) {
        await taskController.loadTasksByUserPermissions(authController.currentUser.value!.id);
      }

      // البحث في المهام المحملة
      final allTasks = taskController.allTasks;
      final filteredTasks = allTasks.where((task) =>
        task.title.toLowerCase().contains(query.toLowerCase()) ||
        (task.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
      ).toList();

      return filteredTasks.take(limit).map((task) => SearchResult(
        id: task.id.toString(),
        type: SearchResultType.task,
        title: task.title,
        subtitle: task.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(task.createdAt),
        relevance: _calculateRelevance(query, task.title),
        data: task,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المهام: $e');
      return [];
    }
  }

  /// البحث في الرسائل
  Future<List<SearchResult>> _searchMessages(String query, int limit) async {
    try {
      final messages = await _messageService.searchMessages(query);
      return messages.take(limit).map((message) => SearchResult(
        id: message.id.toString(),
        type: SearchResultType.message,
        title: 'رسالة من ${message.senderName}',
        subtitle: message.content,
        date: DateTime.fromMillisecondsSinceEpoch(message.createdAt),
        relevance: _calculateRelevance(query, message.content),
        data: message,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الرسائل: $e');
      return [];
    }
  }

  /// البحث في المستخدمين
  Future<List<SearchResult>> _searchUsers(String query, int limit) async {
    try {
      final users = await _userService.searchUsers(query);
      return users.take(limit).map((user) => SearchResult(
        id: user.id.toString(),
        type: SearchResultType.user,
        title: user.name,
        subtitle: user.email ?? 'غير محدد',
        date: DateTime.fromMillisecondsSinceEpoch(user.createdAt),
        relevance: _calculateRelevance(query, user.name),
        data: user,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المستخدمين: $e');
      return [];
    }
  }

  /// البحث في الوثائق
  Future<List<SearchResult>> _searchDocuments(String query, int limit) async {
    try {
      final documents = await _documentService.searchDocuments(query);
      return documents.take(limit).map((document) => SearchResult(
        id: document.id.toString(),
        type: SearchResultType.document,
        title: document.title,
        subtitle: document.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(document.createdAt),
        relevance: _calculateRelevance(query, document.title),
        data: document,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الوثائق: $e');
      return [];
    }
  }

  /// البحث في الأحداث
  Future<List<SearchResult>> _searchEvents(String query, int limit) async {
    try {
      final events = await _eventService.searchEvents(query);
      return events.take(limit).map((event) => SearchResult(
        id: event.id.toString(),
        type: SearchResultType.event,
        title: event.title,
        subtitle: event.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(event.startTime),
        relevance: _calculateRelevance(query, event.title),
        data: event,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأحداث: $e');
      return [];
    }
  }

  /// البحث في التقارير
  Future<List<SearchResult>> _searchReports(String query, int limit) async {
    try {
      final reports = await _reportService.searchReports(query);
      return reports.take(limit).map((report) => SearchResult(
        id: report.id.toString(),
        type: SearchResultType.report,
        title: report.title,
        subtitle: report.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(report.createdAt),
        relevance: _calculateRelevance(query, report.title),
        data: report,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في التقارير: $e');
      return [];
    }
  }

  /// حساب درجة الصلة
  double _calculateRelevance(String query, String text) {
    if (text.isEmpty) return 0.0;
    
    final queryLower = query.toLowerCase();
    final textLower = text.toLowerCase();
    
    // إذا كان النص يحتوي على الاستعلام بالضبط
    if (textLower.contains(queryLower)) {
      // إذا كان النص يبدأ بالاستعلام
      if (textLower.startsWith(queryLower)) {
        return 1.0;
      }
      // إذا كان النص يحتوي على الاستعلام
      return 0.8;
    }
    
    // حساب التشابه بناءً على الكلمات المشتركة
    final queryWords = queryLower.split(' ');
    final textWords = textLower.split(' ');
    
    int matchingWords = 0;
    for (final queryWord in queryWords) {
      if (textWords.any((textWord) => textWord.contains(queryWord))) {
        matchingWords++;
      }
    }
    
    return matchingWords / queryWords.length * 0.6;
  }
}
