import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../services/unified_permission_service.dart';
import 'models/dashboard_models.dart';
import 'services/dashboard_service.dart';
import 'widgets/chart_factory.dart';

/// الشاشة الموحدة للوحة المعلومات
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late final DashboardService _dashboardService;
  late final UnifiedPermissionService _permissionService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// تهيئة الخدمات
  void _initializeServices() {
    // تسجيل الخدمة إذا لم تكن مسجلة
    if (!Get.isRegistered<DashboardService>()) {
      Get.put(DashboardService());
    }
    
    _dashboardService = Get.find<DashboardService>();
    _permissionService = Get.find<UnifiedPermissionService>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Obx(() => _buildBody()),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Icon(
            Icons.dashboard,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text('لوحة المعلومات'),
        ],
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // زر التحديث
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _dashboardService.refreshDashboard,
          tooltip: 'تحديث',
        ),
        
        // زر الفلاتر
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFiltersDialog,
          tooltip: 'الفلاتر',
        ),
        
        // زر الإعدادات
        if (_permissionService.canManageDashboards())
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'الإعدادات',
          ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    switch (_dashboardService.state.state) {
      case DashboardState.loading:
        return _buildLoadingWidget();
      case DashboardState.error:
        return _buildErrorWidget();
      case DashboardState.empty:
        return _buildEmptyWidget();
      case DashboardState.loaded:
        return _buildDashboardGrid();
    }
  }

  /// واجهة التحميل
  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل لوحة المعلومات...'),
        ],
      ),
    );
  }

  /// واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل لوحة المعلومات',
              style: AppStyles.headingMedium.copyWith(
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _dashboardService.state.errorMessage ?? 'خطأ غير معروف',
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _dashboardService.loadDashboard,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// واجهة البيانات الفارغة
  Widget _buildEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد عناصر في لوحة المعلومات',
              style: AppStyles.headingMedium.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة مخططات وعناصر جديدة',
              style: AppStyles.bodyMedium,
            ),
            const SizedBox(height: 24),
            if (_permissionService.canManageDashboards())
              ElevatedButton.icon(
                onPressed: _showAddItemDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة عنصر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة لوحة المعلومات
  Widget _buildDashboardGrid() {
    return RefreshIndicator(
      onRefresh: _dashboardService.refreshDashboard,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.1,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _dashboardService.items.length,
          itemBuilder: (context, index) {
            final item = _dashboardService.items[index];
            return ChartFactory.createChart(
              item: item,
              filters: _dashboardService.currentFilters,
              onRefresh: _dashboardService.refreshDashboard,
            );
          },
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget? _buildFloatingActionButton() {
    if (!_permissionService.canManageDashboards()) return null;
    
    return FloatingActionButton(
      onPressed: _showAddItemDialog,
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  /// عرض حوار إضافة عنصر
  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مخطط جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ChartType.values.map((type) {
            return ListTile(
              leading: Icon(type.icon),
              title: Text(type.displayName),
              subtitle: Text(ChartFactory.getChartTypeDescription(type)),
              onTap: () => _addNewChart(type),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// إضافة مخطط جديد
  void _addNewChart(ChartType chartType) {
    Navigator.of(context).pop();
    
    // إنشاء عنصر جديد
    final item = ChartFactory.createDefaultItem(
      id: 'chart_${DateTime.now().millisecondsSinceEpoch}',
      title: chartType.displayName,
      chartType: chartType,
    );
    
    // إضافة العنصر
    _dashboardService.items.add(item);
    
    // عرض رسالة نجاح
    Get.snackbar(
      'تم الإضافة',
      'تم إضافة ${chartType.displayName} بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
    );
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلاتر لوحة المعلومات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم تطوير الفلاتر قريباً'),
            SizedBox(height: 16),
            Text('ستتمكن من تصفية البيانات حسب:'),
            Text('• التاريخ'),
            Text('• القسم'),
            Text('• المستخدم'),
            Text('• حالة المهمة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات لوحة المعلومات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم تطوير الإعدادات قريباً'),
            SizedBox(height: 16),
            Text('ستتمكن من:'),
            Text('• تخصيص ترتيب المخططات'),
            Text('• تغيير ألوان المخططات'),
            Text('• حفظ تخطيطات مخصصة'),
            Text('• تصدير البيانات'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
