/// تعدادات المهام
///
/// تحتوي على تعدادات الأولوية والحالة للمهام
library;

/// أولوية المهمة
enum TaskPriority {
  low(1, 'منخفضة', 'Low'),
  medium(2, 'متوسطة', 'Medium'),
  high(3, 'عالية', 'High'),
  urgent(4, 'عاجلة', 'Urgent');

  const TaskPriority(this.level, this.displayNameAr, this.displayNameEn);

  final int level;
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الأولوية من المستوى
  static TaskPriority fromLevel(int level) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.level == level,
      orElse: () => TaskPriority.medium,
    );
  }

  /// الحصول على الأولوية من الاسم
  static TaskPriority fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskPriority.values.firstWhere(
      (priority) =>
        priority.displayNameEn.toLowerCase() == lowerName ||
        priority.displayNameAr == name ||
        priority.name.toLowerCase() == lowerName,
      orElse: () => TaskPriority.medium,
    );
  }

  /// الحصول على الأولوية من الاسم (string)
  static TaskPriority fromString(String name) => fromName(name);

  String get stringName => name;
}

/// حالة المهمة - متطابقة مع قاعدة البيانات
enum TaskStatus {
  news(1, 'جديد', 'New'),
  inProgress(2, 'قيد التنفيذ', 'In Progress'),
  completed(3, 'مكتملة', 'Completed');

  const TaskStatus(this.id, this.displayNameAr, this.displayNameEn);

  final int id;
  final String displayNameAr;
  final String displayNameEn;

  /// الحصول على الحالة من المعرف
  static TaskStatus fromId(int id) {
    return TaskStatus.values.firstWhere(
      (status) => status.id == id,
      orElse: () => TaskStatus.news,
    );
  }

  /// الحصول على الحالة من الاسم
  static TaskStatus fromName(String name) {
    final lowerName = name.toLowerCase();
    return TaskStatus.values.firstWhere(
      (status) =>
        status.displayNameEn.toLowerCase() == lowerName ||
        status.displayNameAr == name ||
        status.name.toLowerCase() == lowerName,
      orElse: () => TaskStatus.news,
    );
  }

  /// الحصول على الحالة من الاسم (string)
  static TaskStatus fromString(String name) => fromName(name);

  String get stringName => name;
}
