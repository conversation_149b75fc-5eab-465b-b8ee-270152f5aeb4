// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:get/get.dart';
// import 'package:mockito/mockito.dart';

// import 'package:flutter_application_2/screens/admin/admin_dashboard_new.dart';
// import 'package:flutter_application_2/screens/admin/users/user_management_screen.dart';
// import 'package:flutter_application_2/screens/admin/shared/admin_card_widget.dart';
// import 'package:flutter_application_2/controllers/admin_controller.dart';
// import 'package:flutter_application_2/controllers/auth_controller.dart';
// import 'package:flutter_application_2/services/unified_permission_service.dart';

// import 'admin_system_test.mocks.dart';

// /// اختبارات واجهات النظام الإداري
// /// 
// /// تغطي:
// /// - اختبارات الويدجت
// /// - اختبارات التفاعل
// /// - اختبارات التنقل
// /// - اختبارات العرض
// void main() {
//   group('اختبارات واجهات النظام الإداري', () {
//     late MockUnifiedPermissionService mockPermissionService;
//     late AdminController adminController;
//     late AuthController authController;

//     setUp(() {
//       // إعداد GetX للاختبار
//       Get.testMode = true;
      
//       // إعداد Mock services
//       mockPermissionService = MockUnifiedPermissionService();
      
//       // إعداد Controllers
//       adminController = AdminController();
//       authController = AuthController();
      
//       // تسجيل الخدمات في GetX
//       Get.put<UnifiedPermissionService>(mockPermissionService);
//       Get.put<AdminController>(adminController);
//       Get.put<AuthController>(authController);
//     });

//     tearDown(() {
//       Get.reset();
//     });

//     group('اختبارات AdminCardWidget', () {
//       testWidgets('يجب أن يعرض AdminCardWidget بشكل صحيح', (WidgetTester tester) async {
//         // إعداد البيانات التجريبية
//         const title = 'إدارة المستخدمين';
//         const subtitle = 'إضافة وتعديل وحذف المستخدمين';
//         const icon = Icons.people;
//         bool tapped = false;

//         // بناء الويدجت
//         await tester.pumpWidget(
//           MaterialApp(
//             home: Scaffold(
//               body: AdminCardWidget(
//                 title: title,
//                 subtitle: subtitle,
//                 icon: icon,
//                 onTap: () => tapped = true,
//               ),
//             ),
//           ),
//         );

//         // التحقق من وجود العناصر
//         expect(find.text(title), findsOneWidget);
//         expect(find.text(subtitle), findsOneWidget);
//         expect(find.byIcon(icon), findsOneWidget);

//         // اختبار النقر
//         await tester.tap(find.byType(AdminCardWidget));
//         await tester.pump();

//         expect(tapped, isTrue);
//       });

//       testWidgets('يجب أن يعطل AdminCardWidget عند عدم وجود صلاحية', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(false);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           MaterialApp(
//             home: Scaffold(
//               body: AdminCardWidget(
//                 title: 'إدارة المستخدمين',
//                 subtitle: 'إضافة وتعديل وحذف المستخدمين',
//                 icon: Icons.people,
//                 isEnabled: mockPermissionService.canAccessAdmin(),
//                 onTap: () {},
//               ),
//             ),
//           ),
//         );

//         // التحقق من تعطيل الويدجت
//         final cardWidget = tester.widget<AdminCardWidget>(find.byType(AdminCardWidget));
//         expect(cardWidget.isEnabled, isFalse);
//       });
//     });

//     group('اختبارات لوحة التحكم الإدارية', () {
//       testWidgets('يجب أن تعرض لوحة التحكم الإدارية بشكل صحيح', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(true);
//         when(mockPermissionService.canManageUsers()).thenReturn(true);
//         when(mockPermissionService.canManageRoles()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من وجود العناصر الأساسية
//         expect(find.text('لوحة التحكم الإدارية'), findsOneWidget);
//         expect(find.text('إدارة المستخدمين'), findsOneWidget);
//         expect(find.text('إدارة الأدوار'), findsOneWidget);
//         expect(find.text('إدارة الصلاحيات'), findsOneWidget);
//       });

//       testWidgets('يجب أن تخفي البطاقات عند عدم وجود صلاحيات', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات - عدم وجود صلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(false);
//         when(mockPermissionService.canManageUsers()).thenReturn(false);
//         when(mockPermissionService.canManageRoles()).thenReturn(false);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من عدم وجود البطاقات المحظورة
//         // (يجب أن تكون معطلة أو مخفية)
//         final userManagementCards = find.text('إدارة المستخدمين');
//         if (userManagementCards.evaluate().isNotEmpty) {
//           // إذا كانت موجودة، يجب أن تكون معطلة
//           final cardWidget = tester.widget<AdminCardWidget>(
//             find.ancestor(
//               of: userManagementCards,
//               matching: find.byType(AdminCardWidget),
//             ),
//           );
//           expect(cardWidget.isEnabled, isFalse);
//         }
//       });
//     });

//     group('اختبارات شاشة إدارة المستخدمين', () {
//       testWidgets('يجب أن تعرض شاشة إدارة المستخدمين بشكل صحيح', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canManageUsers()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const UserManagementScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من وجود العناصر الأساسية
//         expect(find.text('إدارة المستخدمين'), findsOneWidget);
//         expect(find.byType(AppBar), findsOneWidget);
//       });

//       testWidgets('يجب أن تعرض رسالة عدم وجود مستخدمين', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canManageUsers()).thenReturn(true);

//         // بناء الويدجت مع قائمة مستخدمين فارغة
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const UserManagementScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من وجود رسالة عدم وجود مستخدمين
//         expect(find.text('لا يوجد مستخدمون'), findsOneWidget);
//       });

//       testWidgets('يجب أن يعرض زر إضافة مستخدم عند وجود صلاحية', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canManageUsers()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const UserManagementScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من وجود زر الإضافة
//         expect(find.byType(FloatingActionButton), findsOneWidget);
//       });

//       testWidgets('يجب أن يخفي زر إضافة مستخدم عند عدم وجود صلاحية', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات - عدم وجود صلاحية
//         when(mockPermissionService.canManageUsers()).thenReturn(false);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const UserManagementScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من عدم وجود زر الإضافة
//         expect(find.byType(FloatingActionButton), findsNothing);
//       });
//     });

//     group('اختبارات التفاعل والتنقل', () {
//       testWidgets('يجب أن ينتقل إلى شاشة إدارة المستخدمين عند النقر', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(true);
//         when(mockPermissionService.canManageUsers()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // النقر على بطاقة إدارة المستخدمين
//         await tester.tap(find.text('إدارة المستخدمين'));
//         await tester.pumpAndSettle();

//         // التحقق من الانتقال إلى الشاشة الصحيحة
//         expect(find.byType(UserManagementScreen), findsOneWidget);
//       });
//     });

//     group('اختبارات الاستجابة للشاشات المختلفة', () {
//       testWidgets('يجب أن تتكيف مع الشاشات الصغيرة', (WidgetTester tester) async {
//         // تعيين حجم شاشة صغير
//         tester.binding.window.physicalSizeTestValue = const Size(400, 800);
//         tester.binding.window.devicePixelRatioTestValue = 1.0;

//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من التكيف مع الشاشة الصغيرة
//         expect(find.byType(AdminDashboardScreen), findsOneWidget);

//         // إعادة تعيين حجم الشاشة
//         addTearDown(tester.binding.window.clearPhysicalSizeTestValue);
//       });

//       testWidgets('يجب أن تتكيف مع الشاشات الكبيرة', (WidgetTester tester) async {
//         // تعيين حجم شاشة كبير
//         tester.binding.window.physicalSizeTestValue = const Size(1200, 800);
//         tester.binding.window.devicePixelRatioTestValue = 1.0;

//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من التكيف مع الشاشة الكبيرة
//         expect(find.byType(AdminDashboardScreen), findsOneWidget);

//         // إعادة تعيين حجم الشاشة
//         addTearDown(tester.binding.window.clearPhysicalSizeTestValue);
//       });
//     });

//     group('اختبارات إمكانية الوصول', () {
//       testWidgets('يجب أن تدعم قارئ الشاشة', (WidgetTester tester) async {
//         // إعداد Mock للصلاحيات
//         when(mockPermissionService.canAccessAdmin()).thenReturn(true);

//         // بناء الويدجت
//         await tester.pumpWidget(
//           GetMaterialApp(
//             home: const AdminDashboardScreen(),
//           ),
//         );

//         // انتظار بناء الويدجت
//         await tester.pumpAndSettle();

//         // التحقق من وجود Semantics للعناصر المهمة
//         expect(find.bySemanticsLabel('لوحة التحكم الإدارية'), findsOneWidget);
//       });
//     });
//   });
// }
